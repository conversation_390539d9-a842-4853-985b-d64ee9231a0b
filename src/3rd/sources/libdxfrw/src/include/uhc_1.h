/*
 * Copyright (C) 1999-2001 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * Unified Hangul Code part 1
 */

static const unsigned short uhc_1_2uni_main_page81[64] = {
  0xac02, 0xac8d, 0xad14, 0xad91, 0xadfa, 0xae7a, 0xaee6, 0xaf57,
  0xafbf, 0xb030, 0xb0a5, 0xb122, 0xb19e, 0xb207, 0xb26f, 0xb2f0,
  0xb366, 0xb3e1, 0xb445, 0xb4ad, 0xb51e, 0xb590, 0xb600, 0xb661,
  0xb6c3, 0xb723, 0xb79f, 0xb811, 0xb885, 0xb8f1, 0xb95a, 0xb9d4,
  0xba47, 0xbac2, 0xbb28, 0xbb9a, 0xbc03, 0xbc80, 0xbcfe, 0xbd67,
  0xbdd2, 0xbe3d, 0xbeb8, 0xbf23, 0xbf83, 0xbfe6, 0xc040, 0xc0a7,
  0xc132, 0xc1b1, 0xc224, 0xc297, 0xc310, 0xc37a, 0xc3db, 0xc446,
  0xc4aa, 0xc50f, 0xc596, 0xc626, 0xc6a8, 0xc726, 0xc7b8, 0xc832,
};
static const unsigned char uhc_1_2uni_page81[5696] = {
  /* 0x81 */
  0x00, 0x01, 0x03, 0x04, 0x09, 0x0a, 0x0b, 0x0c,
  0x0d, 0x16, 0x1c, 0x1d, 0x1f, 0x20, 0x21, 0x23,
  0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2c, 0x30,
  0x31, 0x32, 0x33, 0x34, 0x35, 0x38, 0x39, 0x3b,
  0x3c, 0x3d, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44,
  0x45, 0x46, 0x47, 0x48, 0x4a, 0x4c, 0x4d, 0x4e,
  0x4f, 0x50, 0x51, 0x53, 0x54, 0x55, 0x57, 0x58,
  0x59, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x6b, 0x6c, 0x6d, 0x70, 0x71, 0x73, 0x74,
  0x77, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x80, 0x85,
  0x86, 0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x11, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1e, 0x20, 0x21,
  0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2d,
  0x31, 0x32, 0x33, 0x35, 0x36, 0x38, 0x39, 0x3a,
  0x3c, 0x3d, 0x3e, 0x40, 0x41, 0x42, 0x43, 0x44,
  0x45, 0x46, 0x47, 0x49, 0x4b, 0x4c, 0x4d, 0x4e,
  0x4f, 0x50, 0x51, 0x52, 0x55, 0x56, 0x58, 0x59,
  0x5c, 0x5e, 0x60, 0x61, 0x65, 0x67, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x71, 0x72, 0x74, 0x75, 0x76,
  0x78, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x81, 0x83,
  0x85, 0x86,
  /* 0x82 */
  0x00, 0x01, 0x02, 0x03, 0x05, 0x06, 0x07, 0x09,
  0x0a, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12,
  0x13, 0x14, 0x16, 0x17, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x22, 0x23, 0x25, 0x26, 0x27, 0x29,
  0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x32, 0x34,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3d, 0x3e,
  0x3f, 0x41, 0x42, 0x43, 0x45, 0x46, 0x47, 0x48,
  0x49, 0x4a, 0x4b, 0x4c, 0x4e, 0x50, 0x51, 0x52,
  0x53, 0x54, 0x55, 0x56, 0x57, 0x5a, 0x5b, 0x5d,
  0x5e, 0x63, 0x64, 0x65, 0x66, 0x6a, 0x6c, 0x6f,
  0x70, 0x71, 0x72, 0x73, 0x76, 0x77, 0x79, 0x7a,
  0x7b, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x22, 0x23, 0x24, 0x25, 0x27, 0x28, 0x29, 0x2a,
  0x2b, 0x2c, 0x2d, 0x2e, 0x31, 0x32, 0x34, 0x35,
  0x36, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e,
  0x41, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
  0x4a, 0x4c, 0x4d, 0x4e, 0x50, 0x51, 0x52, 0x54,
  0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c,
  0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64,
  0x65, 0x66,
  /* 0x83 */
  0x00, 0x01, 0x03, 0x04, 0x08, 0x09, 0x0a, 0x0b,
  0x0c, 0x0d, 0x10, 0x12, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
  0x31, 0x32, 0x33, 0x34, 0x35, 0x38, 0x39, 0x3b,
  0x3c, 0x3f, 0x41, 0x42, 0x43, 0x44, 0x45, 0x48,
  0x4a, 0x4d, 0x4e, 0x4f, 0x51, 0x55, 0x57, 0x58,
  0x59, 0x5b, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x64,
  0x68, 0x69, 0x6a, 0x6c, 0x6d, 0x70, 0x71, 0x73,
  0x74, 0x75, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c,
  0x7d, 0x00, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x13, 0x14,
  0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c,
  0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24,
  0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34,
  0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c,
  0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x45, 0x47, 0x48,
  0x49, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51,
  0x54, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x60,
  0x61, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x6b,
  /* 0x84 */
  0x00, 0x01, 0x03, 0x04, 0x06, 0x08, 0x09, 0x0a,
  0x0b, 0x0c, 0x0d, 0x0f, 0x10, 0x11, 0x13, 0x14,
  0x15, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x23, 0x24, 0x25, 0x26, 0x28,
  0x29, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
  0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
  0x42, 0x43, 0x44, 0x45, 0x48, 0x49, 0x4b, 0x4d,
  0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x58,
  0x5a, 0x5e, 0x5f, 0x60, 0x61, 0x64, 0x65, 0x66,
  0x67, 0x68, 0x69, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x00, 0x01, 0x02, 0x03, 0x04, 0x07, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0f, 0x10, 0x11, 0x12,
  0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1a,
  0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x23,
  0x24, 0x25, 0x26, 0x27, 0x28, 0x2a, 0x2b, 0x2c,
  0x2e, 0x2f, 0x30, 0x32, 0x33, 0x34, 0x35, 0x36,
  0x37, 0x38, 0x3b, 0x3c, 0x3d, 0x3f, 0x40, 0x41,
  0x42, 0x43, 0x44, 0x46, 0x47, 0x48, 0x49, 0x4a,
  0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52,
  0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a,
  0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x63, 0x64,
  0x66, 0x67,
  /* 0x85 */
  0x00, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x0b,
  0x0d, 0x10, 0x11, 0x12, 0x13, 0x14, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x33, 0x34,
  0x36, 0x37, 0x38, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e,
  0x3f, 0x40, 0x43, 0x44, 0x46, 0x47, 0x48, 0x49,
  0x4a, 0x4b, 0x4c, 0x4e, 0x4f, 0x50, 0x52, 0x53,
  0x54, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c,
  0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66,
  0x67, 0x68, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x16, 0x17, 0x19,
  0x1b, 0x1d, 0x1f, 0x20, 0x21, 0x22, 0x26, 0x28,
  0x2a, 0x2b, 0x2c, 0x2e, 0x2f, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a,
  0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42,
  0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a,
  0x4b, 0x4e, 0x4f, 0x51, 0x52, 0x53, 0x55, 0x56,
  0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5e, 0x60, 0x62,
  0x63, 0x64, 0x65, 0x66, 0x67, 0x6b, 0x6d, 0x6e,
  0x73, 0x74,
  /* 0x86 */
  0x00, 0x01, 0x02, 0x05, 0x0b, 0x0d, 0x11, 0x12,
  0x14, 0x15, 0x16, 0x18, 0x19, 0x1a, 0x1b, 0x1c,
  0x1d, 0x1e, 0x21, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2d, 0x2e, 0x30, 0x31, 0x32, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3c, 0x3d, 0x3e,
  0x3f, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x65, 0x68, 0x69, 0x6a, 0x6c,
  0x6f, 0x70, 0x71, 0x72, 0x75, 0x79, 0x7a, 0x7b,
  0x7c, 0x00, 0x04, 0x05, 0x07, 0x08, 0x09, 0x0b,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x14, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x20, 0x21, 0x23,
  0x24, 0x25, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x30, 0x31, 0x34, 0x35, 0x37, 0x38, 0x39,
  0x3b, 0x3c, 0x3d, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x58, 0x59, 0x5b, 0x5c, 0x5d, 0x5f,
  0x61, 0x62, 0x63, 0x64, 0x65, 0x68, 0x6a, 0x6c,
  0x6d, 0x6e, 0x6f, 0x73, 0x74, 0x75, 0x77, 0x78,
  0x79, 0x7b,
  /* 0x87 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2f, 0x30, 0x31,
  0x33, 0x34, 0x35, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x40, 0x42, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x48, 0x49, 0x4c, 0x4d, 0x4f, 0x50, 0x51,
  0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a,
  0x5c, 0x5e, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x68, 0x00, 0x02, 0x03, 0x06, 0x07, 0x08, 0x09,
  0x0a, 0x0b, 0x0c, 0x0f, 0x11, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x36, 0x37, 0x38,
  0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40,
  0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48,
  0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50,
  0x52, 0x53, 0x54, 0x56, 0x57, 0x58, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x63, 0x64, 0x65,
  0x66, 0x67,
  /* 0x88 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x07, 0x08, 0x09,
  0x0a, 0x0b, 0x0c, 0x0e, 0x0f, 0x10, 0x11, 0x12,
  0x13, 0x14, 0x17, 0x18, 0x19, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x23, 0x24, 0x26, 0x27, 0x28,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x33, 0x35, 0x38,
  0x39, 0x3a, 0x3c, 0x3e, 0x3f, 0x40, 0x42, 0x43,
  0x44, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c,
  0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54,
  0x55, 0x56, 0x57, 0x58, 0x5b, 0x5c, 0x5e, 0x5f,
  0x60, 0x62, 0x64, 0x65, 0x66, 0x67, 0x68, 0x6b,
  0x6d, 0x6f, 0x70, 0x71, 0x72, 0x74, 0x78, 0x7a,
  0x7b, 0x00, 0x01, 0x02, 0x06, 0x0c, 0x0d, 0x0e,
  0x12, 0x13, 0x15, 0x16, 0x17, 0x19, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x1f, 0x22, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2d, 0x2e, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
  0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
  0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
  0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x67, 0x69, 0x6a, 0x6d, 0x70, 0x71,
  0x72, 0x73,
  /* 0x89 */
  0x00, 0x02, 0x04, 0x06, 0x07, 0x09, 0x0c, 0x0d,
  0x0f, 0x10, 0x11, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1c, 0x20, 0x21, 0x22, 0x23, 0x24,
  0x25, 0x27, 0x28, 0x29, 0x2b, 0x2c, 0x2d, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
  0x43, 0x44, 0x45, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x60, 0x61, 0x63, 0x64, 0x67, 0x69,
  0x6b, 0x6c, 0x6d, 0x70, 0x72, 0x74, 0x76, 0x78,
  0x79, 0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x36, 0x38, 0x39, 0x3a, 0x3c,
  0x3d, 0x3e, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x49, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50,
  0x51, 0x52, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63,
  /* 0x8a */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0d, 0x0e, 0x10, 0x11, 0x12,
  0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1d,
  0x1f, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
  0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
  0x39, 0x3a, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
  0x42, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a,
  0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52,
  0x53, 0x54, 0x55, 0x56, 0x57, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x60, 0x61, 0x62, 0x64, 0x65,
  0x66, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x09, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x14, 0x15, 0x16, 0x18, 0x19, 0x1a, 0x1c,
  0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x24, 0x25,
  0x26, 0x27, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e,
  0x31, 0x32, 0x34, 0x35, 0x38, 0x3a, 0x3b, 0x3c,
  0x3d, 0x3e, 0x41, 0x43, 0x45, 0x46, 0x47, 0x48,
  0x49, 0x4a, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x69, 0x6a, 0x6c,
  0x6d, 0x70,
  /* 0x8b */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x08, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x14, 0x15, 0x17, 0x18,
  0x19, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x24, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x30, 0x31,
  0x33, 0x34, 0x35, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x40, 0x44, 0x45, 0x46, 0x47, 0x48,
  0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50,
  0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58,
  0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60,
  0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68,
  0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70,
  0x71, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x12, 0x13, 0x15, 0x16, 0x17, 0x19, 0x1c,
  0x1d, 0x1e, 0x1f, 0x22, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2e, 0x2f, 0x31, 0x32, 0x33, 0x35, 0x36,
  0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3e, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x49, 0x4a, 0x4b, 0x4c,
  0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54,
  0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5d,
  0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
  0x6e, 0x6f,
  /* 0x8c */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x12, 0x13, 0x15, 0x16, 0x17, 0x19, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x24, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x35, 0x36,
  0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e,
  0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
  0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
  0x39, 0x3a, 0x3d, 0x3e, 0x40, 0x41, 0x42, 0x44,
  0x45, 0x46, 0x47, 0x48, 0x49, 0x4c, 0x4d, 0x4e,
  0x4f, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61,
  /* 0x8d */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2e, 0x2f, 0x30, 0x32,
  0x33, 0x34, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3f, 0x40, 0x41, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e,
  0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56,
  0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e,
  0x5f, 0x00, 0x01, 0x02, 0x03, 0x04, 0x07, 0x08,
  0x0a, 0x0b, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
  0x14, 0x17, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x22, 0x23, 0x24, 0x26, 0x27, 0x28,
  0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3e, 0x3f, 0x40, 0x42, 0x43, 0x44, 0x46,
  0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4f, 0x51,
  0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x5b, 0x5c,
  0x5e, 0x5f, 0x60, 0x62, 0x63, 0x64, 0x65, 0x66,
  0x67, 0x68, 0x6b, 0x70, 0x71, 0x72, 0x77, 0x78,
  0x7a, 0x7b,
  /* 0x8e */
  0x00, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x0b, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x17,
  0x18, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x29,
  0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a,
  0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42,
  0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a,
  0x4b, 0x4c, 0x4f, 0x50, 0x52, 0x53, 0x54, 0x56,
  0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5f, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x6b, 0x6c, 0x6e, 0x6f,
  0x70, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x09, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12,
  0x15, 0x16, 0x18, 0x19, 0x1a, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x25, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x2e, 0x30, 0x31, 0x32, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x41, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4d, 0x4e, 0x50, 0x51, 0x52,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5d,
  0x5f, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x68,
  0x69, 0x6a, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71,
  0x72, 0x73,
  /* 0x8f */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2c, 0x2d, 0x2e, 0x30, 0x31, 0x32, 0x34,
  0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3d, 0x3f,
  0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x48, 0x49,
  0x4a, 0x4c, 0x4d, 0x4e, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x59, 0x5b, 0x5d, 0x5e,
  0x5f, 0x60, 0x61, 0x62, 0x65, 0x66, 0x68, 0x69,
  0x6a, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x09, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12,
  0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x24, 0x25, 0x26, 0x28, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x2e, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4d, 0x4e, 0x50,
  0x51, 0x52, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5c, 0x5d, 0x5f, 0x61, 0x62, 0x63, 0x64,
  0x65, 0x66,
  /* 0x90 */
  0x00, 0x01, 0x03, 0x04, 0x05, 0x07, 0x08, 0x09,
  0x0a, 0x0b, 0x0c, 0x0d, 0x10, 0x12, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1c, 0x1d, 0x1f, 0x20,
  0x21, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2c, 0x2e, 0x31, 0x32, 0x35, 0x36, 0x37, 0x38,
  0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40,
  0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48,
  0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50,
  0x51, 0x54, 0x55, 0x57, 0x58, 0x59, 0x5b, 0x5c,
  0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x64, 0x66, 0x68,
  0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x70, 0x71, 0x73,
  0x79, 0x00, 0x01, 0x02, 0x03, 0x06, 0x08, 0x0b,
  0x0c, 0x0e, 0x12, 0x13, 0x15, 0x16, 0x17, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x22, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2e, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a,
  0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x66, 0x67, 0x69, 0x6a, 0x6b, 0x6d, 0x6f, 0x70,
  0x71, 0x72,
  /* 0x91 */
  0x00, 0x03, 0x05, 0x08, 0x09, 0x0a, 0x0b, 0x0f,
  0x10, 0x12, 0x13, 0x14, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1f, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x2b, 0x2c, 0x2e, 0x2f, 0x30, 0x32,
  0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a,
  0x3b, 0x3f, 0x41, 0x42, 0x43, 0x44, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x63, 0x66, 0x67, 0x68, 0x6a, 0x6c, 0x6d,
  0x6e, 0x6f, 0x70, 0x73, 0x75, 0x77, 0x78, 0x79,
  0x7a, 0x00, 0x01, 0x03, 0x04, 0x05, 0x07, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x18, 0x19, 0x1a,
  0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22,
  0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a,
  0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3b,
  0x3c, 0x3d, 0x3f, 0x40, 0x41, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4c, 0x4e, 0x50,
  0x51, 0x52, 0x53, 0x54, 0x55, 0x57, 0x58, 0x59,
  0x5b, 0x5c, 0x5d, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65,
  /* 0x92 */
  0x00, 0x02, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
  0x0a, 0x0b, 0x0f, 0x11, 0x12, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1e, 0x20, 0x22, 0x23, 0x24, 0x26,
  0x29, 0x2a, 0x2b, 0x2d, 0x2e, 0x2f, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x3a, 0x3c,
  0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x61, 0x62, 0x63, 0x65, 0x66, 0x67,
  0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70,
  0x71, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0b, 0x0c, 0x0d, 0x0f, 0x10,
  0x11, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1b, 0x1c, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x24, 0x25, 0x27, 0x28, 0x29, 0x2b, 0x2c, 0x2d,
  0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x37,
  0x38, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40,
  0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48,
  0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50,
  0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58,
  0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x60, 0x61, 0x63,
  0x64, 0x67,
  /* 0x93 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x07, 0x0b, 0x0d,
  0x0f, 0x10, 0x16, 0x17, 0x1d, 0x1e, 0x1f, 0x20,
  0x23, 0x25, 0x27, 0x28, 0x29, 0x2b, 0x2c, 0x2f,
  0x30, 0x32, 0x33, 0x34, 0x36, 0x37, 0x38, 0x39,
  0x3a, 0x3b, 0x3c, 0x3f, 0x43, 0x44, 0x45, 0x47,
  0x48, 0x4b, 0x4c, 0x4e, 0x4f, 0x50, 0x51, 0x52,
  0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x00, 0x01, 0x02, 0x03, 0x06, 0x07, 0x09,
  0x0a, 0x0d, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x16,
  0x18, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x22, 0x23,
  0x25, 0x26, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x32, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3e, 0x3f, 0x41, 0x42, 0x43, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4e, 0x52, 0x53,
  0x54, 0x56, 0x57, 0x59, 0x5a, 0x5b, 0x5d, 0x5e,
  0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66,
  0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e,
  0x6f, 0x70, 0x71, 0x72, 0x73, 0x77, 0x79, 0x7a,
  0x7b, 0x7d,
  /* 0x94 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x08, 0x0a,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x13, 0x14,
  0x15, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48,
  0x49, 0x4c, 0x4d, 0x4f, 0x50, 0x51, 0x53, 0x54,
  0x55, 0x56, 0x57, 0x58, 0x59, 0x5c, 0x5d, 0x5e,
  0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x67,
  0x68, 0x00, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x1b, 0x1c, 0x1e, 0x1f, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x2b, 0x2d, 0x2f, 0x30, 0x31, 0x34,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3e,
  0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x48, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58,
  0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60,
  0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68,
  0x69, 0x6a,
  /* 0x95 */
  0x00, 0x01, 0x04, 0x05, 0x07, 0x08, 0x09, 0x0b,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
  0x14, 0x15, 0x16, 0x18, 0x19, 0x1a, 0x1b, 0x1c,
  0x1d, 0x1f, 0x20, 0x21, 0x23, 0x24, 0x25, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2f, 0x30,
  0x32, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3c,
  0x3d, 0x3f, 0x40, 0x41, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x48, 0x49, 0x4c, 0x4e, 0x4f, 0x50, 0x51,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x09, 0x0a, 0x0c, 0x0d, 0x0e, 0x10, 0x12, 0x13,
  0x14, 0x15, 0x16, 0x19, 0x1b, 0x1f, 0x20, 0x21,
  0x22, 0x25, 0x26, 0x28, 0x29, 0x2a, 0x2c, 0x2e,
  0x2f, 0x30, 0x31, 0x32, 0x35, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x41, 0x42, 0x44, 0x45, 0x46, 0x48,
  0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x51, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67,
  0x68, 0x69, 0x6a, 0x6c, 0x6d, 0x6e, 0x6f, 0x70,
  0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78,
  0x79, 0x7a,
  /* 0x96 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x1a, 0x1b, 0x1d, 0x1e, 0x21, 0x22, 0x23, 0x24,
  0x25, 0x26, 0x27, 0x29, 0x2a, 0x2e, 0x2f, 0x30,
  0x31, 0x32, 0x33, 0x35, 0x36, 0x37, 0x38, 0x39,
  0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
  0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x4a,
  0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x52, 0x53, 0x54,
  0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c,
  0x5d, 0x5e, 0x5f, 0x62, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1f, 0x20,
  0x22, 0x23, 0x24, 0x26, 0x27, 0x28, 0x29, 0x2a,
  0x2b, 0x2c, 0x2f, 0x30, 0x31, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f,
  /* 0x97 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2e, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
  0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
  0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x4b, 0x4c,
  0x4e, 0x4f, 0x50, 0x52, 0x53, 0x54, 0x55, 0x56,
  0x57, 0x58, 0x5a, 0x5b, 0x5d, 0x5f, 0x60, 0x61,
  0x62, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36,
  0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e,
  0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e,
  0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x57,
  0x58, 0x59,
  /* 0x98 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x19,
  0x1a, 0x1b, 0x1d, 0x1e, 0x1f, 0x21, 0x22, 0x23,
  0x24, 0x25, 0x26, 0x27, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x52, 0x53, 0x55, 0x56, 0x57, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x62, 0x64,
  0x66, 0x00, 0x01, 0x02, 0x03, 0x04, 0x07, 0x0a,
  0x0b, 0x10, 0x11, 0x12, 0x13, 0x14, 0x17, 0x1b,
  0x1c, 0x1d, 0x1f, 0x20, 0x23, 0x24, 0x26, 0x27,
  0x28, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
  0x33, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3f,
  0x40, 0x42, 0x43, 0x44, 0x46, 0x47, 0x48, 0x49,
  0x4a, 0x4b, 0x4c, 0x4f, 0x51, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x5a, 0x5b, 0x5c, 0x5e, 0x5f,
  0x60, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68,
  0x6a, 0x6b, 0x6c, 0x6d, 0x6f, 0x70, 0x71, 0x72,
  0x73, 0x74, 0x7a, 0x7b, 0x7e, 0x81, 0x82, 0x83,
  0x84, 0x87,
  /* 0x99 */
  0x00, 0x01, 0x02, 0x03, 0x05, 0x08, 0x09, 0x0b,
  0x0c, 0x0d, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14,
  0x15, 0x18, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x24, 0x25, 0x27, 0x28, 0x29, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x34, 0x38, 0x39, 0x3a,
  0x3b, 0x3c, 0x3d, 0x3f, 0x40, 0x41, 0x43, 0x44,
  0x45, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x54, 0x55, 0x56,
  0x57, 0x58, 0x59, 0x5d, 0x5f, 0x60, 0x61, 0x63,
  0x65, 0x66, 0x67, 0x68, 0x69, 0x6c, 0x6e, 0x70,
  0x71, 0x72, 0x74, 0x75, 0x78, 0x79, 0x7b, 0x7c,
  0x7d, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x14, 0x15, 0x16, 0x18, 0x19,
  0x1a, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22,
  0x24, 0x25, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x30, 0x31, 0x32, 0x34, 0x35, 0x36, 0x38,
  0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x41, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4d,
  0x4e, 0x50, 0x51, 0x52, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5d, 0x5f, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x69, 0x6a, 0x6c, 0x6d, 0x70,
  0x71, 0x72,
  /* 0x9a */
  0x00, 0x01, 0x02, 0x03, 0x06, 0x08, 0x0a, 0x0c,
  0x0f, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2e, 0x2f, 0x31, 0x32, 0x33,
  0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3d,
  0x3e, 0x3f, 0x40, 0x42, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x4a, 0x4b, 0x4d, 0x4e, 0x4f, 0x51, 0x52,
  0x53, 0x54, 0x55, 0x56, 0x57, 0x5a, 0x5c, 0x5e,
  0x5f, 0x60, 0x61, 0x62, 0x63, 0x66, 0x67, 0x68,
  0x69, 0x6a, 0x6b, 0x6d, 0x6e, 0x6f, 0x70, 0x71,
  0x72, 0x00, 0x02, 0x03, 0x05, 0x07, 0x08, 0x09,
  0x0a, 0x0b, 0x0c, 0x0f, 0x10, 0x12, 0x13, 0x14,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1f, 0x21,
  0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a,
  0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a,
  0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42,
  0x43, 0x44, 0x47, 0x48, 0x4a, 0x4b, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x57, 0x59, 0x5b, 0x5c,
  0x5d, 0x5e, 0x60, 0x63, 0x66, 0x67, 0x68, 0x6a,
  0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x73, 0x74,
  0x77, 0x78,
  /* 0x9b */
  0x00, 0x01, 0x02, 0x06, 0x07, 0x09, 0x0a, 0x0b,
  0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x16,
  0x17, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
  0x31, 0x32, 0x33, 0x34, 0x36, 0x37, 0x38, 0x39,
  0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
  0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
  0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x5a, 0x5b,
  0x5d, 0x5e, 0x5f, 0x61, 0x63, 0x64, 0x65, 0x66,
  0x67, 0x00, 0x01, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0b, 0x0c, 0x0d, 0x0f, 0x10, 0x11, 0x13,
  0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c,
  0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54,
  0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c,
  0x5d, 0x60,
  /* 0x9c */
  0x00, 0x02, 0x03, 0x06, 0x08, 0x09, 0x0a, 0x0b,
  0x0c, 0x0f, 0x10, 0x11, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x1b, 0x1c, 0x1e, 0x1f, 0x20, 0x21,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x2c, 0x2e, 0x2f, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x4a, 0x4b, 0x4c,
  0x4d, 0x4e, 0x4f, 0x50, 0x52, 0x53, 0x54, 0x56,
  0x57, 0x58, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x00, 0x01, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x20, 0x21,
  0x23, 0x24, 0x25, 0x27, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x30, 0x31, 0x32, 0x34, 0x35, 0x36,
  0x37, 0x38, 0x39, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4f, 0x50,
  0x51, 0x52, 0x53, 0x54, 0x55, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63,
  /* 0x9d */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0f, 0x10,
  0x11, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
  0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x40, 0x41, 0x42,
  0x43, 0x44, 0x45, 0x48, 0x49, 0x4b, 0x4c, 0x4d,
  0x4f, 0x51, 0x52, 0x53, 0x54, 0x58, 0x59, 0x5a,
  0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x63,
  0x64, 0x00, 0x02, 0x03, 0x04, 0x06, 0x07, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x1b,
  0x1c, 0x1e, 0x1f, 0x20, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x2b, 0x2d, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x34, 0x37, 0x38, 0x3c, 0x40, 0x41,
  0x42, 0x43, 0x47, 0x4b, 0x4c, 0x4d, 0x50, 0x53,
  0x54, 0x56, 0x57, 0x58, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x60, 0x63, 0x67, 0x68, 0x69, 0x6a,
  0x6b, 0x6c, 0x6f, 0x70, 0x72, 0x73, 0x74, 0x76,
  0x77, 0x79, 0x7a, 0x7b, 0x7c, 0x7f, 0x81, 0x83,
  0x84, 0x85,
  /* 0x9e */
  0x00, 0x03, 0x04, 0x05, 0x07, 0x08, 0x09, 0x0b,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x14,
  0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c,
  0x1d, 0x20, 0x21, 0x24, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x35, 0x37, 0x39, 0x3c, 0x3d, 0x3f, 0x40,
  0x41, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
  0x4c, 0x4e, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x59, 0x5b, 0x5c, 0x5d, 0x5f, 0x62, 0x63, 0x64,
  0x65, 0x6c, 0x6d, 0x6e, 0x73, 0x74, 0x75, 0x77,
  0x78, 0x79, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80,
  0x81, 0x84, 0x87, 0x88, 0x89, 0x8a, 0x8b, 0x8c,
  0x8d, 0x00, 0x01, 0x03, 0x04, 0x05, 0x09, 0x0b,
  0x0c, 0x10, 0x12, 0x14, 0x16, 0x17, 0x18, 0x19,
  0x1c, 0x1d, 0x1f, 0x20, 0x21, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2c, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x38, 0x39, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x47, 0x48, 0x4a, 0x4c, 0x4d, 0x4e, 0x4f, 0x50,
  0x51, 0x54, 0x55, 0x57, 0x58, 0x59, 0x5b, 0x5c,
  0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x64, 0x66, 0x68,
  0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x70, 0x71, 0x73,
  0x74, 0x75, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c,
  0x7d, 0x80,
  /* 0x9f */
  0x00, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x0a,
  0x0b, 0x0d, 0x0e, 0x0f, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x1a, 0x1c, 0x1e, 0x1f, 0x20, 0x21, 0x22,
  0x23, 0x26, 0x27, 0x29, 0x2a, 0x2b, 0x2d, 0x2e,
  0x2f, 0x30, 0x31, 0x32, 0x33, 0x36, 0x37, 0x3a,
  0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x42, 0x43, 0x45,
  0x46, 0x47, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e,
  0x4f, 0x52, 0x53, 0x54, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5e, 0x5f, 0x61, 0x62, 0x63, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6e, 0x70,
  0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x7a, 0x7b,
  0x7d, 0x00, 0x01, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0c, 0x0e, 0x10, 0x12, 0x13, 0x14,
  0x15, 0x18, 0x19, 0x1b, 0x1c, 0x1d, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x25, 0x28, 0x2a, 0x33, 0x34,
  0x35, 0x37, 0x38, 0x39, 0x3b, 0x3c, 0x3d, 0x3e,
  0x3f, 0x40, 0x41, 0x43, 0x44, 0x46, 0x47, 0x48,
  0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x50, 0x51, 0x53,
  0x54, 0x55, 0x59, 0x5a, 0x5b, 0x5c, 0x60, 0x65,
  0x66, 0x67, 0x69, 0x6c, 0x6d, 0x6f, 0x73, 0x75,
  0x76, 0x77, 0x78, 0x79, 0x7c, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x88, 0x89, 0x8b, 0x8c, 0x8d, 0x8f,
  0x90, 0x91,
  /* 0xa0 */
  0x00, 0x01, 0x02, 0x03, 0x06, 0x0a, 0x0b, 0x0c,
  0x0d, 0x0e, 0x0f, 0x12, 0x13, 0x15, 0x17, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x21, 0x22,
  0x23, 0x24, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b,
  0x2d, 0x2e, 0x2f, 0x31, 0x32, 0x33, 0x35, 0x36,
  0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e,
  0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46,
  0x47, 0x4a, 0x4b, 0x4d, 0x4e, 0x4f, 0x51, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x5a, 0x5c, 0x5f, 0x60,
  0x61, 0x62, 0x63, 0x66, 0x67, 0x69, 0x6a, 0x6b,
  0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x76,
  0x78, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x07,
  0x08, 0x09, 0x0b, 0x0c, 0x0d, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x18, 0x19, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36,
  0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x40,
  0x41, 0x43, 0x44, 0x45, 0x47, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x50, 0x52, 0x56, 0x57, 0x58, 0x5c,
  0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6c, 0x6e, 0x70,
  0x71, 0x72,
};

static int
uhc_1_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x81 && c1 <= 0xa0)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if ((c2 >= 0x41 && c2 < 0x5b) || (c2 >= 0x61 && c2 < 0x7b) || (c2 >= 0x81 && c2 < 0xff)) {
        unsigned int row = c1 - 0x81;
        unsigned int col = c2 - (c2 >= 0x81 ? 0x4d : c2 >= 0x61 ? 0x47 : 0x41);
        unsigned int i = 178 * row + col;
        if (i < 5696) {
          *pwc = (ucs4_t) (uhc_1_2uni_main_page81[2*row+(col>=89?1:0)] + uhc_1_2uni_page81[i]);
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short uhc_1_2charset_main[45] = {
  0x8141, 0x81cd, 0x829b, 0x8363, 0x83e9, 0x84b7, 0x8585, 0x8647,
  0x86d3, 0x87a1, 0x8869, 0x88ef, 0x89bd, 0x8a8b, 0x8b4d, 0x8bd9,
  0x8ca7, 0x8d6f, 0x8df5, 0x8ec3, 0x8f91, 0x9053, 0x90df, 0x91ad,
  0x9275, 0x92fb, 0x93c9, 0x9497, 0x9559, 0x95e5, 0x96b3, 0x9781,
  0x9843, 0x98cf, 0x999d, 0x9a65, 0x9aeb, 0x9bb9, 0x9c87, 0x9d49,
  0x9dd5, 0x9ea3, 0x9f6b, 0x9ff1, 0xa0bf,
};
static const unsigned char uhc_1_2charset[5696] = {
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
  0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81,
  0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
  0x8a, 0x8b, 0x8c, 0x8d, 0x94, 0x95, 0x96, 0x97,
  0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f,
  0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0xa6, 0xa7, 0xa8, 0xa9,
  0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf, 0xb0, 0xb1,
  0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7, 0xb8, 0xb9,
  0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf, 0xc6, 0xc7,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
  0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75,
  0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d,
  0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71,
  0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f,
  0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
  0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
  0x90, 0x91, 0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d,
  0x9e, 0x9f, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f, 0x90, 0x91,
  0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99,
  0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f, 0xa0, 0xa1,
  0xa2, 0xa3, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67,
  0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
  0x78, 0x79, 0xbc, 0xbd, 0xbe, 0xbf, 0xc0, 0xc1,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x6e, 0x6f, 0x70, 0x71,
  0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
  0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81,
  0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x8e, 0x8f,
  0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
  0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f,
  0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0xa0, 0xa1,
  0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9,
  0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf, 0xb0, 0xb1,
  0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7, 0xb8, 0xb9,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
  0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75,
  0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d,
  0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x6b, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
  0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f,
  0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
  0x88, 0x89, 0x8a, 0x8b, 0x92, 0x93, 0x94, 0x95,
  0x96, 0x97, 0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d,
  0x9e, 0x9f, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
  0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f, 0x90, 0x91,
  0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99,
  0x9a, 0x9b, 0x9c, 0x9d, 0xa4, 0xa5, 0xa6, 0xa7,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67,
  0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x71, 0x72, 0x73, 0xb6, 0xb7, 0xb8, 0xb9,
  0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf, 0xc0, 0xc1,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x68, 0x69,
  0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71,
  0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
  0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81,
  0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
  0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
  0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f,
  0xa0, 0xa1, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f, 0xa0, 0xa1,
  0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9,
  0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf, 0xb0, 0xb1,
  0xb2, 0xb3, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
  0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75,
  0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d,
  0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
  0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f,
  0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x8c, 0x8d,
  0x8e, 0x8f, 0x90, 0x91, 0x92, 0x93, 0x94, 0x95,
  0x96, 0x97, 0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d,
  0x9e, 0x9f, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x7e, 0x7f, 0x80, 0x81,
  0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
  0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f, 0x90, 0x91,
  0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x9e, 0x9f,
  0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67,
  0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0xb0, 0xb1,
  0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7, 0xb8, 0xb9,
  0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf, 0xc0, 0xc1,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71,
  0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
  0x7a, 0x7b, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
  0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
  0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
  0x98, 0x99, 0x9a, 0x9b, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99,
  0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f, 0xa0, 0xa1,
  0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9,
  0xaa, 0xab, 0xac, 0xad, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
  0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75,
  0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d,
  0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85,
  0x00, 0x01, 0x02, 0x03, 0x46, 0x47, 0x48, 0x49,
  0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x66, 0x67,
  0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
  0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f,
  0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d,
  0x8e, 0x8f, 0x90, 0x91, 0x92, 0x93, 0x94, 0x95,
  0x96, 0x97, 0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d,
  0x9e, 0x9f, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x78, 0x79,
  0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81,
  0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
  0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f, 0x90, 0x91,
  0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f,
  0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67,
  0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf, 0xb0, 0xb1,
  0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7, 0xb8, 0xb9,
  0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf, 0xc0, 0xc1,
  0x00, 0x01, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71,
  0x72, 0x73, 0x74, 0x75, 0x7c, 0x7d, 0x7e, 0x7f,
  0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
  0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
  0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x9c, 0x9d,
  0x9e, 0x9f, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x8e, 0x8f, 0x90, 0x91,
  0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99,
  0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f, 0xa0, 0xa1,
  0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67,
  0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
  0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0xc0, 0xc1,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
  0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81,
  0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
  0x8a, 0x8b, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
  0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f,
  0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7,
  0xa8, 0xa9, 0xaa, 0xab, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9,
  0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf, 0xb0, 0xb1,
  0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7, 0xb8, 0xb9,
  0xba, 0xbb, 0xbc, 0xbd, 0xc4, 0xc5, 0xc6, 0xc7,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
  0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75,
  0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d,
  0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x76, 0x77,
  0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f,
  0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
  0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f,
  0x96, 0x97, 0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d,
  0x9e, 0x9f, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x88, 0x89,
  0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f, 0x90, 0x91,
  0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99,
  0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f, 0xa0, 0xa1,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f,
  0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67,
  0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f,
  0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
  0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf, 0xc0, 0xc1,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53,
  0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b,
  0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
  0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b,
  0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73,
  0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b,
  0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83,
  0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71,
  0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
  0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81,
  0x82, 0x83, 0x84, 0x85, 0x8c, 0x8d, 0x8e, 0x8f,
  0x90, 0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97,
  0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d, 0x9e, 0x9f,
  0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
  0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47,
  0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
  0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57,
  0x58, 0x59, 0x5a, 0x5b, 0x9e, 0x9f, 0xa0, 0xa1,
  0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9,
  0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf, 0xb0, 0xb1,
  0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43, 0x44, 0x45,
  0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
  0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55,
  0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d,
  0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63, 0x64, 0x65,
  0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
  0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75,
  0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d,
  0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x50, 0x51,
  0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
  0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61,
  0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
  0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77,
  0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f,
  0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
  0x88, 0x89, 0x90, 0x91, 0x92, 0x93, 0x94, 0x95,
  0x96, 0x97, 0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9d,
  0x9e, 0x9f, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5,
  0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad,
  0xae, 0xaf, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5,
  0xb6, 0xb7, 0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd,
  0xbe, 0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f,
};

static const Summary16 uhc_1_uni2indx_pageac[459] = {
  /* 0xac00 */
  {    0, 0xf86c }, {    9, 0xc100 }, {   12, 0x4fee }, {   23, 0xecfc },
  {   34, 0xd7fe }, {   47, 0xeeef }, {   60, 0xffff }, {   76, 0xfa6c },
  {   86, 0xe184 }, {   92, 0x4fee }, {  103, 0x68fc }, {  112, 0xc4fe },
  {  122, 0xeeed }, {  134, 0xff5f }, {  148, 0x6a6c }, {  156, 0xcf94 },
  /* 0xad00 */
  {  165, 0x4fae }, {  175, 0xeefd }, {  188, 0xcdfe }, {  200, 0xeecf },
  {  212, 0xfd4f }, {  224, 0xfeee }, {  237, 0xcff5 }, {  249, 0x4786 },
  {  256, 0xecf9 }, {  267, 0xcffe }, {  280, 0xffef }, {  295, 0xff7f },
  {  310, 0xfeec }, {  322, 0xeff4 }, {  334, 0xffee }, {  348, 0x6cff },
  /* 0xae00 */
  {  360, 0xd4fc }, {  370, 0xffef }, {  385, 0xffff }, {  401, 0xfa6c },
  {  411, 0x8b94 }, {  418, 0x4fae }, {  428, 0xecdc }, {  438, 0xc4fe },
  {  448, 0xefcf }, {  461, 0xffff }, {  477, 0xffff }, {  493, 0x8fff },
  {  506, 0x4fee }, {  517, 0xecfc }, {  528, 0xd6ff }, {  541, 0xeeef },
  /* 0xaf00 */
  {  554, 0xde7f }, {  567, 0xfffe }, {  582, 0xcfff }, {  596, 0x4fea },
  {  606, 0xfcf1 }, {  617, 0xcffe }, {  630, 0xffcf }, {  644, 0xfdff },
  {  659, 0xfeee }, {  672, 0xefdc }, {  684, 0xffff }, {  700, 0xecff },
  {  713, 0x947e }, {  722, 0xefef }, {  736, 0xfcff }, {  750, 0xfeec },
  /* 0xb000 */
  {  762, 0xefec }, {  774, 0xcfee }, {  786, 0xfeff }, {  801, 0xffff },
  {  817, 0xaacf }, {  827, 0xdd47 }, {  837, 0xffff }, {  853, 0xcfff },
  {  867, 0x4fee }, {  878, 0x68fd }, {  888, 0x04f8 }, {  894, 0xeec5 },
  {  904, 0xfc4f }, {  915, 0xfeec }, {  927, 0xffde }, {  941, 0xffff },
  /* 0xb100 */
  {  957, 0xe4ff }, {  969, 0xc4f2 }, {  977, 0xeec7 }, {  988, 0xfc4f },
  {  999, 0xfeec }, { 1011, 0xeecc }, { 1021, 0xfffe }, { 1036, 0xecff },
  { 1049, 0xd4fa }, { 1059, 0xeee3 }, { 1070, 0xfeff }, { 1085, 0xffff },
  { 1101, 0xefff }, { 1116, 0x4fee }, { 1127, 0xecff }, { 1140, 0xd5fe },
  /* 0xb200 */
  { 1152, 0xe6cf }, { 1163, 0xfd4f }, { 1175, 0xfffe }, { 1190, 0xefef },
  { 1204, 0xffff }, { 1220, 0xeeff }, { 1234, 0xfcfe }, { 1247, 0xefcf },
  { 1260, 0xfdcf }, { 1273, 0xf8ec }, { 1283, 0xeb94 }, { 1292, 0xffee },
  { 1306, 0xecff }, { 1319, 0xd4fa }, { 1329, 0x068b }, { 1335, 0x7047 },
  /* 0xb300 */
  { 1342, 0xfeec }, { 1354, 0xefc4 }, { 1364, 0xffff }, { 1380, 0xffff },
  { 1396, 0xffff }, { 1412, 0x268f }, { 1420, 0xb54f }, { 1430, 0xfeec },
  { 1442, 0xefc4 }, { 1452, 0xffee }, { 1466, 0xeefc }, { 1478, 0xffff },
  { 1494, 0xa6cf }, { 1504, 0xd54e }, { 1513, 0xfeee }, { 1526, 0xefff },
  /* 0xb400 */
  { 1541, 0xffff }, { 1557, 0xeefe }, { 1570, 0xf4fe }, { 1582, 0xffef },
  { 1597, 0xffff }, { 1613, 0xfeec }, { 1625, 0xefd4 }, { 1636, 0xffff },
  { 1652, 0xfefe }, { 1666, 0xdfff }, { 1681, 0xeeef }, { 1694, 0xfd5f },
  { 1707, 0xfeee }, { 1720, 0xcfde }, { 1732, 0x4fa6 }, { 1741, 0xfefd },
  /* 0xb500 */
  { 1755, 0xffff }, { 1771, 0xe6cf }, { 1782, 0xf84f }, { 1792, 0xfeec },
  { 1804, 0xc7c4 }, { 1812, 0x4fee }, { 1823, 0xfffc }, { 1837, 0xffff },
  { 1853, 0xffff }, { 1869, 0xffff }, { 1885, 0xf2ec }, { 1895, 0xc7c4 },
  { 1903, 0x4fee }, { 1914, 0xfefc }, { 1927, 0xefff }, { 1942, 0xffff },
  /* 0xb600 */
  { 1958, 0xffff }, { 1974, 0xfeec }, { 1986, 0xefdf }, { 2000, 0xffef },
  { 2015, 0xfeff }, { 2030, 0xffff }, { 2046, 0xfeef }, { 2060, 0xffff },
  { 2076, 0xffff }, { 2092, 0xcfff }, { 2106, 0xe7ee }, { 2118, 0xfffd },
  { 2133, 0xffff }, { 2149, 0xffef }, { 2164, 0xffff }, { 2180, 0xfeee },
  /* 0xb700 */
  { 2193, 0xffdc }, { 2206, 0xffff }, { 2222, 0x6cff }, { 2234, 0xf4fe },
  { 2246, 0xeeef }, { 2259, 0xffcf }, { 2273, 0xfeee }, { 2286, 0xcfd4 },
  { 2296, 0x4fee }, { 2307, 0xec38 }, { 2315, 0xc4fe }, { 2325, 0xfecf },
  { 2338, 0xfd7f }, { 2352, 0xffff }, { 2368, 0xcfff }, { 2382, 0x4fee },
  /* 0xb800 */
  { 2393, 0xec7c }, { 2403, 0xd4fe }, { 2414, 0xeecf }, { 2426, 0xfc4f },
  { 2437, 0xffee }, { 2451, 0xcff5 }, { 2463, 0x4fee }, { 2474, 0xeefd },
  { 2487, 0xdfff }, { 2502, 0xffff }, { 2518, 0xfeff }, { 2533, 0xfeee },
  { 2546, 0xefd4 }, { 2557, 0x5fee }, { 2569, 0xecfd }, { 2581, 0xd4fe },
  /* 0xb900 */
  { 2592, 0xffef }, { 2607, 0xfeff }, { 2622, 0xfffe }, { 2637, 0xcfff },
  { 2651, 0x6fee }, { 2663, 0xecfd }, { 2675, 0xd4fe }, { 2686, 0xeecf },
  { 2698, 0x994f }, { 2707, 0xffff }, { 2723, 0xcfff }, { 2737, 0x4fee },
  { 2748, 0x2cfd }, { 2758, 0x94f8 }, { 2766, 0xeec5 }, { 2776, 0xf84f },
  /* 0xba00 */
  { 2786, 0xfefc }, { 2799, 0xffdf }, { 2814, 0xffff }, { 2830, 0xecff },
  { 2843, 0x94fa }, { 2852, 0xeec7 }, { 2863, 0xfc4f }, { 2874, 0xfeec },
  { 2886, 0xef47 }, { 2897, 0xffff }, { 2913, 0xe4ff }, { 2925, 0xd4fa },
  { 2935, 0xfeef }, { 2949, 0xfcff }, { 2963, 0xffff }, { 2979, 0xefff },
  /* 0xbb00 */
  { 2994, 0x5fee }, { 3006, 0xeefd }, { 3019, 0xf5fe }, { 3032, 0x868f },
  { 3040, 0x5d4f }, { 3050, 0xfeee }, { 3063, 0xeff5 }, { 3076, 0xffff },
  { 3092, 0xeeff }, { 3106, 0xfffe }, { 3121, 0xeeef }, { 3134, 0xff6f },
  { 3148, 0xfeee }, { 3161, 0xfff6 }, { 3175, 0xffff }, { 3191, 0x6cff },
  /* 0xbc00 */
  { 3203, 0x44fa }, { 3211, 0x060d }, { 3216, 0xdd4f }, { 3227, 0xfeec },
  { 3239, 0xcdc4 }, { 3247, 0xdffe }, { 3261, 0xffff }, { 3277, 0xffff },
  { 3293, 0xa6cf }, { 3303, 0xf94f }, { 3314, 0xfe6c }, { 3325, 0xcfc4 },
  { 3334, 0x5fee }, { 3346, 0xeedc }, { 3357, 0xffff }, { 3373, 0xee8f },
  /* 0xbd00 */
  { 3384, 0xfd4f }, { 3396, 0xffee }, { 3410, 0xefef }, { 3424, 0xffff },
  { 3440, 0xecfe }, { 3452, 0xfcfe }, { 3465, 0xfeef }, { 3479, 0xffff },
  { 3495, 0xf86c }, { 3504, 0xe9d4 }, { 3513, 0xffef }, { 3528, 0xfefe },
  { 3542, 0xffff }, { 3558, 0xeecf }, { 3570, 0xfdff }, { 3585, 0xfeee },
  /* 0xbe00 */
  { 3598, 0xcfd6 }, { 3609, 0x4fee }, { 3620, 0xffff }, { 3636, 0xffff },
  { 3652, 0xaecf }, { 3663, 0xf14f }, { 3673, 0xfaec }, { 3684, 0xc7c4 },
  { 3692, 0x4fee }, { 3703, 0xfcfc }, { 3715, 0xfeff }, { 3730, 0xffff },
  { 3746, 0xffff }, { 3762, 0xfe6c }, { 3773, 0xefc6 }, { 3784, 0xffff },
  /* 0xbf00 */
  { 3800, 0xfcfd }, { 3813, 0xc4ff }, { 3824, 0xffff }, { 3840, 0xffff },
  { 3856, 0xfeec }, { 3868, 0xffdc }, { 3881, 0xffff }, { 3897, 0xffff },
  { 3913, 0xffff }, { 3929, 0xffef }, { 3944, 0xffff }, { 3960, 0xfffe },
  { 3975, 0xcfdf }, { 3988, 0x6fee }, { 4000, 0xfffd }, { 4015, 0xffff },
  /* 0xc000 */
  { 4031, 0xffff }, { 4047, 0xffff }, { 4063, 0xffff }, { 4079, 0xefff },
  { 4094, 0xffff }, { 4110, 0xeefd }, { 4123, 0xfcfe }, { 4136, 0xffff },
  { 4152, 0xffff }, { 4168, 0xfeec }, { 4180, 0x4fd4 }, { 4189, 0x4f86 },
  { 4197, 0xecdc }, { 4207, 0xc4fe }, { 4217, 0xeecf }, { 4229, 0xfd4f },
  /* 0xc100 */
  { 4241, 0xfeee }, { 4254, 0x0fde }, { 4264, 0x4f26 }, { 4272, 0xecbc },
  { 4282, 0xc4fe }, { 4292, 0xeecf }, { 4304, 0xfc4f }, { 4315, 0xfeee },
  { 4328, 0x8fdf }, { 4340, 0x4fae }, { 4350, 0xecdd }, { 4361, 0xdffe },
  { 4375, 0xeeef }, { 4388, 0xfe6f }, { 4401, 0xfeee }, { 4414, 0xcff4 },
  /* 0xc200 */
  { 4425, 0x4fee }, { 4436, 0x6cfd }, { 4447, 0x54fe }, { 4457, 0xffe9 },
  { 4470, 0xfeff }, { 4485, 0xfeec }, { 4497, 0xcfde }, { 4509, 0x4fee },
  { 4520, 0xfcfd }, { 4533, 0xd6fe }, { 4545, 0xcecf }, { 4556, 0xfd4f },
  { 4568, 0xffff }, { 4584, 0xcfff }, { 4598, 0x47e6 }, { 4607, 0xe4bd },
  /* 0xc300 */
  { 4617, 0xccfe }, { 4628, 0xeec7 }, { 4639, 0xfccf }, { 4651, 0xffff },
  { 4667, 0xffdf }, { 4682, 0xffff }, { 4698, 0xecff }, { 4711, 0xccfa },
  { 4721, 0xeeef }, { 4734, 0xffff }, { 4750, 0xffff }, { 4766, 0xffff },
  { 4782, 0xfffe }, { 4797, 0x6cff }, { 4809, 0xdcfa }, { 4820, 0xfecf },
  /* 0xc400 */
  { 4833, 0xfeff }, { 4848, 0xfffe }, { 4863, 0xefef }, { 4877, 0xcfee },
  { 4889, 0xfeff }, { 4904, 0xffff }, { 4920, 0xeecf }, { 4932, 0xfdcf },
  { 4945, 0xfffe }, { 4960, 0xefef }, { 4974, 0xffff }, { 4990, 0xeeff },
  { 5004, 0xffff }, { 5020, 0xffff }, { 5036, 0xfdff }, { 5051, 0x7aec },
  /* 0xc500 */
  { 5061, 0xeffc }, { 5074, 0xefee }, { 5087, 0xecff }, { 5100, 0xd4fe },
  { 5111, 0x88cf }, { 5119, 0x9c47 }, { 5127, 0xfeec }, { 5139, 0xcfc4 },
  { 5148, 0x4f6e }, { 5158, 0xee5d }, { 5169, 0xfdfe }, { 5183, 0x84cf },
  { 5191, 0xa80f }, { 5198, 0xfeec }, { 5210, 0x8fd4 }, { 5219, 0x0f2e },
  /* 0xc600 */
  { 5227, 0xee1c }, { 5236, 0xe4fe }, { 5247, 0x8ecf }, { 5257, 0xf546 },
  { 5266, 0xfeec }, { 5278, 0xcfc4 }, { 5287, 0x6ffe }, { 5300, 0xecfd },
  { 5312, 0xd4fe }, { 5323, 0xeecf }, { 5335, 0xfd4f }, { 5347, 0xf8ec },
  { 5357, 0xcfd4 }, { 5367, 0xcfee }, { 5379, 0xecfc }, { 5390, 0xdcfe },
  /* 0xc700 */
  { 5402, 0xeecf }, { 5414, 0xfd4f }, { 5426, 0xfeec }, { 5438, 0xcf54 },
  { 5447, 0x4bee }, { 5457, 0xee01 }, { 5464, 0xf6fe }, { 5477, 0x8ecf },
  { 5487, 0xb847 }, { 5495, 0xfa2c }, { 5504, 0xcf84 }, { 5512, 0x4fee },
  { 5523, 0xacfc }, { 5533, 0xdefe }, { 5546, 0xeeef }, { 5559, 0xffff },
  /* 0xc800 */
  { 5575, 0xfaec }, { 5586, 0xcf94 }, { 5595, 0x4fee }, { 5606, 0xeefd },
  { 5619, 0xccfe }, { 5630, 0xffef }, { 5645, 0xffff }, { 5661, 0xfaec },
  { 5672, 0xc714 }, { 5679, 0x5fef }, { 5692, 0x001d },
};

static int
uhc_1_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    if (wc >= 0xac00 && wc < 0xc8b0) {
      const Summary16 *summary = &uhc_1_uni2indx_pageac[(wc>>4)-0xac0];
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        used += summary->indx;
        c = uhc_1_2charset_main[used>>7] + uhc_1_2charset[used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
