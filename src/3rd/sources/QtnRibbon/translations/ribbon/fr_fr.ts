<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="fr_FR">
<context>
    <name>RibbonBarCustomizePage</name>
    <message>
        <location filename="../../src/ribbon/QtnRibbonBarCustomizePage.ui"/>
        <source>Customize Ribbon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Customize the Ribbon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&amp;Choose commands from:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&amp;Add &gt; &gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&lt; &lt; &amp;Remove</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Customize the Ri&amp;bbon:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Ne&amp;w Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&amp;New Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Rena&amp;me...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Customizations:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Re&amp;set</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RibbonQuickAccessBarCustomizePage</name>
    <message>
        <location filename="../../src/ribbon/QtnRibbonQuickAccessBarCustomizePage.ui"/>
        <source>Quick Access Toolbar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Customize the Quick Access Toolbar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&amp;Choose commands from:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>C&amp;ommands:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>S&amp;how Quick Access Toolbar below the Ribbon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&amp;Add &gt; &gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&lt; &lt; &amp;Remove</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>Re&amp;set</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RibbonRenameDialog</name>
    <message>
        <location filename="../../src/ribbon/QtnRibbonRenameDialog.ui"/>
        <source>Rename</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location/>
        <source>&amp;Display Name:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Qtitan::RibbonBar</name>
    <message>
        <location filename="../../src/ribbon/QtnRibbonDef.cpp" line="+32"/>
        <source>Customize Quick Access Toolbar...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>Customize Quick Access Toolbar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+2"/>
        <source>Show Quick Access Toolbar Below the Ribbon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>Show Quick Access Toolbar Above the Ribbon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>Customize the Ribbon...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>Minimize the Ribbon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>All Commands</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+2"/>
        <source>Recent Documents</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>Untitled</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>&lt;Separator&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>New Page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>New Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location line="+1"/>
        <source>One should add commands to custom groups. To create a group, choose page in list and press button &apos;Create group&apos;.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
