#pragma once

#include <DSLCpp/WIZDesignerDSLBaseVisitor.h>

#include "DSLCodeContext.h"

#include <sstream>
#include <exception>
#include <stdarg.h>

namespace WIZDesignerDSL
{

/// @brief 运行错误
/// @param ctx 输出上下文
/// @param fmt 输出格式
/// @param ... 占位符参数列表
#define throwErr(ctx, fmt, ...)                                                                             \
    {                                                                                                       \
        std::ostringstream _msg_;                                                                           \
        {                                                                                                   \
            char str[1024];                                                                                 \
            snprintf(str, sizeof(str), fmt, __VA_ARGS__);                                                   \
            if (ctx != nullptr)                                                                             \
            {                                                                                               \
                _msg_ << "ERROR[" << _codeContext->_inputStream.getSourceName() << ":"                      \
                  << ctx->getStart()->getLine() << ":" << ctx->getStart()->getCharPositionInLine() << "]: " \
                  << str;                                                                                   \
            }                                                                                               \
            else if (_codeContext != nullptr)                                                               \
            {                                                                                               \
                _msg_ << "ERROR[" << _codeContext->_inputStream.getSourceName() << ":" << "]: " << str;;    \
            }                                                                                               \
            else                                                                                            \
            {                                                                                               \
                _msg_ << "ERROR: " << str;;                                                                 \
            }                                                                                               \
        }                                                                                                   \
        throw std::logic_error(_msg_.str());                                                                \
    }

    /**
     * @brief DSL遍历器
     */
    class DSLVisitor : public WIZDesignerDSLBaseVisitor
    {
    public:
        /// @brief 构造函数
        /// @param context 初始上下文
        /// @param codeContext  代码上下文
        /// @param logger 日志输出器
        DSLVisitor(DSLContext &context, DSLCodeContextPtr &codeContext)
            : _context(&context), _codeContext(codeContext)
        {
        }

    protected:
        /// @brief 当前上下文
        DSLContext *_context;
        /// @brief 代码上下文
        DSLCodeContextPtr _codeContext;

    public:
        /**
         * @brief 函数定义
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitFunctionDefine(WIZDesignerDSLParser::FunctionDefineContext *ctx) override
        {
            if (ctx == nullptr || ctx->fnName == nullptr)
                return std::any();

            std::string fnName = ctx->fnName->getText();
            std::vector<std::string> argNames = ctx->argNames ? std::any_cast<std::vector<std::string>>(visit(ctx->argNames))
                                                              : std::vector<std::string>();
            WIZDesignerDSLParser::StatementListContext *statementList = ctx->statementList();
            DSLCodeContextPtr myCodeContext = _codeContext;
            _context->registerFunction(fnName,
                                       [=](DSLContext &, const Array &args) -> std::any
                                       {
                                           // 在新上下文里遍历执行里面的语句
                                           return visitInNewContext(statementList, myCodeContext,
                                                                    [&](DSLContext &ctx)
                                                                    {
                                                                        // 注册一个arguments对象，方便扩展任意参数个数的调用
                                                                        ctx.registerVar("_arguments", args);
                                                                        // 注册参数
                                                                        for (size_t i = 0; i < argNames.size(); i++)
                                                                        {
                                                                            ctx.registerVar(argNames[i], i < args.size() ? args[i] : std::any());
                                                                        }
                                                                    });
                                       });
            return std::any();
        }

        /**
         * @brief IF语句执行
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitIfExpr(WIZDesignerDSLParser::IfExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->cond == nullptr)
                return std::any();

            // 先求if的条件值
            std::any cond = visit(ctx->cond);
            if (cond.type() != DSLRule::BoolType)
            {
                throwErr(ctx->cond, "IF <condition> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::BoolType),
                         DSLRule::TypeName(cond.type()), getCode(ctx->cond).c_str());
            }

            // 如果if条件值为真，则在新上下文中执行if的then语句块
            if (std::any_cast<Bool>(cond))
            {
                return visitInNewContext(ctx->thenExpr, nullptr);
            }

            // 遍历elseif列表
            auto elseifList = ctx->elseIfExpr();
            for (size_t i = 0; i < elseifList.size(); i++)
            {
                // 求elseif的条件值
                auto elseifExpr = elseifList[i];
                std::any elseIfCond = visit(elseifExpr->cond);
                if (elseIfCond.type() != DSLRule::BoolType)
                {
                    throwErr(elseifExpr->cond, "ELSEIF <condition> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::BoolType),
                             DSLRule::TypeName(elseIfCond.type()), getCode(elseifExpr->cond).c_str());
                }
                // 如果elseif的条件值为真，则在新上下文中执行elseif的then语句块
                if (std::any_cast<Bool>(elseIfCond))
                {
                    return visitInNewContext(elseifExpr->thenExpr, nullptr);
                }
            }

            // 最后在新上下文中执行else语句块
            if (ctx->elseExpr)
            {
                return visitInNewContext(ctx->elseExpr, nullptr);
            }
            return std::any();
        }

        /**
         * @brief FOR语句执行
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitForExpr(WIZDesignerDSLParser::ForExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->startValue == nullptr || ctx->endValue == nullptr || ctx->var == nullptr)
                return std::any();
            // 计算起始值
            std::any start = visit(ctx->startValue);
            if (start.type() != DSLRule::NumberType)
            {
                throwErr(ctx->startValue, "FOR-TO <startValue> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::NumberType),
                         DSLRule::TypeName(start.type()), getCode(ctx->startValue).c_str());
            }
            Number startValue = std::any_cast<Number>(start);

            // 计算终止值
            std::any end = visit(ctx->endValue);
            if (end.type() != DSLRule::NumberType)
            {
                throwErr(ctx->endValue, "FOR-TO <endValue> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::NumberType),
                         DSLRule::TypeName(end.type()), getCode(ctx->endValue).c_str());
            }
            Number endValue = std::any_cast<Number>(end);

            // 计算步长
            Number stepValue = startValue < endValue ? 1 : -1;
            if (ctx->step)
            {
                std::any step = visit(ctx->step);
                if (start.type() != DSLRule::NumberType)
                {
                    throwErr(ctx->startValue, "FOR-TO <endValue> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::NumberType),
                             DSLRule::TypeName(start.type()), getCode(ctx->step).c_str());
                }
                stepValue = std::any_cast<Number>(step);
                if (stepValue == 0)
                {
                    throwErr(ctx->startValue, "FOR-TO <step> can not be zero(%s)", getCode(ctx->startValue).c_str());
                }
                else if (startValue <= endValue && stepValue < 0)
                {
                    throwErr(ctx->startValue, "FOR-TO <step> must bigger than zero(%s) while startValue > endValue", getCode(ctx->startValue).c_str());
                }
                else if (startValue > endValue && stepValue > 0)
                {
                    throwErr(ctx->startValue, "FOR-TO <step> must less than zero(%s) while startValue > endValue", getCode(ctx->startValue).c_str());
                }
            }

            std::string varName = ctx->var->getText();
            std::any result;
            for (Number i = startValue; (stepValue > 0 ? i <= endValue : i >= endValue); i += stepValue)
            {
                try
                {
                    result = visitInNewContext(ctx->statementListWithContinueBreak(), nullptr,
                                               [&](DSLContext &ctx)
                                               {
                                                   ctx.registerVar(varName, i);
                                               });
                }
                catch (const std::exception &e)
                {
                    if (e.what() == std::string("CONTINUE;"))
                        continue;
                    if (e.what() == std::string("BREAK;"))
                        break;
                    throw e;
                }
            }
            return result;
        }

        /**
         * @brief FOR-IN语句执行
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitForInExpr(WIZDesignerDSLParser::ForInExprContext* ctx) override
        {
            if (ctx == nullptr || ctx->arr == nullptr || ctx->cond == nullptr || ctx->var == nullptr)
                return std::any();
            // 计算遍历列表
            std::any arrValue = visit(ctx->arr);
            if (arrValue.type() != DSLRule::ArrayType)
            {
                throwErr(ctx->arr, "FOR-IN <arr> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::ArrayType),
                    DSLRule::TypeName(arrValue.type()), getCode(ctx->arr).c_str());
            }
            Array arr = std::any_cast<Array>(arrValue);

            // 遍历列表，判断上下文调用语句块
            auto cond = ctx->cond;
            std::string varName = ctx->var->getText();
            std::any result;
            for (Number i = 0; i < arr.size(); i++)
            {
                try
                {
                    result = visitInNewContext(ctx->statementListWithContinueBreak(), nullptr,
                        [&](DSLContext& ctx)
                        {
                            ctx.registerVar(varName, arr[i]);

                            if (cond)
                            {
                                std::any condVal = visit(cond);
                                if (condVal.type() != DSLRule::BoolType)
                                {
                                    throwErr(cond, "FOR-IN <cond> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::BoolType),
                                        DSLRule::TypeName(condVal.type()), getCode(cond).c_str());
                                }
                                if (!std::any_cast<Bool>(condVal))
                                {
                                    throw std::runtime_error("CONTINUE;");
                                }
                            }
                        });
                }
                catch (const std::exception &e)
                {
                    if (e.what() == std::string("CONTINUE;"))
                        continue;
                    if (e.what() == std::string("BREAK;"))
                        break;
                    throw e;
                }
            }
            return result;
        }

        /**
         * @brief WHILE语句执行
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitWhileExpr(WIZDesignerDSLParser::WhileExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->cond == nullptr)
                return std::any();
            std::any result;
            while (true)
            {
                // 每次都重新遍历条件表达式进行求值
                std::any cond = visit(ctx->cond);
                if (cond.type() != DSLRule::BoolType)
                {
                    throwErr(ctx->cond, "WHILE <condition> must be %s but %s(%s)", DSLRule::TypeName(DSLRule::BoolType),
                             DSLRule::TypeName(cond.type()), getCode(ctx->cond).c_str());
                }

                // 如果条件值为false，则跳出循环
                if (!std::any_cast<Bool>(cond))
                    break;

                try
                {
                    result = visitInNewContext(ctx->statementListWithContinueBreak(), nullptr);
                }
                catch (const std::exception &e)
                {
                    if (e.what() == std::string("CONTINUE;"))
                        continue;
                    if (e.what() == std::string("BREAK;"))
                        break;
                    throw e;
                }
            }
            return result;
        }

        virtual std::any visitContinueWord(WIZDesignerDSLParser::ContinueWordContext *) override
        {
            throw std::runtime_error("CONTINUE;");
        }

        virtual std::any visitBreakWord(WIZDesignerDSLParser::BreakWordContext *) override
        {
            throw std::runtime_error("BREAK;");
        }

        /**
         * @brief 查找语句执行
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitParamAttrIndexOfExpr(WIZDesignerDSLParser::ParamAttrIndexOfExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->type == nullptr || ctx->obj == nullptr || ctx->idx == nullptr)
                return std::any();
            String type = ctx->type->getText();
            std::any obj = visit(ctx->obj);
            int idx = -1;
            std::istringstream iss(ctx->idx->getText());
            if (!(iss >> idx) || idx < 0)
                throwErr(ctx, "Unsupport command: %s", getCode(ctx).c_str());
            Array args = { type, obj , idx };
            return invokeOp(ctx, DSLRule::SymbolNameOp(DSLRule::StringType, "AttributeIdxOF", obj.type()), args);
        }
        virtual std::any visitAttrNumOfExpr(WIZDesignerDSLParser::AttrNumOfExprContext* ctx) override
        {
            if (ctx == nullptr || ctx->attrName() == nullptr || ctx->obj == nullptr || ctx->idx == nullptr)
                return std::any();
            String findType = ctx->attrName()->getText();
            std::any obj = visit(ctx->obj);
            int idx = -1;
            std::istringstream iss(ctx->idx->getText());
            if (!(iss >> idx) || idx < 0)
                throwErr(ctx, "Unsupport command: %s", getCode(ctx).c_str());
            Array args = { findType, obj , idx };
            return invokeOp(ctx, DSLRule::SymbolNameOp(DSLRule::StringType, "AttributeIdxOF", obj.type()), args);
        }
        virtual std::any visitAttrIndexOfExpr(WIZDesignerDSLParser::AttrIndexOfExprContext* ctx) override
        {
            if (ctx == nullptr || ctx->attrName() == nullptr || ctx->obj == nullptr || ctx->idx == nullptr)
                return std::any();
            String type = ctx->attrName()->getText();
            std::any obj = visit(ctx->obj);
            int idx = -1;
            std::istringstream iss(ctx->idx->getText());
            if (!(iss >> idx) || idx < 0)
                throwErr(ctx, "Unsupport command: %s", getCode(ctx).c_str());
            Array args = { type, obj , idx };
            return invokeOp(ctx, DSLRule::SymbolNameOp(DSLRule::StringType, "ChildIdxOF", obj.type()), args);
        }
        virtual std::any visitAttrOfExpr(WIZDesignerDSLParser::AttrOfExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->attrName() == nullptr || ctx->obj == nullptr)
                return std::any();
            String type = ctx->attrName()->getText();
            std::any obj = visit(ctx->obj);
            Array args = {type, obj};
            return invokeOp(ctx, DSLRule::SymbolNameOp(DSLRule::StringType, "OF", obj.type()), args);
        }
        /**
         * @brief 全局变量赋值语句
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitGlobalVariableAssign(WIZDesignerDSLParser::GlobalVariableAssignContext *ctx) override
        {
            if (ctx == nullptr || ctx->right == nullptr || ctx->var == nullptr)
                return std::any();
            std::any value = visit(ctx->right);
            std::string name = ctx->var->getText();
            assert(name.size() > 0 && name[0] == '/');
            if (ctx->def != nullptr)
            {
                _context->getGlobalContext().registerVar(name, value);
            }
            else
            {
                _context->getGlobalContext().setVar(name, value);
            }
            return value;
        }

        /**
         * @brief 全局变量定义语句
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitGlobalVariableDefine(WIZDesignerDSLParser::GlobalVariableDefineContext *ctx) override
        {
            if (ctx == nullptr || ctx->var == nullptr || _context == nullptr)
                return std::any();
            std::string name = ctx->var->getText();
            _context->getGlobalContext().registerVar(name, std::any());
            return std::any();
        }

        /**
         * @brief 普通变量赋值语句
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitLocalVariableAssign(WIZDesignerDSLParser::LocalVariableAssignContext *ctx) override
        {
            if (ctx == nullptr || ctx->right == nullptr || ctx->var == nullptr || _context == nullptr)
                return std::any();
            std::any value = visit(ctx->right);
            std::string name = ctx->var->getText();
            if (ctx->def != nullptr || name[0] == '$')
            {
                _context->registerVar(name, value);
            }
            else
            {
                if (!_context->setVar(name, value))
                {
                    throwErr(ctx, "No such variable with name: %s", name.c_str());
                }
            }
            return value;
        }

        /**
         * @brief 普通变量定义语句
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitLocalVariableDefine(WIZDesignerDSLParser::LocalVariableDefineContext *ctx) override
        {
            if (ctx == nullptr || ctx->var == nullptr || _context == nullptr)
                return std::any();
            std::string name = ctx->var->getText();
            _context->registerVar(name, std::any());
            return std::any();
        }

        /**
         * @brief 一个操作数动作语句
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitActionTypeWith1Expr(WIZDesignerDSLParser::ActionTypeWith1ExprContext* ctx) override
        {
            if (ctx == nullptr || ctx->action == nullptr || ctx->arg1 == nullptr)
                return std::any();
            std::string action = ctx->action->getText();
            std::any arg1 = visit(ctx->arg1);
            Array args = { arg1  };
            return invoke(ctx, DSLRule::SymbolNameAction(action.c_str(), args), args);
        }

        /**
         * @brief 两个操作数动作语句
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitActionTypeWith2Expr(WIZDesignerDSLParser::ActionTypeWith2ExprContext* ctx) override
        {
            if (ctx == nullptr || ctx->action == nullptr || ctx->arg1 == nullptr || ctx->arg2 == nullptr)
                return std::any();
            std::string action = ctx->action->getText();
            std::any arg1 = visit(ctx->arg1);
            std::any arg2 = visit(ctx->arg2);
            Array args = { arg1 , arg2 };
            return invoke(ctx, DSLRule::SymbolNameAction(action.c_str(), args), args);
        }

        /**
         * @brief 后置单位语句
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitUnitConvertExpr(WIZDesignerDSLParser::UnitConvertExprContext* ctx) override
        {
            if (ctx == nullptr || ctx->value == nullptr || ctx->unitType == nullptr)
                return std::any();
            std::any value = visit(ctx->value);
            std::string unitType = ctx->unitType->getText();
            Array args = { value };
            return invoke(ctx, DSLRule::SymbolNameOp(value.type(), unitType.c_str()), args);
        }
        
        /**
         * @brief 访问特殊变量表达式
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitSpecialVariableExpr(WIZDesignerDSLParser::SpecialVariableExprContext* ctx) override
        {
            if (ctx == nullptr || ctx->var == nullptr)
                return std::any();
            // 多个词构成的变量名称，先用空格拼起来再访问
            std::stringstream ss;
            visitJoinTreeNodeText(ss, ctx->var, " ");
            std::string name = ss.str();
            try
            {
                if (ctx->idx != nullptr)
                {
                    auto iStr = ctx->idx->getText();
                    std::stringstream idxStr(iStr);
                    int idx = 0;
                    if (idxStr >> idx)
                    {
                        return _context->getVar(name, idx);
                    }
                    throwErr(ctx, "Parse failed: (%s)", getCode(ctx).c_str());
                }
                return _context->getVar(name);
            }
            catch (const std::exception &e)
            {
                throwErr(ctx, "%s", e.what());
            }
        }

        /**
         * @brief 访问全局变量
         * @param ctx
         * @return std::any
         */
        virtual std::any visitGlobalVariableExpr(WIZDesignerDSLParser::GlobalVariableExprContext *ctx) override
        {
            if (ctx == nullptr || _context == nullptr)
                return std::any();
            std::string name = ctx->getText();
            try
            {
                return _context->getGlobalContext().getVar(name);
            }
            catch (const std::exception &e)
            {
                throwErr(ctx, "%s", e.what());
            }
        }

        /**
         * @brief 访问变量
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitVariable(WIZDesignerDSLParser::VariableContext *ctx) override
        {
            if (ctx == nullptr || _context == nullptr)
                return std::any();
            std::string name = ctx->getText();
            try
            {
                return _context->getVar(name);
            }
            catch (const std::exception &e)
            {
                throwErr(ctx, "%s", e.what());
            }
        }

        /**
         * @brief 函数调用
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitFunctionCall(WIZDesignerDSLParser::FunctionCallContext *ctx) override
        {
            if (ctx == nullptr || ctx->fn == nullptr)
                return std::any();
            // 如果有参数部分，先求遍历求到参数列表，如果没有创建一个空参数列表
            Array args = ctx->args ? std::any_cast<Array>(visit(ctx->args)) : Array();
            // 在上下文中查找符号
            std::string functionName = ctx->fn->getText();
            return invoke(ctx, functionName, args);
        }

        /**
         * @brief 遍历参数列表
         * @param ctx 执行节点上下文
         * @return 参数列表
         */
        virtual std::any visitArgumentList(WIZDesignerDSLParser::ArgumentListContext *ctx)
        {
            if (ctx == nullptr)
                return std::any();
            Array args;
            std::vector<WIZDesignerDSLParser::ExprContext *> expr = ctx->expr();
            args.resize(expr.size());
            for (size_t i = 0; i < expr.size(); i++)
            {
                args[i] = visit(expr[i]);
            }
            return args;
        }

        /**
         * @brief 遍历参数列表
         * @param ctx 执行节点上下文
         * @return 参数列表
         */
        virtual std::any visitParamList(WIZDesignerDSLParser::ParamListContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            std::vector<std::string> args;
            auto ids = ctx->IDENTIFIER();
            args.reserve(ids.size());
            for (size_t i = 0; i < ids.size(); i++)
            {
                if (ids[i] == nullptr)
                {
                    args.emplace_back(std::string());
                    continue;
                }
                args.emplace_back(ids[i]->getText());
            }
            return args;
        }

        /**
         * @brief 逻辑NOT运算
         * @param ctx 执行节点上下文
         * @return bool值
         */
        virtual std::any visitLogicalNotExpr(WIZDesignerDSLParser::LogicalNotExprContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return operatorCall(ctx, ctx->op, nullptr, ctx->right);
        }

        /**
         * @brief 逻辑AndOr运算
         * @param ctx 执行节点上下文
         * @return bool值
         */
        virtual std::any visitLogicalAndOrExpr(WIZDesignerDSLParser::LogicalAndOrExprContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return operatorCall(ctx, ctx->op, ctx->left, ctx->right);
        }

        /**
         * @brief 比较运算运算
         * @param ctx 执行节点上下文
         * @return bool值
         */
        virtual std::any visitComparisonExpr(WIZDesignerDSLParser::ComparisonExprContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return operatorCall(ctx, ctx->op, ctx->left, ctx->right);
        }

        /**
         * @brief 三目运算法，为固定句式，不能重载
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitConditionalOperatorExpr(WIZDesignerDSLParser::ConditionalOperatorExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->cond == nullptr || ctx->left == nullptr || ctx->right == nullptr)
                return std::any();
            // 计算左右边表达式的值
            std::any cond = visit(ctx->cond);
            std::any left = visit(ctx->left);
            std::any right = visit(ctx->right);
            // 执行对应操作符的运算符重载函数
            if (cond.type() != DSLRule::BoolType)
            {
                throwErr(ctx, "<condition>?<left>:<right>: <condition>(%s) must be %s but %s",
                         getCode(ctx->cond).c_str(),
                         DSLRule::TypeName(DSLRule::BoolType),
                         DSLRule::TypeName(cond.type()));
            }
            bool condition = std::any_cast<Bool>(cond);
            return condition ? left : right;
        }

        /**
         * @brief 对象取下标操作
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitTakeBracketsExpr(WIZDesignerDSLParser::TakeBracketsExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->arr == nullptr || ctx->pos == nullptr || _context == nullptr)
                return std::any();
            // 先尝试获取内部变量
            auto nameStr = ctx->arr->getText();
            auto idxStr = ctx->pos->getText();
            std::stringstream idxStream(idxStr);
            int idx = 0;
            if (idxStream >> idx)
            {
                return _context->getVar(nameStr, idx);
            }
            else 
            {

                // 计算左右边表达式的值
                //std::any arr = visit(ctx->arr);
                std::any pos = visit(ctx->pos);
                auto pIndex = std::any_cast<int>(&pos);
                if (pIndex == nullptr) 
                {
                    throwErr(ctx, "Parse failed: (%s)", getCode(ctx).c_str());
                }
                return _context->getVar(nameStr, *pIndex);
            }
        }

        /**
         * @brief 对象取子范围操作
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitRangeTakeBracketsExpr(WIZDesignerDSLParser::RangeTakeBracketsExprContext *ctx) override
        {
            if (ctx == nullptr || ctx->arr == nullptr)
                return std::any();
            // 计算左右边表达式的值
            std::any arr = visit(ctx->arr);
            std::any startPos = ctx->startPos ? visit(ctx->startPos) : std::any();
            std::any endPos = ctx->endPos ? visit(ctx->endPos) : std::any();
            // 执行对应操作符的运算符重载函数
            std::string functionName = DSLRule::SymbolNameOp(arr.type(), "[:]");
            Array args = {arr, startPos, endPos};
            return invokeOp(ctx, functionName, args);
        }

        /**
         * @brief 取负运算
         * @param ctx 执行节点上下文
         * @return 返回类型由左右对象的类型决定
         */
        virtual std::any visitNegateExpr(WIZDesignerDSLParser::NegateExprContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return operatorCall(ctx, ctx->op, nullptr, ctx->right);
        }

        /**
         * @brief 乘除法运算
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitMathMulDivExpr(WIZDesignerDSLParser::MathMulDivExprContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return operatorCall(ctx, ctx->op, ctx->left, ctx->right);
        }

        /**
         * @brief 加减法运算
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitMathPlusMinusExpr(WIZDesignerDSLParser::MathPlusMinusExprContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return operatorCall(ctx, ctx->op, ctx->left, ctx->right);
        }

        /**
         * @brief 取余运算
         * @param ctx 执行节点上下文
         * @return std::any
         */
        virtual std::any visitMathRemExpr(WIZDesignerDSLParser::MathRemExprContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return operatorCall(ctx, ctx->op, ctx->left, ctx->right);
        }

        /**
         * @brief 字符串解析
         * @param ctx 执行节点上下文
         * @return std::string值
         */
        virtual std::any visitStringLiteral(WIZDesignerDSLParser::StringLiteralContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            std::string ret = (std::string)ctx->getText();
            // 去除头尾的引号，内部为我们需要在值
            ret = ret.substr(1, ret.size() - 2);
            // TODO: 处理编码和转义字符？
            return std::move(ret);
        }

        /**
         * @brief 浮点数解析
         * @param ctx 执行节点上下文
         * @return FloatType值
         */
        virtual std::any visitFloatLiteral(WIZDesignerDSLParser::FloatLiteralContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            Float ret = 0.0f;
            auto text = ctx->getText();
            std::istringstream iss(text);
            if (!(iss >> ret))
            {
                throwErr(ctx, "Parse failed: %s(%s)", DSLRule::TypeName(DSLRule::FloatType), getCode(ctx).c_str());
            }
            if (size_t i = text.find('.'); i != std::string::npos)
            {
                int decimalPlaces = static_cast<int>(text.size() - i - 1);
                Float num = static_cast<Float>(std::pow(10, decimalPlaces));

                Float tmp = std::round(ret * num);
                return Float(tmp / num);
            }
            return ret;
        }

        /**
         * @brief 十进制解析
         * @param ctx 执行节点上下文
         * @return NumberType值
         */
        virtual std::any visitDecimalLiteral(WIZDesignerDSLParser::DecimalLiteralContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            Number ret = 0;
            std::istringstream iss(ctx->getText());
            if (!(iss >> ret))
            {
                throwErr(ctx, "Parse failed: %s(%s)", DSLRule::TypeName(DSLRule::NumberType), getCode(ctx).c_str());
            }
            return ret;
        }

        /**
         * @brief BOOL值解析
         * @param ctx 执行节点上下文
         * @return bool值
         */
        virtual std::any visitBooleanLiteral(WIZDesignerDSLParser::BooleanLiteralContext *ctx) override
        {
            if (ctx == nullptr)
                return std::any();
            return (Bool)(ctx->TRUE() != nullptr);
        }

        /**
         * @brief 合并同级节点的结果
         * 同级节点比如"1,2","a, b, c"
         * @param aggregate 合并前的值
         * @param nextResult 新进来的值
         * @return 合并后的值
         */
        virtual std::any aggregateResult(std::any aggregate, std::any nextResult)
        {
            return nextResult.has_value() ? nextResult : aggregate;
        }

        /**
         * @brief 遍历语法树拼接最底层叶子节点的文本内容
         * @param ss 输出流
         * @param treeNode 遍历节点
         * @param split 分隔符
         */
        void visitJoinTreeNodeText(std::stringstream &ss, antlr4::tree::ParseTree *treeNode, const std::string &split)
        {
            if (treeNode == nullptr)
                return;
            if (treeNode->children.empty())
            {
                if (ss.tellp() != 0)
                {
                    ss << split;
                }
                ss << treeNode->getText();
                return;
            }

            for (size_t i = 0; i < treeNode->children.size(); i++)
            {
                antlr4::tree::ParseTree *subTreeNode = treeNode->children[i];
                if (subTreeNode != nullptr)
                {
                    visitJoinTreeNodeText(ss, subTreeNode, split);
                }
            }
        }

        /**
         * @brief 获取节点对应的代码
         * @param ctx 执行节点上下文
         * @return std::string
         */
        std::string getCode(antlr4::ParserRuleContext *ctx)
        {
            if (ctx == nullptr)
                return std::string();
            return _codeContext->_inputStream.getText(antlr4::misc::Interval(ctx->getStart()->getStartIndex(), ctx->getStop()->getStopIndex()));
        }

        /**
         * @brief 在新的上下文中执行
         * @param ctx 执行节点上下文
         * @param codeCtx 执行节点对应的代码上下文
         * @param prepare 新上下文初始化函数
         * @return std::any
         */
        std::any visitInNewContext(antlr4::ParserRuleContext *ctx, DSLCodeContextPtr codeCtx, std::function<void(DSLContext &ctx)> prepare = std::function<void(DSLContext &ctx)>())
        {
            if (ctx == nullptr)
                return std::any();
            std::any result;
            DSLContext newCtx(*_context);
            _context = &newCtx;
            DSLCodeContextPtr oldCodeCtx = _codeContext;
            _codeContext = codeCtx ? codeCtx : oldCodeCtx;
            try
            {
                if (prepare)
                {
                    prepare(newCtx);
                }
                result = visit(ctx);
            }
            catch (const std::exception &e)
            {
                // 恢复上下文，后再重新抛出异常
                _context = newCtx.parentContext();
                _codeContext = oldCodeCtx;
                throw e;
            }
            // 恢复上下文
            _context = newCtx.parentContext();
            _codeContext = oldCodeCtx;
            return result;
        }

        /**
         * @brief 调用函数
         * @param ctx 执行节点上下文
         * @param functionName 函数名称
         * @param args 参数列表
         * @return 返回值
         */
        std::any invoke(antlr4::ParserRuleContext *ctx, const std::string &functionName, Array &args)
        {
            if (_context == nullptr)
                return std::any();
            std::string functionNameWithArgs = DSLRule::SymbolNameFunctionWithArgs(functionName, args);
            auto function = _context->getFunction(functionNameWithArgs);
            if (!function)
            {
                function = _context->getFunction(functionName);
            }

            if (function)
            {
                try
                {
                    return (*function)(*_context, args);
                }
                catch (const std::exception &e)
                {
                    if (e.what() == std::string("CONTINUE;") || e.what() == std::string("BREAK;"))
                    {
                        std::string msg = std::string("<") + e.what() + "> only in FOR/WHILE block inner";
                        throwErr(ctx, "%s,\n%*s%s", getCode(ctx).c_str(), _context->depth(), " ", msg.c_str());
                    }
                    else
                    {
                        throwErr(ctx, "%s,\n%*s%s", getCode(ctx).c_str(), _context->depth(), " ", e.what());
                    }
                }
                catch (...)
                {
                    throwErr(ctx, "Unknown exception while execute: %s", getCode(ctx).c_str());
                }
            }
            else
            {
                throwErr(ctx, "Function %s or %s not registered", functionName.c_str(), functionNameWithArgs.c_str());
            }
        }

        /**
         * @brief 调用运算符重载函数
         * @param ctx 执行节点上下文
         * @param functionName 函数名称
         * @param args 参数列表
         * @return 返回值
         */
        std::any invokeOp(antlr4::ParserRuleContext *ctx, const std::string &functionName, Array &args)
        {
            if (_context == nullptr)
                return std::any();
            auto function = _context->getFunction(functionName);
            if (function)
            {
                try
                {
                    return (*function)(*_context, args);
                }
                catch (const std::exception &e)
                {
                    if (e.what() == std::string("CONTINUE;") || e.what() == std::string("BREAK;"))
                    {
                        std::string msg = std::string("<") + e.what() + "> only in FOR/WHILE block inner";
                        throwErr(ctx, "%s,\n%*s%s", getCode(ctx).c_str(), _context->depth(), " ", msg.c_str());
                    }
                    else
                    {
                        throwErr(ctx, "%s,\n%*s%s", getCode(ctx).c_str(), _context->depth(), " ", e.what());
                    }
                }
                catch (...)
                {
                    throwErr(ctx, "Unknown exception while execute: %s", getCode(ctx).c_str());
                }
            }
            else
            {
                throwErr(ctx, "Function %s not registered while execute: %s", functionName.c_str(), getCode(ctx).c_str());
            }
        }

        /**
         * @brief 操作符运算调用
         * @param ctx 执行节点上下文
         * @param op 操作符类型
         * @param left 左边值
         * @param right 右边值
         * @return std::any
         */
        virtual std::any operatorCall(antlr4::ParserRuleContext *ctx, antlr4::Token *op, antlr4::ParserRuleContext *left, antlr4::ParserRuleContext *right)
        {
            if (op == nullptr)
                return std::any();
            // 提取操作符
            std::string opName = op->getText();

            // 单右边操作符
            if (left == nullptr && right != nullptr)
            {
                // 计算右边表达式的值
                std::any rightValue = visit(right);
                // 执行对应操作符的运算符重载函数
                std::string functionName = DSLRule::SymbolNameOp(opName.c_str(), rightValue.type());
                Array args = {rightValue};
                return invokeOp(ctx, functionName, args);
            }
            // 单左边操作符
            else if (left != nullptr && right == nullptr)
            {
                // 计算左边表达式的值
                std::any leftValue = visit(left);
                // 执行对应操作符的运算符重载函数
                std::string functionName = DSLRule::SymbolNameOp(leftValue.type(), opName.c_str());
                Array args = {leftValue};
                return invokeOp(ctx, functionName, args);
            }
            // 双边操作符
            else
            {
                assert(left != nullptr && right != nullptr);
                // 计算左右边表达式的值
                std::any leftValue = visit(left);
                std::any rightValue = visit(right);
                // 执行对应操作符的运算符重载函数
                std::string functionName = DSLRule::SymbolNameOp(leftValue.type(), opName.c_str(), rightValue.type());
                Array args = {leftValue, rightValue};
                return invokeOp(ctx, functionName, args);
            }
        }
    };
}
