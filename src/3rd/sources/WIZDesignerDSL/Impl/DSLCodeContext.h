#pragma once

#include "WIZDesignerDSL.h"

#include "ANTLRInputStream.h"
#include "DSLCpp/WIZDesignerDSLLexer.h"
#include "DSLCpp/WIZDesignerDSLParser.h"

#include "DSLVisitor.h"

namespace WIZDesignerDSL
{

    class DSLCodeContext
    {
    public:
        antlr4::ANTLRInputStream _inputStream;
        WIZDesignerDSLLexer _lexer;
        antlr4::CommonTokenStream _tokens;
        WIZDesignerDSLParser _parser;
        WIZDesignerDSLParser::ProgramContext *_program;
        WIZDesignerDSLParser::CommandContext *_command;
        WIZDesignerDSLParser::ExprContext *_expr;

        DSLCodeContext(std::string_view code)
            : _inputStream(code),
              _lexer(&_inputStream),
              _tokens(&_lexer),
              _parser(&_tokens),
              _program(nullptr),
              _command(nullptr),
              _expr(nullptr)
        {
        }
    };

    using DSLCodeContextPtr = std::shared_ptr<DSLCodeContext>;
}
