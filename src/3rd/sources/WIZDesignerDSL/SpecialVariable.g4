grammar SpecialVariable;

// 特殊变量一般是多词名称，普通变量名称不需要在此处理，[a-zA-Z_][a-zA-Z0-9_]*
specialVariable
    : attrRPRO
    | arrayTypeName
    ;
    
attrRPRO            : ATTRIB_IDENTIFIER? 'RPRO' IDENTIFIER;
arrayTypeName       : ATTRIB_IDENTIFIER 'DESIGN'? IDENTIFIER
                    | 'DESIGN'? 'PARAM'
                    | 'DESIGN'? 'IPARAM'
                    | ATTRIB_IDENTIFIER? 'PWALLT';

actionTypeWith1
    : actionTWICE
    ;
    
actionTWICE: 'TWICE';

actionTypeWith2
    : actionDIFFERENCE
    | actionTANF
    | actionSUM
    ;

actionDIFFERENCE    : 'DIFFERENCE';
actionTANF          : 'TANF';
actionSUM           : 'SUM';

attrName
    : ATTRIB_IDENTIFIER? (':'? IDENTIFIER)
    | specialVariable
    ;
