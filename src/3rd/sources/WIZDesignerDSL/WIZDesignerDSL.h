#pragma once

#include <string>
#include <typeinfo>
#include <queue>
#include <map>
#include <any>
#include <functional>
#include <sstream>
#include <iostream>
#include <memory>
#include <cmath>

namespace WIZDesignerDSL
{

    class DSLContext;

    /// @brief DSL逻辑值类型
    using Bool = bool;
    /// @brief DSL整数数值类型
    using Number = int;
    /// @brief DSL浮点数值类型
    using Float = float;
    /// @brief DSL字符串值类型
    using String = std::string;
    /// @brief DSL数组
    using Array = std::vector<std::any>;
    /// @brief DSL获取自定义变量的函数原型
    using FnDSLGetVariable = std::function<bool /*如果获取成功返回true*/ (std::any & /*result*/, const std::string & /*name*/)>;
    /// @brief DSL设置自定义变量的函数原型
    using FnDSLSetVariable = std::function<bool /*如果设置成功返回true*/ (const std::string & /*name*/, const std::any & /*value*/)>;
    /// @brief DSL获取自定义数组变量中给定下标的值的函数原型
    using FnDSLGetArrayVariable = std::function<bool /*如果获取成功返回true*/ (std::any & /*result*/, const std::string & /*name*/, const int & /*index*/)>;
    /// @brief DSL函数调用原型
    using FnDSLCall = std::function<std::any(DSLContext &ctx /*currentContext*/, const Array & /*args*/)>;

    /**
     * @brief DSL解析或执行的日志输出器
     */
    class DSLLogger
    {
    public:
        /**
         * @brief 输出一条日志
         * @param msg 日志
         */
        virtual void logOut(std::string_view msg) = 0;
    };

    /**
     * @brief DSL字符串日志输出器
     */
    class DSLStringLogger : public DSLLogger
    {
    public:
        std::stringstream logs;

    public:
        void logOut(std::string_view msg) override
        {
            logs << msg;
        }
    };

    /**
     * @brief DSL控制台日志输出器
     */
    class DSLConsoleLogger : public DSLLogger
    {
    public:
        static DSLConsoleLogger &Instance()
        {
            static DSLConsoleLogger logger;
            return logger;
        }

    public:
        void logOut(std::string_view msg) override
        {
            std::cout << msg << std::endl;
        }
    };
    using std::type_info;
    class DSLRule
    {
    public:
        static constexpr const type_info &BoolType = typeid(Bool);
        static constexpr const type_info &NumberType = typeid(Number);
        static constexpr const type_info &FloatType = typeid(Float);
        static constexpr const type_info &StringType = typeid(String);
        static constexpr const type_info &ArrayType = typeid(Array);

    public:
        /**
         * @brief 输出可读的类型名称
         * @tparam T 类型
         * @return 类型名称
         */
        template <typename T>
        static const char *TypeName()
        {
            return TypeName(typeid(T));
        }

        /**
         * @brief 输出可读的类型名称
         * @param type C++的类型
         * @return 类型名称
         */
        static const char *TypeName(const type_info &type)
        {
            if (type == BoolType)
                return "Bool";
            if (type == NumberType)
                return "Number";
            if (type == FloatType)
                return "Float";
            if (type == StringType)
                return "String";
            if (type == ArrayType)
                return "Array";
            // TODO: 查表扩展
            return type.name();
        }

        /**
         * @brief 生成后置单操作数的运算符重载函数名称，目前只有负号操作
         * @tparam RightType 右边类型
         * @param op 操作符号
         * @return 编码后的函数名称
         */
        template <typename RightType>
        static std::string SymbolNameOp(const char *op)
        {
            return SymbolNameOp(op, typeid(RightType));
        }

        /**
         * @brief 生成后置单操作数的运算符重载函数名称
         * @param op 操作符号
         * @param rightType 右边类型
         * @return 编码后的函数名称
         */
        static std::string SymbolNameOp(const char *op, const type_info &rightType)
        {
            return std::string(op) + "(" + TypeName(rightType) + ")";
        }

        /**
         * @brief 生成前置单操作数的运算符重载函数名称
         * @param op 操作符号
         * @param leftType 左边类型
         * @return 编码后的函数名称
         */
        static std::string SymbolNameOp(const type_info &leftType, const char *op)
        {
            return std::string("(") + TypeName(leftType) + ")" + op;
        }

        /**
         * @brief 生成前后双操作数的运算符重载函数名称
         * @tparam LeftType 左边类型
         * @tparam RightType 右边类型
         * @param op 操作符号
         * @return 编码后的函数名称
         */
        template <typename LeftType, typename RightType>
        static std::string SymbolNameOp(const char *op)
        {
            return SymbolNameOp(typeid(LeftType), op, typeid(RightType));
        }

        /**
         * @brief 生成前后双操作数的运算符重载函数名称
         * @param leftType 左边类型
         * @param op 操作符号
         * @param rightType 右边类型
         * @return 编码后的函数名称
         */
        static std::string SymbolNameOp(const type_info &leftType, const char *op, const type_info &rightType)
        {
            return std::string("(") + TypeName(leftType) + ")" + op + "(" + TypeName(rightType) + ")";
        }

        /**
         * @brief 生成后置操作运算符重载函数名称
         * @param action 操作名称
         * @return 编码后的函数名称
         */
        static std::string SymbolNameAction(const char* action, std::vector<std::reference_wrapper<const std::type_info>> argTypes)
        {
            std::string name_with_types = std::string("ACTION_") + action + "(";
            for (size_t i = 0; i < argTypes.size(); i++)
            {
                if (i != 0)
                    name_with_types += ", ";
                name_with_types += TypeName(argTypes[i].get());
            }
            name_with_types += ")";
            return name_with_types;
        }

        static std::string SymbolNameAction(const std::string& action, Array& args)
        {
            std::string name_with_types = std::string("ACTION_") + action + "(";
            for (size_t i = 0; i < args.size(); i++)
            {
                if (i != 0)
                    name_with_types += ", ";
                name_with_types += TypeName(args[i].type());
            }
            name_with_types += ")";
            return name_with_types;
        }

        /**
         * @brief 生成类型转换函数名称
         * @param distType 目标类型
         * @param sourceType 源类型
         * @return 编码后的函数名称
         */
        static std::string SymbolNameTypeCast(const type_info &distType, const type_info &sourceType)
        {
            return std::string("(") + TypeName(distType) + ")(" + TypeName(sourceType) + ")";
        }

        /**
         * @brief 生成函数重载名称
         * @param name 函数名称
         * @param args 参数列表
         * @return std::string
         */
        static std::string SymbolNameFunctionWithArgs(const std::string &name, Array &args)
        {
            std::string name_with_types = name + "(";
            for (size_t i = 0; i < args.size(); i++)
            {
                if (i != 0)
                    name_with_types += ", ";
                name_with_types += TypeName(args[i].type());
            }
            name_with_types += ")";
            return name_with_types;
        }

        /**
         * @brief 生成函数重载名称
         * @param name 函数名称
         * @param argTypes 参数类型列表
         * @return std::string
         */
        static std::string SymbolNameFunctionWithArgs(const std::string &name, std::vector<std::reference_wrapper<const std::type_info>> argTypes)
        {
            std::string name_with_types = name + "(";
            for (size_t i = 0; i < argTypes.size(); i++)
            {
                if (i != 0)
                    name_with_types += ", ";
                name_with_types += TypeName(argTypes[i].get());
            }
            name_with_types += ")";
            return name_with_types;
        }
    };

    class DSLContext;
    class DSLCodePrivate;

    /**
     * @brief DSL代码
     */
    class DSLCode
    {
    public:
        DSLCode(std::string_view source = std::string_view());
        ~DSLCode();

        /**
         * @brief 设置DSL源码
         * @param source DSL源码
         */
        void setSource(std::string_view source);

        /**
         * @brief 获取DSL源码
         * @return std::string
         */
        std::string getSource() const
        {
            return _source;
        }

        /**
         * @brief 编译DSL程序源码
         * @param 日志输出器
         * @param 解析是否成功
         */
        bool compileProgrom(DSLLogger &logger);

        /**
         * @brief 编译DSL命令源码
         * @param 日志输出器
         * @param 解析是否成功
         */
        bool compileCommand(DSLLogger &logger);

        /**
         * @brief 编译DSL表达式源码
         * @param 日志输出器
         * @param 解析是否成功
         */
        bool compileExpr(DSLLogger &logger);

        /**
         * @brief 打印语法解析树
         * @param pretty 多行格式化输出
         * @return std::string
         */
        std::string getParseTree(bool pretty = false);

    protected:
        friend class DSLContext;
        std::string _source;
        DSLCodePrivate *_d;
    };

    /**
     * @brief DSL执行上下文
     */
    class DSLContext
    {
    public:
        using VariableMap = std::map<std::string, std::any>;
        using FunctionMap = std::map<std::string, FnDSLCall>;
        static constexpr size_t MaxCallStackDepth = 50;

        // 针对OF的临时处理，如果出现了带OF的表达式，这里生成模型时，不再共享
        using NoticeOfOpt = std::function<void()>;
    protected:
        /**
         * @brief 引用变量
         */
        class RefVar
        {
        public:
            RefVar(DSLContext &context, std::string_view refVarName)
                : _context(context), _refVarName(refVarName)
            {
            }
            DSLContext &_context;    //< 引用的上下文
            std::string _refVarName; //< 引用变量的名称，由于有unregisterVar，这里使用const char*/std::string_view引用有风险
        };

    public:
        /**
         * @brief 注册变量，如果变量存在则覆盖值
         * @param name 变量名称
         * @param value 变量值
         */
        DSLContext &registerVar(const std::string &name, const std::any &value)
        {
            _variables[name] = value;
            return *this;
        }

        /**
         * @brief 注册引用变量，如果变量存在则覆盖值
         * @param name 变量名称
         * @param refVarName 引用的变量值
         */
        DSLContext &registerRefVar(const std::string &name, std::string_view refVarName)
        {
            _variables[name] = RefVar(*this, refVarName);
            return *this;
        }

        /**
         * @brief 注册引用表达式，如果变量存在则覆盖值
         * @param name 变量名称
         * @param expr 引用的表达式代码
         */
        DSLContext &registerRefExpr(const std::string &name, std::string_view expr)
        {
            std::shared_ptr<DSLCode> code = std::make_shared<DSLCode>(expr);
            _variables[name] = code;
            return *this;
        }

        /**
         * @brief 注册函数，如果函数存在则重新定义
         * @param name 函数名称
         * @param fn 函数体
         */
        DSLContext &registerFunction(const std::string &name, FnDSLCall fn)
        {
            _functions[name] = fn;
            return *this;
        }

        /**
         * @brief 注册函数，如果函数存在则重新定义
         * @param names 函数名称
         * @param fn 函数体
         */
        DSLContext &registerFunction(std::initializer_list<std::string> names, FnDSLCall fn)
        {
            for (auto name : names)
            {
                _functions[name] = fn;
            }
            return *this;
        }

        /**
         * @brief 获取内部自定义变量访问函数
         * @param fn 变量访问函数
         */
        DSLContext &setFnGetInnerVariable(FnDSLGetVariable fn)
        {
            _fnGetInnerVariable = fn;
            return *this;
        }

        /**
         * @brief 设置内部自定义变量设置函数
         * @param fn 变量设置函数
         */
        DSLContext &setFnSetInnerVariable(FnDSLSetVariable fn)
        {
            _fnSetInnerVariable = fn;
            return *this;
        }

        /**
         * @brief 获取内部自定义变量访问函数
         * @param fn 变量访问函数
         */
        DSLContext &setFnGetInnerArrayVariable(FnDSLGetArrayVariable fn)
        {
            _fnGetInnerArrayVariable = fn;
            return *this;
        }

        /**
         * @brief 调用函数
         * @param result 函数调用返回值
         * @param name 函数名称
         * @param args 参数列表
         * @return 调用失败返回false
         */
        std::any call(std::string name, Array args)
        {
            std::string name_with_types = DSLRule::SymbolNameFunctionWithArgs(name, args);

            auto fn = getFunction(name_with_types);
            if (!fn)
            {
                fn = getFunction(name);
            }

            if (fn)
            {
                return (*fn)(*this, std::move(args));
            }
            return std::any();
        }

    public:
        /**
         * @brief 设置变量
         * @param name 变量名称
         * @param value 变量值
         * @return 没有找到对应变量时返回false
         */
        bool setVar(const std::string &name, const std::any &value)
        {
            // 先调用内部自定义设置函数
            if (_fnSetInnerVariable && _fnSetInnerVariable(name, value))
                return true;

            // 查找变量表
            auto it = _variables.find(name);
            if (it != _variables.end())
            {
                it->second = value;
                return true;
            }

            // 向上层的上下文进行设置
            if (_parent && _parent->setVar(name, value))
            {
                return true;
            }
            return false;
        }

        /**
         * @brief 获取变量
         * @param name 变量名称
         * &param bResultFalse 未找到结果时是否返回空结果
         * @return 变量值
         */
        std::any getVar(const std::string &name)
        {
            std::any result;
            // 先调用内部自定义变量获取函数
            if (_fnGetInnerVariable && _fnGetInnerVariable(result, name))
            {
                return std::move(result);
            }
            // 遍历遍历表
            auto it = _variables.find(name);
            if (it != _variables.end())
            {
                result = it->second;
                // 如果是引用变量类型，则取引用
                if (result.type() == typeid(RefVar))
                {
                    try
                    {
                        RefVar refVar = std::any_cast<RefVar>(result);
                        return refVar._context.getVar(refVar._refVarName);
                    }
                    catch (const std::exception &e)
                    {
                        throw std::logic_error("The reference of " + name + " is missing by " + e.what());
                    }
                }
                // 如果是引用表达式类型，则执行表达式
                else if (result.type() == typeid(std::shared_ptr<DSLCode>))
                {
                    std::shared_ptr<DSLCode> code = std::any_cast<std::shared_ptr<DSLCode>>(result);
                    DSLStringLogger logs;
                    result = expr(*code, &logs);
                    if (logs.logs.tellp() != 0)
                    {
                        throw std::logic_error("The reference expr of " + name + " has error by execute(" + code->_source + ") failed: " + logs.logs.str());
                    }
                    return result;
                }
                return std::move(result);
            }
            // 尝试父上下文中查找
            if (_parent)
            {
                return _parent->getVar(name);
            }

            throw std::logic_error("No such variable with name: " + name);
        }
        /**
         * @brief 获取变量
         * @param name 变量名称
         * &param bResultFalse 未找到结果时是否返回空结果
         * @return 变量值
         */
        std::any getVar(const std::string &name, const int& index)
        {
            std::any result;
            // 先调用内部自定义变量获取函数
            if (_fnGetInnerArrayVariable && _fnGetInnerArrayVariable(result, name, index))
            {
                return std::move(result);
            }
            // 尝试父上下文中查找
            if (_parent)
            {
                return _parent->getVar(name, index);
            }

            throw std::logic_error("No such variable with name: " + name);
        }

        /**
         * @brief 获取函数
         * @param name 函数名称
         * @return 函数体，std::function拷贝比较消耗性能，所以这里返回引用
         */
        FnDSLCall *getFunction(const std::string &name)
        {
            // 遍历函数表
            auto jt = _functions.find(name);
            if (jt != _functions.end())
            {
                return &jt->second;
            }

            if (_parent)
            {
                return _parent->getFunction(name);
            }
            return nullptr;
        }

    public:
        /**
         * @brief 继承的执行上下文
         * @param parent 父上下文
         */
        explicit DSLContext(DSLContext &parentCtx)
            : _parent(&parentCtx), _top(_parent->_top)
        {
            // 检查栈大小，限制最大值
            if (depth() > MaxCallStackDepth)
            {
                throw std::logic_error(std::string("Stackoverflow max callstack depth is ") + std::to_string(MaxCallStackDepth));
            }
        }

        /**
         * @brief 构造函数，顶级上下文需要初始化默认基础函数表
         */
        DSLContext()
            : _parent(nullptr), _top(*this)
        {
            // 获取上下文信息，用于调试，和只能提示
            registerFunction("contextInfo",
                             [](DSLContext &ctx, const Array &) -> std::any
                             {
                                 std::stringstream ss;
                                 std::vector<DSLContext *> callStacks;
                                 DSLContext *c = &ctx;
                                 while (c)
                                 {
                                     callStacks.push_back(c);
                                     c = c->parentContext();
                                 }
                                 std::reverse(callStacks.begin(), callStacks.end());

                                 ss << "contexts: " << std::endl;

                                 for (size_t i = 0; i < callStacks.size(); i++)
                                 {
                                     std::string spaces(i * 4, ' ');
                                     ss << "-------------------------------------------------------------------------------" << std::endl;
                                     for (auto &it : callStacks[i]->_functions)
                                     {
                                         ss << spaces << "| " << it.first << " = function" << std::endl;
                                     }
                                     for (auto &it : callStacks[i]->_variables)
                                     {
                                         ss << spaces << "| " << it.first << " = " << std::any_cast<String>(callStacks[i]->call("STR", {it.second})) << std::endl;
                                     }
                                     if (callStacks[i]->_fnGetInnerVariable)
                                     {
                                         ss << spaces << "| " << "This context has custom getVariable function" << std::endl;
                                     }
                                     if (callStacks[i]->_fnSetInnerVariable)
                                     {
                                         ss << spaces << "| " << "This context has custom setVariable function" << std::endl;
                                     }
                                     if (callStacks[i]->_fnGetInnerArrayVariable)
                                     {
                                         ss << spaces << "| " << "This context has custom getArrayVariable function" << std::endl;
                                     }
                                 }
                                 return std::move((String)ss.str());
                             });
            // 打印输出
            registerFunction("print",
                             [](DSLContext &ctx, const Array &args) -> std::any
                             {
                                 if (args.size() == 0)
                                 {
                                     std::cout << std::endl;
                                 }
                                 else
                                 {
                                     std::cout << std::any_cast<String>(ctx.call("STR", args)) << std::endl;
                                 }
                                 return std::any();
                             });

#pragma region StringConvert
            registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", {DSLRule::BoolType}),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (String)(std::any_cast<Bool>(args[0]) ? "TRUE" : "FALSE");
                             });
            registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", {DSLRule::NumberType}),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (String)std::to_string(std::any_cast<Number>(args[0]));
                             });
            registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", {DSLRule::FloatType}),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (String)std::to_string(std::any_cast<Float>(args[0]));
                             });
            registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", {DSLRule::StringType}),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return String("'") + std::any_cast<String>(args[0]) + String("'");
                             });
            registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", {DSLRule::ArrayType}),
                             [](DSLContext &ctx, const Array &args) -> std::any
                             {
                                 Array arr = std::any_cast<Array>(args[0]);
                                 std::stringstream ss;
                                 ss << '[';
                                 for (size_t i = 0; i < arr.size(); i++)
                                 {
                                     if (i != 0)
                                         ss << ", ";
                                     String sub = std::any_cast<String>(ctx.call("STR", {arr[i]}));
                                     ss << sub;
                                 }
                                 ss << ']';
                                 return ss.str();
                             });
            registerFunction("STR",
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 if (args.size() != 1)
                                     throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
                                 std::any v = args[0];
                                 if (!v.has_value())
                                     return String("Void");
                                 return String("Object{") + v.type().name() + "}";
                             });
#pragma endregion

            // 顶级上下文，初始化基本运算方法
#pragma region 逻辑运算
            // &&
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, "&&", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "AND", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "and", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) && std::any_cast<Bool>(args[1]));
                             });
            // ||
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, "||", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "OR", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "or", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) || std::any_cast<Bool>(args[1]));
                             });
            // !
            registerFunction({DSLRule::SymbolNameOp("!", DSLRule::BoolType),
                              DSLRule::SymbolNameOp("NOT", DSLRule::BoolType),
                              DSLRule::SymbolNameOp("not", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return !std::any_cast<Bool>(args[0]);
                             });
#pragma endregion
#pragma region 比较函数
            // ==
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, "==", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "EQ", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "eq", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) == std::any_cast<Bool>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "==", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "EQ", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "eq", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Number>(args[0]) == std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "==", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "EQ", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "eq", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) == std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "==", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "EQ", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "eq", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)((Float)std::any_cast<Number>(args[0]) == std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "==", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "EQ", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "eq", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) == (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::StringType, "==", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "EQ", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "eq", DSLRule::StringType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<String>(args[0]) == std::any_cast<String>(args[1]));
                             });
            // !=
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, "!=", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "NEQ", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "neq", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) != std::any_cast<Bool>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "!=", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "NEQ", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "neq", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Number>(args[0]) != std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "!=", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "NEQ", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "neq", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) != std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "!=", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "NEQ", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "neq", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)((Float)std::any_cast<Number>(args[0]) != std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "!=", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "NEQ", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "neq", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) != (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::StringType, "!=", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "NEQ", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "neq", DSLRule::StringType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<String>(args[0]) != std::any_cast<String>(args[1]));
                             });
            // <
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, "<", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "LT", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "lt", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) < std::any_cast<Bool>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "<", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "LT", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "lt", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Number>(args[0]) < std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "<", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "LT", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "lt", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) < std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "<", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "LT", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "lt", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)((Float)std::any_cast<Number>(args[0]) < std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "<", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "LT", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "lt", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) < (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::StringType, "<", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "LT", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "lt", DSLRule::StringType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<String>(args[0]) < std::any_cast<String>(args[1]));
                             });
            // <=
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, "<=", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "LE", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "le", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) <= std::any_cast<Bool>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "<=", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "LE", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "le", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Number>(args[0]) <= std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "<=", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "LE", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "le", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) <= std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, "<=", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "LE", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "le", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)((Float)std::any_cast<Number>(args[0]) <= std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, "<=", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "LE", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "le", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) <= (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::StringType, "<=", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "LE", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "le", DSLRule::StringType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<String>(args[0]) <= std::any_cast<String>(args[1]));
                             });
            // >
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, ">", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "GT", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "gt", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) > std::any_cast<Bool>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, ">", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "GT", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "gt", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Number>(args[0]) > std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, ">", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "GT", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "gt", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) > std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, ">", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "GT", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "gt", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)((Float)std::any_cast<Number>(args[0]) > std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, ">", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "GT", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "gt", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) > (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::StringType, ">", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "GT", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "gt", DSLRule::StringType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<String>(args[0]) > std::any_cast<String>(args[1]));
                             });
            // >=
            registerFunction({DSLRule::SymbolNameOp(DSLRule::BoolType, ">=", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "GE", DSLRule::BoolType),
                              DSLRule::SymbolNameOp(DSLRule::BoolType, "ge", DSLRule::BoolType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Bool>(args[0]) >= std::any_cast<Bool>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, ">=", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "GE", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "ge", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Number>(args[0]) >= std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, ">=", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "GE", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "ge", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) >= std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::NumberType, ">=", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "GE", DSLRule::FloatType),
                              DSLRule::SymbolNameOp(DSLRule::NumberType, "ge", DSLRule::FloatType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)((Float)std::any_cast<Number>(args[0]) >= std::any_cast<Float>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::FloatType, ">=", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "GE", DSLRule::NumberType),
                              DSLRule::SymbolNameOp(DSLRule::FloatType, "ge", DSLRule::NumberType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<Float>(args[0]) >= (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::StringType, ">=", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "GE", DSLRule::StringType),
                              DSLRule::SymbolNameOp(DSLRule::StringType, "ge", DSLRule::StringType)},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Bool)(std::any_cast<String>(args[0]) >= std::any_cast<String>(args[1]));
                             });
#pragma endregion
#pragma region Math
            auto stringToFloat = [](const std::string& str, float& outValue)->bool
            {
                if (str.empty())
                    return false;
                // 先判断字符串是否是数值类型 如果有多个小数点或者字符串以小数点开头或结尾,暂时认为不是数值
                if (std::count(str.begin(), str.end(), '.') > 1
                    || str.front() == '.'
                    || str.back() == '.')
                    return false;
                int idx = 0;
                if (str.front() == '-')
                    idx = 1;
                for (; idx < str.size(); ++idx)
                {
                    if (str[idx] != '.' && !std::isdigit(str[idx]))
                        return false;
                }
                if (str.find('.') == std::string::npos)
                {
                    outValue = static_cast<float>(std::stoi(str));
                    return true;
                }
                return std::sscanf(str.c_str(), "%f", &outValue) > 0;
            };

            auto floatToString = [](const float& value, std::string& outStr, size_t decimalPlaces = 5)->bool
            {
                if (decimalPlaces == 0)
                {
                    outStr = std::to_string(static_cast<int>(value));
                    return true;
                }
                char format[64] = { 0 };
                std::sprintf(format, "%s%dlf", "%.", static_cast<int>(decimalPlaces));
                char text[1024] = { 0 };
                std::sprintf(text, format, value);
                outStr = std::string(text);
                if (outStr.find('.') != std::string::npos)
                {
                    while (!outStr.empty())
                    {
                        if (outStr.back() == '0')
                        {
                            outStr.pop_back();
                            continue;
                        }
                        if (outStr.back() == '.')
                            outStr.pop_back();
                        break;
                    }
                }
                return !outStr.empty();
            };
            // -
            registerFunction(DSLRule::SymbolNameOp("-", DSLRule::NumberType), [](DSLContext &, const Array &args) -> std::any
                             { return -std::any_cast<Number>(args[0]); });
            registerFunction(DSLRule::SymbolNameOp("-", DSLRule::FloatType), [](DSLContext &, const Array &args) -> std::any
                             { return -std::any_cast<Float>(args[0]); });
            registerFunction(DSLRule::SymbolNameOp("-", DSLRule::StringType), [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (stringToFloat(attr, num))
                                    return -std::any_cast<Float>(num);
                                return std::any();
                            });
            // *
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "*", DSLRule::NumberType), 
                                DSLRule::SymbolNameOp(DSLRule::NumberType, "TIMES", DSLRule::NumberType) },
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (std::any_cast<Number>(args[0]) * std::any_cast<Number>(args[1]));
                             });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "*", DSLRule::FloatType),
                             DSLRule::SymbolNameOp(DSLRule::FloatType, "TIMES", DSLRule::FloatType) },
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (std::any_cast<Float>(args[0]) * std::any_cast<Float>(args[1]));
                             });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "*", DSLRule::FloatType),
                                DSLRule::SymbolNameOp(DSLRule::NumberType, "TIMES", DSLRule::FloatType) },
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Float)((Float)std::any_cast<Number>(args[0]) * std::any_cast<Float>(args[1]));
                             });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "*", DSLRule::NumberType),
                                DSLRule::SymbolNameOp(DSLRule::FloatType, "TIMES", DSLRule::NumberType) },
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Float)(std::any_cast<Float>(args[0]) * (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "*", DSLRule::FloatType),
                                DSLRule::SymbolNameOp(DSLRule::StringType, "TIMES", DSLRule::FloatType) },
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (stringToFloat(attr, num))
                                    return num * std::any_cast<Float>(args[1]);
                                return std::any();
                            });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "*", DSLRule::StringType),
                                DSLRule::SymbolNameOp(DSLRule::FloatType, "TIMES", DSLRule::StringType) },
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (stringToFloat(attr, num))
                                    return std::any_cast<Float>(args[0]) * num;
                                return std::any();
                            });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "*", DSLRule::NumberType),
                                DSLRule::SymbolNameOp(DSLRule::StringType, "TIMES", DSLRule::NumberType) },
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (stringToFloat(attr, num))
                                    return Number(num) * std::any_cast<Number>(args[1]);
                                return std::any();
                            });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "*", DSLRule::StringType),
                                DSLRule::SymbolNameOp(DSLRule::NumberType, "TIMES", DSLRule::StringType) },
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (stringToFloat(attr, num))
                                    return std::any_cast<Number>(args[0]) * Number(num);
                                return std::any();
                            });
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "*", DSLRule::StringType),
                                DSLRule::SymbolNameOp(DSLRule::StringType, "TIMES", DSLRule::StringType) },
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float left = Float(0);
                                Float right = Float(0);
                                String leftAttr = std::any_cast<String>(args[0]);
                                String rightAttr = std::any_cast<String>(args[1]);
                                if (stringToFloat(leftAttr, left) && stringToFloat(rightAttr, right))
                                    return (Float)(left * right);

                                return std::any();
                            });
            // -
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "-", DSLRule::NumberType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (std::any_cast<Number>(args[0]) - std::any_cast<Number>(args[1]));
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "-", DSLRule::FloatType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (std::any_cast<Float>(args[0]) - std::any_cast<Float>(args[1]));
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "-", DSLRule::FloatType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Float)((Float)std::any_cast<Number>(args[0]) - std::any_cast<Float>(args[1]));
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "-", DSLRule::NumberType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Float)(std::any_cast<Float>(args[0]) - (Float)std::any_cast<Number>(args[1]));
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "-", DSLRule::FloatType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (stringToFloat(attr, num))
                                    return num - std::any_cast<Float>(args[1]);
                                return std::any();
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "-", DSLRule::StringType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (stringToFloat(attr, num))
                                    return std::any_cast<Float>(args[0]) - num;
                                return std::any();
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "-", DSLRule::NumberType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (stringToFloat(attr, num))
                                    return Number(num) - std::any_cast<Number>(args[1]);
                                return std::any();
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "-", DSLRule::StringType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (stringToFloat(attr, num))
                                    return std::any_cast<Number>(args[0]) - Number(num);
                                return std::any();
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "-", DSLRule::StringType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float left = Float(0);
                                Float right = Float(0);
                                String leftAttr = std::any_cast<String>(args[0]);
                                String rightAttr = std::any_cast<String>(args[1]);
                                if (stringToFloat(leftAttr, left) && stringToFloat(rightAttr, right))
                                    return (Float)(left - right);

                                return std::any();
                            });
            // +
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "+", DSLRule::NumberType),
                            [](DSLContext &, const Array &args) -> std::any
                            {
                                return (std::any_cast<Number>(args[0]) + std::any_cast<Number>(args[1]));
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "+", DSLRule::NumberType),
                            [=](DSLContext &, const Array &args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (stringToFloat(attr, num))
                                    return (Number(num) + std::any_cast<Number>(args[1]));
                                std::string text = std::to_string(Number(std::any_cast<Number>(args[1])));
                                return String(attr + text);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "+", DSLRule::StringType),
                            [=](DSLContext &, const Array &args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (stringToFloat(attr, num))
                                    return (std::any_cast<Number>(args[0]) + Number(num));

                                std::string text = std::to_string(Number(std::any_cast<Number>(args[0])));
                                return String(text + attr);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "+", DSLRule::FloatType),
                            [](DSLContext &, const Array &args) -> std::any
                            {
                                return (std::any_cast<Float>(args[0]) + std::any_cast<Float>(args[1]));
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "+", DSLRule::FloatType),
                            [=](DSLContext &, const Array &args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (stringToFloat(attr, num))
                                    return (num + std::any_cast<Float>(args[1]));

                                std::string text;
                                if (!floatToString(float(std::any_cast<Float>(args[1])), text))
                                    return std::any();
                                return String(attr + text);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "+", DSLRule::StringType),
                            [=](DSLContext &, const Array &args) -> std::any
                            {
                                Float num = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (stringToFloat(attr, num))
                                    return (std::any_cast<Float>(args[0]) + num);

                                std::string text;
                                if (!floatToString(float(std::any_cast<Float>(args[0])), text))
                                    return std::any();
                                return String(text + attr);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "+", DSLRule::FloatType),
                            [](DSLContext &, const Array &args) -> std::any
                            {
                                return (Float)((Float)std::any_cast<Number>(args[0]) + std::any_cast<Float>(args[1]));
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "+", DSLRule::NumberType),
                            [](DSLContext &, const Array &args) -> std::any
                            {
                                return (Float)(std::any_cast<Float>(args[0]) + (Float)std::any_cast<Number>(args[1]));
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "+", DSLRule::StringType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float left = Float(0);
                                Float right = Float(0);
                                String leftAttr = std::any_cast<String>(args[0]);
                                String rightAttr = std::any_cast<String>(args[1]);
                                if (stringToFloat(leftAttr, left) && stringToFloat(rightAttr, right))
                                    return (Float)(left + right);

                                return (leftAttr + rightAttr);
                            });
            // /
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "/", DSLRule::NumberType),
                            [](DSLContext &, const Array &args) -> std::any
                            {
                                Number right = std::any_cast<Number>(args[1]);
                                if (right == 0)
                                    throw std::logic_error("Number division by zero");
                                Number left = std::any_cast<Number>(args[0]);
                                return (Float)(static_cast<Float>(left) / static_cast<Float>(right));
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "/", DSLRule::FloatType),
                            [](DSLContext &, const Array &args) -> std::any
                            {
                                Float right = std::any_cast<Float>(args[1]);
                                if (right == 0)
                                    throw std::logic_error("Float division by zero");
                                Float left = std::any_cast<Float>(args[0]);
                                return (Float)(left / right);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "/", DSLRule::FloatType),
                            [](DSLContext &, const Array &args) -> std::any
                            {
                                Float right = std::any_cast<Float>(args[1]);
                                if (right == 0)
                                    throw std::logic_error("Float division by zero");
                                Float left = (Float)std::any_cast<Number>(args[0]);
                                return (Float)(left / right);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "/", DSLRule::NumberType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                Number right = std::any_cast<Number>(args[1]);
                                if (right == 0)
                                    throw std::logic_error("Number division by zero");
                                Float left = std::any_cast<Float>(args[0]);
                                 return (Float)(left / static_cast<Float>(right));
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "/", DSLRule::FloatType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float left = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (!stringToFloat(attr, left))
                                    return std::any();
                                Float right = std::any_cast<Float>(args[1]);
                                if (right == 0)
                                    throw std::logic_error("Float division by zero");
                                return (Float)(left / right);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "/", DSLRule::StringType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float right = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (!stringToFloat(attr, right))
                                    return std::any();
                                if (right == 0)
                                    throw std::logic_error("Float division by zero");
                                Float left = std::any_cast<Float>(args[0]);
                                return (Float)(left / right);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "/", DSLRule::NumberType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float left = Float(0);
                                String attr = std::any_cast<String>(args[0]);
                                if (!stringToFloat(attr, left))
                                    return std::any();
                                Number right = std::any_cast<Number>(args[1]);
                                if (right == 0)
                                    throw std::logic_error("Number division by zero");
                                return (Float)(left / static_cast<Float>(right));
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "/", DSLRule::StringType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float right = Float(0);
                                String attr = std::any_cast<String>(args[1]);
                                if (!stringToFloat(attr, right))
                                    return std::any();
                                if (right == 0)
                                    throw std::logic_error("Number division by zero");
                                Number left = std::any_cast<Number>(args[0]);
                                return (Float)(static_cast<Float>(left) / right);
                            });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::StringType, "/", DSLRule::StringType),
                            [=](DSLContext&, const Array& args) -> std::any
                            {
                                Float left = Float(0);
                                Float right = Float(0);
                                String leftAttr = std::any_cast<String>(args[0]);
                                String rightAttr = std::any_cast<String>(args[1]);
                                if (stringToFloat(leftAttr, left) && stringToFloat(rightAttr, right))
                                {
                                    if (right == 0)
                                        throw std::logic_error("Number division by zero");
                                    return (Float)(left / right);
                                }
                                return std::any();
                            });
            // %
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "%", DSLRule::NumberType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 Number right = std::any_cast<Number>(args[1]);
                                 if (right == Float(0))
                                     throw std::logic_error("Number division by zero");
                                 return (std::any_cast<Number>(args[0]) % right);
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "%", DSLRule::FloatType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Float)fmod((double)std::any_cast<Float>(args[0]), (double)(Number)std::any_cast<Float>(args[1]));
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::NumberType, "%", DSLRule::FloatType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Float)fmod((double)std::any_cast<Number>(args[0]), (double)std::any_cast<Float>(args[1]));
                             });
            registerFunction(DSLRule::SymbolNameOp(DSLRule::FloatType, "%", DSLRule::NumberType),
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 return (Float)fmod((double)std::any_cast<Float>(args[0]), (double)std::any_cast<Number>(args[1]));
                             });
#pragma endregion
#pragma region 数组取下标函数
            registerFunction({DSLRule::SymbolNameOp(DSLRule::ArrayType, "[]")},
                             [=](DSLContext &, const Array &args) -> std::any
                             {
                                Number right = Number(-1);
                                bool bNumber = false;
                                if (args[1].type() == DSLRule::NumberType)
                                {
                                    right = std::any_cast<Number>(args[1]) - 1;
                                    bNumber = true;
                                }
                                else if (args[1].type() == DSLRule::StringType)
                                {
                                    float tempF = 0.0;
                                    if (stringToFloat(std::any_cast<String>(args[1]), tempF))
                                    {
                                        right = static_cast<int>(tempF) - 1;
                                        bNumber = true;
                                    }
                                }
                                if (!args[1].has_value() || !bNumber)
                                {
                                    throw std::logic_error("Array[<pos>]: The <pos> must be NumberType");
                                }
                                Array left = std::any_cast<Array>(args[0]);
                                if (right < Number(0) || right >= left.size())
                                    throw std::logic_error("Array out of bound");
                                return left[right];
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::ArrayType, "[:]")},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 Array arr = std::any_cast<Array>(args[0]);
                                 if (args[1].has_value() && args[1].type() != DSLRule::NumberType)
                                 {
                                     throw std::logic_error(std::string("Array[<startPos>:<endPos>]: The <startPos> must be NumberType but ") +
                                                            DSLRule::TypeName(args[1].type()));
                                 }
                                 if (args[2].has_value() && args[2].type() != DSLRule::NumberType)
                                 {
                                     throw std::logic_error(std::string("Array[<startPos>:<endPos>]: The <endPos> must be NumberType but ") +
                                                            DSLRule::TypeName(args[2].type()));
                                 }
                                 Number startPos = args[1].has_value() ? std::any_cast<Number>(args[1]) - 1 : 0;
                                 Number endPos = args[2].has_value() ? std::any_cast<Number>(args[2]) - 1 : (Number)arr.size();
                                 if (startPos > endPos)
                                     throw std::logic_error("Array[<startPos>:<endPos>]: The <startPos> must less or equal <endPos>");
                                 if (startPos < Number(0) || endPos >= arr.size())
                                     throw std::logic_error("Array out of bound");
                                 return Array(arr.begin() + startPos, arr.begin() + endPos);
                             });
            registerFunction({DSLRule::SymbolNameOp(DSLRule::StringType, "[:]")},
                             [](DSLContext &, const Array &args) -> std::any
                             {
                                 String arr = std::any_cast<String>(args[0]);
                                 if (args[1].has_value() && args[1].type() != DSLRule::NumberType)
                                 {
                                     throw std::logic_error(std::string("Array[<startPos>:<endPos>]: The <startPos> must be NumberType but ") +
                                                            DSLRule::TypeName(args[1].type()));
                                 }
                                 if (args[2].has_value() && args[2].type() != DSLRule::NumberType)
                                 {
                                     throw std::logic_error(std::string("Array[<startPos>:<endPos>]: The <endPos> must be NumberType but ") +
                                                            DSLRule::TypeName(args[2].type()));
                                 }
                                 Number startPos = args[1].has_value() ? std::any_cast<Number>(args[1]) - 1 : 0;
                                 Number endPos = args[2].has_value() ? std::any_cast<Number>(args[2]) - 1 : (Number)arr.size();
                                 if (startPos > endPos)
                                     throw std::logic_error("Array[<startPos>:<endPos>]: The <startPos> must less or equal <endPos>");
                                 if (startPos < Number(0) || endPos >= arr.size())
                                     throw std::logic_error("Array out of bound");
                                 return arr.substr(startPos, endPos - startPos);
                             });
#pragma endregion

#pragma region ATTR OF Array
            registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "OF", DSLRule::ArrayType) },
                [](DSLContext&, const Array& args) -> std::any
                {
                    String attr = std::any_cast<String>(args[0]);
                    Array arr = std::any_cast<Array>(args[1]);
                    if (attr == "LENGTH")
                        return (Number)arr.size();
                    throw std::logic_error("Array no attribute with name: " + attr);
                });
#pragma endregion
        }

        /**
         * @brief 执行DSL代码
         * @param code DSL代码
         * @param logger 日志输出器
         * @return 代码返回值
         */
        std::any exec(DSLCode &code, DSLLogger *logger = nullptr);

        /**
         * @brief 执行DSL命令代码
         * @param code DSL代码
         * @param logger 日志输出器
         * @return 代码返回值
         */
        std::any cmd(DSLCode &code, DSLLogger *logger = nullptr);

        /**
         * @brief 执行DSL表达式代码
         * @param code DSL代码
         * @param logger 日志输出器
         * @return 代码返回值
         */
        std::any expr(DSLCode &code, DSLLogger *logger = nullptr);

        /**
         * @brief 获取顶级的上下文
         * @return 顶级的上下文
         */
        DSLContext &getGlobalContext() const
        {
            return _top;
        }

        /**
         * @brief 获取父上下文
         * @return 父上下文
         */
        DSLContext *parentContext() const
        {
            return _parent;
        }

        /**
         * @brief 获取当前上下文深度
         * @return int
         */
        int depth() const
        {
            if (_parent)
                return 1 + _parent->depth();
            return 0;
        }
        /**
         * @brief 设置OF操作符通知
         */
        void setNoticeOfOpt(const NoticeOfOpt& notice) 
        {
            _noticeOfOpt = notice;
        }
        /**
         * @brief 发送OF操作符通知
         */
        void sendOfOptNotice() const
        {
            if (_noticeOfOpt)
                _noticeOfOpt();
        }
    private:
        VariableMap _variables;
        FunctionMap _functions;
        FnDSLGetVariable _fnGetInnerVariable;
        FnDSLSetVariable _fnSetInnerVariable;
        FnDSLGetArrayVariable _fnGetInnerArrayVariable;

        DSLContext *_parent = nullptr;
        DSLContext &_top; // cache

        NoticeOfOpt _noticeOfOpt;
    };

}
