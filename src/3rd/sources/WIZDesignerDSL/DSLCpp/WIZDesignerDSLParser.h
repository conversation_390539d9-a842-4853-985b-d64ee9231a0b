
// Generated from WIZDesignerDSL.g4 by ANTLR 4.13.2

#pragma once


#include "antlr4-runtime.h"




class  WIZDesignerDSLParser : public antlr4::Parser {
public:
  enum {
    T__0 = 1, T__1 = 2, T__2 = 3, T__3 = 4, T__4 = 5, T__5 = 6, T__6 = 7, 
    T__7 = 8, T__8 = 9, T__9 = 10, T__10 = 11, T__11 = 12, T__12 = 13, T__13 = 14, 
    T__14 = 15, T__15 = 16, T__16 = 17, T__17 = 18, LINE_COMMENT = 19, BLOCK_COMMENT = 20, 
    WS = 21, NEWLINE = 22, SEMI = 23, VAR = 24, GLOBAL = 25, OF = 26, NUM = 27, 
    WHERE = 28, IF = 29, THEN = 30, ELSEIF = 31, ELSE = 32, ENDIF = 33, 
    FOR = 34, BREAK = 35, CONTINUE = 36, TO = 37, IN = 38, DO = 39, STEP = 40, 
    ENDFOR = 41, WHILE = 42, <PERSON>ND<PERSON>H<PERSON>E = 43, DEFINE = 44, ENDDEFINE = 45, 
    FUNCTION = 46, ENDFUNCTION = 47, TRUE = 48, FALSE = 49, AND = 50, OR = 51, 
    EQ = 52, NEQ = 53, LT = 54, LE = 55, GT = 56, GE = 57, NOT = 58, PLUS = 59, 
    MINUS = 60, MUL = 61, DIV = 62, REM = 63, DECIMAL_LITERAL = 64, GLOBAL_IDENTIFIER = 65, 
    LOCAL_IDENTIFIER = 66, IDENTIFIER = 67, ATTRIB_IDENTIFIER = 68, STRING_LITERAL = 69
  };

  enum {
    RuleProgram = 0, RuleStatementList = 1, RuleStatementListWithContinueBreak = 2, 
    RuleStatement = 3, RuleStatementWithContinueBreak = 4, RuleCommand = 5, 
    RuleDeclareCommand = 6, RuleControlCommand = 7, RuleElseIfExpr = 8, 
    RuleExpr = 9, RuleTerm = 10, RuleFactor = 11, RuleFunctionCall = 12, 
    RuleParamList = 13, RuleArgumentList = 14, RuleVariable = 15, RuleConstant = 16, 
    RuleStringLiteral = 17, RuleFloatLiteral = 18, RuleDecimalLiteral = 19, 
    RuleBooleanLiteral = 20, RuleSpecialVariable = 21, RuleAttrRPRO = 22, 
    RuleArrayTypeName = 23, RuleActionTypeWith1 = 24, RuleActionTWICE = 25, 
    RuleActionTypeWith2 = 26, RuleActionDIFFERENCE = 27, RuleActionTANF = 28, 
    RuleActionSUM = 29, RuleAttrName = 30
  };

  explicit WIZDesignerDSLParser(antlr4::TokenStream *input);

  WIZDesignerDSLParser(antlr4::TokenStream *input, const antlr4::atn::ParserATNSimulatorOptions &options);

  ~WIZDesignerDSLParser() override;

  std::string getGrammarFileName() const override;

  const antlr4::atn::ATN& getATN() const override;

  const std::vector<std::string>& getRuleNames() const override;

  const antlr4::dfa::Vocabulary& getVocabulary() const override;

  antlr4::atn::SerializedATNView getSerializedATN() const override;


  class ProgramContext;
  class StatementListContext;
  class StatementListWithContinueBreakContext;
  class StatementContext;
  class StatementWithContinueBreakContext;
  class CommandContext;
  class DeclareCommandContext;
  class ControlCommandContext;
  class ElseIfExprContext;
  class ExprContext;
  class TermContext;
  class FactorContext;
  class FunctionCallContext;
  class ParamListContext;
  class ArgumentListContext;
  class VariableContext;
  class ConstantContext;
  class StringLiteralContext;
  class FloatLiteralContext;
  class DecimalLiteralContext;
  class BooleanLiteralContext;
  class SpecialVariableContext;
  class AttrRPROContext;
  class ArrayTypeNameContext;
  class ActionTypeWith1Context;
  class ActionTWICEContext;
  class ActionTypeWith2Context;
  class ActionDIFFERENCEContext;
  class ActionTANFContext;
  class ActionSUMContext;
  class AttrNameContext; 

  class  ProgramContext : public antlr4::ParserRuleContext {
  public:
    ProgramContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    StatementListContext *statementList();
    antlr4::tree::TerminalNode *EOF();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ProgramContext* program();

  class  StatementListContext : public antlr4::ParserRuleContext {
  public:
    StatementListContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    std::vector<StatementContext *> statement();
    StatementContext* statement(size_t i);
    std::vector<antlr4::tree::TerminalNode *> SEMI();
    antlr4::tree::TerminalNode* SEMI(size_t i);
    std::vector<antlr4::tree::TerminalNode *> NEWLINE();
    antlr4::tree::TerminalNode* NEWLINE(size_t i);

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  StatementListContext* statementList();

  class  StatementListWithContinueBreakContext : public antlr4::ParserRuleContext {
  public:
    StatementListWithContinueBreakContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    std::vector<StatementWithContinueBreakContext *> statementWithContinueBreak();
    StatementWithContinueBreakContext* statementWithContinueBreak(size_t i);
    std::vector<antlr4::tree::TerminalNode *> SEMI();
    antlr4::tree::TerminalNode* SEMI(size_t i);
    std::vector<antlr4::tree::TerminalNode *> NEWLINE();
    antlr4::tree::TerminalNode* NEWLINE(size_t i);

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  StatementListWithContinueBreakContext* statementListWithContinueBreak();

  class  StatementContext : public antlr4::ParserRuleContext {
  public:
    StatementContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    CommandContext *command();
    ExprContext *expr();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  StatementContext* statement();

  class  StatementWithContinueBreakContext : public antlr4::ParserRuleContext {
  public:
    StatementWithContinueBreakContext(antlr4::ParserRuleContext *parent, size_t invokingState);
   
    StatementWithContinueBreakContext() = default;
    void copyFrom(StatementWithContinueBreakContext *context);
    using antlr4::ParserRuleContext::copyFrom;

    virtual size_t getRuleIndex() const override;

   
  };

  class  ContinueWordContext : public StatementWithContinueBreakContext {
  public:
    ContinueWordContext(StatementWithContinueBreakContext *ctx);

    antlr4::Token *continue_ = nullptr;
    antlr4::tree::TerminalNode *CONTINUE();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  NormalStatementContext : public StatementWithContinueBreakContext {
  public:
    NormalStatementContext(StatementWithContinueBreakContext *ctx);

    StatementContext *statement();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  BreakWordContext : public StatementWithContinueBreakContext {
  public:
    BreakWordContext(StatementWithContinueBreakContext *ctx);

    antlr4::Token *break_ = nullptr;
    antlr4::tree::TerminalNode *BREAK();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  StatementWithContinueBreakContext* statementWithContinueBreak();

  class  CommandContext : public antlr4::ParserRuleContext {
  public:
    CommandContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    DeclareCommandContext *declareCommand();
    ControlCommandContext *controlCommand();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  CommandContext* command();

  class  DeclareCommandContext : public antlr4::ParserRuleContext {
  public:
    DeclareCommandContext(antlr4::ParserRuleContext *parent, size_t invokingState);
   
    DeclareCommandContext() = default;
    void copyFrom(DeclareCommandContext *context);
    using antlr4::ParserRuleContext::copyFrom;

    virtual size_t getRuleIndex() const override;

   
  };

  class  LocalVariableDefineContext : public DeclareCommandContext {
  public:
    LocalVariableDefineContext(DeclareCommandContext *ctx);

    antlr4::Token *def = nullptr;
    WIZDesignerDSLParser::VariableContext *var = nullptr;
    antlr4::tree::TerminalNode *VAR();
    VariableContext *variable();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  LocalVariableAssignContext : public DeclareCommandContext {
  public:
    LocalVariableAssignContext(DeclareCommandContext *ctx);

    antlr4::Token *def = nullptr;
    WIZDesignerDSLParser::VariableContext *var = nullptr;
    WIZDesignerDSLParser::ExprContext *right = nullptr;
    VariableContext *variable();
    ExprContext *expr();
    antlr4::tree::TerminalNode *VAR();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  FunctionDefineContext : public DeclareCommandContext {
  public:
    FunctionDefineContext(DeclareCommandContext *ctx);

    antlr4::Token *fnName = nullptr;
    WIZDesignerDSLParser::ParamListContext *argNames = nullptr;
    antlr4::tree::TerminalNode *DEFINE();
    antlr4::tree::TerminalNode *FUNCTION();
    StatementListContext *statementList();
    antlr4::tree::TerminalNode *ENDDEFINE();
    antlr4::tree::TerminalNode *IDENTIFIER();
    ParamListContext *paramList();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  GlobalVariableAssignContext : public DeclareCommandContext {
  public:
    GlobalVariableAssignContext(DeclareCommandContext *ctx);

    antlr4::Token *def = nullptr;
    antlr4::Token *var = nullptr;
    WIZDesignerDSLParser::ExprContext *right = nullptr;
    antlr4::tree::TerminalNode *GLOBAL_IDENTIFIER();
    ExprContext *expr();
    antlr4::tree::TerminalNode *GLOBAL();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  GlobalVariableDefineContext : public DeclareCommandContext {
  public:
    GlobalVariableDefineContext(DeclareCommandContext *ctx);

    antlr4::Token *def = nullptr;
    antlr4::Token *var = nullptr;
    antlr4::tree::TerminalNode *GLOBAL();
    antlr4::tree::TerminalNode *GLOBAL_IDENTIFIER();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  DeclareCommandContext* declareCommand();

  class  ControlCommandContext : public antlr4::ParserRuleContext {
  public:
    ControlCommandContext(antlr4::ParserRuleContext *parent, size_t invokingState);
   
    ControlCommandContext() = default;
    void copyFrom(ControlCommandContext *context);
    using antlr4::ParserRuleContext::copyFrom;

    virtual size_t getRuleIndex() const override;

   
  };

  class  WhileExprContext : public ControlCommandContext {
  public:
    WhileExprContext(ControlCommandContext *ctx);

    WIZDesignerDSLParser::ExprContext *cond = nullptr;
    antlr4::tree::TerminalNode *WHILE();
    StatementListWithContinueBreakContext *statementListWithContinueBreak();
    antlr4::tree::TerminalNode *ENDWHILE();
    ExprContext *expr();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  IfExprContext : public ControlCommandContext {
  public:
    IfExprContext(ControlCommandContext *ctx);

    WIZDesignerDSLParser::ExprContext *cond = nullptr;
    WIZDesignerDSLParser::StatementListContext *thenExpr = nullptr;
    WIZDesignerDSLParser::StatementListContext *elseExpr = nullptr;
    antlr4::tree::TerminalNode *IF();
    antlr4::tree::TerminalNode *THEN();
    antlr4::tree::TerminalNode *ENDIF();
    ExprContext *expr();
    std::vector<ElseIfExprContext *> elseIfExpr();
    ElseIfExprContext* elseIfExpr(size_t i);
    antlr4::tree::TerminalNode *ELSE();
    std::vector<StatementListContext *> statementList();
    StatementListContext* statementList(size_t i);
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  ForInExprContext : public ControlCommandContext {
  public:
    ForInExprContext(ControlCommandContext *ctx);

    antlr4::Token *var = nullptr;
    WIZDesignerDSLParser::ExprContext *arr = nullptr;
    WIZDesignerDSLParser::ExprContext *cond = nullptr;
    antlr4::tree::TerminalNode *FOR();
    antlr4::tree::TerminalNode *IN();
    antlr4::tree::TerminalNode *DO();
    StatementListWithContinueBreakContext *statementListWithContinueBreak();
    antlr4::tree::TerminalNode *ENDFOR();
    antlr4::tree::TerminalNode *IDENTIFIER();
    std::vector<ExprContext *> expr();
    ExprContext* expr(size_t i);
    antlr4::tree::TerminalNode *WHERE();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  ForExprContext : public ControlCommandContext {
  public:
    ForExprContext(ControlCommandContext *ctx);

    antlr4::Token *var = nullptr;
    WIZDesignerDSLParser::ExprContext *startValue = nullptr;
    WIZDesignerDSLParser::ExprContext *endValue = nullptr;
    WIZDesignerDSLParser::ExprContext *step = nullptr;
    antlr4::tree::TerminalNode *FOR();
    antlr4::tree::TerminalNode *TO();
    antlr4::tree::TerminalNode *DO();
    StatementListWithContinueBreakContext *statementListWithContinueBreak();
    antlr4::tree::TerminalNode *ENDFOR();
    antlr4::tree::TerminalNode *IDENTIFIER();
    std::vector<ExprContext *> expr();
    ExprContext* expr(size_t i);
    antlr4::tree::TerminalNode *STEP();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  ControlCommandContext* controlCommand();

  class  ElseIfExprContext : public antlr4::ParserRuleContext {
  public:
    WIZDesignerDSLParser::ExprContext *cond = nullptr;
    WIZDesignerDSLParser::StatementListContext *thenExpr = nullptr;
    ElseIfExprContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *ELSEIF();
    antlr4::tree::TerminalNode *THEN();
    ExprContext *expr();
    StatementListContext *statementList();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ElseIfExprContext* elseIfExpr();

  class  ExprContext : public antlr4::ParserRuleContext {
  public:
    ExprContext(antlr4::ParserRuleContext *parent, size_t invokingState);
   
    ExprContext() = default;
    void copyFrom(ExprContext *context);
    using antlr4::ParserRuleContext::copyFrom;

    virtual size_t getRuleIndex() const override;

   
  };

  class  ExpressionToAtomContext : public ExprContext {
  public:
    ExpressionToAtomContext(ExprContext *ctx);

    TermContext *term();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  ComparisonExprContext : public ExprContext {
  public:
    ComparisonExprContext(ExprContext *ctx);

    WIZDesignerDSLParser::ExprContext *left = nullptr;
    antlr4::Token *op = nullptr;
    WIZDesignerDSLParser::ExprContext *right = nullptr;
    std::vector<ExprContext *> expr();
    ExprContext* expr(size_t i);
    antlr4::tree::TerminalNode *EQ();
    antlr4::tree::TerminalNode *NEQ();
    antlr4::tree::TerminalNode *LT();
    antlr4::tree::TerminalNode *LE();
    antlr4::tree::TerminalNode *GT();
    antlr4::tree::TerminalNode *GE();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  LogicalNotExprContext : public ExprContext {
  public:
    LogicalNotExprContext(ExprContext *ctx);

    antlr4::Token *op = nullptr;
    WIZDesignerDSLParser::ExprContext *right = nullptr;
    antlr4::tree::TerminalNode *NOT();
    ExprContext *expr();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  LogicalAndOrExprContext : public ExprContext {
  public:
    LogicalAndOrExprContext(ExprContext *ctx);

    WIZDesignerDSLParser::ExprContext *left = nullptr;
    antlr4::Token *op = nullptr;
    WIZDesignerDSLParser::ExprContext *right = nullptr;
    std::vector<ExprContext *> expr();
    ExprContext* expr(size_t i);
    antlr4::tree::TerminalNode *AND();
    antlr4::tree::TerminalNode *OR();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  ConditionalOperatorExprContext : public ExprContext {
  public:
    ConditionalOperatorExprContext(ExprContext *ctx);

    WIZDesignerDSLParser::ExprContext *cond = nullptr;
    WIZDesignerDSLParser::ExprContext *left = nullptr;
    WIZDesignerDSLParser::ExprContext *right = nullptr;
    std::vector<ExprContext *> expr();
    ExprContext* expr(size_t i);
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  ExprContext* expr();
  ExprContext* expr(int precedence);
  class  TermContext : public antlr4::ParserRuleContext {
  public:
    TermContext(antlr4::ParserRuleContext *parent, size_t invokingState);
   
    TermContext() = default;
    void copyFrom(TermContext *context);
    using antlr4::ParserRuleContext::copyFrom;

    virtual size_t getRuleIndex() const override;

   
  };

  class  ActionTypeWith2ExprContext : public TermContext {
  public:
    ActionTypeWith2ExprContext(TermContext *ctx);

    WIZDesignerDSLParser::ActionTypeWith2Context *action = nullptr;
    WIZDesignerDSLParser::TermContext *arg1 = nullptr;
    WIZDesignerDSLParser::TermContext *arg2 = nullptr;
    ActionTypeWith2Context *actionTypeWith2();
    std::vector<TermContext *> term();
    TermContext* term(size_t i);
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  MathPlusMinusExprContext : public TermContext {
  public:
    MathPlusMinusExprContext(TermContext *ctx);

    WIZDesignerDSLParser::TermContext *left = nullptr;
    antlr4::Token *op = nullptr;
    WIZDesignerDSLParser::TermContext *right = nullptr;
    std::vector<TermContext *> term();
    TermContext* term(size_t i);
    antlr4::tree::TerminalNode *PLUS();
    antlr4::tree::TerminalNode *MINUS();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  RangeTakeBracketsExprContext : public TermContext {
  public:
    RangeTakeBracketsExprContext(TermContext *ctx);

    WIZDesignerDSLParser::TermContext *arr = nullptr;
    WIZDesignerDSLParser::TermContext *startPos = nullptr;
    WIZDesignerDSLParser::TermContext *endPos = nullptr;
    std::vector<TermContext *> term();
    TermContext* term(size_t i);
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  TakeBracketsExprContext : public TermContext {
  public:
    TakeBracketsExprContext(TermContext *ctx);

    WIZDesignerDSLParser::TermContext *arr = nullptr;
    WIZDesignerDSLParser::TermContext *pos = nullptr;
    std::vector<TermContext *> term();
    TermContext* term(size_t i);
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  BracketsExprContext : public TermContext {
  public:
    BracketsExprContext(TermContext *ctx);

    WIZDesignerDSLParser::ArgumentListContext *items = nullptr;
    ArgumentListContext *argumentList();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  AttrIndexOfExprContext : public TermContext {
  public:
    AttrIndexOfExprContext(TermContext *ctx);

    WIZDesignerDSLParser::AttrNameContext *type = nullptr;
    antlr4::Token *idx = nullptr;
    WIZDesignerDSLParser::TermContext *obj = nullptr;
    antlr4::tree::TerminalNode *OF();
    AttrNameContext *attrName();
    antlr4::tree::TerminalNode *DECIMAL_LITERAL();
    TermContext *term();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  GlobalVariableExprContext : public TermContext {
  public:
    GlobalVariableExprContext(TermContext *ctx);

    antlr4::tree::TerminalNode *GLOBAL_IDENTIFIER();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  UnitConvertExprContext : public TermContext {
  public:
    UnitConvertExprContext(TermContext *ctx);

    WIZDesignerDSLParser::TermContext *value = nullptr;
    antlr4::Token *unitType = nullptr;
    TermContext *term();
    antlr4::tree::TerminalNode *IDENTIFIER();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  NegateExprContext : public TermContext {
  public:
    NegateExprContext(TermContext *ctx);

    antlr4::Token *op = nullptr;
    WIZDesignerDSLParser::TermContext *right = nullptr;
    antlr4::tree::TerminalNode *MINUS();
    TermContext *term();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  AttrNumOfExprContext : public TermContext {
  public:
    AttrNumOfExprContext(TermContext *ctx);

    WIZDesignerDSLParser::AttrNameContext *type = nullptr;
    antlr4::Token *idx = nullptr;
    WIZDesignerDSLParser::TermContext *obj = nullptr;
    antlr4::tree::TerminalNode *NUM();
    antlr4::tree::TerminalNode *OF();
    AttrNameContext *attrName();
    antlr4::tree::TerminalNode *DECIMAL_LITERAL();
    TermContext *term();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  ParamAttrIndexOfExprContext : public TermContext {
  public:
    ParamAttrIndexOfExprContext(TermContext *ctx);

    WIZDesignerDSLParser::AttrNameContext *type = nullptr;
    antlr4::Token *idx = nullptr;
    WIZDesignerDSLParser::TermContext *obj = nullptr;
    antlr4::tree::TerminalNode *OF();
    AttrNameContext *attrName();
    antlr4::tree::TerminalNode *DECIMAL_LITERAL();
    TermContext *term();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  ActionTypeWith1ExprContext : public TermContext {
  public:
    ActionTypeWith1ExprContext(TermContext *ctx);

    WIZDesignerDSLParser::ActionTypeWith1Context *action = nullptr;
    WIZDesignerDSLParser::TermContext *arg1 = nullptr;
    ActionTypeWith1Context *actionTypeWith1();
    TermContext *term();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  SpecialVariableExprContext : public TermContext {
  public:
    SpecialVariableExprContext(TermContext *ctx);

    WIZDesignerDSLParser::SpecialVariableContext *var = nullptr;
    antlr4::Token *idx = nullptr;
    SpecialVariableContext *specialVariable();
    antlr4::tree::TerminalNode *DECIMAL_LITERAL();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  MathRemExprContext : public TermContext {
  public:
    MathRemExprContext(TermContext *ctx);

    WIZDesignerDSLParser::TermContext *left = nullptr;
    antlr4::Token *op = nullptr;
    WIZDesignerDSLParser::TermContext *right = nullptr;
    std::vector<TermContext *> term();
    TermContext* term(size_t i);
    antlr4::tree::TerminalNode *REM();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  AttrOfExprContext : public TermContext {
  public:
    AttrOfExprContext(TermContext *ctx);

    WIZDesignerDSLParser::AttrNameContext *type = nullptr;
    WIZDesignerDSLParser::TermContext *obj = nullptr;
    antlr4::tree::TerminalNode *OF();
    AttrNameContext *attrName();
    TermContext *term();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  MathMulDivExprContext : public TermContext {
  public:
    MathMulDivExprContext(TermContext *ctx);

    WIZDesignerDSLParser::TermContext *left = nullptr;
    antlr4::Token *op = nullptr;
    WIZDesignerDSLParser::TermContext *right = nullptr;
    std::vector<TermContext *> term();
    TermContext* term(size_t i);
    antlr4::tree::TerminalNode *MUL();
    antlr4::tree::TerminalNode *DIV();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  ParenthesesExprContext : public TermContext {
  public:
    ParenthesesExprContext(TermContext *ctx);

    ExprContext *expr();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  class  FactorExprContext : public TermContext {
  public:
    FactorExprContext(TermContext *ctx);

    FactorContext *factor();
    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
  };

  TermContext* term();
  TermContext* term(int precedence);
  class  FactorContext : public antlr4::ParserRuleContext {
  public:
    FactorContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    FunctionCallContext *functionCall();
    VariableContext *variable();
    ConstantContext *constant();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  FactorContext* factor();

  class  FunctionCallContext : public antlr4::ParserRuleContext {
  public:
    antlr4::Token *fn = nullptr;
    WIZDesignerDSLParser::ArgumentListContext *args = nullptr;
    FunctionCallContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *IDENTIFIER();
    ArgumentListContext *argumentList();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  FunctionCallContext* functionCall();

  class  ParamListContext : public antlr4::ParserRuleContext {
  public:
    ParamListContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    std::vector<antlr4::tree::TerminalNode *> IDENTIFIER();
    antlr4::tree::TerminalNode* IDENTIFIER(size_t i);

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ParamListContext* paramList();

  class  ArgumentListContext : public antlr4::ParserRuleContext {
  public:
    ArgumentListContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    std::vector<ExprContext *> expr();
    ExprContext* expr(size_t i);

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ArgumentListContext* argumentList();

  class  VariableContext : public antlr4::ParserRuleContext {
  public:
    VariableContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *LOCAL_IDENTIFIER();
    antlr4::tree::TerminalNode *IDENTIFIER();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  VariableContext* variable();

  class  ConstantContext : public antlr4::ParserRuleContext {
  public:
    ConstantContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    StringLiteralContext *stringLiteral();
    DecimalLiteralContext *decimalLiteral();
    FloatLiteralContext *floatLiteral();
    BooleanLiteralContext *booleanLiteral();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ConstantContext* constant();

  class  StringLiteralContext : public antlr4::ParserRuleContext {
  public:
    StringLiteralContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *STRING_LITERAL();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  StringLiteralContext* stringLiteral();

  class  FloatLiteralContext : public antlr4::ParserRuleContext {
  public:
    FloatLiteralContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    std::vector<DecimalLiteralContext *> decimalLiteral();
    DecimalLiteralContext* decimalLiteral(size_t i);

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  FloatLiteralContext* floatLiteral();

  class  DecimalLiteralContext : public antlr4::ParserRuleContext {
  public:
    DecimalLiteralContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *DECIMAL_LITERAL();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  DecimalLiteralContext* decimalLiteral();

  class  BooleanLiteralContext : public antlr4::ParserRuleContext {
  public:
    BooleanLiteralContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *TRUE();
    antlr4::tree::TerminalNode *FALSE();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  BooleanLiteralContext* booleanLiteral();

  class  SpecialVariableContext : public antlr4::ParserRuleContext {
  public:
    SpecialVariableContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    AttrRPROContext *attrRPRO();
    ArrayTypeNameContext *arrayTypeName();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  SpecialVariableContext* specialVariable();

  class  AttrRPROContext : public antlr4::ParserRuleContext {
  public:
    AttrRPROContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *IDENTIFIER();
    antlr4::tree::TerminalNode *ATTRIB_IDENTIFIER();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  AttrRPROContext* attrRPRO();

  class  ArrayTypeNameContext : public antlr4::ParserRuleContext {
  public:
    ArrayTypeNameContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *ATTRIB_IDENTIFIER();
    antlr4::tree::TerminalNode *IDENTIFIER();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ArrayTypeNameContext* arrayTypeName();

  class  ActionTypeWith1Context : public antlr4::ParserRuleContext {
  public:
    ActionTypeWith1Context(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    ActionTWICEContext *actionTWICE();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ActionTypeWith1Context* actionTypeWith1();

  class  ActionTWICEContext : public antlr4::ParserRuleContext {
  public:
    ActionTWICEContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ActionTWICEContext* actionTWICE();

  class  ActionTypeWith2Context : public antlr4::ParserRuleContext {
  public:
    ActionTypeWith2Context(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    ActionDIFFERENCEContext *actionDIFFERENCE();
    ActionTANFContext *actionTANF();
    ActionSUMContext *actionSUM();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ActionTypeWith2Context* actionTypeWith2();

  class  ActionDIFFERENCEContext : public antlr4::ParserRuleContext {
  public:
    ActionDIFFERENCEContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ActionDIFFERENCEContext* actionDIFFERENCE();

  class  ActionTANFContext : public antlr4::ParserRuleContext {
  public:
    ActionTANFContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ActionTANFContext* actionTANF();

  class  ActionSUMContext : public antlr4::ParserRuleContext {
  public:
    ActionSUMContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  ActionSUMContext* actionSUM();

  class  AttrNameContext : public antlr4::ParserRuleContext {
  public:
    AttrNameContext(antlr4::ParserRuleContext *parent, size_t invokingState);
    virtual size_t getRuleIndex() const override;
    antlr4::tree::TerminalNode *IDENTIFIER();
    antlr4::tree::TerminalNode *ATTRIB_IDENTIFIER();
    SpecialVariableContext *specialVariable();

    virtual void enterRule(antlr4::tree::ParseTreeListener *listener) override;
    virtual void exitRule(antlr4::tree::ParseTreeListener *listener) override;

    virtual std::any accept(antlr4::tree::ParseTreeVisitor *visitor) override;
   
  };

  AttrNameContext* attrName();


  bool sempred(antlr4::RuleContext *_localctx, size_t ruleIndex, size_t predicateIndex) override;

  bool exprSempred(ExprContext *_localctx, size_t predicateIndex);
  bool termSempred(TermContext *_localctx, size_t predicateIndex);

  // By default the static state used to implement the parser is lazily initialized during the first
  // call to the constructor. You can call this function if you wish to initialize the static state
  // ahead of time.
  static void initialize();

private:
};

