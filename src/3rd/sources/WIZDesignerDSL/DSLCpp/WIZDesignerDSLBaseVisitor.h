
// Generated from WIZDesignerDSL.g4 by ANTLR 4.13.2

#pragma once


#include "antlr4-runtime.h"
#include "WIZDesignerDSLVisitor.h"


/**
 * This class provides an empty implementation of WIZDesignerDSLVisitor, which can be
 * extended to create a visitor which only needs to handle a subset of the available methods.
 */
class  WIZDesignerDSLBaseVisitor : public WIZDesignerDSLVisitor {
public:

  virtual std::any visitProgram(WIZDesignerDSLParser::ProgramContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitStatementList(WIZDesignerDSLParser::StatementListContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitStatementListWithContinueBreak(WIZDesignerDSLParser::StatementListWithContinueBreakContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitStatement(WIZDesignerDSLParser::StatementContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitBreakWord(WIZDesignerDSLParser::BreakWordContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitContinueWord(WIZDesignerDSLParser::ContinueWordContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitNormalStatement(WIZDesignerDSLParser::NormalStatementContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitCommand(WIZDesignerDSLParser::CommandContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitGlobalVariableAssign(WIZDesignerDSLParser::GlobalVariableAssignContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitGlobalVariableDefine(WIZDesignerDSLParser::GlobalVariableDefineContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitLocalVariableAssign(WIZDesignerDSLParser::LocalVariableAssignContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitLocalVariableDefine(WIZDesignerDSLParser::LocalVariableDefineContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitFunctionDefine(WIZDesignerDSLParser::FunctionDefineContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitIfExpr(WIZDesignerDSLParser::IfExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitForExpr(WIZDesignerDSLParser::ForExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitForInExpr(WIZDesignerDSLParser::ForInExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitWhileExpr(WIZDesignerDSLParser::WhileExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitElseIfExpr(WIZDesignerDSLParser::ElseIfExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitExpressionToAtom(WIZDesignerDSLParser::ExpressionToAtomContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitComparisonExpr(WIZDesignerDSLParser::ComparisonExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitLogicalNotExpr(WIZDesignerDSLParser::LogicalNotExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitLogicalAndOrExpr(WIZDesignerDSLParser::LogicalAndOrExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitConditionalOperatorExpr(WIZDesignerDSLParser::ConditionalOperatorExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionTypeWith2Expr(WIZDesignerDSLParser::ActionTypeWith2ExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitMathPlusMinusExpr(WIZDesignerDSLParser::MathPlusMinusExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitRangeTakeBracketsExpr(WIZDesignerDSLParser::RangeTakeBracketsExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitTakeBracketsExpr(WIZDesignerDSLParser::TakeBracketsExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitBracketsExpr(WIZDesignerDSLParser::BracketsExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitAttrIndexOfExpr(WIZDesignerDSLParser::AttrIndexOfExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitGlobalVariableExpr(WIZDesignerDSLParser::GlobalVariableExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitUnitConvertExpr(WIZDesignerDSLParser::UnitConvertExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitNegateExpr(WIZDesignerDSLParser::NegateExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitAttrNumOfExpr(WIZDesignerDSLParser::AttrNumOfExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitParamAttrIndexOfExpr(WIZDesignerDSLParser::ParamAttrIndexOfExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionTypeWith1Expr(WIZDesignerDSLParser::ActionTypeWith1ExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitSpecialVariableExpr(WIZDesignerDSLParser::SpecialVariableExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitMathRemExpr(WIZDesignerDSLParser::MathRemExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitAttrOfExpr(WIZDesignerDSLParser::AttrOfExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitMathMulDivExpr(WIZDesignerDSLParser::MathMulDivExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitParenthesesExpr(WIZDesignerDSLParser::ParenthesesExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitFactorExpr(WIZDesignerDSLParser::FactorExprContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitFactor(WIZDesignerDSLParser::FactorContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitFunctionCall(WIZDesignerDSLParser::FunctionCallContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitParamList(WIZDesignerDSLParser::ParamListContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitArgumentList(WIZDesignerDSLParser::ArgumentListContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitVariable(WIZDesignerDSLParser::VariableContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitConstant(WIZDesignerDSLParser::ConstantContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitStringLiteral(WIZDesignerDSLParser::StringLiteralContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitFloatLiteral(WIZDesignerDSLParser::FloatLiteralContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitDecimalLiteral(WIZDesignerDSLParser::DecimalLiteralContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitBooleanLiteral(WIZDesignerDSLParser::BooleanLiteralContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitSpecialVariable(WIZDesignerDSLParser::SpecialVariableContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitAttrRPRO(WIZDesignerDSLParser::AttrRPROContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitArrayTypeName(WIZDesignerDSLParser::ArrayTypeNameContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionTypeWith1(WIZDesignerDSLParser::ActionTypeWith1Context *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionTWICE(WIZDesignerDSLParser::ActionTWICEContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionTypeWith2(WIZDesignerDSLParser::ActionTypeWith2Context *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionDIFFERENCE(WIZDesignerDSLParser::ActionDIFFERENCEContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionTANF(WIZDesignerDSLParser::ActionTANFContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitActionSUM(WIZDesignerDSLParser::ActionSUMContext *ctx) override {
    return visitChildren(ctx);
  }

  virtual std::any visitAttrName(WIZDesignerDSLParser::AttrNameContext *ctx) override {
    return visitChildren(ctx);
  }


};

