#pragma once

#include "../common/WDOperationList.h"
#include "../geometry/WDGeometry.h"
#include "../material/renderState/WDRenderState.h"
#include "WDKeyPoint.h"
#include "WDPLine.h"

WD_NAMESPACE_BEGIN

class WDNode;
class WDCore;

/**
 * @brief 线对象
*/
class WD_API WDGraphableLine
{
public:
    using Style = WDRenderStateLineStipple::Style;
    /**
     * @brief 线顶点的模式
    */
    enum VMode 
    {
        // 顶点数组中, 每两个点构成一条线段
        VM_Lines = 0x0001,
        // 顶点数组中，依次连接第一个点第二个点...到最后一个点, 并连接最后一个点到第一个点，构成一个环
        VM_Loop  = 0x0002,
        // 顶点数组中，依次连接第一个点第二个点...到最后一个点, 构成一条折线
        VM_Strip = 0x0003,
    };

    // 线的顶点数组
    std::vector<FVec3> points;
    // 线顶点的模式
    VMode vMode = VM_Lines;
    // 颜色
    Color color = Color::white;
    // 线宽
    float lineWidth = 1.0f;
    // 线样式
    Style style = Style::SolidLine;
public:
    /**
     * @brief 根据当前线对象已经添加的点计算包围盒
     * @param thickness 当所有点 共线/共面时， 需要给包围盒增加一个厚度以保证包围盒的 min ≠ max, 这个值就是表示厚度值
     * @return 结果包围盒
    */
    DAabb3 calcAabb(float thickness = 1.0f) const;
    /**
     * @brief 合并当前对象的包围盒到指定的包围盒target
     * @param target 目标包围盒
     * @return
    */
    DAabb3& mergeToAabb(DAabb3& target) const;
    /**
    * @brief 与射线相交
    * @param transformMatrix 数据变换矩阵，如果是节点数据，通常指节点的 globalTransform 矩阵
    * @param param 拾取参数
    *   此次拾取的规则配置信息，其中也包含要使用的射线，屏幕坐标以及相机信息
    */
    bool pickup(const DMat4& transformMatrix
        , const WDPickupParam& param
        , WDSelectionInterface::PickupResult& outResult) const;
    /**
    * @brief 框选
    * @param transformMatrix 数据变换矩阵，如果是节点数据，通常指节点的 globalTransform 矩阵
    * @param param 框选参数，此次框选的规则配置信息
    *   此次框选的规则配置信息，其中也包含要使用的框选视椎体以及相机信息
    */
    WDSelectionInterface::FrameSelectResult frameSelect(const DMat4& transformMatrix
        , const WDFrameSelectParam& param) const;
};
using WDGraphableLines = std::vector<WDGraphableLine>;

/**
 * @brief 文本对象
*/
class WD_API WDGraphableText
{
public:
    /**
     * @brief 水平对齐策略
    */
    enum HAlign
    {
        // 左对齐
        HA_Left,
        // 中心对齐,
        HA_Center,
        // 右对齐
        HA_Right,
    };
    /**
     * @brief 垂直对齐策略
    */
    enum VAlign
    {
        // 顶部对齐
        VA_Top,
        // 中心对齐
        VA_Center,
        // 底部对齐
        VA_Bottom,
    };
public:
    /**
     * @brief 文本内容
    */
    std::wstring text;
    /**
     * @brief 是否是像素固定模式
     *  当取值为true时: fontSize 为屏幕像素单位
     *  当取值为false时: fontSize 为世界坐标单位
    */
    bool fixedPixel = false;
    /**
     * @brief 文本位置, 三维空间坐标
    */
    FVec3 position = FVec3::Zero();
    /**
     * @brief 文本右方向
     *  当文本右方向值未指定时，文字将固定朝向屏幕(布告板文字)
    */
    std::optional<FVec3> rightDir = std::nullopt;
    /**
     * @brief 文本上方向
     *  当文本上方向值未指定时，文字将固定朝向屏幕(布告板文字)
    */
    std::optional<FVec3> upDir = std::nullopt;
    /**
     * @brief 绘制的文本颜色
    */
    Color color = Color::white;
    /**
     * @brief 文字大小
     *  单位根据 fixedPixel 取值而变化
    */
    int fontSize = 16;
    /**
     * @brief 水平对齐方式
    */
    HAlign hAlign = HAlign::HA_Left;
    /**
     * @brief 垂直对齐方式
    */
    VAlign vAlign = VAlign::VA_Top;
    /**
     * @brief 文本水平(spacing.x())以及垂直(spacing.y())间距
     *  单位根据 fixedPixel 取值而变化
    */
    FVec2 spacing = FVec2(0.0f);
};
using WDGraphableTexts = std::vector<WDGraphableText>;

/**
* @brief 可图形化对象接口
*/
class WD_API WDGraphableInterface
{
public:
    /**
     * @brief 几何体类型
    */
    enum GGeomType
    {
        // 基本几何体, 一般用于模型导出, 
        GGT_Basic = 0,
        // 碰撞几何体, 一般用于碰撞检查
        GGT_Collision
    };
public:
    /**
    * @brief 数据对象观察者
    */
    class GObserver
    {
    public:
        /**
         * @brief 移除所有图形对象之前通知
         */
        virtual void onRemoveBefore(WDGraphableInterface& sender) = 0;
        /**
         * @brief 添加所有图形对象之后通知
         */
        virtual void onAddAfter(WDGraphableInterface& sender) = 0;
    };
    /**
    * @brief 数据对象观察者列表
    */
    using GObservers = WDOperationList<GObserver*>;
public:
    /**
    * @brief 当前绘制的所有模型的aabb包围盒 Graphable Aabb
    *   子类重写
    */
    virtual DAabb3 gRenderAabb() const
    {
        return DAabb3::Null();
    }
    /**
     * @brief 获取当前绘制使用的几何体列表， 包含了带实体标记,中心线标记，负实体标记，碰撞相关标记，保温标记等的所有几何体
     */
    virtual const WDGeometries* gRenderGeoms() const
    {
        return nullptr;
    }
    /**
     * @brief 获取当前绘制使用的几何体列表是否已经被开孔
     * @return 返回结果说明: 
     *  - std::nullopt 不支持获取是否已被开孔
     *  - first:
     *      true 已经被开孔
     *      false 未被开孔
     *  - second
     *      角度公差
     */
    virtual std::optional<std::pair<bool, float> > gRenderGeomsAlreadyHoles() const 
    {
        return std::nullopt;
    }
    /**
    * @brief 获取点集数据 Graphable PointSets
    *   子类重写
    */
    virtual const WDKeyPoints* gKeyPoints()
    {
        return nullptr;
    }
    /**
    * @brief 获取PLine集数据 Graphable PLineSet
    *   子类重写
    */
    virtual const WDPLines* gPLines()
    {
        return nullptr;
    }
    /**
     * @brief 获取Line集数据 WDGraphableLines
    *   子类重写
    */
    virtual const WDGraphableLines* gLines() 
    {
        return nullptr;
    }
    /**
     * @brief 获取Text集数据 WDGraphableTexts
    *   子类重写
    */
    virtual const WDGraphableTexts* gTexts() 
    {
        return nullptr;
    }
public:
    /**
     * @brief 获取模型是否具有孔洞
     *  用于快速判断当前节点是否需要开孔
    */
    virtual bool hasHoles() const 
    {
        return false;
    }
    /**
     * @brief 获取用于给父节点开孔的负实体列表
     *  用于给当前对象的父对象进行开孔
     * @param pOutNGeoms 可选项, 如果不指定时，只用于获取是否具有负实体
     * @return 是否具有负实体
     */
    virtual bool negativeGeometry(WDGeometries* pOutNGeoms = nullptr) const 
    {
        WDUnused(pOutNGeoms);
        return false;
    }
    /**
     * @brief 获取所属的节点
     */
    virtual const WDNode& ownerNode() const = 0;
public:
    /**
     * @brief 获取是否包含有给父节点开孔用的负几何体
     *  用于给当前对象的父对象判断是否需要开孔
     */
    inline bool hasNegativeGeometry() const
    {
        return this->negativeGeometry(nullptr);
    }
    /**
     * @brief 根据系统配置选项获取绘制的几何体，一般是拾取时使用
     *  比如： 
     *      该接口中默认过滤掉不带实体标志，但是带了硬占有标志的几何体
     *      系统配置中未开启软占有绘制时，该接口将过滤掉所有带有软占有但不带实体标志的几何体
     *      系统配置中未开启保温绘制时，该接口将过滤掉所有带保温标志的几何体
     * @param core core
     * @return 根据系统配置过滤后的绘制几何体
     */
    WDGeometries gRenderGeomsWithSystemOption(WDCore& core) const;
public:
    /**
    * @brief 指定类型获取几何体列表
    * @param gType 几何体类型
    * @param holesParam 孔洞参数
    *   first: 决定是否开孔
    *   second: 如果开孔，这个参数指定开孔的角度公差
    * @param pOutAabb 可选项，是否计算结果几何体列表的包围盒
     * @param lod 可选项，如果指定了lod, 则使用指定的LOD重新生成几何体并返回, 否则使用系统LOD生成的几何体
    */
    virtual WDGeometries gGeoms(GGeomType gType
        , const std::pair<bool, float>& holesParam = std::make_pair(false, 10.0f)
        , DAabb3* pOutAabb = nullptr
        , const std::optional<MeshLODSelection>& lod = std::nullopt)
    {
        WDUnused(gType);
        WDUnused(holesParam);
        WDUnused(pOutAabb);
        WDUnused(lod);
        return WDGeometries();
    }
public:
    /**
    * @brief 获取观察者列表 const
    */
    inline const GObservers& gObservers() const
    {
        return _observers;
    }
    /**
    * @brief 添加观察者
    */
    void addGObserver(GObserver* pObserver);
    /**
    * @brief 移除观察者
    */
    void removeGObserver(GObserver* pObserver);
protected:
    /**
    * @brief 观察者添加之后通知
    *   子类可重写监听
    */
    virtual void onGObserverAdded(GObserver* pObserver)
    {
        WDUnused(pObserver);
    }
    /**
    * @brief 观察者移除之后通知
    *   子类可重写监听
    */
    virtual void onGObserverRemoved(GObserver* pObserver)
    {
        WDUnused(pObserver);
    }
protected:
    /**
     * @brief 发送移除之前通知, 子类调用
     */
    void noticeRemoveBefore();
    /**
     * @brief 发送添加之后通知, 子类调用
     */
    void noticeAddAfter();
private:
    //观察者列表
    GObservers _observers;
};

WD_NAMESPACE_END

