#include "WDObjectAxisEditor.h"
#include "../WDViewer.h"
#include "businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "viewer/objectAxisEditor/WDAxisEditObjectNode.h"
#include "../../undoRedo/WDUndoStack.h"

WD_NAMESPACE_BEGIN

//节点编辑command基类
class ObjectEditCommand : public WDUndoCommand
{
public:
    // 特殊处理，忽略第一次 redo
    bool bIgnoreFirstRedo = false;
    bool bTriggerUpdate = true;
private:
    WDObjectAxisEditor::Object::SharedPtr _pObject;
    WDCore& _core;
    bool _bAutoRepaint;
public:
    ObjectEditCommand(WDCore& core
        , WDObjectAxisEditor::Object::SharedPtr pObject
        , const std::string& name
        , bool bAutoRepaint)
        : WDUndoCommand(name)
        , _core(core)
        , _pObject(pObject)
        , _bAutoRepaint(bAutoRepaint)
    {
    }
    virtual ~ObjectEditCommand()
    {
    }
public:
    virtual void redoP() override final
    {
        if (bIgnoreFirstRedo)
        {
            // 这里针对节点特殊处理，即使第一次忽略了命令，也需要调用到节点的强制触发更新
            // 因为节点移动过程中如果频繁调用触发更新会导致卡顿，所以建议在一次操作完成之后再做触发更新
            auto pNodeObject = std::dynamic_pointer_cast<WDAxisEditObjectNode>(_pObject);
            if (pNodeObject != nullptr)
            {
                for (auto& each : pNodeObject->nodes())
                {
                    auto pNode = each.lock();
                    if (pNode == nullptr)
                        continue;
                    pNode->triggerUpdate(true);
                }
            }
            bIgnoreFirstRedo = false;
            return;
        }

        if (_pObject == nullptr)
            return;

        this->onRedo(*_pObject);
        WDUndoCommand::redoP();

        if (_bAutoRepaint)
            _core.needRepaint();
    }
    virtual void undoP() override final
    {
        if (_pObject == nullptr)
            return;

        WDUndoCommand::undoP();
        this->onUndo(*_pObject);

        if (_bAutoRepaint)
            _core.needRepaint();
    }
protected:
    // 子类重写
    virtual void onRedo(WDObjectAxisEditor::Object& object) = 0;
    // 子类重写
    virtual void onUndo(WDObjectAxisEditor::Object& object) = 0;
};
//节点移动command
class ObjectEditMCommand :public ObjectEditCommand
{
protected:
    DVec3 _offset;
public:
    ObjectEditMCommand(WDCore& core
        , WDObjectAxisEditor::Object::SharedPtr pObject, const DVec3& offset
        , bool bAutoRepaint)
        : ObjectEditCommand(core, pObject, "Object Edit Move Command", bAutoRepaint)
        , _offset(offset)
    {
    }
public:
    virtual void onRedo(WDObjectAxisEditor::Object& object) override
    {
        WDObjectAxisEditor::Object::Move(object, _offset);
    }
    virtual void onUndo(WDObjectAxisEditor::Object& object) override
    {
        WDObjectAxisEditor::Object::Move(object, -_offset);
    }
};
//节点旋转command
class ObjectEditRCommand :public ObjectEditCommand
{
protected:
    DVec3 _axis;
    real _angle;
    DVec3 _center;
public:
    ObjectEditRCommand(WDCore& core
        , WDObjectAxisEditor::Object::SharedPtr pObject
        , const DVec3& axis, real angle, const DVec3& center
        , bool bAutoRepaint)
        : ObjectEditCommand(core, pObject, "Object Edit Rotate Command", bAutoRepaint)
        , _axis(axis)
        , _angle(angle)
        , _center(center)
    {
    }
public:
    virtual void onRedo(WDObjectAxisEditor::Object& object) override
    {
        WDObjectAxisEditor::Object::Rotate(object, _axis, _angle, _center);
    }
    virtual void onUndo(WDObjectAxisEditor::Object& object) override
    {
        WDObjectAxisEditor::Object::Rotate(object, _axis, -_angle, _center);
    }
};
//节点缩放command
class ObjectEditSCommand :public ObjectEditCommand
{
protected:
    DVec3 _scale;
public:
    ObjectEditSCommand(WDCore& core
        , WDObjectAxisEditor::Object::SharedPtr pObject
        , const DVec3& scale
        , bool bAutoRepaint)
        : ObjectEditCommand(core, pObject, "Object Edit Scale Command", bAutoRepaint)
        , _scale(scale)
    {
    }
public:
    virtual void onRedo(WDObjectAxisEditor::Object& object) override
    {
        WDObjectAxisEditor::Object::Scale(object, _scale);
    }
    virtual void onUndo(WDObjectAxisEditor::Object& object) override
    {
        DVec3 invScaling = DVec3(1.0) / _scale;
        WDObjectAxisEditor::Object::Scale(object, invScaling);
    }
};


//节点编辑command基类
class ObjectEditMRCommand : public WDUndoCommand
{
public:
    // 特殊处理，忽略第一次 redo
    bool bIgnoreFirstRedo = false;
    WDObjectAxisEditor::Object::SharedPtr _pObject;
    WDUndoCommand* pMCommand = nullptr;
    WDUndoCommand* pRCommand = nullptr;
    WD::WDNode::WeakPtr prevNode;
private:
    WDCore& _core;
public:
    ObjectEditMRCommand(WDCore& core
    , WDObjectAxisEditor::Object::SharedPtr pObject
    , WD::WDNode::WeakPtr pPrevNode)
        : WDUndoCommand("ObjectEditMRCommand")
        , _core(core), _pObject(pObject), prevNode(pPrevNode)
    {
    }
    virtual ~ObjectEditMRCommand()
    {
        if (pMCommand != nullptr)
        {
            delete pMCommand;
            pMCommand = nullptr;
        }
        if (pRCommand != nullptr)
        {
            delete pRCommand;
            pRCommand = nullptr;
        }
    }
public:
    virtual void redoP() override final
    {
        if (bIgnoreFirstRedo)
        {
            bIgnoreFirstRedo = false;
            update();
            auto pNodeObject = std::dynamic_pointer_cast<WDAxisEditObjectNode>(_pObject);
            if (pNodeObject != nullptr)
            {
                auto& nodes = pNodeObject->nodes();
                if (nodes.empty())
                    return;
                auto pFitstNode = pNodeObject->firstValidNode();
                if (pFitstNode == nullptr)
                    return;
                pFitstNode->triggerUpdate(true);
            }
            return;
        }

        WDUndoCommand::redoP();

        if (pRCommand != nullptr)
            pRCommand->redo();
        if (pMCommand != nullptr)
            pMCommand->redo();
        update();
        auto pNodeObject = std::dynamic_pointer_cast<WDAxisEditObjectNode>(_pObject);
        if (pNodeObject != nullptr)
        {
            auto& nodes = pNodeObject->nodes();
            if (nodes.empty())
                return;
            auto pFitstNode = pNodeObject->firstValidNode();
            if (pFitstNode == nullptr)
                return;
            pFitstNode->triggerUpdate(true);
        }
    }
    virtual void undoP() override final
    {
        WDUndoCommand::undoP();

        if (pMCommand != nullptr)
            pMCommand->undo();
        if (pRCommand != nullptr)
            pRCommand->undo();
        update();
        auto pNodeObject = std::dynamic_pointer_cast<WDAxisEditObjectNode>(_pObject);
        if (pNodeObject != nullptr)
        {
            auto& nodes = pNodeObject->nodes();
            if (nodes.empty())
                return;
            auto pFitstNode = pNodeObject->firstValidNode();
            if (pFitstNode == nullptr)
                return;
            pFitstNode->triggerUpdate(true);
        }
    }
private:
    void update()
    {
        // 特殊处理
        auto pNodeObject = std::dynamic_pointer_cast<WDAxisEditObjectNode>(_pObject);
        if (pNodeObject != nullptr)
        {
            auto& nodes = pNodeObject->nodes();
            if (nodes.empty())
                return;
            auto pFitstNode = pNodeObject->firstValidNode();
            if (pFitstNode == nullptr)
                return;
            auto pBranch = pFitstNode->parent();
            if (pBranch == nullptr || !pBranch->isType("BRAN"))
                return;

            auto pPrevNode = prevNode.lock();
            if (pPrevNode == nullptr)
                return;

            // 判断所有管件是否在一段直管上, 即管件之前没有除直管段的其他类型管件
            if (nodes.size() > 1)
            {
                std::set<WD::WDNode::SharedPtr> nodesSet;
                for (const auto& node : nodes)
                {
                    auto pNode = node.lock();
                    if (pNode == nullptr || pNode->isAnyOfType("TUBI"))
                        continue;
                    // 所有管件必须在同一分支下
                    if (( pNode->parent() != pBranch ))
                        return;

                    if (nodesSet.find(pNode) == nodesSet.end())
                        nodesSet.emplace(pNode);
                }
                if (nodesSet.size() != nodes.size())
                    return;
                std::map<size_t, WD::WDNode::SharedPtr> pipeComsIndexs;
                
                size_t index = 0;
                for (auto& pChild : pBranch->children())
                {
                    if (nodesSet.find(pChild) != nodesSet.end())
                        pipeComsIndexs.emplace(index, pChild);
                    ++index;
                }
                if (nodesSet.size() != pipeComsIndexs.size())
                {
                    assert(false);
                    return;
                }
                auto preItr = pipeComsIndexs.begin();
                auto nexItr = pipeComsIndexs.begin();
                if (nexItr == pipeComsIndexs.end())
                {
                    assert(false);
                    return;
                }
                ++nexItr;
                for ( ;nexItr != pipeComsIndexs.end();++preItr, ++nexItr)
                {
                    if (( preItr->first + 1 ) != nexItr->first)
                        return;
                }
                auto startIdx = pipeComsIndexs.begin()->first;
                if (startIdx == 0)
                {
                    prevNode = pBranch;
                }
                else
                {
                    int prevIdx = static_cast<int>(startIdx) - 1;
                    WD::WDNode::SharedPtr pTmpPrevNode = nullptr;
                    while (prevIdx >= 0)
                    {
                        pTmpPrevNode = pBranch->childAt(prevIdx);
                        if (pTmpPrevNode != nullptr && !pTmpPrevNode->isType("TUBI"))
                            break;
                        --prevIdx;
                    }
                    if (pTmpPrevNode != nullptr && !pTmpPrevNode->isType("TUBI"))
                        prevNode = pTmpPrevNode;
                    else
                        prevNode = pBranch;
                }
                WDBMDPipeUtils::MoveComponents(*pBranch, pipeComsIndexs.begin()->second, pipeComsIndexs.rbegin()->second, pPrevNode);
            }
            else
            {
                uint index = 0;
                std::optional<size_t> startIdx = 0;
                for (auto& pChild : pBranch->children())
                {
                    if (pFitstNode == pChild)
                        startIdx = index;
                    ++index;
                }
                if (startIdx)
                {
                    if (startIdx.value() == 0)
                    {
                        prevNode = pBranch;
                    }
                    else
                    {
                        int prevIdx = static_cast<int>(startIdx.value()) - 1;
                        WD::WDNode::SharedPtr pTmpPrevNode = nullptr;
                        while (prevIdx >= 0)
                        {
                            pTmpPrevNode = pBranch->childAt(prevIdx);
                            if (pTmpPrevNode != nullptr && !pTmpPrevNode->isType("TUBI"))
                                break;
                            --prevIdx;
                        }
                        if (pTmpPrevNode != nullptr && !pTmpPrevNode->isType("TUBI"))
                            prevNode = pTmpPrevNode;
                        else
                            prevNode = pBranch;
                    }
                    WDBMDPipeUtils::MoveComponents(*pBranch, pFitstNode, pFitstNode, pPrevNode);
                }
            }
        }
    }
};


bool WDObjectAxisEditor::Object::Move(Object& object
    , const DVec3& offset
    , bool bTriggerUpdate)
{
    if (offset.lengthSq() <= NumLimits<float>::Epsilon)
        return false;

    if(!object.move(offset))
        return false;
    object.update(bTriggerUpdate);
    return true;
}
bool WDObjectAxisEditor::Object::Rotate(Object& object
    , const DVec3& axis, real angle, const DVec3& center
    , bool bTriggerUpdate)
{
    if (axis.lengthSq() <= NumLimits<float>::Epsilon
        || Abs(angle) <= NumLimits<float>::Epsilon)
        return false;

    if (!object.rotate(axis, angle, center))
        return false;
    object.update(bTriggerUpdate);
    return true;
}
bool WDObjectAxisEditor::Object::Scale(Object& object
    , const DVec3& scaling
    , bool bTriggerUpdate)
{
    if ((Abs(scaling.x - 1.0) <= NumLimits<float>::Epsilon)
        && (Abs(scaling.y - 1.0) <= NumLimits<float>::Epsilon)
        && (Abs(scaling.z - 1.0) <= NumLimits<float>::Epsilon))
        return false;
    if (!object.scale(scaling))
        return false;
    object.update(bTriggerUpdate);
    return true;
}

WDUndoCommand* WDObjectAxisEditor::Object::MakeMoveCommand(WDCore& core
    , Object::SharedPtr pObject
    , const DVec3& offset
    , bool bAutoRepaint)
{
    if (pObject == nullptr || offset.lengthSq() < NumLimits<float>::Epsilon)
        return nullptr;

    return new ObjectEditMCommand(core, pObject, offset, bAutoRepaint);
}
WDUndoCommand* WDObjectAxisEditor::Object::MakeRotateCommand(WDCore& core
    , Object::SharedPtr pObject
    , const DVec3& axis, real angle, const DVec3& center
    , bool bAutoRepaint)
{
    if (pObject == nullptr
        || axis == WD::DVec3::Zero()
        || Abs(angle) <= NumLimits<float>::Epsilon)
        return nullptr;

    return new ObjectEditRCommand(core, pObject, axis, angle, center, bAutoRepaint);
}
WDUndoCommand* WDObjectAxisEditor::Object::MakeScaleCommand(WDCore& core
    , Object::SharedPtr pObject
    , const DVec3& scaling
    , bool bAutoRepaint)
{
    if (pObject == nullptr
        || Abs(scaling.x) < NumLimits<float>::Epsilon
        || Abs(scaling.y) < NumLimits<float>::Epsilon
        || Abs(scaling.z) < NumLimits<float>::Epsilon)
        return nullptr;

    return new ObjectEditSCommand(core, pObject, scaling, bAutoRepaint);
}

class WDObjectAxisEditorPrivate
{
public:
    //
    WDObjectAxisEditor::Object::SharedPtr       _pObject;

    // 编辑类型
    WDObjectAxisEditor::EditType                _eType;
    // 编辑坐标系类型
    WDObjectAxisEditor::EditCoordinateSysType   _ecsType;

    // 移动编辑轴
    WDEditAxisMove _mAxis;
    // 旋转编辑轴
    WDEditAxisRotate _rAxis;
    // 缩放编辑轴
    WDEditAxisScale _sAxis;
    // 移动旋转编辑轴
    WDEditAxisMoveRotate _mrAxis;
    // 单轴移动旋转编辑轴
    WDEditSingleAxisMoveRotate _singleMRAxis;

    std::map< WDObjectAxisEditor::EditType, WDEditAxis* > _typeAxes;

    WDObjectAxisEditor::ObjectChangedDelegate       _objectChangedDelegate;
    WDObjectAxisEditor::EditTypeChangedDelegate     _editTypeChangedDelegate;
    WDObjectAxisEditor::ObjectEditStartDelegate     _objectEditStartDelegate;
    WDObjectAxisEditor::ObjectEditFinishedDelegate  _objectEditFinishedDelegate;

    WDObjectAxisEditor& _d;
    friend class WDObjectAxisEditor;
public:
    WDObjectAxisEditorPrivate(WDObjectAxisEditor& d) :_d(d)
    {
        _eType      = WDObjectAxisEditor::EditType::ET_None;
        _ecsType    = WDObjectAxisEditor::EditCoordinateSysType::ECST_Local;

        _mAxis.mDelegate() += {this,  & WDObjectAxisEditorPrivate::onMDelegate};
        _rAxis.mDelegate() += {this,  & WDObjectAxisEditorPrivate::onRDelegate};
        _sAxis.mDelegate() += {this,  & WDObjectAxisEditorPrivate::onSDelegate};
        _mrAxis.mDelegate() += {this, & WDObjectAxisEditorPrivate::onMRDelegate};
        _singleMRAxis.mDelegate() += {this, & WDObjectAxisEditorPrivate::onSingleMRDelegate};

        _typeAxes[WDObjectAxisEditor::ET_Move]              = &_mAxis;
        _typeAxes[WDObjectAxisEditor::ET_Rotate]            = &_rAxis;
        _typeAxes[WDObjectAxisEditor::ET_Scale]             = &_sAxis;
        _typeAxes[WDObjectAxisEditor::ET_MoveRotate]        = &_mrAxis;
        _typeAxes[WDObjectAxisEditor::ET_SingleMoveRotate]  = &_singleMRAxis;
    }
    ~WDObjectAxisEditorPrivate()
    {
        _typeAxes.clear();

        _mAxis.mDelegate() -= {this,  & WDObjectAxisEditorPrivate::onMDelegate};
        _rAxis.mDelegate() -= {this,  & WDObjectAxisEditorPrivate::onRDelegate};
        _sAxis.mDelegate() -= {this,  & WDObjectAxisEditorPrivate::onSDelegate};
        _mrAxis.mDelegate() -= {this, & WDObjectAxisEditorPrivate::onMRDelegate};
        _singleMRAxis.mDelegate() -= {this, & WDObjectAxisEditorPrivate::onSingleMRDelegate};
    }
public:
    WDEditAxis* getCurrentTypeAxis() const 
    {
        auto fItr = _typeAxes.find(_eType);
        if (fItr == _typeAxes.end())
            return nullptr;
        return fItr->second;
    }
private:
    WDObjectAxisEditor::ObjectEditFinishedDelegate::InvokeDelegate finishInvokeDelegate() 
    {
        return WDObjectAxisEditor::ObjectEditFinishedDelegate::InvokeDelegate([](bool r)
            {
                return r;
            });
    }

    void onMDelegate(WDEditAxis::EditStatus status, const DVec3& vec, const DVec3& absVec
        , WDEditAxisMove& sender)
    {
        WDUnused(sender);
        if (_pObject == nullptr)
            return;

        switch (status)
        {
        case WDEditAxis::ES_Start:
            {
                // 通知编辑开始
                auto fInvoke = finishInvokeDelegate();
                _d._startSuccess = _objectEditStartDelegate(fInvoke, _pObject, _d);
                if (!_d._startSuccess)
                    sender.mouseButtonRelease(_d._viewer.context(), IVec2(0, 0), WDEditAxis::MouseButton::MB_Left);
            }
        break;
        case WDEditAxis::ES_Editting:
        {
            if (_d._startSuccess)
                WDObjectAxisEditor::Object::Move(*_pObject, vec, false);
        }
        break;
        case WDEditAxis::ES_End:
        {
            moveEnd(absVec, true);
        }
        break;
        case WDEditAxis::ES_Command:
        {
            moveEnd(absVec, false);
        }
        break;
        default:
            break;
        }
    }
    void onRDelegate(WDEditAxis::EditStatus status, const DVec3& axis, real angle, real absAngle
        , WDEditAxisRotate& sender)
    {
        WDUnused(sender);

        if (_pObject == nullptr)
            return;

        switch (status)
        {
        case WDEditAxis::ES_Start:
            {
                // 通知编辑开始
                auto fInvoke = finishInvokeDelegate();
                _d._startSuccess = _objectEditStartDelegate(fInvoke, _pObject, _d);
                if (!_d._startSuccess)
                    sender.mouseButtonRelease(_d._viewer.context(), IVec2(0, 0), WDEditAxis::MouseButton::MB_Left);
            }
            break;
        case WDEditAxis::ES_Editting:
            {
                if (_d._startSuccess)
                    WDObjectAxisEditor::Object::Rotate(*_pObject, axis, angle, sender.position(), false);
            }
            break;
        case WDEditAxis::ES_End:
            {
                rotateEnd(axis, absAngle, sender.position(), true);
            }
            break;
        case WDEditAxis::ES_Command:
            {
                rotateEnd(axis, absAngle, sender.position(), false);
            }
            break;
        default:
            break;
        }
    }
    void onSDelegate(WDEditAxis::EditStatus status, const DVec3& scale, const DVec3& absScale
        , WDEditAxisScale& sender)
    {
        WDUnused(sender);

        if (_pObject == nullptr)
            return;

        //根据节点变换锁定计算能够缩放的偏移
        switch (status)
        {
        case WDEditAxis::ES_Start:
            {
                // 通知编辑开始
                auto fInvoke = finishInvokeDelegate();
                _d._startSuccess = _objectEditStartDelegate(fInvoke, _pObject, _d);
                if (!_d._startSuccess)
                    sender.mouseButtonRelease(_d._viewer.context(), IVec2(0, 0), WDEditAxis::MouseButton::MB_Left);
            }
            break;
        case WDEditAxis::ES_Editting:
            {
                if (_d._startSuccess)
                    WDObjectAxisEditor::Object::Scale(*_pObject, scale, false);
            }
            break;
        case WDEditAxis::ES_End:
            {
                scaleEnd(absScale, true);
            }
            break;
        case WDEditAxis::ES_Command:
            {
                scaleEnd(absScale, false);
            }
            break;
        default:
            break;
        }
    }
    void onMRDelegate(WDEditAxis::EditStatus status, WDEditAxisMoveRotate::HandleType hType
        , const DVec4& relativeOffset, const DVec4& absoluteOffset
        , WDEditAxisMoveRotate& sender) 
    {
        WDUnused(sender);

        if (_pObject == nullptr)
            return;

        //根据节点变换锁定计算能够缩放的偏移
        switch (status)
        {
        case WDEditAxis::ES_Start:
        {
            // 通知编辑开始
            auto fInvoke = finishInvokeDelegate();
            _d._startSuccess = _objectEditStartDelegate(fInvoke, _pObject, _d);
            if (!_d._startSuccess)
                sender.mouseButtonRelease(_d._viewer.context(), IVec2(0, 0), WDEditAxis::MouseButton::MB_Left);
        }
        break;
        case WDEditAxis::ES_Editting:
        {
            if (_d._startSuccess)
            {
                switch (hType)
                {
                case WD::WDEditAxisMoveRotate::HT_Move:
                    {
                        if (Abs(relativeOffset.w) <= NumLimits<float>::Epsilon)
                            return;
                        WDObjectAxisEditor::Object::Move(*_pObject, relativeOffset.xyz() * relativeOffset.w, false);
                    }
                    break;
                case WD::WDEditAxisMoveRotate::HT_Rotate:
                    {
                        if (Abs(relativeOffset.w) <= NumLimits<float>::Epsilon)
                            return;
                        WDObjectAxisEditor::Object::Rotate(*_pObject
                            , relativeOffset.xyz()
                            , relativeOffset.w
                            , sender.position()
                            , false);
                    }
                    break;
                default:
                    break;
                }
            }
        }
        break;
        case WDEditAxis::ES_End:
        {
            switch (hType)
            {
            case WD::WDEditAxisMoveRotate::HT_Move:
                {
                    moveEnd(absoluteOffset.xyz() * absoluteOffset.w, true);
                }
                break;
            case WD::WDEditAxisMoveRotate::HT_Rotate:
                {
                    rotateEnd(absoluteOffset.xyz(), absoluteOffset.w, sender.position(), true);
                }
                break;
            default:
                break;
            }

        }
        break;
        case WDEditAxis::ES_Command:
        {
            //校验此次的移动累计偏移量是否接近于0，如果接近于0，则只做通知
            if (Abs(absoluteOffset.w) <= NumLimits<float>::Epsilon)
                return;
            switch (hType)
            {
            case WD::WDEditAxisMoveRotate::HT_Move:
                {
                    moveEnd(absoluteOffset.xyz() * absoluteOffset.w, false);
                }
                break;
            case WD::WDEditAxisMoveRotate::HT_Rotate:
                {
                    rotateEnd(absoluteOffset.xyz(), absoluteOffset.w, sender.position(), false);
                }
                break;
            default:
                break;
            }
        }
        break;
        default:
            break;
        }
    }
    void onSingleMRDelegate(WDEditAxis::EditStatus status
        , WDEditSingleAxisMoveRotate::HandleType hType
        , const DVec4& relativeMOffset
        , const DVec4& absoluteMOffset
        , const DVec4& relativeROffset
        , const DVec4& absoluteROffset
        , const DVec3& center
        , WDEditSingleAxisMoveRotate& sender) 
    {
        WDUnused(sender);

        if (_pObject == nullptr)
            return;

        //根据节点变换锁定计算能够缩放的偏移
        switch (status)
        {
        case WDEditAxis::ES_Start:
            {
                // 通知编辑开始
                auto fInvoke = finishInvokeDelegate();
                _d._startSuccess = _objectEditStartDelegate(fInvoke, _pObject, _d);
                if (!_d._startSuccess)
                    sender.mouseButtonRelease(_d._viewer.context(), IVec2(0, 0), WDEditAxis::MouseButton::MB_Left);
            }
            break;
        case WDEditAxis::ES_Editting:
            {
                if (_d._startSuccess)
                {
                    switch (hType)
                    {
                    case WD::WDEditAxisMoveRotate::HT_Move:
                        {
                            if (Abs(relativeROffset.w) > NumLimits<float>::Epsilon)
                            {
                                WDObjectAxisEditor::Object::Rotate(*_pObject
                                    , relativeROffset.xyz()
                                    , relativeROffset.w
                                    , sender.position()
                                    , false);
                            }
                            if (Abs(relativeMOffset.w) <= NumLimits<float>::Epsilon)
                                return;
                            WDObjectAxisEditor::Object::Move(*_pObject, relativeMOffset.xyz() * relativeMOffset.w, false);
                        }
                        break;
                    case WD::WDEditAxisMoveRotate::HT_Rotate:
                        {
                            if (Abs(relativeROffset.w) <= NumLimits<float>::Epsilon)
                                return;
                            WDObjectAxisEditor::Object::Rotate(*_pObject
                                , relativeROffset.xyz()
                                , relativeROffset.w
                                , sender.position()
                                , false);
                        }
                        break;
                    default:
                        break;
                    }
                }
            }
            break;
        case WDEditAxis::ES_End:
            {
                switch (hType)
                {
                case WD::WDEditAxisMoveRotate::HT_Move:
                    {
                        moveRatateEnd(absoluteMOffset.xyz() * absoluteMOffset.w, absoluteROffset.xyz(), absoluteROffset.w, center, true);
                    }
                    break;
                case WD::WDEditAxisMoveRotate::HT_Rotate:
                    {
                        rotateEnd(absoluteROffset.xyz(), absoluteROffset.w, sender.position(), true);
                    }
                    break;
                default:
                    break;
                }

            }
            break;
        case WDEditAxis::ES_Command:
            {
                switch (hType)
                {
                case WD::WDEditAxisMoveRotate::HT_Move:
                    {
                        //校验此次的移动累计偏移量是否接近于0，如果接近于0，则只做通知
                        if (Abs(absoluteMOffset.w) <= NumLimits<float>::Epsilon)
                            return;
                        moveEnd(absoluteMOffset.xyz() * absoluteMOffset.w, false);
                    }
                    break;
                case WD::WDEditAxisMoveRotate::HT_Rotate:
                    {
                        //校验此次的移动累计偏移量是否接近于0，如果接近于0，则只做通知
                        if (Abs(absoluteROffset.w) <= NumLimits<float>::Epsilon)
                            return;
                        rotateEnd(absoluteROffset.xyz(), absoluteROffset.w, sender.position(), false);
                    }
                    break;
                default:
                    break;
                }
            }
            break;
        default:
            break;
        }
    }

    void moveEnd(const DVec3& offset, bool bIgnoreFirstRedo)
    {
        // 通知编辑结束, 并检查是否应用此次编辑结果
        auto fInvoke = finishInvokeDelegate();
        if (!_objectEditFinishedDelegate(fInvoke, _pObject, _d))
        {
            // 不应用，回退此次编辑
            WDObjectAxisEditor::Object::Move(*_pObject, -offset);
            return;
        }

        // 校验此次的移动累计偏移量是否接近于0
        if (offset.lengthSq() <= NumLimits<float>::Epsilon)
            return;

        // 不生成undo command
        if (!_pObject->autoUndoCommand()) 
            return;

        // 构建移动命令
        auto pRCmd = WDObjectAxisEditor::Object::MakeMoveCommand(_d._viewer.app()
            , _pObject, offset
            , true);
        if (pRCmd == nullptr)
            return;

        // 特殊处理，因为向栈中push时，默认会做一次redo，但是此刻对象的变换实际已经是编辑后的,因此把第一次redo屏蔽掉
        ObjectEditCommand* pTCmd = dynamic_cast<ObjectEditCommand*>(pRCmd);
        assert(pTCmd != nullptr);
        if (pTCmd != nullptr)
            pTCmd->bIgnoreFirstRedo = bIgnoreFirstRedo;

        _d._viewer.app().undoStack().push(pRCmd);
    }
    void rotateEnd(const DVec3& axis, double angle, const DVec3& center, bool bIgnoreFirstRedo)
    {
        // 通知编辑结束, 并检查是否应用此次编辑结果
        auto fInvoke = finishInvokeDelegate();
        if (!_objectEditFinishedDelegate(fInvoke, _pObject, _d))
        {
            // 不应用，回退此次编辑
            WDObjectAxisEditor::Object::Rotate(*_pObject, axis, -angle, center);
            return;
        }

        // 校验轴有效
        if (axis.lengthSq() <= NumLimits<float>::Epsilon)
            return;
        // 校验此次的移动累计偏移量是否接近于0
        if (Abs(angle) <= NumLimits<float>::Epsilon)
            return;

        // 不生成undo command
        if (!_pObject->autoUndoCommand())
            return;

        // 构建旋转命令
        auto pRCmd = WDObjectAxisEditor::Object::MakeRotateCommand(_d._viewer.app()
            , _pObject, axis, angle, center
            , true);
        if (pRCmd == nullptr)
            return;

        // 特殊处理，因为向栈中push时，默认会做一次redo，但是此刻对象的变换实际已经是编辑后的,因此把第一次redo屏蔽掉
        ObjectEditCommand* pTCmd = dynamic_cast<ObjectEditCommand*>(pRCmd);
        assert(pTCmd != nullptr);
        if (pTCmd != nullptr)
            pTCmd->bIgnoreFirstRedo = bIgnoreFirstRedo;

        _d._viewer.app().undoStack().push(pRCmd);
    }
    void moveRatateEnd(const DVec3& offset, const DVec3& axis, double angle, const DVec3& center, bool bIgnoreFirstRedo)
    {
        // 通知编辑结束, 并检查是否应用此次编辑结果
        auto fInvoke = finishInvokeDelegate();
        if (!_objectEditFinishedDelegate(fInvoke, _pObject, _d))
        {
            // 不应用，回退此次编辑
            WDObjectAxisEditor::Object::Move(*_pObject, -offset);
            WDObjectAxisEditor::Object::Rotate(*_pObject, axis, -angle, center);
            return;
        }
        // 不生成undo command
        if (!_pObject->autoUndoCommand())
            return ;
        // 校验移动数据判断是否需要生成移动命令
        bool bGenMCMd = offset.lengthSq() > NumLimits<float>::Epsilon;
        // 校验旋转数据判断是否需要生成旋转命令
        bool bGenRCMd = axis.lengthSq() > NumLimits<float>::Epsilon && Abs(angle) > NumLimits<float>::Epsilon;
        // 如果既不生产移动命令也不生成旋转命令,则返回
        if (!bGenMCMd && !bGenRCMd)
            return;

        WD::WDNode::WeakPtr pPrevNode;
        auto constraint = _singleMRAxis.currentConstraintLine();
        if (constraint)
            pPrevNode = constraint.value().prevNode;
        // 这里用一个 父命令来包含移动和旋转两个操作
        auto pCommand = new ObjectEditMRCommand(_d._viewer.app(), _pObject, pPrevNode);
        // 构建旋转命令
        if (bGenRCMd)
            pCommand->pRCommand = WDObjectAxisEditor::Object::MakeRotateCommand(_d._viewer.app(), _pObject, axis, angle, center, false);
        // 构建移动命令
        if (bGenMCMd)
            pCommand->pMCommand = WDObjectAxisEditor::Object::MakeMoveCommand(_d._viewer.app(), _pObject, offset, false);

        pCommand->setNoticeAfterRedo([this](const WDUndoCommand&)
        {
            this->_d._viewer.app().needRepaint();
        });
        pCommand->setNoticeAfterUndo([this](const WDUndoCommand&)
        {
            this->_d._viewer.app().needRepaint();
        });


        pCommand->bIgnoreFirstRedo = bIgnoreFirstRedo;
        _d._viewer.app().undoStack().push(pCommand);

    }
    void scaleEnd(const DVec3& scaling, bool bIgnoreFirstRedo)
    {
        // 通知编辑结束, 并检查是否应用此次编辑结果
        auto fInvoke = finishInvokeDelegate();
        if (!_objectEditFinishedDelegate(fInvoke, _pObject, _d))
        {
            // 不应用，回退此次编辑
            WDObjectAxisEditor::Object::Scale(*_pObject, DVec3(1.0) / scaling);
            return;
        }

        // 校验此次的移动累计偏移量是否接近于0
        if (Abs(1.0 - scaling.x) <= NumLimits<float>::Epsilon
            && Abs(1.0 - scaling.y) <= NumLimits<float>::Epsilon
            && Abs(1.0 - scaling.z) <= NumLimits<float>::Epsilon)
            return;

        // 不生成undo command
        if (!_pObject->autoUndoCommand())
            return;

        // 构建缩放命令
        auto pRCmd = WDObjectAxisEditor::Object::MakeScaleCommand(_d._viewer.app()
            , _pObject, scaling
            , true);
        if (pRCmd == nullptr)
            return;

        // 特殊处理，因为向栈中push时，默认会做一次redo，但是此刻对象的变换实际已经是编辑后的,因此把第一次redo屏蔽掉
        ObjectEditCommand* pTCmd = dynamic_cast<ObjectEditCommand*>(pRCmd);
        assert(pTCmd != nullptr);
        if (pTCmd != nullptr)
            pTCmd->bIgnoreFirstRedo = bIgnoreFirstRedo;

        _d._viewer.app().undoStack().push(pRCmd);
    }
};

WDObjectAxisEditor::WDObjectAxisEditor(WDViewer& viewer)
    : _viewer(viewer)
{
    _p = new WDObjectAxisEditorPrivate(*this);
    // 默认开始是成功的
    _startSuccess = true;
}
WDObjectAxisEditor::~WDObjectAxisEditor()
{
    delete _p;
    _p = nullptr;
}

WDEditAxisMove& WDObjectAxisEditor::getAxisM()
{
    return  _p->_mAxis;
}
WDEditAxisRotate& WDObjectAxisEditor::getAxisR()
{
    return  _p->_rAxis;
}
WDEditAxisScale& WDObjectAxisEditor::getAxisS()
{
    return  _p->_sAxis;
}
WDEditAxisMoveRotate& WDObjectAxisEditor::getAxisMR()
{
    return  _p->_mrAxis;
}
WDEditSingleAxisMoveRotate& WDObjectAxisEditor::getAxisSingleMR()
{
    return _p->_singleMRAxis;
}

void WDObjectAxisEditor::setObject(Object::SharedPtr pObject)
{
    auto pOldObject = _p->_pObject;

    if (pOldObject == pObject)
        return;

    if (pOldObject != nullptr 
        && pObject != nullptr 
        && pOldObject->equals(*pObject))
        return;

    _p->_pObject = pObject;

    auto pAxis = _p->getCurrentTypeAxis();
    if (pAxis != nullptr)
        pAxis->setOffset(DVec3::Zero());
    // 通知当前节点改变
    _p->_objectChangedDelegate(pOldObject, pObject, *this);
}
WDObjectAxisEditor::Object::SharedPtr WDObjectAxisEditor::object() const
{
    return _p->_pObject;
}
void WDObjectAxisEditor::setEditType(EditType type)
{
    if (_p->_eType == type)
        return;

    EditType oldType = _p->_eType;
    _p->_eType = type;

    _p->_editTypeChangedDelegate(_p->_eType, oldType, *this);
}
WDObjectAxisEditor::EditType WDObjectAxisEditor::editType() const
{
    return _p->_eType;
}
void WDObjectAxisEditor::setEditCoordinateSysType(EditCoordinateSysType type)
{
    _p->_ecsType = type;
}
WDObjectAxisEditor::EditCoordinateSysType WDObjectAxisEditor::editCoordinateSysType() const
{
    return _p->_ecsType;
}

WDObjectAxisEditor::ObjectChangedDelegate& WDObjectAxisEditor::objectChangedDelegate()
{
    return _p->_objectChangedDelegate;
}
WDObjectAxisEditor::EditTypeChangedDelegate& WDObjectAxisEditor::editTypeChangedDelegate()
{
    return _p->_editTypeChangedDelegate;
}
WDObjectAxisEditor::ObjectEditStartDelegate& WDObjectAxisEditor::objectEditStartDelegate()
{
    return _p->_objectEditStartDelegate;
}
WDObjectAxisEditor::ObjectEditFinishedDelegate& WDObjectAxisEditor::objectEditFinishedDelegate()
{
    return _p->_objectEditFinishedDelegate;
}

void WDObjectAxisEditor::forceUpdate()
{
    update(_viewer.context());
}

bool WDObjectAxisEditor::mousePress(const IVec2& mousePos, WDEditAxis::MouseButton btn)
{
    if (_p->_pObject == nullptr)
        return false;
    if (!_p->_pObject->valid())
        return false;

    WDContext& context = _viewer.context();
    // 如果轴编辑前校验为空,则默认有编辑权限,否则默认没有编辑权限
    if (_p->_objectEditStartDelegate.empty())
        _startSuccess = true;
    else
        _startSuccess = false;

    auto pAxis = _p->getCurrentTypeAxis();
    if (pAxis == nullptr)
        return false;

    return pAxis->mouseButtonPress(context, mousePos, btn);
}
bool WDObjectAxisEditor::mouseRelease(const IVec2& mousePos, WDEditAxis::MouseButton btn)
{
    if (_p->_pObject == nullptr)
        return false;
    if (!_p->_pObject->valid())
        return false;

    WDContext& context = _viewer.context();
    // 如果轴编辑前校验为空,则默认有编辑权限,否则默认没有编辑权限
    if (_p->_objectEditStartDelegate.empty())
        _startSuccess = true;
    else
        _startSuccess = false;
    
    auto pAxis = _p->getCurrentTypeAxis();
    if (pAxis == nullptr)
        return false;

    return pAxis->mouseButtonRelease(context, mousePos, btn);
}
bool WDObjectAxisEditor::mouseMove(const IVec2& mousePos)
{
    if (_p->_pObject == nullptr)
        return false;
    if (!_p->_pObject->valid())
        return false;
    auto pAxis = _p->getCurrentTypeAxis();
    if (pAxis == nullptr)
        return false;
    // 如果当前已经选中了轴但是没有权限操作
    if (!_startSuccess && pAxis->isAxisSelected())
        return false;

    WDContext& context = _viewer.context();

    return pAxis->mouseMove(context, mousePos);
}
bool WDObjectAxisEditor::mouseDoubleClicked(const IVec2& mousePos, WDEditAxis::MouseButton btn)
{
    if (_p->_pObject == nullptr)
        return false;
    if (!_p->_pObject->valid())
        return false;

    WDContext& context = _viewer.context();

    auto pAxis = _p->getCurrentTypeAxis();
    if (pAxis == nullptr)
        return false;

    return pAxis->mouseDoubleClicked(context, mousePos, btn);
}

void WDObjectAxisEditor::update(WDContext& context)
{
    if (_p->_pObject == nullptr)
        return ;
    if (!_p->_pObject->valid())
        return ;

    auto pAxis = _p->getCurrentTypeAxis();
    if (pAxis == nullptr)
        return;

    //更新transform矩阵
    DMat4 transform = DMat4::Identity();
    switch (_p->_ecsType)
    {
    case ECST_Global:
        {
            auto gTsfm      = _p->_pObject->globalTransform();
            DVec3 gPos      = gTsfm[3].xyz();
            transform.identity();
            transform[3]    = DVec4(gPos, 1.0);
        }
        break;
    case ECST_Parent:
        {
            auto gTsfm      = _p->_pObject->globalTransform();
            DVec3 gPos      = gTsfm[3].xyz();
            transform       = _p->_pObject->parentGlobalTransform();
            transform[3]    = DVec4(gPos, 1.0);
        }
        break;
    case ECST_Local:
        {
            transform       = _p->_pObject->globalTransform();
        }
        break;
    case ECST_Screen:
        {
            auto gTsfm          = _p->_pObject->globalTransform();
            DVec3 gPos          = gTsfm[3].xyz();

            WDCamera& camera    = _viewer.context().camera();
            DVec3 axisX         = camera.rightDir();
            DVec3 axisY         = camera.upDir();
            DVec3 axisZ         = -camera.frontDir();

            transform[0]        = DVec4(axisX, 0.0);
            transform[1]        = DVec4(axisY, 0.0);
            transform[2]        = DVec4(axisZ, 0.0);
            transform[3]        = DVec4(gPos, 1.0);
        }
        break;
    default:
        break;
    }

    // 更新编辑轴
    pAxis->setTransform(transform);
    pAxis->update(context);
}
void WDObjectAxisEditor::render(WDContext& context)
{
    if (_p->_pObject == nullptr)
        return;
    if (!_p->_pObject->valid())
        return;

    auto pAxis = _p->getCurrentTypeAxis();
    if (pAxis == nullptr)
        return;

    // 绘制编辑轴
    pAxis->render(context);
}

WD_NAMESPACE_END