#pragma once

#include "../../../common/WDSharedObjectPool.h"
#include "../../../node/WDNode.h"
#include "../../../geometry/WDGeometry.h"
#include "../../typeMgr/WDBMAttrValue.h"
#include "../../../math/geometric/standardPrimitives/WDLoftSPline.h"
#include <any>

WD_NAMESPACE_BEGIN

class WDCore;
class WDBMCModelBuilderP;
class CAttributeGetP;
class WDBMCModelBuilder;

/**
 * @brief 元件相关节点的属性获取对象
 */
class WD_API CAttributeGet
{
public:
    CAttributeGet();
    ~CAttributeGet();
public:
    /**
     * @brief 指定元件节点，获取其对应名称的属性值(非数组类型)
     * @param node 需要获取属性的节点
     * @param attrName 属性名称
     * @param bOk 是否获取成功
     * @return 属性值,如果属性值是表达式，这里会对表达式进行求值，再将求值的结果返回
     */
    WDBMAttrValue getAttribute(const WDNode& node, const std::string& attrName, bool* bOk = nullptr);
    /**
     * @brief 指定元件节点，获取其对应名称的属性值(数组类型)
     * @param node 需要获取属性的节点
     * @param attrName 属性名称
     * @param index 数组下标
     *  !注意: 这里下标从1开始
     * @param bOk 是否获取成功
     * @return 属性值,如果属性值是表达式，这里会对表达式进行求值，再将求值的结果返回
     */
    WDBMAttrValue getAttribute(const WDNode& node, const std::string& attrName, int index, bool* bOk = nullptr);
    /**
     * @brief 直接对一个表达式求值
     * @param expr 表达式
     * @param bOk 是否求值成功
     * @param pOutError 输出的错误信息
     * @return 结果
     */
    WDBMAttrValue execExpression(const std::string& expr, bool* bOk = nullptr, std::string* pOutError = nullptr);
private:
    std::shared_ptr<CAttributeGetP> _p = nullptr;
    friend class WDBMCModelBuilder;
};

/**
 * @brief 元件模型构建器
*/
class WD_API WDBMCModelBuilder
{
public:
    /**
     * @brief 一个元件生成的模型数据(带有对应的节点)
    */
    struct WD_API CMNData
    {
    public:
        // 点节点以及其生成的关键点数据，一个点节点仅生成一个点数据
        struct CMKeyPoint
        {
            // 点节点
            WDNode::WeakPtr     node;
            // 关键点数据
            WDKeyPoint          keyPoint;
        };
        // 点数据列表
        using CMKeyPoints = std::vector<CMKeyPoint>;
        // PLine节点 以及 其生成的PLine数据，一个PLine节点仅生成一个PLine数据
        struct CMPLine
        {
            // PLine节点
            WDNode::WeakPtr     node;
            // PLine数据
            WDPLine             pLine;
        };
        // PLine数据列表
        using CMPLines = std::vector<CMPLine>;
        /**
         * @brief 负型数据, 负型节点以及其生成的几何体(负几何体)
         */
        struct CMNGeom
        {
            // 负型节点
            WDNode::WeakPtr             node;
            // 几何体数据(负几何体，应该带有GF_Negative标志)
            WDGeometry::SharedPtr       pGeom = nullptr;
        };
        // 负型数据列表
        using CMNGeoms = std::vector<CMNGeom>;
        // 正型数据，正型节点以及其生成的几何体(正几何体)
        struct CMGeom
        {
            // 型节点
            WDNode::WeakPtr             node;
            // 正几何体
            WDGeometry::SharedPtr       pGeom = nullptr;
            // 正几何体下挂载的负几何体列表, 用于给正几何体开孔
            CMNGeoms                    nGeoms;
        };
        // 型数据列表
        using CMGeoms = std::vector<CMGeom>;
    public:
        // 元件节点
        WDNode::WeakPtr node;
        // 点数据列表
        CMKeyPoints     keyPoints;
        // PLine数据列表
        CMPLines        pLines;
        // 型数据列表
        CMGeoms         geoms;
        // 负型实体数据列表
        CMNGeoms        nGeoms;
    };
    /**
     * @brief 一个元件生成的模型数据(不带节点)
    */
    struct WD_API CMData
    {
    public:
        struct GData 
        {
            // 正几何体
            WDGeometry::SharedPtr pGeom = nullptr;
            // 正几何体下挂载的负几何体列表, 用于给正几何体开孔
            WDGeometries nGeoms;
        };
        using GDatas = std::vector<GData>;
    public:
        // 关键点列表
        WDKeyPoints     keyPoints;
        // PLine线数据列表
        WDPLines        pLines;
        // 正实体数据列表
        GDatas          geoms;
        // 负实体数据列表
        WDGeometries    nGeoms;
    public:
        inline bool empty() const
        {
            return keyPoints.empty() && pLines.empty() && geoms.empty() && nGeoms.empty();
        }
    public:
        static CMData FromCMNData(const CMNData& data);
    };
public:
    // 全局变量值类型: DVec3
    static constexpr const char* GVarDrnS = "DrnS";
    // 全局变量值类型: DVec3
    static constexpr const char* GVarDrnE = "DrnE";
    // 全局变量值类型: std::string
    static constexpr const char* GVarJustLine = "JustLine";
    // 全局变量值类型: double
    static constexpr const char* GVarBAngle = "BAngle";
    // 全局变量值类型: WDLoftSpline
    static constexpr const char* GVarLoftSpline = "LoftSpline";
private:
    /**
     * @brief 用来标识元件模型的唯一性
    */
    using Key = std::pair<std::vector<WDBMAttrValue>, std::optional<WDLoftSPline> >;
    /**
     * @brief 共享模型对象
    */
    using SharedObject =   WDSharedObjectT<CMData>;
    /**
     * @brief 共享模型对象池，使用 Key来标识其唯一性
    */
    using Pool =   WDSharedObjectPoolT<Key, SharedObject>;

    /**
     * @brief 共享保温模型对象
     */
    using SharedObjectI = WDSharedObjectT<CMData::GDatas
    >;
    /**
     * @brief 共享保温模型对象池，使用 IKey来标识其唯一性
    */
    using PoolI = WDSharedObjectPoolT<Key, SharedObjectI>;
public:
    /**
    * @brief 共享的模型数据
    *   通过SharedModel::data() 获取模型数据
    */
    using SharedModel = Pool::Object;
    /**
    * @brief 共享的保温模型数据
    *   通过SharedModel::data() 获取模型数据
    */
    using SharedModelI = PoolI::Object;
    /**
     * @brief 变量值获取回调
     * @param name 变量名称
     * @param index 数组变量索引, 如果不是数组变量，index值为std::nullopt
     * @param outValue 输出结果，当结果值不存在时，需要赋值为 std::any()
	 *	目前any支持三种数据类型: int, float, std::string
     * @return 是否支持当前变量的获取
     *  !注意: 这里的返回值代表的是当前名称的变量是否支持获取，而非结果是否有效
     */
    using ValueGetFunction = std::function<bool(const std::string& name, std::any& outValue, const std::optional<int>& index)>;
public:
    WDBMCModelBuilder(WDCore& core);
    WDBMCModelBuilder(const WDBMCModelBuilder& right) = delete;
    WDBMCModelBuilder(WDBMCModelBuilder&& right) = delete;
    WDBMCModelBuilder& operator=(const WDBMCModelBuilder& right) = delete;
    WDBMCModelBuilder& operator=(WDBMCModelBuilder&& right) = delete;
    virtual ~WDBMCModelBuilder();
public:
    /**
     * @brief 生成元件模型
     * @param pScomNode 元件节点
     * @param vGet 变量值获取回调
     * @return 元件模型数据 @see ref CMNData
    */
    CMNData build(WDNode::SharedPtr pScomNode
        , const ValueGetFunction& vGet);
    /**
     * @brief 从缓存中获取元件模型
     *  如果缓存中存在，则直接返回缓存中的元件模型, 否则将返回新建的元件模型，并将新建的元件模型放到缓存中
     * @param node 当前(设计)节点
     * @param pOldModel 之前生成的元件模型
     *  如果值不为nullptr, 将根据pOldModel的缓存key和将要新生成的元件模型的缓存Key对比是否有必要生成新的模型
     * @return 共享的模型数据对象
    */
    SharedModel get(const WDNode& node
        , SharedModel pOldModel = nullptr);
    /**
     * @brief 获取元件保温模型
     * @param node 当前(设计)节点
     * @param pOldModel 之前生成的元件保温模型
     * @return 共享的保温模型数据对象
     */
    SharedModelI getInsu(const WDNode& node
        , SharedModelI pOldModel = nullptr);
    /**
     * @brief 获取元件相关节点属性获取对象
     * @param node 设计模块节点(引用元件的设计节点)
     * @return 属性获取对象，部分元件节点的属性为表达式，该对象获取其属性时会解析该表达式
     */
    CAttributeGet cAttributeGet(const WDNode& desiNode);
    /**
     * @brief 获取元件相关节点属性获取对象
     * @param pDesiNode 设计节点，可以不指定，
     * @param pCataSPNode 等级节点，可以不指定
     *  如果指定了，解析中使用到等级相关的变量则使用当前指定的等级节点
     *  如果未指定但是指定了设计节点，则默认获取设计节点的等级节点
     * @param pCataScomNode 元件节点
     *  如果指定了，使用指定的元件节点
     *  如果未指定，先尝试从指定的等级节点获取元件，如果指定的等级节点未获取到元件，再尝试从设计节点获取元件节点
     * @return 属性获取对象，部分元件节点的属性为表达式，该对象获取其属性时会解析该表达式
     */
    CAttributeGet cAttributeGet(WDNode::SharedPtr pDesiNode
        , WDNode::SharedPtr pCataSPNode = nullptr
        , WDNode::SharedPtr pCataScomNode = nullptr);
    /**
     * @brief 获取元件相关节点属性获取对象
     * @param function 回调函数，用于自定义指定表达式中使用的变量的值
     * @return 属性获取对象，部分元件节点的属性为表达式，该对象获取其属性时会解析该表达式
     */
    CAttributeGet cAttributeGet(const ValueGetFunction& function = ValueGetFunction{});
private:
    WDBMCModelBuilderP* _p;
    friend class WDBMCModelBuilderP;
};

WD_NAMESPACE_END