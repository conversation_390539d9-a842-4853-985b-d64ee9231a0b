#pragma once
#include "../../../../node/WDNode.h"
#include "../../../../log/WDLoggerPort.h"
#include "../../../../math/DirectionParser.h"
#include "../../../../math/PositionParser.h"
#include "../WDBMCModelBuilder.h"
#include "WIZDesignerDSL.h"
WD_NAMESPACE_BEGIN

class Context;

#ifdef PARSE_ASSERT_ENABLED
#define PARSE_ASSERT(expression) assert(expression)
#else
#define PARSE_ASSERT(expression) ((void)0)
#endif

//#define PARSE_ASSERT_ENABLED

/**
* @brief 校验某个字符串表示的值是否是 "unset"(不区分大小写)
* @param str 可能出现是 "unset"(不区分大小写) 值的字符串
* @return str是否是 "unset"(不区分大小写)
*/
static inline bool IsUnsetValue(const std::string& str)
{
    return _stricmp(str.c_str(), "unset") == 0;
}

/**
* @brief 输出参数解析错误信息
* @param printLogs 已经输出过的日志,如果已经输出过相同的日志,则跳过当前日志,否则会把当前日志也添加列表中
*/
void ParseAttriFaildLogPrint(Context& cxt, const WDNode& node
    , const std::string& name
    , const std::string& value
    , const std::string& errLog);
/**
* @brief 输出参数解析错误信息
*/
void ParseNodeFaildLogPrint(Context& cxt, const WDNode& node, const char* text);
/**
* @brief 输出参数解析错误信息
*/
void ParseFaildLogPrint(Context& cxt, const std::string& text);

// 朝向字符串解析对象
using DirParser = FDirectionParser;
using PosParser = FPositionParser;

using DSLContext        = WIZDesignerDSL::DSLContext;
using DSLString         = WIZDesignerDSL::String;
using DSLRule           = WIZDesignerDSL::DSLRule;
using DSLArray          = WIZDesignerDSL::Array;
using DSLNumber         = WIZDesignerDSL::Number;
using DSLFloat          = WIZDesignerDSL::Float;
using DSLBool           = WIZDesignerDSL::Bool;
using DSLCode           = WIZDesignerDSL::DSLCode;
using DSLStringLogger   = WIZDesignerDSL::DSLStringLogger;

struct DSLKeyPointResult
{
    FVec3 position = FVec3::Zero();
    FVec3 direction = FVec3::AxisZ();
};

// 保存所以别名和原本名称的对应关系
static std::map<std::string, std::string> attrAliasMap=
{
    {"MTXR",    "Matxt"},
    {"Mtxref",  "Matxt"},
};

void DSLInit(DSLContext& ctx);
bool DSLExec(DSLContext& dsl, const std::string& expr, std::any& outValue, std::string& outErr, bool bCheckExpression = false);
bool DSLExec(DSLContext& dsl, const std::string& expr, float& outValue, std::string& outErr, bool bCheckExpression = false);
bool DSLExec(DSLContext& dsl, const std::string& expr, std::string& outValue, std::string& outErr, bool bCheckExpression = false);
bool DSLExecDirection(DSLContext& dsl, const std::string& expr, DSLKeyPointResult& outValue, std::string& outErr);
bool DSLExecPosition(DSLContext& dsl, const std::string& expr, FVec3& outPosition, std::string& outErr);

WD_NAMESPACE_END