#include "DSLCommon.h"
#include "Context.h"
WD_NAMESPACE_BEGIN

void ParseAttriFaildLogPrint(Context& cxt, const WD::WDNode& node, const std::string& name, const std::string& value, const std::string& errLog)
{
    if (cxt.bSilence)
        return;
    std::string outStr;
    outStr.reserve(1024);

    char buf[1024] = {0};

    sprintf_s(buf, sizeof(buf), u8"注意:节点[%s]类型[%s]属性[%s]解析失败,属性值:%s"
        , node.name().c_str()
        , node.type().data()
        , name.c_str()
        , value.c_str());
    outStr.append(buf);

    if (!errLog.empty()) 
    {
        sprintf_s(buf, sizeof(buf), u8"错误信息:%s", errLog.c_str());
        outStr.append(buf);
    }
    if (cxt.node() != nullptr)
    {
        sprintf_s(buf, sizeof(buf), u8"设计节点:[%s]类型:[%s]"
            , cxt.node()->name().c_str()
            , cxt.node()->type().data());

        outStr.append("-");
        outStr.append(buf);
    }
    if (cxt.scomNode() != nullptr)
    {
        sprintf_s(buf, sizeof(buf), u8"元件节点:[%s]类型:[%s]"
            , cxt.scomNode()->name().c_str()
            , cxt.scomNode()->type().data());
        outStr.append("-");
        outStr.append(buf);
    }

    LOG_WARN << outStr;
}
void ParseNodeFaildLogPrint(Context& cxt, const WDNode& node, const char* text)
{
    if (cxt.bSilence)
        return;
    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), u8"注意: 节点[%s]类型[%s], %s!"
        , node.name().c_str()
        , node.type().data()
        , text);
    LOG_WARN << buf;
}
void ParseFaildLogPrint(Context& cxt, const std::string& text)
{
    if (text.empty())
        return;
    if (cxt.bSilence)
        return;
    LOG_WARN << text;
}

/**
 * @brief 字符串转浮点数
 * @param str 
 * @param outValue 
 * @return 
*/
static bool StringToFloat(const std::string& str, float& outValue)
{
    // 先判断字符串是否是数值类型 如果有多个小数点或者字符串以小数点开头或结尾,暂时认为不是数值
    if (std::count(str.begin(), str.end(), '.') > 1
        || str.front() == '.'
        || str.back() == '.')
        return false;
    int idx = 0;
    if (str.front() == '-')
        idx = 1;
    for (; idx < str.size(); ++idx)
    {
        if (str[idx] != '.' && str[idx] < '0' && str[idx] > '9')
            return false;
    }

    float tmpFloat = 0.0f;
    if (sscanf(str.c_str(), "%f", &tmpFloat) <= 0)
        return false;

    if (size_t i = str.find('.'); i != std::string::npos)
    {
        int decimalPlaces = static_cast<int>(str.size() - i - 1);
        float num = static_cast<float>(std::pow(10, decimalPlaces));
        outValue = std::round(tmpFloat * num) / num;
    }
    else
    {
        outValue = tmpFloat;
    }
    return true;
};
static std::string FlostToString(const float& val, int decimalPlaces = 5)
{
    if (decimalPlaces == 0)
    {
        return std::to_string(static_cast<int>(val));
    }
    char format[64] = { 0 };
    std::sprintf(format, "%s%dlf", "%.", static_cast<int>(decimalPlaces));
    char text[1024] = { 0 };
    std::sprintf(text, format, val);
    std::string outStr = std::string(text);
    if (outStr.find('.') != std::string::npos)
    {
        while (!outStr.empty())
        {
            if (outStr.back() == '0')
            {
                outStr.pop_back();
                continue;
            }
            if (outStr.back() == '.')
                outStr.pop_back();
            break;
        }
    }
    return outStr;
}

void DSLInit(DSLContext& ctx)
{
    // -
    ctx.registerFunction(DSLRule::SymbolNameOp("-", typeid(DSLKeyPointResult))
        , [](DSLContext&, const DSLArray& args) -> std::any
    { 
        auto r = std::any_cast<DSLKeyPointResult>(args[0]);
    r.direction = -r.direction;
    return r;
    });

    /************* abs *************/
    ctx.registerFunction({ "ABS", "abs" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        return (DSLNumber)Abs((double)std::any_cast<DSLNumber>(x));
    }
    else if (type == DSLRule::FloatType)
    {
        return (DSLFloat)Abs((double)std::any_cast<DSLFloat>(x));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        if (StringToFloat(v, f))
            return (DSLFloat)Abs((double)std::any_cast<DSLFloat>(f));
        return std::any();
    }
    else
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    });
    /************* sqrt *************/
    ctx.registerFunction({ "SQRT", "sqrt" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        return (DSLNumber)Sqrt((double)std::any_cast<DSLNumber>(x));
    }
    else if (type == DSLRule::FloatType)
    {
        return (DSLFloat)Sqrt((double)std::any_cast<DSLFloat>(x));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        if (StringToFloat(v, f))
            return (DSLFloat)Sqrt((double)std::any_cast<DSLFloat>(f));
        return std::any();
    }
    else
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    });
    /************* tan *************/
    ctx.registerFunction({ "TAN", "tan" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        auto v = std::any_cast<DSLNumber>(x);
        return (DSLFloat)Tan((double)DegToRad<double>(static_cast<double>(v)));
    }
    else if (type == DSLRule::FloatType)
    {
        auto v = std::any_cast<DSLFloat>(x);
        return (DSLFloat)Tan((double)DegToRad<double>(static_cast<double>(v)));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        if (StringToFloat(v, f))
            return (DSLFloat)Tan((double)DegToRad<double>(static_cast<double>(f)));
        return std::any();
    }
    else
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    });
    /************* atan *************/
    ctx.registerFunction({ "ATAN", "atan" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        auto v = std::any_cast<DSLNumber>(x);
        // Atan的返回值是弧度,这里转为角度
        return (DSLFloat)RadToDeg<double>(ATan(static_cast<double>(v)));
    }
    else if (type == DSLRule::FloatType)
    {
        auto v = std::any_cast<DSLFloat>(x);
        // Atan的返回值是弧度,这里转为角度
        return (DSLFloat)RadToDeg<double>(ATan(static_cast<double>(v)));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        // Atan的返回值是弧度,这里转为角度
        if (StringToFloat(v, f))
            return (DSLFloat)RadToDeg<double>(ATan(static_cast<double>(f)));
        return std::any();
    }
    else
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    });
    /************* cos *************/
    ctx.registerFunction({ "COS", "cos" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        auto v = std::any_cast<DSLNumber>(x);
        return (DSLFloat)Cos((double)DegToRad<double>(static_cast<double>(v)));
    }
    else if (type == DSLRule::FloatType)
    {
        auto v = std::any_cast<DSLFloat>(x);
        return (DSLFloat)Cos((double)DegToRad<double>(static_cast<double>(v)));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        if (StringToFloat(v, f))
            return (DSLFloat)Cos((double)DegToRad<double>(static_cast<double>(f)));
        return std::any();
    }
    else
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    });
    /************* acos *************/
    ctx.registerFunction({ "ACOS", "acos" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        auto v = std::any_cast<DSLNumber>(x);
        // Acos的返回值是弧度,这里转为角度
        return (DSLFloat)RadToDeg<double>(ACos(static_cast<double>(v)));
    }
    else if (type == DSLRule::FloatType)
    {
        auto v = std::any_cast<DSLFloat>(x);
        // Acos的返回值是弧度,这里转为角度
        return (DSLFloat)RadToDeg<double>(ACos(static_cast<double>(v)));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        // Acos的返回值是弧度,这里转为角度
        if (StringToFloat(v, f))
            return (DSLFloat)RadToDeg<double>(ACos(static_cast<double>(f)));
        return std::any();
    }
    else
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    });
    /************* sin *************/
    ctx.registerFunction({ "SIN", "sin" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        auto v = std::any_cast<DSLNumber>(x);
        return (DSLFloat)Sin((double)DegToRad<double>(static_cast<double>(v)));
    }
    else if (type == DSLRule::FloatType)
    {
        auto v = std::any_cast<DSLFloat>(x);
        return (DSLFloat)Sin((double)DegToRad<double>(static_cast<double>(v)));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        if (StringToFloat(v, f))
            return (DSLFloat)Sin((double)DegToRad<double>(static_cast<double>(f)));
        return std::any();
    }
    else
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    });
    /************* asin *************/
    ctx.registerFunction({ "ASIN", "asin" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        auto v = std::any_cast<DSLNumber>(x);
        // Asin的返回值是弧度,这里转为角度
        return (DSLFloat)RadToDeg<double>(ASin(static_cast<double>(v)));
    }
    else if (type == DSLRule::FloatType)
    {
        auto v = std::any_cast<DSLFloat>(x);
        // Asin的返回值是弧度,这里转为角度
        return (DSLFloat)RadToDeg<double>(ASin(static_cast<double>(v)));
    }
    else if (type == DSLRule::StringType)
    {
        auto v = std::any_cast<DSLString>(x);
        float f = 0.0;
        // Asin的返回值是弧度,这里转为角度
        if (StringToFloat(v, f))
            return (DSLFloat)RadToDeg<double>(ASin(static_cast<double>(f)));
        return std::any();
    }
    else
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    });
    /************* atant *************/
    ctx.registerFunction({ "ATANT", "atant" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 2)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    auto convertToDoble = [] (const std::any& val) -> double
    {
        auto& type = val.type();
        if (type == DSLRule::NumberType)
        {
            return static_cast<double>(std::any_cast<DSLNumber>(val));
        }
        else if (type == DSLRule::FloatType)
        {
            return static_cast<double>(std::any_cast<DSLFloat>(val));
        }
        else if (type == DSLRule::StringType)
        {
            auto v = std::any_cast<DSLString>(val);
            float f = 0.0;
            if (StringToFloat(v, f))
                return static_cast<double>(f);
        }
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    };
    // 注意 atant 的参数为  (y, x);
    double yVal = convertToDoble(args[0]);
    double xVal = convertToDoble(args[1]);

    // Atan的返回值是弧度,这里转为角度
    // 当两个值均为0时不合法
    if (xVal == 0.0 && yVal == 0.0)
        throw std::invalid_argument("values illegal!");
    // 当xVal值为0时，即arctan(∞），数学上没有意义
    // 这里特殊处理，判断yVal
    // yVal大于零则返回90度
    // yVal小于零则返回-90度
    // （90度和-90度的正切值均为∞）
    if (xVal == 0.0)
    {
        if (yVal > 0)
            return DSLFloat(90.0);
        else
            return DSLFloat(-90.0);
    }
    // 0度和180度的正切值均为0
    // 这里根据xVal的符号来判断
    // 大于0则返回0度
    // 小于0则返回180度
    if (yVal == 0.0)
    {
        if (xVal > 0)
            return DSLFloat(0.0);
        else
            return DSLFloat(180.0);
    }
    double angle = RadToDeg<double>(ATan(fabs(yVal / xVal)));
    // 第一象限
    if (xVal >= 0.0 && yVal >= 0.0)
        return DSLFloat(angle);
    // 第二象限
    if (xVal <= 0.0 && yVal >= 0.0)
        return DSLFloat(180 - angle);
    // 第三象限
    if (xVal <= 0.0 && yVal <= 0.0)
        return DSLFloat(-180 + angle);
    // 第四象限
    if (xVal >= 0.0 && yVal <= 0.0)
        return DSLFloat(-angle);

    assert(false);
    return DSLFloat(angle);
    });
    /************* STR *************/
#pragma region STR
    ctx.registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", { DSLRule::BoolType }),
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        return (DSLString)(std::any_cast<DSLBool>(args[0]) ? "TRUE" : "FALSE");
    });
    ctx.registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", { DSLRule::NumberType }),
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        return (DSLString)std::to_string(std::any_cast<DSLNumber>(args[0]));
    });
    ctx.registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", { DSLRule::FloatType }),
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        return (DSLString)FlostToString(std::any_cast<DSLFloat>(args[0]));
    });
    ctx.registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", { DSLRule::StringType }),
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        return std::any_cast<DSLString>(args[0]);
    });
    ctx.registerFunction(DSLRule::SymbolNameFunctionWithArgs("STR", { DSLRule::ArrayType }),
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        DSLArray arr = std::any_cast<DSLArray>(args[0]);
    std::stringstream ss;
    ss << '[';
    for (size_t i = 0; i < arr.size(); i++)
    {
        if (i != 0)
            ss << ", ";
        DSLString sub = std::any_cast<DSLString>(ctx.call("STR", { arr[i] }));
        ss << sub;
    }
    ss << ']';
    return DSLString(ss.str());
    });
    ctx.registerFunction("STR",
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        WDUnused(ctx);
    if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));
    std::any v = args[0];
    if (!v.has_value())
        throw std::logic_error("STR函数不支持的参数类型!" );

    throw std::logic_error(std::string("STR函数不支持的参数类型!") + v.type().name());
    });
    ctx.registerFunction({ "SUBS", "subs", "Subs"},
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 2)
        return std::any();
    if (args[0].type() != DSLRule::StringType
        || args[1].type() != DSLRule::NumberType)
        return std::any();
    auto str = std::any_cast<DSLString>(args[0]);
    auto size = std::any_cast<DSLNumber>(args[1]);
    if (size <= 0 || str.size() < size)
        return std::any();
    str.erase(str.begin(), str.begin() + size);
    return DSLString(str);
    });
#pragma endregion
    /************* 后置单位 *************/
#pragma region BackUnit
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "MM"),
        DSLRule::SymbolNameOp(DSLRule::NumberType, "mm") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return std::any_cast<DSLNumber>(args[0]);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "MM"),
        DSLRule::SymbolNameOp(DSLRule::FloatType, "mm") },
        [](DSLContext& ctx, const DSLArray& args)->std::any
    {
        WDUnused(ctx);
    return (std::any_cast<DSLFloat>(args[0]));
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "CM"),
        DSLRule::SymbolNameOp(DSLRule::NumberType, "cm") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (std::any_cast<DSLNumber>(args[0]) * 10);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "CM"),
        DSLRule::SymbolNameOp(DSLRule::FloatType, "cm") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (std::any_cast<DSLFloat>(args[0]) * 10.0f);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "DM"),
        DSLRule::SymbolNameOp(DSLRule::NumberType, "dm") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (std::any_cast<DSLNumber>(args[0]) * 100);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "DM"),
        DSLRule::SymbolNameOp(DSLRule::FloatType, "dm") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (std::any_cast<DSLFloat>(args[0]) * 100.0f);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "M"),
        DSLRule::SymbolNameOp(DSLRule::NumberType, "m"),
        DSLRule::SymbolNameOp(DSLRule::NumberType, "meter") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (std::any_cast<DSLNumber>(args[0]) * 1000);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "M"),
        DSLRule::SymbolNameOp(DSLRule::FloatType, "m"),
        DSLRule::SymbolNameOp(DSLRule::FloatType, "meter") },
        [](DSLContext& ctx, const DSLArray& args)->std::any
    {
        WDUnused(ctx);
    return (std::any_cast<DSLFloat>(args[0]) * 1000.0f);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "IN"),
        DSLRule::SymbolNameOp(DSLRule::NumberType, "in") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (DSLNumber)((float)std::any_cast<DSLNumber>(args[0]) * 25.4f);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "IN"),
        DSLRule::SymbolNameOp(DSLRule::FloatType, "in") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (std::any_cast<DSLFloat>(args[0]) * 25.4f);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::NumberType, "FT"),
        DSLRule::SymbolNameOp(DSLRule::NumberType, "ft") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (DSLNumber)((float)std::any_cast<DSLNumber>(args[0]) * 304.8f);
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::FloatType, "FT"),
        DSLRule::SymbolNameOp(DSLRule::FloatType, "ft") },
        [](DSLContext& ctx, const DSLArray& args)->std::any 
    {
        WDUnused(ctx);
    return (std::any_cast<DSLFloat>(args[0]) * 304.8f);
    });
#pragma endregion
    /************* DISTCONVERT *************/
    ctx.registerFunction({ "DISTCONVERT" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 1)
        throw std::invalid_argument(std::string("Invalid argument count ") + std::to_string(args.size()));

    static const std::unordered_map<std::string, float> _unitMap =
    {
        {""         ,   (1.0f)},            // 没有单位默认使用mm
        {"mm"       ,   (1.0f)},            // 毫米
        {"cm"       ,   (10.0f)},           // 厘米
        {"dm"       ,   (100.0f)},          // 分米
        {"m"        ,   (1000.0f)},         // 米
        {"km"       ,   (1000000.0f)},      // 千米
        {"in"       ,   (25.4f)},           // 英尺
        {"ft"       ,   (304.8f)},          // 英寸
        {"meter"    ,   (1000.0f)},         // 米
        {"pascal"   ,   (1.0f)},            // 标准压强单位?
        {"atm"      ,   (101325.0f)}        // atmos?气压单位?
    };

    std::any x = args[0];
    auto& type = x.type();
    if (type == DSLRule::NumberType)
    {
        auto rValue = std::any_cast<DSLNumber>(x);
        return rValue;
    }
    else if (type == DSLRule::FloatType)
    {
        auto rValue = std::any_cast<DSLFloat>(x);
        return rValue;
    }
    else if (type == DSLRule::StringType)
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    else
    {
        throw std::invalid_argument(std::string("Invalid argument type ") + DSLRule::TypeName(type));
    }
    });

    /************* ACTION: SUM (SUM PARAM 6 IPARAM 1 = PARAM 6 + IPARAM 1)*************/
#pragma region ActionSUM
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::NumberType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
    auto v2 = std::any_cast<DSLNumber>(args[1]);
    return DSLNumber(v1 + v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::FloatType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
    auto v2 = std::any_cast<DSLNumber>(args[1]);
    return DSLFloat(v1 + (DSLFloat)v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::NumberType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
    auto v2 = std::any_cast<DSLFloat>(args[1]);
    return DSLFloat((DSLFloat)v1 + v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::FloatType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
    auto v2 = std::any_cast<DSLFloat>(args[1]);
    return DSLFloat(v1 + v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::StringType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1))
    {
        auto v2 = std::any_cast<DSLNumber>(args[1]);
        return DSLFloat(v1 + DSLFloat(v2));
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::NumberType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[1]), v2))
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
        return DSLFloat(DSLFloat(v1) + v2);
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::StringType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1))
    {
        auto v2 = std::any_cast<DSLFloat>(args[1]);
        return DSLFloat(v1 + v2);
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::FloatType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[1]), v2))
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
        return DSLFloat(v1 + v2);
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("SUM", { DSLRule::StringType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1) && StringToFloat(std::any_cast<DSLString>(args[1]), v2))
        return DSLFloat(v1 + v2);
    return std::any();
    });
#pragma endregion
    /************* ACTION: DIFFERENCE (DIFFERENCE PARAM 2 PARAM 7  = PARAM 2 - PARAM 7)*************/
#pragma region ActionDIFFERENCE
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::NumberType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
    auto v2 = std::any_cast<DSLNumber>(args[1]);
    return DSLNumber(v1 - v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::FloatType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
    auto v2 = std::any_cast<DSLNumber>(args[1]);
    return DSLFloat(v1 - (DSLFloat)v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::NumberType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
    auto v2 = std::any_cast<DSLFloat>(args[1]);
    return DSLFloat((DSLFloat)v1 - v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::FloatType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
    auto v2 = std::any_cast<DSLFloat>(args[1]);
    return DSLFloat(v1 - v2);
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::StringType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1))
    {
        auto v2 = std::any_cast<DSLNumber>(args[1]);
        return DSLNumber(v1) - v2;
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::NumberType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[1]), v2))
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
        return DSLFloat(DSLFloat(v1)-v2);
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::StringType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1))
    {
        auto v2 = std::any_cast<DSLFloat>(args[1]);
        return DSLFloat(DSLFloat(v1) - v2);
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::FloatType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[1]), v2))
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
        return DSLFloat(v1 - DSLFloat(v2));
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("DIFFERENCE", { DSLRule::StringType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1) && StringToFloat(std::any_cast<DSLString>(args[1]), v2))
        return DSLFloat(v1 - v2);
    return std::any();
    });
#pragma endregion
    /************* ACTION: TANF (TANF PARAM 2 DDANGLE =  TAN(DDANGLE / 2) * PARAM 2 ) *************/
#pragma region ActionTANF
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::NumberType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
    auto v2 = std::any_cast<DSLNumber>(args[1]);
    return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::FloatType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
    auto v2 = std::any_cast<DSLNumber>(args[1]);
    return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::NumberType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
    auto v2 = std::any_cast<DSLFloat>(args[1]);
    return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::FloatType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
    auto v2 = std::any_cast<DSLFloat>(args[1]);
    return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::StringType, DSLRule::NumberType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1))
    {
        auto v2 = std::any_cast<DSLNumber>(args[1]);
        return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::NumberType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[1]), v2))
    {
        auto v1 = std::any_cast<DSLNumber>(args[0]);
        return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::StringType, DSLRule::FloatType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1))
    {
        auto v2 = std::any_cast<DSLFloat>(args[1]);
        return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::FloatType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[1]), v2))
    {
        auto v1 = std::any_cast<DSLFloat>(args[0]);
        return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    }
    return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TANF", { DSLRule::StringType, DSLRule::StringType }) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v1 = 0.0f;
    float v2 = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v1) && StringToFloat(std::any_cast<DSLString>(args[1]), v2))
        return (DSLFloat)(Tan(DegToRad<double>(static_cast<double>(v2) / 2.0)) * static_cast<double>(v1));
    return std::any();
    });
#pragma endregion
    /************* ACTION: TWICE (TWICE PARAM 2 =  2 * PARAM 2 ) *************/
#pragma region ActionTWICE
    ctx.registerFunction({ DSLRule::SymbolNameAction("TWICE", { DSLRule::NumberType}) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        return DSLNumber(2 * std::any_cast<DSLNumber>(args[0]));
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TWICE", { DSLRule::FloatType}) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        return DSLFloat(2.0f * std::any_cast<DSLFloat>(args[0]));
    });
    ctx.registerFunction({ DSLRule::SymbolNameAction("TWICE", { DSLRule::StringType}) },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        float v = 0.0f;
    if (StringToFloat(std::any_cast<DSLString>(args[0]), v))
        return DSLFloat(2.0f * v);
    return std::any();
    });
#pragma endregion
    /************* Rule: OF *************/
#pragma region RuleOF
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "OF", typeid(WDNode::SharedPtr)) },
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        // 发送OF操作符通知
        ctx.sendOfOptNotice();
        auto pNode = std::any_cast<WDNode::SharedPtr>(args[1]);
        if (pNode == nullptr)
            return std::any();
        std::string attrName = std::any_cast<DSLString>(args[0]);
        if (attrName.empty())
            return std::any();
        // 特殊处理,FLNN和NAME都是获取节点名称
        // FLNN:如果节点名称以/开头,去掉/
        // NAME:如果节点名称不以/开头,加上/
        if (_stricmp(attrName.c_str(), "FLNN") == 0)
        {
            std::string nodeName = pNode->name();
            if (!nodeName.empty() && nodeName.front() == '/')
                nodeName.erase(nodeName.begin());
            if (nodeName.empty())
                throw std::logic_error("节点名称为空!");
            return DSLString(nodeName);
        }
        else if (_stricmp(attrName.c_str(), "NAME") == 0)
        {
            std::string nodeName = pNode->name();
            if (!nodeName.empty() && nodeName.front() != '/')
                nodeName.insert(nodeName.begin(), '/');
            if (nodeName.empty())
                throw std::logic_error("节点名称为空!");
            return DSLString(nodeName);
        }

        auto val = pNode->getAttribute(attrName);
        if (!val.valid())
        {
            if (auto itr = attrAliasMap.find(attrName); itr != attrAliasMap.end())
                val = pNode->getAttribute(itr->second);
        }
        if (!val.valid())
        {
            char str[1024];
            snprintf(str, sizeof(str), u8"%s类型节点没有%s名称的属性!", pNode->type().data(), attrName.c_str());
            throw std::logic_error(str);
        }
        switch (val.type())
        {
        case WDBMAttrValue::Type::T_NodeRef:
            {
                return val.toNodeRef().refNode();
            }
            break;
        case WDBMAttrValue::Type::T_Int:
            {
                return DSLNumber(val.toInt());
            }
            break;
        case WDBMAttrValue::Type::T_Double:
            {
                return DSLFloat(val.toDouble());
            }
            break;
        case WDBMAttrValue::Type::T_String:
            {
                return DSLString(val.toString());
            }
            break;
        default:
            break;
        }

        return DSLString(val.convertToString());
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "ChildIdxOF", typeid(WDNode::SharedPtr)) },
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        // 发送OF操作符通知
        ctx.sendOfOptNotice();
        auto pNode = std::any_cast<WDNode::SharedPtr>(args[1]);
        if (pNode == nullptr)
            return std::any();

        auto nodeType = std::any_cast<std::string>(args[0]);
        auto childIdx = std::any_cast<int>(args[2]);
        int idx = 1;
        for (auto& pChild : pNode->children())
        {
            if (pChild == nullptr)
                continue;
            if (nodeType == pChild->type())
            {
                if (idx == childIdx)
                    return pChild;
                ++idx;
            }
        }
        return std::any();
        });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "AttributeIdxOF", typeid(WDNode::SharedPtr)) },
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        // 发送OF操作符通知
        ctx.sendOfOptNotice();
        auto pNode = std::any_cast<WDNode::SharedPtr>(args[1]);
        if (pNode == nullptr)
            return std::any();
        // 这里下标转为从0开始
        int idx = std::any_cast<int>(args[2]) - 1;
        if (idx < 0)
            return std::any();
        auto attrName = std::any_cast<std::string>(args[0]);
        auto listAttrs = pNode->getAttribute(attrName);
        if (!listAttrs.valid())
        {
            if (auto itr = attrAliasMap.find(attrName); itr != attrAliasMap.end())
                listAttrs = pNode->getAttribute(itr->second);
            if (!listAttrs.valid())
                return std::any();
        }
        switch (listAttrs.type())
        {
        case WDBMAttrValue::Type::T_DoubleVector:
            {
                std::vector<double> dVec;
                if (!listAttrs.toDoubleVector(dVec) || dVec.size() <= idx)
                    return std::any();
                return DSLFloat(dVec[idx]);
            }
            break;
        case WDBMAttrValue::Type::T_StringVector:
            {
                std::vector<std::string> sVec;
                if (!listAttrs.toStringVector(sVec) || sVec.size() <= idx)
                    return std::any();
                return DSLString(sVec[idx]);
            }
            break;
        case WDBMAttrValue::Type::T_IntVector:
            {
                std::vector<int> iVec;
                if (!listAttrs.toIntVector(iVec) || iVec.size() <= idx)
                    return std::any();
                return DSLNumber(iVec[idx]);
            }
            break;
        case WDBMAttrValue::Type::T_NodeRefs:
            {
                WDBMNodeRefs refs;
                if (!listAttrs.toNodeRefVector(refs) || refs.size() <= idx)
                    return std::any();
                return refs[idx].refNode();
            }
            break;
        default:
            break;
        }
        return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "OF", typeid(const WDNode*)) },
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        // 发送OF操作符通知
        ctx.sendOfOptNotice();
        auto pNode = std::any_cast<const WDNode*>(args[1]);
        if (pNode == nullptr)
            return std::any();
        std::string attrName = std::any_cast<DSLString>(args[0]);
        if (attrName.empty())
            return std::any();
        // 特殊处理,FLNN和NAME都是获取节点名称
        // FLNN:如果节点名称以/开头,去掉/
        // NAME:如果节点名称不以/开头,加上/
        if (_stricmp(attrName.c_str(), "FLNN") == 0)
        {
            std::string nodeName = pNode->name();
            if (!nodeName.empty() && nodeName.front() == '/')
                nodeName.erase(nodeName.begin());
            if (nodeName.empty())
                throw std::logic_error("节点名称为空!");
            return DSLString(nodeName);
        }
        else if (_stricmp(attrName.c_str(), "NAME") == 0)
        {
            std::string nodeName = pNode->name();
            if (!nodeName.empty() && nodeName.front() != '/')
                nodeName.insert(nodeName.begin(), '/');
            if (nodeName.empty())
                throw std::logic_error("节点名称为空!");
            return DSLString(nodeName);
        }

        auto val = pNode->getAttribute(attrName);
        if (!val.valid())
        {
            if (auto itr = attrAliasMap.find(attrName); itr != attrAliasMap.end())
                val = pNode->getAttribute(itr->second);
        }
        if (!val.valid())
        {
            char str[1024];
            snprintf(str, sizeof(str), "%s 类型节点没有%s名称的属性!", pNode->type().data(), attrName.c_str());
            throw std::logic_error(str);
        }
        switch (val.type())
        {
        case WDBMAttrValue::Type::T_NodeRef:
            {
                return val.toNodeRef().refNode();
            }
            break;
        case WDBMAttrValue::Type::T_Int:
            {
                return DSLNumber(val.toInt());
            }
            break;
        case WDBMAttrValue::Type::T_Double:
            {
                return DSLFloat(val.toDouble());
            }
            break;
        case WDBMAttrValue::Type::T_String:
            {
                return DSLString(val.toString());
            }
            break;
        default:
            break;
        }

        return DSLString(val.convertToString());
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "ChildIdxOF", typeid(const WDNode*)) },
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        // 发送OF操作符通知
        ctx.sendOfOptNotice();
        auto pNode = std::any_cast<const WDNode*>(args[1]);
        if (pNode == nullptr)
            return std::any();

        auto nodeType = std::any_cast<std::string>(args[0]);
        auto childIdx = std::any_cast<int>(args[2]);
        int idx = 1;
        for (auto& pChild : pNode->children())
        {
            if (pChild == nullptr)
                continue;
            if (nodeType == pChild->type())
            {
                if (idx == childIdx)
                    return pChild;
                ++idx;
            }
        }
        return std::any();
    });
    ctx.registerFunction({ DSLRule::SymbolNameOp(DSLRule::StringType, "AttributeIdxOF", typeid(const WDNode*)) },
        [](DSLContext& ctx, const DSLArray& args) -> std::any
    {
        // 发送OF操作符通知
        ctx.sendOfOptNotice();
        auto pNode = std::any_cast<const WDNode*>(args[1]);
        if (pNode == nullptr)
            return std::any();
        // 这里下标转为从0开始
        int idx = std::any_cast<int>(args[2]) - 1;
        if (idx < 0)
            return std::any();
        auto attrName = std::any_cast<std::string>(args[0]);
        auto listAttrs = pNode->getAttribute(attrName);
        if (!listAttrs.valid())
        {
            if (auto itr = attrAliasMap.find(attrName); itr != attrAliasMap.end())
                listAttrs = pNode->getAttribute(itr->second);
            if (!listAttrs.valid())
                return std::any();
        }
        switch (listAttrs.type())
        {
        case WDBMAttrValue::Type::T_DoubleVector:
            {
                std::vector<double> dVec;
                if (!listAttrs.toDoubleVector(dVec) || dVec.size() <= idx)
                    return std::any();
                return DSLFloat(dVec[idx]);
            }
            break;
        case WDBMAttrValue::Type::T_StringVector:
            {
                std::vector<std::string> sVec;
                if (!listAttrs.toStringVector(sVec) || sVec.size() <= idx)
                    return std::any();
                return DSLString(sVec[idx]);
            }
            break;
        case WDBMAttrValue::Type::T_IntVector:
            {
                std::vector<int> iVec;
                if (!listAttrs.toIntVector(iVec) || iVec.size() <= idx)
                    return std::any();
                return DSLNumber(iVec[idx]);
            }
            break;
        case WDBMAttrValue::Type::T_NodeRefs:
            {
                WDBMNodeRefs refs;
                if (!listAttrs.toNodeRefVector(refs) || refs.size() <= idx)
                    return std::any();
                return refs[idx].refNode();
            }
            break;
        default:
            break;
        }
        return std::any();
    });
#pragma endregion
    /************* Function: MAX (PARAM 6, IPARAM 1) = std::max(PARAM 6, IPARAM 1)*************/
#pragma region FunctionMAX
    ctx.registerFunction({ "MAX", "max" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 2)
        return std::any();
    float v1 = 0.0;
    float v2 = 0.0;
    const auto& v1Type = args[0].type();
    if (v1Type == DSLRule::NumberType)
    {
        const auto& v2Type = args[1].type();
        if (v2Type == DSLRule::NumberType)
        {
            return DSLNumber(std::max(std::any_cast<DSLNumber>(args[0]), std::any_cast<DSLNumber>(args[1])));
        }
        else if (v2Type == DSLRule::FloatType)
        {
            v2 = static_cast<float>(std::any_cast<DSLFloat>(args[1]));
        }
        else if (v2Type == DSLRule::StringType)
        {
            auto str = std::any_cast<DSLString>(args[1]);
            if (!StringToFloat(str, v2))
                return std::any();
            if (str.find('.') == std::string::npos)
                return DSLNumber(std::max(std::any_cast<DSLNumber>(args[0]), static_cast<int>(v2)));
        }
        else
        {
            return std::any();
        }
        v1 = static_cast<float>(std::any_cast<DSLNumber>(args[0]));
    }
    else if (v1Type == DSLRule::FloatType)
    {
        v1 = static_cast<float>(std::any_cast<DSLFloat>(args[0]));
        const auto& v2Type = args[1].type();
        if (v2Type == DSLRule::NumberType)
        {
            v2 = static_cast<float>(std::any_cast<DSLNumber>(args[1]));
        }
        else if (v2Type == DSLRule::FloatType)
        {
            v2 = static_cast<float>(std::any_cast<DSLFloat>(args[1]));
        }
        else if (v2Type == DSLRule::StringType)
        {
            if (!StringToFloat(std::any_cast<DSLString>(args[1]), v2))
                return std::any();
        }
        else
        {
            return std::any();
        }
    }
    else if (v1Type == DSLRule::StringType)
    {
        auto str = std::any_cast<DSLString>(args[0]);
        if (!StringToFloat(str, v1))
            return std::any();
        const auto& v2Type = args[1].type();
        if (v2Type == DSLRule::NumberType)
        {
            if (str.find('.') == std::string::npos)
                return DSLNumber(std::max(static_cast<int>(v1), std::any_cast<DSLNumber>(args[1])));

            v2 = static_cast<float>(std::any_cast<DSLNumber>(args[1]));
        }
        else if (v2Type == DSLRule::FloatType)
        {
            v2 = static_cast<float>(std::any_cast<DSLFloat>(args[1]));
        }
        else if (v2Type == DSLRule::StringType)
        {
            if (!StringToFloat(std::any_cast<DSLString>(args[1]), v2))
                return std::any();

            auto strArg1 = std::any_cast<DSLString>(args[1]);
            if (!StringToFloat(strArg1, v2))
                return std::any();
            if (str.find('.') == std::string::npos && strArg1.find('.') == std::string::npos)
            {
                return DSLNumber(std::max(static_cast<int>(v1), static_cast<int>(v2)));
            }
        }
        else
        {
            return std::any();
        }
    }
    else
    {
        return std::any();
    }
    return DSLFloat(std::max(v1, v2));
    });
#pragma endregion
    /************* Function: MIN (PARAM 6, IPARAM 1) = std::min(PARAM 6, IPARAM 1)*************/
#pragma region FunctionMIN
    ctx.registerFunction({ "MIN", "min" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 2)
        return std::any();
    float v1 = 0.0;
    float v2 = 0.0;
    const auto& v1Type = args[0].type();
    if (v1Type == DSLRule::NumberType)
    {
        const auto& v2Type = args[1].type();
        if (v2Type == DSLRule::NumberType)
        {
            return DSLNumber(std::min(std::any_cast<DSLNumber>(args[0]), std::any_cast<DSLNumber>(args[1])));
        }
        else if (v2Type == DSLRule::FloatType)
        {
            v2 = static_cast<float>(std::any_cast<DSLFloat>(args[1]));
        }
        else if (v2Type == DSLRule::StringType)
        {
            auto str = std::any_cast<DSLString>(args[1]);
            if (!StringToFloat(str, v2))
                return std::any();
            if (str.find('.') == std::string::npos)
                return DSLNumber(std::min(std::any_cast<DSLNumber>(args[0]), static_cast<int>(v2)));
        }
        else
        {
            return std::any();
        }
        v1 = static_cast<float>(std::any_cast<DSLNumber>(args[0]));
    }
    else if (v1Type == DSLRule::FloatType)
    {
        v1 = static_cast<float>(std::any_cast<DSLFloat>(args[0]));
        const auto& v2Type = args[1].type();
        if (v2Type == DSLRule::NumberType)
        {
            v2 = static_cast<float>(std::any_cast<DSLNumber>(args[1]));
        }
        else if (v2Type == DSLRule::FloatType)
        {
            v2 = static_cast<float>(std::any_cast<DSLFloat>(args[1]));
        }
        else if (v2Type == DSLRule::StringType)
        {
            if (!StringToFloat(std::any_cast<DSLString>(args[1]), v2))
                return std::any();
        }
        else
        {
            return std::any();
        }
    }
    else if (v1Type == DSLRule::StringType)
    {
        auto str = std::any_cast<DSLString>(args[0]);
        if (!StringToFloat(str, v1))
            return std::any();
        const auto& v2Type = args[1].type();
        if (v2Type == DSLRule::NumberType)
        {
            if (str.find('.') == std::string::npos)
                return DSLNumber(std::min(static_cast<int>(v1), std::any_cast<DSLNumber>(args[1])));

            v2 = static_cast<float>(std::any_cast<DSLNumber>(args[1]));
        }
        else if (v2Type == DSLRule::FloatType)
        {
            v2 = static_cast<float>(std::any_cast<DSLFloat>(args[1]));
        }
        else if (v2Type == DSLRule::StringType)
        {
            if (!StringToFloat(std::any_cast<DSLString>(args[1]), v2))
                return std::any();

            auto strArg1 = std::any_cast<DSLString>(args[1]);
            if (!StringToFloat(strArg1, v2))
                return std::any();
            if (str.find('.') == std::string::npos && strArg1.find('.') == std::string::npos)
            {
                return DSLNumber(std::min(static_cast<int>(v1), static_cast<int>(v2)));
            }
        }
        else
        {
            return std::any();
        }
    }
    else
    {
        return std::any();
    }
    return DSLFloat(std::min(v1, v2));
    });
#pragma endregion
    /************* Function: POW (PARAM 6, Int) 幂运算*************/
#pragma region FunctionPOW
    ctx.registerFunction({ "POW", "pow" },
        [](DSLContext&, const DSLArray& args) -> std::any
    {
        if (args.size() != 2)
        return std::any();
    float v1 = 0.0;
    float v2 = 0.0;
    const auto& v1Type = args[0].type();
    if (v1Type == DSLRule::NumberType)
    {
        v1 = static_cast<float>(std::any_cast<DSLNumber>(args[0]));
    }
    else if (v1Type == DSLRule::FloatType)
    {
        v1 = static_cast<float>(std::any_cast<DSLFloat>(args[0]));
    }
    else if (v1Type == DSLRule::StringType)
    {
        if (!StringToFloat(std::any_cast<DSLString>(args[0]), v1))
            return std::any();
    }
    const auto& v2Type = args[1].type();
    if (v2Type == DSLRule::NumberType)
    {
        v2 = static_cast<float>(std::any_cast<DSLNumber>(args[1]));
    }
    else if (v2Type == DSLRule::FloatType)
    {
        v2 = static_cast<float>(std::any_cast<DSLFloat>(args[1]));
    }
    else if (v2Type == DSLRule::StringType)
    {
        if (!StringToFloat(std::any_cast<DSLString>(args[1]), v2))
            return std::any();
    }
    if (v1Type == DSLRule::FloatType || v2Type == DSLRule::FloatType)
        return std::pow(v1, v2);

    return DSLFloat(std::pow(v1, v2));
    });
#pragma endregion
}
bool DSLExec(DSLContext& dsl, const std::string& expr, std::any& outValue, std::string& outErr, bool bCheckExpression)
{
    if (expr.empty())
        return true;
    if (bCheckExpression)
    {
        if (expr.front() != '(' || expr.back() != ')')
        {
            outValue = expr;
            return true;
        }
    }
    DSLStringLogger logger;
    DSLCode code(expr);
    outValue = dsl.exec(code, &logger);
    if (logger.logs.tellp() != 0)
    {
        outErr = logger.logs.str();
        return false;
    }
    return true;
}
bool DSLExec(DSLContext& dsl, const std::string& expr, float& outValue, std::string& outErr, bool bCheckExpression)
{
    std::any rValue;
    if (!DSLExec(dsl, expr, rValue, outErr, bCheckExpression))
        return false;
    auto pNumber = std::any_cast<DSLNumber>(&rValue);
    if (pNumber != nullptr) 
    {
        outValue = static_cast<float>(*pNumber);
        return true;
    }
    auto pFloat = std::any_cast<DSLFloat>(&rValue);
    if (pFloat != nullptr)
    {
        outValue = *pFloat;
        return true;
    }
    auto pString = std::any_cast<DSLString>(&rValue);
    if (pString != nullptr)
    {
        float num = 0.0f;
        if (sscanf(pString->c_str(), "%f", &num) > 0)
        {
            outValue = num;
            return true;
        }
    }
    //outErr = "Invalid Result Data Type!";
    return false;
}
bool DSLExec(DSLContext& dsl, const std::string& expr, std::string& outValue, std::string& outErr, bool bCheckExpression)
{
    std::any rValue;
    if (!DSLExec(dsl, expr, rValue, outErr, bCheckExpression))
        return false;
    auto pNumber = std::any_cast<DSLNumber>(&rValue);
    if (pNumber != nullptr)
    {
        outValue = WD::ToString<DSLNumber>(*pNumber);
        return true;
    }
    auto pFloat = std::any_cast<DSLFloat>(&rValue);
    if (pFloat != nullptr)
    {
        outValue = WD::ToString<DSLFloat>(*pFloat);
        return true;
    }
    auto pString = std::any_cast<DSLString>(&rValue);
    if (pString != nullptr)
    {
        outValue = *pString;
        return true;
    }
    //outErr = "Invalid Result Data Type!";
    return false;
}
bool DSLExecDirection(DSLContext& dsl, const std::string& expr, DSLKeyPointResult& outValue, std::string& outErr) 
{
    // 先通过轴向表达式解析
    // !注意: 这里有个特殊处理, 发现轴向表达式中存在 "AXIS -X" 这种写法, 这里直接忽略 AXIS
    size_t fPos = expr.find("AXIS");
    if (fPos != std::string::npos)
    {
        std::string tExpr = expr;
        tExpr.replace(fPos, 4, "");
        bool bRet = DirParser::ExpressionDirection<DSLContext>(tExpr, outValue.direction, dsl
            , [&outErr](const std::string& expressStr, float& outValue, DSLContext& dsl)
            {
                if (!DSLExec(dsl, expressStr, outValue, outErr))
                    return false;
                return true;
            });

        if (bRet) 
            return true;
    }
    else
    {
        bool bRet = DirParser::ExpressionDirection<DSLContext>(expr, outValue.direction, dsl
            , [&outErr](const std::string& expressStr, float& outValue, DSLContext& dsl)
            {
                if (!DSLExec(dsl, expressStr, outValue, outErr))
                    return false;
                return true;
            });

        if (bRet) 
            return true;
    }
    // 再通过表达式解析，这里主要处理的就是关键点的引用(表达式的值一般是: P1, P2, ... 这些类型)
    std::any rValue;
    if (DSLExec(dsl, expr, rValue, outErr))
    {
        auto pKeyPtRet = std::any_cast<DSLKeyPointResult>(&rValue);
        if (pKeyPtRet != nullptr)
        {
            outValue = *pKeyPtRet;
            return true;
        }
    }
    //outErr = "Invalid Result Data Type!";
    return false;
}
bool DSLExecPosition(DSLContext& dsl, const std::string& expr, FVec3& outPosition, std::string& outErr)
{
    // 先通过位置表达式解析
    auto bRet = PosParser::ExpressionPosition<DSLContext>(expr, outPosition, dsl
        , [&outErr](const std::string& expressStr, float& outValue, DSLContext& dsl)
        {
            if (!DSLExec(dsl, expressStr, outValue, outErr))
                return false;
            return true;
        });
    if (bRet)
        return true;

    std::any rValue;
    if (DSLExec(dsl, expr, rValue, outErr))
    {
        auto pKeyPtRet = std::any_cast<DSLKeyPointResult>(&rValue);
        if (pKeyPtRet != nullptr)
        {
            outPosition = pKeyPtRet->position;
            return true;
        }
    }
    //outErr = "Invalid Result Data Type!";
    return false;
}


WD_NAMESPACE_END