#include "ValueGet.h"
#include "Context.h"
#include "../../../typeMgr/WDBMTypeMgr.h"

WD_NAMESPACE_BEGIN

std::optional<std::any> ValueGetBase::GetAttrValue(Context& cxt
    , const WDBMAttrValue& value
    , const std::optional<int>& index
    , const FuncStringParse& funcStrParse)
{
    if (!value.valid())
        return std::nullopt;

    switch (value.type())
    {
    case WDBMAttrValue::Type::T_Bool:
    {
        return DSLBool(value.toBool());
    }
    break;
    case WDBMAttrValue::Type::T_Int:
    {
        return DSLNumber(value.toInt());
    }
    break;
    case WDBMAttrValue::Type::T_Double:
    {
        return DSLFloat(value.toDouble());
    }
    break;
    case WDBMAttrValue::Type::T_String:
    {
        auto rStr = value.toString();
        if (funcStrParse && !rStr.empty())
            return funcStrParse(rStr, cxt);
        else
            return rStr;
    }
    break;
    case WDBMAttrValue::Type::T_Word:
    {
        // Word当成字符串处理
        return std::string(value.toWord());
    }
    break;
    case WDBMAttrValue::Type::T_NodeRef:
    {
        return value.toNodeRef().refNode();
    }
    break;
    case WDBMAttrValue::Type::T_IntVector:
    {
        if (!index)
            return std::nullopt;
        auto v = value.toIntVector();
        auto idx = index.value() - 1;
        if (idx >= 0 && idx < v.size())
            return DSLNumber(v.at(idx));
    }
    break;
    case WDBMAttrValue::Type::T_DoubleVector:
    {
        if (!index)
            return std::nullopt;
        auto v = value.toDoubleVector();
        auto idx = index.value() - 1;
        if (idx >= 0 && idx < v.size())
            return DSLFloat(v.at(idx));
    }
    break;
    case WDBMAttrValue::Type::T_StringVector:
    {
        if (!index)
            return std::nullopt;
        auto v = value.toStringVector();
        auto idx = index.value() - 1;
        if (idx >= 0 && idx < v.size())
        {
            auto rStr = v.at(idx);
            if (funcStrParse && !rStr.empty())
                return funcStrParse(rStr, cxt);
            else
                return rStr;
        }
    }
    break;
    case WDBMAttrValue::Type::T_NodeRefs:
    {
        if (!index)
            return std::nullopt;
        auto v = value.toNodeRefVector();
        auto idx = index.value() - 1;
        if (idx >= 0 && idx < v.size())
            return v.at(idx).refNode();
    }
    break;
    default:
        assert(false && "暂未支持");
        break;
    }
    return std::nullopt;
}
std::optional<std::any> ValueGetBase::GetAttrValueByNode(Context& cxt
    , const WDNode& node
    , const WDBMAttrDesc& aDesc
    , const std::optional<int>& index
    , const FuncStringParse& funcStrParse)
{
    return GetAttrValue(cxt, aDesc.value(node), index, funcStrParse);
}

std::any ValueGetMgr::getValue(Context& cxt, const std::string& name, const std::optional<int>& index)
{
    std::any rValue;
    // 先从值获取对象中查找
    for (auto pVGet : _vGets)
    {
        if (pVGet == nullptr)
            continue;
        auto bSupport = pVGet->getValue(cxt, name, rValue, index);
        if (bSupport)
            return rValue;
    }
    // 再从值获取回调中查找
    if (_vGetFunc) 
    {
        auto bSupport = _vGetFunc(name, rValue, index);
        if (bSupport)
            return rValue;
    }
    ParseFaildLogPrint(cxt, "!注意: 变量不支持, 变量名:" + name);
    return std::any();
}


GVarsValueGet::GVarsValueGet()
{
    _map = {
         { "DDANGLE", "Angle" }
        ,{ "DDHEIGHT", "Height" }
        ,{ "DDRADIUS", "Radius" } };
}

bool GVarsValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    // 查询映射表
    auto fItr = _map.find(name);
    if (fItr == _map.end())
        return bSupport;

    // 支持变量, 开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;
    auto pTypeDesc = pCurrNode->getTypeDesc();
    if (pTypeDesc == nullptr)
        return bSupport;
    // 通过映射表，获取属性名称
    const auto& aName = fItr->second;
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get(aName);
    if (pAttrDesc == nullptr)
        return bSupport;
    // 记录属性描述对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendADesc(pAttrDesc);
    // 获取属性值
    auto tRet = GetAttrValueByNode(cxt, *pCurrNode, *pAttrDesc, index);
    if (tRet)
        outValue = tRet.value();
    return bSupport;
}
bool AttrValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const 
{
    bool bSupport = false;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;
    auto pTypeDesc = pCurrNode->getTypeDesc();
    if (pTypeDesc == nullptr)
        return bSupport;
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get(name);
    if (pAttrDesc == nullptr)
        return bSupport;
    // 支持变量，开始获取变量值
    bSupport = true;

    // 记录属性描述对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendADesc(pAttrDesc);
    // 获取属性值
    auto tRet = GetAttrValueByNode(cxt, *pCurrNode, *pAttrDesc, index);
    if (tRet)
        outValue = tRet.value();
    return bSupport;
}
bool DespValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;

    if (!index || index.value() < 0)
        return bSupport;
    // 判断是否是DESP
    if (_stricmp("DESP", name.c_str()) != 0 
        && _stricmp("DESIGN PARAM", name.c_str()) != 0
        && _stricmp("DDESP", name.c_str()) != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;
    auto pTypeDesc = pCurrNode->getTypeDesc();
    if (pTypeDesc == nullptr)
        return bSupport;
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get("DESP");
    if (pAttrDesc == nullptr)
        return bSupport;
    // 记录属性描述对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendADesc(pAttrDesc);
    // 获取属性值
    auto tRet = GetAttrValueByNode(cxt, *pCurrNode, *pAttrDesc, index, [](const std::string& str, Context& cxt)
        {
            WDUnused(cxt);
            // 这里尝试转换为数值型，如果转换失败，则返回源字符串
            bool bOk = false;
            float val = FromString<float>(str, &bOk);
            if (bOk)
                return std::any(DSLFloat(val));
            else
                return std::any(str);
        });
    if (tRet)
        outValue = tRet.value();
    else // 注意，这里设计参数解析失败时，需要返回0
        outValue = DSLFloat(0.0f);
    return bSupport;
}

bool ParamValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    if (!index || index.value() < 0)
        return bSupport;
    // 判断是否是元件参数
    if (_stricmp("PARA", name.c_str()) != 0
        && _stricmp("PARAM", name.c_str()) != 0
        && _stricmp("CPAR", name.c_str()) != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pSCOMNode = cxt.scomNode();
    if (pSCOMNode == nullptr)
        return bSupport;
    auto pTypeDesc = pSCOMNode->getTypeDesc();
    if (pTypeDesc == nullptr)
        return bSupport;
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get("Param");
    if (pAttrDesc == nullptr)
        return bSupport;
    // 获取属性值
    auto tRet = GetAttrValueByNode(cxt, *pSCOMNode, *pAttrDesc, index, [](const std::string& str, Context& cxt)
        {
            WDUnused(cxt);
            // 这里尝试转换为数值型，如果转换失败，则返回源字符串
            bool bOk = false;
            float val = FromString<float>(str, &bOk);
            if (bOk)
                return std::any(DSLFloat(val));
            else
                return std::any(str);
        });
    if (tRet)
        outValue = tRet.value();
    return bSupport;
}
bool ODespValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    if (!index || index.value() < 0)
        return bSupport;
    // 判断是否是父节点设计参数
    if (_stricmp("ODESP", name.c_str()) != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;

    // 获取ODESP属性
    bool bOk = false;
    DNodeODESPGet oGet;
    auto rValue = oGet.get(*pCurrNode, &bOk);
    if (!bOk)
        return bSupport;
    // 记录属性描述对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendAGet<DNodeODESPGet>();
    // 获取属性值
    auto tRet = GetAttrValue(cxt, rValue, index, [](const std::string& str, Context& cxt)
        {
            WDUnused(cxt);
            // 这里尝试转换为数值型，如果转换失败，则返回源字符串
            bool bOk = false;
            float val = FromString<float>(str, &bOk);
            if (bOk)
                return std::any(DSLFloat(val));
            else
                return std::any(str);
        });
    if (tRet)
        outValue = tRet.value();
    else // 注意，这里设计参数解析失败时，需要返回0
        outValue = DSLFloat(0.0f);
    return bSupport;
}

bool OParamValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    if (!index || index.value() < 0)
        return bSupport;

    // 判断是否是父节点元件参数
    if (_stricmp("OPAR", name.c_str()) != 0
        && _stricmp("OPARAM", name.c_str()) != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;

    // 获取OPAR属性
    bool bOk = false;
    DNodeOParamGet oGet;
    auto rValue = oGet.get(*pCurrNode, &bOk);
    if (!bOk)
        return bSupport;
    // 记录属性描述对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendAGet<DNodeOParamGet>();
    // 获取属性值
    auto tRet = GetAttrValue(cxt, rValue, index, [](const std::string& str, Context& cxt)
        {
            WDUnused(cxt);
            // 这里尝试转换为数值型，如果转换失败，则返回源字符串
            bool bOk = false;
            float val = FromString<float>(str, &bOk);
            if (bOk)
                return std::any(DSLFloat(val));
            else
                return std::any(str);
        });
    if (tRet)
        outValue = tRet.value();
    return bSupport;
}
bool IParamValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    if (!index || index.value() < 0)
        return bSupport;
    // 判断是否是保温参数
    if (_stricmp("IPARAM", name.c_str()) != 0
        && _stricmp("IPAR", name.c_str()) != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;
    // 保温即使没有值，也应该返回0
    outValue = DSLFloat(0.0f);
    // 如果未开启保温，默认返回0.0f, 并且不用缓存保温属性描述对象，因为所有节点的保温都是0.0
    if (!cxt.bInsu())
        return bSupport;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;
    auto pTypeDesc = pCurrNode->getTypeDesc();
    if (pTypeDesc == nullptr)
        return bSupport;
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get("Iparam");
    if (pAttrDesc == nullptr)
        return bSupport;

    // 记录属性描述对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendADesc(pAttrDesc);
    // 获取属性值
    auto tRet = GetAttrValueByNode(cxt, *pCurrNode, *pAttrDesc, index);
    if (tRet)
        outValue = tRet.value();
    else // 如果获取失败，这里返回0
        outValue = DSLFloat(0.0f);
    return bSupport;

}

bool KeyPointValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    WDUnused(index);
    bool bSupport = false;
    // 判断是否是点集 形式 PN (N >= 0)
    if (name.size() >= 2 && (name[0] == 'P'))
    {
        for (int i = 1; i < name.size(); ++i)
        {
            if (name[i] < '0' || name[i] > '9')
            {
                return bSupport;
            }
        }
    }
    else
    {
        return bSupport;
    }
    // 支持变量，开始获取变量值
    bSupport = true;

    auto ret = cxt.keyPtSetCatch().query(name);
    if (ret)
    {
        DSLKeyPointResult retPoint;
        retPoint.position = FVec3(ret.value().position);
        retPoint.direction = FVec3(ret.value().direction);
        outValue = retPoint;
    }
    return bSupport;
}

bool PWALLTValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    if (!index || index.value() < 0)
        return bSupport;
    // 判断是否是取壁厚
    if (_stricmp(name.c_str(), "PWALLT") != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;

    auto& node = *pCurrNode;
    auto pSPCONode = Context::GetSPCONode(node);
    if (pSPCONode == nullptr)
        return bSupport;

    // 无法进行共享
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->setSharedDisabled();

    // 获取节点名称
    char ptName[64] = { 0 };
    sprintf(ptName, "P%d", index.value());
    auto rPt = cxt.keyPtSetCatch().query(ptName);
    if (!rPt)
        return bSupport;

    bool bOk = false;
    double bore = FromString<double>(rPt.value().bore(), &bOk);
    if (!bOk)
        return bSupport;
    // 获取SPEC节点
    auto pSPECNode = pSPCONode->parent();
    while (pSPECNode != nullptr)
    {
        if (pSPECNode->isType("SPEC"))
            break;
        pSPECNode = pSPECNode->parent();
    }
    if (pSPECNode == nullptr)
        return bSupport;
    // 获取管道数据表
    auto pPdareferenceNode = pSPECNode->getAttribute("Pdareference").toNodeRef().refNode();
    if (pPdareferenceNode == nullptr)
        return bSupport;
    WD::WDNode::SharedPtr pPDAELENode = nullptr;
    for (auto& pChild : pPdareferenceNode->children())
    {
        if (pChild == nullptr)
            continue;
        if (pChild->getAttribute("NBore").toDouble() == bore)
        {
            pPDAELENode = pChild;
            break;
        }
    }
    if (pPDAELENode == nullptr)
        return bSupport;
    auto pWthreferenceNode = pPDAELENode->getAttribute("Wthreference").toNodeRef().refNode();
    if (pWthreferenceNode == nullptr)
        return bSupport;
    WD::WDNode::SharedPtr pWTHELENode = nullptr;
    for (auto& pChild : pWthreferenceNode->children())
    {
        if (pChild == nullptr)
            continue;
        if (pChild->getAttribute("NBore").toDouble() == bore)
        {
            pWTHELENode = pChild;
            break;
        }
    }
    if (pWTHELENode == nullptr)
        return bSupport;
    outValue = DSLFloat(pWTHELENode->getAttribute("WThickness").toDouble());
    return bSupport;
}

bool RPRODataValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    // 判断属性是否是数据引用
    if (_strnicmp(name.c_str(), "RPRO ", 5) != 0
        && _strnicmp(name.c_str(), "RPRO_", 5) != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;
    // 提取属性名称中的数据键
    std::string key = std::string(name.begin() + 5, name.end());
    if (key.empty())
        return bSupport;
    // 查询number，如果查询的number有效，则先尝试从设计模块节点的DESP属性来获取值
    auto rNumber = cxt.dataSetCatch().queryNumberByDKey(key);
    if (rNumber) 
    {
        auto pTypeDesc = pCurrNode->getTypeDesc();
        if (pTypeDesc != nullptr)
        {
            // 根据名称查询属性描述
            auto pAttrDesc = pTypeDesc->get("DESP");
            if (pAttrDesc != nullptr)
            {
                // 记录属性描述对象
                if (cxt.pDNodeAKey != nullptr)
                    cxt.pDNodeAKey->appendADesc(pAttrDesc);
                // 此次解析不报告错误
                cxt.bSilence = true;
                // 获取属性值
                auto tRet = GetAttrValueByNode(cxt, *pCurrNode, *pAttrDesc, index, [](const std::string& str, Context& cxt)
                    {
                        // 这里执行表达式解析，如果执行失败，则返回源字符串
                        std::any outValue;
                        std::string errStr;
                        if (DSLExec(cxt.dsl, str, outValue, errStr))
                        {
                            return outValue;
                        }
                        else
                        {
                            return std::any(str);
                        }
                    });
                cxt.bSilence = false;
                if (tRet)
                {
                    outValue = tRet.value();
                    return bSupport;
                }
            }
        }
    }
    // 否则获取对应Key的DATA对象的Pproperty属性作为结果值
    outValue = cxt.dataSetCatch().queryByDKey(key);
    if (!outValue.has_value())// 注意，这里设计参数解析失败时，需要返回0
        outValue = DSLFloat(0.0f);

    return bSupport;
}

bool LOHEValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    bool bSupport = false;
    if (_stricmp(name.c_str(), "LOHE") != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;

    bool bOk = false;
    DNodeLOHEGet get;
    auto rValue = get.get(*pCurrNode, &bOk);
    if (!bOk)
        return bSupport;
    // 记录属性获取对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendAGet<DNodeLOHEGet>();
    // 获取属性值
    auto tRet = GetAttrValue(cxt, rValue, index);
    if (tRet)
        outValue = tRet.value();
    return bSupport;
}

bool CEValueGet::getValue(Context& cxt
    , const std::string& name
    , std::any& outValue
    , const std::optional<int>& index) const
{
    WDUnused(index);
    bool bSupport = false;
    // 
    if (_stricmp(name.c_str(), "CE") != 0)
        return bSupport;

    // 支持变量，开始获取变量值
    bSupport = true;

    auto pCurrNode = cxt.node();
    if (pCurrNode == nullptr)
        return bSupport;
    outValue = pCurrNode;
    return bSupport;
}

WD_NAMESPACE_END

