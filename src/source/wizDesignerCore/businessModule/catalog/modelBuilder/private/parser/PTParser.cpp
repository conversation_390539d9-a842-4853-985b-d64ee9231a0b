#include "PTParser.h"

WD_NAMESPACE_BEGIN

PTParserBase::PTParserBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
{
    _pTypeDesc = typeMgr.get(type);
    PARSE_ASSERT(_pTypeDesc != nullptr);
    std::fill(_attrs.begin(), _attrs.end(), nullptr);
    // 缓存部分通用属性
    if (_pTypeDesc != nullptr)
    {
        // 关键点编号
        _pAttrDescNumber = _pTypeDesc->get("Number");
        // 公称直径
        _pAttrDescBore = _pTypeDesc->get("Pbore");
        // 连接形式
        _pAttrDescCType = _pTypeDesc->get("Pconnect");
    }
}

PTParserBase::~PTParserBase()
{

}

int PTParserBase::getNumber(Context& context, const WDNode& node)
{
    WDUnused(context);
    if (_pAttrDescNumber == nullptr)
        return -1;
    auto rValue = _pAttrDescNumber->valueCRef(node);
    auto pInt = rValue.toInt();
    if (pInt == nullptr)
        return -1;
    return *pInt;
}
std::string PTParserBase::getBore(Context& context, const WDNode& node)
{
    if (_pAttrDescBore == nullptr)
        return "";
    auto rValue = _pAttrDescBore->valueCRef(node);
    auto pStr = rValue.toString();
    if (pStr == nullptr)
        return "";
    if (IsUnsetValue(*pStr))
        return "";
    std::string rBore;
    if (DSLExec(context.dsl, (*pStr), rBore, _lastErrStr))
        return rBore;
    return (*pStr);
}
std::string PTParserBase::getConnectType(Context& context, const WDNode& node)
{
    if (_pAttrDescCType == nullptr)
        return "";
    auto rValue = _pAttrDescCType->valueCRef(node);
    auto pStr = rValue.toString();
    if (pStr == nullptr)
        return "";
    if (IsUnsetValue(*pStr))
        return "";
    // 判断是否是元件参数, 如果是元件参数，直接获取元件参数
    {
        std::string tStr;
        tStr.reserve(pStr->size());
        for (auto ch : (*pStr))
        {
            bool bSkip = false;
            switch (ch)
            {
            case ' ':
            case '[':
            case ']':
            case '(':
            case ')':
                bSkip = true;
                break;
            default:
                break;
            }
            if (bSkip)
                continue;
            tStr.push_back(static_cast<char>(std::toupper(ch)));
        }
        // 如果以ATTRIB开头，则移除ATTRIB
        if (tStr.size() > 6 && tStr[0] == 'A' && tStr[1] == 'T' && tStr[2] == 'T' && tStr[3] == 'R' && tStr[4] == 'I' && tStr[5] == 'B')
        {
            tStr = tStr.substr(6);
        }
        // "PARA"
        // "PARAM"
        // "CPAR"
        if (tStr.size() > 4)
        {
            size_t cnt = 0;
            if (tStr[0] == 'P' && tStr[1] == 'A' && tStr[2] == 'R' && tStr[3] == 'A' && tStr[4] == 'M')
                cnt = 5;
            else if (tStr[0] == 'P' && tStr[1] == 'A' && tStr[2] == 'R' && tStr[3] == 'A')
                cnt = 4;
            else if (tStr[0] == 'C' && tStr[1] == 'P' && tStr[2] == 'A' && tStr[3] == 'R')
                cnt = 4;

            std::string numStr = tStr.substr(cnt);
            bool isNumber = true;
            if (!numStr.empty())
            {
                for (auto ch : numStr)
                {
                    if (ch < '0' || ch > '9')
                    {
                        isNumber = false;
                        break;
                    }
                }
            }
            if (isNumber)
            {
                bool bOk = false;
                int num = FromString<int>(numStr, &bOk);
                if (num > 0 && bOk)
                {
                    auto retAValue = context.valueGetMgr().getValue(context, tStr.substr(0, cnt), num);
                    auto pRetStr = std::any_cast<std::string>(&retAValue);
                    if (pRetStr != nullptr && !pRetStr->empty())
                        return *pRetStr;
                }
            }
        }
    }
    // 走到这里说明不是元件参数，直接返回字符串本身
    return (*pStr);
}

bool PTParserBase::put(size_t index, const std::string_view& name)
{
    if (_pTypeDesc == nullptr)
        return false;
    assert(index < _attrs.size() && "数组越界!");
    _attrs[index] = _pTypeDesc->get(name);
    assert(_attrs[index] != nullptr);
    return _attrs[index] != nullptr;
}

bool PTParserBase::parse(size_t index, Context& context, const WDNode& node, float& outValue)
{
    auto pRValue = this->get(index, node);
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (rValue.empty() || IsUnsetValue(rValue))
    {
        outValue = 0.0f;
        return true;
    }
    if (!DSLExec(context.dsl, rValue, outValue, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, getName(index), rValue, _lastErrStr);
        return false;
    }
    return true;
}
bool PTParserBase::parse(size_t index, Context& context, const WDNode& node, DSLKeyPointResult& outRet)
{
    auto pRValue = this->get(index, node);
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (IsUnsetValue(rValue))
        return false;
    if (!DSLExecDirection(context.dsl, rValue, outRet, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, getName(index), rValue, _lastErrStr);
        return false;
    }
    return true;
}
bool PTParserBase::parse(size_t index, Context& context, const WDNode& node, FVec3& outRet)
{
    auto pRValue = this->get(index, node);
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (IsUnsetValue(rValue))
        return false;
    if (!DSLExecPosition(context.dsl, rValue, outRet, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, getName(index), rValue, _lastErrStr);
        return false;
    }
    return true;
}

std::optional<WDKeyPoint> PTParserPTAX::build(Context& context, const WDNode& node)
{
    // 关键点编号
    int number = getNumber(context, node);
    if (number == -1)
        return std::nullopt;
    int index = 0;
    // 解析朝向
    DSLKeyPointResult retPosDir;
    if (!parse(index++, context, node, retPosDir))
        return std::nullopt;
    // 解析距离
    float distance = 0.0f;
    if (!parse(index++, context, node, distance))
        return std::nullopt;
    // 结果关键点
    return WDKeyPoint(number
        , DVec3(retPosDir.direction * distance + retPosDir.position)
        , DVec3(retPosDir.direction)
        , this->getBore(context, node)
        , this->getConnectType(context, node));
}

std::optional<WDKeyPoint> PTParserPTCA::build(Context& context, const WDNode& node)
{
    // 关键点编号
    int number = getNumber(context, node);
    if (number == -1)
        return std::nullopt;
    int index = 0;
    // 解析朝向
    DSLKeyPointResult retPosDir;
    if (!parse(index++, context, node, retPosDir))
        return std::nullopt;
    // 解析坐标 X
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return std::nullopt;
    // 解析坐标 Y
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return std::nullopt;
    // 解析坐标 z
    float pz = 0.0f;
    if (!parse(index++, context, node, pz))
        return std::nullopt;
    // 结果关键点
    return WDKeyPoint(number
        , DVec3(px, py, pz) + DVec3(retPosDir.position)
        , DVec3(retPosDir.direction)
        , this->getBore(context, node)
        , this->getConnectType(context, node));
}

std::optional<WDKeyPoint> PTParserPTMI::build(Context& context, const WDNode& node)
{
    // 关键点编号
    int number = getNumber(context, node);
    if (number == -1)
        return std::nullopt;
    int index = 0;
    // 解析朝向
    DSLKeyPointResult retPosDir;
    if (!parse(index++, context, node, retPosDir))
        return std::nullopt;
    // 解析坐标 X
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return std::nullopt;
    // 解析坐标 Y
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return std::nullopt;
    // 解析坐标 z
    float pz = 0.0f;
    if (!parse(index++, context, node, pz))
        return std::nullopt;
    // 结果关键点
    return WDKeyPoint(number
        , DVec3(px, py, pz) + DVec3(retPosDir.position)
        , DVec3(retPosDir.direction)
        , this->getBore(context, node)
        , this->getConnectType(context, node));
}

std::optional<WDKeyPoint> PTParserPTPOS::build(Context& context, const WDNode& node)
{
    // 关键点编号
    int number = getNumber(context, node);
    if (number == -1)
        return std::nullopt;
    int index = 0;
    // 解析朝向
    DSLKeyPointResult retPosDir;
    if (!parse(index++, context, node, retPosDir))
        return std::nullopt;
    // 解析位置
    FVec3 retPosition = FVec3::Zero();
    if (!parse(index++, context, node, retPosition))
        return std::nullopt;
    // 结果关键点
    return WDKeyPoint(number
        , DVec3(retPosition) + DVec3(retPosDir.position)
        , DVec3(retPosDir.direction)
        , this->getBore(context, node)
        , this->getConnectType(context, node));
}

WD_NAMESPACE_END

