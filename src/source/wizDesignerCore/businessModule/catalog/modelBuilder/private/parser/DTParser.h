#pragma once

#include "../DSLCommon.h"
#include "../Context.h"
#include "../../../../typeMgr/WDBMTypeMgr.h"

WD_NAMESPACE_BEGIN

/**
 * @brief DATA解析
 */
class DATAParser
{
private:
    // 类型描述
    const WDBMTypeDesc* _pTypeDesc;
    // Number属性描述
    const WDBMAttrDesc* _pAttrDescNumber = nullptr;
    // Dkey属性描述
    const WDBMAttrDesc* _pAttrDescDkey = nullptr;
    // Purpose属性描述
    const WDBMAttrDesc* _pAttrDescPurpose = nullptr;
    // Dproperty属性描述
    const WDBMAttrDesc* _pAttrDescDPty = nullptr;
    // Pproperty属性描述
    const WDBMAttrDesc* _pAttrDescPPty = nullptr;
    // 空名称
    std::string _emptyName = "";
    // 错误信息
    std::string _lastErrStr = "";
public:
    /**
     * @brief  校验节点是否是否关键点节点
     */
    static inline bool IsDTNode(const WDNode& node) 
    {
        return node.isType("DATA");
    }
public:
    DATAParser(const WDBMTypeMgr& typeMgr, const std::string_view& type = "DATA");
    virtual ~DATAParser();
public:
    /**
     * @brief 获取编号
     * @return 返回-1时，表示无效
     */
    int number(Context& context, const WDNode& node);
    /**
     * @brief 获取DKey
     */
    std::string dKey(Context& context, const WDNode& node);
    /**
     * @brief 获取用途属性
     */
    std::string purpose(Context& context, const WDNode& node);
    /**
     * @brief 获取dProperty
     */
    std::optional<std::any> dProperty(Context& context, const WDNode& node);
    /**
     * @brief 获取pProperty
     */
    std::optional<std::any> pProperty(Context& context, const WDNode& node);
protected:
    bool parse(Context& context
        , const WDNode& node
        , const WDBMAttrDesc& aDesc
        , std::any& outValue);
};

WD_NAMESPACE_END

