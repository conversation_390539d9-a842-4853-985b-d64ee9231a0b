#include "GMParser.h"

WD_NAMESPACE_BEGIN


// 当给定的端面法向和拉伸方向的角度接近90度时，给定的法向不生效，误差值为 0.057296 (值是广群试出来的);
static constexpr double SEDrnErrorVal = 0.057296;

/**
* @brief 输出型几何体创建失败错误信息
*/
void GeomFaildLogPrint(Context& cxt
    , const WDNode& gmNode
    , const char* errStr = "创建型几何体失败，可能是参数不合法!")
{
    if (cxt.bSilence)
        return;

    char buf[1024] = { 0 };

    std::string outStr;
    outStr.reserve(1024);
    sprintf_s(buf, sizeof(buf), u8"注意:型节点:[%s]类型:[%s]%s"
        , gmNode.name().c_str()
        , gmNode.type().data()
        , errStr);
    outStr.append(buf);

    if (cxt.node() != nullptr)
    {
        sprintf_s(buf, sizeof(buf), u8"设计节点:[%s]类型:[%s]"
            , cxt.node()->name().c_str()
            , cxt.node()->type().data());

        outStr.append("-");
        outStr.append(buf);
    }
    if (cxt.scomNode() != nullptr)
    {
        sprintf_s(buf, sizeof(buf), u8"元件节点:[%s]类型:[%s]"
            , cxt.scomNode()->name().c_str()
            , cxt.scomNode()->type().data());
        outStr.append("-");
        outStr.append(buf);
    }
    LOG_WARN << outStr;
}

GMParserBase::GMParserBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
{
    _pTypeDesc = typeMgr.get(type);
    PARSE_ASSERT(_pTypeDesc != nullptr);
    std::fill(_attrs.begin(), _attrs.end(), nullptr);
    // 缓存部分通用属性
    if (_pTypeDesc != nullptr)
    {
        _pAttrDescLevel = _pTypeDesc->get("Level");
        _pAttrDescObstruction = _pTypeDesc->get("Obstruction");
        _pAttrDescTuflag = _pTypeDesc->get("Tuflag");
    }
}
GMParserBase::~GMParserBase()
{

}

WDBMLevelRange GMParserBase::levelRange(const WDNode& node) const
{
    assert(_pTypeDesc == node.getTypeDesc());

    if (_pAttrDescLevel == nullptr)
        return WDBMLevelRange(0, 0);
    auto ref = _pAttrDescLevel->valueCRef(node);
    auto pRet = ref.toLevelRange();
    if (pRet == nullptr)
        return WDBMLevelRange(0, 0);
    return (*pRet);
}
int GMParserBase::obstruction(const WDNode& node) const
{
    assert(_pTypeDesc == node.getTypeDesc());

    if (_pAttrDescObstruction == nullptr)
        return 0;
    auto ref = _pAttrDescObstruction->valueCRef(node);
    auto pRet = ref.toInt();
    if (pRet == nullptr)
        return 0;
    return *(pRet);
}
bool GMParserBase::tuFlag(const WDNode& node) const
{
    assert(_pTypeDesc == node.getTypeDesc());

    if (_pAttrDescTuflag == nullptr)
        return false;
    auto ref = _pAttrDescTuflag->valueCRef(node);
    auto pRet = ref.toBool();
    if (pRet == nullptr)
        return false;
    return *(pRet);
}

WDGeometry::GFlags GMParserBase::flags(const WDNode& node) const
{
    assert(_pTypeDesc == node.getTypeDesc());

    WDGeometry::GFlags rFlags;
    // 实体标记
    if (tuFlag(node))
        rFlags.addFlag(WDGeometry::GF_Entity);
    // 负实体标记
    if (this->isNegative())
        rFlags.addFlag(WDGeometry::GF_Negative);
    // 空间占有属性标记
    WDGeometry::AddObstructionFlags(rFlags, this->obstruction(node));

    return rFlags;
}

bool GMParserBase::check(const WDNode& node, bool bInsu) const
{
    assert(_pTypeDesc == node.getTypeDesc());

    // 校验实体标识，如果具有实体标识，则返回true
    bool tuFlag = this->tuFlag(node);
    if (tuFlag)
        return true;
    // 如果是生成保温模型，且没有实体标识，则返回false
    if (bInsu && !tuFlag)
        return false;
    // 校验空间占有属性，如果具有空间占有属性，则返回true
    int obstruction = this->obstruction(node);
    switch (obstruction)
    {
    case 1: // 软占有 Soft
        return true;
        break;
    case 2: // 硬占有 Hard
        return true;
        break;
    default:
        break;
    }
    return false;
}

bool GMParserBase::parse(size_t index, Context& context, const WDNode& node, float& outValue)
{
    auto pRValue = this->get(index, node);
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (rValue.empty() || IsUnsetValue(rValue))
    {
        outValue = 0.0f;
        return true;
    }
    if (!DSLExec(context.dsl, rValue, outValue, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, getName(index), rValue, _lastErrStr);
        return false;
    }
    return true;
}
bool GMParserBase::parse(size_t index, Context& context, const WDNode& node, FVec3& outPosition, FVec3& outDirection)
{
    auto pRValue = this->get(index, node);
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (IsUnsetValue(rValue))
        return false;
    DSLKeyPointResult ret;
    if (!DSLExecDirection(context.dsl, rValue, ret, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, getName(index), rValue, _lastErrStr);
        return false;
    }

    outPosition = ret.position;
    outDirection = ret.direction;

    return true;
}

WDGeometry::SharedPtr GMParserSBOXBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    WDUnused(lodSelection);

    size_t index = 0;
    // X轴向长度
    float xLength = 0.0f;
    if (!parse(index++, context, node, xLength))
        return nullptr;
    // Y轴向长度
    float yLength = 0.0f;
    if (!parse(index++, context, node, yLength))
        return nullptr;
    // Z轴向长度
    float zLength = 0.0f;
    if (!parse(index++, context, node, zLength))
        return nullptr;
    // X坐标
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return nullptr;
    // Y坐标
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return nullptr;
    // Z坐标
    float pz = 0.0f;
    if (!parse(index++, context, node, pz))
        return nullptr;

    FMeshTransform outTransform;
    float outXLength = 0.0f;
    float outYLength = 0.0f;
    float outZLength = 0.0f;
    if (!BoxBuilder::SBOXToBOX(Abs(xLength), Abs(yLength), Abs(zLength), px, py, pz
        , outTransform
        , outXLength
        , outYLength
        , outZLength))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createBox(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outXLength, outYLength, outZLength
        , flags
        , level);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);

    return pRGeom;
}

WDGeometry::SharedPtr GMParserSCONBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析 direction PAAX 方向
    FVec3 position;
    FVec3 direction;
    if (!parse(index++, context, node, position, direction))
        return nullptr;
    float distance = 0.0f;
    //解析 distance PDIS 到球心的距离
    if (!parse(index++, context, node, distance))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;

    FMeshTransform outTransform;
    float outTopDiameter = 0.0f;
    float outBottomDiameter = 0.0f;
    float outHeight = 0.0f;
    if (!ConeBuilder::SCONToCONE(direction, position, distance, diameter
        , outTransform
        , outTopDiameter
        , outBottomDiameter
        , outHeight))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createCone(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outTopDiameter
        , outBottomDiameter
        , outHeight
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserLCYLBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解轴方向和位置
    FVec3 position;
    FVec3 direction;
    if (!parse(index++, context, node, position, direction))
        return nullptr;
    //解析距离
    float bDistance = 0.0f;
    if (!parse(index++, context, node, bDistance))
        return nullptr;
    //解析高度
    float tDistance = 0.0f;
    if (!parse(index++, context, node, tDistance))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;

    FMeshTransform outTransform;
    float outDiameter = 0.0f;
    float outHeight = 0.0f;

    if (!CylinderBuilder::LCYLToCYLI(direction, position, bDistance, tDistance, diameter
        , outTransform
        , outDiameter
        , outHeight))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createCylinder(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outDiameter
        , outHeight
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSCYLBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解轴方向和位置
    FVec3 position;
    FVec3 direction;
    if (!parse(index++, context, node, position, direction))
        return nullptr;
    //解析距离
    float distance = 0.0f;
    if (!parse(index++, context, node, distance))
        return nullptr;
    //解析高度
    float height = 0.0f;
    if (!parse(index++, context, node, height))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;

    FMeshTransform outTransform;
    float outDiameter = 0.0f;
    float outHeight = 0.0f;
    if (!CylinderBuilder::SCYLToCYLI(direction, position, distance, height, diameter
        , outTransform
        , outDiameter
        , outHeight))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createCylinder(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outDiameter
        , outHeight
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSSLCBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解轴方向和位置
    FVec3 position;
    FVec3 direction;
    if (!parse(index++, context, node, position, direction))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;
    //解析高度
    float height = 0.0f;
    if (!parse(index++, context, node, height))
        return nullptr;
    //解析到原点的距离
    float distance = 0.0f;
    if (!parse(index++, context, node, distance))
        return nullptr;
    //解析顶面X倾斜
    float xTShear = 0.0f;
    if (!parse(index++, context, node, xTShear))
        return nullptr;
    //解析顶面Y倾斜
    float yTShear = 0.0f;
    if (!parse(index++, context, node, yTShear))
        return nullptr;
    //解析底面X倾斜
    float xBShear = 0.0f;
    if (!parse(index++, context, node, xBShear))
        return nullptr;
    //解析底面Y倾斜
    float yBShear = 0.0f;
    if (!parse(index++, context, node, yBShear))
        return nullptr;

    FMeshTransform outTransform;
    float outDiameter = 0.0f;
    float outHeight = 0.0f;
    float outTopXShear = 0.0f;
    float outTopYShear = 0.0f;
    float outBottomXShear = 0.0f;
    float outBottomYShear = 0.0f;
    if (!SlopedCylinderBuilder::SSLCToSLCY(direction, position, height, diameter, xTShear, yTShear, xBShear, yBShear, distance
        , outTransform
        , outDiameter
        , outHeight
        , outTopXShear
        , outTopYShear
        , outBottomXShear
        , outBottomYShear))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createSlopedCylinder(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outDiameter
        , outHeight
        , outTopXShear
        , outTopYShear
        , outBottomXShear
        , outBottomYShear
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSDSH::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解轴方向和位置
    FVec3 position;
    FVec3 direction;
    if (!parse(index++, context, node, position, direction))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;
    //解析高度
    float height = 0.0f;
    if (!parse(index++, context, node, height))
        return nullptr;
    //解析半径
    float radius = 0.0f;
    if (!parse(index++, context, node, radius))
        return nullptr;
    //解析偏移量
    float distance = 0.0f;
    if (!parse(index++, context, node, distance))
        return nullptr;

    FMeshTransform outTransform;
    float outDiameter = 0.0f;
    float outRadius = 0.0f;
    float outHeight = 0.0f;

    if (!DishBuilder::SDSHToDISH(direction, position, distance, height, radius, diameter
        , outTransform
        , outDiameter
        , outRadius
        , outHeight))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createDish(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outDiameter
        , outRadius
        , outHeight
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserLPYRBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    WDUnused(lodSelection);
    size_t index = 0;
    //解轴方向和位置
    FVec3 bPosition;
    FVec3 bAxis;
    if (!parse(index++, context, node, bPosition, bAxis))
        return nullptr;
    FVec3 cPosition;
    FVec3 cAxis;
    if (!parse(index++, context, node, cPosition, cAxis))
        return nullptr;
    FVec3 aPosition;
    FVec3 aAxis;
    if (!parse(index++, context, node, aPosition, aAxis))
        return nullptr;

    //解析 顶面X方向长度
    float btpLength = 0.0f;
    if (!parse(index++, context, node, btpLength))
        return nullptr;
    //解析 顶面Y方向长度
    float ctpLength = 0.0f;
    if (!parse(index++, context, node, ctpLength))
        return nullptr;
    //解析 底面X方向长度
    float bbtLength = 0.0f;
    if (!parse(index++, context, node, bbtLength))
        return nullptr;
    //解析 底面Y方向长度
    float cbtLength = 0.0f;
    if (!parse(index++, context, node, cbtLength))
        return nullptr;
    //解析 到顶面的距离
    float tDistance = 0.0f;
    if (!parse(index++, context, node, tDistance))
        return nullptr;
    //解析 到底面的距离
    float bDistance = 0.0f;
    if (!parse(index++, context, node, bDistance))
        return nullptr;
    //解析 X方向偏移量
    float bOffset = 0.0f;
    if (!parse(index++, context, node, bOffset))
        return nullptr;
    //解析 Y方向偏移量
    float cOffset = 0.0f;
    if (!parse(index++, context, node, cOffset))
        return nullptr;

    WDGeometry::SharedPtr pRGeom = nullptr;

    FMeshTransform outTransform;
    float outXTop = 0;
    float outYTop = 0;
    float outXBottom = 0;
    float outYBottom = 0;
    float outHeight = 0;
    float outXOffset = 0;
    float outYOffset = 0;
    if (!PyramidBuilder::LPYRToPYRA(
        bAxis, bPosition
        , cAxis, cPosition
        , aAxis, aPosition
        , btpLength, ctpLength
        , bbtLength, cbtLength
        , tDistance, bDistance
        , bOffset, cOffset
        , outTransform
        , outXTop
        , outYTop
        , outXBottom
        , outYBottom
        , outHeight
        , outXOffset
        , outYOffset))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    pRGeom = gmMgr.createPyramid(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outXTop
        , outYTop
        , outXBottom
        , outYBottom
        , outHeight
        , outXOffset
        , outYOffset
        , flags
        , level);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSCTOBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析aAxis
    FVec3 aPosition;
    FVec3 aDirection;
    if (!parse(index++, context, node, aPosition, aDirection))
        return nullptr;
    //解析bAxis
    FVec3 bPosition;
    FVec3 bDirection;
    if (!parse(index++, context, node, bPosition, bDirection))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;

    FMeshTransform outTransform;
    float outInsideRadius = 0.0f;
    float outOutsideRadius = 0.0f;
    float outAngle = 0.0f;

    if (!CircularTorusBuilder::SCTOToCTOR(aDirection, aPosition, bDirection, bPosition, diameter
        , outTransform
        , outInsideRadius
        , outOutsideRadius
        , outAngle))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createCircularTorus(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outInsideRadius
        , outOutsideRadius
        , outAngle
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSRTOBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析aAxis
    FVec3 aPosition;
    FVec3 aDirection;
    if (!parse(index++, context, node, aPosition, aDirection))
        return nullptr;
    //解析bAxis
    FVec3 bPosition;
    FVec3 bDirection;
    if (!parse(index++, context, node, bPosition, bDirection))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;
    //解析高度
    float height = 0.0f;
    if (!parse(index++, context, node, height))
        return nullptr;

    FMeshTransform outTransform;
    float outInsideRadius = 0.0f;
    float outOutsideRadius = 0.0f;
    float outHeight = 0.0f;
    float outAngle = 0.0f;

    if (!RectangularTorusBuilder::SRTOToRTOR(aDirection, aPosition, bDirection, bPosition, diameter, height
        , outTransform
        , outInsideRadius
        , outOutsideRadius
        , outHeight
        , outAngle))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createRectangularTorus(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outInsideRadius
        , outOutsideRadius
        , outHeight
        , outAngle
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserLSNOBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析aAxis
    FVec3 aPosition;
    FVec3 aDirection;
    if (!parse(index++, context, node, aPosition, aDirection))
        return nullptr;
    //解析bAxis
    FVec3 bPosition;
    FVec3 bDirection;
    if (!parse(index++, context, node, bPosition, bDirection))
        return nullptr;
    //解析原点到顶面的距离
    float tDistance = 0.0f;
    if (!parse(index++, context, node, tDistance))
        return nullptr;
    //解析原点到底面的距离
    float bDistance = 0.0f;
    if (!parse(index++, context, node, bDistance))
        return nullptr;
    //解析顶面直径
    float tDiameter = 0.0f;
    if (!parse(index++, context, node, tDiameter))
        return nullptr;
    //解析底面直径
    float bDiameter = 0.0f;
    if (!parse(index++, context, node, bDiameter))
        return nullptr;
    //解析偏移
    float offset = 0.0f;
    if (!parse(index++, context, node, offset))
        return nullptr;

    FMeshTransform outTransform;
    float outTopDiameter = 0.0f;
    float outBottomDiameter = 0.0f;
    float outHeight = 0.0f;
    float outXOffset = 0.0f;
    float outYOffset = 0.0f;

    if (!SnoutBuilder::LSNOToSNOU(aDirection, aPosition, bDirection, bPosition, tDistance, bDistance, tDiameter, bDiameter, offset
        , outTransform
        , outTopDiameter
        , outBottomDiameter
        , outHeight
        , outXOffset
        , outYOffset))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createSnout(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outTopDiameter
        , outBottomDiameter
        , outHeight
        , outXOffset
        , outYOffset
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSSPHBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析 direction PAAX 方向
    FVec3 position;
    FVec3 direction;
    if (!parse(index++, context, node, position, direction))
        return nullptr;
    //解析 distance PDIS 到球心的距离
    float distance = 0.0f;
    if (!parse(index++, context, node, distance))
        return nullptr;
    //解析直径
    float diameter = 0.0f;
    if (!parse(index++, context, node, diameter))
        return nullptr;

    FMeshTransform outTransform;
    float outDiameter = 0.0f;
    if (!SphereBuilder::SSPHToSPHE(direction, position, distance, diameter
        , outTransform
        , outDiameter))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createSphere(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outDiameter
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSEXTBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析 PAAX A轴方向
    FVec3 paaxis;
    FVec3 paaxisPos = FVec3::Zero();
    if (!parse(index++, context, node, paaxisPos, paaxis))
        return nullptr;
    //解析 PBAX B轴方向
    FVec3 pbaxis;
    FVec3 pbaxisPos = FVec3::Zero();
    if (!parse(index++, context, node, pbaxisPos, pbaxis))
        return nullptr;
    //解析 PHEI 拉伸的高度
    float pheight = 0.0f;
    if (!parse(index++, context, node, pheight))
        return nullptr;
    //解析 PX   X轴坐标
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return nullptr;
    //解析 PY   Y轴坐标
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return nullptr;
    //解析 PZ   Z轴坐标
    float pz = 0.0f;
    if (!parse(index++, context, node, pz))
        return nullptr;

    WDNode::SharedPtr pSLOONode = nullptr;
    // SEXT和SREV下可能有不止一个SLOO节点(只使用第一个SLOO节点下的顶点数据)
    for (auto pChild : node.children())
    {
        if (pChild == nullptr || !pChild->isType("SLOO"))
            continue;
        if (pChild->children().empty())
            continue;
        pSLOONode = pChild;
        break;
    }
    if (pSLOONode == nullptr)
    {
        GeomFaildLogPrint(context, node, "环节点不存在!");
        return nullptr;
    }

    ExtrusionBuilder::VertexDatas vertexDatas;
    vertexDatas.reserve(node.children().size());
    //解析SVER节点数据
    for (auto pSVERNode : pSLOONode->children())
    {
        if (pSVERNode == nullptr || !pSVERNode->isType("SVER"))
            continue;
        size_t indexSVER = 0;
        //解析 PX   X轴坐标
        float vertexPx = 0.0f;
        if (!parseSVER(indexSVER++, context, *pSVERNode, vertexPx))
            continue;
        //解析 PY   Y轴坐标
        float vertexPy = 0.0f;
        if (!parseSVER(indexSVER++, context, *pSVERNode, vertexPy))
            continue;
        //解析 PRAD 半径
        float vertexPrad = 0.0f;
        if (!parseSVER(indexSVER++, context, *pSVERNode, vertexPrad))
            continue;

        vertexDatas.push_back({});
        vertexDatas.back().first = FVec3(vertexPx, vertexPy, vertexPrad);
        vertexDatas.back().second = FVec3(0.0f, 0.0f, 0.0f);
    }
    
    if (vertexDatas.empty())
        return nullptr;

    FMeshTransform outTransform;
    float outHeight = 0.0f;
    FVec3 outDirection = FVec3::AxisZ();
    ExtrusionBuilder::Justification outJustification = ExtrusionBuilder::Justification::J_Bottom;
    FVec3Vector outLoop;
    if (!ExtrusionBuilder::SEXTToEXTR(paaxis, paaxisPos, pbaxis, Abs(pheight), px, py, pz, vertexDatas
        , outTransform
        , outHeight
        , outDirection
        , outJustification
        , outLoop))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createExtrusion(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outHeight
        , outDirection
        , outJustification
        , outLoop
        , flags
        , level
        , lodSelection);

    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

void GMParserSEXTBase::prepareSVER(const WDBMTypeMgr& typeMgr)
{
    auto pSVERTypeDesc = typeMgr.get("SVER");
    assert(pSVERTypeDesc != nullptr);
    if (pSVERTypeDesc != nullptr)
    {
        size_t index = 0;
        _attrsSVER[index++] = pSVERTypeDesc->get("Px");
        _attrsSVER[index++] = pSVERTypeDesc->get("Py");
        _attrsSVER[index++] = pSVERTypeDesc->get("Pradius");
    }
}

bool GMParserSEXTBase::parseSVER(size_t index, Context& context, const WDNode& nodeSVER, float& outValue)
{
    assert(index < _attrsSVER.size() && "数组越界!");
    if (_attrsSVER[index] == nullptr)
        return false;

    std::string rValue = "";
    if (!_attrsSVER[index]->value(nodeSVER).toString(rValue))
        return false;

    if (!DSLExec(context.dsl, rValue, outValue, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, nodeSVER, getSVERName(index), rValue, _lastErrStr);
        return false;
    }

    return true;
}

WDGeometry::SharedPtr GMParserSREVBase::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析 PAAX A轴方向
    FVec3 paaxis;
    FVec3 paaxisPos = FVec3::Zero();
    if (!parse(index++, context, node, paaxisPos, paaxis))
        return nullptr;
    //解析 PBAX B轴方向
    FVec3 pbaxis;
    FVec3 pbaxisPos = FVec3::Zero();
    if (!parse(index++, context, node, pbaxisPos, pbaxis))
        return nullptr;
    //解析 PANG 旋转角度
    float pang = 0.0f;
    if (!parse(index++, context, node, pang))
        return nullptr;
    //解析 PX   X轴坐标
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return nullptr;
    //解析 PY   Y轴坐标
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return nullptr;
    //解析 PZ   Z轴坐标
    float pz = 0.0f;
    if (!parse(index++, context, node, pz))
        return nullptr;

    WDNode::SharedPtr pSLOONode = nullptr;
    // SEXT和SREV下可能有不止一个SLOO节点(只使用第一个SLOO节点下的顶点数据)
    for (auto pChild : node.children())
    {
        if (pChild == nullptr || !pChild->isType("SLOO"))
            continue;
        if (pChild->children().empty())
            continue;
        pSLOONode = pChild;
    }
    if (pSLOONode == nullptr) 
    {
        GeomFaildLogPrint(context, node, "环节点不存在!");
        return nullptr;
    }

    RevolutionBuilder::VertexDatas vertexDatas;
    vertexDatas.reserve(node.children().size());
    //解析SVER节点数据
    for (auto pSVERNode : pSLOONode->children())
    {
        if (pSVERNode == nullptr || !pSVERNode->isType("SVER"))
            continue;

        size_t indexSVER = 0;
        //解析 PX   X轴坐标
        float vertexPx = 0.0f;
        if (!parseSVER(indexSVER++, context, *pSVERNode, vertexPx))
            continue;
        //解析 PY   Y轴坐标
        float vertexPy = 0.0f;
        if (!parseSVER(indexSVER++, context, *pSVERNode, vertexPy))
            continue;
        //解析 PRAD 半径
        float vertexPrad = 0.0f;
        if (!parseSVER(indexSVER++, context, *pSVERNode, vertexPrad))
            continue;

        vertexDatas.push_back({});
        vertexDatas.back() = FVec3(vertexPx, vertexPy, vertexPrad);
    }
    if (vertexDatas.empty())
        return nullptr;

    FMeshTransform outTransform;
    FVec3 outCenter = FVec3::Zero();
    FVec3 outAxis = FVec3::AxisZ();
    float outAngle = 0.0f;
    FVec3Vector outLoop;
    if (!RevolutionBuilder::SREVToREVO(paaxis, paaxisPos, pbaxis, pang, px, py, pz, vertexDatas
        , outTransform
        , outCenter
        , outAxis
        , outAngle
        , outLoop))
    {
        GeomFaildLogPrint(context, node);
        return nullptr;
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createRevolution(DVec3(outTransform.translation)
        , DQuat(outTransform.rotation)
        , outCenter
        , outAxis
        , outAngle
        , outLoop
        , flags
        , level
        , lodSelection);
    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

void GMParserSREVBase::prepareSVER(const WDBMTypeMgr& typeMgr)
{
    auto pSVERTypeDesc = typeMgr.get("SVER");
    assert(pSVERTypeDesc != nullptr);
    if (pSVERTypeDesc != nullptr)
    {
        size_t index = 0;
        _attrsSVER[index++] = pSVERTypeDesc->get("Px");
        _attrsSVER[index++] = pSVERTypeDesc->get("Py");
        _attrsSVER[index++] = pSVERTypeDesc->get("Pradius");
    }
}

bool GMParserSREVBase::parseSVER(size_t index, Context& context, const WDNode& nodeSVER, float& outValue)
{
    assert(index < _attrsSVER.size() && "数组越界!");
    if (_attrsSVER[index] == nullptr)
        return false;

    std::string rValue = "";
    if (!_attrsSVER[index]->value(nodeSVER).toString(rValue))
        return false;

    if (!DSLExec(context.dsl, rValue, outValue, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, nodeSVER, getSVERName(index), rValue, _lastErrStr);
        return false;
    }

    return true;
}

WDGeometry::SharedPtr GMParserSREC::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析 Px X坐标
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return nullptr;
    //解析 Py Y坐标
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return nullptr;
    //解析 Pxlength X长度
    float pxLength = 0.0f;
    if (!parse(index++, context, node, pxLength))
        return nullptr;
    //解析 Pylength Y长度
    float pyLength = 0.0f;
    if (!parse(index++, context, node, pyLength))
        return nullptr;
    //解析 Plaxis 方向角
    FVec3 plaxis = FVec3::AxisY();
    FVec3 plaxisPos = FVec3::Zero();
    if (!parse(index++, context, node, plaxisPos, plaxis))
        return nullptr;
    //解析 Dx X偏移量
    float dx = 0.0f;
    if (!parse(index++, context, node, dx))
        return nullptr;
    //解析 Dy Y偏移量
    float dy = 0.0f;
    if (!parse(index++, context, node, dy))
        return nullptr;
    //解析 Dxl X长度增量
    float dxl = 0.0f;
    if (!parse(index++, context, node, dxl))
        return nullptr;
    //解析 Dyl Y长度增量
    float dyl = 0.0f;
    if (!parse(index++, context, node, dyl))
        return nullptr;

    // 准备计算所需的参数
    const auto& rParam = context.splineParams();
    FVec3Vector splineVs = rParam.spline.vertices(lodSelection);
    if (splineVs.size() < 2)
    {
        GeomFaildLogPrint(context, node, "创建型几何体失败,spline顶点个数小于2!");
        return nullptr;
    }

    // y轴方向
    FVec3 tAxisY = plaxis;
    // 计算x轴方向
    FVec3 tAxisX = FVec3::Cross(plaxis, FVec3::AxisZ()).normalized();

    FVec2 justPt = FVec2(context.justPoint);
    FMat3 rMat = FMat3::MakeRotationZ(rParam.bAngle);
    // 起始端面矩形尺寸
    const FVec2 sizeS = FVec2(pxLength, pyLength);
    const FVec2 halfSizeS = sizeS * 0.5f;
    // 计算矩形的起始端面四个顶点
    FVec2Array<4> vsS;
    vsS[0] = FVec2(-halfSizeS.x, -halfSizeS.y);
    vsS[1] = FVec2(halfSizeS.x, -halfSizeS.y);
    vsS[2] = FVec2(halfSizeS.x, halfSizeS.y);
    vsS[3] = FVec2(-halfSizeS.x, halfSizeS.y);
    FVec3Vector loopS;
    loopS.reserve(vsS.size());
    for (const auto& v : vsS)
    {
        FVec2 posX = tAxisX.xy() * (px + v.x - justPt.x);
        FVec2 posY = tAxisY.xy() * (py + v.y - justPt.y);

        loopS.push_back(rMat * FVec3(posX + posY));
    }
    // 终止端面矩形尺寸
    const FVec2 sizeE = FVec2(pxLength + dxl, pyLength + dyl);
    const FVec2 halfSizeE = sizeE * 0.5f;
    // 计算矩形的终止端面四个顶点
    FVec2Array<4> vsE;
    vsE[0] = FVec2(-halfSizeE.x + dx, -halfSizeE.y + dy);
    vsE[1] = FVec2(halfSizeE.x + dx, -halfSizeE.y + dy);
    vsE[2] = FVec2(halfSizeE.x + dx, halfSizeE.y + dy);
    vsE[3] = FVec2(-halfSizeE.x + dx, halfSizeE.y + dy);

    FVec3Vector loopE;
    loopE.reserve(vsE.size());
    for (const auto& v : vsE)
    {
        FVec2 posX = tAxisX.xy() * (px + v.x - justPt.x);
        FVec2 posY = tAxisY.xy() * (py + v.y - justPt.y);

        loopE.push_back(rMat * FVec3(posX + posY));
    }

    if (loopS.empty() || loopE.empty())
        return nullptr;

    // 起始端面法线
    FVec3 sDir = FVec3::Normalize(splineVs[0] - splineVs[1]);
    if (rParam.drnS)
    {
        // 当给定的端面法向和拉伸方向的角度接近90度时，给定的法向不生效，临界值为 90 - 0.057296 (值是广群试出来的)
        if (FVec3::Angle(sDir, rParam.drnS.value()) < (90 - SEDrnErrorVal))
            sDir = rParam.drnS.value();
    }
    // 结束端面法线
    FVec3 eDir = FVec3::Normalize(splineVs[splineVs.size() - 1] - splineVs[splineVs.size() - 2]);
    if (rParam.drnE)
    {
        // 当给定的端面法向和拉伸方向的角度接近90度时，给定的法向不生效，临界值为 90 - 0.057296 (值是广群试出来的)
        if (FVec3::Angle(eDir, rParam.drnE.value()) < (90 - SEDrnErrorVal))
            eDir = rParam.drnE.value();
    }

    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createLofting(sDir
        , eDir
        , plaxisPos
        , loopS
        , loopE
        , splineVs
        , flags
        , level
        , lodSelection);
    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSANN::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    size_t index = 0;
    //解析 Px X坐标
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return nullptr;
    //解析 Py Y坐标
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return nullptr;
    //解析 Plaxis 方向角
    FVec3 plaxis = FVec3::AxisY();
    FVec3 plaxisPos = FVec3::Zero();
    if (!parse(index++, context, node, plaxisPos, plaxis))
        return nullptr;
    //解析 Pangle 对向角
    float pangle = 0.0f;
    if (!parse(index++, context, node, pangle))
        return nullptr;
    //解析 Pradius 半径
    float pradius = 0.0f;
    if (!parse(index++, context, node, pradius))
        return nullptr;
    //解析 Pwidth 宽度
    float pwidth = 0.0f;
    if (!parse(index++, context, node, pwidth))
        return nullptr;
    //解析 Dx X偏移量
    float dx = 0.0f;
    if (!parse(index++, context, node, dx))
        return nullptr;
    //解析 Dy Y偏移量
    float dy = 0.0f;
    if (!parse(index++, context, node, dy))
        return nullptr;
    //解析 Dradius 半径偏移量
    float dradius = 0.0f;
    if (!parse(index++, context, node, dradius))
        return nullptr;
    //解析 Dwidth 宽度偏移量
    float dwidth = 0.0f;
    if (!parse(index++, context, node, dwidth))
        return nullptr;

    // 准备计算所需的参数
    const auto& rParam = context.splineParams();
    FVec3Vector splineVs = rParam.spline.vertices(lodSelection);
    if (splineVs.size() < 2)
    {
        GeomFaildLogPrint(context, node, "创建型几何体失败,spline顶点个数小于2!");
        return nullptr;
    }

    // 外环的起点到圆心的向量
    FVec2 outSPt = FVec2(0.0f, 1.0f) * pradius;
    // 内环的起点到圆心的向量
    FVec2 inSPt = FVec2(0.0f, 1.0f) * (pradius - pwidth);
    // 角度规格化到 [-360, 360]范围
    pangle = Clamp(pangle, -360.0f, 360.0f);
    // 获取分段数
    uint seg = lodSelection.getSegment(pangle, pradius);
    assert(seg > 0);
    float segF = static_cast<float>(seg);

    // 顶点列表, 顶点个为 (分段数 + 1) * 2
    FVec2Vector vs((seg + 1) * 2);

    // 方向角
    auto rAngle = WD::FVec3::Angle(plaxis, WD::FVec3::AxisX());
    if (!WD::FVec3::InTheSameDirection(WD::FVec3::Cross(WD::FVec3::AxisX(), plaxis).normalized(), WD::FVec3::AxisZ()))
        rAngle = 360 - rAngle;

    // 旋转法生成圆弧
    for (uint i = 0; i <= seg; ++i)
    {
        float iF = static_cast<float>(i);
        float tAngle = iF / segF * pangle;
        FMat3 rotMat = FMat3::MakeRotationZ(tAngle + rAngle);
        // 外环顶点, 绕Z轴逆时针
        vs[i] = (rotMat * FVec3(outSPt)).xy();
        // 内环顶点, 绕Z轴顺时针
        vs[vs.size() - 1 - i] = (rotMat * FVec3(inSPt)).xy();
    };

    FVec3Vector loop;
    loop.reserve(node.childCount());
    FVec2 justPt = FVec2(context.justPoint);
    FMat3 rMat = FMat3::MakeRotationZ(rParam.bAngle);
    for (const auto& v : vs)
    {
        FVec2 posX = WD::FVec2::AxisX() * (px + v.x - justPt.x);
        FVec2 posY = WD::FVec2::AxisY() * (py + v.y - justPt.y);

        loop.push_back(rMat * FVec3(posX + posY));
    }

    if (loop.empty())
        return nullptr;

    // 起始端面法线
    FVec3 sDir = FVec3::Normalize(splineVs[0] - splineVs[1]);
    if (rParam.drnS)
    {
        // 当给定的端面法向和拉伸方向的角度接近90度时，给定的法向不生效，临界值为 90 - 0.057296 (值是广群试出来的)
        if (FVec3::Angle(sDir, rParam.drnS.value()) < (90 - SEDrnErrorVal))
            sDir = rParam.drnS.value();
    }
    // 结束端面法线
    FVec3 eDir = FVec3::Normalize(splineVs[splineVs.size() - 1] - splineVs[splineVs.size() - 2]);
    if (rParam.drnE)
    {
        // 当给定的端面法向和拉伸方向的角度接近90度时，给定的法向不生效，临界值为 90 - 0.057296 (值是广群试出来的)
        if (FVec3::Angle(eDir, rParam.drnE.value()) < (90 - SEDrnErrorVal))
            eDir = rParam.drnE.value();
    }
    // 这里会给几何体设置位置,因此不能用共享方式创建
    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createLofting(sDir
        , eDir
        , plaxisPos
        , loop
        , loop
        , splineVs
        , flags
        , level
        , lodSelection);
    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

WDGeometry::SharedPtr GMParserSPRO::build(Context& context, const WDNode& node, const MeshLODSelection& lodSelection)
{
    // 子节点不足三个，肯定不能构成环
    if (node.childCount() < 3)
        return nullptr;

    size_t index = 0;
    // 解析Plaxis
    FVec3 plaxis = FVec3::AxisY();
    FVec3 plaxisPos = FVec3::Zero();
    if (!parse(index++, context, node, plaxisPos, plaxis))
        return nullptr; 

    // 准备计算所需的参数
    const auto& rParam = context.splineParams();
    FVec3Vector splineVs = rParam.spline.vertices(lodSelection);
    if (splineVs.size() < 2)
    {
        GeomFaildLogPrint(context, node, "创建型几何体失败,spline顶点个数小于2!");
        return nullptr;
    }

    FVec3 tAxisY = plaxis;
    FVec3 tAxisX = FVec3::Cross(plaxis, FVec3::AxisZ()).normalized();

    // 遍历下方顶点节点，获取拉伸面顶点数据
    FVec3Vector loop;
    loop.reserve(node.childCount());
    FVec2 justPt = FVec2(context.justPoint);
    FMat3 rMat = FMat3::MakeRotationZ(rParam.bAngle);
    std::vector<float> dls;
    for (auto pSPVENode : node.children())
    {
        if (pSPVENode == nullptr || !pSPVENode->isType("SPVE"))
            continue;
        size_t indexSPVE = 0;
        // 解析Px
        float px = 0.0f;
        if (!parseSPVE(indexSPVE++, context, *pSPVENode, px))
            return nullptr;
        // 解析Py
        float py = 0.0f;
        if (!parseSPVE(indexSPVE++, context, *pSPVENode, py))
            return nullptr;
        // 解析半径
        float pradius = 0.0f;
        if (!parseSPVE(indexSPVE++, context, *pSPVENode, pradius))
            return nullptr;
        // 解析Dx
        float dx = 0.0f;
        if (!parseSPVE(indexSPVE++, context, *pSPVENode, dx))
            return nullptr;
        dls.push_back(dx);
        // 解析Dy
        float dy = 0.0f;
        if (!parseSPVE(indexSPVE++, context, *pSPVENode, dy))
            return nullptr;
        dls.push_back(dy);
        // 解析半径偏移
        float drad = 0.0f;
        if (!parseSPVE(indexSPVE++, context, *pSPVENode, drad))
            return nullptr;
        dls.push_back(drad);

        WDUnused(dx);
        WDUnused(dy);
        WDUnused(drad);

        FVec2 posX = tAxisX.xy() * (px - justPt.x);
        FVec2 posY = tAxisY.xy() * (py - justPt.y);

        loop.push_back(rMat * FVec3(posX + posY));
        loop.back().z = pradius;
    }

    if (loop.empty())
        return nullptr;
    // 起始端面法线
    FVec3 sDir = FVec3::Normalize(splineVs[0] - splineVs[1]);
    if (rParam.drnS)
    {
        // 当给定的端面法向和拉伸方向的角度接近90度时，给定的法向不生效，临界值为 90 - 0.057296 (值是广群试出来的)
        if (FVec3::Angle(sDir, rParam.drnS.value()) < (90 - SEDrnErrorVal))
            sDir = rParam.drnS.value();
    }
    // 结束端面法线
    FVec3 eDir = FVec3::Normalize(splineVs[splineVs.size() - 1] - splineVs[splineVs.size() - 2]);
    if (rParam.drnE)
    {
        // 当给定的端面法向和拉伸方向的角度接近90度时，给定的法向不生效，临界值为 90 - 0.057296 (值是广群试出来的)
        if (FVec3::Angle(eDir, rParam.drnE.value()) < (90 - SEDrnErrorVal))
            eDir = rParam.drnE.value();
    }
    auto& gmMgr = context.core().geometryMgr();
    // 标志
    auto flags = this->flags(node);
    if (context.bInsu())
        flags.addFlag(WDGeometry::GF_Insu);
    // 层级
    auto level = this->levelRange(node).minMax();
    // 创建
    auto pRGeom = gmMgr.createLofting(sDir
        , eDir
        , plaxisPos
        , loop
        , loop
        , splineVs
        , flags
        , level
        , lodSelection);
    if (pRGeom == nullptr)
        GeomFaildLogPrint(context, node);
    return pRGeom;
}

void GMParserSPRO::prepareSPVE(const WDBMTypeMgr& typeMgr)
{
    auto pSPVETypeDesc = typeMgr.get("SPVE");
    assert(pSPVETypeDesc != nullptr);
    if (pSPVETypeDesc != nullptr)
    {
        size_t index = 0;
        _attrsSPVE[index++] = pSPVETypeDesc->get("Px");
        _attrsSPVE[index++] = pSPVETypeDesc->get("Py");
        _attrsSPVE[index++] = pSPVETypeDesc->get("Pradius");
        _attrsSPVE[index++] = pSPVETypeDesc->get("Dx");
        _attrsSPVE[index++] = pSPVETypeDesc->get("Dy");
        _attrsSPVE[index++] = pSPVETypeDesc->get("Dradius");
    }
}

bool GMParserSPRO::parseSPVE(size_t index, Context& context, const WDNode& nodeSPVE, float& outValue) const
{
    assert(index < _attrsSPVE.size() && "数组越界!");
    if (_attrsSPVE[index] == nullptr)
        return false;

    std::string rValue = "";
    if (!_attrsSPVE[index]->value(nodeSPVE).toString(rValue))
        return false;

    std::string errorStr;
    if (!DSLExec(context.dsl, rValue, outValue, errorStr))
    {
        ParseAttriFaildLogPrint(context, nodeSPVE, getSPVEName(index), rValue, errorStr);
        return false;
    }

    return true;
}

WD_NAMESPACE_END

