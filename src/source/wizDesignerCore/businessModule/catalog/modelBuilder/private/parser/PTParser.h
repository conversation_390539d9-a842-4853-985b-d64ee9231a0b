#pragma once

#include "../DSLCommon.h"
#include "../Context.h"
#include "../../../../typeMgr/WDBMTypeMgr.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 关键点解析基类
 */
class PTParserBase 
{
private:
    // 类型描述
    const WDBMTypeDesc* _pTypeDesc;
    // 用于生成关键点数据的参数描述
    std::array<const WDBMAttrDesc*, 50> _attrs;
    // 关键点Number属性描述
    const WDBMAttrDesc* _pAttrDescNumber = nullptr;
    // 关键点Bore属性描述
    const WDBMAttrDesc* _pAttrDescBore = nullptr;
    // 关键点ConnectType属性描述
    const WDBMAttrDesc* _pAttrDescCType = nullptr;
    // 空名称
    std::string _emptyName = "";
    // 错误信息
    std::string _lastErrStr = "";
public:
    /**
     * @brief  校验节点是否是否关键点节点
     */
    static inline bool IsPTNode(const WDNode& node) 
    {
        return node.isAnyOfType("PTAX", "PTCA", "PTMI", "PTPOS");
    }
public:
    PTParserBase(const WDBMTypeMgr& typeMgr, const std::string_view& type);
    virtual ~PTParserBase();
public:
    /**
     * @brief 生成关键点
     * @param context 
     * @param node 
     * @return 
     */
    virtual std::optional<WDKeyPoint> build(Context& context, const WDNode& node) = 0;
public:
    /**
     * @brief 获取关键点编号
     * @return 关键点编号，无效时返回-1
     */
    int getNumber(Context& context, const WDNode& node);
    /**
     * @brief 获取公称直径
     */
    std::string getBore(Context& context, const WDNode& node);
    /**
     * @brief 获取连接形式
     */
    std::string getConnectType(Context& context, const WDNode& node);
protected:
    bool put(size_t index, const std::string_view& name);
    bool parse(size_t index
        , Context& context
        , const WDNode& node
        , float& outValue);
    bool parse(size_t index
        , Context& context
        , const WDNode& node
        , DSLKeyPointResult& outRet);
    bool parse(size_t index
        , Context& context
        , const WDNode& node
        , FVec3& outRet);
private:
    inline const std::string& getName(size_t index) const
    {
        assert(index < _attrs.size() && "数组越界!");
        if (_attrs[index] == nullptr)
            return _emptyName;
        return _attrs[index]->name();
    }
    inline const std::string* get(size_t index, const WDNode& node) const
    {
        assert(index < _attrs.size() && "数组越界!");
        if (_attrs[index] == nullptr)
            return nullptr;
        auto vCRef = _attrs[index]->valueCRef(node);
        return vCRef.toString();
    }
};
using PTParsers = std::vector<PTParserBase*>;

class PTParserPTAX : public PTParserBase
{
public:
    PTParserPTAX(const WDBMTypeMgr& typeMgr)
        : PTParserBase(typeMgr, "PTAX")
    {
        size_t index = 0;
        put(index++, "Paxis");
        put(index++, "Pdistance");
    }
public:
    virtual std::optional<WDKeyPoint> build(Context& context
        , const WDNode& node) override;
};

class PTParserPTCA : public PTParserBase
{
public:
    PTParserPTCA(const WDBMTypeMgr& typeMgr)
        : PTParserBase(typeMgr, "PTCA")
    {
        size_t index = 0;
        put(index++, "Ptcdirection");
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Pz");
    }
public:
    virtual std::optional<WDKeyPoint> build(Context& context
        , const WDNode& node) override;
};

class PTParserPTMI : public PTParserBase
{
public:
    PTParserPTMI(const WDBMTypeMgr& typeMgr)
        : PTParserBase(typeMgr, "PTMI")
    {
        size_t index = 0;
        put(index++, "Paxis");
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Pz");
    }
public:
    virtual std::optional<WDKeyPoint> build(Context& context
        , const WDNode& node) override;
};

class PTParserPTPOS : public PTParserBase
{
public:
    PTParserPTPOS(const WDBMTypeMgr& typeMgr)
        : PTParserBase(typeMgr, "PTPOS")
    {
        size_t index = 0;
        put(index++, "Ptcdirection");
        put(index++, "Ptcposition");
    }
public:
    virtual std::optional<WDKeyPoint> build(Context& context
        , const WDNode& node) override;
};


WD_NAMESPACE_END

