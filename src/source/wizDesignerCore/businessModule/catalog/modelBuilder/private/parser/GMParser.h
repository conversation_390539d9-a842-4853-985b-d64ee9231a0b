#pragma once

#include "../Context.h"
#include "../DSLCommon.h"
#include "../../../../../geometry/WDGeometryMgr.h"
#include "../../../../typeMgr/WDBMTypeMgr.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 型解析基类
 */
class GMParserBase 
{
private:
    // 类型描述
    const WDBMTypeDesc* _pTypeDesc;
    // 用于生成几何体的参数描述
    std::array<const WDBMAttrDesc*, 50> _attrs;
    // 图层范围属性描述 "Level"
    const WDBMAttrDesc* _pAttrDescLevel;
    // 空间占有属性描述 "Obstruction"
    const WDBMAttrDesc* _pAttrDescObstruction;
    // 实体标识属性 "Tuflag"
    const WDBMAttrDesc* _pAttrDescTuflag;
protected:
    // 空名称
    std::string _emptyName = "";
    // 错误信息
    std::string _lastErrStr = "";
public:
    /**
     * @brief 根据节点类型校验是否负型类型
     */
    static inline bool IsNGMNode(const WDNode& gmNode)
    {
        return gmNode.isAnyOfType("NSBO", "NLSN", "NSCO", "NSSP"
            , "NLCY", "NSCY", "NSSL", "NSCT"
            , "NSRT", "NLPY", "NSDS", "NSEX"
            , "NSRE");
    }
public:
    GMParserBase(const WDBMTypeMgr& typeMgr, const std::string_view& type);
    virtual ~GMParserBase();
public:
    /**
     * @brief 获取图层范围
     */
    WDBMLevelRange levelRange(const WDNode& node) const;
    /**
     * @brief 获取空间占有属性
     */
    int obstruction(const WDNode& node) const;
    /**
     * @brief 获取实体标志
     */
    bool tuFlag(const WDNode& node) const;
    /**
     * @brief 获取根据型节点，获取生成的几何体的标志
     */
    WDGeometry::GFlags flags(const WDNode& node) const;
    /**
     * @brief 校验型对象是否满足生成几何体
     */
    bool check(const WDNode& node, bool bInsu) const;
public:
    /**
     * @brief 生成几何体
     * @param context 
     * @param node 
     * @param bInsu 保温标记
     * @param lodSelection 
     * @return 
     */
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) = 0;
    /**
     * @brief 是否负实体类型
     */
    virtual bool isNegative() const 
    {
        return false;
    }
protected:
    inline bool put(size_t index, const std::string_view& name)
    {
        if (_pTypeDesc == nullptr)
            return false;
        assert(index < _attrs.size() && "数组越界!");
        _attrs[index] = _pTypeDesc->get(name);
        assert(_attrs[index] != nullptr);
        return _attrs[index] != nullptr;
    }
    bool parse(size_t index
        , Context& context
        , const WDNode& node
        , float& outValue);
    bool parse(size_t index
        , Context& context
        , const WDNode& node
        , FVec3& outPosition
        , FVec3& outDirection);
private:
    inline const std::string& getName(size_t index) const
    {
        assert(index < _attrs.size() && "数组越界!");
        if (_attrs[index] == nullptr)
            return _emptyName;
        return _attrs[index]->name();
    }
    inline const std::string* get(size_t index, const WDNode& node) const
    {
        assert(index < _attrs.size() && "数组越界!");
        if (_attrs[index] == nullptr)
            return nullptr;
        auto vCRef = _attrs[index]->valueCRef(node);
        return vCRef.toString();
    }
};
using GMParsers = std::vector<GMParserBase*>;


class GMParserSBOXBase: public GMParserBase
{
public:
    GMParserSBOXBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Pxlength");
        put(index++, "Pylength");
        put(index++, "Pzlength");
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Pz");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserSBOX : public GMParserSBOXBase
{
public:
    GMParserSBOX(const WDBMTypeMgr& typeMgr)
        : GMParserSBOXBase(typeMgr, "SBOX") {}
};
class GMParserNSBO : public GMParserSBOXBase
{
public:
    GMParserNSBO(const WDBMTypeMgr& typeMgr)
        : GMParserSBOXBase(typeMgr, "NSBO") {}
public:
    virtual bool isNegative() const override 
    {
        return true;
    }
};

class GMParserSCONBase : public GMParserBase
{
public:
    GMParserSCONBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paxis");
        put(index++, "Pdistance");
        put(index++, "Pdiameter");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserSCON : public GMParserSCONBase
{
public:
    GMParserSCON(const WDBMTypeMgr& typeMgr)
        : GMParserSCONBase(typeMgr, "SCON")
    {
    }
};
class GMParserNSCO : public GMParserSCONBase
{
public:
    GMParserNSCO(const WDBMTypeMgr& typeMgr)
        : GMParserSCONBase(typeMgr, "NSCO")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserLCYLBase: public GMParserBase
{
public:
    GMParserLCYLBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type) 
    {
        size_t index = 0;
        put(index++, "Paxis");
        put(index++, "Pbdistance");
        put(index++, "Ptdistance");
        put(index++, "Pdiameter");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserLCYL : public GMParserLCYLBase
{
public:
    GMParserLCYL(const WDBMTypeMgr& typeMgr)
        : GMParserLCYLBase(typeMgr, "LCYL")
    {
    }
};
class GMParserNLCY : public GMParserLCYLBase
{
public:
    GMParserNLCY(const WDBMTypeMgr& typeMgr)
        : GMParserLCYLBase(typeMgr, "NLCY")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSCYLBase : public GMParserBase
{
public:
    GMParserSCYLBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paxis");
        put(index++, "Pdistance");
        put(index++, "Pheight");
        put(index++, "Pdiameter");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserSCYL: public GMParserSCYLBase
{
public:
    GMParserSCYL(const WDBMTypeMgr& typeMgr)
        : GMParserSCYLBase(typeMgr, "SCYL")
    {
    }
};
class GMParserNSCY: public GMParserSCYLBase
{
public:
    GMParserNSCY(const WDBMTypeMgr& typeMgr)
        : GMParserSCYLBase(typeMgr, "NSCY")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSSLCBase : public GMParserBase
{
public:
    GMParserSSLCBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paxis");
        put(index++, "Pdiameter");
        put(index++, "Pheight");
        put(index++, "Pdistance");
        put(index++, "Pxtshear");
        put(index++, "Pytshear");
        put(index++, "Pxbshear");
        put(index++, "Pybshear");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserSSLC: public GMParserSSLCBase
{
public:
    GMParserSSLC(const WDBMTypeMgr& typeMgr)
        : GMParserSSLCBase(typeMgr, "SSLC")
    {
    }
};
class GMParserNSSL: public GMParserSSLCBase
{
public:
    GMParserNSSL(const WDBMTypeMgr& typeMgr)
        : GMParserSSLCBase(typeMgr, "NSSL")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSDSH: public GMParserBase
{
public:
    GMParserSDSH(const WDBMTypeMgr& typeMgr)
        : GMParserBase(typeMgr, "SDSH")
    {
        size_t index = 0;
        put(index++, "Paxis");
        put(index++, "Pdiameter");
        put(index++, "Pheight");
        put(index++, "Pradius");
        put(index++, "Pdistance");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};

class GMParserLPYRBase : public GMParserBase
{
public:
    GMParserLPYRBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Pbaxis");
        put(index++, "Pcaxis");
        put(index++, "Paaxis");
        put(index++, "Pbtplength");
        put(index++, "Pctplength");
        put(index++, "Pbbtlength");
        put(index++, "Pcbtlength");
        put(index++, "Ptdistance");
        put(index++, "Pbdistance");
        put(index++, "Pboffset");
        put(index++, "Pcoffset");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserLPYR: public GMParserLPYRBase
{
public:
    GMParserLPYR(const WDBMTypeMgr& typeMgr)
        : GMParserLPYRBase(typeMgr, "LPYR")
    {
    }
};
class GMParserNLPY: public GMParserLPYRBase
{
public:
    GMParserNLPY(const WDBMTypeMgr& typeMgr)
        : GMParserLPYRBase(typeMgr, "NLPY")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSCTOBase : public GMParserBase
{
public:
    GMParserSCTOBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paaxis");
        put(index++, "Pbaxis");
        put(index++, "Pdiameter");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserSCTO: public GMParserSCTOBase
{
public:
    GMParserSCTO(const WDBMTypeMgr& typeMgr)
        : GMParserSCTOBase(typeMgr, "SCTO")
    {
    }
};
class GMParserNSCT: public GMParserSCTOBase
{
public:
    GMParserNSCT(const WDBMTypeMgr& typeMgr)
        : GMParserSCTOBase(typeMgr, "NSCT")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSRTOBase : public GMParserBase
{
public:
    GMParserSRTOBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paaxis");
        put(index++, "Pbaxis");
        put(index++, "Pdiameter");
        put(index++, "Pheight");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserSRTO: public GMParserSRTOBase
{
public:
    GMParserSRTO(const WDBMTypeMgr& typeMgr)
        : GMParserSRTOBase(typeMgr, "SRTO")
    {
    }
};
class GMParserNSRT: public GMParserSRTOBase
{
public:
    GMParserNSRT(const WDBMTypeMgr& typeMgr)
        : GMParserSRTOBase(typeMgr, "NSRT")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserLSNOBase : public GMParserBase
{
public:
    GMParserLSNOBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paaxis");
        put(index++, "Pbaxis");
        put(index++, "Ptdistance");
        put(index++, "Pbdistance");
        put(index++, "Ptdiameter");
        put(index++, "Pbdiameter");
        put(index++, "Poffset");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserLSNO: public GMParserLSNOBase
{
public:
    GMParserLSNO(const WDBMTypeMgr& typeMgr)
        : GMParserLSNOBase(typeMgr, "LSNO")
    {
    }
};
class GMParserNLSN: public GMParserLSNOBase
{
public:
    GMParserNLSN(const WDBMTypeMgr& typeMgr)
        : GMParserLSNOBase(typeMgr, "NLSN")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSSPHBase : public GMParserBase
{
public:
    GMParserSSPHBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++ , "Paxis");
        put(index++ , "Pdistance");
        put(index++ , "Pdiameter");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};
class GMParserSSPH: public GMParserSSPHBase
{
public:
    GMParserSSPH(const WDBMTypeMgr& typeMgr)
        : GMParserSSPHBase(typeMgr, "SSPH")
    {
    }
};
class GMParserNSSP: public GMParserSSPHBase
{
public:
    GMParserNSSP(const WDBMTypeMgr& typeMgr)
        : GMParserSSPHBase(typeMgr, "NSSP")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSEXTBase : public GMParserBase
{
public:
    GMParserSEXTBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paaxis");
        put(index++, "Pbaxis");
        put(index++, "Pheight");
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Pz");

        prepareSVER(typeMgr);
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
private:
    std::array<const WDBMAttrDesc*, 3> _attrsSVER;
    void prepareSVER(const WDBMTypeMgr& typeMgr);
    bool parseSVER(size_t index
        , Context& context
        , const WDNode& nodeSVER
        , float& outValue);
    inline const std::string& getSVERName(size_t index) const
    {
        assert(index < _attrsSVER.size() && "数组越界!");
        if (_attrsSVER[index] == nullptr)
            return _emptyName;
        return _attrsSVER[index]->name();
    }
};
class GMParserSEXT: public GMParserSEXTBase
{
public:
    GMParserSEXT(const WDBMTypeMgr& typeMgr)
        : GMParserSEXTBase(typeMgr, "SEXT")
    {
    }
};
class GMParserNSEX: public GMParserSEXTBase
{
public:
    GMParserNSEX(const WDBMTypeMgr& typeMgr)
        : GMParserSEXTBase(typeMgr, "NSEX")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSREVBase : public GMParserBase
{
public:
    GMParserSREVBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
        : GMParserBase(typeMgr, type)
    {
        size_t index = 0;
        put(index++, "Paaxis");
        put(index++, "Pbaxis");
        put(index++, "Pangle");
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Pz");

        prepareSVER(typeMgr);
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
private:
    std::array<const WDBMAttrDesc*, 3> _attrsSVER;
    void prepareSVER(const WDBMTypeMgr& typeMgr);
    bool parseSVER(size_t index
        , Context& context
        , const WDNode& nodeSVER
        , float& outValue);
    inline const std::string& getSVERName(size_t index) const
    {
        assert(index < _attrsSVER.size() && "数组越界!");
        if (_attrsSVER[index] == nullptr)
            return _emptyName;
        return _attrsSVER[index]->name();
    }
};
class GMParserSREV: public GMParserSREVBase
{
public:
    GMParserSREV(const WDBMTypeMgr& typeMgr)
        : GMParserSREVBase(typeMgr, "SREV")
    {
    }
};
class GMParserNSRE: public GMParserSREVBase
{
public:
    GMParserNSRE(const WDBMTypeMgr& typeMgr)
        : GMParserSREVBase(typeMgr, "NSRE")
    {
    }
public:
    virtual bool isNegative() const override
    {
        return true;
    }
};

class GMParserSREC: public GMParserBase
{
public:
    GMParserSREC(const WDBMTypeMgr& typeMgr)
        : GMParserBase(typeMgr, "SREC")
    {
        size_t index = 0;
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Pxlength");
        put(index++, "Pylength");
        put(index++, "Plaxis");
        put(index++, "Dx");
        put(index++, "Dy");
        put(index++, "Dxl");
        put(index++, "Dyl");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};

class GMParserSANN: public GMParserBase
{
public:
    GMParserSANN(const WDBMTypeMgr& typeMgr)
        : GMParserBase(typeMgr, "SANN")
    {
        size_t index = 0;
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Plaxis");
        put(index++, "Pangle");
        put(index++, "Pradius");
        put(index++, "Pwidth");
        put(index++, "Dx");
        put(index++, "Dy");
        put(index++, "Dradius");
        put(index++, "Dwidth");
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
};

class GMParserSPRO: public GMParserBase
{
public:
    GMParserSPRO(const WDBMTypeMgr& typeMgr)
        : GMParserBase(typeMgr, "SPRO")
    {
        size_t index = 0;
        put(index++, "Plaxis");

        prepareSPVE(typeMgr);
    }
public:
    virtual WDGeometry::SharedPtr build(Context& context
        , const WDNode& node
        , const MeshLODSelection& lodSelection = MeshLODSelection()) override;
private:
    std::array<const WDBMAttrDesc*, 6> _attrsSPVE;
    void prepareSPVE(const WDBMTypeMgr& typeMgr);
    bool parseSPVE(size_t index
        , Context& context
        , const WDNode& nodeSPVE
        , float& outValue) const;
    inline const std::string& getSPVEName(size_t index) const
    {
        assert(index < _attrsSPVE.size() && "数组越界!");
        if (_attrsSPVE[index] == nullptr)
            return _emptyName;
        return _attrsSPVE[index]->name();
    }

};

WD_NAMESPACE_END

