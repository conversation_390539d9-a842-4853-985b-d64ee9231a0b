#include "DTParser.h"

WD_NAMESPACE_BEGIN

DATAParser::DATAParser(const WDBMTypeMgr& typeMgr, const std::string_view& type)
{
    _pTypeDesc = typeMgr.get(type);
    PARSE_ASSERT(_pTypeDesc != nullptr);
    // 缓存部分通用属性
    if (_pTypeDesc != nullptr)
    {
        // Number属性描述
        _pAttrDescNumber = _pTypeDesc->get("Number");
        // Dkey属性描述
        _pAttrDescDkey = _pTypeDesc->get("Dkey");
        // Purpose属性描述
        _pAttrDescPurpose = _pTypeDesc->get("Purpose");
        // Dproperty属性描述
        _pAttrDescDPty = _pTypeDesc->get("Dproperty");
        // Pproperty属性描述
        _pAttrDescPPty = _pTypeDesc->get("Pproperty");
    }
}
DATAParser::~DATAParser()
{

}

int DATAParser::number(Context& context, const WDNode& node)
{
    WDUnused(context);
    if (_pAttrDescNumber == nullptr)
        return -1;
    auto rValue = _pAttrDescNumber->valueCRef(node);
    auto pV = rValue.toInt();
    if (pV == nullptr)
        return -1;
    return *pV;
}
std::string DATAParser::dKey(Context& context, const WDNode& node)
{
    WDUnused(context);
    if (_pAttrDescDkey == nullptr)
        return "";
    auto rValue = _pAttrDescDkey->valueCRef(node);
    auto pV = rValue.toString();
    if (pV == nullptr)
        return "";
    return *pV;
}
std::string DATAParser::purpose(Context& context, const WDNode& node)
{
    WDUnused(context);
    if (_pAttrDescPurpose == nullptr)
        return "";
    auto rValue = _pAttrDescPurpose->valueCRef(node);
    auto pV = rValue.toWord();
    if (pV == nullptr)
        return "";
    return std::string(*pV);
}
std::optional<std::any> DATAParser::dProperty(Context& context, const WDNode& node)
{
    if (_pAttrDescDPty == nullptr)
        return std::nullopt;
    std::any ret;
    if (!parse(context, node, *_pAttrDescDPty, ret))
        return std::nullopt;
    return ret;
}
std::optional<std::any> DATAParser::pProperty(Context& context, const WDNode& node)
{
    if (_pAttrDescPPty == nullptr)
        return std::nullopt;
    std::any ret;
    if (!parse(context, node, *_pAttrDescPPty, ret))
        return std::nullopt;
    return ret;
}

bool DATAParser::parse(Context& context, const WDNode& node, const WDBMAttrDesc& aDesc, std::any& outValue)
{
    auto cref = aDesc.valueCRef(node);
    auto pRValue = cref.toString();
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (IsUnsetValue(rValue))
        return false;
    if (!DSLExec(context.dsl, rValue, outValue, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, aDesc.name(), rValue, _lastErrStr);
        return false;
    }
    return true;
}

WD_NAMESPACE_END

