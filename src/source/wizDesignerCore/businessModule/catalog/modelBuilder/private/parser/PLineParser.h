#pragma once

#include "../DSLCommon.h"
#include "../Context.h"
#include "../../../../typeMgr/WDBMTypeMgr.h"

WD_NAMESPACE_BEGIN

/**
 * @brief PLine线解析基类
 */
class PLineParserBase 
{
private:
    // 类型描述
    const WDBMTypeDesc* _pTypeDesc;
    // 用于生成关键点数据的参数描述
    std::array<const WDBMAttrDesc*, 50> _attrs;
    // PLine线Key属性描述
    const WDBMAttrDesc* _pAttrDescKey = nullptr;
    // 空名称
    std::string _emptyName = "";
    // 错误信息
    std::string _lastErrStr = "";
public:
    /**
     * @brief 校验节点是否PLine节点
     */
    static inline bool IsPLineNode(const WDNode& node)
    {
        return node.isType("PLIN");
    }
public:
    PLineParserBase(const WDBMTypeMgr& typeMgr, const std::string_view& type);
    virtual ~PLineParserBase();
public:
    /**
     * @brief 生成PLine线
     * @param context 
     * @param node 
     * @param length PLine线长度,用于计算终点
     * @param direction PLine线起点到终点的方向,用于计算终点
     * @return 
     */
    virtual std::optional<WDPLine> build(Context& context
        , const WDNode& node
        , double length
        , const DVec3& dir) = 0;
public:
    /**
     * @brief 获取PLine Key
     * @return 无效时返回""
     */
    std::string getKey(Context& context, const WDNode& node);
protected:
    inline bool put(size_t index, const std::string_view& name)
    {
        if (_pTypeDesc == nullptr)
            return false;
        assert(index < _attrs.size() && "数组越界!");
        _attrs[index] = _pTypeDesc->get(name);
        assert(_attrs[index] != nullptr);
        return _attrs[index] != nullptr;
    }
    bool parse(size_t index
        , Context& context
        , const WDNode& node
        , float& outValue);

    bool parse(size_t index
        , Context& context
        , const WDNode& node
        , DSLKeyPointResult& outRet);
private:
    inline const std::string& getName(size_t index) const
    {
        assert(index < _attrs.size() && "数组越界!");
        if (_attrs[index] == nullptr)
            return _emptyName;
        return _attrs[index]->name();
    }
    inline const std::string* get(size_t index, const WDNode& node) const
    {
        assert(index < _attrs.size() && "数组越界!");
        if (_attrs[index] == nullptr)
            return nullptr;
        auto vCRef = _attrs[index]->valueCRef(node);
        return vCRef.toString();
    }
};
using PLineParsers = std::vector<PLineParserBase*>;

class PLineParserPLIN : public PLineParserBase
{
public:
    PLineParserPLIN(const WDBMTypeMgr& typeMgr)
        : PLineParserBase(typeMgr, "PLIN")
    {
        size_t index = 0;
        put(index++, "Px");
        put(index++, "Py");
        put(index++, "Plaxis");
    }
public:
    virtual std::optional<WDPLine> build(Context& context
        , const WDNode& node
        , double length
        , const DVec3& direction) override;
};

WD_NAMESPACE_END

