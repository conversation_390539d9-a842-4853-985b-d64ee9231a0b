#include "PLineParser.h"

WD_NAMESPACE_BEGIN

PLineParserBase::PLineParserBase(const WDBMTypeMgr& typeMgr, const std::string_view& type)
{
    _pTypeDesc = typeMgr.get(type);
    PARSE_ASSERT(_pTypeDesc != nullptr);
    std::fill(_attrs.begin(), _attrs.end(), nullptr);
    // 缓存部分通用属性
    if (_pTypeDesc != nullptr)
    {
        // PLine Key
        _pAttrDescKey = _pTypeDesc->get("Pkey");
    }
}

PLineParserBase::~PLineParserBase()
{

}

std::string PLineParserBase::getKey(Context& context, const WDNode& node)
{
    WDUnused(context);
    if (_pAttrDescKey == nullptr)
        return "";
    auto rValue = _pAttrDescKey->valueCRef(node);
    auto pStr = rValue.toString();
    if (pStr == nullptr)
        return "";
    return *pStr;
}

bool PLineParserBase::parse(size_t index, Context& context, const WDNode& node, float& outValue)
{
    auto pRValue = this->get(index, node);
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (rValue.empty() || IsUnsetValue(rValue))
    {
        outValue = 0.0f;
        return true;
    }
    if (!DSLExec(context.dsl, rValue, outValue, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, getName(index), rValue, _lastErrStr);
        return false;
    }
    return true;
}
bool PLineParserBase::parse(size_t index, Context& context, const WDNode& node, DSLKeyPointResult& outRet)
{
    auto pRValue = this->get(index, node);
    if (pRValue == nullptr)
    {
        PARSE_ASSERT(false);
        return false;
    }
    const auto& rValue = (*pRValue);
    if (IsUnsetValue(rValue))
        return false;
    if (!DSLExecDirection(context.dsl, rValue, outRet, _lastErrStr))
    {
        ParseAttriFaildLogPrint(context, node, getName(index), rValue, _lastErrStr);
        return false;
    }
    return true;
}

std::optional<WDPLine> PLineParserPLIN::build(Context& context, const WDNode& node, double length, const DVec3& direction)
{
    // PLine Key
    auto key = getKey(context, node);

    int index = 0;
    // 解析起点位置的x分量
    float px = 0.0f;
    if (!parse(index++, context, node, px))
        return std::nullopt;
    // 解析起点位置的y分量
    float py = 0.0f;
    if (!parse(index++, context, node, py))
        return std::nullopt;
    // 解析起点朝向
    DSLKeyPointResult axisRet;
    if (!parse(index++, context, node, axisRet))
        return std::nullopt;
    // 开始点
    DVec3 sPos = DVec3(px, py, 0.0);
    // 计算结束点
    DVec3 ePos = sPos + length * direction;

    return WDPLine(key
        , sPos
        , DVec3(axisRet.direction)
        , ePos);
}

WD_NAMESPACE_END

