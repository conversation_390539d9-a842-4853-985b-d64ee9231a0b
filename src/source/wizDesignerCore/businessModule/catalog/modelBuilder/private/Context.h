#pragma once

#include "WDCore.h"
#include "../WDBMCModelBuilder.h"
#include "ValueGet.h"
#include "DNodeAttrKey.h"
#include "catch/DataSetCatch.h"
#include "catch/KeyPtSetCatch.h"

WD_NAMESPACE_BEGIN

class Context;
class DATAParser;
class PTParserBase;
class PLineParserBase;
class GMParserBase;

/**
 * @brief 样条线参数，生成拉升体或扫描体时使用
 */
class SPLineParams 
{
public:
    // 指定放样的起始端面法线
    std::optional<FVec3> drnS = std::nullopt;
    // 指定放样的结束端面法线
    std::optional<FVec3> drnE = std::nullopt;
    // 指定放样轮廓顶点的对齐点
    std::string justLine = "NA";
    // 放样体的样条线
    WDLoftSPline spline = WDLoftSPline();
    // 轮廓的旋转角度
    float bAngle = 0.0f;
};

/*
* @brief 元件库节点构建数据上下文
*  Build Data Context
*/
class Context
{
public:
    using GMParsers = std::vector<GMParserBase*>;
    using PTParsers = std::vector<PTParserBase*>;
    using PLineParsers = std::vector<PLineParserBase*>;
    /**
     * @brief 样条线参数获取回调
     */
    using FunctionSplineParamGet = std::function<SPLineParams(Context& cxt)>;
public:
    // 解析结果
    WDBMCModelBuilder::CMNData ret;
    // 表达式解析上下文对象
    WIZDesignerDSL::DSLContext& dsl;
    // 对齐点, 解析 PLine 和 部分带轮廓的拉伸体时，需要使用该对齐点来偏移轮廓的顶点
    DVec2 justPoint = DVec2::Zero();
    // 使用设计节点属性生成唯一Key的对象
    std::shared_ptr<DNodeAttrKey> pDNodeAKey = nullptr;
    // 沉默，不输入任何报错日志
    bool bSilence = false;
private:
    // core 对象
    WDCore& _core;
    // DATA解析对象
    DATAParser& _dtParser;
    // 关键点解析对象列表
    const PTParsers& _ptParsers;
    // PLine线解析对象列表
    const PLineParsers& _plineParsers;
    // 型解析对象列表
    const GMParsers& _gmParsers;
    // 值获取管理对象
    ValueGetMgr& _valueGetMgr;
    // 当前节点
    const WDNode* _pNode;
    // 当前等级节点
    const WDNode* _pSPCONode;
    // 当前元件节点
    WDNode::WeakPtr _scomNode;
    // 数据集缓存
    DataSetCatch _dataSetCatch;
    // 关键点集缓存
    KeyPtSetCatch _keyPointCatch;
    // 样条线，结构专业类型的节点生成，并在生成PLine线和型时使用
    std::optional<SPLineParams> _splineParams = std::nullopt;
    FunctionSplineParamGet _splineParamGetFunc;
    // 缓存已经获取过的变量值
    using Key = std::pair<std::string, std::optional<int> >;
    std::map<Key, std::any> _varCatch;

    // 是否保温
    bool _bInsu;
public:
    Context(WDCore& core
        , const WDNode* pDNode
        , const WDNode* pSPCONode
        , WDNode::SharedPtr pSCOMNode
        , WIZDesignerDSL::DSLContext& dslCxt
        , DATAParser& dtParser
        , const PTParsers& ptParsers
        , const PLineParsers& plineParsers
        , const GMParsers& gmParsers
        , ValueGetMgr& valueGetMgr
        , bool bInsu
        , const FunctionSplineParamGet& splineParamGetFunc = FunctionSplineParamGet());
    ~Context();
public:
    /**
     * @brief 获取core对象
     */
    inline WDCore& core() 
    {
        return _core;
    }
    /**
     * @brief 指定DATA节点，查询DATA解析对象
     */
    inline DATAParser& dtParser() const 
    {
        return _dtParser;
    }
    /**
     * @brief 指定关键点节点，查询关键点解析对象
     */
    inline PTParserBase* ptParser(const WDNode& ptNode) const
    {
        if (ptNode.typeId() >= _ptParsers.size())
            return nullptr;
        return _ptParsers[ptNode.typeId()];
    }
    /**
     * @brief 指定PLine节点，查询PLine解析对象
     */
    inline PLineParserBase* plineParser(const WDNode& plNode) const
    {
        if (plNode.typeId() >= _plineParsers.size())
            return nullptr;
        return _plineParsers[plNode.typeId()];
    }
    /**
     * @brief 指定型节点，查询型解析对象
     */
    inline GMParserBase* gmParser(const WDNode& gmNode) const
    {
        if (gmNode.typeId() >= _gmParsers.size())
            return nullptr;
        return _gmParsers[gmNode.typeId()];
    }
    /**
    * @brief 获取当前设计节点
    */
    inline const WD::WDNode* node() const
    {
        return _pNode;
    }
    /**
     * @brief 获取当前等级节点
     */
    inline const WD::WDNode* spcoNode() const 
    {
        return _pSPCONode;
    }
    /**
     * @brief 获取当前的元件节点
     */
    inline WDNode::SharedPtr scomNode() const 
    {
        return _scomNode.lock();
    }
    /**
     * @brief 获取数据集缓存
     */
    inline DataSetCatch& dataSetCatch()
    {
        return _dataSetCatch;
    }
    /**
     * @brief 获取关键点集缓存
     */
    inline KeyPtSetCatch& keyPtSetCatch()
    {
        return _keyPointCatch;
    }
    /**
     * @brief 获取样条线参数
     */
    const SPLineParams& splineParams();
    /**
     * @brief 获取值获取对象管理
     */
    inline ValueGetMgr& valueGetMgr()
    {
        return _valueGetMgr;
    }
    /**
     * @brief 获取是否保温
     */
    inline bool bInsu() const 
    {
        return _bInsu;
    }
public:
    /**
     * @brief 获取指定设计模块节点的元件节点
     * @param desiNode 设计模块节点
     * @return 元件节点
     */
    static WDNode::SharedPtr GetSCOMNode(const WDNode& desiNode);
    /**
     * @brief 指定等级节点获取对应的元件节点
     * @param spNode 等级节点
     * @return 元件节点
     */
    static WDNode::SharedPtr GetSCOMNodeBySPCONode(const WDNode& spNode);
    /**
     * @brief 获取指定设计模块节点的等级节点
     * @param desiNode 设计模块节点
     * @return 等级节点
     */
    static WDNode::SharedPtr GetSPCONode(const WDNode& desiNode);
};

WD_NAMESPACE_END