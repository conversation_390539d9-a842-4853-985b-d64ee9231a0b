#include "Context.h"
#include "parser/PTParser.h"
#include "parser/DTParser.h"

WD_NAMESPACE_BEGIN

Context::Context(WDCore& core
    , const WDNode* pDNode
    , const WDNode* pSPCONode
    , WDNode::SharedPtr pSCOMNode
    , WIZDesignerDSL::DSLContext& dslCxt
    , DATAParser& dtParser
    , const PTParsers& ptParsers
    , const PLineParsers& plineParsers
    , const GMParsers& gmParsers
    , ValueGetMgr& valueGetMgr
    , bool bInsu
    , const FunctionSplineParamGet& splineParamGetFunc)
    : _core(core)
    , dsl(dslCxt)
    , _pNode(pDNode)
    , _pSPCONode(pSPCONode)
    , _scomNode(pSCOMNode)
    , _dtParser(dtParser)
    , _ptParsers(ptParsers)
    , _plineParsers(plineParsers)
    , _gmParsers(gmParsers)
    , _valueGetMgr(valueGetMgr)
    , _keyPointCatch(*this)
    , _dataSetCatch(*this)
    , _bInsu(bInsu)
    , _splineParamGetFunc(splineParamGetFunc)
{
    // 重置变量缓存
    _varCatch.clear();
    // 注册属性获取方法
    dsl.setFnGetInnerVariable([=](std::any& result, const std::string& name)-> bool
        {
            // 从已获取的变量缓存中查找
            auto key = Key(name, std::nullopt);
            auto fItr = _varCatch.find(key);
            if (fItr != _varCatch.end())
            {
                if (fItr->second.has_value())
                    result = fItr->second;
                return fItr->second.has_value();
            }
            // 通过值获取管理
            auto tmp = _valueGetMgr.getValue(*this, name);
            if (tmp.has_value())
                result = tmp;
            // 缓存
            _varCatch[key] = tmp;

            return tmp.has_value();
        });
    // 注册数组类型属性获取方法
    dsl.setFnGetInnerArrayVariable([=](std::any& result, const std::string& name, int index)-> bool
        {
            // 从已获取的变量缓存中查找
            auto key = Key(name, index);
            auto fItr = _varCatch.find(key);
            if (fItr != _varCatch.end())
            {
                if (fItr->second.has_value())
                    result = fItr->second;
                return fItr->second.has_value();
            }
            auto tmp = _valueGetMgr.getValue(*this, name, index);
            if (tmp.has_value())
                result = tmp;
            // 缓存
            _varCatch[key] = tmp;

            return tmp.has_value();
        });
    dsl.setFnSetInnerVariable([=](const std::string& name, const std::any& value)->bool
        {
            WDUnused(name);
            WDUnused(value);
            return false;
        });
    // OF操作符的特殊处理，如果出现了OF操作符，则直接设置为非共享，因为目前针对OF的共享还不支持
    dsl.setNoticeOfOpt([this]()
        {
            if (this->pDNodeAKey != nullptr)
                this->pDNodeAKey->setSharedDisabled();
        });
}
Context::~Context()
{

}

const SPLineParams& Context::splineParams()
{
    if (_splineParams)
        return _splineParams.value();

    if (_splineParamGetFunc) 
        _splineParams = _splineParamGetFunc(*this);

    return _splineParams.value();
}

WDNode::SharedPtr Context::GetSCOMNode(const WDNode& desiNode)
{
    auto pRNode = desiNode.getAttribute("Spref").toNodeRef().refNode();
    if (pRNode != nullptr)
    {
        if (pRNode->isType("SPCO"))
            return pRNode->getAttribute("Catref").toNodeRef().refNode();
        else if (pRNode->isAnyOfType("SCOM", "SFIT", "SPRF", "JOIN"))
            return pRNode;
        else
            return nullptr;
    }
    else
    {
        pRNode = desiNode.getAttribute("Catref").toNodeRef().refNode();
        if (pRNode == nullptr)
            return nullptr;
        if (pRNode->isType("SPCO"))
            return pRNode->getAttribute("Catref").toNodeRef().refNode();
        else if (pRNode->isAnyOfType("SCOM", "SFIT", "SPRF", "JOIN"))
            return pRNode;
        else
            return nullptr;
    }
}
WDNode::SharedPtr Context::GetSCOMNodeBySPCONode(const WDNode& spNode) 
{
    return spNode.getAttribute("Catref").toNodeRef().refNode();
}
WDNode::SharedPtr Context::GetSPCONode(const WDNode& desiNode)
{
    auto pRNode = desiNode.getAttribute("Spref").toNodeRef().refNode();
    if (pRNode != nullptr && pRNode->isType("SPCO"))
        return pRNode;
    pRNode = desiNode.getAttribute("Catref").toNodeRef().refNode();
    if (pRNode != nullptr && pRNode->isType("SPCO"))
        return pRNode;
    return nullptr;
}

WD_NAMESPACE_END
