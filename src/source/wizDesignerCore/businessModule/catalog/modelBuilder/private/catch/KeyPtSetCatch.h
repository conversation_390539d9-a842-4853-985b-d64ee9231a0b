#pragma once

#include "WDCore.h"
#include "../../../../../node/WDNode.h"
#include "../../WDBMCModelBuilder.h"

WD_NAMESPACE_BEGIN

class Context;

/**
 * @brief 关键点缓存，因为关键点可能会被多次复用，因此这里将解析的结果缓存
 */
class KeyPtSetCatch 
{
private:
    // 上下文对象
    Context& _cxt;
    // 点集节点
    WDNode::WeakPtr _nodePTSE;
    // 数据
    struct Data
    {
    private:
        // 关键点节点
        WD::WDNode::WeakPtr _node;
        // 缓存解析后的结果
        WDKeyPoint _keyPoint;
        // 是否被解析
        bool _parsed = false;
        // 是否解析成功
        bool _successed = false;
    public:
        WDNode::SharedPtr node() const 
        {
            return _node.lock();
        }
        Data(WDNode::SharedPtr pPtNode = nullptr);
    public:
        const WDKeyPoint& rKeyPoint(Context& cxt, bool* bOk = nullptr);
    };
    // 所有数据
    std::vector<Data> _datas;
    // 名称对应的下标
    std::unordered_map<std::string, size_t> _nameMap;
    // 是否已初始化
    bool _initinalized;
public:
    KeyPtSetCatch(Context& cxt);
public:
    /**
     * @brief 指定点集节点("PTSE"),初始化缓存
     * @param nodePTSE 点集节点("PTSE")
     * @return 是否初始化成功，如果指定的点集节点无效，则初始化失败
     */
    bool init(WDNode::SharedPtr pNodePTSE);
    /**
     * @brief 获取所有的结果关键点
     */
    void getAll(WDBMCModelBuilder::CMNData::CMKeyPoints& ret);
    /**
     * @brief 指定名称，查询关键点
     * @param name 名称, eg: P1, P2, ...
     * @return 对应名称的关键点如果不存在，则返回std::nullopt
     */
    std::optional<WDKeyPoint> query(const std::string& name);
private:
    // 使用时再初始化
    void initP();
};

WD_NAMESPACE_END