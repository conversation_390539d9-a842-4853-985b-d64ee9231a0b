#include "KeyPtSetCatch.h"
#include "../Context.h"
#include "../parser/PTParser.h"

WD_NAMESPACE_BEGIN

KeyPtSetCatch::Data::Data(WDNode::SharedPtr pPtNode)
    : _node(pPtNode)
{
}

const WDKeyPoint& KeyPtSetCatch::Data::rKeyPoint(Context& cxt, bool* bOk)
{
    if (_parsed)
    {
        SetValueToBooleanPtr(bOk, _successed);
        return _keyPoint;
    }

    _parsed = true;
    _successed = false;
    SetValueToBooleanPtr(bOk, false);

    // 解析关键点的值
    std::string lastError;
    auto pPtNode = _node.lock();
    if (pPtNode == nullptr)
    {
        return _keyPoint;
    }
    auto pParser = cxt.ptParser(*pPtNode);
    if (pParser == nullptr)
    {
        PARSE_ASSERT(false && "注意: 未知的关键点类型!");
        ParseNodeFaildLogPrint(cxt, *pPtNode, "未知的关键点类型!");
        return _keyPoint;
    }
    auto rValue = pParser->build(cxt, *pPtNode);
    if (rValue)
    {
        _keyPoint = rValue.value();
        _successed = true;
    }

    SetValueToBooleanPtr(bOk, _successed);
    return _keyPoint;
}

KeyPtSetCatch::KeyPtSetCatch(Context& cxt): _cxt(cxt)
{
    _initinalized = false;
}
bool KeyPtSetCatch::init(WDNode::SharedPtr pNodePTSE)
{
    if (pNodePTSE == nullptr)
        return false;
    if (!pNodePTSE->isType("PTSE"))
        return false;
    _nodePTSE = pNodePTSE;
    return true;
}
void KeyPtSetCatch::getAll(WDBMCModelBuilder::CMNData::CMKeyPoints& ret)
{
    initP();

    ret.reserve(_datas.size());
    bool bOk = false;
    for (auto& dt : _datas)
    {
        auto pNode = dt.node();
        if (pNode == nullptr)
            continue;
        const auto& rKPt = dt.rKeyPoint(_cxt, & bOk);
        if (!bOk)
            continue;
        ret.push_back({ pNode, rKPt });
    }
}
std::optional<WDKeyPoint> KeyPtSetCatch::query(const std::string& name)
{
    initP();
    auto itr = _nameMap.find(name);
    if (itr == _nameMap.end())
        return std::nullopt;
    if (itr->second < _datas.size())
    {
        bool bOk = false;
        auto retPt = _datas[itr->second].rKeyPoint(_cxt, &bOk);
        if (!bOk)
            return std::nullopt;
        return retPt;
    }
    return std::nullopt;
}
void KeyPtSetCatch::initP()
{
    if (_initinalized)
        return;
    _initinalized = true;

    _datas.clear();
    _nameMap.clear();

    auto pNode = _nodePTSE.lock();
    if (pNode == nullptr)
        return;
    auto& nodePTSE = *pNode;

    char buf[1024] = { 0 };
    _datas.reserve(nodePTSE.children().size());
    for (auto pPtNode : nodePTSE.children())
    {
        if (pPtNode == nullptr || !PTParserBase::IsPTNode(*pPtNode))
        {
            PARSE_ASSERT(false);
            continue;
        }
        auto pParser = _cxt.ptParser(*pPtNode);
        if (pParser == nullptr)
        {
            PARSE_ASSERT(false);
            continue;
        }
        int number = pParser->getNumber(_cxt, *pPtNode);
        if (number < 0)
        {
            PARSE_ASSERT(false);
            continue;
        }
        size_t index = _datas.size();
        // 添加到数据
        _datas.emplace_back(Data(pPtNode));
        // 拼接Number，做为关键点的变量名称
        sprintf_s(buf, sizeof(buf), "P%d", number);
        // 缓存, 相同名称的话，跳过
        if (_nameMap.find(buf) == _nameMap.end())
            _nameMap[buf] = index;
    }
}

WD_NAMESPACE_END
