#pragma once

#include "WDCore.h"
#include "../../../../../node/WDNode.h"

WD_NAMESPACE_BEGIN

class Context;

/**
 * @brief 数据集缓存, 数据集中的某个数据可能会被多次复用，因此这里将解析的结果缓存
 */
class DataSetCatch 
{
private:
    // 上下文对象
    Context& _cxt;
    // 数据集节点
    WDNode::WeakPtr _nodeDTSE;
    // 数据
    struct Data
    {
    private:
        // 数据节点
        WD::WDNode::WeakPtr _node;
        // 缓存解析后的结果
        std::any _rValue;
        // 是否被解析
        bool _parsed = false;
        // 是否解析成功
        bool _successed = false;
    public:
        Data(WDNode::SharedPtr pPtNode = nullptr);
    public:
        inline WD::WDNode::SharedPtr node() 
        {
            return _node.lock();
        }
        const std::any& rValue(Context& cxt, bool* bOk = nullptr);
    };
    // 所有数据
    std::vector<Data> _datas;
    // DKey对应的下标
    std::unordered_map<std::string, size_t> _keyMap;
    // number对应的下标
    std::unordered_map<int, size_t> _numberMap;
    // 是否已初始化
    bool _initinalized;
public:
    DataSetCatch(Context& cxt);
public:
    /**
     * @brief 指定数据集节点("DTSE"),初始化缓存
     * @param nodeDTSE 数据集节点("DTSE")
     * @return 是否初始化成功，如果指定的数据集节点无效，则初始化失败
     */
    bool init(WDNode::SharedPtr pNodeDTSE);
    /**
     * @brief 指定number，查询值
     *  一般用于 DESP [number] 的值获取
     */
    std::any queryByNumber(int number);
    /**
     * @brief 指定 DKey, 查询值
     *  一般用于 RPRO dKey 的值获取
     */
    std::any queryByDKey(const std::string& dKey);
    /**
     * @brief 指定DKey，查询对应的number值
     * !注意: 如果DATA的Purpose不是DESP，这个接口会返回std::nullopt
     */
    std::optional<int> queryNumberByDKey(const std::string& dKey);
private:
    // 使用时再初始化
    void initP();
};

WD_NAMESPACE_END