#include "../Context.h"
#include "../parser/DTParser.h"
#include "DataSetCatch.h"

WD_NAMESPACE_BEGIN

DataSetCatch::Data::Data(WDNode::SharedPtr pDtNode) 
    :_node(pDtNode)
{

}
const std::any& DataSetCatch::Data::rValue(Context& cxt, bool* bOk)
{
    if (_parsed)
    {
        SetValueToBooleanPtr(bOk, _successed);
        return _rValue;
    }
    _parsed = true;
    _successed = false;

    SetValueToBooleanPtr(bOk, false);

    auto pDATANode = _node.lock();
    if (pDATANode == nullptr)
    {
        SetValueToBooleanPtr(bOk, false);
        return _rValue;
    }
    // RPRO 使用 pProperty
    auto tRet = cxt.dtParser().pProperty(cxt, *pDATANode);
    if (tRet) 
    {
        _successed = true;
        _rValue = tRet.value();
    }
    else 
    {
        tRet = cxt.dtParser().dProperty(cxt, *pDATANode);
        if (tRet)
        {
            _successed = true;
            _rValue = tRet.value();
        }
    }
    SetValueToBooleanPtr(bOk, _successed);
    return _rValue;
}

DataSetCatch::DataSetCatch(Context& cxt)
    :_cxt(cxt)
{
    _initinalized = false;
}
bool DataSetCatch::init(WDNode::SharedPtr pNodeDTSE)
{
    if (pNodeDTSE == nullptr)
        return false;
    if(!pNodeDTSE->isType("DTSE"))
        return false;
    _nodeDTSE = pNodeDTSE;
    return true;
}
std::any DataSetCatch::queryByNumber(int number)
{
    initP();
    auto fItr = _numberMap.find(number);
    if (fItr == _numberMap.end())
        return std::any();
    if (fItr->second < _datas.size())
        return _datas[fItr->second].rValue(_cxt);
    return std::any();
}
std::any DataSetCatch::queryByDKey(const std::string& dKey)
{
    initP();
    auto fItr = _keyMap.find(dKey);
    if (fItr == _keyMap.end())
        return std::any();
    if (fItr->second < _datas.size())
        return _datas[fItr->second].rValue(_cxt);

    return std::any();
}
std::optional<int> DataSetCatch::queryNumberByDKey(const std::string& dKey)
{
    initP();
    auto fItr = _keyMap.find(dKey);
    if (fItr == _keyMap.end())
        return std::nullopt;
    if (fItr->second < _datas.size())
    {
        auto pNode = _datas[fItr->second].node();
        if (pNode == nullptr)
            return std::nullopt;
        int number = _cxt.dtParser().number(_cxt, *pNode);
        if (number == -1)
            return std::nullopt;
        auto purpose = _cxt.dtParser().purpose(_cxt, *pNode);
        if (_stricmp(purpose.c_str(), "DESP") != 0)
            return std::nullopt;
        return number;
    }
    return std::nullopt;
}
void DataSetCatch::initP()
{
    if (_initinalized)
        return;
    _initinalized = true;

    _numberMap.clear();
    _keyMap.clear();
    _datas.clear();

    auto pNode = _nodeDTSE.lock();
    if (pNode == nullptr)
        return;
    auto& nodeDTSE = *pNode;

    _datas.reserve(nodeDTSE.children().size());
    for (auto pDATANode : nodeDTSE.children())
    {
        if (pDATANode == nullptr || !DATAParser::IsDTNode(*pDATANode))
            continue;
        // 记录索引
        size_t index = _datas.size();
        // 添加
        _datas.emplace_back(Data(pDATANode));
        // 如果number有效且用途是DESP，则缓存number对应的DATA索引
        auto number = _cxt.dtParser().number(_cxt, *pDATANode);
        if (number >= 0)
        {
            auto purpose = _cxt.dtParser().purpose(_cxt, *pDATANode);
            if (_stricmp(purpose.c_str(), "DESP") != 0)
            {
                if (_numberMap.find(number) == _numberMap.end())
                    _numberMap[number] = index;
            }
        }
        // 缓存dKey对应的DATA索引
        auto key = _cxt.dtParser().dKey(_cxt, *pDATANode);
        if (!key.empty() && _keyMap.find(key) == _keyMap.end())
            _keyMap[key] = index;
    }

}

WD_NAMESPACE_END
