#pragma once

#include "../WDBMCModelBuilder.h"
#include "parser/DTParser.h"
#include "parser/PTParser.h"
#include "parser/PLineParser.h"
#include "parser/GMParser.h"
#include "ValueGet.h"
#include "DNodeAttrKey.h"

WD_NAMESPACE_BEGIN


class WDBMCModelBuilderP
{
public:
    using Pool = WDBMCModelBuilder::Pool;
    using PoolI = WDBMCModelBuilder::PoolI;

    using CMData = WDBMCModelBuilder::CMData;
    using CMGDatas = WDBMCModelBuilder::CMData::GDatas;
public:
    WDCore& _core;
    WDBMCModelBuilder& _d;
    /**
     * @brief 元件生成的数据缓存
     */
    template <typename TPool, typename TData>
    struct TCatch
    {
    public:
        using SharedModel = typename TPool::Object;
        using SharedObject = WDSharedObjectT<TData>;
    public:
        using SharedPtr = std::shared_ptr<TCatch>;
        static SharedPtr MakeShared()
        {
            return std::make_shared<TCatch>();
        }
    private:
        // 共享模型对象池
        TPool _pool;
        // 使用设计模块节点属性来生成唯一Key以进行模型共享优化
        std::shared_ptr<DNodeAttrKey> _pAKey = nullptr;
    public:
        /**
         * @brief 设置设计节点唯一Key生成对象
         */
        inline void setAKey(std::shared_ptr<DNodeAttrKey> pAKey)
        {
            _pAKey = pAKey;
        }
        /**
         * @brief 是否支持共享
         */
        inline bool sharedEnabled() const
        {
            if (_pAKey == nullptr)
                return false;
            return _pAKey->sharedEnabled();
        }
        /**
         * @brief 查询共享模型
         */
        SharedModel queryModel(const WDNode& node)
        {
            if (!this->sharedEnabled())
                return nullptr;
            auto key = _pAKey->get(node);
            return _pool.query(key);
        }
        /**
         * @brief 缓存共享模型
         */
        SharedModel catchModel(const WDNode& node, TData&& tData)
        {
            if (!this->sharedEnabled())
                return nullptr;
            auto key = _pAKey->get(node);
            // 创建共享模型
            auto pSaredModel = std::make_shared<SharedObject>(std::move(tData));
            // 添加到共享池中
            bool bAdd = _pool.add(key, pSaredModel);
            assert(bAdd && "注意: 共享模型添加到共享池失败!");
            if (!bAdd)
                return nullptr;
            return pSaredModel;
        }
        /**
         * @brief 比较当前节点获取的Key与共享模型中已存在的key是否相同
         * @param node 
         * @param pModel 
         * @return 
         */
        bool compareModelKey(const WDNode& node, SharedModel pModel)
        {
            if (!this->sharedEnabled())
                return false;
            if (pModel == nullptr)
                return false;
            auto pOldKey = _pool.key(*pModel);
            if (pOldKey == nullptr)
                return false;
            return _pAKey->compareGet(node, *pOldKey);
        }
    };
    using CatchM = TCatch<Pool, CMData>;
    // 设计模块类型对应的模型数据
    using DTDescMap = std::unordered_map<const WDBMTypeDesc*, CatchM::SharedPtr>;
    // 存放元件以及其生成的元件模型
    using ScomMap = std::unordered_map<WDNode::SharedPtr, DTDescMap>;
    // 元件生成数据的缓存
    ScomMap _scomMap;

    using CatchI = TCatch<PoolI, CMGDatas>;
    // 设计模块类型对应的模型数据
    using DTDescMapI = std::unordered_map<const WDBMTypeDesc*, CatchI::SharedPtr>;
    // 存放元件以及其生成的元件模型
    using ScomMapI = std::unordered_map<WDNode::SharedPtr, DTDescMapI>;
    // 元件生成保温模型数据的缓存
    ScomMapI _scomMapI;
private:
    // 表达式解析
    WIZDesignerDSL::DSLContext _dsl;
    // 数据解析对象
    DATAParser* _pDataParser;
    // 关键点解析对象列表
    PTParsers _ptParsers;
    // PLine解析对象列表
    PLineParsers _plineParsers;
    // 型解析对象列表
    GMParsers _gmParsers;
    // 设计模块元件解析值获取管理对象
    ValueGetMgr _dValueGetMgr;
    // 元件模块元件解析值获取管理对象
    ValueGetMgr _cValueGetMgr;
    // 
    ValueGetMgr _cbValueGetMgr;
public:
    WDBMCModelBuilderP(WDCore& core, WDBMCModelBuilder& d);
    ~WDBMCModelBuilderP();
public:
    /**
     * @brief 表达式解析上下文
     */
    WIZDesignerDSL::DSLContext& dsl() 
    {
        return _dsl;
    }
    /**
     * @brief DATA解析对象
     */
    DATAParser& dtParser();
    /**
     * @brief 关键点解析对象列表
     */
    const PTParsers& ptParsers();
    /**
     * @brief PLine解析对象列表
     */
    const PLineParsers& plineParsers();
    /**
     * @brief 型解析对象列表
     */
    const GMParsers& gmParses();
    /**
     * @brief 设计模块元件解析值获取管理对象
     */
    ValueGetMgr& dValueGetMgr();
    /**
     * @brief 元件模块元件解析值获取管理对象
     */
    ValueGetMgr& cValueGetMgr(const WDBMCModelBuilder::ValueGetFunction& func);
    /**
     * @brief 
     * @param func 
     * @return 
     */
    ValueGetMgr& cbValueGetMgr(const WDBMCModelBuilder::ValueGetFunction& func);
    /**
     * @brief 获取设计模块样条线参数
     */
    SPLineParams dSPLineParam(Context &cxt);
    /**
     * @brief 获取元件模块样条线参数
     */
    SPLineParams cSPLineParam(Context& cxt);
public:
    /**
     * @brief 生成元件模型
     * @param cxt 上下文对象
    */
    bool getModel(Context& cxt);
    /**
     * @brief 获取保温模型
     * @param cxt 上下文对象
     */
    bool getInsuModel(Context& cxt);

    /**
     * @brief 执行context初始化
     * @param cxt 
     * @return 
     */
    void contextInit(Context& cxt);
private:
    /**
     * @brief 解析PLine列表
     * @param pPTSS PLine集节点
     * @param cxt 上下文数据
    */
    static void ParsePTSS(const WDNode& nodePTSS
        , Context& cxt);
    /**
     * @brief 解析正型集
     */
    static void ParseGMs(const WDNode& nodeGMs, Context& cxt);
    /**
     * @brief 解析负型集
     */
    static void ParseNGMs(const WDNode& nodeNGMs, Context& cxt);
private:
    struct SCOMRefNodes 
    {
    public:
        // 元件引用的数据集节点
        WDNode::SharedPtr pNodeDTSE = nullptr;
        // 元件引用的点集节点
        WDNode::SharedPtr pNodePTSE = nullptr;
        // 元件引用的PLine集节点
        WDNode::SharedPtr pNodePTSS = nullptr;
        // 元件引用的型集节点(这里目前包含 GMSE 和 GMSS 两种类型的节点，但是具体的型节点都是从它们的子节点去获取)
        WDNode::SharedPtr pNodeGMs = nullptr;
        // 元件引用的负型集节点(NGMS)
        WDNode::SharedPtr pNodeNGMS = nullptr;
    };
    // 获取元件节点引用的节点
    SCOMRefNodes getSCOMRefNode(Context& cxt, const WDNode& scomNode);
};

WD_NAMESPACE_END