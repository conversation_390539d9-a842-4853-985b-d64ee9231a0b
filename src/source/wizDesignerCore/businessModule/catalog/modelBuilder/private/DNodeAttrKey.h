#pragma once

#include "../../../typeMgr/WDBMAttrDesc.h"
#include "../../../../math/geometric/standardPrimitives/WDLoftSPline.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 特殊情况，指定节点，获取其样条线
 */
class DNodeSPLineGet
{
public:
    /**
     * @brief 指定节点，获取其样条线参数
     * @param node 节点
     * @param bOk 是否获取成功
     * @return 结果
     */
    WDLoftSPline get(const WDNode& node, bool* bOk = nullptr) const;
};
/**
 * @brief 设计模块节点特殊属性获取对象基类
 */
class DNodeAttrGetBase
{
public:
    /**
     * @brief 指定节点, 获取其特殊属性
     * @param node 节点
     * @param bOk 是否存在该属性
     *  !注意: 这里返回的是是否存在该属性，而不是属性值是否有效
     *  比如: ODESP 的获取是获取到当前节点父节点的设计参数属性
     *      如果当前节点的父节点为空，则应该返回false
     *      如果当前节点的父节点不存在DESP属性，则应该返回false
     *      只要当前节点存在父节点且父节点存在DESP属性，不管这个属性值是否是空，这里都应该返回true
     * @return 结果值
     */
    virtual WDBMAttrValue get(const WDNode& node, bool* bOk = nullptr) const = 0;
};
/**
 * @brief 设计模块节点特殊属性获取对象基类, 带下标, !注意: 这里的下标从1开始
 */
class DNodeArrayAttrGetBase : public DNodeAttrGetBase
{
private:
    int _index;
public:
    DNodeArrayAttrGetBase(int index)
        :_index(index) 
    {
    }
public:
    /**
     * @brief 获取索引
     */
    inline int index() const
    {
        return _index;
    }
};


/**
 * @brief 通过设计模块节点属性值的列表来生成一个唯一key
 */
class DNodeAttrKey
{
public:
    /**
     * @brief Key
     */
    using Key = std::pair<std::vector<WDBMAttrValue>, std::optional<WDLoftSPline> >;
private:
    using GetBaseSPtr = std::shared_ptr<DNodeAttrGetBase>;
    // 可共享标志
    bool _bEnabled = true;
    // 从节点自身获取的一些属性
    std::vector<const WDBMAttrDesc*> _attrs;
    // 缓存，用于查询是否已存在
    std::set<const WDBMAttrDesc*> _attrSet;

    // 一些特殊属性的Get对象
    std::vector<GetBaseSPtr> _aGets;
    std::set<std::string> _aGetTypes;

    std::shared_ptr<DNodeSPLineGet> _pSplineGet = nullptr;
public:
    /**
     * @brief 添加属性描述
     */
    inline void appendADesc(const WDBMAttrDesc* pADesc) 
    {
        assert(pADesc != nullptr);
        if (pADesc == nullptr)
            return ;
        // 查找是否已存在，如果已存在则不重复添加
        auto fItr = _attrSet.find(pADesc);
        if (fItr != _attrSet.end())
            return;
        _attrs.push_back(pADesc);
        _attrSet.insert(pADesc);
    }
    /**
     * @brief 添加特殊属性获取对象
     */
    template <typename TGet>
    inline void appendAGet()
    {
        std::string typeName = typeid(TGet).name();
        auto fItr = _aGetTypes.find(typeName);
        if (fItr != _aGetTypes.end())
            return ;

        auto p = std::make_shared<TGet>();
        _aGets.push_back(p);
        _aGetTypes.insert(typeName);
        return ;
    }
    /**
     * @brief 添加样条线属性获取对象
     */
    inline DNodeSPLineGet& appendSPLineGet() 
    {
        if (_pSplineGet != nullptr)
            return *_pSplineGet;
        _pSplineGet = std::make_shared<DNodeSPLineGet>();
        return (*_pSplineGet);
    }
    /**
     * @brief 取消可共享标志
     *  只有部分使用了及其特殊变量的表达式的情况不能进行共享
     *  比如:壁厚数据的获取(表达式中的变量名称为:"PWALLT")
     *      需要拿到元件关键点上的公称直径再通过公称直径去等级上查询某个厚度值
     *      这种变量即使知道设计模块的节点，也需要解析元件之后才能知道具体值, 因此需要这种类型变量时，一般都会将 canShare设置为false
     *      表示无法共享
     */
    inline void setSharedDisabled() 
    {
        _bEnabled = false;
    }
public:
    /**
     * @brief 是否可共享标志, 默认值为true
     *  只有部分使用了及其特殊变量的表达式的情况不能进行共享
     *  比如:壁厚数据的获取(表达式中的变量名称为:"PWALLT")
     *      需要拿到元件关键点上的公称直径再通过公称直径去等级上查询某个厚度值
     *      这种变量即使知道设计模块的节点，也需要解析元件之后才能知道具体值, 因此需要这种类型变量时，一般都会将 canShare设置为false
     *      表示无法共享
     */
    inline bool sharedEnabled() const 
    {
        return _bEnabled;
    }
    /**
     * @brief 指定节点，获取属性列表
     * @param node 节点对象
     * @return 节点获取的属性列表, 可以为空
     */
    Key get(const WDNode& node) const;
    /**
     * @brief 指定一个已存在的key，与当前节点获取的key做比较，如果不一致，返回false
     * @param node 节点
     * @param oldKey 已存在的key
     * @return 是否一致
     */
    bool compareGet(const WDNode& node, const Key& oldKey) const;
};

/**
 * @brief ODESP属性获取
 *  获取当前节点父节点的DESP属性值
 */
class DNodeODESPGet : public DNodeAttrGetBase
{
public:
    WDBMAttrValue get(const WDNode& node, bool* bOk = nullptr)  const override;
};
/**
 * @brief OPARAM属性获取
 *  获取当前节点父节点元件参数属性
 */
class DNodeOParamGet : public DNodeAttrGetBase
{
public:
    WDBMAttrValue get(const WDNode& node, bool* bOk = nullptr)  const override;
};
/**
 * @brief 起点朝向("Drnstart")属性获取
 *  这个属性本身没有特殊之处，但是数据由于会存在浮点精度误差，导致作为Key对比时由于误差导致本来很接近的朝向，无法复用
 *  eg: va = [0.000000001, 1.0, 0.0]; vb = [0.0000000002, 1.0, 0.0]
 *      如果舍去浮点误差，这里va和vb可以认为是相等的
 *  因此:这个Get对象就是尽可能地将这种误差舍弃，以保证va==vb
 */
class DNodeDrnstartGet : public DNodeAttrGetBase 
{
public:
    WDBMAttrValue get(const WDNode& node, bool* bOk = nullptr)  const override;
};
/**
 * @brief 终点朝向("Drnend")属性获取
 *  这个属性本身没有特殊之处，但是数据由于会存在浮点精度误差，导致作为Key对比时由于误差导致本来很接近的朝向，无法复用
 *  eg: va = [0.000000001, 1.0, 0.0]; vb = [0.0000000002, 1.0, 0.0]
 *      如果舍去浮点误差，这里va和vb可以认为是相等的
 *  因此:这个Get对象就是尽可能地将这种误差舍弃，以保证va==vb
 */
class DNodeDrnendGet : public DNodeAttrGetBase 
{
public:
    WDBMAttrValue get(const WDNode& node, bool* bOk = nullptr)  const override;
};
/**
 * @brief LOHE环高度数据属性获取
 */
class DNodeLOHEGet : public DNodeAttrGetBase
{
public:
    WDBMAttrValue get(const WDNode& node, bool* bOk = nullptr)  const override;
};


WD_NAMESPACE_END