#pragma once
#include "DSLCommon.h"

WD_NAMESPACE_BEGIN

class Context;
/**
 * @brief 值获取基类
 */
class ValueGetBase 
{
public:
	/**
	* @brief 指定变量名称(如果是数组类型变量，还需要指定数组下标)获取值
	* @param cxt 上下文对象, 存储此次解析的上下文数据
	* @param name 属性名称
	* @param outValue 输出的结果值
	* @param index 数组类型属性的下标, !注意: 这里的下标从1开始
	* @return 是否支持当前名称变量的获取
	*	!注意: 这里的返回值标识的是当前Get对象是否支持对应名称变量的获取
	*		  而非变量值是否获取成功, 因为变量值是否获取成功可以用outValue是否为空来判断
	*/
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const = 0;
protected:
	/**
	 * @brief 字符串类型值解析
	 */
	using FuncStringParse = std::function<std::optional<std::any>(const std::string& str, Context& cxt)>;
	/**
	 * @brief 指定属性值，转换为表达式使用的std::any值
	 * @param valueCRef 属性值
	 * @param index 下标, !注意: 这里的下标从1开始
	 * @return 结果
	 */
	static std::optional<std::any> GetAttrValue(Context& cxt
		, const WDBMAttrValue& value
		, const std::optional<int>& index = std::nullopt
		, const FuncStringParse& funcStrParse = FuncStringParse());
	/**
	 * @brief 获取节点的属性值
	 * @param node 节点
	 * @param name 属性名称
	 * @param index 如果指定了该索引，表示当前属性为数组类型的属性，且该索引是数组下标
	 *	!注意: 这里的下标从1开始
	 * @return 失败返回std::nullopt
	 */
	static std::optional<std::any> GetAttrValueByNode(Context& cxt
		, const WDNode& node
		, const WDBMAttrDesc& aDesc
		, const std::optional<int>& index = std::nullopt
		, const FuncStringParse& funcStrParse = FuncStringParse());
};

/**
 * @brief 解析值获取管理类
*/
class ValueGetMgr
{
public:
	/**
	 * @brief 变量值获取回调
	 * @param name 变量名称
	 * @param index 数组变量索引, 如果不是数组变量，index值为std::nullopt, !注意: 这里的下标从1开始
	 * @param outValue 输出结果，当结果值不存在时，需要赋值为 std::any()
	 *	目前any支持三种数据类型: int, float, std::string
	 * @return 是否支持当前变量的获取
	 *  !注意: 这里的返回值代表的是当前名称的变量是否支持获取，而非结果是否有效
	 */
	using ValueGetFunction = std::function<bool(const std::string& name, std::any& outValue, const std::optional<int>& index)>;
private:
	using ValueGetSPtr = std::shared_ptr<ValueGetBase>;
	using ValueGets = std::vector<ValueGetSPtr>;
	// 值获取对象
	ValueGets _vGets;
	// 值获取回调
	ValueGetFunction _vGetFunc;
public:
	/**
	 * @brief 值获取对象列表是否为空
	 */
	inline bool empty() const 
	{
		return _vGets.empty();
	}
	/**
	 * @brief 添加值获取对象
	 * @tparam TValueGetSubClass 值获取对象的具体实现类类型
	 * @return 结果
	 */
	template <typename TValueGetSubClass, typename ... Args>
	inline TValueGetSubClass& addValueGet(Args&&...args)
	{
		auto pRet = std::make_shared<TValueGetSubClass>(args...);
		_vGets.push_back(pRet);
		return *pRet;
	}
	/**
	 * @brief 设置值获取回调
	 */
	inline void setValueGetCallback(const ValueGetFunction& func = ValueGetFunction())
	{
		_vGetFunc = func;
	}
public:
	std::any getValue(Context& cxt
		, const std::string& name
		, const std::optional<int>& index = std::nullopt);
};



// 设计节点的全局变量属性获取对象
// eg: DDANGLE DDHEIGHT DDRADIUS
class GVarsValueGet : public ValueGetBase 
{
public:
	using GVarNameTable = std::unordered_map<std::string, std::string>;
private:
	GVarNameTable _map;
public:
	GVarsValueGet();
public:
	inline GVarNameTable& nameTable() 
	{
		return _map;
	}
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// 设计节点常规属性获取对象
// eg: 关键的 Height, Angle 等属性
class AttrValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// 设计节点Desp属性获取对象
class DespValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// 元件节点PARAM属性获取对象
class ParamValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// 设计节点ODesp属性获取对象
class ODespValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// 设计节点OParam属性获取对象
class OParamValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// 设计节点IParam属性获取对象
class IParamValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// 关键点集数据属性获取对象
class KeyPointValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// PWALLT壁厚数据属性获取对象
class PWALLTValueGet: public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// RPRO DATA数据属性获取对象
class RPRODataValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// LOHE环高度数据属性获取对象
class LOHEValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};

// CE当前节点数据属性获取对象
class CEValueGet : public ValueGetBase
{
public:
	virtual bool getValue(Context& cxt
		, const std::string& name
		, std::any& outValue
		, const std::optional<int>& index = std::nullopt) const override;
};


WD_NAMESPACE_END