#include "DNodeAttrKey.h"
#include "Context.h"
#include "../../../../node/WDNode.h"
#include "../../../typeMgr/WDBMTypeMgr.h"

WD_NAMESPACE_BEGIN

WDLoftSPline DNodeSPLineGet::get(const WDNode& node, bool* bOk) const
{
    SetValueToBooleanPtr(bOk, false);
    // 先判断是否是有起点和终点来进行放样的节点类型:如 SCTN, STWALL
    auto pADescPosS = node.getAttrDesc("Posstart");
    auto pADescPosE = node.getAttrDesc("Posend");
    if (pADescPosS != nullptr && pADescPosE != nullptr)
    {
        WDLoftSPline rLine;
        bool bOkS = false;
        bool bOkE = false;
        auto posS = pADescPosS->value(node).toDVec3(&bOkS);
        auto posE = pADescPosE->value(node).toDVec3(&bOkE);
        // 保证起点和终点均有效
        if (bOkS && bOkE)
        {
            auto lMat = node.localTransform();
            // 起点
            posS = lMat.inverse() * posS;
            V3EVNormalized(posS, false, 0.00001);
            rLine.addPoint(WDLoftSPline::Point(posS));
            // 终点
            posE = lMat.inverse() * posE;
            V3EVNormalized(posE, false, 0.00001);
            rLine.addPoint(WDLoftSPline::Point(posE));

            SetValueToBooleanPtr(bOk, true);
        }
        // 返回结果
        return rLine;
    }
    // 再判断是否是用SPINE类型的子节点来进行放样的节点类型:如 WALL, GENSEC
    // 获取到子节点(SPINE)以计算放样的样条曲线
    for (auto pChild : node.children())
    {
        // 如果节点下有多个Spine节点,这里用第一个
        if (pChild == nullptr || !pChild->isType("SPINE"))
            continue;
        WDLoftSPline rLine;
        // 获取SPINE的子节点: POINSP和CURVE节点
        // 其中子节点的排布方式为: POINSP CURVE POINSP CURVE POINSP ...
        // 表示: 每一组 POINSP CURVE POINSP 可能将生成一段圆弧(也有可能不会生成圆弧)
        for (auto& pSubChild : pChild->children())
        {
            if (pSubChild == nullptr)
                continue;
            if (pSubChild->isType("POINSP"))
            {
                WDLoftSPline::Point point;
                point.position = FVec3(pSubChild->getAttribute("Position").toDVec3());
                V3EVNormalized(point.position, false, 0.00001f);
                rLine.addPoint(point);
            }
            else if (pSubChild->isType("CURVE"))
            {
                WDLoftSPline::Curve curve;
                curve.type = WDLoftSPline::Curve::TypeFromString(pSubChild->getAttribute("CurType").toWord().c_str());
                curve.position = FVec3(pSubChild->getAttribute("Position").toDVec3());
                V3EVNormalized(curve.position, false, 0.00001f);
                curve.cPoint = FVec3(pSubChild->getAttribute("Cposition").toDVec3());
                V3EVNormalized(curve.cPoint, false, 0.00001f);
                curve.radius = static_cast<float>(pSubChild->getAttribute("Radius").toDouble());
                curve.radsetFlag = pSubChild->getAttribute("Radsetflag").toBool();
                curve.bulgeFactor = static_cast<float>(pSubChild->getAttribute("Bulgefactor").toDouble());
                rLine.addCurve(curve);
            }
            else
            {
                // 其他的子节点类型，这里忽略
                auto type = pSubChild->type();
                WDUnused(type);
                assert(false);
            }
        }
        // 曲线至少有两个控制点才有效
        if (rLine.cPoints().size() >= 2)
        {
            SetValueToBooleanPtr(bOk, true);
            return rLine;
        }
    }
    return WDLoftSPline();
}

DNodeAttrKey::Key DNodeAttrKey::get(const WDNode& node) const
{
    Key rKey;
    rKey.first.reserve(_attrs.size() + _aGets.size());
    // 自身获取的属性
    for (const auto* pADesc : _attrs)
    {
        if (pADesc == nullptr)
        {
            assert(false);
            rKey.first.push_back(WDBMAttrValue());
        }
        else
        {
            rKey.first.push_back(pADesc->value(node));
        }
    }
    // 特殊属性的Getter
    for (auto pGet : _aGets)
    {
        if (pGet == nullptr)
        {
            assert(false);
            rKey.first.push_back(WDBMAttrValue());
            continue;
        }
        rKey.first.push_back(pGet->get(node));
    }

    if (_pSplineGet != nullptr) 
        rKey.second = _pSplineGet->get(node);

    return rKey;
}
bool DNodeAttrKey::compareGet(const WDNode& node, const Key& oldKey) const
{
    // 首先比较样条线参数
    if (_pSplineGet != nullptr) 
    {
        if (!oldKey.second)
            return false;
        if (oldKey.second.value() != _pSplineGet->get(node))
            return false;
    }
    else
    {
        // 不存在样条线获取函数，但是之前的key存在样条线
        if (oldKey.second)
            return false;
    }

    // 再比较属性列表
    size_t allCnt = _attrs.size() + _aGets.size();
    if (oldKey.first.size() != allCnt)
    {
        // 这里一定要保证两个列表的长度一致的
        assert(false);
        return false;
    }

    // 这种是正常情况，说明这个元件生成模型时，没有用到设计模块的任何属性
    if (allCnt == 0 && oldKey.first.empty())
        return true;

    // 自身获取的属性
    size_t idx = 0;
    for (const auto* pADesc : _attrs)
    {
        // 已经超出范围了, 直接认为不一致
        if (idx >= oldKey.first.size())
            return false;
        if (pADesc == nullptr)
        {
            assert(false);
            if (oldKey.first[idx] != WDBMAttrValue())
                return false;
        }
        else
        {
            if (oldKey.first[idx] != pADesc->value(node))
                return false;
        }
        idx++;
    }
    // 特殊属性的Getter
    for (auto pGet : _aGets)
    {
        // 已经超出范围了, 直接认为不一致
        if (idx >= oldKey.first.size())
            return false;
        if (pGet == nullptr)
        {
            assert(false);
            if (oldKey.first[idx] != WDBMAttrValueCRef())
                return false;
        }
        else
        {
            if (oldKey.first[idx] != pGet->get(node))
                return false;
        }
        idx++;
    }
    return true;
}

WDBMAttrValue DNodeODESPGet::get(const WDNode& node, bool* bOk) const
{
    SetValueToBooleanPtr(bOk, false);
    // 获取父节点
    auto pParentNode = node.parent();
    if (pParentNode == nullptr)
        return WDBMAttrValue();
    auto pTypeDesc = pParentNode->getTypeDesc();
    if (pTypeDesc == nullptr)
        return WDBMAttrValue();
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get("DESP");
    if (pAttrDesc == nullptr)
        return WDBMAttrValue();
    SetValueToBooleanPtr(bOk, true);
    return pAttrDesc->value(*pParentNode);
}

WDBMAttrValue DNodeOParamGet::get(const WDNode& node, bool* bOk) const
{
    SetValueToBooleanPtr(bOk, false);
    auto pParentNode = node.parent();
    if (pParentNode == nullptr)
        return WDBMAttrValue();
    auto pScomNode = Context::GetSCOMNode(*pParentNode);
    if (pScomNode == nullptr)
        return WDBMAttrValue();
    auto pTypeDesc = pScomNode->getTypeDesc();
    if (pTypeDesc == nullptr)
        return WDBMAttrValue();
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get("Param");
    if (pAttrDesc == nullptr)
        return WDBMAttrValue();

    SetValueToBooleanPtr(bOk, true);
    return pAttrDesc->value(*pScomNode);
}

WDBMAttrValue DNodeDrnstartGet::get(const WDNode& node, bool* bOk) const
{
    SetValueToBooleanPtr(bOk, false);
    auto pTypeDesc = node.getTypeDesc();
    if (pTypeDesc == nullptr)
        return WDBMAttrValue();
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get("Drnstart");
    if (pAttrDesc == nullptr)
        return WDBMAttrValue();
    SetValueToBooleanPtr(bOk, true);
    bool bVOk = false;
    auto pos = pAttrDesc->value(node).toDVec3(&bVOk);
    if (bOk)
    {
        // 获取成功，归一化
        V3EVNormalized(pos, true, 0.000001);
        return WDBMAttrValue(pos);
    }
    else
    {
        return WDBMAttrValue();
    }
}

WDBMAttrValue DNodeDrnendGet::get(const WDNode& node, bool* bOk) const
{
    SetValueToBooleanPtr(bOk, false);
    auto pTypeDesc = node.getTypeDesc();
    if (pTypeDesc == nullptr)
        return WDBMAttrValue();
    // 根据名称查询属性描述
    auto pAttrDesc = pTypeDesc->get("Drnend");
    if (pAttrDesc == nullptr)
        return WDBMAttrValue();
    SetValueToBooleanPtr(bOk, true);
    bool bVOk = false;
    auto pos = pAttrDesc->value(node).toDVec3(&bVOk);
    if (bOk)
    {
        // 获取成功，归一化
        V3EVNormalized(pos, true, 0.000001);
        return WDBMAttrValue(pos);
    }
    else
    {
        return WDBMAttrValue();
    }
}

WDBMAttrValue DNodeLOHEGet::get(const WDNode& node, bool* bOk) const
{
    SetValueToBooleanPtr(bOk, false);

    auto pParent = node.parent();
    if (pParent == nullptr)
        WDBMAttrValue();
    // 向上找到有子节点是环类型("PLOO")的祖先节点, 再取环节点上的高度("Height")属性
    while (pParent != nullptr)
    {
        for (auto pChild : pParent->children())
        {
            if (pChild == nullptr || !pChild->isType("PLOO"))
                continue;
            auto pADesc = pChild->getAttrDesc("Height");
            if (pADesc == nullptr)
                continue;
            SetValueToBooleanPtr(bOk, true);
            return pADesc->value(*pChild);
        }
        pParent = pParent->parent();
    }
    return WDBMAttrValue();
}

WD_NAMESPACE_END

