#include "WDBMCModelBuilderP.h"
#include "../../WDBMCatalog.h"

WD_NAMESPACE_BEGIN


WDBMCModelBuilderP::WDBMCModelBuilderP(WDCore& core, WDBMCModelBuilder& d)
    : _core(core)
    , _d(d)
{
    _pDataParser = nullptr;
    // 初始化表达式解析对象
    DSLInit(_dsl);
}
WDBMCModelBuilderP::~WDBMCModelBuilderP()
{
}

DATAParser& WDBMCModelBuilderP::dtParser()
{
    if (_pDataParser == nullptr)
    {
        const auto& typeMgr = _core.getBMCatalog().typeMgr();
        _pDataParser = new DATAParser(typeMgr);
    }
    return *_pDataParser;
}
const PTParsers& WDBMCModelBuilderP::ptParsers()
{

#define PT_PARSES_ADD(Type) {\
    auto pTypeDesc = typeMgr.get(#Type);\
    assert(pTypeDesc != nullptr && pTypeDesc->id() != NumLimits<ushort>::Max);\
    if (pTypeDesc != nullptr && pTypeDesc->id() != NumLimits<ushort>::Max)\
    {\
        maxId = Max(maxId, pTypeDesc->id());\
        tmps.push_back({ pTypeDesc->id(), new PTParser##Type(typeMgr) });\
    }};

    if (_ptParsers.empty())
    {
        const auto& typeMgr = _core.getBMCatalog().typeMgr();
        ushort maxId = 0;
        std::vector<std::pair<ushort, PTParserBase*> > tmps;

        PT_PARSES_ADD(PTAX);
        PT_PARSES_ADD(PTCA);
        PT_PARSES_ADD(PTMI);
        PT_PARSES_ADD(PTPOS);

        _ptParsers.resize(maxId + 1, nullptr);
        for (const auto& t : tmps)
            _ptParsers[t.first] = t.second;

    }

#undef PT_PARSES_ADD

    return _ptParsers;
}
const PLineParsers& WDBMCModelBuilderP::plineParsers()
{

#define PLINE_PARSES_ADD(Type) {\
    auto pTypeDesc = typeMgr.get(#Type);\
    assert(pTypeDesc != nullptr && pTypeDesc->id() != NumLimits<ushort>::Max);\
    if (pTypeDesc != nullptr && pTypeDesc->id() != NumLimits<ushort>::Max)\
    {\
        maxId = Max(maxId, pTypeDesc->id());\
        tmps.push_back({ pTypeDesc->id(), new PLineParser##Type(typeMgr) });\
    }};

    if (_plineParsers.empty())
    {
        const auto& typeMgr = _core.getBMCatalog().typeMgr();
        ushort maxId = 0;
        std::vector<std::pair<ushort, PLineParserBase*> > tmps;

        PLINE_PARSES_ADD(PLIN);

        _plineParsers.resize(maxId + 1, nullptr);
        for (const auto& t : tmps)
            _plineParsers[t.first] = t.second;
    }

#undef PLINE_PARSES_ADD

    return _plineParsers;
}
const GMParsers& WDBMCModelBuilderP::gmParses()
{
#define GM_PARSES_ADD(Type){\
    auto pTypeDesc = typeMgr.get(#Type);\
    assert(pTypeDesc != nullptr && pTypeDesc->id() != NumLimits<ushort>::Max);\
    if (pTypeDesc != nullptr && pTypeDesc->id() != NumLimits<ushort>::Max)\
    {\
        maxId = Max(maxId, pTypeDesc->id());\
        tmps.push_back({ pTypeDesc->id(), new GMParser##Type(typeMgr) });\
    }};

    if (_gmParsers.empty())
    {
        const auto& typeMgr = _core.getBMCatalog().typeMgr();
        ushort maxId = 0;
        std::vector<std::pair<ushort, GMParserBase*> > tmps;

        GM_PARSES_ADD(SBOX);
        GM_PARSES_ADD(NSBO);

        GM_PARSES_ADD(SCON);
        GM_PARSES_ADD(NSCO);

        GM_PARSES_ADD(LCYL);
        GM_PARSES_ADD(NLCY);
        
        GM_PARSES_ADD(SCYL);
        GM_PARSES_ADD(NSCY);

        GM_PARSES_ADD(SSLC);
        GM_PARSES_ADD(NSSL);

        GM_PARSES_ADD(SDSH);

        GM_PARSES_ADD(LPYR);
        GM_PARSES_ADD(NLPY);

        GM_PARSES_ADD(SCTO);
        GM_PARSES_ADD(NSCT);

        GM_PARSES_ADD(SRTO);
        GM_PARSES_ADD(NSRT);

        GM_PARSES_ADD(LSNO);
        GM_PARSES_ADD(NLSN);

        GM_PARSES_ADD(SSPH);
        GM_PARSES_ADD(NSSP);

        GM_PARSES_ADD(SEXT);
        GM_PARSES_ADD(NSEX);

        GM_PARSES_ADD(SREV);
        GM_PARSES_ADD(NSRE);

        GM_PARSES_ADD(SREC);

        GM_PARSES_ADD(SANN);

        GM_PARSES_ADD(SPRO);

        _gmParsers.resize(maxId + 1, nullptr);
        for (const auto& t : tmps) 
            _gmParsers[t.first] = t.second;

    }

#undef GM_PARSES_ADD

    return _gmParsers;
}
ValueGetMgr& WDBMCModelBuilderP::dValueGetMgr()
{
    // 设计模块值解析管理对象
    if (_dValueGetMgr.empty())
    {
        // 元件节点PARAM属性获取对象
        _dValueGetMgr.addValueGet<ParamValueGet>();
        // 关键点集数据属性获取对象
        _dValueGetMgr.addValueGet<KeyPointValueGet>();
        // 设计节点Desp属性获取对象
        _dValueGetMgr.addValueGet<DespValueGet>();
        // 设计节点IParam属性获取对象
        _dValueGetMgr.addValueGet<IParamValueGet>();
        // RPRO DATA数据属性获取对象
        _dValueGetMgr.addValueGet<RPRODataValueGet>();
        // 设计节点的全局变量属性获取对象
        _dValueGetMgr.addValueGet<GVarsValueGet>();
        // 设计节点常规属性获取对象
        _dValueGetMgr.addValueGet<AttrValueGet>();
        // 设计节点ODesp属性获取对象
        _dValueGetMgr.addValueGet<ODespValueGet>();
        // 设计节点OParam属性获取对象
        _dValueGetMgr.addValueGet<OParamValueGet>();
        // PWALLT壁厚数据属性获取对象
        _dValueGetMgr.addValueGet<PWALLTValueGet>();
        // LOHE环高度数据属性获取对象
        _dValueGetMgr.addValueGet<LOHEValueGet>();
        // CE当前节点数据属性获取对象
        _dValueGetMgr.addValueGet<CEValueGet>();
    }
    // 无回调
    _dValueGetMgr.setValueGetCallback({});
    return _dValueGetMgr;
}
ValueGetMgr& WDBMCModelBuilderP::cValueGetMgr(const WDBMCModelBuilder::ValueGetFunction& func)
{
    if (_cValueGetMgr.empty()) 
    {
        // 元件节点PARAM属性获取对象
        _cValueGetMgr.addValueGet<ParamValueGet>();
        // 关键点集数据属性获取对象
        _cValueGetMgr.addValueGet<KeyPointValueGet>();
        // RPRO DATA数据属性获取对象
        _cValueGetMgr.addValueGet<RPRODataValueGet>();
    }
    // 设置回调
    _dValueGetMgr.setValueGetCallback(func);
    return _cValueGetMgr;
}

ValueGetMgr& WDBMCModelBuilderP::cbValueGetMgr(const WDBMCModelBuilder::ValueGetFunction& func)
{
    // 设置回调
    _cbValueGetMgr.setValueGetCallback(func);
    return _cbValueGetMgr;
}
SPLineParams WDBMCModelBuilderP::dSPLineParam(Context& cxt)
{
    if (cxt.node() == nullptr)
        return SPLineParams();

    const auto& node = *(cxt.node());
    SPLineParams rPar;
    // 起点朝向
    {
        DNodeDrnstartGet sGet;
        bool bOk = false;
        auto rValue = sGet.get(node, &bOk);
        if (bOk)
        {
            if (cxt.pDNodeAKey != nullptr)
                cxt.pDNodeAKey->appendAGet<DNodeDrnstartGet>();
            if (rValue.valid())
                rPar.drnS = FVec3(rValue.toDVec3());
        }
    }
    // 终点朝向
    {
        DNodeDrnendGet eGet;
        bool bOk = false;
        auto rValue = eGet.get(node, &bOk);
        if (bOk)
        {
            if (cxt.pDNodeAKey != nullptr)
                cxt.pDNodeAKey->appendAGet<DNodeDrnstartGet>();
            if (rValue.valid())
                rPar.drnE = FVec3(rValue.toDVec3());
        }
    }
    // 对齐线
    auto pADescJusline = node.getAttrDesc("Jusline");
    if (pADescJusline != nullptr)
    {
        if (cxt.pDNodeAKey != nullptr)
            cxt.pDNodeAKey->appendADesc(pADescJusline);
        bool bOk = false;
        auto rVal = pADescJusline->value(node).toWord(&bOk);
        if (bOk)
            rPar.justLine = rVal;
    }
    // BAngle在部分节点不作为模型的数据,而是作为生成矩阵的条件
    if (!node.isAnyOfType("SCTN", "STWALL"))
    {
        auto pADescBAngle = node.getAttrDesc("Bangle");
        if (pADescBAngle != nullptr)
        {
            if (cxt.pDNodeAKey != nullptr)
                cxt.pDNodeAKey->appendADesc(pADescBAngle);
            bool bOk = false;
            auto rVal = pADescBAngle->value(node).toDouble(&bOk);
            if (bOk)
                rPar.bAngle = static_cast<float>(rVal);
        }
    }
    // 获取样条线
    DNodeSPLineGet spLineGet;
    rPar.spline = spLineGet.get(node);
    // 添加样条线顶点获取对象
    if (cxt.pDNodeAKey != nullptr)
        cxt.pDNodeAKey->appendSPLineGet();

    return rPar;
}
SPLineParams WDBMCModelBuilderP::cSPLineParam(Context& cxt)
{
    WDUnused(cxt);
    SPLineParams rPar;
    rPar.drnS = WD::FVec3::AxisZ();
    rPar.drnE = WD::FVec3::AxisNZ();
    rPar.justLine = "NA";
    rPar.bAngle = 0.0f;
    rPar.spline.addPoint(WD::WDLoftSPline::Point(WD::DVec3::Zero()));
    rPar.spline.addPoint(WD::WDLoftSPline::Point(WD::DVec3(0.0f, 0.0f, 1000.0f)));
    return rPar;
}


bool WDBMCModelBuilderP::getModel(Context& cxt)
{
    auto pComNode = cxt.scomNode();
    assert(pComNode != nullptr && "注意: 获取元件模型时，传入的元件节点为 nullptr!");
    if (pComNode == nullptr)
        return false;

    // 获取引用的节点
    auto r = getSCOMRefNode(cxt, *pComNode);
    // 准备数据集列表
    if (r.pNodeDTSE != nullptr)
        cxt.dataSetCatch().init(r.pNodeDTSE);

    // 准备点集列表
    if (r.pNodePTSE != nullptr)
        cxt.keyPtSetCatch().init(r.pNodePTSE);

    // 解析点集列表
    if (r.pNodePTSE != nullptr)
        cxt.keyPtSetCatch().getAll(cxt.ret.keyPoints);

    // 解析PLine列表
    if (r.pNodePTSS != nullptr)
        ParsePTSS(*(r.pNodePTSS), cxt);

    // 解析型集列表
    if (r.pNodeGMs != nullptr)
        ParseGMs(*(r.pNodeGMs), cxt);

    // 解析负型集列表
    if (r.pNodeNGMS != nullptr)
        ParseNGMs(*(r.pNodeNGMS), cxt);

    return true;
}

bool WDBMCModelBuilderP::getInsuModel(Context& cxt)
{
    if (cxt.node() == nullptr)
        return false;
    auto pComNode = cxt.scomNode();
    if (pComNode == nullptr)
        return false;

    auto pADescIParam = cxt.node()->getAttrDesc("Iparam");
    if (pADescIParam == nullptr)
        return false;
    std::vector<double> iparams;
    if (!pADescIParam->value(*(cxt.node())).toDoubleVector(iparams))
        return false;
    if (iparams.empty())
        return false;

    // 获取引用的节点
    auto r = getSCOMRefNode(cxt, *pComNode);

    // 准备数据集列表
    if (r.pNodeDTSE != nullptr)
        cxt.dataSetCatch().init(r.pNodeDTSE);

    // 准备点集列表
    if (r.pNodePTSE != nullptr)
        cxt.keyPtSetCatch().init(r.pNodePTSE);

    // 解析型集列表
    if (r.pNodeGMs != nullptr)
        ParseGMs(*(r.pNodeGMs), cxt);

    return true;
}

void WDBMCModelBuilderP::contextInit(Context& cxt)
{
    auto pComNode = cxt.scomNode();
    if (pComNode != nullptr)
    {
        // 获取引用的节点
        auto r = getSCOMRefNode(cxt, *pComNode);
        // 准备数据集列表
        if (r.pNodeDTSE != nullptr)
            cxt.dataSetCatch().init(r.pNodeDTSE);
        // 准备点集列表
        if (r.pNodePTSE != nullptr)
            cxt.keyPtSetCatch().init(r.pNodePTSE);
    }
}

void WDBMCModelBuilderP::ParsePTSS(const WDNode& nodePTSS
    , Context& cxt)
{
    assert(nodePTSS.isType("PTSS"));

    // 准备计算所需的PLine参数
    const auto& rParam = cxt.splineParams();
    auto splineVs = rParam.spline.vertices();
    if (splineVs.size() < 2)
        return;
    const FVec3& posS = splineVs[0];
    const FVec3& posE = splineVs[1];
    const std::string& justLine = rParam.justLine;
    // 计算PLine线到终点的方向和距离
    FVec3 vec = FVec3(posE - posS);
    double length = vec.length();
    DVec3 direction = DVec3(vec.normalized());
    // 对齐点
    bool bJustPt = false;
    cxt.justPoint = DVec2::Zero();
    // 开始解析
    std::string errorStr;
    cxt.ret.pLines.reserve(nodePTSS.childCount());
    for (size_t i = 0; i < nodePTSS.childCount(); ++i)
    {
        WDNode::SharedPtr pPLINNode = nodePTSS.childAt(i);
        if (pPLINNode == nullptr || !PLineParserBase::IsPLineNode(*pPLINNode))
            continue;
        auto pParser = cxt.plineParser(*pPLINNode);
        if (pParser == nullptr)
        {
            PARSE_ASSERT(false);
            ParseNodeFaildLogPrint(cxt, *pPLINNode, "暂未支持解析!");
            continue;
        }
        auto rPLineOpt = pParser->build(cxt, *pPLINNode, length, direction);
        if (!rPLineOpt)
        {
            PARSE_ASSERT(false);
            continue;
        }
        auto& rPLine = rPLineOpt.value();
        if (justLine == rPLine.key())
        {
            cxt.justPoint = rPLine.sPosition.xy();
            bJustPt = true;
        }

        // 解析成功，添加PLine数据
        cxt.ret.pLines.push_back({ pPLINNode, rPLine });
    }
    // 应用对齐点
    if (bJustPt)
    {
        for (auto& pl : cxt.ret.pLines)
        {
            DVec3 tSPos = DVec3(pl.pLine.sPosition);
            DVec3 tEPos = DVec3(pl.pLine.ePosition);
            tSPos.setXy(tSPos.xy() - cxt.justPoint);
            tEPos.setXy(tEPos.xy() - cxt.justPoint);
            pl.pLine.sPosition = tSPos;
            pl.pLine.ePosition = tEPos;
        }
    }
}

void WDBMCModelBuilderP::ParseGMs(const WDNode& nodeGMs, Context& cxt)
{
    // 校验是否GMSE节点
    assert(nodeGMs.isType("GMSE") || nodeGMs.isType("GMSS"));
    // 几何体结果列表
    auto& rGeoms = cxt.ret.geoms;
    for (auto& pGMNode : nodeGMs.children())
    {
        if (pGMNode == nullptr)
            continue;
        auto pParser = cxt.gmParser(*pGMNode);
        if (pParser == nullptr)
        {
            // 已知还没有支持的类型, 这里跳过，不用输出日志
            if (pGMNode->isAnyOfType("BOXI", "LINE", "SDIS"))
                continue;

            ParseNodeFaildLogPrint(cxt, *pGMNode, "暂未支持解析!");
            continue;
        }
        // 校验型标志以及Level，以判定当前型是否需要生成几何体
        if (!pParser->check(*pGMNode, cxt.bInsu()))
            continue;
        // 生成几何体
        auto pRGeom = pParser->build(cxt, *pGMNode);
        if (pRGeom == nullptr)
            continue;
        // 加入
        rGeoms.push_back({});
        rGeoms.back().node = pGMNode;
        rGeoms.back().pGeom = pRGeom;
        // 解析型节点下挂载的负型列表(孔洞)
        {
            auto& gm = rGeoms.back();
            gm.nGeoms.reserve(pGMNode->childCount());
            for (auto& pNGMNode : pGMNode->children())
            {
                if (pNGMNode == nullptr)
                    continue;
                // 跳过非负型的类型
                if (pNGMNode->isAnyOfType("SPVE", "SLOO"))
                    continue;
                auto pNParser = cxt.gmParser(*pNGMNode);
                if (pNParser == nullptr)
                {
                    ParseNodeFaildLogPrint(cxt, *pNGMNode, "暂未支持解析!");
                    continue;
                }
                // 校验型标志以及Level，以判定当前型是否需要生成几何体
                if (!pNParser->check(*pNGMNode, cxt.bInsu()))
                    continue;

                // 生成几何体
                auto pRNGeom = pNParser->build(cxt, *pNGMNode);
                if (pRNGeom == nullptr)
                    continue;

                gm.nGeoms.push_back({ pNGMNode, pRNGeom });
            }
        }
    }
}
void WDBMCModelBuilderP::ParseNGMs(const WDNode& nodeNGMs, Context& cxt)
{
    // 校验是否GMSE节点
    assert(nodeNGMs.isType("NGMS"));
    // 几何体结果列表
    auto& rNGeoms = cxt.ret.nGeoms;
    for (size_t i = 0; i < nodeNGMs.childCount(); ++i)
    {
        auto pNGMNode = nodeNGMs.childAt(i);
        if (pNGMNode == nullptr)
            continue;
        auto pParser = cxt.gmParser(*pNGMNode);
        if (pParser == nullptr)
        {
            ParseNodeFaildLogPrint(cxt, *pNGMNode, "暂未支持解析!");
            continue;
        }
        // 校验型标志以及Level，以判定当前型是否需要生成几何体
        if (!pParser->check(*pNGMNode, cxt.bInsu()))
            continue;

        // 生成几何体
        auto pRGeom = pParser->build(cxt, *pNGMNode);
        if (pRGeom == nullptr)
            continue;

        rNGeoms.push_back({pNGMNode, pRGeom });
    }
}

WDBMCModelBuilderP::SCOMRefNodes WDBMCModelBuilderP::getSCOMRefNode(Context& cxt, const WDNode& scomNode)
{
    SCOMRefNodes ret;
    if (scomNode.isType("SCOM"))
    {
        struct SCOMCatch 
        {
            const WDBMAttrDesc* _pADescDtref = nullptr;
            const WDBMAttrDesc* _pADescPtref = nullptr;
            const WDBMAttrDesc* _pADescGmref = nullptr;
        public:
            SCOMCatch(const WDBMTypeMgr& typeMgr)
            {
                auto pTDesc = typeMgr.get("SCOM");
                if (pTDesc != nullptr)
                {
                    // 数据集节点
                    _pADescDtref = pTDesc->get("Dtref");
                    // 点集节点 
                    _pADescPtref = pTDesc->get("Ptref");
                    // 型集节点
                    _pADescGmref = pTDesc->get("Gmref");
                }
            }
        public:
            SCOMRefNodes get(const WDNode& node) const
            {
                SCOMRefNodes ret;
                // 数据集节点
                if (_pADescDtref != nullptr)
                    ret.pNodeDTSE = _pADescDtref->value(node).toNodeRef().refNode();
                // 点集节点 
                if (_pADescPtref != nullptr)
                    ret.pNodePTSE = _pADescPtref->value(node).toNodeRef().refNode();
                // 型集节点
                if (_pADescGmref != nullptr)
                    ret.pNodeGMs = _pADescGmref->value(node).toNodeRef().refNode();
                return ret;
            }
        };

        static SCOMCatch tCatch = SCOMCatch(_core.getBMCatalog().typeMgr());
        return tCatch.get(scomNode);
    }
    else if (scomNode.isType("SPRF"))
    {
        struct SPRFCatch
        {
            const WDBMAttrDesc* _pADescDtref = nullptr;
            const WDBMAttrDesc* _pADescPstref = nullptr;
            const WDBMAttrDesc* _pADescGstref = nullptr;
        public:
            SPRFCatch(const WDBMTypeMgr& typeMgr)
            {
                auto pTDesc = typeMgr.get("SPRF");
                if (pTDesc != nullptr)
                {
                    // 数据集节点
                    _pADescDtref = pTDesc->get("Dtref");
                    // PLine集节点
                    _pADescPstref = pTDesc->get("Pstref");
                    // 型集节点
                    _pADescGstref = pTDesc->get("Gstref");
                }
            }
        public:
            SCOMRefNodes get(const WDNode& node) const
            {
                SCOMRefNodes ret;
                if (_pADescDtref != nullptr)
                    ret.pNodeDTSE = _pADescDtref->value(node).toNodeRef().refNode();
                if (_pADescPstref != nullptr)
                    ret.pNodePTSS = _pADescPstref->value(node).toNodeRef().refNode();
                if (_pADescGstref != nullptr)
                    ret.pNodeGMs = _pADescGstref->value(node).toNodeRef().refNode();
                return ret;
            }
        };

        static SPRFCatch tCatch = SPRFCatch(_core.getBMCatalog().typeMgr());
        return tCatch.get(scomNode);
    }
    else if (scomNode.isType("SFIT"))
    {
        struct SFITCatch
        {
            const WDBMAttrDesc* _pADescDtref = nullptr;
            const WDBMAttrDesc* _pADescPtref = nullptr;
            const WDBMAttrDesc* _pADescGmref = nullptr;
            const WDBMAttrDesc* _pADescNgmref = nullptr;
        public:
            SFITCatch(const WDBMTypeMgr& typeMgr)
            {
                auto pTDesc = typeMgr.get("SFIT");
                if (pTDesc != nullptr)
                {
                    // 数据集节点
                    _pADescDtref = pTDesc->get("Dtref");
                    // 点集节点
                    _pADescPtref = pTDesc->get("Ptref");
                    // 型集节点
                    _pADescGmref = pTDesc->get("Gmref");
                    // 负型集节点
                    _pADescNgmref = pTDesc->get("Ngmref");
                }
            }
        public:
            SCOMRefNodes get(const WDNode& node) const
            {
                SCOMRefNodes ret;
                if (_pADescDtref != nullptr)
                    ret.pNodeDTSE = _pADescDtref->value(node).toNodeRef().refNode();
                if (_pADescPtref != nullptr)
                    ret.pNodePTSE = _pADescPtref->value(node).toNodeRef().refNode();
                if (_pADescGmref != nullptr)
                    ret.pNodeGMs = _pADescGmref->value(node).toNodeRef().refNode();
                if (_pADescNgmref != nullptr)
                    ret.pNodeNGMS = _pADescNgmref->value(node).toNodeRef().refNode();
                return ret;
            }
        };

        static SFITCatch tCatch = SFITCatch(_core.getBMCatalog().typeMgr());
        return tCatch.get(scomNode);
    }
    else if (scomNode.isType("JOIN"))
    {
        struct JOINCatch
        {
            const WDBMAttrDesc* _pADescDtref = nullptr;
            const WDBMAttrDesc* _pADescPstref = nullptr;
            const WDBMAttrDesc* _pADescPtref = nullptr;
            const WDBMAttrDesc* _pADescGmref = nullptr;
            const WDBMAttrDesc* _pADescNgmref = nullptr;
        public:
            JOINCatch(const WDBMTypeMgr& typeMgr)
            {
                auto pTDesc = typeMgr.get("JOIN");
                if (pTDesc != nullptr)
                {
                    // 数据集节点
                    _pADescDtref = pTDesc->get("Dtref");
                    // PLIN线集节点
                    _pADescPstref = pTDesc->get("Pstref");
                    // 点集节点
                    _pADescPtref = pTDesc->get("Ptref");
                    // 型集节点
                    _pADescGmref = pTDesc->get("Gmref");
                    // 负型集节点
                    _pADescNgmref = pTDesc->get("Ngmref");
                }
            }
        public:
            SCOMRefNodes get(const WDNode& node) const
            {
                SCOMRefNodes ret;
                if (_pADescDtref != nullptr)
                    ret.pNodeDTSE = _pADescDtref->value(node).toNodeRef().refNode();
                if (_pADescPstref != nullptr)
                    ret.pNodePTSS = _pADescPstref->value(node).toNodeRef().refNode();
                if (_pADescPtref != nullptr)
                    ret.pNodePTSE = _pADescPtref->value(node).toNodeRef().refNode();
                if (_pADescGmref != nullptr)
                    ret.pNodeGMs = _pADescGmref->value(node).toNodeRef().refNode();
                if (_pADescNgmref != nullptr)
                    ret.pNodeNGMS = _pADescNgmref->value(node).toNodeRef().refNode();
                return ret;
            }
        };

        static JOINCatch tCatch = JOINCatch(_core.getBMCatalog().typeMgr());
        return tCatch.get(scomNode);
    }
    else
    {
        ParseNodeFaildLogPrint(cxt, scomNode, u8"元件的解析未支持!");
        assert(false && "注意: 构建元件模型时，出现了意料之外的元件节点类型！");
    }
    return ret;
}

WD_NAMESPACE_END

