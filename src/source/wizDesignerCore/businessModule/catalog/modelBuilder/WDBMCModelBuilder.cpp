#include "WDBMCModelBuilder.h"
#include "private/WDBMCModelBuilderP.h"
#include "../../design/WDBMDesign.h"


WD_NAMESPACE_BEGIN

/***************************** WDBMCModelBuilder ******************************/

class CAttributeGetP 
{
public:
    Context cxt;
public:
    CAttributeGetP(WDCore& core
        , const WDNode* pDNode
        , const WDNode* pSPCONode
        , WDNode::SharedPtr pSCOMNode
        , WIZDesignerDSL::DSLContext& dslCxt
        , DATAParser& dtParser
        , const PTParsers& ptParsers
        , const PLineParsers& plineParsers
        , const GMParsers& gmParsers
        , ValueGetMgr& valueGetMgr)
        : cxt(core, pDNode, pSPCONode, pSCOMNode, dslCxt
            , dtParser, ptParsers, plineParsers, gmParsers
            , valueGetMgr, false)
    {
        cxt.pDNodeAKey = nullptr;
    }
};

CAttributeGet::CAttributeGet()
{
    _p = nullptr;
}
CAttributeGet::~CAttributeGet()
{
    _p = nullptr;
}

WDBMAttrValue CAttributeGet::getAttribute(const WDNode& node, const std::string& attrName, bool* bOk)
{
    SetValueToBooleanPtr(bOk, false);
    if (_p == nullptr)
        return WDBMAttrValue();
    auto ret = node.getAttribute(attrName);
    // 只有字符串类型才可能是表达式，其他类型直接返回属性值即可
    WDBMAttrValueType type = ret.type();
    switch (type)
    {
    case WD::T_String:
        {
            // 走到这里说明已经算是执行成功
            SetValueToBooleanPtr(bOk, true);
            std::string outV;
            if (!ret.toString(outV))
                return ret;
            if (IsUnsetValue(outV))
                return WDBMAttrValue();
            std::any rValue;
            std::string errorStr;

            // 此次解析不报告错误
            _p->cxt.bSilence = true;
            if (!DSLExec(_p->cxt.dsl, outV, rValue, errorStr))
            {
                _p->cxt.bSilence = false;
                return ret;
            }
            _p->cxt.bSilence = false;
            if (!rValue.has_value())
                return ret;
            // 是否float类型
            auto pFloat = std::any_cast<float>(&rValue);
            if (pFloat != nullptr)
                return WDBMAttrValue(static_cast<double>(*pFloat));
            // 是否string类型
            auto pString = std::any_cast<std::string>(&rValue);
            if (pString != nullptr)
                return WDBMAttrValue(*pString);
            // 是否int类型
            auto pInt = std::any_cast<int>(&rValue);
            if (pInt != nullptr)
                return WDBMAttrValue(*pInt);
            // 返回原始值
            return ret;
        }
        break;
    default:
        break;
    }
    // 其他类型，说明不是表达式，直接返回值
    SetValueToBooleanPtr(bOk, true);
    return ret;
}
WDBMAttrValue CAttributeGet::getAttribute(const WDNode& node, const std::string& attrName, int index, bool* bOk)
{
    SetValueToBooleanPtr(bOk, false);
    if (_p == nullptr)
        return WDBMAttrValue();
    auto ret = node.getAttribute(attrName);
    // 只有字符串类型才可能是表达式，其他类型直接返回属性值即可
    WDBMAttrValueType type = ret.type();
    switch (type)
    {
    case WD::T_IntVector:
        {
            bool tOk = false;
            auto outV = ret.toIntVector(&tOk);
            // 属性获取失败
            if (!tOk)
                return WDBMAttrValue();
            // 数组越界
            if (index < 0 || index > outV.size())
                return WDBMAttrValue();
            // 走到这里说明已经算是执行成功
            SetValueToBooleanPtr(bOk, true);
            return WDBMAttrValue(outV[index]);
        }
        break;
    case WD::T_DoubleVector:
        {
            bool tOk = false;
            auto outV = ret.toDoubleVector(&tOk);
            // 属性获取失败
            if (!tOk)
                return WDBMAttrValue();
            // 数组越界
            if (index < 0 || index > outV.size())
                return WDBMAttrValue();
            // 走到这里说明已经算是执行成功
            SetValueToBooleanPtr(bOk, true);
            return WDBMAttrValue(outV[index]);
        }
        break;
    case WD::T_StringVector:
        {
            bool tOk = false;
            auto outV = ret.toStringVector(&tOk);
            // 属性获取失败
            if (!tOk)
                return WDBMAttrValue();
            // 数组越界
            if (index < 0 || index > outV.size())
                return WDBMAttrValue();
            // 走到这里说明已经算是执行成功
            SetValueToBooleanPtr(bOk, true);
            const auto& rStr = outV[index];
            if (IsUnsetValue(rStr))
                return WDBMAttrValue();
            std::any rValue;
            std::string errorStr;
            // 此次解析不报告错误
            _p->cxt.bSilence = true;
            if (!DSLExec(_p->cxt.dsl, rStr, rValue, errorStr))
            {
                _p->cxt.bSilence = false;
                return WDBMAttrValue(rStr);
            }
            _p->cxt.bSilence = false;

            if (!rValue.has_value())
                return WDBMAttrValue(rStr);
            // 是否float类型
            auto pFloat = std::any_cast<float>(&rValue);
            if (pFloat != nullptr)
                return WDBMAttrValue(static_cast<double>(*pFloat));
            // 是否string类型
            auto pString = std::any_cast<std::string>(&rValue);
            if (pString != nullptr)
                return WDBMAttrValue(*pString);
            // 是否int类型
            auto pInt = std::any_cast<int>(&rValue);
            if (pInt != nullptr)
                return WDBMAttrValue(*pInt);
            // 返回原始值
            return WDBMAttrValue(rStr);
        }
        break;
    case WD::T_NodeRefs:
        {
            bool tOk = false;
            auto outV = ret.toNodeRefVector(&tOk);
            // 属性获取失败
            if (!tOk)
                return WDBMAttrValue();
            // 数组越界
            if (index < 0 || index > outV.size())
                return WDBMAttrValue();
            // 走到这里说明已经算是执行成功
            SetValueToBooleanPtr(bOk, true);
            return WDBMAttrValue(outV[index]);
        }
        break;
    default:
        break;
    }
    // 其他类型，直接返回值
    SetValueToBooleanPtr(bOk, true);
    return ret;
}

WDBMAttrValue CAttributeGet::execExpression(const std::string& expr, bool* bOk, std::string* pOutError)
{
    std::any rValue;
    std::string errStr;
    // 此次解析不报告错误
    _p->cxt.bSilence = true;
    auto ret = DSLExec(_p->cxt.dsl, expr, rValue, errStr);
    _p->cxt.bSilence = false;
    SetValueToBooleanPtr(bOk, ret);
    if (!ret)
    {
        if (pOutError != nullptr)
            *pOutError = errStr;
        return WDBMAttrValue(expr);
    }

    // 是否float类型
    auto pFloat = std::any_cast<float>(&rValue);
    if (pFloat != nullptr)
        return WDBMAttrValue(static_cast<double>(*pFloat));
    // 是否string类型
    auto pString = std::any_cast<std::string>(&rValue);
    if (pString != nullptr)
        return WDBMAttrValue(*pString);
    // 是否int类型
    auto pInt = std::any_cast<int>(&rValue);
    if (pInt != nullptr)
        return WDBMAttrValue(*pInt);

    return WDBMAttrValue(expr);
}

WDBMCModelBuilder::CMData WDBMCModelBuilder::CMData::FromCMNData(const CMNData& data)
{
    CMData ret;
    // 关键点列表
    ret.keyPoints.reserve(data.keyPoints.size());
    for (const auto& i : data.keyPoints)
    {
        assert(i.node.lock() != nullptr);
        ret.keyPoints.push_back(i.keyPoint);
    }
    // PLine线数据列表
    ret.pLines.reserve(data.pLines.size());
    for (const auto& i : data.pLines)
    {
        assert(i.node.lock() != nullptr);
        ret.pLines.push_back(i.pLine);
    }
    // 正实体数据列表
    ret.geoms.reserve(data.geoms.size());
    for (const auto& i : data.geoms)
    {
        assert(i.node.lock() != nullptr);
        ret.geoms.push_back(GData());
        ret.geoms.back().pGeom = i.pGeom;
        // 正实体附带的负实体数据列表
        ret.geoms.back().nGeoms.reserve(i.nGeoms.size());
        for (const auto& n : i.nGeoms) 
        {
            assert(n.node.lock() != nullptr);
            ret.geoms.back().nGeoms.push_back(n.pGeom);
        }
    }
    // 负实体数据列表
    ret.nGeoms.reserve(data.nGeoms.size());
    for (const auto& i : data.nGeoms)
    {
        assert(i.node.lock() != nullptr);
        ret.nGeoms.push_back(i.pGeom);
    }
    return ret;
}

WDBMCModelBuilder::WDBMCModelBuilder(WDCore& core)
{
    _p = new WDBMCModelBuilderP(core, *this);
}
WDBMCModelBuilder::~WDBMCModelBuilder()
{
    delete _p;
    _p = nullptr;
}

WDBMCModelBuilder::CMNData  WDBMCModelBuilder::build(WDNode::SharedPtr pScomNode
    , const ValueGetFunction& vGet)
{
    if (pScomNode == nullptr)
    {
        assert(false);
        return CMNData();
    }

    // 构建上下文对象
    Context cxt(_p->_core
        , nullptr   // 设计节点
        , nullptr   // 等级节点
        , pScomNode // 元件节点
        , _p->dsl()
        , _p->dtParser()
        , _p->ptParsers()
        , _p->plineParsers()
        , _p->gmParses()
        , _p->cValueGetMgr(vGet)
        , false
        , [=](Context& cxt) 
        {
            return _p->cSPLineParam(cxt);
        });

    _p->getModel(cxt);

    return std::move(cxt.ret);
}

WDBMCModelBuilder::SharedModel WDBMCModelBuilder::get(const WDNode& node
    , SharedModel pOldModel)
{
    if (node.getTypeDesc() == nullptr)
    {
        assert(false);
        return nullptr;
    }
    // 获取元件节点
    WDNode::SharedPtr pScomNode = Context::GetSCOMNode(node);
    if (pScomNode == nullptr)
        return nullptr;

    // 构建上下文对象
    Context cxt(_p->_core
        , &node // 设计节点
        , Context::GetSPCONode(node).get() // 等级节点
        , pScomNode // 元件节点
        , _p->dsl()
        , _p->dtParser()
        , _p->ptParsers()
        , _p->plineParsers()
        , _p->gmParses()
        , _p->dValueGetMgr()
        , false
        , [=](Context& cxt)
        {
            return _p->dSPLineParam(cxt);
        });

    using MBP = WDBMCModelBuilderP;
    auto fItr = _p->_scomMap.find(pScomNode); 
    if (fItr != _p->_scomMap.end())
    {
        MBP::DTDescMap& dMap = fItr->second;
        auto fDItr = dMap.find(node.getTypeDesc());
        if (fDItr != dMap.end() && fDItr->second != nullptr) 
        {
            MBP::CatchM& sData = *(fDItr->second);
            // 可以共享，查询共享数据
            if (sData.sharedEnabled())
            {
                // 如果存在已生成的模型，并且当前节点获取的模型Key与已存在模型的一致，则返回已生成的模型
                if (pOldModel != nullptr && sData.compareModelKey(node, pOldModel))
                    return pOldModel;
                // 查询到对应类型, 先根据设计节点，获取缓存key，如果对应key的数据已存在，则直接返回元件模型
                auto rModel = sData.queryModel(node);
                if (rModel != nullptr)
                    return rModel;
                cxt.pDNodeAKey = nullptr;
                // 否则，走解析流程, 再缓存结果并返回
                _p->getModel(cxt);
                auto tRet = CMData::FromCMNData(cxt.ret);
                if (tRet.empty())
                    return nullptr; // 证明没有数据
                return sData.catchModel(node, std::move(tRet));
            }
            // 不能共享，直接走解析流程并使用结果
            else
            {
                cxt.pDNodeAKey = nullptr;
                // 否则，走解析流程, 再缓存结果并返回
                _p->getModel(cxt);
                auto tRet = CMData::FromCMNData(cxt.ret);
                if (tRet.empty())
                    return nullptr; // 证明没有数据
                auto pRModel = std::make_shared<WDBMCModelBuilder::SharedObject>(std::move(tRet));;
                return pRModel;
            }
        }
        else 
        {
            cxt.pDNodeAKey = std::make_shared<DNodeAttrKey>();
            // 未查找到对应的元件, 先走解析流程, 再缓存结果并返回
            _p->getModel(cxt);
            auto tRet = CMData::FromCMNData(cxt.ret);
            if (tRet.empty())
                return nullptr; // 证明没有数据
            auto pSData = MBP::CatchM::MakeShared();
            pSData->setAKey(cxt.pDNodeAKey);
            // 结果模型
            SharedModel pRModel = nullptr;
            // 可以共享，进行缓存
            if (cxt.pDNodeAKey->sharedEnabled())
                pRModel = pSData->catchModel(node, std::move(tRet));
            // 不能共享, 直接创建模型
            else
                pRModel = std::make_shared<WDBMCModelBuilder::SharedObject>(std::move(tRet));
            // 缓存数据
            dMap[node.getTypeDesc()] = pSData;
            return pRModel;
        }
    }
    else
    {
        cxt.pDNodeAKey = std::make_shared<DNodeAttrKey>();
        // 未查找到对应的元件, 先走解析流程, 再缓存结果并返回
        _p->getModel(cxt);
        auto tRet = CMData::FromCMNData(cxt.ret);
        if (tRet.empty())
            return nullptr; // 证明没有数据
        auto pSData = MBP::CatchM::MakeShared();
        pSData->setAKey(cxt.pDNodeAKey);
        // 结果模型
        SharedModel pRModel = nullptr;
        // 可以共享，进行缓存
        if (cxt.pDNodeAKey->sharedEnabled())
            pRModel = pSData->catchModel(node, std::move(tRet));
        // 不能共享, 直接创建模型
        else
            pRModel = std::make_shared<WDBMCModelBuilder::SharedObject>(std::move(tRet));
        // 缓存元件,类型以及解析数据
        MBP::DTDescMap dMap;
        dMap[node.getTypeDesc()] = pSData;
        _p->_scomMap[pScomNode] = std::move(dMap);

        return pRModel;
    }
}
WDBMCModelBuilder::SharedModelI WDBMCModelBuilder::getInsu(const WDNode& node
    , SharedModelI pOldModel)
{
    auto pTypeDesc = node.getTypeDesc();
    if (pTypeDesc == nullptr)
    {
        assert(false);
        return nullptr;
    }
    // 首先判断节点是否具有保温属性
    auto pADescIParam = pTypeDesc->get("Iparam");
    if (pADescIParam == nullptr)
        return nullptr;
    std::vector<double> iparams;
    if (!pADescIParam->value(node).toDoubleVector(iparams))
        return nullptr;
    if (iparams.empty())
        return nullptr;
    bool bHadInsu = false;
    // 判断是否可能具有保温厚度
    for (auto i : iparams)
    {
        if (i > NumLimits<float>::Epsilon) 
        {
            bHadInsu = true;
            break;
        }
    }
    if (!bHadInsu)
        return nullptr;


    // 获取元件节点
    WDNode::SharedPtr pScomNode = Context::GetSCOMNode(node);
    if (pScomNode == nullptr)
        return nullptr;

    // 构建上下文对象
    Context cxt(_p->_core
        , &node // 设计节点
        , Context::GetSPCONode(node).get() // 等级节点
        , pScomNode // 元件节点
        , _p->dsl()
        , _p->dtParser()
        , _p->ptParsers()
        , _p->plineParsers()
        , _p->gmParses()
        , _p->dValueGetMgr()
        , true
        , [=](Context& cxt)
        {
            return _p->dSPLineParam(cxt);
        });

    using MBP = WDBMCModelBuilderP;
    auto fItr = _p->_scomMapI.find(pScomNode);
    if (fItr != _p->_scomMapI.end())
    {
        MBP::DTDescMapI& dMap = fItr->second;
        auto fDItr = dMap.find(node.getTypeDesc());
        if (fDItr != dMap.end() && fDItr->second != nullptr)
        {
            MBP::CatchI& sData = *(fDItr->second);
            // 可以共享，查询共享数据
            if (sData.sharedEnabled())
            {
                // 如果存在已生成的模型，并且当前节点获取的模型Key与已存在模型的一致，则返回已生成的模型
                if (pOldModel != nullptr && sData.compareModelKey(node, pOldModel))
                    return pOldModel;
                // 查询到对应类型, 先根据设计节点，获取缓存key，如果对应key的数据已存在，则直接返回元件模型
                auto rModel = sData.queryModel(node);
                if (rModel != nullptr)
                    return rModel;
                cxt.pDNodeAKey = nullptr;
                // 否则，走解析流程, 再缓存结果并返回
                _p->getInsuModel(cxt);
                auto tRet = CMData::FromCMNData(cxt.ret);
                if (tRet.empty())
                    return nullptr; // 证明没有数据
                return sData.catchModel(node, std::move(tRet.geoms));
            }
            // 不能共享，直接走解析流程并使用结果
            else
            {
                cxt.pDNodeAKey = nullptr;
                // 否则，走解析流程, 再缓存结果并返回
                _p->getInsuModel(cxt);
                auto tRet = CMData::FromCMNData(cxt.ret);
                if (tRet.empty())
                    return nullptr; // 证明没有数据
                auto pRModel = std::make_shared<WDBMCModelBuilder::SharedObjectI>(std::move(tRet.geoms));;
                return pRModel;
            }
        }
        else
        {
            cxt.pDNodeAKey = std::make_shared<DNodeAttrKey>();
            // 未查找到对应的元件, 先走解析流程, 再缓存结果并返回
            _p->getInsuModel(cxt);
            auto tRet = CMData::FromCMNData(cxt.ret);
            if (tRet.empty())
                return nullptr; // 证明没有数据
            auto pSData = MBP::CatchI::MakeShared();
            pSData->setAKey(cxt.pDNodeAKey);
            // 结果模型
            SharedModelI pRModel = nullptr;
            // 可以共享，进行缓存
            if (cxt.pDNodeAKey->sharedEnabled())
                pRModel = pSData->catchModel(node, std::move(tRet.geoms));
            // 不能共享, 直接创建模型
            else
                pRModel = std::make_shared<WDBMCModelBuilder::SharedObjectI>(std::move(tRet.geoms));
            // 缓存数据
            dMap[node.getTypeDesc()] = pSData;
            return pRModel;
        }
    }
    else
    {
        cxt.pDNodeAKey = std::make_shared<DNodeAttrKey>();
        // 未查找到对应的元件, 先走解析流程, 再缓存结果并返回
        _p->getInsuModel(cxt);
        auto tRet = CMData::FromCMNData(cxt.ret);
        if (tRet.empty())
            return nullptr; // 证明没有数据
        auto pSData = MBP::CatchI::MakeShared();
        pSData->setAKey(cxt.pDNodeAKey);
        // 结果模型
        SharedModelI pRModel = nullptr;
        // 可以共享，进行缓存
        if (cxt.pDNodeAKey->sharedEnabled())
            pRModel = pSData->catchModel(node, std::move(tRet.geoms));
        // 不能共享, 直接创建模型
        else
            pRModel = std::make_shared<WDBMCModelBuilder::SharedObjectI>(std::move(tRet.geoms));
        // 缓存元件,类型以及解析数据
        MBP::DTDescMapI dMap;
        dMap[node.getTypeDesc()] = pSData;
        _p->_scomMapI[pScomNode] = std::move(dMap);

        return pRModel;
    }
}

CAttributeGet WDBMCModelBuilder::cAttributeGet(const WDNode& desiNode)
{
    CAttributeGet cAGet;

    cAGet._p = std::make_shared<CAttributeGetP>(_p->_core
        , &desiNode
        , Context::GetSPCONode(desiNode).get()
        , Context::GetSCOMNode(desiNode)
        , _p->dsl()
        , _p->dtParser()
        , _p->ptParsers()
        , _p->plineParsers()
        , _p->gmParses()
        , _p->dValueGetMgr());
    // 初始化cxt
    _p->contextInit(cAGet._p->cxt);
    return cAGet;
}

CAttributeGet WDBMCModelBuilder::cAttributeGet(WDNode::SharedPtr pDesiNode
    , WDNode::SharedPtr pCataSPNode 
    , WDNode::SharedPtr pCataScomNode )
{
    CAttributeGet cAGet;

    auto pSPNode = pCataSPNode;
    if (pSPNode == nullptr && pDesiNode != nullptr)
        pSPNode = Context::GetSPCONode(*pDesiNode);

    auto pSComNode = pCataScomNode;
    if (pSComNode == nullptr && pSPNode != nullptr)
        pSComNode = Context::GetSCOMNodeBySPCONode(*pSPNode);

    if (pSComNode == nullptr && pDesiNode != nullptr)
        pSComNode = Context::GetSCOMNode(*pSPNode);

    cAGet._p = std::make_shared<CAttributeGetP>(_p->_core
        , pDesiNode.get()
        , pSPNode.get()
        , pSComNode
        , _p->dsl()
        , _p->dtParser()
        , _p->ptParsers()
        , _p->plineParsers()
        , _p->gmParses()
        , _p->dValueGetMgr());
    // 初始化cxt
    _p->contextInit(cAGet._p->cxt);

    return cAGet;
}

CAttributeGet WDBMCModelBuilder::cAttributeGet(const ValueGetFunction& function)
{
    CAttributeGet cAGet;

    cAGet._p = std::make_shared<CAttributeGetP>(_p->_core
        , nullptr
        , nullptr
        , nullptr
        , _p->dsl()
        , _p->dtParser()
        , _p->ptParsers()
        , _p->plineParsers()
        , _p->gmParses()
        , _p->cbValueGetMgr(function));
    // 初始化cxt
    _p->contextInit(cAGet._p->cxt);

    return cAGet;
}

WD_NAMESPACE_END

