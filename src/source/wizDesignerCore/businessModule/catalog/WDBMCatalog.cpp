#include "WDBMCatalog.h"

#include "../../WDCore.h"

#include "../typeMgr/WDBMTypeMgr.h"
#include "../typeMgr/WDBMAttrEnumDictionary.h"
#include "modelBuilder/WDBMCModelBuilder.h"
#include "../WDBMRefCollector.h"

#include "private/WDBMCCataGMBase.h"
#include "private/WDBMCCataPLIN.h"
#include "private/WDBMCCataPTBase.h"

WD_NAMESPACE_BEGIN

void RegistObjects(WDBMCatalog& bmCata)
{
    auto& typeMgr = bmCata.typeMgr();
    // 型，重新指定这些类型的创建函数
    std::vector<std::string> gmTypes = { "SBOX", "BOXI", "SCON", "LCYL", "SCYL", "SSLC", "TUBE", "SDIS"
        , "SDSH", "SLINE", "LINE", "LPYR", "SCTO", "SRTO", "LSNO", "SSPH", "SEXT", "SREV"
        , "NSBO", "NBOX", "NSCO", "NLCY", "NSCY", "NSSL", "NTUB", "NLPY", "NSCT", "NSRT", "NLSN", "NSSP", "NSEX", "NSRE", "NSDS"
        , "SREC", "SANN", "SPRO" };
    for (const auto& type : gmTypes)
    {
        auto pTypeDesc = typeMgr.get(type);
        if (pTypeDesc == nullptr)
        {
            assert(false);
            continue;
        }
        pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMCCataGMBase(node);
            });
    }
    // 点，重新指定这些类型的创建函数
    std::vector<std::string> ptTypes = { "PTAX", "PTCA", "PTMI", "PTPOS" };
    for (const auto& type : ptTypes)
    {
        auto pTypeDesc = typeMgr.get(type);
        if (pTypeDesc == nullptr)
        {
            assert(false);
            continue;
        }
        pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMCCataPTBase(node);
            });
    }
    // PLine线，重新指定这些类型的创建函数
    std::vector<std::string> plineTypes = { "PLIN" };
    for (const auto& type : plineTypes)
    {
        auto pTypeDesc = typeMgr.get(type);
        if (pTypeDesc == nullptr)
        {
            assert(false);
            continue;
        }
        pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMCCataPLIN(node);
            });
    }
}

WDBMCatalog::WDBMCatalog(WDCore& core)
    : WDBMBase(core, "Catalog")
{
    _pModelBuilder  =   new WDBMCModelBuilder(core);
    _despMgr        =   new WDBMDespMgr(core);
}
WDBMCatalog::~WDBMCatalog()
{
    if (_pModelBuilder != nullptr)
    {
        delete _pModelBuilder;
        _pModelBuilder = nullptr;
    }
    if (_despMgr != nullptr)
    {
        delete _despMgr;
        _despMgr = nullptr;
    }
}

bool WDBMCatalog::initP()
{
    auto& core = this->core();

    // 注册 空间占有属性字典
    this->attrEnumDictMgr().get("Obstruction").set({ {"None", int(0)}, {"Soft", int(1)}, {"Hard", int(2)} });

    // 先从配置文件中加载类型配置
    std::string typeNodeCfgFile = std::string(core.dataDirPath()) + std::string("nodeTypeConfig/nodeTypeConfigCatalog.xml");
    if (!LoadTypeNodeConfigFromXMLV1(typeNodeCfgFile, this->typeMgr()))
    {
        assert(false && "类型配置加载失败!");
        return false;
    }

    // 再通过代码设置类型配置
    RegistObjects(*this);

    return true;
}

WD::WDNode::SharedPtr WDBMCatalog::createRoot()
{
    // 元件根节点
    auto pCataRoot = this->create("WORL", "元件库根节点");
    if (pCataRoot != nullptr)
    {
        // 根节点用一个固定的uuid
        pCataRoot->setUuid(WDUuid::FromString("06DEAAA1-77E5-4C74-A680-6C688160724E"));
        // 移除根节点的编辑属性
        pCataRoot->setFlags(pCataRoot->flags().removeFlags(WDNode::F_Editted, WDNode::F_EdittedStatus, WDNode::F_Created));
    }
    return pCataRoot;
}
void WDBMCatalog::uninitP()
{
}

std::vector<std::string> WDBMCatalog::collectTranslationFiles() const
{
    const char* dataPath = this->core().dataDirPath();

    std::vector<std::string> rFiles;
    rFiles.reserve(2);

    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "%s/translate/zh_cn/Catalog/Type.xml", dataPath);
    rFiles.push_back(buf);
    sprintf_s(buf, sizeof(buf), "%s/translate/zh_cn/Catalog/Attribute.xml", dataPath);
    rFiles.push_back(buf);
    sprintf_s(buf, sizeof(buf), "%s/translate/zh_cn/Catalog/EnumDictionaryKey.xml", dataPath);
    rFiles.push_back(buf);

    return rFiles;
}

void WDBMCatalog::updateNodeRef(WDBMRefUpdater& collector) const
{
    // 更新元件库节点引用
    collector.updateCRefs(*this);
}


WD_NAMESPACE_END 