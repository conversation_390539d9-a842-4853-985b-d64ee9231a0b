#pragma once

#include "../../WDBDBase.h"
#include "../../../graphable/WDGraphableInterface.h"

WD_NAMESPACE_BEGIN
/**
* @brief PLIN 元件库 钢结构 点
*/
class WD_API WDBMCCataPLIN : public WDBDBase
    , public WDGraphableInterface
{
private:
    // 存储生成的PLine线列表, 用于绘制
    WDPLines _pLines;
public:
    WDBMCCataPLIN(WDNode& node)
        : WDBDBase(node)
    {
    }
    ~WDBMCCataPLIN() 
    {
    }
public:
    virtual WDBDBase* clone(WDNode& node) const override;
public:
    /**
     * @brief 设置PLine列表, 用于绘制
    */
    inline void setPLines(const WDPLines& pLines)
    {
        _pLines = pLines;
    }
    /**
     * @brief 清空用于绘制的PLine列表
    */
    inline void resetPLines()
    {
        WDPLines().swap(_pLines);
    }
public:
    virtual WDGraphableInterface* graphableSupporter() override;
    virtual const WDPLines* gPLines() override;
    virtual bool hasHoles() const override;
    virtual const WDNode& ownerNode() const override
    {
        return this->node();
    }
};

WD_NAMESPACE_END

