#pragma once

#include "../WDBMBase.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 设计模块对象
 */
class WD_API WDBMDesign: public WDBMBase
{
public:
    WDBMDesign(WDCore& core);
    WDBMDesign(const WDBMDesign& right) = delete;
    WDBMDesign(WDBMDesign&& right) = delete;
    WDBMDesign& operator=(const WDBMDesign& right) = delete;
    WDBMDesign& operator=(WDBMDesign&& right) = delete;
    ~WDBMDesign();
public:
    /**
    * @brief 校验节点名称是否已存在
    * @param name 预校验的节点名称
    * @return 节点名称存在时返回true, 不存在时返回false, 如果传入的节点名称为空字符串，则固定返回false
    */
    virtual bool nameExists(const std::string_view& name) const override;
    /**
    * @brief 批量校验名称是否已存在
    * @param names 需要校验的名称组
    * @return names中已存在的名称
    */
    virtual std::set<std::string_view> nameExists(const std::vector<std::string>& names) const override;
protected:
    /**
    * @brief 初始化设计管理模块
    *   1.注册设计模块业务数据对象创建函数
    *   2.加载默认路径的类型节点配置文件(xml)来初始化类型节点构建器
    *   3.创建默认的设计模块跟节点
    * @return 是否初始化成功
    */
    virtual bool initP() override;
    /**
     * @brief 创建根节点
    */
    virtual WD::WDNode::SharedPtr createRoot() override;
    /**
     * @brief 反始化设计管理模块
    */
    virtual void uninitP() override;
    /**
     * @brief 收集当前模块类型以及属性的翻译文件名称列表(全路径名称)
    */
    virtual std::vector<std::string> collectTranslationFiles() const override;
    /**
     * @brief 节点销毁之后的操作
    */
    virtual void onNodeDestroyAfter(WDNode::SharedPtr pParentNode) const override;
    /**
     * @brief 节点销毁之后的操作
    */
    virtual void onNodeSetParentAfter(WDNode::SharedPtr pParentNode) const override;
    /**
     * @brief 更新已经被收集的节点的引用，由子类实现
    */
    virtual void updateNodeRef(WDBMRefUpdater& collector) const override;
};

WD_NAMESPACE_END

