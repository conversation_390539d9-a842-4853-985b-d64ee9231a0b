#include "WDBMDEquiSub.h"


WD_NAMESPACE_BEGIN

WDBMDEquiNOZZ::WDBMDEquiNOZZ(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{

}
WDBMDEquiNOZZ::~WDBMDEquiNOZZ()
{

}

void WDBMDEquiNOZZ::RegistAttribute(WDBMTypeDesc& typeDesc)
{
    // 管嘴的保温目前固定返空
    typeDesc.add("Iparam"
        , WDBMAttrValueType::T_DoubleVector
        , [](const WDNode& ) -> std::vector<double>
        {
            return std::vector<double>();
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::Flags(WDBMAttrDesc::F_Hidden | WDBMAttrDesc::F_Update));
}

WDBDBase* WDBMDEquiNOZZ::clone(WDNode& node) const
{
    auto p = new WDBMDEquiNOZZ(node);
    p->copy(*this);
    return p;
}
void WDBMDEquiNOZZ::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDEquiNOZZ* pSrc = dynamic_cast<const WDBMDEquiNOZZ*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDEquiNOZZ::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDEquiNOZZ::graphableSupporter()
{
    return &_modelHelpter;
}

WDBMNodeRef WDBMDEquiNOZZ::mSpref() const
{
    return this->node().getAttribute("Catref").toNodeRef();
}
void WDBMDEquiNOZZ::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);
    gVars.setValue<double>("DDHEIGHT", this->node().getAttribute("Height").toDouble());
    gVars.setValue<double>("IPARAM1", 0.0);
}

void WDBMDEquiNOZZ::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(core);
    WDUnused(bInsu);
    outGVars.setValue<double>("DDHEIGHT", this->node().getAttribute("Height").toDouble());
    outGVars.setValue<double>("IPARAM1", 0.0);
}
void WDBMDEquiNOZZ::onModelUpdate(WDCore& core, WDNode& node)
{
    WDUnused(node);
    // 生成元件模型
    _modelHelpter.updateModel(core);
}


WD_NAMESPACE_END