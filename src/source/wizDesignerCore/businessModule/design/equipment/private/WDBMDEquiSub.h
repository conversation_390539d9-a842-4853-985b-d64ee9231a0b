#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"

WD_NAMESPACE_BEGIN

/**
* @brief 管嘴
*/
class WD_API WDBMDEquiNOZZ: public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    //
    WDBMDModelHelpter _modelHelpter;
public:
    WDBMDEquiNOZZ(WDNode& node);
    ~WDBMDEquiNOZZ();
public:
    static void RegistAttribute(WDBMTypeDesc& des);
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void copy(const WDBDBase& src) override;
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};


WD_NAMESPACE_END

