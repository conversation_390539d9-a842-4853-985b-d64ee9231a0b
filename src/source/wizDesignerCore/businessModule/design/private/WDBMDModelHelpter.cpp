#include "WDBMDModelHelpter.h"

#include "WDCore.h"
#include "../../catalog/WDBMCatalog.h"
#include "../../dataType/WDBMNodeRef.h"
#include "../../../common/WDConfig.h"
#include "../../WDBDBase.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 负实体几何体校验
 * @param geom 几何体
 * @param levelCheck 是否校验层级
 * @return 是否通过校验
 */
bool NGeomCheck(const WDGeometry& geom, bool levelCheck)
{
    // 如果是负实体，必须包含负实体标记
    if (!geom.gFlags().hasFlag(WDGeometry::GF_Negative))
        return false;
    // 校验层级
    if (levelCheck)
        return WDBMLevelRange::Check(geom.level(), WDBMLevelRange::DefaultLevel);
    else
        return true;
}
/**
 * @brief 开孔
 * @param gMgr  几何体管理
 * @param pGeom  要开孔的正实体
 * @param pTsfmNGeoms 带有变换矩阵的负实体列表
 * @param pNGeoms 不带变换矩阵的负实体列表
 * @param nGeomLevelCheck 负实体是否校验层级
 * @param holesLod 开孔几何体的LOD
 * @param bWireframe 是否输出边线
 * @param mergedCatch 合并缓存，开孔时，需要将 pTsfmNGeoms 和 pNGeoms 合并到统一个列表中，可以使用该缓存防止频繁释放和分配内存
 * @return 开孔的结果，失败返回nullptr
 */
WDGeometry::SharedPtr GHoles(WDGeometryMgr& gMgr
    , WDGeometry::SharedPtr pGeom
    , const WDGeometryBoolean::TsfmGeometries* pTsfmNGeoms
    , const WDGeometries* pNGeoms
    , bool nGeomLevelCheck
    , const MeshLODSelection& holesLod
    , bool bWireframe
    , WDGeometryBoolean::TsfmGeometries& mergedCatch)
{
    assert(pGeom != nullptr);
    mergedCatch.clear();
    if (pNGeoms != nullptr)
    {
        WDGeometries rGeoms;
        rGeoms.reserve(pNGeoms->size());
        for (auto pNGeom : (*pNGeoms))
        {
            if (pNGeom == nullptr)
                continue;
            if (!NGeomCheck(*pNGeom, nGeomLevelCheck))
                continue;
            rGeoms.push_back(pNGeom);
        }
        if (!rGeoms.empty())
            mergedCatch.push_back({ std::nullopt, std::move(rGeoms) });
    }
    if (pTsfmNGeoms != nullptr)
    {
        for (const auto& tsfmGeom : (*pTsfmNGeoms))
        {
            WDGeometries rGeoms;
            rGeoms.reserve(tsfmGeom.second.size());
            for (auto pNGeom : tsfmGeom.second)
            {
                if (pNGeom == nullptr)
                    continue;
                if (!NGeomCheck(*pNGeom, nGeomLevelCheck))
                    continue;
                rGeoms.push_back(pNGeom);
            }
            if (!rGeoms.empty())
                mergedCatch.push_back({ tsfmGeom.first, std::move(rGeoms) });
        }
    }
    return WDGeometryBoolean::Diff(gMgr
        , pGeom
        , mergedCatch
        , holesLod
        , pGeom->gFlags()
        , pGeom->level()
        , bWireframe);
}

WDBMDModelHelpter::WDBMDModelHelpter(MDelegate& deleg)
    : _deleg(deleg)
{

}
WDBMDModelHelpter::~WDBMDModelHelpter()
{
    // 通知模型移除
    noticeRemove();
}

bool WDBMDModelHelpter::HasNGeom(const WDNode& node)
{
    auto pBase = node.getBDBase();
    if (pBase == nullptr)
        return false;
    auto pGraph = pBase->graphableSupporter();
    if (pGraph == nullptr)
        return false;
    return pGraph->hasNegativeGeometry();
}
WDGeometries WDBMDModelHelpter::GetNGeoms(const WDNode& node)
{
    auto pBase = node.getBDBase();
    if (pBase == nullptr)
        return {};
    auto pGraph = pBase->graphableSupporter();
    if (pGraph == nullptr)
        return {};
    WDGeometries rGeoms;
    pGraph->negativeGeometry(&rGeoms);
    return rGeoms;
}

void WDBMDModelHelpter::updateModelBegin(WDCore& core)
{
    // 通知模型移除
    noticeRemove();
    // 重置数据
    resetData();

    // 元件全局变量
    WDBMGVars gVars;
    _deleg.mGVars(core, gVars);

    // 在元件模型构建器中查询缓存的元件模型
    auto& builder = core.getBMCatalog().modelBuilder();
    _pModel = builder.get(_deleg.mNode(), _pModel);
    _pInsuModel = builder.getInsu(_deleg.mNode(), _pInsuModel);

    if (_pModel != nullptr)
    {
        // 将元件模型的关键点数据添加到缓存中, 并扩充包围盒
        _keyPoints = _pModel->data().keyPoints;
        for (const auto& kPt : _keyPoints)
        {
            _aabb.expandByPoint(kPt.position);
        }
        // 将元件模型的PLine数据添加到缓存中, 并扩充包围盒
        _pLines = _pModel->data().pLines;
        for (const auto& pl : _pLines)
        {
            _aabb.expandByPoint(pl.sPosition);
            _aabb.expandByPoint(pl.ePosition);
        }
    }
}
void WDBMDModelHelpter::updateModelEnd(WDCore& core)
{
    // 获取根据对象原始数据生成的模型数据
    MData mData;
    _deleg.mData(core, mData);

    // 缓存
    _mGeoms = mData.geoms;

    // 添加点集数据到缓存中, 并扩充包围盒
    _keyPoints.insert(_keyPoints.end(), mData.keyPoints.begin(), mData.keyPoints.end());
    for (const auto& kPt : mData.keyPoints)
    {
        _aabb.expandByPoint(kPt.position);
    }
    // 添加GLine数据到缓存中, 并扩充包围盒
    _gLines = std::move(mData.gLines);
    for (const auto& gl : _gLines)
    {
        _aabb.expandByAabb(gl.calcAabb());
    }
    // 添加GText数据到缓存中, 由于文字可能是三维文字也可能是二维文字，所以这里不会使用文字扩充包围盒
    _gTexts = std::move(mData.gTexts);

    // 更新绘制几何体到缓存中
    if (core.holesDrawn().first) // 开孔
        this->updateRenderGeomWithHoles(core.geometryMgr(), MeshLODSelection(core.holesDrawn().second));
    else // 不开孔
        this->updateRenderGeom();

    // 通知模型添加
    this->noticeAdd();

}
void WDBMDModelHelpter::copy(const WDBMDModelHelpter& right)
{
    // 通知模型移除
    noticeRemove();

    _aabb = right._aabb;
    _renderGeoms = right._renderGeoms;
    _keyPoints = right._keyPoints;
    _pLines = right._pLines;
    _gLines = right._gLines;
    _gTexts = right._gTexts;

    _pModel = right._pModel;
    _pInsuModel = right._pInsuModel;

    _mGeoms = right._mGeoms;

    _bRenderGeomsHArcTolerance = right._bRenderGeomsHArcTolerance;
    _bRenderGeomsAlreadyHoles = right._bRenderGeomsAlreadyHoles;
    // 通知模型添加
    noticeAdd();
}

bool WDBMDModelHelpter::pickup(const DMat4& transformMatrix, const WDPickupParam& param
    , PickupResult& outResult) const
{
    bool bRet = false;

    // 用于负实体拾取线框
    WDPickupParam tParam = param;
    tParam.setPickupMode(WDPickupParam::PM_WireFrame);

    // 拾取几何体
    auto geoms = this->gRenderGeomsWithSystemOption(Core());
    for (const auto& pGeo : geoms)
    {
        if (pGeo == nullptr)
            continue;
        const auto& flags = pGeo->gFlags(); 

        // 如果是负实体，则拾取其线框
        if (flags.hasFlag(WDGeometry::GF_Negative))
        {
            if (!pGeo->pickup(transformMatrix, tParam, outResult))
                continue;
            bRet = true;
        }
        // 否则直接拾取
        else
        {
            if (!pGeo->pickup(transformMatrix, param, outResult))
                continue;
            bRet = true;
        }
    }

    // 拾取线
    const auto& gLines = _gLines;
    for (const auto& gLine : gLines)
    {
        if (gLine.pickup(transformMatrix, param, outResult))
            bRet = true;
    }

    return bRet;
}
WDSelectionInterface::FrameSelectResult WDBMDModelHelpter::frameSelect(const DMat4& transformMatrix
    , const WDFrameSelectParam& param) const
{
    std::vector<FrameSelectResult> rets;

    // 用于负实体框选线框
    WDFrameSelectParam tParam = param;
    tParam.setFrameSelectionMode(WDFrameSelectParam::FSM_WireFrame);

    // 框选几何体
    auto geoms = this->gRenderGeomsWithSystemOption(Core());
    for (const auto& pGeo : geoms)
    {
        if (pGeo == nullptr)
            continue;
        const auto& flags = pGeo->gFlags();
        // 如果是负实体，则框选其线框
        if (flags.hasFlag(WDGeometry::GF_Negative))
        {
            auto ret = pGeo->frameSelect(transformMatrix, tParam);
            if (ret == FrameSelectResult::FSR_NoData)
                continue;
            rets.push_back(ret);
        }
        // 否则直接框选
        else
        {
            auto ret = pGeo->frameSelect(transformMatrix, param);
            if (ret == FrameSelectResult::FSR_NoData)
                continue;
            rets.push_back(ret);
        }
    }

    // 框选线
    const auto& gLines = _gLines;
    for (const auto& gLine : gLines)
    {
        auto ret = gLine.frameSelect(transformMatrix, param);
        if (ret == FrameSelectResult::FSR_NoData)
            continue;
        rets.emplace_back(ret);
    }

    return MergeFrameSelectResult(rets);
}

DAabb3 WDBMDModelHelpter::gRenderAabb() const
{
    return _aabb;
}
const WDGeometries* WDBMDModelHelpter::gRenderGeoms() const
{
    if (_renderGeoms.empty())
        return nullptr;
    return &_renderGeoms;
}

std::optional<std::pair<bool, float> > WDBMDModelHelpter::gRenderGeomsAlreadyHoles() const
{
    return std::make_pair(_bRenderGeomsAlreadyHoles, _bRenderGeomsHArcTolerance);
}

const WDKeyPoints* WDBMDModelHelpter::gKeyPoints()
{
    if (_keyPoints.empty())
        return nullptr;
    return &_keyPoints;
}
const WDPLines* WDBMDModelHelpter::gPLines()
{
    if (_pLines.empty())
        return nullptr;
    return &_pLines;
}
const WDGraphableLines* WDBMDModelHelpter::gLines()
{
    if (_gLines.empty())
        return nullptr;
    return &_gLines;
}
const WDGraphableTexts* WDBMDModelHelpter::gTexts()
{
    if (_gTexts.empty())
        return nullptr;
    return &_gTexts;
}

bool WDBMDModelHelpter::hasHoles() const
{
    // 检测元件模型是否具有孔洞
    if (_pModel != nullptr)
    {
        for (const auto& gm : _pModel->data().geoms) 
        {
            if (!gm.nGeoms.empty())
                return true;
        }
    }
    // 节点属性生成的模型是否具有孔洞
    return _deleg.mHasHoles();
}
bool WDBMDModelHelpter::negativeGeometry(WDGeometries* pOutNGeoms) const
{
    // 如果未指定，只检测是否有负几何体
    if (pOutNGeoms == nullptr) 
    {
        // 元件模型的负型集
        if (_pModel != nullptr && !_pModel->data().nGeoms.empty())
            return true;
        // 节点属性生成的负几何体列表
        for (auto pGeom : _mGeoms)
        {
            if (pGeom != nullptr && pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
                return true;
        }
        return false;
    }
    // 如果指定了，则将负几何体加入到结果列表中
    else
    {
        size_t cnt = 0;
        // 节点属性生成的负几何体列表
        for (auto pGeom : _mGeoms)
        {
            if (pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
                cnt++;
        }
        // 元件模型的负型集
        if (_pModel != nullptr)
            cnt += _pModel->data().nGeoms.size();

        pOutNGeoms->reserve(cnt);
        // 加入 节点属性生成的负几何体列表
        for (auto pGeom : _mGeoms)
        {
            if (pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
                pOutNGeoms->push_back(pGeom);
        }
        // 加入 元件模型的负型集
        if (_pModel != nullptr)
        {
            pOutNGeoms->insert(pOutNGeoms->end(), _pModel->data().nGeoms.begin(), _pModel->data().nGeoms.end());
        }
        return cnt > 0;
    }
}
const WDNode& WDBMDModelHelpter::ownerNode() const
{
    return _deleg.mNode();
}

WDGeometries WDBMDModelHelpter::gGeoms(GGeomType gType
    , const std::pair<bool, float>& holesParam
    , DAabb3* pOutAabb
    , const std::optional<MeshLODSelection>& lod)
{
    WDCore& core = Core();
    switch (gType)
    {
    case WD::WDGraphableInterface::GGT_Basic:
        return this->gBasicGeoms(core.geometryMgr(), holesParam, pOutAabb, lod);
        break;
    case WD::WDGraphableInterface::GGT_Collision:
        return this->gCollisionGeoms(core.geometryMgr(), holesParam, pOutAabb, lod);
        break;
    default:
        break;
    }
    return {};
#if 0
    bool bNeedHoles = holesParam.first;
    MeshLODSelection holesLod = MeshLODSelection(holesParam.second);

    auto funcGeomCheck = [gType, bNeedHoles](const WDGeometry& geom) ->bool
        {
            switch (gType)
            {
            case WD::WDGraphableInterface::GGT_Basic:
            {
                const auto& gFlags = geom.gFlags();
                // 不包含负实体
                if (gFlags.hasFlag(WDGeometry::GF_Negative))
                    return false;
                // 非实体
                if (!gFlags.hasFlag(WDGeometry::GF_Entity))
                    return false;
                // 校验层级
                if (!WDBMLevelRange::Check(geom.level(), WDBMLevelRange::DefaultLevel))
                    return false;
                return true;
            }
            break;
            case WD::WDGraphableInterface::GGT_Collision:
            {
                const auto& gFlags = geom.gFlags();
                // 不包含负实体
                if (gFlags.hasFlag(WDGeometry::GF_Negative))
                {
                    return false;
                }
                // 如果是保温模型，还需要检测其实体/中心线标记
                if (gFlags.hasFlag(WDGeometry::GF_Insu))
                {
                    if (!gFlags.hasAnyOfFlags(WDGeometry::GF_Entity, WDGeometry::GF_CenterLine))
                        return false;
                }
                // 只要有软/硬碰撞的标志，均认为是碰撞体
                return gFlags.hasAnyOfFlags(WDGeometry::GF_CSoft // 软碰撞
                    , WDGeometry::GF_CHard); // 硬碰撞
            }
            break;
            case WD::WDGraphableInterface::GGT_All:
            {
                const auto& gFlags = geom.gFlags();
                // 如果开孔，则不包含负实体
                if (gFlags.hasFlag(WDGeometry::GF_Negative) && bNeedHoles)
                {
                    return false;
                }
                // 如果是保温模型，还需要检测其实体/中心线标记
                if (gFlags.hasFlag(WDGeometry::GF_Insu))
                {
                    if (!gFlags.hasAnyOfFlags(WDGeometry::GF_Entity, WDGeometry::GF_CenterLine))
                        return false;
                }
                // 其他任意几何体都能通过
                return true;
            }
            break;
            default:
                break;
            }
            return false;
        };

    WDCore& core = Core();

    WDGeometries rGeoms;
    // 是否包含保温
    bool bInsu = false;
    switch (gType)
    {
    case WD::WDGraphableInterface::GGT_Collision:
    case WD::WDGraphableInterface::GGT_All:
        bInsu = true;
        break;
    default:
        break;
    }
    // 先统计个数
    size_t cnt = 0;
    {
        // 由节点本身属性生成的几何体
        for (auto pGeom : _mGeoms)
        {
            if (pGeom == nullptr)
                continue;
            // 检测标记
            if (!funcGeomCheck(*pGeom))
                continue;
            cnt++;
        }
        // 由节点引用的元件生成的几何体
        if (_pModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 检测标记
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                cnt++;
            }
        }
        // 节点引用的元件生成的保温几何体列表
        if (bInsu && _pInsuModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pInsuModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 检测标记
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                cnt++;
            }
        }
    }
    // 预分配内存
    rGeoms.reserve(cnt);
    // 不开孔
    if (!bNeedHoles)
    {
        // 由节点本身属性生成的几何体
        for (auto pGeom : _mGeoms)
        {
            if (pGeom == nullptr)
                continue;
            // 检测标记
            if (!funcGeomCheck(*pGeom))
                continue;
            rGeoms.push_back(pGeom);
            // 合并包围盒
            if (pOutAabb != nullptr)
                pOutAabb->unions(pGeom->aabb());
        }
        // 由节点引用的元件生成的几何体
        if (_pModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 检测标记
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                rGeoms.push_back(gm.pGeom);
                // 合并包围盒
                if (pOutAabb != nullptr)
                    pOutAabb->unions(gm.pGeom->aabb());
            }
        }
        // 节点引用的元件生成的保温几何体列表
        if (bInsu && _pInsuModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pInsuModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 检测标记
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                rGeoms.push_back(gm.pGeom);
                // 合并包围盒
                if (pOutAabb != nullptr)
                    pOutAabb->unions(gm.pGeom->aabb());
            }
        }
    }
    // 开孔
    else
    {
        WDGeometryBoolean::TsfmGeometries tsfmNGeoms;
        tsfmNGeoms.reserve(100);
        if (cnt > 0)
            _deleg.mNGeoms(tsfmNGeoms);

        WDGeometryBoolean::TsfmGeometries mergedTsfmNGeoms;
        // 由节点本身属性生成的几何体
        for (auto pGeom : _mGeoms)
        {
            if (pGeom == nullptr)
                continue;
            // 检测标记
            if (!funcGeomCheck(*pGeom))
                continue;
            // 开孔
            bool bHoles = false;
            if (!tsfmNGeoms.empty())
            {
                auto pRGeom = WDGeometryBoolean::Diff(core.geometryMgr()
                    , pGeom
                    , tsfmNGeoms
                    , holesLod
                    , pGeom->gFlags()
                    , pGeom->level()
                    , false);
                if (pRGeom != nullptr)
                {
                    rGeoms.push_back(pRGeom);
                    // 合并包围盒
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(pGeom->aabb());
                    bHoles = true;
                }
            }
            // 如果未开孔，则添加为原来的几何体
            if (!bHoles)
            {
                rGeoms.push_back(pGeom);
                // 合并包围盒
                if (pOutAabb != nullptr)
                    pOutAabb->unions(pGeom->aabb());
            }
        }
        // 由节点引用的元件生成的几何体
        if (_pModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 检测标记
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                // 开孔
                bool bHoles = false;
                if (!gm.nGeoms.empty() && !tsfmNGeoms.empty())
                {
                    mergedTsfmNGeoms.clear();
                    mergedTsfmNGeoms.push_back({ DMat4::Identity(), gm.nGeoms });
                    mergedTsfmNGeoms.insert(mergedTsfmNGeoms.end(), tsfmNGeoms.begin(), tsfmNGeoms.end());
                    auto pRGeom = WDGeometryBoolean::Diff(core.geometryMgr()
                        , gm.pGeom
                        , mergedTsfmNGeoms
                        , holesLod
                        , gm.pGeom->gFlags()
                        , gm.pGeom->level()
                        , false);
                    if (pRGeom != nullptr)
                    {
                        rGeoms.push_back(pRGeom);
                        // 合并包围盒
                        if (pOutAabb != nullptr)
                            pOutAabb->unions(pRGeom->aabb());
                        bHoles = true;
                    }
                }
                else if (!gm.nGeoms.empty())
                {
                    WDGeometryBoolean::TsfmGeometries tfGs = { { DMat4::Identity(), gm.nGeoms } };
                    auto pRGeom = WDGeometryBoolean::Diff(core.geometryMgr()
                        , gm.pGeom
                        , tfGs
                        , holesLod
                        , gm.pGeom->gFlags()
                        , gm.pGeom->level()
                        , false);
                    if (pRGeom != nullptr)
                    {
                        rGeoms.push_back(pRGeom);
                        // 合并包围盒
                        if (pOutAabb != nullptr)
                            pOutAabb->unions(pRGeom->aabb());
                        bHoles = true;
                    }
                }
                else if (!tsfmNGeoms.empty())
                {
                    auto pRGeom = WDGeometryBoolean::Diff(core.geometryMgr()
                        , gm.pGeom
                        , tsfmNGeoms
                        , holesLod
                        , gm.pGeom->gFlags()
                        , gm.pGeom->level()
                        , false);
                    if (pRGeom != nullptr)
                    {
                        rGeoms.push_back(pRGeom);
                        // 合并包围盒
                        if (pOutAabb != nullptr)
                            pOutAabb->unions(pRGeom->aabb());
                        bHoles = true;
                    }
                }
                // 如果未开孔，则添加为原来的几何体
                if (!bHoles)
                {
                    rGeoms.push_back(gm.pGeom);
                    // 合并包围盒
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(gm.pGeom->aabb());
                }
            }
        }
        // 节点引用的元件生成的保温几何体列表
        if (bInsu && _pInsuModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pInsuModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 检测标记
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                // 开孔
                bool bHoles = false;
                if (!gm.nGeoms.empty() && !tsfmNGeoms.empty())
                {
                    mergedTsfmNGeoms.clear();
                    mergedTsfmNGeoms.push_back({ DMat4::Identity(), gm.nGeoms });
                    mergedTsfmNGeoms.insert(mergedTsfmNGeoms.end(), tsfmNGeoms.begin(), tsfmNGeoms.end());
                    auto pRGeom = WDGeometryBoolean::Diff(core.geometryMgr()
                        , gm.pGeom
                        , mergedTsfmNGeoms
                        , holesLod
                        , gm.pGeom->gFlags()
                        , gm.pGeom->level()
                        , true);
                    if (pRGeom != nullptr)
                    {
                        rGeoms.push_back(pRGeom);
                        // 合并包围盒
                        if (pOutAabb != nullptr)
                            pOutAabb->unions(pRGeom->aabb());
                        bHoles = true;
                    }
                }
                else if (!gm.nGeoms.empty())
                {
                    WDGeometryBoolean::TsfmGeometries tfGs = { { DMat4::Identity(), gm.nGeoms } };
                    auto pRGeom = WDGeometryBoolean::Diff(core.geometryMgr()
                        , gm.pGeom
                        , tfGs
                        , holesLod
                        , gm.pGeom->gFlags()
                        , gm.pGeom->level()
                        , true);
                    if (pRGeom != nullptr)
                    {
                        rGeoms.push_back(pRGeom);
                        rAabb.unions(pRGeom->aabb());
                        bHoles = true;
                    }
                }
                else if (!tsfmNGeoms.empty())
                {
                    auto pRGeom = WDGeometryBoolean::Diff(core.geometryMgr()
                        , gm.pGeom
                        , tsfmNGeoms
                        , holesLod
                        , gm.pGeom->gFlags()
                        , gm.pGeom->level()
                        , true);
                    if (pRGeom != nullptr)
                    {
                        rGeoms.push_back(pRGeom);
                        rAabb.unions(pRGeom->aabb());
                        bHoles = true;
                    }
                }
                // 如果未开孔，则添加为原来的几何体
                if (!bHoles)
                {
                    rGeoms.push_back(gm.pGeom);
                    rAabb.unions(gm.pGeom->aabb());
                }
            }
        }
    }

    return rGeoms;
#endif
}

enum GCheckType
{
    // 来自设计模块的几何体
    GCT_Desi = 0,
    // 来自元件模块的元件模型
    GCT_Cata ,
    // 元件库保温模型
    GCT_CataInsu ,
};
bool RenderGeomCheck(const WDGeometry& geom, GCheckType type)
{
    switch (type)
    {
    case GCT_Desi:
        {
            const auto& flags = geom.gFlags();
            // 检测标记
            if (flags.hasFlag(WDGeometry::GF_Insu))
            {
                // 对于保温模型，既要有保温标记，还要有实体/中心线标记
                if (!flags.hasAnyOfFlags(WDGeometry::GF_Entity, WDGeometry::GF_CenterLine))
                    return false;
            }
            else if (!flags.hasAnyOfFlags(WDGeometry::GF_Entity // 实体标记
                , WDGeometry::GF_Negative   // 负实体标记
                , WDGeometry::GF_CenterLine // 中心线标记
                , WDGeometry::GF_CSoft      // 软碰撞
                , WDGeometry::GF_CHard))    // 硬碰撞
            {
                return false;
            }
            // 校验层级
            if (!WDBMLevelRange::Check(geom.level(), WDBMLevelRange::DefaultLevel))
                return false;

            return true;
        }
        break;
    case GCT_Cata:
        {
            const auto& flags = geom.gFlags();
            // 检测标记
            if (flags.hasFlag(WDGeometry::GF_Insu))
            {
                // 对于保温模型，既要有保温标记，还要有实体/中心线标记
                if (!flags.hasAnyOfFlags(WDGeometry::GF_Entity, WDGeometry::GF_CenterLine))
                    return false;
            }
            else if (!flags.hasAnyOfFlags(WDGeometry::GF_Entity // 实体标记
                , WDGeometry::GF_CenterLine // 中心线标记
                , WDGeometry::GF_CSoft      // 软碰撞
                , WDGeometry::GF_CHard))    // 硬碰撞
            {
                return false;
            }
            // 校验层级
            if (!WDBMLevelRange::Check(geom.level(), WDBMLevelRange::DefaultLevel))
                return false;
            return true;
        }
        break;
    case GCT_CataInsu:
        {
            const auto& flags = geom.gFlags();
            // 检测标记 
            if (!flags.hasFlag(WDGeometry::GF_Insu))
                return false;
            // 对于保温模型，既要有保温标记，还要有实体/中心线标记
            if (!flags.hasAnyOfFlags(WDGeometry::GF_Entity, WDGeometry::GF_CenterLine))
                return false;
            // 校验层级
            if (!WDBMLevelRange::Check(geom.level(), WDBMLevelRange::DefaultLevel))
                return false;
            return true;
        }
        break;
    default:
        break;
    }

    return false;
}

void WDBMDModelHelpter::updateRenderGeom()
{
    // 记录未被开孔
    _bRenderGeomsHArcTolerance = 10.0f;
    _bRenderGeomsAlreadyHoles = false;

    /**************** 统计几何体个数,以便于预分配内存 ************************/
    size_t cnt = 0;
    // 节点本身属性生成的几何体列表
    for (auto pGeom : _mGeoms)
    {
        if (pGeom == nullptr)
            continue;
        // 校验
        if (!RenderGeomCheck(*pGeom, GCheckType::GCT_Desi))
            continue;
        cnt++;
    }
    // 节点引用的元件生成的几何体列表
    if (_pModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pModel->data().geoms)
        {
            if (gm.pGeom == nullptr)
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_Cata))
                continue;
            cnt++;
            // 还需要添加附带的负实体
            for (auto pNGm : gm.nGeoms)
            {
                if (pNGm == nullptr)
                    continue;
                // 校验
                if (!NGeomCheck(*pNGm, true))
                    continue;
                cnt++;
            }
        }
        // 负型集
        for (auto pNGm : _pModel->data().nGeoms)
        {
            if (pNGm == nullptr)
                continue;
            // 校验
            if (!NGeomCheck(*pNGm, true))
                continue;
            cnt++;
        }
    }
    // 节点引用的元件生成的保温几何体列表
    if (_pInsuModel != nullptr)
    {
        // 保温型集
        for (const auto& gm : _pInsuModel->data())
        {
            if (gm.pGeom == nullptr)
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_CataInsu))
                continue;
            cnt++;
            // 还需要添加附带的负实体
            for (auto pNGm : gm.nGeoms)
            {
                if (pNGm == nullptr)
                    continue;
                // 校验
                if (!NGeomCheck(*pNGm, true))
                    continue;
                cnt++;
            }
        }
    }

    if (cnt == 0)
        return;
    // 预分配内存
    _renderGeoms.reserve(cnt);

    /**************** 更新到绘制几何体列表中 ************************/
    // 由节点本身属性生成的几何体
    for (auto pGeom : _mGeoms)
    {
        if (pGeom == nullptr)
            continue;
        // 校验
        if (!RenderGeomCheck(*pGeom, GCheckType::GCT_Desi))
            continue;
        _renderGeoms.push_back(pGeom);
        _aabb.unions(pGeom->aabb());
    }
    // 由节点引用的元件生成的几何体
    if (_pModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pModel->data().geoms)
        {
            if (gm.pGeom == nullptr)
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_Cata))
                continue;
            _renderGeoms.push_back(gm.pGeom);
            _aabb.unions(gm.pGeom->aabb());
            // 还需要添加附带的负实体
            for (auto pNGm : gm.nGeoms)
            {
                if (pNGm == nullptr)
                    continue;
                // 校验
                if (!NGeomCheck(*pNGm, true))
                    continue;
                _renderGeoms.push_back(pNGm);
                _aabb.unions(pNGm->aabb());
            }
        }
        // 负型集
        for (auto pNGm : _pModel->data().nGeoms)
        {
            if (pNGm == nullptr)
                continue;
            // 校验
            if (!NGeomCheck(*pNGm, true))
                continue;
            _renderGeoms.push_back(pNGm);
            _aabb.unions(pNGm->aabb());
        }
    }
    // 由节点引用的元件生成的保温几何体
    if (_pInsuModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pInsuModel->data())
        {
            if (gm.pGeom == nullptr)
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_CataInsu))
                continue;
            _renderGeoms.push_back(gm.pGeom);
            _aabb.unions(gm.pGeom->aabb());
            // 还需要添加附带的负实体
            for (auto pNGm : gm.nGeoms)
            {
                if (pNGm == nullptr)
                    continue;
                // 校验
                if (!NGeomCheck(*pNGm, true))
                    continue;
                _renderGeoms.push_back(pNGm);
                _aabb.unions(pNGm->aabb());
            }
        }
    }
}
void WDBMDModelHelpter::updateRenderGeomWithHoles(WDGeometryMgr& gMgr, const MeshLODSelection& holesLod)
{
    // 记录已被开孔
    _bRenderGeomsAlreadyHoles = true;
    _bRenderGeomsHArcTolerance = holesLod.arcTolerance();

    /**************** 统计几何体个数,以便于预分配内存 ************************/
    size_t cnt = 0;
    // 节点本身属性生成的几何体列表
    for (auto pGeom : _mGeoms)
    {
        if (pGeom == nullptr)
            continue;
        // 开孔后，负实体不再加入绘制
        if (pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
            continue;
        // 校验
        if (!RenderGeomCheck(*pGeom, GCheckType::GCT_Desi))
            continue;
        cnt++;
    }
    // 节点引用的元件生成的几何体列表
    if (_pModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pModel->data().geoms)
        {
            if (gm.pGeom == nullptr)
                continue;
            // 开孔后，负实体不再加入绘制
            if (gm.pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_Cata))
                continue;
            cnt++;
        }
    }
    // 节点引用的元件生成的保温几何体列表
    if (_pInsuModel != nullptr)
    {
        // 保温正型集
        for (const auto& gm : _pInsuModel->data())
        {
            if (gm.pGeom == nullptr)
                continue;
            // 开孔后，负实体不再加入绘制
            if (gm.pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_CataInsu))
                continue;
            cnt++;
        }
    }

    if (cnt == 0)
        return;
    // 预分配内存
    _renderGeoms.reserve(cnt);

    // 获取开孔用的负实体
    WDGeometryBoolean::TsfmGeometries tsfmNGeoms;
    tsfmNGeoms.reserve(100);
    if (cnt > 0)
        _deleg.mNGeoms(tsfmNGeoms);
    // 存放开孔负实体用的缓存
    WDGeometryBoolean::TsfmGeometries mergedTsfmNGeomsCatch;
    mergedTsfmNGeomsCatch.reserve(100);
    // 由节点本身属性生成的几何体
    for (auto pGeom : _mGeoms)
    {
        if (pGeom == nullptr)
            continue;
        // 开孔后，负实体不再加入绘制
        if (pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
            continue;
        // 校验
        if (!RenderGeomCheck(*pGeom, GCheckType::GCT_Desi))
            continue;
        // 开孔
        auto pRGeom = GHoles(gMgr, pGeom, &tsfmNGeoms, nullptr, true, holesLod, true, mergedTsfmNGeomsCatch);
        if (pRGeom != nullptr)
        {
            _renderGeoms.push_back(pRGeom);
            _aabb.unions(pRGeom->aabb());
        }
        else
        {
            _renderGeoms.push_back(pGeom);
            _aabb.unions(pGeom->aabb());
        }
    }
    // 由节点引用的元件生成的几何体
    if (_pModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pModel->data().geoms)
        {
            if (gm.pGeom == nullptr)
                continue;
            // 开孔后，负实体不再加入绘制
            if (gm.pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_Cata))
                continue;
            // 开孔
            auto pRGeom = GHoles(gMgr, gm.pGeom, &tsfmNGeoms, &gm.nGeoms, true, holesLod, true, mergedTsfmNGeomsCatch);
            if (pRGeom != nullptr)
            {
                _renderGeoms.push_back(pRGeom);
                _aabb.unions(pRGeom->aabb());
            }
            else
            {
                _renderGeoms.push_back(gm.pGeom);
                _aabb.unions(gm.pGeom->aabb());
            }
        }
    }
    // 由节点引用的元件生成的保温几何体
    if (_pInsuModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pInsuModel->data())
        {
            if (gm.pGeom == nullptr)
                continue;
            // 开孔后，负实体不再加入绘制
            if (gm.pGeom->gFlags().hasFlag(WDGeometry::GF_Negative))
                continue;
            // 校验
            if (!RenderGeomCheck(*gm.pGeom, GCheckType::GCT_CataInsu))
                continue;
            // 开孔
            auto pRGeom = GHoles(gMgr, gm.pGeom, &tsfmNGeoms, &gm.nGeoms, true, holesLod, true, mergedTsfmNGeomsCatch);
            if (pRGeom != nullptr)
            {
                _renderGeoms.push_back(pRGeom);
                _aabb.unions(pRGeom->aabb());
            }
            else
            {
                _renderGeoms.push_back(gm.pGeom);
                _aabb.unions(gm.pGeom->aabb());
            }
        }
    }
}

WDGeometries WDBMDModelHelpter::gBasicGeoms(WDGeometryMgr& gMgr
    , const std::pair<bool, float>& holesParam
    , DAabb3* pOutAabb
    , const std::optional<MeshLODSelection>& lod) const
{
    WDUnused(lod);
    // 置空包围盒
    if (pOutAabb != nullptr)
        *pOutAabb = DAabb3::Null();
    // 几何体标志以及层级校验函数
    auto funcGeomCheck = [](const WDGeometry& geom)
        {
            const auto& gFlags = geom.gFlags();
            // 不包含负实体
            if (gFlags.hasFlag(WDGeometry::GF_Negative))
                return false;
            // 非实体
            if (!gFlags.hasFlag(WDGeometry::GF_Entity))
                return false;
            // 校验层级
            if (!WDBMLevelRange::Check(geom.level(), WDBMLevelRange::DefaultLevel))
                return false;
            return true;
        };
    // 结果几何体
    WDGeometries rGeoms;
    /**************** 统计几何体个数,以便于预分配内存 ************************/
    size_t cnt = 0;
    // 节点本身属性生成的几何体列表
    for (auto pGeom : _mGeoms)
    {
        if (pGeom == nullptr)
            continue;
        // 校验
        if (!funcGeomCheck(*pGeom))
            continue;
        cnt++;
    }
    // 节点引用的元件生成的几何体列表
    if (_pModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pModel->data().geoms)
        {
            if (gm.pGeom == nullptr)
                continue;
            // 校验
            if (!funcGeomCheck(*gm.pGeom))
                continue;
            cnt++;
        }
    }
    if (cnt == 0)
        return rGeoms;
    // 预分配内存
    rGeoms.reserve(cnt);
    // 不开孔
    if (!holesParam.first) 
    {
        // 由节点本身属性生成的几何体
        for (auto pGeom : _mGeoms)
        {
            if (pGeom == nullptr)
                continue;
            // 校验
            if (!funcGeomCheck(*pGeom))
                continue;
            rGeoms.push_back(pGeom);
            if (pOutAabb != nullptr)
                pOutAabb->unions(pGeom->aabb());
        }
        // 由节点引用的元件生成的几何体
        if (_pModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 校验
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                rGeoms.push_back(gm.pGeom);
                if (pOutAabb != nullptr)
                    pOutAabb->unions(gm.pGeom->aabb());
            }
        }
    }
    // 开孔
    else
    {
        // 开孔模型使用的LOD
        MeshLODSelection holesLod(holesParam.second, MeshLODSelection::T_ArcTolerance);
        // 获取开孔用的负实体
        WDGeometryBoolean::TsfmGeometries tsfmNGeoms;
        tsfmNGeoms.reserve(100);
        if (cnt > 0)
            _deleg.mNGeoms(tsfmNGeoms);
        // 存放开孔负实体用的缓存
        WDGeometryBoolean::TsfmGeometries mergedTsfmNGeomsCatch;
        mergedTsfmNGeomsCatch.reserve(100);
        // 由节点本身属性生成的几何体
        for (auto pGeom : _mGeoms)
        {
            if (pGeom == nullptr)
                continue;
            // 校验
            if (!funcGeomCheck(*pGeom))
                continue;
            // 开孔
            auto pRGeom = GHoles(gMgr, pGeom, &tsfmNGeoms, nullptr, true, holesLod, false, mergedTsfmNGeomsCatch);
            if (pRGeom != nullptr)
            {
                rGeoms.push_back(pRGeom);
                if (pOutAabb != nullptr)
                    pOutAabb->unions(pRGeom->aabb());
            }
            else
            {
                rGeoms.push_back(pGeom);
                if (pOutAabb != nullptr)
                    pOutAabb->unions(pGeom->aabb());
            }
        }
        // 由节点引用的元件生成的几何体
        if (_pModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 校验
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                // 开孔
                auto pRGeom = GHoles(gMgr, gm.pGeom, &tsfmNGeoms, &gm.nGeoms, true, holesLod, false, mergedTsfmNGeomsCatch);
                if (pRGeom != nullptr)
                {
                    rGeoms.push_back(pRGeom);
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(pRGeom->aabb());
                }
                else
                {
                    rGeoms.push_back(gm.pGeom);
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(gm.pGeom->aabb());
                }
            }
        }
    }

    return rGeoms;
}

WDGeometries WDBMDModelHelpter::gCollisionGeoms(WDGeometryMgr& gMgr
    , const std::pair<bool, float>& holesParam
    , DAabb3* pOutAabb
    , const std::optional<MeshLODSelection>& lod) const
{
    WDUnused(lod);
    // 置空包围盒
    if (pOutAabb != nullptr)
        *pOutAabb = DAabb3::Null();

    // 几何体校验，碰撞几何体不校验层级
    auto funcGeomCheck = [](const WDGeometry& geom)
        {
            const auto& gFlags = geom.gFlags();
            // 不包含负实体
            if (gFlags.hasFlag(WDGeometry::GF_Negative))
                return false;
            // 如果是保温模型，还需要检测其实体/中心线标记
            if (gFlags.hasFlag(WDGeometry::GF_Insu))
                if (!gFlags.hasAnyOfFlags(WDGeometry::GF_Entity, WDGeometry::GF_CenterLine))
                    return false;
            // 只要有软/硬碰撞的标志，均认为是碰撞体
            return gFlags.hasAnyOfFlags(WDGeometry::GF_CSoft // 软碰撞
                , WDGeometry::GF_CHard); // 硬碰撞
        };
    // 结果几何体
    WDGeometries rGeoms;
    /**************** 统计几何体个数,以便于预分配内存 ************************/
    size_t cnt = 0;
    // 节点本身属性生成的几何体列表
    for (auto pGeom : _mGeoms)
    {
        if (pGeom == nullptr)
            continue;
        // 校验
        if (!funcGeomCheck(*pGeom))
            continue;
        cnt++;
    }
    // 节点引用的元件生成的几何体列表
    if (_pModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pModel->data().geoms)
        {
            if (gm.pGeom == nullptr)
                continue;
            // 校验
            if (!funcGeomCheck(*gm.pGeom))
                continue;
            cnt++;
        }
    }
    // 节点引用的元件生成的保温几何体列表
    if (_pInsuModel != nullptr)
    {
        // 正型集
        for (const auto& gm : _pInsuModel->data())
        {
            if (gm.pGeom == nullptr)
                continue;
            // 校验
            if (!funcGeomCheck(*gm.pGeom))
                continue;
            cnt++;
        }
    }
    if (cnt == 0)
        return rGeoms;
    // 预分配内存
    rGeoms.reserve(cnt);
    // 不开孔
    if (!holesParam.first)
    {
        // 由节点本身属性生成的几何体
        for (auto pGeom : _mGeoms)
        {
            if (pGeom == nullptr)
                continue;
            // 校验
            if (!funcGeomCheck(*pGeom))
                continue;
            rGeoms.push_back(pGeom);
            if (pOutAabb != nullptr)
                pOutAabb->unions(pGeom->aabb());
        }
        // 由节点引用的元件生成的几何体
        if (_pModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 校验
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                rGeoms.push_back(gm.pGeom);
                if (pOutAabb != nullptr)
                    pOutAabb->unions(gm.pGeom->aabb());
            }
        }
        // 节点引用的元件生成的保温几何体列表
        if (_pInsuModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pInsuModel->data())
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 校验
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                rGeoms.push_back(gm.pGeom);
                // 合并包围盒
                if (pOutAabb != nullptr)
                    pOutAabb->unions(gm.pGeom->aabb());
            }
        }
    }
    // 开孔
    else
    {
        // 开孔模型使用的LOD
        MeshLODSelection holesLod(holesParam.second, MeshLODSelection::T_ArcTolerance);
        // 获取开孔用的负实体
        WDGeometryBoolean::TsfmGeometries tsfmNGeoms;
        tsfmNGeoms.reserve(100);
        if (cnt > 0)
            _deleg.mNGeoms(tsfmNGeoms);
        // 存放开孔负实体用的缓存
        WDGeometryBoolean::TsfmGeometries mergedTsfmNGeomsCatch;
        mergedTsfmNGeomsCatch.reserve(100);
        // 由节点本身属性生成的几何体
        for (auto pGeom : _mGeoms)
        {
            if (pGeom == nullptr)
                continue;
            // 校验
            if (!funcGeomCheck(*pGeom))
                continue;
            // 开孔
            auto pRGeom = GHoles(gMgr, pGeom, &tsfmNGeoms, nullptr, false, holesLod, false, mergedTsfmNGeomsCatch);
            if (pRGeom != nullptr)
            {
                rGeoms.push_back(pRGeom);
                if (pOutAabb != nullptr)
                    pOutAabb->unions(pRGeom->aabb());
            }
            else
            {
                rGeoms.push_back(pGeom);
                if (pOutAabb != nullptr)
                    pOutAabb->unions(pGeom->aabb());
            }
        }
        // 由节点引用的元件生成的几何体
        if (_pModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pModel->data().geoms)
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 校验
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                // 开孔
                auto pRGeom = GHoles(gMgr, gm.pGeom, &tsfmNGeoms, &gm.nGeoms, false, holesLod, false, mergedTsfmNGeomsCatch);
                if (pRGeom != nullptr)
                {
                    rGeoms.push_back(pRGeom);
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(pRGeom->aabb());
                }
                else
                {
                    rGeoms.push_back(gm.pGeom);
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(gm.pGeom->aabb());
                }
            }
        }
        // 节点引用的元件生成的保温几何体列表
        if (_pInsuModel != nullptr)
        {
            // 正型集
            for (const auto& gm : _pInsuModel->data())
            {
                if (gm.pGeom == nullptr)
                    continue;
                // 校验
                if (!funcGeomCheck(*gm.pGeom))
                    continue;
                // 开孔
                auto pRGeom = GHoles(gMgr, gm.pGeom, &tsfmNGeoms, &gm.nGeoms, false, holesLod, false, mergedTsfmNGeomsCatch);
                if (pRGeom != nullptr)
                {
                    rGeoms.push_back(pRGeom);
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(pRGeom->aabb());
                }
                else
                {
                    rGeoms.push_back(gm.pGeom);
                    if (pOutAabb != nullptr)
                        pOutAabb->unions(gm.pGeom->aabb());
                }
            }
        }
    }
    return rGeoms;
}

WD_NAMESPACE_END

