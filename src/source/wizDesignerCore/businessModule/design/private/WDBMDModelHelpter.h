#pragma once

#include "../../../selections/WDSelectionInterface.h"
#include "../../../graphable/WDGraphableInterface.h"
#include "../../catalog/modelBuilder/WDBMCModelBuilder.h"
#include "../../../geometry/WDGeometryBoolean.h"
#include "../../dataType/WDBMGVars.h"

WD_NAMESPACE_BEGIN

class WDBMNodeRef;

class WD_API WDBMDModelHelpter : public WDSelectionInterface
    , public WDGraphableInterface
{
public:
    /**
     * @brief 一个模型包含的所有数据
    */
    class MData
    {
    public:
        // 当前节点参数生成的几何体列表, 包含正几何体和负几何体, 带有碰撞标识的，带有保温标识的几何体
        WDGeometries        geoms;
        // 点集列表
        WDKeyPoints         keyPoints;
        // PLine列表
        WDPLines            pLines;
        // GLine列表
        WDGraphableLines    gLines;
        // GTexts列表
        WDGraphableTexts    gTexts;
    };
    /**
     * @brief 模型数据代理
    */
    class WD_API MDelegate
    {
    protected:
        /**
         * @brief 返回所属的节点
         */
        virtual WDNode& mNode() = 0;
        /**
         * @brief 获取对象自身参数生成的模型数据
        */
        virtual void mData(WDCore& core, MData& data) 
        {
            WDUnused(core);
            WDUnused(data);
        }
        /**
         * @brief 获取生成元件模型的等级(或元件)节点引用对象
        */
        virtual WDBMNodeRef mSpref() const
        {
            return WDBMNodeRef();
        }
        /**
         * @brief 向gVars中设置生成基础模型使用的全局变量值
        */
        virtual void mGVars(WDCore& core, WDBMGVars& gVars) const 
        {
            WDUnused(core);
            WDUnused(gVars);
        }
        /**
         * @brief 向gVars中设置生成保温模型使用的全局变量值
         *  注意: 如果保温模型中的全局变量值与基础模型的全局变量值一致，这里不再需要重复设置
         * @return 是否生成保温模型
        */
        virtual bool mInsuGVars(WDCore& core, WDBMGVars& gVars) const
        {
            WDUnused(core);
            WDUnused(gVars);
            return false;
        }
        /**
         * @brief 获取对象自身参数生成的模型是否具有孔洞
        */
        virtual bool mHasHoles() const 
        {
            return false;
        }
        /**
         * @brief 获取需要给正实体开孔的负实体列表
         */
        virtual void mNGeoms(WDGeometryBoolean::TsfmGeometries& outTsfmGeoms) const 
        {
            WDUnused(outTsfmGeoms);
        }

        friend class WDBMDModelHelpter;
    };
private:
    // 当前绘制数据构成的包围盒
    DAabb3 _aabb = DAabb3::Null();
    // 绘制使用的几何体数据列表
    // 列表中包含: 
    // 由节点属性本身生成的模型， 例如设备基本体
    // 由节点引用的元件生成的模型， 例如管件
    // 由节点属性本身生成的负实体，例如设备基本体中的负基本体
    // 由节点引用的元件型集生成的负实体，例如管嘴中的负圆柱，包括部分结构元件中的负型集生成的几何体
    // 带有软碰撞标志的几何体
    // 保温几何体
    WDGeometries _renderGeoms;
    // 点集数据列表
    WDKeyPoints _keyPoints;
    // PLine集数据列表
    WDPLines _pLines;
    // GLine列表
    WDGraphableLines _gLines;
    // GTexts列表
    WDGraphableTexts _gTexts;

    // 模型数据代理
    MDelegate& _deleg;
    // 缓存引用的元件模型指针
    WDBMCModelBuilder::SharedModel _pModel = nullptr;
    // 缓存引用的保温元件模型指针
    WDBMCModelBuilder::SharedModelI _pInsuModel = nullptr;

    // 缓存通过节点属性生成的几何体列表
    WDGeometries _mGeoms;

    // 记录用于绘制的几何体开孔时的角度公差
    float _bRenderGeomsHArcTolerance = 10.0f;
    // 记录用于绘制的几何体是否已被开孔
    bool _bRenderGeomsAlreadyHoles = false;
public:
    WDBMDModelHelpter(MDelegate& deleg);
    ~WDBMDModelHelpter();
public:
    /**
     * @brief 获取节点中是否带有负几何体(用于给父节点开孔)
     */
    static bool HasNGeom(const WDNode& node);
    /**
     * @brief 获取节点中的负几何体(用于给父节点开孔)
     */
    static WDGeometries GetNGeoms(const WDNode& node);
public:
    /**
     * @brief 开始更新模型, 必须与结束更新模型成对调用
     */
    void updateModelBegin(WDCore& core);
    /**
     * @brief 结束更新模型, 必须与结束更新模型成对调用
     */
    void updateModelEnd(WDCore& core);
    /**
     * @brief 更新模型，先调用了开始更新模型，再调用了结束更新模型
    */
    inline void updateModel(WDCore& core) 
    {
        updateModelBegin(core);
        updateModelEnd(core);
    }
public:
    /**
     * @brief 仅执行模型拷贝
     *  因为生成的模型会缓存到该对象中，因此，当使用该对象的客户端对象执行拷贝时，需要仅拷贝生成的模型对象，则应该使用该方法
     * @param right
    */
    void copy(const WDBMDModelHelpter& right);
public:
    virtual bool pickup(const DMat4& transformMatrix
        , const WDPickupParam& param
        , PickupResult& outResult) const override;
    virtual FrameSelectResult frameSelect(const DMat4& transformMatrix
        , const WDFrameSelectParam& param) const override;

    virtual DAabb3 gRenderAabb() const override;
    virtual const WDGeometries* gRenderGeoms() const override;
    virtual std::optional<std::pair<bool, float> > gRenderGeomsAlreadyHoles() const override;
    virtual const WDKeyPoints* gKeyPoints() override;
    virtual const WDPLines* gPLines() override;
    virtual const WDGraphableLines* gLines() override;
    virtual const WDGraphableTexts* gTexts()override;
public:
    virtual bool hasHoles() const override;
    virtual bool negativeGeometry(WDGeometries* pOutNGeoms = nullptr) const override;
    virtual const WDNode& ownerNode() const override;
public:
    virtual WDGeometries gGeoms(GGeomType gType
        , const std::pair<bool, float>& holesParam = std::make_pair(false, 10.0f)
        , DAabb3* pOutAabb = nullptr
        , const std::optional<MeshLODSelection>& lod = std::nullopt) override;
private:
    // 通知模型添加
    inline void noticeAdd()
    {
        this->noticeAddAfter();
    }
    // 通知模型移除
    inline void noticeRemove()
    {
        // 发送通知
        this->noticeRemoveBefore();
    }
    // 清除数据
    inline void resetData() 
    {
        _aabb = DAabb3::Null();

        _renderGeoms.clear();
        _keyPoints.clear();
        _pLines.clear();
        _gLines.clear();
        _gTexts.clear();

        _mGeoms.clear();

        _bRenderGeomsAlreadyHoles = false;
        _bRenderGeomsHArcTolerance = 10.0f;
    }

    // 更新绘制用的几何体
    void updateRenderGeom();
    // 更新绘制用的几何体(带孔洞)
    void updateRenderGeomWithHoles(WDGeometryMgr& gMgr, const MeshLODSelection& holesLod);

    // 获取基本几何体列表
    WDGeometries gBasicGeoms(WDGeometryMgr& gMgr
        , const std::pair<bool, float>& holesParam
        , DAabb3* pOutAabb
        , const std::optional<MeshLODSelection>& lod) const;
    // 获取碰撞检查使用的几何体列表
    WDGeometries gCollisionGeoms(WDGeometryMgr& gMgr
        , const std::pair<bool, float>& holesParam
        , DAabb3* pOutAabb
        , const std::optional<MeshLODSelection>& lod) const;

};

WD_NAMESPACE_END

