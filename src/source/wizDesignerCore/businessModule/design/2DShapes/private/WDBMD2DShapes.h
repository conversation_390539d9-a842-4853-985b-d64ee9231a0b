#pragma once
#include    "../../../WDBDBase.h"
#include    "../../WDBMDesign.h"
#include "../../private/WDBMDModelHelpter.h"

/// 暂时不考虑填充，只考虑边框

WD_NAMESPACE_BEGIN

class WD_API Base2D : public WDBDBase
    , public WDSelectionInterface
    , public WDGraphableInterface
{
protected:
    // 用于对象的三角面
    WDGeometries _geoms;
    // 用于对象的线框
    WDGraphableLines _lineObjects;
    // 文字对象
    WDGraphableTexts _textObjects;
    // 包围盒，由线包围盒与面包围盒合并计算出
    DAabb3 _aabb;
public:
    Base2D(WDNode& node)
        : WDBDBase(node)
    {
    }
protected:
    /**
     * @brief 判断当前参数下的描边是否有效
     * @return 
    */
    virtual bool bStrokeValid() const;
    /**
     * @brief 判断当前参数下的填充是否有效
     * @return 
    */
    virtual bool bFillValid() const;
public:
    virtual const WDSelectionInterface* selectionSupporter() const override
    {
        return this;
    }
    virtual WDGraphableInterface* graphableSupporter() override
    {
        return this;
    }
    virtual const WDGeometries* gRenderGeoms() const override final
    {
        return &_geoms;
    }
    virtual const WDGraphableLines* gLines() override
    {
        return &_lineObjects;
    }
    virtual const WDGraphableTexts* gTexts() override final
    {
        return &_textObjects;
    }
    virtual DAabb3 gRenderAabb() const override
    {
        return _aabb;
    }
    virtual const WDNode& ownerNode() const override
    {
        return this->node();
    }
    virtual bool pickup(const DMat4& transformMatrix, const WDPickupParam& param, PickupResult& outResult) const override;
    virtual FrameSelectResult frameSelect(const DMat4& transformMatrix, const WDFrameSelectParam& param) const override;
};

/// 目前暂时不考虑折线/闭合折线/path形成闭合区域的问题

// 线段/折线/闭合折线
// 用vert节点表示顶点
class WD_API PolyLine2D: public Base2D
{
public:
    PolyLine2D(WDNode& node)
        : Base2D(node)
    {
    }
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void onModelUpdate(WDCore&, WDNode&) override final;
};


class WD_API Path2D : public Base2D
{
public:
    Path2D(WDNode& node)
        : Base2D(node)
    {
    }
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void onModelUpdate(WDCore&, WDNode&) override final;
};

class WD_API Rect2D : public Base2D
{
public:
    Rect2D(WDNode& node)
        : Base2D(node)
    {
    }
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void onModelUpdate(WDCore&, WDNode&) override final;
};

class WD_API Circle2D : public Base2D
{
public:
    Circle2D(WDNode& node)
        : Base2D(node)
    {
    }
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void onModelUpdate(WDCore&, WDNode&) override final;
};

// 椭圆
class WD_API Ellipse2D : public Base2D
{
public:
    Ellipse2D(WDNode& node)
        : Base2D(node)
    {
    }
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void onModelUpdate(WDCore&, WDNode&) override final;
};

class WD_API Text2D : public Base2D
{
public:
    Text2D(WDNode& node)
        : Base2D(node)
    {
    }
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void onModelUpdate(WDCore&, WDNode&) override final;
};

WD_NAMESPACE_END

