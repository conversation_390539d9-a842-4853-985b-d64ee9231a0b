#include "../../../../math/geometric/curve/Curves.h"
#include "WDBMD2DShapes.h"
#include "../../../../WDCore.h"
#include "../../../../math/utils/clipper.hpp"

// 为了计算aabb，需要在z方向上扩展一个有效厚度
static constexpr double AABB_Z_OFFSET = 0.5;
// 弧线分隔段数
static constexpr size_t ArcSegs = 24;

WD_NAMESPACE_BEGIN

bool Base2D::bStrokeValid() const
{
    // 如果颜色为透明或者线宽为0则无效
    Color stroke = node().getAttribute("StrokeColor").toColor();
    double strokeWidth = node().getAttribute("StrokeWidth").toDouble();
    return stroke.a != 0 && strokeWidth > NumLimits<double>::Epsilon;
}

bool Base2D::bFillValid() const
{
    Color fill = node().getAttribute("FillColor").toColor();
    return fill.a != 0;
}

bool Base2D::pickup(const DMat4& transformMatrix, const WDPickupParam& param, PickupResult& outResult) const
{
    if (_lineObjects.empty() && _geoms.empty())
        return false;

    DAabb3 tAabb = _aabb;
    tAabb.transform(transformMatrix);
    if (!param.intersectAabb(tAabb))
        return false;

    // 拾取线数据
    bool bRet = false;
    for (const auto& line : _lineObjects)
    {
        if (line.pickup(transformMatrix, param, outResult))
            bRet = true;
    }
    // 拾取面数据
    for (auto pGeom : _geoms)
    {
        if (pGeom != nullptr && pGeom->pickup(transformMatrix, param, outResult))
            bRet = true;
    }

    return bRet;
}

Base2D::FrameSelectResult Base2D::frameSelect(const DMat4& transformMatrix, const WDFrameSelectParam& param) const
{
    using SelectRet = WDSelectionInterface::FrameSelectResult;

    if (_lineObjects.empty() && _geoms.empty())
        return SelectRet::FSR_NoData;

    DAabb3 tAabb = _aabb;
    tAabb.transform(transformMatrix);
    auto ret = param.intersectAabb(tAabb);
    switch (ret)
    {
    case WD::WDFrameSelectParam::R_InvaildData:
        return SelectRet::FSR_NoData;
        break;
    case WD::WDFrameSelectParam::R_WhollyWithout:
        return SelectRet::FSR_WhollyWithout;
        break;
    case WD::WDFrameSelectParam::R_PartiallyWithin:
        break;
    case WD::WDFrameSelectParam::R_WhollyWithin:
        break;
    default:
        return SelectRet::FSR_NoData;
        break;
    }
    // 框选线数据
    std::vector<SelectRet> rets;
    rets.reserve(_lineObjects.size() + 1);
    for (const auto& line : _lineObjects)
    {
        rets.emplace_back(line.frameSelect(transformMatrix, param));
    }
    // 框选面数据
    for (auto pGeom : _geoms)
    {
        if (pGeom != nullptr)
        {
            rets.emplace_back(pGeom->frameSelect(transformMatrix, param));
        }
    }
    return WDSelectionInterface::MergeFrameSelectResult(rets);
}

// 通过三角剖分获取所有封闭区域（存在自相交的情况）
static WDGeometries AreasByPoints(const FVec3Vector& points, WDCore& core)
{
    WDGeometries geoms;
    // 获取所有简单多边形
    constexpr auto sLimit = 10000.0;
    constexpr auto sLimitInv = 1.0 / sLimit;
    ClipperLib::Path path;
    path.reserve(points.size());
    for (const auto& p : points)
    {
        auto iX = static_cast<ClipperLib::cInt>(p.x * sLimit);
        auto iY = static_cast<ClipperLib::cInt>(p.y * sLimit);
        path.push_back(ClipperLib::IntPoint(iX, iY));
    }

    // 输出所有多边形的顶点
    ClipperLib::Paths outPaths;
    ClipperLib::SimplifyPolygon(path, outPaths, ClipperLib::pftNonZero);
    // 画出每个多边形
    geoms.reserve(outPaths.size());
    for (const auto& p : outPaths)
    {
        FVec3Vector poss;
        poss.reserve(p.size());
        for (const auto& v : p)
        {
            auto fX = static_cast<float>(static_cast<double>(v.X) * sLimitInv);
            auto fY = static_cast<float>(static_cast<double>(v.Y) * sLimitInv);
            poss.push_back({ fX, fY, 0 });
        }
        if (poss.size() - 1 > NumLimits<byte>::Max || poss.size() < 3)
            continue;
        auto shapeIndices = TEarcut<FVec3, byte>::Exec(poss);
        if (shapeIndices.size() < 3)
            continue;

        // 为每个多边形构建几何体
        auto pMesh = WD::WDMesh::MakeShared();
        pMesh->setPositions(poss);
        pMesh->computeAabb();
        pMesh->setVertDescSetPtr(WDVertDescSetInstance::v3n3());

        WDPrimitiveSet  pri;
        pri.setPrimitiveType(WDPrimitiveSet::PT_Triangles);
        // 确定平面法线
        FPlane plane(poss[shapeIndices[0]], poss[shapeIndices[1]], poss[shapeIndices[2]]);
        std::vector<FVec3> norms;
        norms.resize(poss.size(), plane.normal);
        pMesh->setNormals(norms);

        pri.setDrawElementByteData(shapeIndices);
        pMesh->addPrimitiveSet(pri, WDMesh::Solid);

        auto pPolyhedron = core.geometryMgr().createPolyhedron(pMesh);
        if (pPolyhedron != nullptr)
        {
            pPolyhedron->gFlags() = WDGeometry::GF_Entity;
            geoms.push_back(pPolyhedron);
        }
    }
    return geoms;
}
WDBDBase* PolyLine2D::clone(WDNode& node) const
{
    auto p = new PolyLine2D(node);
    p->copy(*this);
    return p;
}

void PolyLine2D::onModelUpdate(WDCore& core, WDNode& node)
{
    noticeRemoveBefore();
    _lineObjects.clear();
    _geoms.clear();

    if (!bStrokeValid() && !bStrokeValid())
        return;

    // 读取顶点
    FVec3Vector points;
    points.reserve(node.childCount());
    for (auto pChild : node.children())
    {
        if (pChild == nullptr || !pChild->isType("VERT"))
            continue;
        points.push_back(FVec3(pChild->localTransform().extractTranslation()));
    }
    if (bStrokeValid())
    {
        _lineObjects.resize(1);
        _lineObjects.back().vMode = node.getAttribute("BLoop").toBool() ? WDGraphableLine::VM_Loop : WDGraphableLine::VM_Strip;
        _lineObjects.back().points = points;
        _lineObjects.back().color = node.getAttribute("StrokeColor").toColor();
        _lineObjects.back().lineWidth = static_cast<float>(node.getAttribute("StrokeWidth").toDouble());
        _lineObjects.back().style = static_cast<WDGraphableLine::Style>(node.getAttribute("LineStyle").toInt());
        _aabb = _lineObjects.back().calcAabb(AABB_Z_OFFSET);
    }

    if (bFillValid())
    {
        auto geoms = AreasByPoints(points, core);
        _geoms.reserve(geoms.size());
        for (const auto pGeom : geoms)
        {
            if (pGeom == nullptr)
                continue;
            _aabb.expandByAabb(pGeom->aabb());
            _geoms.push_back(pGeom);
        }
        // 设置节点颜色
        node.setAttribute("AutoColor", node.getAttribute("FillColor").toColor());
    }
    noticeAddAfter();
}

WDBDBase* Path2D::clone(WDNode& node) const
{
    auto p = new Path2D(node);
    p->copy(*this);
    return p;
}

void Path2D::onModelUpdate(WDCore& core, WDNode& node)
{
    noticeRemoveBefore();
    _lineObjects.clear();
    _geoms.clear();

    if (!bStrokeValid() && !bFillValid())
        return ;

    FVec3Vector poss;
    _lineObjects.reserve(node.childCount());
    for (auto pChild : node.children())
    {
        if (pChild == nullptr || !pChild->isType("CMD2D"))
            continue;
        // 读取命令和顶点
        const std::string cmdType = pChild->getAttribute("PathCmd").toString();
        FVec3Vector verts;
        verts.reserve(pChild->childCount());
        for (auto pVert : pChild->children())
        {
            if (pVert == nullptr || !pVert->isType("VERT"))
                continue;
            verts.push_back(FVec3(pVert->localTransform().extractTranslation()));
        }

        if (cmdType == "M")
        {
            // 处理上一个图元
            if (poss.size() >= 2)
            {
                WDGraphableLine line;
                line.points = poss;
                line.vMode = WDGraphableLine::VM_Strip;
                line.color = node.getAttribute("StrokeColor").toColor();
                line.lineWidth = static_cast<float>(node.getAttribute("StrokeWidth").toDouble());
                line.style = static_cast<WDGraphableLine::Style>(node.getAttribute("lineStyle").toInt());
                _lineObjects.push_back(line);

                poss.clear();
            }
            // M下只有一个顶点
            if (!verts.empty())
                poss.push_back(verts[0]);
        }
        else if (cmdType == "L")
        {
            if (!verts.empty())
                poss.push_back(verts[0]);
        }
        else if (cmdType == "C")
        {
            if (poss.empty() && verts.size() < 3)
                continue;
            TCubicBezierCurve2<float> cubicCurve({ poss.back().x, poss.back().y }, verts[0].xy(), verts[1].xy(), verts[2].xy());
            for (const auto& p : cubicCurve.points(ArcSegs))
            {
                poss.push_back({ p.x, p.y, 0 });
            }
        }
        else if (cmdType == "Z")
        {
            if (poss.empty())
                continue;
            poss.push_back(poss[0]);
        }
        else
        {
            //
            auto assertInfo = std::string("不支持该命令：") + cmdType;
            assert(false && assertInfo.data());
        }
    }

    // 处理最后一个图元
    if (poss.size() >= 2)
    {
        WDGraphableLine line;
        line.points = poss;
        line.vMode = WDGraphableLine::VM_Strip;
        line.color = node.getAttribute("StrokeColor").toColor();
        line.lineWidth = static_cast<float>(node.getAttribute("StrokeWidth").toDouble());
        line.style = static_cast<WDGraphableLine::Style>(node.getAttribute("LineStyle").toInt());
        _lineObjects.push_back(line);
    }

    if (bFillValid())
    {
        for (const auto& line : _lineObjects)
        {
            // 每条线单独计算
            for (const auto pGeom : AreasByPoints(line.points, core))
            {
                if (pGeom == nullptr)
                    continue;
                _aabb.expandByAabb(pGeom->aabb());
                _geoms.push_back(pGeom);
            }
        }
        // 设置节点颜色
        node.setAttribute("AutoColor", node.getAttribute("FillColor").toColor());
    }

    if (bStrokeValid())
    {
        // 计算aabb
        for (const auto& line : _lineObjects)
        {
            _aabb.expandByAabb(line.calcAabb(AABB_Z_OFFSET));
        }
    }
    else
        _lineObjects.clear();

    noticeAddAfter();
}

WDBDBase* Rect2D::clone(WDNode& node) const
{
    auto p = new Rect2D(node);
    p->copy(*this);
    return p;
}

void Rect2D::onModelUpdate(WDCore& core, WDNode& node)
{
    noticeRemoveBefore();
    _lineObjects.clear();
    _geoms.clear();

    if (!bStrokeValid() && !bStrokeValid())
        return;

    auto center = node.getAttribute("Center").toDVec3();
    auto width = float(node.getAttribute("Width").toDouble());
    auto height = float(node.getAttribute("Height").toDouble());
    auto rx = node.getAttribute("CornerRx").toDouble();
    auto ry = node.getAttribute("CornerRy").toDouble();

    if (width <= NumLimits<float>::Epsilon || height <= NumLimits<float>::Epsilon)
        return;

    // p0 p1
    // p2 p3
    auto p0 = FVec3(center) + FVec3{float(- width / 2), float(height / 2), 0};// 左上
    auto p1 = p0 + FVec3(width, 0, 0);// 右上
    auto p2 = p0 + FVec3(0, -height, 0);// 左下
    auto p3 = p0 + FVec3(width, -height, 0);// 右下

    if (rx < NumLimits<double>::Epsilon || ry < NumLimits<double>::Epsilon)
    {
        // 统一为不等于0的
        auto v = Max(rx, ry);
        rx = v;
        ry = v;
    }

    FVec3Vector poss;
    if (DVec2::IsZero(DVec2{ rx, ry }))
    {
        poss.reserve(5);
        poss.push_back(p0);
        poss.push_back(p2);
        poss.push_back(p3);
        poss.push_back(p1);
    }
    else
    {
        // 圆角中心
        Vec2 c1(p0.x + rx, p0.y - ry);
        Vec2 c2(p1.x - rx, p1.y - ry);
        Vec2 c3(p2.x + rx, p2.y + ry);
        Vec2 c4(p3.x - rx, p3.y + ry);

        //根据rx,ry创建圆弧
        TEllipseCurve<double> eCurv1(c1.x, c1.y, rx, ry, 0.5 * PI, PI);
        auto points1 = eCurv1.points(ArcSegs);
        poss.reserve(4 * points1.size() + 1);
        for (const auto& p : points1)
        {
            poss.push_back({ float(p.x), float(p.y), 0 });
        }
        TEllipseCurve<double> eCurv3(c3.x, c3.y, rx, ry, PI, 1.5 * PI);
        for (const auto& p : eCurv3.points(ArcSegs))
        {
            poss.push_back({ float(p.x), float(p.y), 0 });
        }
        TEllipseCurve<double> eCurv4(c4.x, c4.y, rx, ry, 1.5 * PI, 2.0 * PI);
        for (const auto& p : eCurv4.points(ArcSegs))
        {
            poss.push_back({ float(p.x), float(p.y), 0 });
        }
        TEllipseCurve<double> eCurv2(c2.x, c2.y, rx, ry, 0, 0.5 * PI);
        for (const auto& p : eCurv2.points(ArcSegs))
        {
            poss.push_back({ float(p.x), float(p.y), 0 });
        }
    }

    if (bStrokeValid())
    {
        _lineObjects.resize(1);
        _lineObjects.back().vMode = WDGraphableLine::VM_Loop;
        _lineObjects.back().points = poss;
        _lineObjects.back().color = node.getAttribute("StrokeColor").toColor();
        _lineObjects.back().lineWidth = static_cast<float>(node.getAttribute("StrokeWidth").toDouble());
        _lineObjects.back().style = static_cast<WDGraphableLine::Style>(node.getAttribute("LineStyle").toInt());
        _aabb = _lineObjects.back().calcAabb(AABB_Z_OFFSET);
    }

    // 三角面
    if (bFillValid())
    {
        // 加入中心点
        poss.push_back(FVec3(center));
        // 目前考虑将顶点限制在byte大小内
        if (poss.size() - 1 > NumLimits<byte>::Max)
        {
            return ;
        }
        // 由于圆角的存在，这里直接按照类似圆的方式进行构造
        size_t idxLast = poss.size() - 1;

        auto pMesh = WD::WDMesh::MakeShared();
        pMesh->setPositions(poss);
        pMesh->computeAabb();
        pMesh->setVertDescSetPtr(WDVertDescSetInstance::v3n3());

        WDPrimitiveSet  pri;
        pri.setPrimitiveType(WDPrimitiveSet::PT_Triangles);
        WDPrimitiveSet::ElementByteData data;
        data.resize((poss.size() - 1) * 3);
        for (int i = 0; i != idxLast; i++)
        {
            data[i * 3 + 0] = static_cast<byte>(idxLast);
            data[i * 3 + 1] = static_cast<byte>(i);
            data[i * 3 + 2] = static_cast<byte>((i + 1) % (poss.size() - 1));
        };
        // 确定平面法线
        std::vector<FVec3> norms;
        FPlane plane(poss[data[0]], poss[data[1]], poss[data[2]]);
        norms.resize(poss.size(), plane.normal);
        pMesh->setNormals(norms);

        pri.setDrawElementByteData(data);
        pMesh->addPrimitiveSet(pri, WDMesh::Solid);

        auto pPolyhedron = core.geometryMgr().createPolyhedron(pMesh);
        if (pPolyhedron != nullptr)
        {
            pPolyhedron->gFlags() = WDGeometry::GF_Entity;
            _geoms.push_back(pPolyhedron);
            _aabb.expandByAabb(pPolyhedron->aabb());
            // 设置节点颜色
            node.setAttribute("AutoColor", node.getAttribute("FillColor").toColor());
        }
    }
    noticeAddAfter();
}

WDBDBase* Circle2D::clone(WDNode& node) const
{
    auto p = new Circle2D(node);
    p->copy(*this);
    return p;
}

void Circle2D::onModelUpdate(WDCore& core, WDNode& node)
{
    noticeRemoveBefore();
    _lineObjects.clear();
    _geoms.clear();

    if (!bStrokeValid() && !bStrokeValid())
        return;

    auto center = node.getAttribute("Center").toDVec3();
    auto r = node.getAttribute("Diameter").toDouble() / 2.0f;
    if (r <= NumLimits<double>::Epsilon)
        return;

    TArcCurve<double> circle(center.x, center.y, r);
    auto points = circle.points(ArcSegs);
    FVec3Vector  poss;
    poss.reserve(points.size() + 1);
    for (const auto& p : points) // 给出分段数
    {
        poss.push_back({ float(p.x), float(p.y), 0 });
    }

    if (bStrokeValid())
    {
        _lineObjects.resize(1);
        _lineObjects.back().vMode = WDGraphableLine::VM_Loop;
        _lineObjects.back().points = poss;
        _lineObjects.back().color = node.getAttribute("StrokeColor").toColor();
        _lineObjects.back().lineWidth = static_cast<float>(node.getAttribute("StrokeWidth").toDouble());
        _lineObjects.back().style = static_cast<WDGraphableLine::Style>(node.getAttribute("LineStyle").toInt());
        _aabb.expandByAabb(_lineObjects.back().calcAabb(AABB_Z_OFFSET));
    }
    // 三角面
    if (bFillValid())
    {
        poss.push_back(FVec3(center)); // 圆心
        // 目前考虑将顶点限制在byte大小内
        if (poss.size() - 1 > NumLimits<byte>::Max)
        {
            return;
        }
        // 圆心的下标
        int idxCenter = int(poss.size()) - 1;

        auto pMesh = WD::WDMesh::MakeShared();
        pMesh->setPositions(poss);
        pMesh->computeAabb();
        pMesh->setVertDescSetPtr(WDVertDescSetInstance::v3n3());

        WDPrimitiveSet  pri;
        pri.setPrimitiveType(WDPrimitiveSet::PT_Triangles);
        WDPrimitiveSet::ElementByteData data;
        data.resize((poss.size() - 1) * 3);
        for (int i = 0; i != idxCenter; i++)
        {
            data[i * 3 + 0] = static_cast<byte>(idxCenter);
            data[i * 3 + 1] = static_cast<byte>(i);
            data[i * 3 + 2] = static_cast<byte>((i + 1) % (poss.size() - 1));
        };
        // 确定平面法线
        std::vector<FVec3> norms;
        FPlane plane(poss[data[0]], poss[data[1]], poss[data[2]]);
        norms.resize(poss.size(), plane.normal);
        pMesh->setNormals(norms);

        pri.setDrawElementByteData(data);
        pMesh->addPrimitiveSet(pri, WDMesh::Solid);

        auto pPolyhedron = core.geometryMgr().createPolyhedron(pMesh);
        if (pPolyhedron != nullptr)
        {
            pPolyhedron->gFlags() = WDGeometry::GF_Entity;
            _geoms.push_back(pPolyhedron);
            _aabb.expandByAabb(pPolyhedron->aabb());
            // 设置节点颜色
            node.setAttribute("AutoColor", node.getAttribute("FillColor").toColor());
        }
    }
    noticeAddAfter();
}

WDBDBase* Ellipse2D::clone(WDNode& node) const
{
    auto p = new Ellipse2D(node);
    p->copy(*this);
    return p;
}

void Ellipse2D::onModelUpdate(WDCore& core, WDNode& node)
{
    noticeRemoveBefore();
    _lineObjects.clear();
    _geoms.clear();

    if (!bStrokeValid() && !bStrokeValid())
        return;

    auto center = node.getAttribute("Center").toDVec3();
    auto rx = node.getAttribute("xDiameter").toDouble() / 2.0f;
    auto ry = node.getAttribute("yDiameter").toDouble() / 2.0f;
    if (rx <= NumLimits<double>::Epsilon || ry <= NumLimits<double>::Epsilon)
        return;

    FVec3Vector  poss;
    TEllipseCurve<double> ellipse(center.x, center.y, rx, ry);
    auto points = ellipse.points(ArcSegs);
    poss.reserve(points.size() + 1);
    for (const auto& p : points)
    {
        poss.push_back({ float(p.x), float(p.y), 0 });
    }

    if (bStrokeValid())
    {
        _lineObjects.resize(1);
        _lineObjects.back().vMode = WDGraphableLine::VM_Loop;
        _lineObjects.back().points = poss;
        _lineObjects.back().color = node.getAttribute("StrokeColor").toColor();
        _lineObjects.back().lineWidth = static_cast<float>(node.getAttribute("StrokeWidth").toDouble());
        _lineObjects.back().style = static_cast<WDGraphableLine::Style>(node.getAttribute("LineStyle").toInt());
        _aabb = _lineObjects.back().calcAabb(AABB_Z_OFFSET);
    }

    // 三角面
    if (bFillValid())
    {
        poss.push_back(FVec3(center)); // 圆心
        // 目前考虑将顶点限制在byte大小内
        if (poss.size() - 1 > NumLimits<byte>::Max)
        {
            return;
        }
        // 圆心的下标
        int idxCenter = int(poss.size()) - 1;

        auto pMesh = WD::WDMesh::MakeShared();
        pMesh->setPositions(poss);
        pMesh->computeAabb();
        pMesh->setVertDescSetPtr(WDVertDescSetInstance::v3n3());

        WDPrimitiveSet  pri;
        pri.setPrimitiveType(WDPrimitiveSet::PT_Triangles);
        WDPrimitiveSet::ElementByteData data;
        data.resize((poss.size() - 1) * 3);
        for (int i = 0; i != idxCenter; i++)
        {
            data[i * 3 + 0] = static_cast<byte>(idxCenter);
            data[i * 3 + 1] = static_cast<byte>(i);
            data[i * 3 + 2] = static_cast<byte>((i + 1) % (poss.size() - 1));
        };
        // 确定平面法线
        std::vector<FVec3> norms;
        FPlane plane(poss[data[0]], poss[data[1]], poss[data[2]]);
        norms.resize(poss.size(), plane.normal);
        pMesh->setNormals(norms);

        pri.setDrawElementByteData(data);
        pMesh->addPrimitiveSet(pri, WDMesh::Solid);

        auto pPolyhedron = core.geometryMgr().createPolyhedron(pMesh);
        if (pPolyhedron != nullptr)
        {
            pPolyhedron->gFlags() = WDGeometry::GF_Entity;
            _geoms.push_back(pPolyhedron);
            _aabb.expandByAabb(pPolyhedron->aabb());
            // 设置节点颜色
            node.setAttribute("AutoColor", node.getAttribute("FillColor").toColor());
        }
    }
    noticeAddAfter();
}

WDBDBase* Text2D::clone(WDNode& node) const
{
    auto p = new Text2D(node);
    p->copy(*this);
    return p;
}

void Text2D::onModelUpdate(WDCore&, WDNode& node)
{
    noticeRemoveBefore();
    _textObjects.clear();

    auto position = node.getAttribute("TextPosition").toDVec3();
    double fontSize = node.getAttribute("FontSize").toDouble();
    //std::string fontStyle = node.getAttribute("FontStyle").toString();
    std::string text = node.getAttribute("Text").toString();
    // 按照描边色来，暂时不考虑填充色
    Color color = node.getAttribute("StrokeColor").toColor();
    bool fixedPixel = node.getAttribute("FixedPixel").toBool();
    // 单位向量
    Vec3 upDir = node.getAttribute("UpDir").toDVec3();
    Vec3 rightDir = node.getAttribute("RightDir").toDVec3();
    // 对齐方式
    auto vAlign = node.getAttribute("VAlign").toInt();
    auto hAlign = node.getAttribute("HAlign").toInt();

    if (fontSize <= NumLimits<double>::Epsilon || text.empty() || color.a == 0)
        return;

    // 使节点上的缩放生效
    auto nodeScale = node.globalScaling();
    upDir *= nodeScale.y;
    rightDir *= nodeScale.x;

    // 使节点上的旋转生效
    auto nodeRot = node.globalRotation();
    auto rotMat = WD::Mat4::FromQuat(nodeRot);
    auto realUpDir = rotMat * upDir;
    auto realRightDir = rotMat * rightDir;

    _textObjects.resize(1);
    _textObjects.back().text = stringToWString(text);
    _textObjects.back().color = color;
    _textObjects.back().fixedPixel = fixedPixel;
    _textObjects.back().upDir = FVec3(realUpDir);
    _textObjects.back().rightDir = FVec3(realRightDir);
    _textObjects.back().position = FVec3(position);
    _textObjects.back().fontSize = static_cast<int>(fontSize);
    // svg中，text-anchor/dominant-baseline可以设置对齐方式
    _textObjects.back().vAlign = WDGraphableText::VAlign(vAlign);
    _textObjects.back().hAlign = WDGraphableText::HAlign(hAlign);
    noticeAddAfter();
}

WD_NAMESPACE_END
