#include "WDBMFGridUtils.h"
#include "scene/WDScene.h"
#include "WDCore.h"
#include "private/WDBMDSctnSub.h"

bool WD::WDBMFGridUtils::SetFrmwShowType(WD::WDCore& core, WDBMFGridUtils::FrmwShowInfos& infos)
{
	bool success = true;
	for (auto& pair : infos)
	{
		WD::WDNode& frmw = pair.first;
		FrmwShowType& type = pair.second;
		//校验类型
		if (!frmw.isType("FRMW") || frmw.getAttribute("Purpose").convertToString() != "GRID")
		{
			success = false;
			continue;
		}
		for (auto& sbfr : frmw.children())
		{
			if (sbfr == nullptr)
			{
				continue;
			}
			//高程编号
			std::string elevation = sbfr->getAttribute("Description").convertToString();
			std::vector<WD::WDNode::SharedPtr> xGrds, yGrds;
			xGrds.reserve(sbfr->childCount() / 2);//x轴线
			yGrds.reserve(sbfr->childCount() / 2);//y轴线
			for (size_t i = 0; i < sbfr->children().size(); ++i)
			{
				auto& sctn = sbfr->children()[i];
				if (sctn == nullptr)
				{
					continue;
				}
				const auto& gType = sctn->getAttribute("Gtype").toWord();
				(gType == "XGRD" ? xGrds : yGrds).push_back(sctn);

				auto pData = dynamic_cast<WD::WDBMDSctnSCTN*>(sctn->getBDBase());
				if (pData == nullptr)
				{
					continue;
				}
				pData->setElevation(elevation);
			}
			//计算间隔
			for (size_t i = 0; i < xGrds.size(); i++)
			{				
				WD::WDNode::SharedPtr current = xGrds[i];
				auto pData = dynamic_cast<WD::WDBMDSctnSCTN*>(current->getBDBase());
				if (pData == nullptr)
				{
					continue;
				}
				double offsetToLast = 0.0;
				if (i != 0 && xGrds[i - 1] != nullptr)
				{					
					auto& lastOne = xGrds[i - 1];
					WD::DVec3 start = current->getAttribute("Posstart").toDVec3();
					WD::DVec3 lastStart = lastOne->getAttribute("Posstart").toDVec3();
					offsetToLast = WD::DVec3::Distance(start, lastStart);
				}
				pData->setShowType(type);
				pData->setOffsetToLast(offsetToLast);
			}
			for (size_t i = 0; i < yGrds.size(); i++)
			{
				WD::WDNode::SharedPtr current = yGrds[i];
				auto pData = dynamic_cast<WD::WDBMDSctnSCTN*>(current->getBDBase());
				if (pData == nullptr)
				{
					continue;
				}
				double offsetToLast = 0.0;
				if (i != 0 && yGrds[i - 1] != nullptr)
				{					
					auto& lastOne = yGrds[i - 1];
					WD::DVec3 start = current->getAttribute("Posstart").toDVec3();
					WD::DVec3 lastStart = lastOne->getAttribute("Posstart").toDVec3();
					offsetToLast = WD::DVec3::Distance(start, lastStart);
				}
				pData->setShowType(type);
				pData->setOffsetToLast(offsetToLast);
			}
		}
		frmw.triggerUpdate(true);
	}
	core.needRepaint();
	return success;
}

WD::WDBMFGridUtils::FrmwShowType WD::WDBMFGridUtils::GetFrmwShowType(WDNode& frmw)
{
	//校验类型
	if (!frmw.isType("FRMW") || frmw.getAttribute("Purpose").convertToString() != "GRID")
	{
		return FST_Nothing;
	}
	for (auto& sbfr : frmw.children())
	{
		if (sbfr == nullptr)
		{
			continue;
		}
		for(auto& sctn : sbfr->children())
		{
			if (sctn == nullptr)
			{
				continue;
			}
			auto pData = dynamic_cast<WD::WDBMDSctnSCTN*>(sctn->getBDBase());
			if (pData == nullptr)
			{
				continue;
			}
			return pData->getShowType();
		}
	}	
	return FST_Nothing;
}