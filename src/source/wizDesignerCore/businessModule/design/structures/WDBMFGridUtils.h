#pragma     once

#include    "../../../node/WDNode.h"

WD_NAMESPACE_BEGIN
class WDCore;
class WD_API WDBMFGridUtils
{
public:
	enum FrmwShowType
	{
		//不显示编号
		FST_Nothing = 0,
		//显示编号
		FST_Key = 1,
		//显示间隔距离
		FST_Position = 2,
		//显示高程和编号
		FST_LvlKey = 3
	};
	using FrmwShowInfos = std::vector<std::pair<WDNode&, FrmwShowType>>;
	/**
	 * @brief 按类型显示frmw
	 * @param core 上下文
	 * @param infos 显示类型
	 * @return
	*/
	static bool SetFrmwShowType(WDCore& core, FrmwShowInfos& infos);
	/**
	 * @brief 获取FRMW节点的显示类型
	 * @param frmw 
	 * @return 
	*/
	static FrmwShowType GetFrmwShowType(WDNode& frmw);
};
WD_NAMESPACE_END