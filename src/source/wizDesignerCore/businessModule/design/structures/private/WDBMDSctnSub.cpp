#include "WDBMDSctnSub.h"
#include "WDCore.h"
#include "../../../../math/geometric/standardPrimitives/WDLoftSPline.h"
#include "../../private/WDBMDHolesCommon.h"
#include "WDBMDFittSub.h"
#include <stdio.h>

WD_NAMESPACE_BEGIN


WDBMDStruGENSEC::WDBMDStruGENSEC(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDStruGENSEC::~WDBMDStruGENSEC()
{}
void    WDBMDStruGENSEC::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDStruGENSEC* pSrc = dynamic_cast<const WDBMDStruGENSEC*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDStruGENSEC::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDStruGENSEC::graphableSupporter()
{
    return &_modelHelpter;
}

void WDBMDStruGENSEC::mData(WDCore& core, WDBMDModelHelpter::MData& data)
{
    WDUnused(core);
    // 如果具有等级，则不生成虚线
    if (mSpref().refNode() != nullptr)
        return;

    auto& node = this->node();
    WDNode::SharedPtr pNodeSPINE = nullptr;
    // 获取到子节点(SPINE)以计算放样的样条曲线
    for (size_t i = 0; i < node.childCount(); ++i)
    {
        auto pChild = node.childAt(i);
        if (pChild == nullptr)
            continue;
        if (pChild->isType("SPINE"))
        {
            pNodeSPINE = pChild;
            break;
        }
    }
    if (pNodeSPINE == nullptr)
    {
        assert(false && "未找到SPINE类型的子节点!");
        return;
    }
    // 获取SPINE的子节点: POINSP和CURVE节点
    // 其中子节点的排布方式为: POINSP CURVE POINSP CURVE POINSP ...
    // 表示: 每一组 POINSP CURVE POINSP 可能将生成一段圆弧(也有可能不会生成圆弧)
    WDLoftSPline spline;
    for (size_t i = 0; i < pNodeSPINE->childCount(); ++i)
    {
        auto pChild = pNodeSPINE->childAt(i);
        if (pChild == nullptr)
            continue;
        if (pChild->isType("POINSP"))
        {
            WDLoftSPline::Point point;
            point.position = FVec3(pChild->getAttribute("Position").toDVec3());
            spline.addPoint(point);
        }
        else if (pChild->isType("CURVE"))
        {
            WDLoftSPline::Curve curve;
            curve.type = WDLoftSPline::Curve::TypeFromString(pChild->getAttribute("CurType").toWord().c_str());
            curve.position = FVec3(pChild->getAttribute("Position").toDVec3());
            curve.cPoint = FVec3(pChild->getAttribute("Cposition").toDVec3());
            curve.radius = static_cast<float>(pChild->getAttribute("Radius").toDouble());
            curve.radsetFlag = pChild->getAttribute("Radsetflag").toBool();
            curve.bulgeFactor = static_cast<float>(pChild->getAttribute("Bulgefactor").toDouble());
            spline.addCurve(curve);
        }
        else
        {
            // 其他的子节点类型，这里忽略
        }
    }
    // 获取曲线顶点并设置给模型数据
    auto vs = spline.vertices();
    if (!vs.empty())
    {
        data.gLines.push_back(WDGraphableLine());
        auto& gLine     = data.gLines.back();
        gLine.points    = std::move(vs);
        gLine.color     = Color(255, 165, 0, 255);
        gLine.style     = WDGraphableLine::Style::DashLine;
        gLine.vMode     = WDGraphableLine::VMode::VM_Strip;
    }
}
WDBMNodeRef WDBMDStruGENSEC::mSpref() const
{
    return node().getAttribute("Spref").toNodeRef();
}
void WDBMDStruGENSEC::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);

    // 获取当前对象关联的节点
    auto& node = this->node();

    WDNode::SharedPtr pNodeSPINE = nullptr;
    // 获取到子节点(SPINE)以计算放样的样条曲线
    for (size_t i = 0; i < node.childCount(); ++i)
    {
        auto pChild = node.childAt(i);
        if (pChild == nullptr || !pChild->isType("SPINE"))
            continue;
        pNodeSPINE = pChild;
        break;
    }
    if (pNodeSPINE == nullptr)
    {
        assert(false && "未找到SPINE类型的子节点!");
        return;
    }
    WDLoftSPline spline;
    // 获取SPINE的子节点: POINSP和CURVE节点
    // 其中子节点的排布方式为: POINSP CURVE POINSP CURVE POINSP ...
    // 表示: 每一组 POINSP CURVE POINSP 可能将生成一段圆弧(也有可能不会生成圆弧)
    for (size_t i = 0; i < pNodeSPINE->childCount(); ++i)
    {
        auto pChild = pNodeSPINE->childAt(i);
        if (pChild == nullptr)
            continue;
        if (pChild->isType("POINSP"))
        {
            WDLoftSPline::Point point;
            point.position = FVec3(pChild->getAttribute("Position").toDVec3());
            spline.addPoint(point);
        }
        else if (pChild->isType("CURVE"))
        {
            WDLoftSPline::Curve curve;
            curve.type = WDLoftSPline::Curve::TypeFromString(pChild->getAttribute("CurType").toWord().c_str());
            curve.position = FVec3(pChild->getAttribute("Position").toDVec3());
            curve.cPoint = FVec3(pChild->getAttribute("Cposition").toDVec3());
            curve.radius = static_cast<float>(pChild->getAttribute("Radius").toDouble());
            curve.radsetFlag = pChild->getAttribute("Radsetflag").toBool();
            curve.bulgeFactor = static_cast<float>(pChild->getAttribute("Bulgefactor").toDouble());
            spline.addCurve(curve);
        }
        else
        {
            // 其他的子节点类型，这里忽略
        }
    }
    const auto sDrn = pNodeSPINE->getAttribute("Drnstart").toDVec3();
    if (sDrn != WD::DVec3::Zero())
        gVars.setValue(WDBMCModelBuilder::GVarDrnS, sDrn);
    const auto eDrn = pNodeSPINE->getAttribute("Drnend").toDVec3();
    if (eDrn != WD::DVec3::Zero())
        gVars.setValue(WDBMCModelBuilder::GVarDrnE, eDrn);

    gVars.setValue(WDBMCModelBuilder::GVarJustLine, std::string(node.getAttribute("Jusline").toWord()));
    // bAngle
    gVars.setValue(WDBMCModelBuilder::GVarBAngle, node.getAttribute("Bangle").toDouble());
    // 放样体样条线
    gVars.setValue(WDBMCModelBuilder::GVarLoftSpline, spline);
    // 设计参数
    char    key[128] = { 0 };
    auto desParams = node.getAttribute("Desparam").toDoubleVector();
    for (int i = 0; i < desParams.size(); ++i)
    {
        sprintf(key, "DESP %d", (i + 1));
        gVars.setValue(key, desParams[i]);
    }
}
bool WDBMDStruGENSEC::mHasHoles() const
{
    for (auto& pChild : this->node().children())
    {
        if (pChild == nullptr)
            continue;
        // 检查子节点中是否带有孔洞
        if (WDBMDModelHelpter::HasNGeom(*pChild))
            return true;
        if (CheckNGeomCOFI(*pChild)     // 贯穿件
            || CheckNGeomFitt(*pChild)  // 附件
            || CheckNGeomTMPL(*pChild)) // 模板
            return true;
    }
    return false;
}
void WDBMDStruGENSEC::mNGeoms(WDGeometryBoolean::TsfmGeometries& outTsfmGeoms) const
{
    for (auto pChild : this->node().children())
    {
        if (pChild == nullptr)
            continue;
        // 检查子节点中是否带有孔洞
        if (WDBMDModelHelpter::HasNGeom(*pChild))
        {
            auto nGeoms = WDBMDModelHelpter::GetNGeoms(*pChild);
            if (!nGeoms.empty())
                outTsfmGeoms.push_back({ pChild->localTransform(), nGeoms });
        }
        // 贯穿件
        GetNGeomCOFI(*pChild, this->node().globalTransform(), outTsfmGeoms);
        // 附件
        GetNGeomFitt(*pChild, DMat4::Identity(), outTsfmGeoms);
        // 模板
        GetNGeomTMPL(*pChild, DMat4::Identity(), outTsfmGeoms);
    }
}

void WDBMDStruGENSEC::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(bInsu);
    this->mGVars(core, outGVars);
}
void WDBMDStruGENSEC::onModelUpdate(WDCore& core, WDNode& node)
{
    WDUnused(node);
    _modelHelpter.updateModel(core);
}

WDBMDSctnSCTN::WDBMDSctnSCTN(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
    ,_offsetToLast(0.0)
    ,_showType(WDBMFGridUtils::FrmwShowType::FST_Nothing)
{
}
WDBMDSctnSCTN::~WDBMDSctnSCTN()
{

}

void WDBMDSctnSCTN::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDSctnSCTN* pSrc = dynamic_cast<const WDBMDSctnSCTN*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
    _showType = pSrc->_showType;//显示类型
    _offsetToLast = pSrc->_offsetToLast;//与上一个之间的偏移
    _elevation = pSrc->_elevation;
}

const WDSelectionInterface* WDBMDSctnSCTN::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface*   WDBMDSctnSCTN::graphableSupporter()
{
    return &_modelHelpter;
}
/**
 * @brief 获取轴线显示的文本
 * @param sctn 
 * @return 
*/
static void GetText(WDBMDSctnSCTN& sctn, std::string& headText, std::string& tailText)
{
    WD::WDNode& node = sctn.node();
    WD::WDNode::SharedPtr parent = node.parent();
    if (parent == nullptr)
    {
        return;
    }
    //获取第一个x轴线或第一个y轴线
    auto number = node.getAttribute("Number").toInt();
    if (number == 1)
    {
        auto gType = node.getAttribute("Gtype").toWord();
        tailText = gType == "XGRD" ? "Y" : "X";
    }
  
    //高程编号    
    auto showType = sctn.getShowType();
    //默认不显示
    if (showType == WDBMFGridUtils::FrmwShowType::FST_Nothing)
    {
        //不显示各种编号
    }
    else if (showType == WDBMFGridUtils::FrmwShowType::FST_Key)
    {
        //显示编号
        headText = node.getAttribute("Description").toString();
    }
    else if (showType == WDBMFGridUtils::FrmwShowType::FST_Position)
    {
        //显示间隔距离        
        char textBuf[1024] = { 0 };
        sprintf_s(textBuf, 1024, "%.0lf", sctn.getOffsetToLast());
        headText = textBuf;
    }
    else
    {
        //显示高程和编号
        headText = sctn.getElevation() + "/" + node.getAttribute("Description").toString();
    }
}

void WDBMDSctnSCTN::mData(WDCore& core, WDBMDModelHelpter::MData& data)
{
    WDUnused(core);
    // 如果具有等级，则不生成虚线
    if (mSpref().refNode() != nullptr)
        return;

    auto lMat = node().localTransform();
    // spline
    WDLoftSPline spline;
    auto sPos = node().getAttribute("Posstart").toDVec3();
    sPos = lMat.inverse() * sPos;

    auto ePos = node().getAttribute("Posend").toDVec3();
    ePos = lMat.inverse() * ePos;

    if (DVec3::DistanceSq(sPos, ePos) >= NumLimits<float>::Epsilon)
    {        
        data.gLines.resize(3);
        auto& gLine     = data.gLines[0];
        gLine.points    = { FVec3(sPos) , FVec3(ePos) };
        gLine.color     = Color(255, 165, 0, 255);
        gLine.style     = WDGraphableLine::Style::DashLine;
        gLine.vMode     = WDGraphableLine::VMode::VM_Lines;
        //起点添加延长线
        WD::DVec3 dir = (ePos - sPos).normalize();
        double length = DVec3::Distance(sPos, ePos);
        {                        
            auto& sLine = data.gLines[1];
            sLine.points = { FVec3(sPos - dir * length / 10.0) , FVec3(sPos) };
            sLine.color = Color(255, 165, 0, 255);
            sLine.style = WDGraphableLine::Style::DashLine;
            sLine.vMode = WDGraphableLine::VMode::VM_Lines;
        }
        //终点添加延长线
        {            
            data.gLines.push_back(WDGraphableLine());
            auto& eLine = data.gLines[2];
            eLine.points = { FVec3(ePos), FVec3(ePos + dir * length / 10.0) };
            eLine.color = Color(255, 165, 0, 255);
            eLine.style = WDGraphableLine::Style::DashLine;
            eLine.vMode = WDGraphableLine::VMode::VM_Lines;
        }
        //使用Description生成文本
        std::string headText, tailText;
        GetText(*this, headText, tailText);
        if (headText != "")
        {            
            auto gType = node().getAttribute("Gtype").toWord();
            data.gTexts.push_back(WDGraphableText());
            auto& gText = data.gTexts.back();
            gText.position = data.gLines[1].points[0];
            gText.color = (gType == "XGRD" ? Color::Key::red : Color::Key::green);
            gText.text = stringToWString(headText);
            gText.fontSize = 16;
            gText.hAlign = WDGraphableText::HAlign::HA_Left;
            gText.vAlign = WDGraphableText::VAlign::VA_Bottom;
            gText.fixedPixel = true;
        }
        if (tailText != "")
        {
            auto gType = node().getAttribute("Gtype").toWord();
            data.gTexts.push_back(WDGraphableText());
            auto& gText = data.gTexts.back();
            gText.position = data.gLines[2].points[1];
            gText.color = (gType == "XGRD" ? Color::Key::green : Color::Key::red);
            gText.text = stringToWString(tailText);
            gText.fontSize = 16;
            gText.hAlign = WDGraphableText::HAlign::HA_Left;
            gText.vAlign = WDGraphableText::VAlign::VA_Bottom;
            gText.fixedPixel = true;
        }
    }

}
WDBMNodeRef WDBMDSctnSCTN::mSpref() const
{
    return node().getAttribute("Spref").toNodeRef();
}
void WDBMDSctnSCTN::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);

    auto lMat = node().localTransform();
    auto lRSMat = DMat4::ToMat3(lMat);
    // start drn
    auto sDrn = node().getAttribute("Drnstart").toDVec3();
    if (sDrn.lengthSq() >= NumLimits<float>::Epsilon)
    {
        sDrn = (lRSMat.inverse() * sDrn).normalized();
        V3EVNormalized(sDrn, true, 0.000001);
        gVars.setValue(WDBMCModelBuilder::GVarDrnS, sDrn);
    }
    // end drn
    auto eDrn = node().getAttribute("Drnend").toDVec3();
    if (eDrn.lengthSq() >= NumLimits<float>::Epsilon)
    {
        eDrn = (lRSMat.inverse() * eDrn).normalized();
        V3EVNormalized(eDrn, true, 0.000001);
        gVars.setValue(WDBMCModelBuilder::GVarDrnE, eDrn);
    }
    // 对齐线
    gVars.setValue(WDBMCModelBuilder::GVarJustLine, std::string(node().getAttribute("Jusline").toWord()));
    // spline
    WDLoftSPline spline;
    auto sPos = node().getAttribute("Posstart").toDVec3();
    sPos = lMat.inverse() * sPos;
    V3EVNormalized(sPos, false, 0.0001);
    spline.addPoint(WDLoftSPline::Point(sPos));

    auto ePos = node().getAttribute("Posend").toDVec3();
    ePos = lMat.inverse() * ePos;
    V3EVNormalized(ePos, false, 0.0001);
    spline.addPoint(WDLoftSPline::Point(ePos));
    gVars.setValue(WDBMCModelBuilder::GVarLoftSpline, spline);

    char key[128] = { 0 };
    int index = 1;
    auto desParams = node().getAttribute("Desparam").toStringVector();
    for (int i = 0; i < desParams.size(); ++i)
    {
        bool bOk = false;
        double val = FromString<double>(desParams[i], &bOk);
        if (bOk)
        {
            sprintf(key, "DESP %d", index++);
            gVars.setValue(key, val);
        }
    }
}
bool WDBMDSctnSCTN::mHasHoles() const
{
    for (auto& pChild : this->node().children())
    {
        if (pChild == nullptr)
            continue;
        // 检查子节点中是否带有孔洞
        if (WDBMDModelHelpter::HasNGeom(*pChild))
            return true;
        if (CheckNGeomCOFI(*pChild)     // 贯穿件
            || CheckNGeomFitt(*pChild)  // 附件
            || CheckNGeomTMPL(*pChild)) // 模板
            return true;
    }
    return false;
}
void WDBMDSctnSCTN::mNGeoms(WDGeometryBoolean::TsfmGeometries& outTsfmGeoms) const
{
    for (auto pChild : this->node().children())
    {
        if (pChild == nullptr)
            continue;
        // 检查子节点中是否带有孔洞
        if (WDBMDModelHelpter::HasNGeom(*pChild))
        {
            auto nGeoms = WDBMDModelHelpter::GetNGeoms(*pChild);
            if (!nGeoms.empty())
                outTsfmGeoms.push_back({ pChild->localTransform(), nGeoms });
        }
        // 贯穿件
        GetNGeomCOFI(*pChild, this->node().globalTransform(), outTsfmGeoms);
        // 附件
        GetNGeomFitt(*pChild, DMat4::Identity(), outTsfmGeoms);
        // 模板
        GetNGeomTMPL(*pChild, DMat4::Identity(), outTsfmGeoms);
    }
}

void    WDBMDSctnSCTN::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(bInsu);
    this->mGVars(core, outGVars);
}
void    WDBMDSctnSCTN::onModelUpdateBeforeChildren(WDCore& core, WDNode& node)
{
    WDUnused(node);
    // 更新模型
    _modelHelpter.updateModelBegin(core);
}
void    WDBMDSctnSCTN::onModelUpdate(WDCore& core, WDNode& node)
{
    WDUnused(node);
    _modelHelpter.updateModelEnd(core);
}


WD_NAMESPACE_END
