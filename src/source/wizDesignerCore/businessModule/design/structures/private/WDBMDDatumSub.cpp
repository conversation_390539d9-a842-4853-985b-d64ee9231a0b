#include "WDBMDDatumSub.h"
#include "WDCore.h"
#include "../../../../math/geometric/standardPrimitives/WDLoftSPline.h"
#include "../../../../geometry/WDGeometryMgr.h"

WD_NAMESPACE_BEGIN

WDBMDDatum::WDBMDDatum(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDDatum::~WDBMDDatum()
{

}

void    WDBMDDatum::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDDatum* pSrc = dynamic_cast<const WDBMDDatum*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const   WDSelectionInterface* WDBMDDatum::selectionSupporter() const
{
    return nullptr;
}
WDGraphableInterface*   WDBMDDatum::graphableSupporter()
{
    return &_modelHelpter;
}

void    WDBMDDatum::mData(WDCore& core, WDBMDModelHelpter::MData& data)
{
    WDUnused(core);
    auto lMat = node().localTransform();
    //获取基点
    auto& origin = WD::DVec3::Zero();
    //高度
    auto height = static_cast<float>(node().getAttribute("Height").toDouble());
    auto coneHeight = height / 5.0f;
    //xyz轴线
    data.gLines.resize(3);
    FVec3 endOffset[3] = { WD::FVec3::AxisX() * height, WD::FVec3::AxisY() * height, WD::FVec3::AxisZ() * height };
    for (int i = 0; i < 3; ++i)
    {
        auto& gLine = data.gLines[i];
        gLine.points = { FVec3(origin) , FVec3(origin) + endOffset[i]};
        gLine.color = Color(255, 165, 0, 255);
        gLine.style = WDGraphableLine::Style::SolidLine;
        gLine.vMode = WDGraphableLine::VMode::VM_Lines;
    }
    //圆锥
    if (height > NumLimits<float>::Epsilon)
    {
        WD::DVec3 position = origin + (height - coneHeight / 2.0) * WD::DVec3::AxisY();
        WD::DQuat rotation = Quat::FromVectors(WD::DVec3::AxisZ(), WD::DVec3::AxisY());
        WDGeometryStdPris::SharedPtr pCone = core.geometryMgr().createCone(position, rotation
            , 0.0f, coneHeight, coneHeight
            , WDGeometry::GFlag::GF_Entity);
        if (pCone != nullptr)
        {
            //add
            data.geoms.push_back(pCone);
        }
    }
}
WDBMNodeRef WDBMDDatum::mSpref() const
{
    return WDBMNodeRef();
}

void    WDBMDDatum::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);
    WDUnused(gVars);
}
bool WDBMDDatum::mHasHoles() const
{
    return false;
}

void WDBMDDatum::mNGeoms(WDGeometryBoolean::TsfmGeometries& outTsfmGeoms) const
{
    WDUnused(outTsfmGeoms);
}

void    WDBMDDatum::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(bInsu);
    this->mGVars(core, outGVars);
}
void    WDBMDDatum::onModelUpdateBeforeChildren(WDCore& core, WDNode& node)
{
    WDUnused(node);
    // 更新模型
    _modelHelpter.updateModel(core);
}
void    WDBMDDatum::onModelUpdate(WDCore& core, WDNode& node)
{
    WDUnused(core);
    WDUnused(node);
}
WD_NAMESPACE_END
