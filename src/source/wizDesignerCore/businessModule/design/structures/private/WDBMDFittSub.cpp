#include "WDBMDFittSub.h"

WD_NAMESPACE_BEGIN

WDBMDFittingBase::WDBMDFittingBase(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDFittingBase::~WDBMDFittingBase()
{
}
void WDBMDFittingBase::copy(const WDBDBase& src) 
{
    if (this == &src)
        return;
    const WDBMDFittingBase* pSrc = dynamic_cast<const WDBMDFittingBase*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDFittingBase::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDFittingBase::graphableSupporter()
{
    return &_modelHelpter;
}

WDBMNodeRef WDBMDFittingBase::mSpref() const
{
    return node().getAttribute("Spref").toNodeRef();
}
void  WDBMDFittingBase::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);
    auto& node = this->node();

    char buf[1024] = {};
    auto desparam = node.getAttribute("Desparam").toDoubleVector();
    for (size_t i = 0; i < desparam.size(); ++i)
    {
        sprintf_s(buf, sizeof(buf), "DESP %d", static_cast<int>(i + 1));
        gVars.setValue<double>(buf, desparam[i]);
    }
    //  复合附件会使用父节点的设计参数
    auto pParent = node.parent();
    if (pParent == nullptr)
        return;

    auto despVal = pParent->getAttribute("Desparam");
    switch (despVal.type())
    {
    case WDBMAttrValue::Type::T_DoubleVector:
        {
            const auto& pDesParams = despVal.data<std::vector<double>>();
            if (pDesParams != nullptr)
            {
                for (size_t i = 0; i < pDesParams->size(); ++i)
                {
                    sprintf_s(buf, sizeof(buf), "ODESP %d", static_cast<int>(i + 1));
                    gVars.setValue<double>(buf, pDesParams->at(i));
                }
            }
        }
        break;
    case WDBMAttrValue::Type::T_StringVector:
        {
            const auto& pDesParams = despVal.data<StringVector>();
            if (pDesParams != nullptr)
            {
                int index = 1;
                for (size_t i = 0; i < pDesParams->size(); ++i)
                {
                    bool bOk = false;
                    double val = FromString<double>(pDesParams->at(i), &bOk);
                    if (bOk)
                    {
                        sprintf_s(buf, sizeof(buf), "ODESP %d", index++);
                        gVars.setValue(buf, val);
                    }
                }
            }
        }
        break;
    default:
        {
            assert(false);
        }
        break;
    }
    // 获取父节点的等级引用
    auto spcoVal = pParent->getAttribute("Spref");
    auto pSpcoRef = spcoVal.data<WDBMNodeRef>();
    if (pSpcoRef != nullptr)
    {
        auto pSpcoNode = pSpcoRef->refNode();
        if (pSpcoNode != nullptr)
        {
            // 获取父节点的元件引用
            auto catRefVal = pSpcoNode->getAttribute("Catref");
            auto pCatRef = catRefVal.data<WDBMNodeRef>();
            if (pCatRef != nullptr)
            {
                auto pCatNode = pCatRef->refNode();
                // 如果未获取到，有可能是因为父节点直接引用了元件,跳过了等级
                if (pCatNode == nullptr)
                    pCatNode = pSpcoNode;

                if (pCatNode != nullptr)
                {
                    auto paramVal = pCatNode->getAttribute("Param");
                    auto pParam = paramVal.data<StringVector>();
                    if (pParam != nullptr)
                    {
                        int index = 1;
                        for (size_t i = 0; i < pParam->size(); ++i)
                        {
                            bool bOk = false;
                            double val = FromString<double>(pParam->at(i), &bOk);
                            if (bOk)
                            {
                                sprintf_s(buf, sizeof(buf), "OPARAM %d", index++);
                                gVars.setValue(buf, val);
                            }
                        }
                    }
                    else
                    {
                        assert(false);
                    }
                }
                else
                {
                    assert(false);
                }
            }
        }
    }

}

void WDBMDFittingBase::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(bInsu);
    this->mGVars(core, outGVars);
}
void WDBMDFittingBase::onModelUpdate(WDCore& core, WDNode&)
{
    _modelHelpter.updateModel(core);
}

WDBMDpaneFittingBase::WDBMDpaneFittingBase(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDpaneFittingBase::~WDBMDpaneFittingBase()
{
}
void WDBMDpaneFittingBase::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDpaneFittingBase* pSrc = dynamic_cast<const WDBMDpaneFittingBase*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDpaneFittingBase::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDpaneFittingBase::graphableSupporter()
{
    return &_modelHelpter;
}

WDBMNodeRef WDBMDpaneFittingBase::mSpref() const
{
    return node().getAttribute("Spref").toNodeRef();
}
void  WDBMDpaneFittingBase::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);
    auto& node = this->node();
    char buf[1024] = {};
    auto desparam = node.getAttribute("Desparam").toDoubleVector();
    for (size_t i = 0; i < desparam.size(); ++i)
    {
        sprintf_s(buf, sizeof(buf), "DESP %d", static_cast<int>(i + 1));
        gVars.setValue<double>(buf, desparam[i]);
    }

    //  附件会使用父节点的设计参数
    auto pParent = node.parent();
    if (pParent == nullptr)
        return;

    auto despVal = pParent->getAttribute("Desparam");
    if (despVal.valid())
    {
        auto pDesp = despVal.data<std::vector<double>>();
        if (pDesp != nullptr)
        {
            for (size_t i = 0; i < pDesp->size(); ++i)
            {
                sprintf_s(buf, sizeof(buf), "ODESP %d", static_cast<int>(i + 1));
                gVars.setValue<double>(buf, pDesp->at(i));
            }
        }
    }

    //拉伸体环节点
    //  在子节点中找到PLOO节点 (子节点中可能会有PFIT节点)
    for (auto& pPLOONode : pParent->children())
    {
        if (pPLOONode == nullptr)
            continue;
        if (!pPLOONode->isType("PLOO"))
            continue;
        // 获取父节点的高度
        const double height = pPLOONode->getAttribute("Height").toDouble();
        gVars.setValue<double>("LOHE", height);
        gVars.setValue<double>("ATTRIB LOHE", height);
        break;
    }
}

void WDBMDpaneFittingBase::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(bInsu);
    this->mGVars(core, outGVars);
}
void WDBMDpaneFittingBase::onModelUpdate(WDCore& core, WDNode&)
{
    _modelHelpter.updateModel(core);
}

WDBMDFittSBFI::WDBMDFittSBFI(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDFittSBFI::~WDBMDFittSBFI()
{
}
void WDBMDFittSBFI::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDFittSBFI* pSrc = dynamic_cast<const WDBMDFittSBFI*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDFittSBFI::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDFittSBFI::graphableSupporter()
{
    return &_modelHelpter;
}

WDBMNodeRef WDBMDFittSBFI::mSpref() const
{
    return node().getAttribute("Spref").toNodeRef();
}
void WDBMDFittSBFI::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);
    auto& node = this->node();
    char buf[1024] = {};
    auto desparam = node.getAttribute("Desparam").toDoubleVector();
    for (size_t i = 0; i < desparam.size(); ++i)
    {
        sprintf_s(buf, sizeof(buf), "DESP %d", static_cast<int>(i + 1));
        gVars.setValue<double>(buf, desparam[i]);
    }
    //  子附件会使用父节点的设计参数
    auto pParent = node.parent();
    if (pParent == nullptr)
        return;

    const auto despVal = pParent->getAttribute("Desparam");
    if (despVal.valid())
    {
        auto pDesp = despVal.data<std::vector<double>>();
        assert(pDesp != nullptr);
        if (pDesp != nullptr)
        {
            for (size_t i = 0; i < pDesp->size(); ++i)
            {
                sprintf_s(buf, sizeof(buf), "ODESP %d", static_cast<int>(i + 1));
                gVars.setValue<double>(buf, pDesp->at(i));
            }
        }
    }
    if (pParent->isType("CMPF"))
    {
        while (pParent != nullptr)
        {
            if (pParent->isAnyOfType("STRU", "TMPL"))
                break;
            if (pParent->isAnyOfType("PANE", "FLOOR", "GWALL", "SCREED"))
            {
                for (auto& pChild : pParent->children())
                {
                    if (pChild->isType("PLOO"))
                    {
                        auto heightVal = pChild->getAttribute("Height");
                        if (heightVal.valid())
                        {
                            auto pHeight = heightVal.data<double>();
                            if (pHeight != nullptr)
                            {
                                gVars.setValue<double>("ATTRIB LOHE", *pHeight);
                                gVars.setValue<double>("LOHE", *pHeight);
                                break;
                            }
                        }
                    }
                }
                break;
            }
            pParent = pParent->parent();
        }
    }
    
}

void WDBMDFittSBFI::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(bInsu);
    this->mGVars(core, outGVars);
}
void WDBMDFittSBFI::onModelUpdate(WDCore& core, WDNode& node)
{
    WDUnused(node);
    _modelHelpter.updateModel(core);
}

WDBMDFittFIXING::WDBMDFittFIXING(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDFittFIXING::~WDBMDFittFIXING()
{
}
void WDBMDFittFIXING::copy(const WDBDBase& src) 
{
    if (this == &src)
        return;

    const WDBMDFittFIXING* pSrc = dynamic_cast<const WDBMDFittFIXING*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDFittFIXING::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDFittFIXING::graphableSupporter()
{
    return &_modelHelpter;
}

WDBMNodeRef WDBMDFittFIXING::mSpref() const
{
    return node().getAttribute("Spref").toNodeRef();
}
void  WDBMDFittFIXING::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);
    char buf[1024] = {};
    auto desparam = node().getAttribute("Desparam").toDoubleVector();
    for (size_t i = 0; i < desparam.size(); ++i)
    {
        sprintf_s(buf, sizeof(buf), "DESP %d", static_cast<int>(i + 1));
        gVars.setValue<double>(buf, desparam[i]);
    }
}

void WDBMDFittFIXING::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    WDUnused(bInsu);
    this->mGVars(core, outGVars);
}
void WDBMDFittFIXING::onModelUpdate(WDCore& core, WDNode& node)
{
    WDUnused(node);
    _modelHelpter.updateModel(core);
}

WD_NAMESPACE_END
