#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"

WD_NAMESPACE_BEGIN

/**
* @brief 附件基类
*/
class WD_API WDBMDFittingBase : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter _modelHelpter;
public:
    WDBMDFittingBase(WDNode& node);
    virtual ~WDBMDFittingBase();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDFittingBase(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

/**
* @brief 板附件基类
* Panel Fitting可用于表示附着在面板上的任何项目
*/
class WD_API WDBMDpaneFittingBase: public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter _modelHelpter;
public:
    WDBMDpaneFittingBase(WDNode& node);
    virtual ~WDBMDpaneFittingBase();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDpaneFittingBase(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

/**
* @brief 子附件
*/
class WD_API WDBMDFittSBFI : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter _modelHelpter;
public:
    WDBMDFittSBFI(WDNode& node);
    virtual ~WDBMDFittSBFI();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDFittSBFI(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;

};

/**
* @brief 固定件
*/
class WD_API WDBMDFittFIXING : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter _modelHelpter;
public:
    WDBMDFittFIXING(WDNode& node);
    virtual ~WDBMDFittFIXING();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDFittFIXING(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

WD_NAMESPACE_END

