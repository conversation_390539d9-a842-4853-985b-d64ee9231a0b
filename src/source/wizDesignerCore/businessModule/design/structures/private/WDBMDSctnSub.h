#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"
#include "../WDBMFGridUtils.h"
WD_NAMESPACE_BEGIN
/**
* @brief 通用的截面类型节点，SCTN 是 GENSEC的特殊件
SCTN 是专指那些工字型的，GENSEC 就包含各种外型的截面体
*/
class WD_API WDBMDStruGENSEC : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter _modelHelpter;
public:
    WDBMDStruGENSEC(WDNode& node);
    virtual ~WDBMDStruGENSEC();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDStruGENSEC(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual void mData(WDCore& core, WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
    virtual bool mHasHoles() const override;
    virtual void mNGeoms(WDGeometryBoolean::TsfmGeometries& outTsfmGeoms) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

/**
* @brief 截面
*/
class WD_API WDBMDSctnSCTN : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter _modelHelpter;
    WD::WDBMFGridUtils::FrmwShowType _showType;//显示类型
    double _offsetToLast;//与上一个之间的偏移
    std::string _elevation;
public:
    WDBMDSctnSCTN(WDNode& node);
    virtual ~WDBMDSctnSCTN();
public:
    inline const WD::WDBMFGridUtils::FrmwShowType getShowType() const 
    {
        return _showType;
    }

    inline void setShowType(const WDBMFGridUtils::FrmwShowType& showType)
    {
        _showType = showType;
    }
    inline double getOffsetToLast() const
    {
        return _offsetToLast;
    }
    inline void setOffsetToLast(const double& offset)
    {
       _offsetToLast = offset;
    }
    inline std::string getElevation() const
    {
        return _elevation;
    }
    inline void setElevation(const std::string& elevation)
    {
        _elevation = elevation;
    }
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDSctnSCTN(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual void mData(WDCore& core, WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
    virtual bool mHasHoles() const override;
    virtual void mNGeoms(WDGeometryBoolean::TsfmGeometries& outTsfmGeoms) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdateBeforeChildren(WDCore& core, WDNode& node) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

WD_NAMESPACE_END

