#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"
WD_NAMESPACE_BEGIN

/**
* @brief DATUM与结构轴网关联，用于表达结构轴网的XYZ方向
*/
class WD_API WDBMDDatum : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter   _modelHelpter;
public:
    WDBMDDatum(WDNode& node);
    virtual ~WDBMDDatum();
public:
    virtual void copy(const WDBDBase& src) override;
    virtual inline WDBDBase* clone(WDNode& node) const override
    {
        auto p = new WDBMDDatum(node);
        p->copy(*this);
        return p;
    }
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual void mData(WDCore& core, WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
    virtual bool mHasHoles() const override;
    virtual void mNGeoms(WDGeometryBoolean::TsfmGeometries& outTsfmGeoms) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdateBeforeChildren(WDCore& core, WDNode& node) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};

WD_NAMESPACE_END

