#include "WDBMDPipeUtils.h"
#include "private/WDBMDPipeBRAN.h"
#include "private/WDBMDPipeTUBI.h"
#include "private/WDBMDPipeCOMS.h"
#include "../../../WDCore.h"
#include "../../catalog/WDBMCatalog.h"
#include "../../../common/WDDeviation.h"


WD_NAMESPACE_BEGIN;

/**
* @brief 获取引用的元件节点
* @param nodeRef 节点引用对象
* @return   如果该对象是等级节点引用，则获取等级引用的元件节点;
*           如果该对象是元件节点引用(SCOM)，则直接返回其引用的元件节点(SCOM), 否则返回nullptr
*/
WDNode::SharedPtr GetRefSCOMNode(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return nullptr;

    if (pNode->isType("SPCO"))
        return pNode->getAttribute("Catref").toNodeRef().refNode();
    else if (pNode->isAnyOfType("SCOM", "SPRF", "JOIN", "SFIT"))
        return pNode;
    else
        return nullptr;
};

// 根据保温等级节点，获取保温值
static std::optional<std::vector<double>> GetInsulationFromSPCO(WDNode::SharedPtr pSPCO)
{
    if (pSPCO == nullptr || !pSPCO->isType("SPCO"))
        return std::nullopt;
    // 获取SPCO引用元件
    auto pSCOM = pSPCO->getAttribute("Catref").toNodeRef().refNode();
    if (pSCOM == nullptr)
        return std::nullopt;
    // 获取保温元件的保温参数，目前暂定是第一个参数(保温元件共有两个参数：第一个参数是直径，第二个参数是半径)
    const auto params = pSCOM->getAttribute("Param").toStringVector();
    assert(!params.empty());
    if (params.empty())
        return std::nullopt;
    std::vector<double> value;
    bool bOk = false;
    double rValue1 = FromString<double>(params[0], &bOk);
    if (!bOk)
        return std::nullopt;
    value.emplace_back(rValue1);
    if (params.size() >= 2)
    {
        double rValue2 = FromString<double>(params[1], &bOk);
        if (bOk)
            value.emplace_back(rValue2);
    }
    return value;
}

bool WDBMDPipeUtils::CheckAnswer(const WDNode& node, int value, IntervalType iType)
{
    bool bOkA = false;
    bool bOkB = false;
    auto answerStr      = node.getAttribute("Answer").toString(&bOkA);
    auto maxAnswerStr   = node.getAttribute("Maxanswer").toString(&bOkB);
    int a = 0;
    int b = 0;
    if (bOkA)
        a = FromString<int>(answerStr, &bOkA);
    if (bOkB)
        b = FromString<int>(maxAnswerStr, &bOkB);

    // 如果Maxanswer有效，且Answer为空，则answer默认设置为0
    if (bOkB && answerStr.empty())
    {
        a = 0;
        bOkA = true;
    }

    if (bOkA && bOkB)
    {
        switch (iType)
        {
            // 左闭右闭
        case IT_LCRC:
            if (value >= a && value <= b)
                return true;
            break;
            // 左开右闭
        case IT_LORC:
            if (value > a && value <= b)
                return true;
            break;
            // 左闭右开
        case IT_LCRO:
            if (value >= a && value < b)
                return true;
            break;
            // 左开右开
        case IT_LORO:
            if (value > a && value < b)
                return true;
            break;
        default:
            break;
        }
    }
    else if (bOkA && (value == a))
    {
        return true;
    }
    else if (bOkB && (value == b))
    {
        return true;
    }

    return false;
}
bool WDBMDPipeUtils::CheckAnswer(const WDNode& node, double value, IntervalType iType)
{
    bool bOkA = false;
    bool bOkB = false;
    auto answerStr      = node.getAttribute("Answer").toString(&bOkA);
    auto maxAnswerStr   = node.getAttribute("Maxanswer").toString(&bOkB);
    double a = 0.0;
    double b = 0.0;

    if (bOkA)
        a = FromString<double>(answerStr, &bOkA);
    if (bOkB)
        b = FromString<double>(maxAnswerStr, &bOkB);

    // 如果Maxanswer有效，且Answer为空，则answer默认设置为0
    if (bOkB && answerStr.empty())
    {
        a = 0.0;
        bOkA = true;
    }

    if (bOkA && bOkB)
    {
        switch (iType)
        {
            // 左闭右闭
        case IT_LCRC:
            if (value >= a && value <= b)
                return true;
            break;
            // 左开右闭
        case IT_LORC:
            if (value > a && value <= b)
                return true;
            break;
            // 左闭右开
        case IT_LCRO:
            if (value >= a && value < b)
                return true;
            break;
            // 左开右开
        case IT_LORO:
            if (value > a && value < b)
                return true;
            break;
        default:
            break;
        }
    }
    else if (bOkA && (value == a))
    {
        return true;
    }
    else if (bOkB && (value == b))
    {
        // answer处理失败了，但是maxAnswer有值，该如何处理？
        assert(false);
    }

    return false;
}
bool WDBMDPipeUtils::CheckAnswer(const WDNode& node, const std::string& value)
{
    std::string tAnsStr;
    if (!node.getAttribute("Answer").toString(tAnsStr))
        return false;
    return value == tAnsStr;
}
bool WDBMDPipeUtils::CheckTAnswer(const WDNode& node, const std::string& value)
{
    std::string tAnsStr;
    if (!node.getAttribute("Tanswer").toString(tAnsStr))
        return false;
    return value == tAnsStr;
}

bool WDBMDPipeUtils::IsPipeComponent(const WD::WDNode& node)
{
    return node.isAnyOfType("ELBO", "BEND", "TEE", "VALV", "REDU", "FLAN", "CROS", "GASK"
        , "DUCT", "VENT", "FTUB", "SHU", "COUP", "CLOS", "OLET", "LJSE", "CAP", "FBLI", "VTWA"
        , "VFWA", "TRAP", "FILT", "WELD", "PCOM", "UNIO", "INST", "ATTA", "TAPE", "TRNS", "STRT"
        , "OFST", "PLEN", "PLAT", "THRE", "BRCO", "MESH", "GRIL", "COWL", "DAMP", "HFAN", "SILE"
        , "BATT", "AHU", "STIF", "HACC", "HSAD", "IDAM", "TP", "SPLR", "SKIR", "FLEX", "PTAP");
}
int WDBMDPipeUtils::Fork(const WD::WDNode& node)
{
    bool bOk = false;
    int aN = node.getAttribute("Arrive").toInt(&bOk);
    if (!bOk)
        return 3;
    int lN = node.getAttribute("Leave").toInt(&bOk);
    if (!bOk)
        return 3;

#define IS_TWO_NUMBERS(a, b, numA, numB) ((a == numA && b == numB) || (a == numB && b == numA))

    if (IS_TWO_NUMBERS(aN, lN, 1, 2))
        return 3;
    else if (IS_TWO_NUMBERS(aN, lN, 1, 3))
        return 2;
    else if (IS_TWO_NUMBERS(aN, lN, 2, 3))
        return 1;
    else
        return 3;

#undef IS_TWO_NUMBERS
}

WDNode::SharedPtr WDBMDPipeUtils::GetStubeBySPEC(WDNode::SharedPtr pSPECNode, const std::string& bore)
{
    if (pSPECNode == nullptr)
        return nullptr;

    if (bore.empty())
        return nullptr;

    // 转换公称直径
    bool bOk = false;
    int iBore = FromString<int>(bore, &bOk);
    if (!bOk)
    {
        assert(false && "注意: 公称直径转换为int时失败!");
        return nullptr;
    }
    WDNode::SharedPtr pRetNode = nullptr;
    for (auto pSELETypeNode : pSPECNode->children())
    {
        if (pSELETypeNode == nullptr || !pSELETypeNode->isType("SELE"))
            continue;
        if(!pSELETypeNode->isType("SELE"))
            continue;
        // 直管类型答案值
        if (!CheckTAnswer(*pSELETypeNode, "TUBE"))
            continue;
        // 再查找满足公称直径的SELE节点
        for (auto pSELEBoreNode : pSELETypeNode->children()) 
        {
            if (CheckAnswer(*pSELEBoreNode, iBore))
            {
                pRetNode = pSELEBoreNode;
                break;
            }
        }
        // 找到了该节点，跳出
        if (pRetNode != nullptr)
            break;
    }
    if(pRetNode == nullptr)
        return nullptr;
    // 拿到最终的叶子节点，也就是SPCO类型节点
    while (pRetNode->childCount() > 0)
    {
        pRetNode = pRetNode->children().front();
        assert(pRetNode != nullptr);
    }
    //校验是否是SPCO节点
    if (!pRetNode->isType("SPCO"))
        return nullptr;
    return pRetNode;
}

std::optional<std::vector<double>> WDBMDPipeUtils::GetPipeComponentInsulation(WDCore& core, const WDNode& pipeComNode)
{
    WDUnused(core);
    // 管件的等级节点
    auto spref = pipeComNode.getAttribute("Spref").toNodeRef();
    if (!spref.valid())
        return std::nullopt;
    // 获取保温等级SPEC节点
    auto pISpecNode = pipeComNode.getAttribute("Ispec").toNodeRef().refNode();
    if (pISpecNode == nullptr)
        return std::nullopt;
    // 校验父节点有效且父节点是分支节点
    if (pipeComNode.parent() == nullptr || !pipeComNode.parent()->isType("BRAN"))
        return std::nullopt;
    // 获取管件公称直径
    int bore = 0;
    auto pSCOMNode = GetRefSCOMNode(spref.refNode());
    if (pSCOMNode != nullptr)
    {
        // 管件节点的公称直径从元件的第一个参数参数中获取
        const StringVector params = pSCOMNode->getAttribute("Param").toStringVector();
        if (!params.empty())
            bore = FromString<int>(params.at(0));
    }
    if (bore == 0)
        return std::nullopt;
    // 获取所属父分支节点温度
    double temperature = pipeComNode.parent()->getAttribute("Temperature").toDouble();
    // 根据公称直径和温度，选择保温 SPCE节点下，符合条件的SPCO节点
    // 目前按照固定的逻辑查找保温: 
    // ->SPEC                                    问题(Quesiont):TYPE
    // (->SELE  回答(Tanswer):INSU     问题(Quesiont):TEMP) or (->SELE  回答(Answer,Maxanswer):值1~值2    问题(Quesiont):PBOR)
    // (->SELE  回答(Answer,Maxanswer):值1~值2    问题(Quesiont):PBOR) or (->SELE  回答(Tanswer):INSU     问题(Quesiont):TEMP)
    // ->SPCO  回答(Answer,Maxanswer):值1~值2
    WDNode::SharedPtr pISpcoNode = nullptr;
    for (auto pSELENode: pISpecNode->children())
    {
        if (pSELENode == nullptr || !pSELENode->isType("SELE"))
            continue;
        if(!pSELENode->isType("SELE"))
            continue;
        if (!CheckTAnswer(*pSELENode, "INSU"))
            continue;
        pISpcoNode = pSELENode;
        while (pISpcoNode != nullptr)
        {
            auto question = pISpcoNode->getAttribute("Question").toString();
            // 校验温度
            if (question == "TEMP") 
            {
                bool bFind = false;
                for (auto pChild : pISpcoNode->children()) 
                {
                    if (pChild == nullptr)
                        continue;
                    if (CheckAnswer(*pChild, temperature, IT_LORC))
                    {
                        bFind = true;
                        pISpcoNode = pChild;
                        break;
                    }
                }
                if (!bFind)
                {
                    // 温度校验没有找到合适的SELE
                    pISpcoNode = nullptr;
                    break;
                }

            }
            // 校验管径
            else if(question == "PBOR") 
            {
                bool bFind = false;
                for (auto pChild : pISpcoNode->children())
                {
                    if (pChild == nullptr)
                        continue;
                    if (CheckAnswer(*pChild, bore))
                    {
                        bFind = true;
                        pISpcoNode = pChild;
                        break;
                    }
                }
                if (!bFind)
                {
                    // 管径校验没有找到合适的SELE
                    pISpcoNode = nullptr;
                    break;
                }
            }
            else
                break;
        }
        // 找到了对应的等级节点
        if (pISpcoNode != nullptr)
            break;
    }

    while (pISpcoNode != nullptr && !pISpcoNode->isType("SPCO"))
    {
        const auto& children = pISpcoNode->children();
        if (children.empty())
            break;
        pISpcoNode = children.front();
    }

    // 根据保温等级节点获取保温参数
    return GetInsulationFromSPCO(pISpcoNode);
}

std::string WDBMDPipeUtils::GetPipeComponentDescriptionBySPCO(WDCore& core
    , WDNode::SharedPtr pSPCONode)
{
    // 获取管件描述（spco->detref->Rtext属性值和spco->Matxt->Xtext属性值）
    if (pSPCONode == nullptr)
        return "";

    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet(nullptr, pSPCONode);

    std::string rText;
    // 文档节点 
    auto pDetRefNode = pSPCONode->getAttribute("Detref").toNodeRef().refNode();
    if (pDetRefNode != nullptr)
        rText = aGet.getAttribute(*pDetRefNode, "Rtext").convertToString();

    std::string xText;
    // 材料文本节点
    auto pMatxtRefNode = pSPCONode->getAttribute("Matxt").toNodeRef().refNode();
    if (pMatxtRefNode != nullptr)
        xText = aGet.getAttribute(*pMatxtRefNode, "Xtext").convertToString();

    if (!rText.empty() && !xText.empty())
        return rText + " " + xText;

    return rText + xText;
}

bool WDBMDPipeUtils::IsBendComponent(WD::WDNode& node)
{
    auto pArrive = node.keyPoint(node.getAttribute("Arrive").toInt());
    auto pLeave = node.keyPoint(node.getAttribute("Leave").toInt());
    if (pArrive == nullptr || pLeave == nullptr)
        return false;

    return !DVec3::OnTheSameLine(pArrive->direction, pLeave->direction);
}
void WDBMDPipeUtils::UpdateDefaultRadiusToDDRADIUS(WD::WDNode& node)
{
    if (!WD::WDBMDPipeUtils::IsPipeComponent(node))
        return;
    // DDRADIUS默认值为第一个参数公称直径的两倍（从PDMS的数据找规律找出来的。。。)
    auto pNode = node.getAttribute("Spref").toNodeRef().refNode();
    if (pNode == nullptr)
        return;
    // 获取等级引用的元件节点
    auto pScom = pNode->getAttribute("Catref").toNodeRef().refNode();
    if (pScom == nullptr)
        return;
    // 获取元件节点的参数列表
    auto paramValues = pScom->getAttribute("Param").toStringVector();
    if (paramValues.empty())
        return;
    bool bOk = false;
    auto tValue = 2.0 * WD::FromString<double>(paramValues.at(0), &bOk);
    if (bOk)
        node.setAttribute("Radius", tValue);
}


bool WDBMDPipeUtils::UpdateConnectionCt(WDCore& core, WDNode::SharedPtr pCtBran)
{
    return WDBMDPipeBRAN::UpdateConnectionCt(core, pCtBran);
}
std::vector<WDNode::SharedPtr> WDBMDPipeUtils::Tubings(WDNode::SharedPtr pBranch)
{
    if (pBranch == nullptr)
        return std::vector<WDNode::SharedPtr>();

    std::vector<WDNode::SharedPtr>  rNodes;
    for (size_t i = 0; i < pBranch->childCount(); ++i)
    {
        WDNode::SharedPtr pChild = pBranch->childAt(i);
        if (pChild == nullptr)
            continue;
        if (!pChild->isType("TUBI"))
            continue;
        rNodes.push_back(pChild);
    }
    return rNodes;
}
std::vector<WDNode::SharedPtr> WDBMDPipeUtils::PipeComponents(WDNode::SharedPtr pBranch
    , const std::vector<std::string>& comTypeFilter)
{
    if (pBranch == nullptr)
        return std::vector<WDNode::SharedPtr>();

    // 管件类型过滤，如果被过滤掉，返回true，否则返回false
    auto funcFilter = [&comTypeFilter](WDNode& comNode) -> bool
        {
            std::string tType = std::string(comNode.type());
            auto fItr = std::find(comTypeFilter.begin(), comTypeFilter.end(), tType);
            return fItr != comTypeFilter.end();
        };

    bool bFilter = !comTypeFilter.empty();
    std::vector<WDNode::SharedPtr>  rNodes;
    for (size_t i = 0; i < pBranch->childCount(); ++i)
    {
        WDNode::SharedPtr pChild = pBranch->childAt(i);
        if (pChild == nullptr)
            continue;
        if (!WDBMDPipeUtils::IsPipeComponent(*pChild))
            continue;
        // 检查该管件的类型是否要被过滤
        if (bFilter && funcFilter(*pChild))
            continue;

        rNodes.push_back(pChild);
    }
    return rNodes;
}
WDNode::SharedPtr WDBMDPipeUtils::PrevPipeComponent(WDNode::SharedPtr pBranch
    , WDNode::SharedPtr pBranchChild
    , const std::vector<std::string>& comTypeFilter)
{
    if (pBranch == nullptr
        || pBranchChild == nullptr
        || pBranchChild->parent() != pBranch)
        return nullptr;

    int childCnt = static_cast<int> (pBranch->childCount());
    int lastIndex = -1;
    for (int i = 0; i < childCnt; ++i)
    {
        WDNode::SharedPtr pChild = pBranch->childAt(i);
        if (pChild == pBranchChild)
        {
            lastIndex = i - 1;
            break;
        }
    }

    if (lastIndex < 0 || lastIndex >= childCnt)
        return nullptr;

    for (int i = lastIndex; i >= 0; --i)
    {
        WDNode::SharedPtr pChild = pBranch->childAt(i);
        if (!WDBMDPipeUtils::IsPipeComponent(*pChild)
            || std::find(comTypeFilter.begin(), comTypeFilter.end(), pChild->type()) != comTypeFilter.end())
            continue;

        return pChild;
    }

    return nullptr;
}
WDNode::SharedPtr WDBMDPipeUtils::NextPipeComponent(WDNode::SharedPtr pBranch
    , WDNode::SharedPtr pBranchChild
    , const std::vector<std::string>& comTypeFilter)
{
    if (pBranch == nullptr
        || pBranchChild == nullptr
        || pBranchChild->parent() != pBranch)
        return nullptr;

    int childCnt = static_cast<int> (pBranch->childCount());
    int firstIndex = -1;
    for (int i = 0; i < childCnt; ++i)
    {
        WDNode::SharedPtr pChild = pBranch->childAt(i);
        if (pChild == pBranchChild)
        {
            firstIndex = i + 1;
            break;
        }
    }

    if (firstIndex < 0 || firstIndex >= childCnt)
        return nullptr;

    for (int i = firstIndex; i < childCnt; ++i)
    {
        WDNode::SharedPtr pChild = pBranch->childAt(i);
        if (!WDBMDPipeUtils::IsPipeComponent(*pChild)
            || std::find(comTypeFilter.begin(), comTypeFilter.end(), pChild->type()) != comTypeFilter.end())
            continue;
        return pChild;
    }

    return nullptr;
}
WDNode::SharedPtr WDBMDPipeUtils::AdjacentPipeComponent(WDNode::SharedPtr pBranch
    , WDNode::SharedPtr pBranchChild
    , const std::vector<std::string>& comTypeFilter)
{

    if (pBranch == nullptr
        || pBranchChild == nullptr
        || pBranchChild->parent() != pBranch)
        return nullptr;

    int childCnt = static_cast<int> (pBranch->childCount());
    int nextFirstIndex = -1;
    int prevLastIndex = -1;
    for (int i = 0; i < childCnt; ++i)
    {
        WDNode::SharedPtr pChild = pBranch->childAt(i);
        if (pChild == pBranchChild)
        {
            nextFirstIndex = i + 1;
            prevLastIndex = i - 1;
            break;
        }
    }
    for (int step = 0; step < childCnt; ++step)
    {
        int tPLIndex = prevLastIndex - step;
        if (tPLIndex >= 0 && tPLIndex < childCnt)
        {
            WDNode::SharedPtr pChild = pBranch->childAt(tPLIndex);
            if (WDBMDPipeUtils::IsPipeComponent(*pChild))
            {
                return pChild;
            }
        }

        int tNFIndex = nextFirstIndex + step;
        if (tNFIndex >= 0 && tNFIndex < childCnt)
        {
            WDNode::SharedPtr pChild = pBranch->childAt(tNFIndex);
            if (WDBMDPipeUtils::IsPipeComponent(*pChild)
                && std::find(comTypeFilter.begin(), comTypeFilter.end(), pChild->type()) == comTypeFilter.end())
            {
                return pChild;
            }
        }
    }

    return nullptr;
}

void WDBMDPipeUtils::MoveComponents(WDNode& branNode
    , WD::WDNode::SharedPtr pStartNode
    , WD::WDNode::SharedPtr pEndNode
    , WD::WDNode::SharedPtr pPrevNode)
{
    if (pStartNode == nullptr || pEndNode == nullptr || pPrevNode == nullptr)
        return;

    bool bNeedUpdate = false;
    // 重新填充子节点
    branNode.reorder([pStartNode, pEndNode, pPrevNode, &bNeedUpdate](const WD::WDNode::Nodes& children, WDNode& parent) -> WDNode::Nodes
    {
        WDUnused(parent);
        auto sItr = std::find(children.begin(), children.end(), pStartNode);
        if (sItr == children.end())
            return children;
        auto eItr = std::find(children.begin(), children.end(), pEndNode);
        if (eItr == children.end())
            return children;
        // 起点不能在终点后面,数据错误,不做重排序
        if (sItr > eItr)
            return children;
        if (pPrevNode.get() != &parent)
        {
            auto tItr = std::find(children.begin(), children.end(), pPrevNode);
            if (tItr == children.end())
                return children;
            // 不需要重新排序
            if (sItr <= tItr && tItr <= eItr)
                return children;
        }

        bNeedUpdate = true;
        WDNode::Nodes nodes;

        nodes.insert(nodes.end(), children.begin(), sItr);
        nodes.insert(nodes.end(), eItr + 1, children.end());

        WD::WDNode::NodesItr tItr;
        if (pPrevNode.get() != &parent)
        {
            tItr = std::find(nodes.begin(), nodes.end(), pPrevNode);
            if (tItr == nodes.end())
                return children;
            if (*tItr == nodes.back())
                tItr = nodes.end();
            else
                ++tItr;
        }
        else
        {
            tItr = nodes.begin();
        }

        nodes.insert(tItr, sItr, eItr + 1);


        return nodes;
    });

    if (bNeedUpdate)
        branNode.triggerUpdate(true);
}

void WDBMDPipeUtils::SetPipelineDirectionDisplayEnabled(WDNode& branNode, bool enabled)
{
    auto pData = dynamic_cast<WD::WDBMDPipeBRAN*>(branNode.getBDBase());
    if (pData == nullptr)
        return;
    auto flags = pData->flags();
    flags.setFlag(WDBMDPipeBRAN::Flag::Flag_PipelineDirectionDisplay, enabled);
    pData->setFlags(flags);
}
bool WDBMDPipeUtils::GetPipelineDirectionDisplayEnabled(const WDNode& branNode)
{
    auto pData = dynamic_cast<WD::WDBMDPipeBRAN*>(branNode.getBDBase());
    if (pData == nullptr)
        return false;
    return pData->flags().hasFlag(WDBMDPipeBRAN::Flag::Flag_PipelineDirectionDisplay);
}

static WDNode::Nodes GetCocos(WDCore& core)
{
    WDNode::Nodes   cocos;
    auto            pRoot   =   core.getBMCatalog().root();
    if (pRoot == nullptr)
        return cocos;
    // 获取根节点下所有CCTA节点
    WDNode::Nodes pCctas;
    for (const auto& pChild : pRoot->children())
    {
        if (pChild == nullptr)
            continue;
        if (!pChild->isType("CCTA"))
            continue;
        pCctas.push_back(pChild);
    }
    for (const auto& pCcta : pCctas)
    {
        if (pCcta == nullptr)
            continue;
        // 遍历获取所有coco节点
        for (const auto& pChild : pCcta->children())
        {
            if (pChild == nullptr)
                continue;
            if (!pChild->isType("COCO"))
                continue;
            cocos.push_back(pChild);
        }
    }
    return cocos;
}
/**
* @brief 连接结果
*/
struct ConnectResult
{
    // 可连接
    bool connectable    =   false;
    // 警告
    bool warning        =   false;
};
constexpr const char* COCOConnectAny    = "ANY";
constexpr const char* COCOConnectNull   = "NULL";
static ConnectResult Connectable(const WDNode::Nodes& cocos
    , const std::string& connType1
    , const std::string& connType2)
{
    if (cocos.empty())
    {
        //不创建且报错
        return { false, true };
    }

    // 当前两点连接类型
    std::string connectType1 = connType1;
    std::string connectType2 = connType2;

    if (connectType1.empty())
        connectType1 = COCOConnectNull;
    if (connectType2.empty())
        connectType2 = COCOConnectNull;

    bool bNull = false;
    if (connectType1 == COCOConnectNull
        || connectType2 == COCOConnectNull)
    {
        bNull = true;
    }
    std::vector<std::array<std::string, 2> > cocoStrings;
    cocoStrings.reserve(cocos.size());
    //先整理数据
    bool bExistAnyAny = false;
    for (const WDNode::SharedPtr& pCoco : cocos)
    {
        if (pCoco == nullptr)
            continue;
        std::string cType   =   pCoco->getAttribute("Ctype").toString();
        auto cocoTypes      =   WD::StringSplit(cType, " ");
        //assert(cocoTypes.size() == 2);
        if (cocoTypes.size() != 2)
        {
            //assert(false && "coco data exception!");
            continue;
        }

        //如果存在ANY-ANY项，直接加一个标志，不用临时存储到数组中
        if (cocoTypes[0] == COCOConnectAny
            && cocoTypes[1] == COCOConnectAny)
        {
            bExistAnyAny = true;
            continue;
        }

        cocoStrings.push_back({cocoTypes[0], cocoTypes[1]});
    }
    //COCO表为空
    if (cocoStrings.empty() && !bExistAnyAny)
    {
        //不创建且报错
        return { false, true };
    }

    //存在ANY-ANY项且传入的两个连接方式也是ANY-ANY
    if (bExistAnyAny
        && connectType1 == COCOConnectAny
        && connectType2 == COCOConnectAny)
    {
        return { true, false };
    }


    //直接查找两种类型是否存在,如果存在NULL,则报警告
    for (size_t i = 0; i < cocoStrings.size(); ++i)
    {
        const auto& cocoTypes = cocoStrings[i];
        if ((connectType1 == cocoTypes[0] && connectType2 == cocoTypes[1])
            || (connectType2 == cocoTypes[0] && connectType1 == cocoTypes[1]))
        {
            if (bNull)
            {
                //创建且报错
                return { true, true };
            }
            else
            {
                //正常创建
                return { true, false };
            }
        }
    }
    //如果没直接找到， 查找是否存在 "xxx-ANY" 或 "ANY-xxx"
    for (size_t i = 0; i < cocoStrings.size(); ++i)
    {
        const auto& cocoTypes = cocoStrings[i];

        if ((connectType1       == cocoTypes[0] && cocoTypes[1] == COCOConnectAny)
            || (connectType2    == cocoTypes[0] && cocoTypes[1] == COCOConnectAny)
            || (connectType1    == cocoTypes[1] && cocoTypes[0] == COCOConnectAny)
            || (connectType2    == cocoTypes[1] && cocoTypes[0] == COCOConnectAny))
        {
            //正常创建
            return { true, false };
        }
    }
    // 检测是否存在ANY-ANY
    if (bExistAnyAny)
    {
        //创建且报错
        return { true, true };
    }
    //不创建且报错
    return { false, true };

}
enum CheckCOCOResult
{
    //校验失败,不能连接
    //管件连接校验COCO表时,出现了意料之外的数据类型
    CCCR_Faild = 0,
    //校验成功，可以连接，但需要两个管件之间相距100mm，并且警告提示
    CCCR_Waring,
    //校验成功，可以连接
    CCCR_Success,
};
static CheckCOCOResult CheckCOCOTable(const WDNode::Nodes& cocos
    , const std::string& connectType1
    , const std::string& connectType2)
{
    // 连接校验
    auto ret = Connectable(cocos
        , connectType1
        , connectType2);
    //不能连接
    if (!ret.connectable)
        return CCCR_Faild;
    //可以连接但报警告,并且两个管件相距100毫米
    if (ret.warning)
        return CCCR_Waring;
    //可以连接
    return CCCR_Success;
}
/**
 * @brief 管件连接点
 * @param node 管件节点
 * @param reverse 是否逆流向
 * @return 点数据
 */
static WDKeyPoint ConnectPt(WDNode& node, bool reverse)
{
    WDKeyPoint result;

    if (reverse)
    {
        auto pPt = node.keyPoint(node.getAttribute("Leave").toInt());
        if (pPt != nullptr)
            result = *pPt;
    }
    else
    {
        auto pPt = node.keyPoint(node.getAttribute("Arrive").toInt());
        if (pPt != nullptr)
            result = *pPt;
    }

    return result;
}
/**
* @brief 管件连接点的另一个点
* @param node 管件节点
* @param reverse 是否逆流向
* @return 点数据
*/
static WDKeyPoint OtherPt(WDNode& node, bool reverse)
{
    WDKeyPoint result;

    if (reverse)
    {
        auto pPt = node.keyPoint(node.getAttribute("Arrive").toInt());
        if (pPt != nullptr)
            result = *pPt;
    }
    else
    {
        auto pPt = node.keyPoint(node.getAttribute("Leave").toInt());
        if (pPt != nullptr)
            result = *pPt;
    }

    return result;
}
/**
* @brief 管件目标连接点
* @param node 管件节点
* @param reverse 是否逆流向
* @return 点数据
*/
static WDKeyPoint TargetPt(WDNode& node, bool reverse)
{
    WDKeyPoint result;

    // 获取父节点
    auto pBran = node.parent();
    if (pBran == nullptr || !pBran->isType("BRAN"))
        return result;

    if (reverse)
    {
        auto pNext = WDBMDPipeUtils::NextPipeComponent(pBran, WDNode::ToShared(&node), {"ATTA"});
        if (pNext != nullptr)
        {
            auto pPt = pNext->keyPoint(pNext->getAttribute("Arrive").toInt());
            if (pPt != nullptr)
            {
                result.setBore(pPt->bore());
                result.setConnType(pPt->connType());
            }
        }
        else
        {
            result.setBore(pBran->getAttribute("Tbore").toString());
            result.setConnType(pBran->getAttribute("Tconnect").toString());
        }
    }
    else
    {
        auto pPrev = WDBMDPipeUtils::PrevPipeComponent(pBran, WDNode::ToShared(&node), {"ATTA"});
        if (pPrev != nullptr)
        {
            auto pPt = pPrev->keyPoint(pPrev->getAttribute("Leave").toInt());
            if (pPt != nullptr)
            {
                result.setBore(pPt->bore());
                result.setConnType(pPt->connType());
            }
        }
        else
        {
            result.setBore(pBran->getAttribute("Hbore").toString());
            result.setConnType(pBran->getAttribute("Hconnect").toString());
        }
    }

    return result;
}
/**
* @brief 字符串去单位化
* @param str 字符串
* @return 去单位化后的字符串
*/
static std::string Deunitization(std::string_view str)
{
    auto    resMm = StringSplit(str.data(), "mm");
    if (resMm.empty())
        return "";
    auto& strMm = resMm.front();
    if (strMm.empty())
        return "";
    auto    resKg = StringSplit(strMm, "kg");
    if (resKg.empty())
        return "";
    auto& strKg = resKg.front();

    return strKg;
}
/**
 * @brief 校验公称直径是否匹配
 * @param bore1 公称直径1
 * @param bore2 公称直径2
 * @return 是否匹配
 */
static bool CheckBore(std::string_view bore1, std::string_view bore2)
{
    // 去单位化
    std::string valueStr1 = Deunitization(bore1);
    std::string valueStr2 = Deunitization(bore2);
    // 转double
    bool bOk = false;
    auto value1 = WD::FromString<double>(valueStr1, &bOk);
    if (!bOk)
        return false;
    bOk = false;
    auto value2 = WD::FromString<double>(valueStr2, &bOk);
    if (!bOk)
        return false;

    // 设定的公称直径直接强制匹配，无需考虑精确度
    return value1 == value2;
}
WDBMDPipeUtils::ConnectCheckResult WDBMDPipeUtils::CheckConnect(WDCore& core, WDNode& node, bool reverse)
{
    ConnectCheckResult result = ConnectCheckResult::CCR_None;

    // 读取是否开启COCO校验配置
    auto pCfgPipeBran = core.cfg().query("pipeBranch");
    if (pCfgPipeBran != nullptr)
    {
        auto pCfgEnableCOCOCheck = pCfgPipeBran->query("coco.check.enable");
        if (pCfgEnableCOCOCheck != nullptr)
        {
            auto pValue = pCfgEnableCOCOCheck->value<bool>();
            if (pValue != nullptr)
            {
                if (!pValue)
                    return result;
            }
        }
    }

    // 元件库的COCO表
    WD::WDNode::Nodes cocos = GetCocos(core);

    if (!IsPipeComponent(node))
        return result;

    // 连接关键点，管件连接的一端
    auto connPt = ConnectPt(node, reverse);
    // 目标关键点，管件连接到的一端
    auto targetPt = TargetPt(node, reverse);

    // 校验DN
    if (CheckBore(connPt.bore(), targetPt.bore()))
    {
        // 校验连接类型
        if (CheckCOCOTable(cocos, connPt.connType(), targetPt.connType()) == CCCR_Success)
        {
            return CCR_None;
        }
        else
        {
            // 管件连接的另一端关键点
            auto otherPt = OtherPt(node, reverse);
            // 校验另一端DN
            if (CheckBore(otherPt.bore(), targetPt.bore()))
            {
                // 校验另一端连接类型
                if (CheckCOCOTable(cocos, otherPt.connType(), targetPt.connType()) == CCCR_Success)
                {
                    return CCR_Flip;
                }
                else
                {
                    // 此处需要对顺流向创建的FLAN类型做特殊处理，具体原因待查
                    if (node.isType("FLAN") && !reverse)
                        return CCR_SpacingFlip;
                    else 
                        return CCR_Spacing;
                }
            }
            else
            {
                // 此处需要对顺流向创建的FLAN类型做特殊处理，具体原因待查
                if (node.isType("FLAN") && !reverse)
                    return CCR_SpacingFlip;
                else 
                    return CCR_Spacing;
            }
        }
    }
    else
    {
        // 管件连接的另一端关键点
        auto otherPt = OtherPt(node, reverse);
        // 校验另一端DN
        if (CheckBore(otherPt.bore(), targetPt.bore()))
        {
            // 校验另一端连接类型
            if (CheckCOCOTable(cocos, otherPt.connType(), targetPt.connType()) == CCCR_Success)
                return CCR_Flip;
            else
                return CCR_SpacingFlip;
        }
        else
        {
            // 直接原点创建
            return CCR_Origin;
        }
    }

    //return result;
}

WD_NAMESPACE_END