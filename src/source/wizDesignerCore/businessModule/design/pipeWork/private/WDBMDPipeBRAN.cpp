#include "WDBMDPipeBRAN.h"
#include "WDBMDPipeTUBI.h"
#include "WDBMDPipeCOMS.h"
#include "WDCore.h"
#include "../WDBMDPipeUtils.h"
#include "../../WDBMDesign.h"
#include "../../../WDBMColorTable.h"
#include "../../../../common/WDDeviation.h"

WD_NAMESPACE_BEGIN

//创建直管的最小距离
static constexpr const double TubiCreateMinDistance = 1.0;

// 创建直管对象
static WDNode::SharedPtr CreateTubi(WDCore& app
    , WDNode& branch
    , const WDBMNodeRef& tubeRef
    , const WDBMNodeRef& tubeInsuRef
    , const WDBMNodeRef& tubeTracRef
    , const DVec3& hPos
    , const DVec3& tPos
    , const WDNode& prevNode
    , WDNode::SharedPtr pNextNode)
{
    auto pBranch = WDNode::ToShared(&branch);
    //创建直管节点
    auto pTubi = app.getBMDesign().create(pBranch, "TUBI", pNextNode);
    if (pTubi == nullptr)
        return nullptr;

    // 设置管子等级引用
    pTubi->setAttribute("Spref", WDBMNodeRef(tubeRef));
    pTubi->setAttribute("Ispec", WDBMNodeRef(tubeInsuRef));
    pTubi->setAttribute("Tspec", WDBMNodeRef(tubeTracRef));

    // 设置直管的起点和终点
    pTubi->setAttribute("Hposition", hPos);
    pTubi->setAttribute("Tposition", tPos);

    // 设置颜色(或材质)
    {
        // 如果前一个节点不是分支，则证明是管件
        if (!prevNode.isType("BRAN")) 
        {
            pTubi->setAttribute("AutoColor", prevNode.getAttribute("AutoColor"));
            pTubi->setMaterial(prevNode.material());
        }
        // 或者下一个管件有效
        else if (pNextNode != nullptr) 
        {
            pTubi->setAttribute("AutoColor", pNextNode->getAttribute("AutoColor"));
            pTubi->setMaterial(pNextNode->material());
        }
        // 否则使用系统默认颜色
        else
        {
            app.getBMDesign().colorTable().setNodeColor(*pTubi);
        }
    }
    // 设置标记
    auto flags = pTubi->flags();
    // 移除默认的编辑属性，因为直管是自动生成的，编辑没有意义
    flags.removeFlags(WDNode::F_Editted, WDNode::F_EdittedStatus);
    // 根据前一个管件的标记，设置直管的标记
    if (prevNode.flags().hasFlag(WDNode::F_WireFrame))
        flags.addFlag(WDNode::F_WireFrame);
    if (prevNode.flags().hasFlag(WDNode::F_CustomHightlight))
    {
        flags.addFlag(WDNode::F_CustomHightlight);
        pTubi->setCustomHighlightColor(prevNode.customHighlightColor());
    }
    if (prevNode.flags().hasFlag(WDNode::F_Active))
        flags.addFlag(WDNode::F_Active);
    if (prevNode.flags().hasFlag(WDNode::F_Highlight))
        flags.addFlag(WDNode::F_Highlight);
    if (prevNode.flags().hasFlag(WDNode::F_Clip))
        flags.addFlag(WDNode::F_Clip);

    pTubi->setFlags(flags);
    // 调用更新
    pTubi->update();

    return pTubi;
}
// 更新直管对象
static void UpdateTubi(WDCore& app
    , WDNode& tubiNode
    , const WDBMNodeRef& tubeRef
    , const WDBMNodeRef& tubeInsuRef
    , const WDBMNodeRef& tubeTracRef
    , const DVec3& hPos
    , const DVec3& tPos
    , const WDNode& prevNode
    , WDNode::SharedPtr pNextNode)
{
    // 更新管子等级引用
    tubiNode.setAttribute("Spref", WDBMNodeRef(tubeRef));
    tubiNode.setAttribute("Ispec", WDBMNodeRef(tubeInsuRef));
    tubiNode.setAttribute("Tspec", WDBMNodeRef(tubeTracRef));

    // 更新直管的起点和终点
    tubiNode.setAttribute("Hposition", hPos);
    tubiNode.setAttribute("Tposition", tPos);

    // 更新颜色(或材质)
    {
        // 如果前一个节点不是分支，则证明是管件
        if (!prevNode.isType("BRAN"))
        {
            tubiNode.setAttribute("AutoColor", prevNode.getAttribute("AutoColor"));
            tubiNode.setMaterial(prevNode.material());
        }
        // 或者下一个管件有效
        else if (pNextNode != nullptr)
        {
            tubiNode.setAttribute("AutoColor", pNextNode->getAttribute("AutoColor"));
            tubiNode.setMaterial(pNextNode->material());
        }
        // 否则使用系统默认颜色
        else
        {
            app.getBMDesign().colorTable().setNodeColor(tubiNode);
        }
    }
    // 移除默认的编辑属性，因为直管是自动生成的，编辑没有意义
    auto flags = tubiNode.flags();
    flags.removeFlags(WDNode::F_Editted, WDNode::F_EdittedStatus);
    tubiNode.setFlags(flags);

    tubiNode.update();
}

static void DestroyTubi(WD::WDNode::SharedPtr pTubiNode) 
{
    if (pTubiNode == nullptr || pTubiNode->parent() == nullptr)
        return;
    pTubiNode->sendDestroy();
    pTubiNode->parent()->removeChild(pTubiNode);
}

WDBMDPipeBRAN::WDBMDPipeBRAN(WDNode& node)
    :WDBDBase(node)
{
    _flags          =   Flag::Flag_None;
}
WDBMDPipeBRAN::~WDBMDPipeBRAN()
{
}

bool WDBMDPipeBRAN::UpdateConnectionCt(WDCore& core
    , WDNode::SharedPtr pCtBran)
{
    WDUnused(core);
    if (pCtBran == nullptr)
        return false;

    //获取分支业务数据
    auto pBranData = dynamic_cast<WDBMDPipeBRAN*>(pCtBran->getBDBase());
    if (pBranData == nullptr)
        return false;

    //清除所有未连接的数据,将在后续过程中重新计算并创建
    auto& lines = pBranData->_lines;
    // 通知线移除
    if (!lines.empty())
    {
        pBranData->noticeRemoveBefore();
        WDGraphableLines().swap(lines);
    }

    // 分支所有子节点
    auto    pChildren = pCtBran->children();

    // 上游位置
    DVec3               gPrevPos = pCtBran->getAttribute("Hposition").toDVec3();
    // 上游方向
    DVec3               gPrevDir = pCtBran->getAttribute("Hdirection").toDVec3();
    // 上游tubi节点
    WDNode::SharedPtr   pPrevTubi = nullptr;
    // 上游管子等级节点
    WDNode::SharedPtr   pPrevStube = pCtBran->getAttribute("Hstube").toNodeRef().refNode();
    // 上游管径
    std::string         prevBore = pCtBran->getAttribute("Hbore").toString();
    // 上游桥件是否是BEND
    bool                prevIsBend = false;

    FVec3Vector unconnPts;
    for (auto& pChild : pChildren)
    {
        if (pChild == nullptr)
            continue;

        if (pChild->isType("TUBI"))
        {
            pPrevTubi = pChild;
            continue;
        }
        else if (WDBMDPipeUtils::IsPipeComponent(*pChild))
        {
            // 获取桥件出入口点
            auto    pArrive = pChild->keyPoint(pChild->getAttribute("Arrive").toInt());
            auto    pLeave = pChild->keyPoint(pChild->getAttribute("Leave").toInt());
            if (pArrive == nullptr || pLeave == nullptr)
                continue;

            // 更新管子节点
            auto    gNextPos = pArrive->transformedPosition(pChild->localTransform());
            auto    gNextDir = pArrive->transformedDirection(pChild->localRSTransform());
            auto& nextBore = pArrive->bore();
            if (pPrevTubi != nullptr)
            {
                UpdateTubiWithRemove(*pCtBran, *pPrevTubi
                    , gPrevPos, gPrevDir, prevBore
                    , gNextPos, gNextDir, nextBore
                    , prevIsBend
                    , unconnPts);
                // 下一次的 上游tubi节点需重新获取
                pPrevTubi = nullptr;
            }
            else
            {
                UpdateTubiWithCreate(*pCtBran, pChild
                    , pPrevStube, gPrevPos, gPrevDir, prevBore
                    , gNextPos, gNextDir, nextBore
                    , unconnPts);
            }

            // 更新中间数据
            gPrevPos = pLeave->transformedPosition(pChild->localTransform());
            gPrevDir = pLeave->transformedDirection(pChild->localRSTransform());
            pPrevStube = pChild->getAttribute("Lstube").toNodeRef().refNode();
            prevBore = pLeave->bore();
            if (pChild->isType("BEND"))
            {
                prevIsBend = true;
            }
            else
            {
                prevIsBend = false;
            }
        }
    }

    // 分支尾坐标朝向
    DVec3   gTPos = pCtBran->getAttribute("Tposition").toDVec3();
    DVec3   gTDir = pCtBran->getAttribute("Tdirection").toDVec3();
    // 分支尾管径
    auto    tBore = pCtBran->getAttribute("Tbore").toString();
    if (pPrevTubi != nullptr)
    {
        // 更新Tubi节点
        UpdateTubiWithRemove(*pCtBran, *pPrevTubi
            , gPrevPos, gPrevDir, prevBore
            , gTPos, gTDir, tBore
            , prevIsBend
            , unconnPts);
    }
    else
    {
        // 更新Tubi节点
        UpdateTubiWithCreate(*pCtBran, nullptr
            , pPrevStube, gPrevPos, gPrevDir
            , prevBore, gTPos, gTDir
            , tBore
            , unconnPts);
    }

    // 处理未连接线
    if (!unconnPts.empty())
    {
        lines.push_back(WDGraphableLine());
        lines.back().points = std::forward<FVec3Vector>(unconnPts);
        lines.back().color = Color(50, 255, 0, 255);
        lines.back().lineWidth = 2.0f;
        // 通知线添加
        pBranData->noticeAddAfter();
    }

    //连接计算完成,调用节点更新
    pCtBran->setFlags(pCtBran->flags().addFlag(WDNode::F_Update));
    pCtBran->update();

    return true;
}

void WDBMDPipeBRAN::setFlags(Flags flgs)
{
    if (_flags == flgs)
        return;
    auto oldFlag = _flags;
    _flags = flgs;

}

WDBDBase* WDBMDPipeBRAN::clone(WDNode& node) const
{
    auto p = new WDBMDPipeBRAN(node);
    p->copy(*this);
    return p;
}
void WDBMDPipeBRAN::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDPipeBRAN* pSrc = dynamic_cast<const WDBMDPipeBRAN*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    this->_lines            =   pSrc->_lines;
}

WDGraphableInterface* WDBMDPipeBRAN::graphableSupporter()
{
    return this;
}
const WDSelectionInterface* WDBMDPipeBRAN::selectionSupporter() const
{
    return this;
}

bool WDBMDPipeBRAN::pickup(const DMat4& transformMatrix
    , const WDPickupParam& param
    , PickupResult& outResult) const
{
    if (_lines.empty())
        return false;
    // 拾取线数据
    bool bRet = false;
    for (const auto& line : _lines)
    {
        if (line.pickup(transformMatrix, param, outResult))
            bRet = true;
    }
    return bRet;
}
WDBMDPipeBRAN::FrameSelectResult WDBMDPipeBRAN::frameSelect(const DMat4& transformMatrix
    , const WDFrameSelectParam& param) const
{
    using SelectRet = WDSelectionInterface::FrameSelectResult;

    if (_lines.empty())
        return SelectRet::FSR_NoData;

    // 框选线数据
    std::vector<SelectRet> rets;
    rets.reserve(_lines.size());
    for (const auto& line : _lines)
    {
        auto ret = line.frameSelect(transformMatrix, param);
        rets.emplace_back(ret);
    }

    return WDSelectionInterface::MergeFrameSelectResult(rets);
}

DAabb3 WDBMDPipeBRAN::gRenderAabb() const
{
    DAabb3 rAabb;
    if (_lines.empty())
        return rAabb;
    for (const auto& line : _lines)
    {
        line.mergeToAabb(rAabb);
    }
    return rAabb;
}
const WDGraphableLines* WDBMDPipeBRAN::gLines()
{
    if (_lines.empty())
        return nullptr;
    return &_lines;
}

/**
* @brief 更新分支的头管子等级引用, 会用分支管道等级和头管径去查找一个管子等级并设置为头管子等级引用
* -info 如果没有查找到对应的管子等级，则不修改头管子等级引用（这里理论上需要置空，但为了和PXXS保持一致所以也不修改）
* @param node 分支节点
*/
void PipeBranHRefUpdate(WD::WDNode& node)
{
    // 分支上的管道等级
    auto pSpec = node.getAttribute("Pspec").toNodeRef().refNode();
    if (pSpec == nullptr)
        return;
    // 获取管子等级
    const auto& hBore = node.getAttribute("Hbore").toString();
    auto pStube = WDBMDPipeUtils::GetStubeBySPEC(pSpec, hBore);
    // 设置当前管子等级
    if (pStube != nullptr)
        node.setAttribute("Hstube", WDBMNodeRef(pStube));
}

void WDBMDPipeBRAN::onModelUpdate(WDCore& core, WDNode& node)
{
    // 更新管子引用, 当分支的头管子等级为空时, 会用分支上的管道等级和分支头管径去查找一个管子等级并设置为自身的管子引用
    PipeBranHRefUpdate(node);
    // 更新连接
    UpdateConnection(core, node);
}

void WDBMDPipeBRAN::UpdateTubiWithRemove(WDNode& bran
        , WDNode& tubi
        , const DVec3& gHPos
        , const DVec3& gHDir
        , const std::string& hBore
        , const DVec3& gTPos
        , const DVec3& gTDir
        , const std::string& tBore
        , bool prevIsBend
        , FVec3Vector& unconnPts)
{
    if (!bran.isType("BRAN"))
        return ;
    if (!tubi.isType("TUBI"))
        return;

    // 校验能否生成管子
    auto    cRet    =   DKeyPoint::CheckConnectionTo(gHPos, gHDir, gTPos, gTDir, WDDeviation::GetDistDeviation(DVec3::Distance(gHPos, gTPos)));
        
    if (!cRet.first && cRet.second >= TubiCreateMinDistance)
    {
        unconnPts.push_back(FVec3(gHPos));
        unconnPts.push_back(FVec3(gTPos));
    }
    else
    {
        if (cRet.second >= TubiCreateMinDistance)
        {
            // 判断两端管径是否相同
            if (FromString<double>(hBore) == FromString<double>(tBore))
            {
                tubi.setAttribute("Hposition", gHPos);
                tubi.setAttribute("Tposition", gTPos);
                tubi.update();
                return ;
            }
            else
            {
                unconnPts.push_back(FVec3(gHPos));
                unconnPts.push_back(FVec3(gTPos));
            }
        }
        else if (cRet.second <= -TubiCreateMinDistance)
        {
            unconnPts.push_back(FVec3(gHPos));
            unconnPts.push_back(FVec3(gTPos));
        }
    }

    if (!prevIsBend)
    {
        // 发送即将销毁通知
        tubi.sendDestroy();
        // 移除直管节点
        bran.removeChild(WDNode::ToShared(&tubi));
    }
    else
    {
        tubi.setAttribute("Tposition", tubi.getAttribute("Hposition"));
        tubi.update();
    }
}

WDNode::SharedPtr WDBMDPipeBRAN::UpdateTubiWithCreate(WDNode& bran
        , WDNode::SharedPtr pNext
        , WDNode::SharedPtr pSpco
        , const DVec3& gHPos
        , const DVec3& gHDir
        , const std::string& hBore
        , const DVec3& gTPos
        , const DVec3& gTDir
        , const std::string& tBore
        , FVec3Vector& unconnPts)
{
    if (!bran.isType("BRAN"))
        return nullptr;
    auto pBmBase = bran.getBMBase();
    if (pBmBase == nullptr)
        return nullptr;
    // 校验能否生成管子
    auto    cRet    =   DKeyPoint::CheckConnectionTo(gHPos, gHDir, gTPos, gTDir, WDDeviation::GetDistDeviation(DVec3::Distance(gHPos, gTPos)));

    WDNode::SharedPtr pTubi = nullptr;
    if (!cRet.first && cRet.second >= TubiCreateMinDistance)
    {
        unconnPts.push_back(FVec3(gHPos));
        unconnPts.push_back(FVec3(gTPos));
    }
    else
    {
        if (cRet.second >= TubiCreateMinDistance)
        {
            // 判断两端管径是否相同
            if (FromString<double>(hBore) == FromString<double>(tBore))
            {
                // 新建管子节点
                pTubi = pBmBase->create(WDNode::ToShared(&bran), "TUBI", pNext);
                if (pTubi != nullptr)
                {
                    pTubi->setAttribute("Spref", WDBMNodeRef(pSpco));

                    //设置颜色(或材质), 获取前一个或后一个有效的管件节点，然后使用该节点的颜色
                    //如果未获取到，则使用默认颜色
                    {
                        //先从下一个管件节点拿材质
                        bool bSet = false;
                        if (pNext != nullptr)
                        {
                            pTubi->setAttribute("AutoColor", pNext->getAttribute("AutoColor"));
                            pTubi->setMaterial(pNext->material());
                            bSet = true;
                        }
                        //再从临近的管件节点拿材质
                        if (!bSet)
                        {
                            auto pPipeComNode = WDBMDPipeUtils::AdjacentPipeComponent(WDNode::ToShared(&bran), pTubi);
                            if (pPipeComNode != nullptr)
                            {
                                pTubi->setAttribute("AutoColor", pPipeComNode->getAttribute("AutoColor"));
                                pTubi->setMaterial(pPipeComNode->material());
                                bSet = true;
                            }
                        }
                    }
                    // 更新
                    pTubi->update();

                    // 设置管子起始结束坐标
                    pTubi->setAttribute("Hposition", gHPos);
                    pTubi->setAttribute("Tposition", gTPos);
                    pTubi->update();
                }
            }
            else
            {
                unconnPts.push_back(FVec3(gHPos));
                unconnPts.push_back(FVec3(gTPos));
            }
        }
        else if (cRet.second <= -TubiCreateMinDistance)
        {
            unconnPts.push_back(FVec3(gHPos));
            unconnPts.push_back(FVec3(gTPos));
        }
    }

    return pTubi;
}


bool    WDBMDPipeBRAN::UpdateConnection(WDCore& core
    , WDNode& branch)
{
    //获取分支业务数据
    auto pBranchData = dynamic_cast<WDBMDPipeBRAN*>(branch.getBDBase());
    if (pBranchData == nullptr)
        return false;

    //清除所有未连接的数据,将在后续过程中重新计算并创建
    auto& lines = pBranchData->_lines;
    // 通知线移除
    if (!lines.empty())
    {
        pBranchData->noticeRemoveBefore();
        WDGraphableLines().swap(lines);
    }

    struct DTubi
    {
        // 直管节点
        WDNode::SharedPtr pTubi = nullptr;
        // 是否被重复使用
        bool reused = false;
    };
    // 直管以及其前一个管件
    std::unordered_map<const WDNode*, DTubi> tubiMap;
    // 管件列表
    std::vector<WDNode::SharedPtr> coms;
    // 冗余的管子
    std::vector<WDNode::SharedPtr> redundancyTubis;
    // 获取管件列表，直管列表
    {
        coms.reserve(branch.childCount());
        redundancyTubis.reserve(branch.childCount());
        WD::WDNode::SharedPtr pPrevNode = WD::WDNode::ToShared(&branch);
        for (auto pChild : branch.children())
        {
            if (pChild == nullptr)
                continue;
            if (pChild->isType("TUBI"))
            {
                if (pPrevNode != nullptr)
                {
                    tubiMap[pPrevNode.get()] = { pChild, false };
                    pPrevNode = nullptr;
                }
                else
                {
                    redundancyTubis.push_back(pChild);
                }
            }
            // ATTA管件不会参与连接计算，这里全部过滤掉
            else if (pChild->isType("ATTA"))
            {
                continue;
            }
            else if (WDBMDPipeUtils::IsPipeComponent(*pChild))
            {
                coms.push_back(pChild);
                pPrevNode = pChild;
            }
        }
    }
    // 销毁冗余的管子
    for (const auto& pTubi : redundancyTubis)
    {
        DestroyTubi(pTubi);
    }

    // 指定前一个管件, 查找直管
    auto funcFindTubi = [&tubiMap](const WDNode& prevNode) ->DTubi*
        {
            auto fItr = tubiMap.find(&prevNode);
            if (fItr == tubiMap.end())
                return nullptr;
            return &(fItr->second);
        };

    auto hBore = branch.getAttribute("Hbore").toString();
    auto tBore = branch.getAttribute("Tbore").toString();
    auto dHBore = FromString<double>(hBore);
    auto dTBore = FromString<double>(tBore);
    auto hStube = branch.getAttribute("Hstube").toNodeRef();
    auto iSpec = branch.getAttribute("Ispec").toNodeRef();
    auto tSpec = branch.getAttribute("Tspec").toNodeRef();
    // 这里的连接计算统一在当前分支的坐标系下进行运算, 在当前分支节点坐标系下描述的 分支起点位置和朝向，终点位置和朝向
    DVec3 branHPos = branch.getAttribute("Hposition").toDVec3(); 
    DVec3 branHDir = branch.getAttribute("Hdirection").toDVec3();
    DVec3 branTPos = branch.getAttribute("Tposition").toDVec3(); 
    DVec3 branTDir = branch.getAttribute("Tdirection").toDVec3();
    DKeyPoint branHKeyPt = DKeyPoint(branHPos, branHDir);
    DKeyPoint branTKeyPt = DKeyPoint(branTPos, branTDir);

    FVec3Vector unconnPts;
    /******* 1. 如果分支不包含管件，则连接 头 和 尾********/
    if (coms.empty())
    {
        // 关键点数据
        const DKeyPoint& ptA = branHKeyPt;
        const DKeyPoint& ptB = branTKeyPt;
        // 校验连接
        auto cRet = DKeyPoint::CheckConnectionTo(ptA, ptB, WDDeviation::GetDistDeviation(DVec3::Distance(ptA.position, ptB.position)));
        if (cRet.first // 可以连接
            && cRet.second >= TubiCreateMinDistance // 满足生成直管的最小距离
            && dHBore == dTBore // 相同的公称直径
            && !hStube.empty()) // 管子等级有效
        {
            // 查找直管
            auto pDTubi = funcFindTubi(branch);
            if (pDTubi != nullptr && pDTubi->pTubi != nullptr)
            {
                pDTubi->reused = true;
                // 更新直管
                UpdateTubi(core, *(pDTubi->pTubi)
                    , hStube, iSpec, tSpec
                    , ptA.position, ptB.position
                    , branch, nullptr);
            }
            else
            {
                // 创建直管
                CreateTubi(core, branch
                    , hStube, iSpec, tSpec
                    , ptA.position, ptB.position
                    , branch, nullptr);
            }
        }
        else 
        {
            // 距离大于最小直管距离，则生成虚线
            if (Abs(cRet.second) >= TubiCreateMinDistance)
            {
                unconnPts.push_back(FVec3(ptA.position));
                unconnPts.push_back(FVec3(ptB.position));
            }
        }
    }
    else
    {
        /******* 2. 如果分支包含管件，则先连接 头 和 第一个管件********/
        {
            //拿到第一个管件
            WDNode::SharedPtr pFirstChild = coms[0];
            //第一个管件的入口点
            auto pPt = pFirstChild->keyPoint(pFirstChild->getAttribute("Arrive").toInt());
            if (pPt != nullptr)
            {
                auto dBore = FromString<double>(pPt->bore());
                //关键点数据
                const DKeyPoint& ptA = branHKeyPt;
                DKeyPoint ptB = pPt->transformed(pFirstChild->localTransform());
                //校验连接
                auto cRet = DKeyPoint::CheckConnectionTo(ptA, ptB, WDDeviation::GetDistDeviation(DVec3::Distance(ptA.position, ptB.position)));
                if (cRet.first // 可以连接
                    && cRet.second >= TubiCreateMinDistance // 满足生成直管的最小距离
                    && dHBore == dBore// 相同的公称直径
                    && !hStube.empty()) // 管子等级有效
                {
                    // 查找直管
                    auto pDTubi = funcFindTubi(branch);
                    if (pDTubi != nullptr && pDTubi->pTubi != nullptr)
                    {
                        pDTubi->reused = true;
                        // 更新直管
                        UpdateTubi(core, *(pDTubi->pTubi)
                            , hStube, iSpec, tSpec
                            , ptA.position, ptB.position
                            , branch, pFirstChild);
                    }
                    else
                    {
                        // 创建直管
                        CreateTubi(core, branch
                            , hStube, iSpec, tSpec
                            , ptA.position, ptB.position
                            , branch, pFirstChild);
                    }
                }
                else
                {
                    // 距离大于最小直管距离，则生成虚线
                    if (Abs(cRet.second) >= TubiCreateMinDistance)
                    {
                        unconnPts.push_back(FVec3(ptA.position));
                        unconnPts.push_back(FVec3(ptB.position));
                    }
                }
            }
            else
            {
                // 没拿到管道的关键点时用管件的坐标作为关键点的坐标生成虚线
                auto p0 = branHPos;
                auto p1 = pFirstChild->getAttribute("Position").toDVec3();
                if (DVec3::Distance(p0, p1) >= TubiCreateMinDistance)
                {
                    unconnPts.push_back(FVec3(p0));
                    unconnPts.push_back(FVec3(p1));
                }
            }
        }

        /******* 3. 连接 管件 和 管件********/
        {
            for (size_t i = 0; i < coms.size() - 1; ++i)
            {
                //拿到前一个和后一个管件
                WDNode::SharedPtr pChildLeave = coms[i];
                WDNode::SharedPtr pChildArrive = coms[i + 1];
                auto pPtLeave = pChildLeave->keyPoint(pChildLeave->getAttribute("Leave").toInt());
                auto pPtArrive = pChildArrive->keyPoint(pChildArrive->getAttribute("Arrive").toInt());
                if (pPtLeave != nullptr && pPtArrive != nullptr)
                {
                    auto dPrevBore = FromString<double>(pPtLeave->bore());
                    auto dNextBore = FromString<double>(pPtArrive->bore());
                    //关键点数据
                    DKeyPoint ptA = pPtLeave->transformed(pChildLeave->localTransform());
                    DKeyPoint ptB = pPtArrive->transformed(pChildArrive->localTransform());
                    // 管子等级
                    auto refLstube = pChildLeave->getAttribute("Lstube").toNodeRef();
                    //校验连接
                    auto cRet = DKeyPoint::CheckConnectionTo(ptA, ptB, WDDeviation::GetDistDeviation(DVec3::Distance(ptA.position, ptB.position)));
                    if (cRet.first // 可以连接
                        && cRet.second >= TubiCreateMinDistance // 满足生成直管的最小距离
                        && dPrevBore == dNextBore // 相同的公称直径
                        && !refLstube.empty()) // 管子等级有效
                    {
                        // 查找直管
                        auto pDTubi = funcFindTubi(*pChildLeave);
                        if (pDTubi != nullptr && pDTubi->pTubi != nullptr)
                        {
                            pDTubi->reused = true;
                            // 更新直管
                            UpdateTubi(core, *(pDTubi->pTubi)
                                , refLstube, iSpec, tSpec
                                , ptA.position, ptB.position
                                , *pChildLeave, pChildArrive);
                        }
                        else
                        {
                            // 创建直管
                            CreateTubi(core, branch
                                , refLstube, iSpec, tSpec
                                , ptA.position, ptB.position
                                , *pChildLeave, pChildArrive);
                        }
                    }
                    else
                    {
                        // 距离大于最小直管距离，则生成虚线
                        if (Abs(cRet.second) >= TubiCreateMinDistance)
                        {
                            unconnPts.push_back(FVec3(ptA.position));
                            unconnPts.push_back(FVec3(ptB.position));
                        }
                    }
                }
                else
                {
                    DVec3 p0 = DVec3::Zero();
                    DVec3 p1 = DVec3::Zero();
                    // 没拿到管道的关键点时用管件的坐标作为关键点的坐标生成虚线
                    if (pPtLeave != nullptr)
                        p0 = pPtLeave->transformedPosition(pChildLeave->localTransform());
                    else
                        p0 = pChildLeave->getAttribute("Position").toDVec3();

                    if (pPtArrive != nullptr)
                        p1 = pPtArrive->transformedPosition(pChildArrive->localTransform());
                    else
                        p1 = pChildArrive->getAttribute("Position").toDVec3();

                    if (DVec3::Distance(p0, p1) >= TubiCreateMinDistance)
                    {
                        unconnPts.push_back(FVec3(p0));
                        unconnPts.push_back(FVec3(p1));
                    }
                }
            }
        }

        /******* 4. 连接 最后一个管件 和 尾********/
        {
            //拿到最后一个管件
            WDNode::SharedPtr pLastChild = coms[coms.size() - 1];
            //拿到管件数据
            auto pPt = pLastChild->keyPoint(pLastChild->getAttribute("Leave").toInt());
            if (pPt != nullptr)
            {
                auto dBore = FromString<double>(pPt->bore());
                //关键点数据
                DKeyPoint ptA = pPt->transformed(pLastChild->localTransform());
                const DKeyPoint& ptB = branTKeyPt;
                // 管子等级
                auto refLstube = pLastChild->getAttribute("Lstube").toNodeRef();
                //校验连接
                auto cRet = DKeyPoint::CheckConnectionTo(ptA, ptB, WDDeviation::GetDistDeviation(DVec3::Distance(ptA.position, ptB.position)));
                if (cRet.first // 可以连接
                    && cRet.second >= TubiCreateMinDistance // 满足生成直管的最小距离
                    && dBore == dTBore // 相同的公称直径
                    && !refLstube.empty()) // 管子等级有效
                {
                    // 查找直管
                    auto pDTubi = funcFindTubi(*pLastChild);
                    if (pDTubi != nullptr && pDTubi->pTubi != nullptr)
                    {
                        pDTubi->reused = true;
                        // 更新直管
                        UpdateTubi(core, *(pDTubi->pTubi)
                            , refLstube, iSpec, tSpec
                            , ptA.position, ptB.position
                            , *pLastChild, nullptr);
                    }
                    else
                    {
                        // 创建直管
                        CreateTubi(core, branch
                            , refLstube, iSpec, tSpec
                            , ptA.position, ptB.position
                            , *pLastChild, nullptr);
                    }
                }
                else
                {
                    // 距离大于最小直管距离，则生成虚线
                    if (Abs(cRet.second) >= TubiCreateMinDistance)
                    {
                        unconnPts.push_back(FVec3(ptA.position));
                        unconnPts.push_back(FVec3(ptB.position));
                    }
                }
            }
            else
            {
                // 没拿到管道的关键点时用管件的坐标作为关键点的坐标生成虚线
                auto p0 = pLastChild->getAttribute("Position").toDVec3();
                auto p1 = branTPos;
                if (DVec3::Distance(p0, p1) >= TubiCreateMinDistance)
                {
                    unconnPts.push_back(FVec3(p0));
                    unconnPts.push_back(FVec3(p1));
                }
            }
        }
    }

    // 将所有未使用的管件统一销毁
    for (auto itr = tubiMap.begin(); itr != tubiMap.end(); ++itr) 
    {
        if (itr->second.pTubi == nullptr)
            continue;
        if (itr->second.reused)
            continue;
        DestroyTubi(itr->second.pTubi);
    }

    // 处理未连接线
    if (!unconnPts.empty())
    {
        lines.push_back(WDGraphableLine());
        lines.back().points = std::forward<FVec3Vector>(unconnPts);
        lines.back().color = Color(50, 255, 0, 255);
        lines.back().lineWidth = 2.0f;
        // 通知线添加
        pBranchData->noticeAddAfter();
    }
    return true;
}


WD_NAMESPACE_END