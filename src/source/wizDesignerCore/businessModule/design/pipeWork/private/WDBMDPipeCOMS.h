#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"

WD_NAMESPACE_BEGIN

/**
* @brief 管件数据
*   可以指定多种管件类型
*   例如:  
*       垫片
*       法兰
*       异径管
*       管子
*       阀门
*       弯头
*       ...... (还有很多未列举)
*   都是管件类型的一种
*/
class WD_API WDBMDPipeCOMS : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    WDBMDModelHelpter _modelHelpter;
public:
    WDBMDPipeCOMS(WDNode& node);
    virtual ~WDBMDPipeCOMS();
public:
    static void RegistAttribute(WDBMTypeDesc& des);
public:
    /**
    * @brief 重置管件连接(或 初始化管件连接)
    * @param app
    * @param pPipeComponentNode 管件节点
    * @param autoConnection 是否自动连接
    *   true: 将自动将管件的入口点重置到前一个管件的出口点(或分支起点)，保证管件之前正常连接
    *   false: 将管件放置到世界坐标原点并且不发生任何旋转
    * @param distance 如果自动连接(autoConnection == true)时
    *   前一个管件的出口点到当前管件入口点的距离,将按照一定的距离间隔摆放管件
    */
    static bool RestoreConnection(WDCore& app
        , WDNode::SharedPtr pPipeComponentNode
        , bool autoConnection = true
        , double spoolDistance = 0.0);
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void  copy(const WDBDBase& src) override;
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual void mData(WDCore& core,WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef mSpref() const override;
    virtual void mGVars(WDCore& core, WDBMGVars& gVars) const override;
    virtual bool mInsuGVars(WDCore& core, WDBMGVars& gVars) const override;
protected:
    virtual void onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu) override;
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
};


/**
 * @brief 指定管件节点，获取管件描述
 *  管件描述 = pSPCONode->detref->Rtext属性值 + pSPCONode->Matxt->Xtext属性
 * @param dNode 管件节点
 * @return 管件描述
*/
std::string GetPipeComponentDescription(WDCore& core
    , const WDNode& dNode);

WD_NAMESPACE_END

