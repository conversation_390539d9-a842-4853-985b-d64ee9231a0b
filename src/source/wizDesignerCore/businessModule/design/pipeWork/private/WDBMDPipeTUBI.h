#pragma once

#include "../../../WDBDBase.h"
#include "../../private/WDBMDModelHelpter.h"
#include "WDBMDPipeCOMS.h"

WD_NAMESPACE_BEGIN

/**
* @brief 直管数据
*/
class WD_API WDBMDPipeTUBI : public WDBDBase
    , public WDBMDModelHelpter::MDelegate
{
private:
    // 模型生成
    WDBMDModelHelpter   _modelHelpter;
public:
    WDBMDPipeTUBI(WDNode& node);
    ~WDBMDPipeTUBI();
public:
    static void RegistAttribute(WDBMTypeDesc& des);
public:
    virtual WDBDBase* clone(WDNode& node) const override;
    virtual void copy(const WDBDBase& src) override;
public:
    virtual const WDSelectionInterface* selectionSupporter() const override;
    virtual WDGraphableInterface* graphableSupporter() override;
protected:
    virtual WDNode& mNode() override
    {
        return this->node();
    }
    virtual void mData(WDCore& core, WDBMDModelHelpter::MData& data) override;
    virtual WDBMNodeRef mSpref() const override;
protected:
    virtual void onModelUpdate(WDCore& core, WDNode& node) override;
private:
    /**
     * @brief 获取外径
    */
    double diameter() const;
};


WD_NAMESPACE_END

