#include "WDBMDPipeTUBI.h"

#include "../../../../geometry/WDGeometryMgr.h"
#include "../../../../WDCore.h"
#include "../WDBMDPipeUtils.h"

WD_NAMESPACE_BEGIN

WDBMDPipeTUBI::WDBMDPipeTUBI(WDNode& node)
    : _modelHelpter(*this)
    , WDBDBase(node)
{
}
WDBMDPipeTUBI::~WDBMDPipeTUBI()
{
}

void WDBMDPipeTUBI::RegistAttribute(WDBMTypeDesc& des)
{
    auto& core = des.core();
    // 长度属性
    des.add("ltlength", WDBMAttrValueType::T_Double
        , [](const WDNode& node)
        {
            auto hPos = node.getAttribute("Hposition").toDVec3();
            auto tPos = node.getAttribute("Tposition").toDVec3();
            return DVec3::Distance(hPos, tPos);
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::F_ReadOnly);
    // 公称直径属性
    des.add("Lbore", WDBMAttrValueType::T_Double
        , [](const WDNode& node)
        {
            auto pSprefNode = node.getAttribute("Spref").toNodeRef().refNode();
            if (pSprefNode == nullptr)
                return 0.0;
            auto pSCOMNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
            if (pSCOMNode == nullptr)
                return 0.0;
            //默认从第一个参数中获取公称直径
            const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
            if (vals.size() < 1)
                return 0.0;
            bool bOk = false;
            double rBore = FromString<double>(vals[0], &bOk);
            if (!bOk)
                return 0.0;
            return rBore;
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::F_ReadOnly);
    // 公称直径属性
    des.add("Abore", WDBMAttrValueType::T_Double
        , [](const WDNode& node)
        {
            auto pSprefNode = node.getAttribute("Spref").toNodeRef().refNode();
            if (pSprefNode == nullptr)
                return 0.0;
            auto pSCOMNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
            if (pSCOMNode == nullptr)
                return 0.0;
            //默认从第一个参数中获取公称直径
            const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
            if (vals.size() < 1)
                return 0.0;
            bool bOk = false;
            double rBore = FromString<double>(vals[0], &bOk);
            if (!bOk)
                return 0.0;
            return rBore;
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::Flag(WDBMAttrDesc::F_ReadOnly | WDBMAttrDesc::F_Hidden));
    //管件描述  "PipeComponentDescription"
    // 经过讨论(客户有这个需求)，我们需要将管件描述属性引入进来(PXXS没有这个属性，只能通过命令行去查)
    des.add("PipeComponentDescription"
        , WDBMAttrValueType::T_String
        , [&core](const WDNode& node) -> std::string
        {
            auto pBase = node.getBDBase();
            if (pBase == nullptr)
                return "";
            auto ret = GetPipeComponentDescription(core, node);
            return ret;
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::F_ReadOnly);
    // 保温参数  "Iparam" (直径，半径)
    // 将保温参数属性引入进来(PXXS中这个属性是隐藏属性，也是动态属性 根据打开的情况实时获取的，可通过命令行去查)
    des.add("Iparam"
        , WDBMAttrValueType::T_DoubleVector
        , [&core](const WDNode& node) -> std::vector<double>
        {
            // node计算保温参数
            auto rValue = WDBMDPipeUtils::GetPipeComponentInsulation(core, node);
            if (rValue)
                return rValue.value();
            else
                return std::vector<double>();
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::Flags(WDBMAttrDesc::F_Hidden | WDBMAttrDesc::F_Update));

    // Aodiam 属性, 入口点外径
    des.add("Aodiam"
        , WDBMAttrValueType::T_Double
        , [&core](const WDNode& node)
        {
            auto pSprefNode = node.getAttribute("Spref").toNodeRef().refNode();
            if (pSprefNode == nullptr)
                return WDBMAttrValue(0.0);
            // 获取等级引用的元件
            auto pSCOMNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
            if (pSCOMNode == nullptr)
                return WDBMAttrValue(0.0);
            //默认从第二个参数中获取外径
            const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
            if (vals.size() < 2)
                return WDBMAttrValue(0.0);
            bool bOk = false;
            double rDiam = FromString<double>(vals[1], &bOk);
            if (!bOk)
                return WDBMAttrValue(0.0);
            return WDBMAttrValue(rDiam);
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , { WDBMAttrDesc::F_ReadOnly | WDBMAttrDesc::F_Hidden });
    // Lodiam 属性, 出口点外径
    des.add("Lodiam"
        , WDBMAttrValueType::T_Double
        , [&core](const WDNode& node)
        {
            auto pSprefNode = node.getAttribute("Spref").toNodeRef().refNode();
            if (pSprefNode == nullptr)
                return WDBMAttrValue(0.0);
            // 获取等级引用的元件
            auto pSCOMNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
            if (pSCOMNode == nullptr)
                return WDBMAttrValue(0.0);
            //默认从第二个参数中获取外径
            const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
            if (vals.size() < 2)
                return WDBMAttrValue(0.0);
            bool bOk = false;
            double rDiam = FromString<double>(vals[1], &bOk);
            if (!bOk)
                return WDBMAttrValue(0.0);
            return WDBMAttrValue(rDiam);
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , { WDBMAttrDesc::F_ReadOnly | WDBMAttrDesc::F_Hidden });

    // 给直管类型添加自动生成标志，表明直管类型不需要做序列化
    auto flags = des.flags();
    flags.addFlag(WDBMTypeDesc::F_AutoGeneration);
    des.setFlags(flags);
}

WDBDBase* WDBMDPipeTUBI::clone(WDNode& node) const
{
    auto p = new WDBMDPipeTUBI(node);
    p->copy(*this);
    return p;
}
void    WDBMDPipeTUBI::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDPipeTUBI* pSrc = dynamic_cast<const WDBMDPipeTUBI*>(&src);
    if (pSrc == nullptr)
        return;

    WDBDBase::copy(src);

    _modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDPipeTUBI::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDPipeTUBI::graphableSupporter()
{
    return &_modelHelpter;
}

void WDBMDPipeTUBI::mData(WDCore& core, WDBMDModelHelpter::MData& data)
{
    auto& node = this->node();
    // 长度
    float len  = static_cast<float>(node.getAttribute("ltlength").toDouble());
    // 长度为0，无法生成直管
    if (len <= NumLimits<float>::Epsilon)
        return;
    // 外径
    float diam  = static_cast<float>(node.getAttribute("Lodiam").toDouble());
    // 外径为0，无法生成直管
    if (diam <= NumLimits<float>::Epsilon)
        return;
    // 基础模型
    WDGeometry::GFlags flags = { WDGeometry::GF_Entity | WDGeometry::GF_CHard };
    auto pRGeom = core.geometryMgr().createCylinder(diam, len, flags);
    if (pRGeom != nullptr)
        data.geoms.push_back(pRGeom);
    // 保温模型
    double insulation = 0.0;
    auto iParam = node.getAttribute("Iparam").toDoubleVector();
    if (iParam.size() != 0)
        insulation = iParam.at(0);
    // 保温半径小于0时，不用生成保温模型
    if (insulation > NumLimits<double>::Epsilon)
    {
        WDGeometry::GFlags tFlags = { WDGeometry::GF_Entity | WDGeometry::GF_Insu };
        auto pRGeomInsu = core.geometryMgr().createCylinder(diam + static_cast<float>(insulation), len, tFlags);
        if (pRGeomInsu != nullptr)
            data.geoms.push_back(pRGeomInsu);
    }
}

WDBMNodeRef WDBMDPipeTUBI::mSpref() const
{
    // 直管不用生成元件模型，因此这里不用返回等级节点
    return WDBMNodeRef();
}

void WDBMDPipeTUBI::onModelUpdate(WDCore& core, WDNode& node)
{
    // 更新， 用于获取元件上的关键点数据
    WDUnused(node);
    _modelHelpter.updateModel(core);
}

double  WDBMDPipeTUBI::diameter() const
{
    // 先获取到直管等级
    auto pSprefNode = node().getAttribute("Spref").toNodeRef().refNode();
    if (pSprefNode == nullptr)
        return 0.0;
    // 获取等级引用的元件
    auto pSCOMNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
    if (pSCOMNode == nullptr)
        return 0.0;
    // 默认从第二个参数中获取外径
    const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
    if (vals.size() < 2)
        return 0.0;
    bool bOk = false;
    double rDiam = FromString<double>(vals[1], &bOk);
    if (!bOk)
        return 0.0;
    return rDiam;
}

WD_NAMESPACE_END