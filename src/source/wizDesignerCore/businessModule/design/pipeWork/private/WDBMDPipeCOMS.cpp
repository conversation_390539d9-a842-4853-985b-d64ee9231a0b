#include "WDBMDPipeCOMS.h"

#include "WDBMDPipeBRAN.h"
#include "../../../../WDCore.h"
#include "../../../../geometry/WDGeometryMgr.h"
#include "../WDBMDPipeUtils.h"
#include "../../../catalog/WDBMCatalog.h"


WD_NAMESPACE_BEGIN

/**
 * 套件节点 法兰1-垫片1-阀门-垫片2-阀门2。
 * 第1个数组存放阀门向前的管件[垫片1， 法兰2]；
 * 第2个数据存放阀门向后的管件[垫片2，法兰2]
*/
using Kit = std::pair<std::vector<WD::WDNode::SharedPtr>, std::vector<WD::WDNode::SharedPtr> >;
// 根据某一个节点，获取组成套件的节点 前面的节点集。后面的节点，从中心开始，向两端下标递增
static Kit GetPipeComponentKits(WD::WDNode& node)
{
    static constexpr const char* strVALV = "VALV";
    static constexpr const char* strINST = "INST";
    static constexpr const char* strPCOM = "PCOM";
    static constexpr const char* strFILT = "FILT";
    static constexpr const char* strFLAN = "FLAN";
    static constexpr const char* strGASK = "GASK";
    static constexpr const char* strFBLI = "FBLI";

    // 套件样式
    using PrevsNexts = std::pair<std::vector<std::string_view>, std::vector<std::string_view> >;
    static std::map<std::string_view, PrevsNexts> tMap = {
        // FLAN-GASK-VALV-GASK-FLAN
        {strVALV, PrevsNexts({strGASK, strFLAN}, {strGASK, strFLAN})},
        // FLAN-GASK-INST-GASK-FLAN
        {strINST, PrevsNexts({strGASK, strFLAN}, {strGASK, strFLAN})},
        // FLAN-GASK-PCOM-GASK-FLAN
        {strPCOM, PrevsNexts({strGASK, strFLAN}, {strGASK, strFLAN})}
    };

    auto nodeType = node.type();
    auto typeIt = tMap.find(nodeType);
    if (typeIt == tMap.end())
    {
        // node节点不属于VALV/INST/PCOM类型,不存在套件样式
        return {};
    }

    WD::WDNode::SharedPtr pComNode = WD::WDNode::ToShared(&node);
    auto pParent = node.parent();
    if (pParent == nullptr)
        return {};

    // 确定当前管件在子列表中的索引
    int idx = -1;
    const auto& children = pParent->children();
    for (size_t i = 0; i < children.size(); ++i)
    {
        if (children[i] == nullptr)
            continue;
        if (children[i] == pComNode)
        {
            idx = static_cast<int>(i);
            break;
        }
    }
    if (idx == -1)
        return {};

    std::vector<WD::WDNode::SharedPtr> preNodes;
    // 根据索引向前查找
    {
        auto pPrevTypeVec = typeIt->second.first;
        for (int i = idx - 1; i >= 0; --i)
        {
            auto pChild = children[i];
            if (pChild == nullptr)
                continue;

            // 跳过TUBI类型
            if (pChild->isType("TUBI"))
                continue;
            // 获取类型的下标
            int typeIndex = static_cast<int>(preNodes.size());
            if (typeIndex < 0 || typeIndex >= pPrevTypeVec.size())
                break;

            auto destType = pPrevTypeVec[typeIndex];
            if (pChild->isType(destType))
            {
                preNodes.push_back(pChild);
            }
            else
            {
                break;
            }
        }
        if (preNodes.size() != pPrevTypeVec.size())
        {
            // 套件节点不齐全
            preNodes.clear();
            return {};;
        }
    }

    std::vector<WD::WDNode::SharedPtr> nextNodes;
    // 根据索引向后查找
    {
        auto pNextTypeVec = typeIt->second.second;
        for (int i = idx + 1; i < children.size(); ++i)
        {
            auto pChild = children[i];
            if (pChild == nullptr)
                continue;
            // 跳过TUBI类型
            if (pChild->isType("TUBI"))
                continue;

            // 获取类型的下标
            int typeIndex = static_cast<int>(nextNodes.size());
            if (typeIndex < 0 || typeIndex >= pNextTypeVec.size())
                break;

            auto destType = pNextTypeVec[typeIndex];
            if (pChild->isType(destType))
            {
                nextNodes.push_back(pChild);
            }
            else
            {
                break;
            }
        }
        if (nextNodes.size() != pNextTypeVec.size())
        {
            // 套件节点不齐全
            nextNodes.clear();
            return {};;
        }
    }
    return Kit(std::move(preNodes), std::move(nextNodes));
}

/********************************以下6个接口为临时的，后续会用简单的接口直接替换***********************/
/**
* @brief 管件需要对齐的位置
*/
enum AlignPos
{
    // 前
    Forward,
    // 后
    Backward,
};
/**
 * @brief 将component管件的alignPos对齐到gAlignDir方向
*/
static void ComAlignDir(WDNode& component, const DVec3& gAlignDir, AlignPos alignPos)
{
    // 获取管件用来对齐的朝向
    int number = 0;
    switch (alignPos)
    {
    case WD::Forward:
        number = component.getAttribute("Arrive").toInt();
        break;
    case WD::Backward:
        number = component.getAttribute("Leave").toInt();
        break;
    default:
        break;
    }
    auto pPt = component.keyPoint(number);
    if (pPt == nullptr)
        return;
    auto gDir = pPt->transformedDirection(component.globalTransform());
    //计算位置以及朝向
    DVec3   nv0 = gDir.normalized();
    DVec3   nv1 = gAlignDir.normalized();
    DQuat   tQuat;
    //这里旋转计算加入角度误差，误差值未 0.001°
    static constexpr const double AngleErrorValue = 0.001;
    double      tAng = WD::DVec3::Angle(nv0, nv1);
    if (tAng <= AngleErrorValue)
    {
        //俩向量方向相同，不做旋转
        tQuat = WD::DQuat::Identity();
    }
    else if (tAng >= 180 - AngleErrorValue)
    {
        //俩向量反向
        nv1 = -nv0;
        tQuat = WD::DQuat::FromVectors(nv0, nv1);
    }
    else
    {
        tQuat = WD::DQuat::FromVectors(nv0, nv1);
    }
    component.setAttribute("Orientation WRT World", tQuat * component.getAttribute("Orientation WRT World").toQuat());
}
/**
 * @brief 将component管件的alignPos对齐到gAlignPos位置
*/
static void ComAlignPos(WDNode& component, const DVec3& gAlignPos, AlignPos alignPos)
{
    // 获取管件用来对齐的位置
    int number = 0;
    switch (alignPos)
    {
    case WD::Forward:
        number = component.getAttribute("Arrive").toInt();
        break;
    case WD::Backward:
        number = component.getAttribute("Leave").toInt();
        break;
    default:
        break;
    }
    auto pPt = component.keyPoint(number);
    if (pPt == nullptr)
        return;
    auto gPos = pPt->transformedPosition(component.globalTransform());
    auto gOffset = gAlignPos - gPos;
    component.setAttribute("Position WRT World", component.getAttribute("Position WRT World").toDVec3() + gOffset);
}

/**
 * @brief 套件对齐，node模型发生变化后，两端的套件（垫片/法兰）自动对齐到阀门的两端
 * @param core
 * @param node :VALV、INST、PCOM、FILT类型节点
*/
static void AutoAlignKit(WD::WDNode& node)
{
    // 当前节点node参数被修改之后，需要拿到套件中的所有节点，依次根据当前节点的改变做对齐
    auto kites = GetPipeComponentKits(node);
    const auto& boforeNodes = kites.first;
    const auto& afterNodes = kites.second;
    if (boforeNodes.empty() && afterNodes.empty())
        return;

    // 对齐前面的管件
    auto pPtArrive = node.keyPoint(node.getAttribute("Arrive").toInt());
    if (pPtArrive != nullptr)
    {
        auto gPos = pPtArrive->transformedPosition(node.globalTransform());
        auto gDir = pPtArrive->transformedDirection(node.globalTransform());
        for (auto pNode : boforeNodes)
        {
            if (pNode == nullptr)
                continue;

            ComAlignPos(*pNode, gPos, Backward);
            ComAlignDir(*pNode, -gDir, Backward);
            pNode->update();

            // 获取入口点坐标和朝向
            auto pTPtArrive = pNode->keyPoint(pNode->getAttribute("Arrive").toInt());
            if (pTPtArrive != nullptr)
            {
                gPos = pTPtArrive->transformedPosition(pNode->globalTransform());
                gDir = pTPtArrive->transformedDirection(pNode->globalTransform());
            }
        }
    }
    // 对齐后面的管件
    auto pPtLeave = node.keyPoint(node.getAttribute("Leave").toInt());
    if (pPtLeave != nullptr)
    {
        auto gPos = pPtLeave->transformedPosition(node.globalTransform());
        auto gDir = pPtLeave->transformedDirection(node.globalTransform());
        for (auto pNode : afterNodes)
        {
            if (pNode == nullptr)
                continue;
            ComAlignPos(*pNode, gPos, Forward);
            ComAlignDir(*pNode, -gDir, Forward);
            pNode->update();

            // 获取入口点坐标和朝向
            auto pTPtLeave = pNode->keyPoint(pNode->getAttribute("Leave").toInt());
            if (pTPtLeave != nullptr)
            {
                gPos = pTPtLeave->transformedPosition(pNode->globalTransform());
                gDir = pTPtLeave->transformedDirection(pNode->globalTransform());
            }
        }
    }
}

/**
 * @brief 字符串去单位化
 * @param str 字符串
 * @return 去单位化后的字符串
*/
static std::string Deunitization(const std::string& str)
{
    auto    resMm = StringSplit(str, "mm");
    if (resMm.empty())
        return "";
    auto& strMm = resMm.front();
    if (strMm.empty())
        return "";
    auto    resKg = StringSplit(strMm, "kg");
    if (resKg.empty())
        return "";
    auto& strKg = resKg.front();

    return strKg;
}
static void AddParamsToGVars(const StringVector& params, WDBMGVars& gVars)
{
    char buf[1024] = { 0 };
    for (size_t i = 0; i < params.size(); ++i)
    {
        auto tStr = Deunitization(params.at(i));

        sprintf_s(buf, sizeof(buf), "DESP %d", (int)(i + 1));
        gVars.setValue(std::string(buf), tStr);
    }
}

WDBMDPipeCOMS::WDBMDPipeCOMS(WDNode& node)
    : WDBDBase(node)
    , _modelHelpter(*this)
{
}
WDBMDPipeCOMS::~WDBMDPipeCOMS()
{
}
void WDBMDPipeCOMS::RegistAttribute(WDBMTypeDesc& typeDesc)
{
    auto& core = typeDesc.core();
    // 管件描述  "PipeComponentDescription"
    // 经过讨论(客户有这个需求)，我们需要将管件描述属性引入进来(PXXS没有这个属性，只能通过命令行去查)
    typeDesc.add("PipeComponentDescription"
        , WDBMAttrValueType::T_String
        , [&core](const WDNode& node) -> std::string
        {
            auto pBase = node.getBDBase();
            if (pBase == nullptr)
                return "";

            auto ret = GetPipeComponentDescription(core, node);
            return ret;
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::F_ReadOnly);
    // 保温参数  "Iparam" (直径，半径)
    // 将保温参数属性引入进来(PXXS中这个属性是隐藏属性，也是动态属性 根据打开的情况实时获取的，可通过命令行去查)
    typeDesc.add("Iparam"
        , WDBMAttrValueType::T_DoubleVector
        , [&core](const WDNode& node) -> std::vector<double>
        {
            // node计算保温参数
            auto rValue = WDBMDPipeUtils::GetPipeComponentInsulation(core, node);
            if (rValue)
                return rValue.value();
            else
                return std::vector<double>();
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::Flags(WDBMAttrDesc::F_Hidden | WDBMAttrDesc::F_Update));
    // 当高度和设计参数属性被设置后，如果是套件，需要偏移套件中的当前管件以及当前管件后的套件中的所有管件
    {
        auto funcKitsOffAttrSetAfter = [](WDNode& node
            , const WDBMAttrValue& value
            , const WDBMAttrValue& prevValue
            , const WDBMAttrDesc& sender)
            {
                WDUnused(value);
                WDUnused(prevValue);
                WDUnused(sender);
                node.updateModel();
                AutoAlignKit(node);
            };
        std::vector<std::string> kitsOffAttrs = { "Desparam", "Height" };
        for (const auto& kitsOffAttr : kitsOffAttrs)
        {
            auto pKitsOffAttrDesc = typeDesc.get(kitsOffAttr);
            if (pKitsOffAttrDesc == nullptr)
            {
                assert(false);
                continue;
            }
            pKitsOffAttrDesc->functionValueSetAfter() = funcKitsOffAttrSetAfter;
        }
    }
    // 公称直径属性
    typeDesc.add("Abore", WDBMAttrValueType::T_Double
        , [](const WDNode& node)
        {
            auto pKeyPt = node.keyPoint(node.getAttribute("Arrive").toInt());
            if (pKeyPt == nullptr)
                return 0.0;
            bool bOk = false;
            double rBore = FromString<double>(pKeyPt->bore(), &bOk);
            if (!bOk)
                return 0.0;
            return rBore;
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::Flag(WDBMAttrDesc::F_ReadOnly | WDBMAttrDesc::F_Hidden));
    // 公称直径属性
    typeDesc.add("Lbore", WDBMAttrValueType::T_Double
        , [](const WDNode& node)
        {
            auto pKeyPt = node.keyPoint(node.getAttribute("Leave").toInt());
            if (pKeyPt == nullptr)
                return 0.0;
            bool bOk = false;
            double rBore = FromString<double>(pKeyPt->bore(), &bOk);
            if (!bOk)
                return 0.0;
            return rBore;
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , WDBMAttrDesc::Flag(WDBMAttrDesc::F_ReadOnly | WDBMAttrDesc::F_Hidden));
    // Aodiam 属性, 入口点外径
    typeDesc.add("Aodiam"
        , WDBMAttrValueType::T_Double
        , [&core](const WDNode& node)
        {
            auto pParent = node.parent();
            if (pParent == nullptr || !pParent->isType("BRAN"))
                return WDBMAttrValue(0.0);
                
            WDNode::SharedPtr pPrevCom = nullptr;
            for (auto pNode : pParent->children()) 
            {
                if (pNode == nullptr)
                    continue;
                if (!WDBMDPipeUtils::IsPipeComponent(*pNode))
                    continue;
                // 找到当前管件了
                if (pNode.get() == &node)
                    break;
                pPrevCom = pNode;
            }
            WDNode::SharedPtr pRefNode = nullptr;
            // 如果存在前一个管件，则获取前一个管件的Lstube
            if (pPrevCom != nullptr)
                pRefNode = pPrevCom->getAttribute("Lstube").toNodeRef().refNode();
            // 如果不存在，则说明是第一个管件，需要从分支上获取Lstube
            else
                pRefNode = pParent->getAttribute("Hstube").toNodeRef().refNode();

            if (pRefNode == nullptr)
                return WDBMAttrValue(0.0);

            // 获取等级引用的元件
            auto pSCOMNode = pRefNode->getAttribute("Catref").toNodeRef().refNode();
            if (pSCOMNode == nullptr)
                return WDBMAttrValue(0.0);
            //默认从第二个参数中获取外径
            const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
            if (vals.size() < 2)
                return WDBMAttrValue(0.0);
            bool bOk = false;
            double rDiam = FromString<double>(vals[1], &bOk);
            if (!bOk)
                return WDBMAttrValue(0.0);
            return WDBMAttrValue(rDiam);
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , { WDBMAttrDesc::F_ReadOnly | WDBMAttrDesc::F_Hidden });
    // Lodiam 属性, 出口点外径
    typeDesc.add("Lodiam"
        , WDBMAttrValueType::T_Double
        , [&core](const WDNode& node)
        {
            auto pRefNode = node.getAttribute("Lstube").toNodeRef().refNode();
            if (pRefNode == nullptr)
                return WDBMAttrValue(0.0);

            // 获取等级引用的元件
            auto pSCOMNode = pRefNode->getAttribute("Catref").toNodeRef().refNode();
            if (pSCOMNode == nullptr)
                return WDBMAttrValue(0.0);
            //默认从第二个参数中获取外径
            const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
            if (vals.size() < 2)
                return WDBMAttrValue(0.0);
            bool bOk = false;
            double rDiam = FromString<double>(vals[1], &bOk);
            if (!bOk)
                return WDBMAttrValue(0.0);
            return WDBMAttrValue(rDiam);
        }
        , WDBMAttrDesc::FunctionSet()
        , ""
        , { WDBMAttrDesc::F_ReadOnly | WDBMAttrDesc::F_Hidden });
}
bool    WDBMDPipeCOMS::RestoreConnection(WDCore& app
    , WDNode::SharedPtr pPipeComponentNode
    , bool autoConnection
    , double spoolDistance)
{
    WDUnused(app);
    if (pPipeComponentNode == nullptr)
        return false;
    // 当前管件所属的分支节点
    WDNode::SharedPtr pBranchNode = pPipeComponentNode->parent();
    if (pBranchNode == nullptr || !pBranchNode->isType("BRAN"))
        return false;
    // 不自动连接，直接设置世界坐标位置与旋转
    if (!autoConnection)
    {
        pPipeComponentNode->setAttribute("Position WRT World", DVec3(0.0));
        pPipeComponentNode->setAttribute("Orientation WRT World", DQuat::Identity());
        pPipeComponentNode->update();
        //更新分支连接
        pBranchNode->triggerUpdate(true);
        return true;
    }
    // 获取管件入口点
    auto    pPtArrive       = pPipeComponentNode->keyPoint(pPipeComponentNode->getAttribute("Arrive").toInt());
    if (pPtArrive == nullptr) 
        return false;

    // 调用管件节点的update，保证管件的 globalTransform 是最新值,以便于接下来的连接计算
    // 不用担心重复调用的情况，因为节点的update内部会检测更新标记
    pPipeComponentNode->update();

    //获取前一个管件
    WDNode::SharedPtr pPrevPipeCom = WDBMDPipeUtils::PrevPipeComponent(pBranchNode, pPipeComponentNode);
    if (pPrevPipeCom == nullptr)
    { //如果分支不存在管件，则将管件 arrive点 默认放到分支起点
        DVec3 gPosLeave     = pBranchNode->getAttribute("Hposition WRT World").toDVec3();
        DVec3 gDirLeave     = pBranchNode->getAttribute("Hdirection WRT World").toDVec3();
        DVec3 lPosArrive    = DVec3(pPtArrive->position);
        DVec3 lDirArrive    = (Vec3::Normalize(DVec3(pPtArrive->direction)));
        //计算位置以及朝向
        DVec3 nv0           = (-lDirArrive).normalized();
        DVec3 nv1           = gDirLeave.normalized();
        
        DQuat tQuat;
        //这里旋转计算加入角度误差，误差值未 0.001°
        static constexpr const double AngleErrorValue = 0.001;
        double tAng     = DVec3::Angle(nv0, nv1);
        if (tAng <= AngleErrorValue)
        {
            //俩向量方向相同，不做旋转
            tQuat = DQuat::Identity();
        }
        else if (tAng >= 180 - AngleErrorValue)
        {
            //俩向量反向
            nv1 = -nv0;
            tQuat = DQuat::FromVectors(nv0, nv1);
        }
        else
        {
            tQuat = DQuat::FromVectors(nv0, nv1);
        }

        DMat3 gROffset = DMat3::FromQuat(tQuat);
        DVec3 gTOffset = -lPosArrive;
        gTOffset = gROffset * gTOffset;
        DVec3 gSPoolOffset = gDirLeave * spoolDistance;
        pPipeComponentNode->setAttribute("Position WRT World", gPosLeave + gSPoolOffset + gTOffset);
        pPipeComponentNode->setAttribute("Orientation WRT World", gROffset.toQuat());
        pPipeComponentNode->update();
    }
    else
    {//如果分支存在管件，则将管件的 arrive点 与前一个管件的 leave点重合
        auto pPtLeave = pPrevPipeCom->keyPoint(pPrevPipeCom->getAttribute("Leave").toInt());
        if (pPtLeave != nullptr)
        {
            DVec3 gPosLeave  = pPtLeave->transformedPosition(pPrevPipeCom->globalTransform());
            DVec3 gDirLeave  = Vec3::Normalize(pPtLeave->transformedDirection(pPrevPipeCom->globalRSTransform()));
            DVec3 lPosArrive = DVec3(pPtArrive->position);
            DVec3 lDirArrive = (Vec3::Normalize(DVec3(pPtArrive->direction)));
            //计算位置以及朝向
            DVec3 nv0        = (-lDirArrive).normalized();
            DVec3 nv1        = gDirLeave.normalized();
            
            DQuat tQuat;
            //这里旋转计算加入角度误差，误差值未 0.001°
            static constexpr const double AngleErrorValue = 0.001;
            double tAng     = DVec3::Angle(nv0, nv1);
            if (tAng <= AngleErrorValue)
            {
                //俩向量方向相同，不做旋转
                tQuat = DQuat::Identity();
            }
            else if (tAng >= 180 - AngleErrorValue)
            {
                //俩向量反向
                nv1 = -nv0;
                tQuat = DQuat::FromVectors(nv0, nv1);
            }
            else
            {
                tQuat = DQuat::FromVectors(nv0, nv1);
            }

            DMat3 gROffset = DMat3::FromQuat(tQuat);
            DVec3 gTOffset = -lPosArrive;
            gTOffset = gROffset * gTOffset;
            DVec3 gSPoolOffset = gDirLeave * spoolDistance;
            //
            DVec3 gT = pPipeComponentNode->globalTranslation();
            pPipeComponentNode->setAttribute("Position WRT World", gPosLeave + gSPoolOffset + gTOffset);
            pPipeComponentNode->setAttribute("Orientation WRT World", gROffset.toQuat());
            pPipeComponentNode->update();
        }
    }
    //更新分支连接
    pBranchNode->triggerUpdate(true);
    return true;
}

WDBDBase* WDBMDPipeCOMS::clone(WDNode& node) const
{
    auto p = new WDBMDPipeCOMS(node);
    p->copy(*this);
    return p;
}
void WDBMDPipeCOMS::copy(const WDBDBase& src)
{
    if (this == &src)
        return;

    const WDBMDPipeCOMS* pSrc = dynamic_cast<const WDBMDPipeCOMS*>(&src);
    if (pSrc == nullptr)
        return;
    
    WDBDBase::copy(src);

    this->_modelHelpter.copy(pSrc->_modelHelpter);
}

const WDSelectionInterface* WDBMDPipeCOMS::selectionSupporter() const
{
    return &_modelHelpter;
}
WDGraphableInterface* WDBMDPipeCOMS::graphableSupporter()
{
    return &_modelHelpter;
}

void WDBMDPipeCOMS::mData(WDCore& core, WDBMDModelHelpter::MData& data)
{
    auto pSprefNode = this->node().getAttribute("Spref").toNodeRef().refNode();
    if (pSprefNode != nullptr)
        return;
    // 管子等级如果为空，则不生成球体模型
    auto pLstubeNode = this->node().getAttribute("Lstube").toNodeRef().refNode();
    if (pLstubeNode == nullptr)
        return;
    auto pSCOMNode = pLstubeNode->getAttribute("Catref").toNodeRef().refNode();
    if (pSCOMNode == nullptr)
        return;
    //默认从第一个参数中获取公称直径
    const auto vals = pSCOMNode->getAttribute("Param").toStringVector();
    if (vals.size() < 1)
        return;
    bool bOk = false;
    float rBore = FromString<float>(vals[0], &bOk);
    if (!bOk)
        return;
    // 创建基本体
    WDGeometry::GFlag flags = WDGeometry::GFlag::GF_Entity;
    auto pSphere = core.geometryMgr().createSphere(rBore * 2.0f, flags);
    if (pSphere == nullptr)
        return;
    // 添加到模型中
    data.geoms.push_back(pSphere);
    // 生成关键点 P1~PN点
    auto keyPts = pSphere->getKeyPoints();
    data.keyPoints.insert(data.keyPoints.end(), keyPts.begin(), keyPts.end());
}
WDBMNodeRef WDBMDPipeCOMS::mSpref() const
{
    return this->node().getAttribute("Spref").toNodeRef();
}
void WDBMDPipeCOMS::mGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);

    //元件库全局变量
    gVars.setValue<double>("DDANGLE", this->node().getAttribute("Angle").toDouble());
    gVars.setValue<double>("DDHEIGHT", this->node().getAttribute("Height").toDouble());
    gVars.setValue<double>("DDRADIUS", this->node().getAttribute("Radius").toDouble());
    gVars.setValue<double>("IPARAM1", 0.0);

    // 设计参数
    auto desParams = this->node().getAttribute("Desparam").toStringVector();
    AddParamsToGVars(desParams, gVars);

}
bool WDBMDPipeCOMS::mInsuGVars(WDCore& core, WDBMGVars& gVars) const
{
    WDUnused(core);
    WDNode& node = this->node();

    double insulation = 0.0;
    if (this->node().getAttribute("Ispec").toNodeRef().valid())
    {
        // node计算保温参数
        auto value = node.getAttribute("Iparam").toDoubleVector();
        if (value.size() != 0)
            insulation = value.at(0);
    }

    // 保温半径小于0时，不用生成保温模型
    if (insulation > NumLimits<float>::Epsilon)
    {
        gVars.setValue<double>("IPARAM1", insulation);
        return true;
    }
    else
    {
        return false;
    }
}

void WDBMDPipeCOMS::onCollectGVars(WDCore& core, WDBMGVars& outGVars, bool bInsu)
{
    this->mGVars(core, outGVars);
    if (bInsu)
        this->mInsuGVars(core, outGVars);
}

/**
 * @brief 更新管件的管子引用, 会用管件所属管道等级和自身的出口管径去查找一个管子等级并设置为自身的管子引用
 * -info 如果没有查找到对应的管子等级，则不修改自身管子等级引用（这里理论上需要置空，但为了和PXXS保持一致所以也不修改）
 * @param node 管件节点
 */
void PipeComsRefUpdate(WD::WDNode& node)
{
    // 根据管件自身的pSpec等级和管件自身出口管径属性设置管件自身的管子等级
    auto spRef = node.getAttribute("Spref").toNodeRef();
    if (!spRef.valid())
        return ;
    // 管件所属管道等级
    auto pSpco = spRef.refNode();
    if (pSpco == nullptr)
        return ;
    auto pSpec = pSpco;
    while (pSpec != nullptr)
    {
        if (pSpec->isType("SPEC"))
            break;
        pSpec = pSpec->parent();
    }
    if (pSpec == nullptr)
        return ;

    // 出口点
    auto pPtLeave = node.keyPoint(node.getAttribute("Leave").toInt());
    if (pPtLeave == nullptr)
        return ;
    // 获取管子等级
    auto pStube = WDBMDPipeUtils::GetStubeBySPEC(pSpec, pPtLeave->bore());
    // 设置当前管子等级
    if (pStube != nullptr)
        node.setAttribute("Lstube", WDBMNodeRef(pStube));
}

void WDBMDPipeCOMS::onModelUpdate(WDCore& core, WDNode& node)
{
    assert(this->node().getAttribute("Arrive").toInt() > 0
        && this->node().getAttribute("Leave").toInt() > 0);
    // 重新更新模型
    _modelHelpter.updateModel(core);
    // 更新管子引用, 当管件的管子等级为空时, 会用分支上的管道等级和自身的出口管径去查找一个管子等级并设置为自身的管子引用
    PipeComsRefUpdate(node);
}


std::string GetPipeComponentDescription(WDCore& core
    , const WDNode& dNode)
{
    // 获取管件描述（spco->detref->Rtext属性值和spco->Matxt->Xtext属性值）
    auto pSPCONode = dNode.getAttribute("Spref").toNodeRef().refNode();
    if (pSPCONode == nullptr)
        return "";

    // 获取属性获取对象
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet(dNode);
    std::string rText;
    // 文档节点 
    auto pDetRefNode = pSPCONode->getAttribute("Detref").toNodeRef().refNode();
    if (pDetRefNode != nullptr)
        rText = aGet.getAttribute(*pDetRefNode, "Rtext").convertToString();
    // 材料文本节点
    auto pMatxtRefNode = pSPCONode->getAttribute("Matxt").toNodeRef().refNode();
    std::string xText;
    if (pMatxtRefNode != nullptr)
        xText = aGet.getAttribute(*pMatxtRefNode, "Xtext").convertToString();

    if (!rText.empty() && !xText.empty())
        return rText + " " + xText;

    return rText + xText;
}

WD_NAMESPACE_END