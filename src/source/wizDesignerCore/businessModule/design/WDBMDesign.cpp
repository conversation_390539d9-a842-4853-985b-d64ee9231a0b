#include "WDBMDesign.h"
#include "../typeMgr/WDBMTypeMgr.h"
#include "../typeMgr/private/WDBMTypeRegistHelpter.h"
#include "../typeMgr/WDBMAttrEnumDictionary.h"
#include "../WDBMAuditObjectMgr.h"
#include "../WDBMRefCollector.h"
#include "../WDBMColorTable.h"

#include "pipeWork/private/WDBMDPipeBRAN.h"
#include "pipeWork/private/WDBMDPipeTUBI.h"
#include "pipeWork/private/WDBMDPipePIPCA.h"
#include "pipeWork/private/WDBMDPipeCOMS.h"
#include "pipeWork/WDBMDPipeUtils.h"
#include "auxiliaryLine/private/WDBMDAuxiSub.h"
#include "equipment/private/WDBMDEquiSub.h"
#include "equipment/private/WDBMDEquiPriBase.h"
#include "equipment/private/WDBMDEquiPOLYHEDRON.h"
#include "structures/private/WDBMDFittSub.h"
#include "structures/private/WDBMDFloorSub.h"
#include "structures/private/WDBMDPaneSub.h"
#include "structures/private/WDBMDSctnSub.h"
#include "structures/private/WDBMDWallSub.h"
#include "structures/private/WDBMDDatumSub.h"
#include "support/private/WDBMDSuppSub.h"
#include "WDCore.h"
#include "../../common/WDDeviation.h"
#include "2DShapes/private/WDBMD2DShapes.h"
#include "../catalog/WDBMCatalog.h"

WD_NAMESPACE_BEGIN;

// 设备
void RegistEquiObjects(WDBMDesign& bmDesi)
{
    // 设备模块 管嘴
    {
        auto pTypeDesc = bmDesi.typeMgr().get("NOZZ");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDEquiNOZZ(node);
                });
            // 注册其他属性
            WDBMDEquiNOZZ::RegistAttribute(*pTypeDesc);
        }
    }
    // 设备标准基本体
    {
        std::vector<std::string> types = { "BOX", "NBOX", "CTOR", "NCTO", "CONE", "NCON", "CYLI", "NCYL"
            , "DISH", "NDIS", "ELPS", "NELP", "EXTR", "NEXT", "PYRA", "NPYR", "RTOR", "NRTO", "REVO", "NREV"
            , "SLCY", "NSLC", "SNOU", "NSNO", "SPHE", "NSPH", "POHE" };
        for (const auto& type : types)
        {
            auto pTypeDesc = bmDesi.typeMgr().get(type);
            assert(pTypeDesc != nullptr);
            if (pTypeDesc == nullptr)
                continue;

            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDEquiPrisBase(node);
                });
        }
    }
    // 多面体
    {
        std::vector<std::string> types = { "POLYHEDRON", "NPOLYHEDRON" };
        for (const auto& type : types)
        {
            auto pTypeDesc = bmDesi.typeMgr().get(type);
            assert(pTypeDesc != nullptr);
            if (pTypeDesc == nullptr)
                continue;
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDEquiPolyhedronBase(node);
                });
        }
    }
    // 二维图形（暂定）
    {
        auto pTypeDesc = bmDesi.typeMgr().get("POLYLINE2D");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new PolyLine2D(node);
                });
        }
        pTypeDesc = bmDesi.typeMgr().get("PATH2D");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new Path2D(node);
                });
        }
        pTypeDesc = bmDesi.typeMgr().get("RECT2D");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new Rect2D(node);
                });
        }
        pTypeDesc = bmDesi.typeMgr().get("CIRCLE2D");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new Circle2D(node);
                });
        }
        pTypeDesc = bmDesi.typeMgr().get("ELLIPSE2D");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new Ellipse2D(node);
                });
        }
        pTypeDesc = bmDesi.typeMgr().get("TEXT2D");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new Text2D(node);
                });
        }
    }
}
// 管道
void RegistPipeObjects(WDBMDesign& bmDesi) 
{
    // 管道模块 分支
    {
        auto pTypeDesc = bmDesi.typeMgr().get("BRAN");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDPipeBRAN(node);
                });
        }
    }
    // 管道模块 直管
    {
        auto pTypeDesc = bmDesi.typeMgr().get("TUBI");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDPipeTUBI(node);
                });
            // 注册其他属性
            WDBMDPipeTUBI::RegistAttribute(*pTypeDesc);
        }
    }
    // 管道模块 管件
    {
        std::vector<std::string> types = { "GASK", "FLAN", "REDU", "VALV", "ELBO", "ATTA","TEE", "INST"
            , "WELD", "BEND", "CAP", "CLOS", "COUP", "CROS", "FTUB", "OLET", "PCOM", "DUCT", "LJSE"
            , "FILT", "TRAP", "UNIO", "VFWA", "VTWA", "SHU", "VENT", "HELE", "INSU", "TRAC", "TUBE"
            , "AHU", "BATT", "COWL", "DAMP", "FLEX", "GRIL", "HACC", "HFAN", "HSAD", "IDAM", "MESH"
            , "OFST", "PLAT", "PLEN", "SILE", "SKIR", "SPLR", "STRT", "TAPE", "THRE", "TP", "TRNS"
            , "BRCO", "FBLI", "STIF", "PTAP" };
        for (const auto& type : types)
        {
            auto pTypeDesc = bmDesi.typeMgr().get(type);
            assert(pTypeDesc != nullptr);
            if (pTypeDesc == nullptr)
                continue;
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDPipeCOMS(node);
                });
            // 注册其他属性
            WDBMDPipeCOMS::RegistAttribute(*pTypeDesc);
        }
    }
    // 管道模块 PIPCA 管道分接点
    {
        auto pTypeDesc = bmDesi.typeMgr().get("PIPCA");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDPipePIPCA(node);
                });
        }
    }
}
// 结构
void RegistStructuresObjects(WDBMDesign& bmDesi)
{
    //墙
    {
        auto pTypeDesc = bmDesi.typeMgr().get("STWALL");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDWallSTWALL(node);
            });
        }
    }
    // 环形墙
    {
        auto pTypeDesc = bmDesi.typeMgr().get("WALL");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDWallWALL(node);
            });
        }
    }
    // 自定义墙
    {
        auto pTypeDesc = bmDesi.typeMgr().get("GWALL");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDWallGWALL(node);
            });
        }
    }
    // (复合)板附件
    {
        std::vector<std::string> types = { "PFIT", "CMPF"};
        for (const auto& type : types)
        {
            auto pTypeDesc = bmDesi.typeMgr().get(type);
            assert(pTypeDesc != nullptr);
            if (pTypeDesc == nullptr)
                continue;
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDpaneFittingBase(node);
            });
        }
    }
    // (复合)附件
    {
        std::vector<std::string> types = { "FITT", "CMFI" };
        for (const auto& type : types)
        {
            auto pTypeDesc = bmDesi.typeMgr().get(type);
            assert(pTypeDesc != nullptr);
            if (pTypeDesc == nullptr)
                continue;
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDFittingBase(node);
            });
        }
    }
    // 子附件
    {
        auto pTypeDesc = bmDesi.typeMgr().get("SBFI");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDFittSBFI(node);
            });
        }
    }
    // 固定件
    {
        auto pTypeDesc = bmDesi.typeMgr().get("FIXING");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDFittFIXING(node);
            });
        }
    }
    // 通用截面类型
    {
        auto pTypeDesc = bmDesi.typeMgr().get("GENSEC");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDStruGENSEC(node);
            });
        }
    }
    // 梁柱
    {
        auto pTypeDesc = bmDesi.typeMgr().get("SCTN");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDSctnSCTN(node);
            });
        }
    }
    // 楼板
    {
        auto pTypeDesc = bmDesi.typeMgr().get("PANE");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDPanePANE(node);
            });
        }
    }
    // 屏幕,是Panel的一种
    {
        auto pTypeDesc = bmDesi.typeMgr().get("SCREED");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDPaneSCREED(node);
            });
        }
    }
    //面
    {
        auto pTypeDesc = bmDesi.typeMgr().get("FLOOR");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
            {
                return new WDBMDFloorFLOOR(node);
            });
        }
    }
    // DATUM
    {
        auto pTypeDesc = bmDesi.typeMgr().get("DATUM");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDDatum(node);
                });
        }
    }
}
// 支吊架
void RegistSuppObjects(WDBMDesign& bmDesi) 
{
    // 支吊架，支吊件
    {
        std::vector<std::string> types = { "PCLA", "HELE", "SCLA", "HNUT", "HROD", "TRNB", "WPAD", "VSPR"
            , "CLEV", "HPIN", "SLUG", "SHOE", "UBOLT", "BWLD", "EYRD", "RCPL", "SNUB" };
        for (const auto& type : types)
        {
            auto pTypeDesc = bmDesi.typeMgr().get(type);
            assert(pTypeDesc != nullptr);
            if (pTypeDesc == nullptr)
                continue;
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDSuppComs(node);
                });
        }
    }
}
// 轴网
void RegistAuxiObjects(WDBMDesign& bmDesi)
{
    //轴网
    {
        auto pTypeDesc = bmDesi.typeMgr().get("GRIDSY");
        assert(pTypeDesc != nullptr);
        if (pTypeDesc != nullptr)
        {
            // 设置创建函数
            pTypeDesc->setCreateFuncion([](WDNode& node)->WDBDBase*
                {
                    return new WDBMDAuxiGRIDSY(node);
                });
        }
    }
}
// 注册设计模块所有需要创建模型的节点
void RegistObjects(WDBMDesign& bmDesi)
{
    RegistEquiObjects(bmDesi);
    RegistPipeObjects(bmDesi);
    RegistStructuresObjects(bmDesi);
    RegistSuppObjects(bmDesi);
    RegistAuxiObjects(bmDesi);
}

WDBMDesign::WDBMDesign(WDCore& core)
    :WDBMBase(core, "Design")
{
}
WDBMDesign::~WDBMDesign()
{
}

bool WDBMDesign::nameExists(const std::string_view& name) const
{
    if (name.empty())
        return false;
    if (WDBMBase::nameExists(name))
        return true;

    auto pCataRootNode = core().getBMCatalog().root();
    if (pCataRootNode == nullptr)
        return false;
    bool bExists = false;
    WDNode::RecursionHelpterR(*pCataRootNode, [&name, &bExists](WDNode& node)
    {
        if (!node.isNamed())
            return false;
        if (node.srcName() != name)
            return false;
        bExists = true;
        return true;
    });
    return bExists;
}

std::set<std::string_view> WDBMDesign::nameExists(const std::vector<std::string>& names)const
{
    std::set<std::string_view> exitNames;

    std::set<std::string> allNames(names.begin(), names.end());

    auto func = [&allNames, &exitNames](WDNode& node) ->bool
    {
        if (allNames.empty())
            return true;

        if (!node.isNamed())
            return false;

        const auto& nodeName = node.srcName();
        // 判断当前节点名称names中是否存在
        auto itr = allNames.find(nodeName);
        if (itr != allNames.end())
        {
            exitNames.insert(nodeName);
            allNames.erase(itr);
        }
        return allNames.empty();
    };

    auto pDesiRootNode = core().getBMDesign().root();
    if (pDesiRootNode != nullptr)
        WDNode::RecursionHelpterR(*pDesiRootNode, func);

    auto pCataRootNode = core().getBMCatalog().root();
    if (pCataRootNode != nullptr)
        WDNode::RecursionHelpterR(*pCataRootNode, func);

    return exitNames;
}

bool WDBMDesign::initP()
{
    auto& core = this->core();

    // 注册 空间占有属性字典
    this->attrEnumDictMgr().get("Obstruction").set({ {"None", int(0)}, {"Soft", int(1)}, {"Hard", int(2)} });

    // 先从配置文件中加载类型配置
    std::string typeNodeCfgFile = std::string(core.dataDirPath()) + std::string("nodeTypeConfig/nodeTypeConfigDesign.xml");
    if (!LoadTypeNodeConfigFromXMLV1(typeNodeCfgFile, this->typeMgr()))
        return false;

    // 再通过代码设置类型配置
    RegistObjects(*this);

    // 绑定误差配置项回调
    WDDeviation::BindConfigItem(core, "pipe.connectionMaxAngle");

    // 初始化颜色对象表
    std::string colorTableCfgFile = std::string(core.dataDirPath()) + std::string("colorTableDesign.xml");
    this->colorTable().fromXml(colorTableCfgFile.c_str());

    return true;
}
WD::WDNode::SharedPtr WDBMDesign::createRoot()
{
    // 创建默认的根节点
    auto pDesiRoot = this->create("WORL", "设计根节点");
    if (pDesiRoot != nullptr)
    {
        // 根节点用一个固定的uuid
        pDesiRoot->setUuid(WDUuid::FromString("8CCF6FB2-2922-4B5A-B64D-A9A06497F9B1"));
        // 移除根节点的编辑属性
        pDesiRoot->setFlags(pDesiRoot->flags().removeFlags( WDNode::F_Editted, WDNode::F_EdittedStatus, WDNode::F_Created));
    }
    return  pDesiRoot;
}
void WDBMDesign::uninitP()
{

}

std::vector<std::string> WDBMDesign::collectTranslationFiles() const
{
    const char* dataPath = this->core().dataDirPath();

    std::vector<std::string> rFiles;
    rFiles.reserve(2);

    char buf[1024] = { 0 };
    sprintf_s(buf, sizeof(buf), "%s/translate/zh_cn/Design/Type.xml", dataPath);
    rFiles.push_back(buf);
    sprintf_s(buf, sizeof(buf), "%s/translate/zh_cn/Design/Attribute.xml", dataPath);
    rFiles.push_back(buf);
    sprintf_s(buf, sizeof(buf), "%s/translate/zh_cn/Design/EnumDictionaryKey.xml", dataPath);
    rFiles.push_back(buf);

    return rFiles;
}

void WDBMDesign::onNodeDestroyAfter(WDNode::SharedPtr pParentNode) const
{
    if (pParentNode == nullptr)
        return;
    // 如果是分支/管件/直管节点的子节点被移除，则触发其更新
    if (pParentNode->isType("BRAN")
        || pParentNode->isType("TUBI")
        || WDBMDPipeUtils::IsPipeComponent(*pParentNode))
    {
        pParentNode->triggerUpdate(true);
    }
}
void WDBMDesign::onNodeSetParentAfter(WDNode::SharedPtr pParentNode) const
{
    if (pParentNode == nullptr)
        return;
    // 如果是分支/管件/直管节点的子节点被移除，则触发其更新
    if (pParentNode->isType("BRAN")
        || pParentNode->isType("TUBI")
        || WDBMDPipeUtils::IsPipeComponent(*pParentNode))
    {
        pParentNode->triggerUpdate(true);
    }
}

void WDBMDesign::updateNodeRef(WDBMRefUpdater& collector) const
{
    // 更新几何对象引用(非参数化)
    collector.updateDGeoRefs(*this);
    // 更新元件库节点引用,设计模块的节点会引用到元件库的节点(例如：引用元件的等级节点)
    collector.updateCRefs(this->core().getBMCatalog());
    // 更新设计模块节点引用
    collector.updateDRefs(*this);
}




WD_NAMESPACE_END

