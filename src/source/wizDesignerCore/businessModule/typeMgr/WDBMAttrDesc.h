#pragma     once

#include "WDBMAttrValue.h"

WD_NAMESPACE_BEGIN

class WDBMBase;
class WDBMAttrEnumDict;
/**
* @brief 业务数据节点属性描述
*/
class WD_API WDBMAttrDesc
{
public:
    // 默认的浮点数精度
    static constexpr const int DefaultDecimals = 2;
public:
    /**
     * @brief 属性标志
    */
    enum Flag
    {
        // 无
        F_None      = 0,
        // 只读标识, 表示当前属性只读; 如果不带有该标志, 表示当前属性可读可写
        F_ReadOnly  = 1 << 0,
        // 隐藏属性标志, 带有该标志时, 表示当前属性默认不提供显示;
        F_Hidden    = 1 << 1,
        // 更新标志, 如果带有该标志的属性被修改，则需要自动给节点添加WDNode::Flag_Update, 以保证节点接下来能够正常地更新计算变换以及模型数据
        F_Update    = 1 << 2,
    };
    using Flags = WDFlags<Flag, uint>;
	/**
	 * @brief 属性值
	*/
	using Value = WDBMAttrValue;
	/**
	 * @brief 属性值的获取方法
	*/
	using FunctionGet = std::function<Value(const WDNode& node)>;
	/**
	 * @brief 属性值的设置方法
	*/
	using FunctionSet = std::function<bool(WDNode& node, const Value& value)>;

    /**
     * @brief 属性的数据标志, 针对不同类型数据的一些额外标志说明
     */
    enum DataFlag 
    {
        // 无
        DF_None = 0,
        // 设计模块引用属性的连接标志，有该标志表明当前属性是一个设计模块的连接属性
        DF_DConnections = 1 << 0,
    };
    using DataFlags = WDFlags<DataFlag, uint>;
public:
    /**
     * @brief 构造
     * @param name 属性名称
     * @param type 属性的值类型, 必须指定且有效
     * @param sampleName 属性的简称, 选择指定
     * @param id 属性id, 由外部指定
    */
    WDBMAttrDesc(const std::string& name
        , WDBMAttrValueType type
        , const std::string& sampleName = ""
        , const Flags& flags = Flag::F_None
        , ushort id = NumLimits<ushort>::Max);
    /**
     * @brief 构造
     * @param name 属性名称
     * @param type 属性的值类型, 必须指定且有效
     * @param fGet 属性值的获取回调, 由添加者自定义
     * @param fSet 属性值的设置回调, 由添加者自定义, 可以指定为空，如果为空时表示属性不能被设置(将默认添加只读标志)
     * @param sampleName 属性的简称, 选择指定
     * @param id 属性id, 由外部指定
    */
    WDBMAttrDesc(const std::string& name
        , WDBMAttrValueType type
        , const FunctionGet& fGet
        , const FunctionSet& fSet = FunctionSet()
        , const std::string& sampleName = ""
        , const Flags& flags = Flag::F_None
        , ushort id = NumLimits<ushort>::Max);

	WDBMAttrDesc(const WDBMAttrDesc& right) = default;
	WDBMAttrDesc(WDBMAttrDesc&& right) = default;
	WDBMAttrDesc& operator=(const WDBMAttrDesc& right) = default;
	WDBMAttrDesc& operator=(WDBMAttrDesc&& right) = default;
    virtual ~WDBMAttrDesc();
public:
    /**
     * @brief 自定义属性前缀
    */
    static constexpr char CustomAttrNamePrefix = ':';
    /**
     * @brief 指定属性名称，判断是否自定义属性
    */
    static constexpr bool IsCustom(const std::string_view& name)
    {
        if (name.empty())
            return false;
        return name[0] == CustomAttrNamePrefix;
    }
public:
    /**
    * @brief 是否有效
    */
    inline bool valid() const;

    /**
     * @brief 获取类型Id
    */
    inline ushort id() const
    {
        return _id;
    }

    /**
     * @brief 获取属性值的类型
    */
    inline const WDBMAttrValueType& type() const;
    /**
     * @brief 获取属性值的类型
    */
    inline void setType(const WDBMAttrValueType& type);

    /**
     * @brief 获取属性名称
    */
    inline const std::string& name() const;
    /**
     * @brief 设置属性名称
    */
    inline void setName(const std::string& name);

    /**
    * @brief 获取属性简称
    */
    inline const std::string& sampleName() const;
    /**
     * @brief 设置属性简称
    */
    inline void setSampleName(const std::string& sampleName);

    /**
     * @brief 是否自定义属性
    */
    inline bool isCustom() const;

    /**
     * @brief 获取属性类别
    */
    inline const std::string& category() const;
    /**
     * @brief 设置属性类别
    */
    inline void setCategory(const std::string& category);

    /**
     * @brief 获取属性标志
    */
    inline const Flags& flags() const;
    /**
     * @brief 获取属性标志
    */
    inline Flags& flags();

    /**
     * @brief 设置属性的默认值
     * @return 是否设置成功,如果指定的值类型与属性的值类型不匹配，则设置失败
    */
    bool setDefaultValue(const Value& value);
    /**
     * @brief 获取属性的默认值
    */
    inline const Value& defaultValue() const;
    /**
     * @brief 设置属性最小值(数值类型有效: int, double)
    */
    bool setMinimumValue(const Value& minimumValue);
    /**
     * @brief 获取属性的最小值(数值类型有效: int, double)
    */
    inline const Value& minimumValue() const;
    /**
     * @brief 设置属性最大值(数值类型有效: int, double)
    */
    bool setMaximumValue(const Value& maximumValue);
    /**
     * @brief 获取属性的最大值(数值类型有效: int, double)
    */
    inline const Value& maximumValue() const;
    /**
     * @brief 设置属性的步长(用于辅助界面调整值, 数值类型有效: int, double)
    */
    bool setSingleStep(const Value& singleStep);
    /**
     * @brief 获取属性的步长(用于辅助界面调整值, 数值类型有效: int, double)
    */
    inline const Value& singleStep() const;
    /**
     * @brief 设置属性的精度(浮点型有效: double)
    */
    inline void setDecimals(int decimals);
    /**
     * @brief 获取属性的精度(浮点型有效: double)
    */
    inline int decimals() const;

    /**
     * @brief 设置正则(字符串类型有效: String, Word, StringVector)
     */
    inline void setRegexp(const std::string& regexp);
    /**
     * @brief 获取正则(字符串类型有效: String, Word, StringVector)
     */
    inline const std::string& regexp() const;

    /**
     * @brief 设置引用属性引用的节点所属的模块名称
     *  指定了有效的模块名称时，更新节点引用节点只会到指定的模块下查询，否则会到所有模块中查询
     *  用于更新节点引用加速，减少不必要的查询
     */
    inline void setRefNodeModuleName(const std::string& moduleName);
    /**
     * @brief 获取引用属性引用的节点所属的模块名称
     */
    inline const std::string& refNodeModuleName() const;
    /**
     * @brief 获取缓存的引用属性引用的节点所属的模块对象
     *  需要初始化之后才能正确获取
     */
    inline const WDBMBase* refNodeModule() const;

    /**
     * @brief 设置 属性引用的枚举字典名称(使用字典项来索引属性的值, 下拉列表)
    */
    inline void setEnumDictionaryName(const std::string& dictName);
    /**
     * @brief 获取 属性引用的枚举字典名称(使用字典项来索引属性的值, 下拉列表)
    */
    inline const std::string& enumDictionaryName() const;
    /**
    * @brief 设置枚举字典对象
    */
    inline void setEnumDict(const WDBMAttrEnumDict*);
    /**
     * @brief 获取缓存的枚举字典对象
     *  需要初始化之后才能正确获取
    */
    inline const WDBMAttrEnumDict* enumDict() const;
    /**
     * @brief 获取数据标志
     */
    inline DataFlags& dataFlags();
    /**
     * @brief 获取数据标志
     */
    inline const DataFlags& dataFlags() const;
public:

    /**
     * @brief 值设置之前通知
     * @param node 节点
     * @param value 设置之后的新值
     * @param preValue 设置之前的旧值
     * @param sender 发送者
     */
    using FunctionValueSetBefore    = std::function<void(WDNode& node
        , const Value& value
        , const Value& prevValue
        , const WDBMAttrDesc& sender)>;
    /**
     * @brief 值设置之后通知
     * @param node 节点
     * @param value 设置之后的新值
     * @param preValue 设置之前的旧值
     * @param sender 发送者
     */
    using FunctionValueSetAfter     = std::function<void(WDNode& node
        , const Value& value
        , const Value& prevValue
        , const WDBMAttrDesc& sender)>;

    /**
     * @brief 值设置之前的通知回调
     */
    inline FunctionValueSetBefore& functionValueSetBefore();
    /**
     * @brief 值设置之后的通知回调
     */
    inline FunctionValueSetAfter& functionValueSetAfter();

    /**
     * @brief 指定节点获取该属性的属性值
     * @return 属性值，如果获取失败，将返回无效的属性值(Value::valid()为false)
    */
    Value value(const WDNode& node) const;
    /**
     * @brief 指定节点设置该属性的属性值
     * @param value 值
     * @return 是否设置成功, 成功返回true, 否则返回false
     *	当属性不具有可写属性或未注册设置函数时，均设置失败
    */
    bool setValue(WDNode& node, const Value& value) const;
    /**
     * @brief 获取值对象的const引用
     * @param node 节点
     */
    WDBMAttrValueCRef valueCRef(const WDNode& node) const;
    /**
     * @brief 获取存放属性值内存的首地址
     *   对性能有极高要求的场景使用，性能略高于 valueCRef(node), 但是由于直接使用 void* ,所以安全性很低
     * @param node 节点
     * @return 存放值内存的首地址, 如果存放属性值内存首地址无效，则返回nullptr
     */
    const void* valueAddr(const WDNode& node) const;
public:
    /**
     * @brief 计算属性值占用的内存大小(字节)
    */
    static size_t Sizeof(const WDBMAttrDesc& attr);
public:
    /**
     * @brief 标志位转换为字符串表示
    */
    static const char* FlagToStr(Flag type);
    /**
     * @brief 字符串表示转换到标志位
    */
    static Flag FlagFromStr(const std::string_view& type);
    /**
     * @brief 数据标志位转换为字符串表示
    */
    static const char* DataFlagToStr(DataFlag type);
    /**
     * @brief 字符串表示转换到数据标志位
    */
    static DataFlag DataFlagFromStr(const std::string_view& type);
private:
    void memberInit();
private:
    // 属性名称
    std::string _name;
    // 属性名称简称
    std::string _sampleName;
    // 属性ID，由类型管理自动分配
    ushort      _id;
    // 属性类别, 可以理解为属性的分组
    std::string _category;
    // 属性值的类型
    WDBMAttrValueType _type;
    // 属性标志
    Flags _flags;

    // 属性的默认值
    Value _defaultValue;
    // 属性的最小值(数值类型有效: int, double)
    Value _minimumValue;
    // 属性的最大值(数值类型有效: int, double)
    Value _maximumValue;
    // 属性的步长(用于辅助界面调整值, 数值类型有效: int, double)
    Value _singleStep;
    // 小数点后保留的位数(浮点型有效: double)
    int _decimals;

    // 属性的正则表达式(字符串类型有效: String, Word, StringVector)
    std::string _regexp;
    // 属性引用的枚举字典名称(使用字典项来索引属性的值, 下拉列表)
    std::string _enumDictName;
    // 如果是引用属性，这里指定属性引用的节点所属的模块名称, 未指定名称时，默认以元件模块处理
    std::string _refNodeModuleName;

    // 缓存的枚举字典对象
    const WDBMAttrEnumDict* _pEnumDict;
    // 缓存的引用属性引用的节点所属的模块对象
    const WDBMBase* _pBMBase;

    // 数据标志
    DataFlags _dataFlags;

    // 值设置之前回调
    FunctionValueSetBefore _funcValueSetBefore;
    // 值设置之后的回调
    FunctionValueSetAfter _funcValueSetAfter;
private:
    //
    friend class WDBMTypeDesc;
    friend class WDBDBase;
    // 内存描述
    struct MemDesc
    {
        // 存放当前属性字段的首地址偏移
        size_t offset = 0;
        // 存放当前属性字段的内存大小
        size_t size = 0;

        MemDesc(size_t offset = 0, size_t size = 0)
            : offset(offset), size(size)
        {
        }
    };
    // 获取/设置函数描述
    struct FGetSetDesc
    {
        // 属性值的获取方法
        FunctionGet fGet;
        // 属性值的设置方法
        FunctionSet fSet;

        FGetSetDesc(const FunctionGet& fGet, const FunctionSet& fSet)
            : fGet(fGet), fSet(fSet)
        {
        }
    };

    // 属性值的存取方式
    using VMethod = std::variant<std::monostate
        , MemDesc           // 属性值在内存中的描述
        , FGetSetDesc>;     // 属性值不用分配内存，因为提供了设置和获取(可以不指定)回调
    VMethod _vMethod;
private:
    // 获取属性字段内存描述对象, 如果属性不需要分配内存，则返回nullptr
    inline MemDesc* getMemDesc()
    {
        return std::get_if<1>(&_vMethod);
    }
    inline const MemDesc* getMemDesc() const
    {
        return std::get_if<1>(&_vMethod);
    }
    // 获取属性获取设置函数的描述对象
    inline FGetSetDesc* getFGetSetDesc()
    {
        return std::get_if<2>(&_vMethod);
    }
    inline const FGetSetDesc* getFGetSetDesc() const
    {
        return std::get_if<2>(&_vMethod);
    }
};

WD_NAMESPACE_END

#include "WDBMAttrDesc.inl"

