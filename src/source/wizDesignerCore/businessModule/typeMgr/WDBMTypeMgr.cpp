#include "WDBMTypeMgr.h"
#include "../../WDCore.h"
#include "../design/WDBMDesign.h"
#include "../WDBMAuditObjectMgr.h"
#include "../WDBDBase.h"

WD_NAMESPACE_BEGIN

// 类型属性名称
static constexpr const char* NodeTypePropertyName   = "Type"; 
// 名称属性名称
static constexpr const char* NodeNamePropertyName   = "Name";
// Id属性名称
static constexpr const char* NodeGuidPropertyName  = "Guid";
// 所属父对象属性名称
static constexpr const char* NodeOwnerPropertyName  = "Owner";
// 锁定标志属性名称
static constexpr const char* NodeLockPropertyName   = "Lock";
// 显隐标志属性名称
static constexpr const char* NodeVisiblePropertyName   = "Visible";
// 状态值属性名称
static constexpr const char* NodeStatusPropertyName = "状态值";
// 参考号属性名称
static constexpr const char* NodeRefNoPropertyName  = "RefNo";
// 实体颜色属性名称
static constexpr const char* NodeAutoColorPropertyName  = "AutoColor";


WDBMTypeMgr::WDBMTypeMgr(WDBMBase& bmBase)
    :_bmBase(bmBase)
{
    _initialized = false;
}
WDBMTypeMgr::~WDBMTypeMgr()
{
    _nameMap.clear();
    while (!_types.empty())
    {
        if (_types.back() != nullptr)
        {
            delete _types.back();
            _types.pop_back();
        }
    }
}

WDBMTypeDesc* WDBMTypeMgr::regist(const std::string_view& typeName)
{
    if (this->initialized()) 
    {
        assert(false && "类型管理已被初始化，不允许再注册类型!");
        return nullptr;
    }
    if (typeName.empty())
    {
        assert(false && "类型名称不能为空!");
        return nullptr;
    }
    if (this->contains(typeName))
    {
        assert(false && "类型已被注册!");
        return nullptr;
    }
    // 通过数组下标生成类型Id
    ushort id = static_cast<ushort>(_types.size());
    // 创建类型描述
    auto pDesc = new WDBMTypeDesc(this->bmBase(), typeName, id);
    // 添加到数组中
    _types.push_back(pDesc);
    // 保存指针到map中,便于快速查找
    _nameMap.insert({ pDesc->name(), pDesc });

    // 1.名称属性
    {
        pDesc->add(NodeNamePropertyName
            , WDBMAttrValueType::T_String
            // 获取方法
            , [](const WDNode& node) ->WDBMAttrValue
            {
                return WDBMAttrDesc::Value(node.name());
            }
            // 设置方法
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pName = value.data<std::string>();
                if (pName != nullptr)
                {
                    node.setName(*pName);
                    return true;
                }
                return false;
            });
    }
    // 2.类型属性
    {
        pDesc->add(NodeTypePropertyName
            , WDBMAttrValueType::T_String
            // 获取方法
            , [=](const WDNode& node) ->WDBMAttrValue
            {
                return WDBMAttrDesc::Value(std::string(node.type()));
            }
            // 设置方法
            , WDBMAttrDesc::FunctionSet()
            // 简称
            , ""
            // 标志
            , WDBMAttrDesc::Flag::F_ReadOnly);
    }
    // 3.Guid(Id)属性
    {
        pDesc->add(NodeGuidPropertyName
            , WDBMAttrValueType::T_Uuid
            // 获取方法
            , [=](const WDNode& node) ->WDBMAttrValue
            {
                return WDBMAttrDesc::Value(node.uuid());
            }
            // 设置方法
            , WDBMAttrDesc::FunctionSet()
            // 简称
            , ""
            // 标志
            , WDBMAttrDesc::Flag::F_ReadOnly);
    }
    // 4.父对象属性
    {
        pDesc->add(NodeOwnerPropertyName
            , WDBMAttrValueType::T_NodeRef
            // 获取方法
            , [=](const WDNode& node) ->WDBMAttrValue
            {
                return WDBMAttrValue(WDBMNodeRef(node.parent()));
            }
            // 设置方法
            , WDBMAttrDesc::FunctionSet()
            // 简称
            , ""
            // 标志
            , WDBMAttrDesc::Flag::F_ReadOnly);
    }
    // 5.锁定属性
    {
        pDesc->add(NodeLockPropertyName
            , WDBMAttrValueType::T_Bool
            // 获取方法
            , [](const WDNode& node) ->WDBMAttrValue
            {
                bool bLock = node.flags().hasFlag(WDNode::F_Lock);
                return WDBMAttrDesc::Value(bLock);
            }
            // 设置方法
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pLockBool = value.data<bool>();
                if (pLockBool != nullptr)
                {
                    auto flags = node.flags();
                    flags.setFlag(WDNode::F_Lock, *pLockBool);
                    node.setFlags(flags);
                    return true;
                }
                auto pLockInt = value.data<int>();
                if (pLockInt != nullptr)
                {
                    auto flags = node.flags();
                    flags.setFlag(WDNode::F_Lock, static_cast<bool>(*pLockInt));
                    node.setFlags(flags);
                    return true;
                }
                return false;
            });
    }
    // 6.可见性属性
    {
        pDesc->add(NodeVisiblePropertyName
            , WDBMAttrValueType::T_Bool
            // 获取方法
            , [](const WDNode& node) ->WDBMAttrValue
            {
                bool bLock = node.flags().hasFlag(WDNode::F_Visible);
                return WDBMAttrDesc::Value(bLock);
            }
            // 设置方法
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pLockBool = value.data<bool>();
                if (pLockBool != nullptr)
                {
                    auto flags = node.flags();
                    flags.setFlag(WDNode::F_Visible, *pLockBool);
                    node.setFlags(flags);
                    return true;
                }
                auto pLockInt = value.data<int>();
                if (pLockInt != nullptr)
                {
                    auto flags = node.flags();
                    flags.setFlag(WDNode::F_Visible, static_cast<bool>(*pLockInt));
                    node.setFlags(flags);
                    return true;
                }
                return false;
            });
    }
    // 7.状态值属性
    {
        auto& auditMgr = Core().getBMDesign().auditObjectMgr();
        if (auditMgr.enabled() && auditMgr.types().find(std::string(typeName)) != auditMgr.types().end())
        {
            pDesc->add(NodeStatusPropertyName
                , WDBMAttrValueType::T_String
                // 获取方法
                , [](const WDNode& node) ->WDBMAttrValue
                {
                    // 获取节点的status
                    auto optStatus = Core().getBMDesign().auditObjectMgr().queryStatus(node.uuid());
                    if (optStatus)
                        return WDBMAttrDesc::Value(optStatus.value());
                    return WDBMAttrDesc::Value(std::string("unset"));
                }
                // 设置方法
                , WDBMAttrDesc::FunctionSet()
                // 简称
                , ""
                // 标志
                , WDBMAttrDesc::Flag::F_ReadOnly
                );
        }
    }
    // 8.PDMS引用号属性名称
    {
        pDesc->add(NodeRefNoPropertyName
            , WDBMAttrValueType::T_String
            // 获取方法
            , [](const WDNode& node) ->WDBMAttrValue
            {
                auto pData = node.getBDBase();
                if (pData == nullptr)
                    return WDBMAttrDesc::Value();
                return WDBMAttrDesc::Value(pData->refNode());
            }
            // 设置方法
            , [](WDNode& node, const WDBMAttrValue& value)->bool
            {
                auto pData = node.getBDBase();
                if (pData == nullptr || value.type() != T_String)
                    return false;
                pData->setRefNo(value.toString());
                return true;
            }
            // 简称
            , ""
            // 标志
            , WDBMAttrDesc::Flag::F_ReadOnly);
    }
    // 9.实体颜色属性名称
    {
        pDesc->add(NodeAutoColorPropertyName
            , WDBMAttrValueType::T_Color
            // 获取方法
            , [](const WDNode& node) ->WDBMAttrValue
        {
            return WDBMAttrDesc::Value(node.basicColor());
        }
            // 设置方法
            , [](WDNode& node, const WDBMAttrValue& value)->bool
        {
            auto pColor = value.data<Color>();
            if (pColor != nullptr)
            {
                node.setBasicColor(*pColor);
                return true;
            }
            return false;
        }
            // 简称
            , ""
            // 标志
            , WDBMAttrDesc::Flags{WDBMAttrDesc::Flag::F_Hidden, WDBMAttrDesc::Flag::F_ReadOnly});
    }
    return pDesc;
}

WDBMTypeDesc* WDBMTypeMgr::get(const std::string_view& typeName) const
{
    auto fItr = _nameMap.find(typeName);
    if (fItr == _nameMap.end())
        return nullptr;
    return fItr->second;
}
WDBMTypeDesc* WDBMTypeMgr::getById(ushort id) const
{
    if (id < _types.size())
        return _types[id];
    return nullptr;
}
const StringVector& WDBMTypeMgr::supportedParentTypes(const std::string_view& typeName) const
{
    static const StringVector sRVector;
    auto pDesc = this->get(typeName);
    if (pDesc == nullptr)
        return sRVector;

    return pDesc->parentTypes();
}
StringVector WDBMTypeMgr::supportedChildTypes(const std::string_view& typeName) const
{
    StringVector rTypes;
    if (typeName.empty())
        return rTypes;

    for (auto pDesc : _types)
    {
        if (pDesc == nullptr)
        {
            assert(false);
            continue;
        }
        if (pDesc->containsParentType(typeName))
        {
            rTypes.push_back(pDesc->name());
        }
    }

    return rTypes;
}
bool WDBMTypeMgr::checkParent(const std::string_view& typeName, const std::string_view& parentTypeName) const
{
    auto pDesc = this->get(typeName);
    if (pDesc == nullptr)
        return false;
    return pDesc->containsParentType(parentTypeName);
}

void WDBMTypeMgr::init()
{
    if (_initialized)
    {
        assert(false && "重复调用了属性描述管理的初始化!");
        return;
    }
    _initialized = true;
    for (auto pType : _types) 
    {
        if (pType == nullptr)
            continue;
        pType->init();
    }
}
WDNode::SharedPtr WDBMTypeMgr::build(WDBMBase& bmBase
    , const std::string_view& typeName
    , const std::string_view& nodeName
    , WDNode::SharedPtr pParentNode
    , WDNode::SharedPtr pNextNode) const
{
    if (!this->initialized())
    {
        assert(false && "类型管理未初始化，不允许创建类型节点!");
        return nullptr;
    }
    if (typeName.empty())
    {
        assert(false && "注意,类型名称为空!");
        return nullptr;
    }
    // 查询类型描述
    const auto pDesc = this->get(typeName);
    if (pDesc == nullptr)
    {
        assert(false && "注意,查找类型描述失败,可能是类型未注册!");
        return nullptr;
    }
    // 校验父类型
    if (pParentNode != nullptr)
    {
        std::string_view parentTypeName = pParentNode->type();
        if (!pDesc->containsParentType(parentTypeName))
        {
            assert(false && "注意,指定的父类型无法挂载当前类型!");
            return nullptr;
        }
    }
    // 创建节点
    auto pNewNode = WDNode::MakeShared();
    // 关联模块对象,设置节点Id
    pNewNode->setType(bmBase, typeName);
    // 创建数据对象
    WDBDBase* pBase = pNewNode->createBD();
    if (pBase == nullptr)
    {
        assert(false && "注意，数据对象创建失败,请检查对象的类型Id!");
        return nullptr;
    }
    // 设置节点名称
    pNewNode->setName(std::string(nodeName));

    // 如果指定了父节点或下一个节点，则处理挂载到父节点
    if (pParentNode == nullptr)
    {
        if (pNextNode != nullptr && pNextNode->parent() != nullptr)
        {
            pNextNode->parent()->insertChild(pNewNode, pNextNode);
        }
    }
    else if (pParentNode != nullptr)
    {
        if (pNextNode != nullptr && pParentNode == pNextNode->parent())
            pParentNode->insertChild(pNewNode, pNextNode);
        else
            pParentNode->addChild(pNewNode);
    }

    return pNewNode;
}
WDBDBase* WDBMTypeMgr::build(WDBMBase& bmBase
    , WDNode& node
    , const std::string_view& typeName) const
{
    if (!this->initialized())
    {
        assert(false && "类型管理未初始化，不允许创建类型节点!");
        return nullptr;
    }
    // 查询类型描述
    const auto pDesc = this->get(typeName);
    if (pDesc == nullptr)
    {
        assert(false && "注意,查找类型描述失败,可能是类型未注册!");
        return nullptr;
    }
    // 关联模块对象,设置节点Id
    node.setType(bmBase, typeName);
    // 创建数据对象
    WDBDBase* pBase = node.createBD();
    if (pBase == nullptr)
    {
        assert(false && "注意，数据对象创建失败,请检查对象的类型Id!");
        return nullptr;
    }

    return pBase;
}



WD_NAMESPACE_END

