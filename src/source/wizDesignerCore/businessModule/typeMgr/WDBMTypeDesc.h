#pragma     once

#include "WDBMAttrDesc.h"

WD_NAMESPACE_BEGIN

class WDCore;
class WDBDBase;
class WDBMBase;

/**
* @brief 业务数据节点类型描述
*/
class WD_API WDBMTypeDesc
{
public:
    /**
     * @brief 类型节点标志
    */
    enum Flag
    {
        // 无
        F_None = 0,
        // 表明类型是由程序自动生成的(而非用户创建的), 这种类型通常可以不用做序列化
        // 例如管道中的直管类型，就是通过分支更新连接生成的，因此直管可以不用参与序列化
        F_AutoGeneration = 1 << 0,
        // 表明类型是否支持触发更新
        // 如果有此标记, 节点调用triggerUpdate()时, 会优先判断是否存在是否存在父节点
        // , 如果存在父节点则调用父节点的triggerUpdate()，否则调用节点自身的update()
        // 如果没有此标记，表示不支持触发更新，调用节点的 triggreUpdate时，等价于调用 update();
        F_TriggerUpdate = 1 << 1,
    };
    using Flags = WDFlags<Flag, uint>;
    /**
     * @brief 属性描述列表
    */
    using AttrDescs         = std::vector<WDBMAttrDesc*>;
    /**
     * @brief 资源(目前是指文件路径)
    */
    using Resource          = std::string;
    /**
     * @brief 资源列表
    */
    using Resources         = std::map<std::string, Resource>;
    /**
     * @brief 类型对象的创建方法
    */
    using FunctionCreate    = std::function<WDBDBase*(WDNode& node)>;
    /**
     * @brief 属性值的获取方法
    */
    using FunctionAttrGet   = WDBMAttrDesc::FunctionGet;
    /**
     * @brief 属性值的设置方法
    */
    using FunctionAttrSet   = WDBMAttrDesc::FunctionSet;
    /**
     * @brief 变换类型, 不同的变换类型会自动注册不同的变换属性
     */
    enum TransformType
    {
        // 无
        TT_None = 0,
        // 使用 Position 计算变换矩阵
        TT_Pos,
        // 使用 Position, Orientation 计算变换矩阵
        TT_PosOri,
        // 使用 Position, Orientation, Scaling 计算变换矩阵
        TT_PosOriScale,
        // 使用 Bangle, Posstart, Posend, Drnstart, Drnend 计算变换矩阵
        TT_SEPosDrnBangle,
        // 使用 Drnstart, Drnend 计算变换矩阵
        TT_SEDrn,
        // 使用 Hposition, Tposition, Hdirection, Tdirection 计算变换矩阵
        TT_HTPosDir,
        // 使用 Hposition, Tposition 计算变换矩阵
        TT_HTPos,
        // 使用 Position, Sjustification, Bangle 计算变换矩阵
        TT_PosSjustBangle,
        // 使用 Delposition, Posline, Zdistance, Bangle 计算变换矩阵
        TT_DelposPoslineZdisBangle,
        // 使用 Delposition, Zdirection, Bangle 计算变换矩阵
        TT_DelposZdirBangle,
    };
public:
    /**
     * @brief 构造
     * @param name 类型名称，必须指定有效名称
     * @param id 类型id
    */
    explicit WDBMTypeDesc(WDBMBase& bmBase, const std::string_view& name, ushort id = NumLimits<ushort>::Max);
    WDBMTypeDesc(const WDBMTypeDesc& right) = default;
    WDBMTypeDesc(WDBMTypeDesc&& right) = default;
    WDBMTypeDesc& operator=(const WDBMTypeDesc& right) = default;
    WDBMTypeDesc& operator=(WDBMTypeDesc&& right) = default;
    ~WDBMTypeDesc();
public:
    /**
     * @brief 获取所属的模块对象
     */
    inline WDBMBase& bmBase() const 
    {
        return _bmBase;
    }
    /**
     * @brief 获取core
     */
    WDCore& core() const;
    /**
    * @brief 是否有效
    */
    inline bool valid() const
    {
        return !_name.empty();
    }
    /**
    * @brief 获取类型名称
    */
    inline const std::string& name() const
    {
        return _name;
    }
    /**
     * @brief 获取类型id
     */
    inline ushort id() const 
    {
        return _id;
    }
    /**
     * @brief 获取类型标志
    */
    inline const Flags& flags() const
    {
        return _flags;
    }
    /**
     * @brief 设置类型标志
    */
    inline void setFlags(const Flags& flags)
    {
        _flags = flags;
    }

    /**
    * @brief 是否根类型
    */
    inline bool isRoot() const
    {
        return _parentTypes.empty();
    }
    /**
     * @brief 获取变换类型
     */
    inline TransformType transformType() const
    {
        return _transformType;
    }
    /**
     * @brief 设置变换类型
     */
    inline void setTransformType(TransformType tType)
    {
        _transformType = tType;
    }
    /**
    * @brief 设置父类型列表
    */
    inline void setParentTypes(const StringVector& parentTypes)
    {
        _parentTypes = parentTypes;
    }
    /**
    * @brief 设置父类型列表
    */
    inline void setParentTypes(StringVector&& parentTypes)
    {
        _parentTypes = std::forward<StringVector>(parentTypes);
    }
    /**
    * @brief 是否包含父类型
    */
    inline bool containsParentType(const std::string_view& parentType) const
    {
        return std::find(_parentTypes.begin(), _parentTypes.end(), parentType) != _parentTypes.end();
    }
    /**
    * @brief 获取父类型列表
    */
    inline const StringVector& parentTypes() const
    {
        return _parentTypes;
    }

    /**
     * @brief 指定名称添加属性描述
     *  创建对应对象的业务数据时，会为该属性自动分配内存，用来存放属性的值
     * @param name 属性描述名称, 必须保证不与其他属性描述的名称重复
     * @param valueType 属性的值类型, 必须指定有效的值类型
     * @param sampleName 属性描述名称简称, 可选项, 如果指定了简称, 则必须保证不与其他属性描述的简称重复
     * @return
     *  1. 如果属性名称为空,返回nullptr
     *  2. 如果属性的值类型无效,返回nullptr
     *  2. 如果对该类型已被初始化(被用来创建对象了), 将无法继续创建属性描述对象,将返回nullptr
     *  3. 如果对应名称的属性描述已存在,返回nullptr
     *  4. 如果对应名称的属性描述不存在,但属性简称存在,将返回nullptr,因为不同名称的属性,简称是不能重复的
    */
    WDBMAttrDesc* add(const std::string& name
        , WDBMAttrValueType valueType
        , const std::string& sampleName = ""
        , const WDBMAttrDesc::Flags& flags = WDBMAttrDesc::Flag::F_None);
    /**
     * @brief 指定名称以及属性值的获取回调添加属性描述
     *  创建对应对象的业务数据时，不会为该属性自动分配内存，且属性只能获取，无法设置
     * @param name 属性描述名称, 必须保证不与其他属性描述的名称重复
     * @param valueType 属性的值类型, 必须指定有效的值类型
     * @param fGet 属性值的获取回调, 由添加者自定义
     * @param sampleName 属性描述名称简称, 可选项, 如果指定了简称, 则必须保证不与其他属性描述的简称重复
     * @return
     *  1. 如果属性名称为空,返回nullptr
     *  2. 如果属性的值类型无效,返回nullptr
     *  2. 如果对该类型已被初始化(被用来创建对象了), 将无法继续创建属性描述对象,将返回nullptr
     *  3. 如果对应名称的属性描述已存在,返回nullptr
     *  4. 如果对应名称的属性描述不存在,但属性简称存在,将返回nullptr,因为不同名称的属性,简称是不能重复的
     *  5. 如果属性值的获取方法(fGet)无效，返回nullptr
     */
    WDBMAttrDesc* add(const std::string& name
        , WDBMAttrValueType valueType
        , const FunctionAttrGet& fGet
        , const FunctionAttrSet& fSet = FunctionAttrSet()
        , const std::string& sampleName = ""
        , const WDBMAttrDesc::Flags& flags = WDBMAttrDesc::Flag::F_None);
    /**
     * @brief 根据属性描述Id获取属性描述
     * @param 当Id无效时，返回nullptr, 否则返回对应Id的属性
    */
    WDBMAttrDesc* get(ushort id) const;
    /**
    * @brief 根据名称或简称获取属性描述
    */
    WDBMAttrDesc* get(const std::string_view& name) const;
    /**
    * @brief 是否包含对应名称或简称的属性描述
    */
    inline bool contains(const std::string_view& name) const
    {
        return this->get(name) != nullptr;
    }
    /**
    * @brief 获取所有已添加的属性描述
    */
    inline const AttrDescs& attrDescs() const
    {
        return _attrDescs;
    }

    /**
    * @brief 设置类型说明文本
    */
    inline void setDescribe(const std::string& describe)
    {
        _describe = describe;
    }
    /**
    * @brief 设置类型说明文本
    */
    inline void setDescribe(std::string&& describe)
    {
        _describe = std::forward<std::string>(describe);
    }
    /**
    * @brief 获取类型说明文本
    */
    inline const std::string& describe() const
    {
        return _describe;
    }
    /**
    * @brief 查询资源
    * @param resourceId 资源ID
    * @return 存在对应ID的资源对象则返回对象指针，不存在返回nullptr
    */
    const Resource* findResource(const std::string& resourceName) const;
    /**
    * @brief 添加资源
    * @param resourceId 资源ID
    * @param resource 资源数据
    * @return 当相同名称的资源存在时，添加失败，且返回false
    */
    bool addResource(const std::string& resourceName, const Resource& resource);
    /**
    * @brief 移除资源
    * @param resourceId 资源ID
    */
    void removeResource(const std::string& resourceName);
    /**
    * @brief 获取所有资源
    */
    inline const Resources& resources() const
    {
        return _resources;
    }

    /**
     * @brief 设置创建函数,用于创建对应类型的数据对象
    */
    inline void setCreateFuncion(FunctionCreate cFunc)
    {
        _funcCreate = cFunc;
    }
public:
    /**
     * @brief 使用现有的数据初始化描述对象，调用初始化后，将不能再添加属性描述对象
    */
    void init();
    /**
     * @brief 是否已被初始化
    */
    inline bool initialized() const
    {
        return _initialized;
    }
    /**
     * @brief 创建数据对象
     *  1. 创建数据对象
     *  2. 针对用户自定义的属性列表，分配这些属性存储的内存, 调用init(base)函数
     * @return 创建成功返回数据对象指针，失败返回nullptr
    */
    WDBDBase* create(WDNode& node);
public:
    /**
     * @brief 标志位转换为字符串表示
    */
    static const char* FlagToStr(Flag type);
    /**
     * @brief 字符串表示转换到标志位
    */
    static Flag FlagFromStr(const std::string_view& type);
    /**
     * @brief 变换类型名称与字符串之间的转换
     */
    static const char* TransformTypeToStr(TransformType type);
    /**
     * @brief 变换类型名称与字符串之间的转换
     */
    static TransformType TransformTypeFromStr(const std::string_view& type);
private:
    // 通过名称查询
    WDBMAttrDesc* queryByName(const std::string_view& name) const;
    // 通过简称查询
    WDBMAttrDesc* queryBySampleName(const std::string_view& sampleName) const ;
private:
    // 类型名称
    std::string     _name;
    // 所属模块
    WDBMBase&       _bmBase;
    // 支持挂载到的父类型
    StringVector    _parentTypes;
    // 默认属性描述列表
    AttrDescs       _attrDescs;
    // 属性名称缓存, 用于加速查询
    std::unordered_map<std::string_view, WDBMAttrDesc*> _attrDescNameMap;
    // 属性简称缓存, 用于加速查询
    std::unordered_map<std::string_view, WDBMAttrDesc*> _attrDescSNameMap;
    // 类型说明
    std::string     _describe;
    // 资源列表
    Resources       _resources;
    // 类型对象的创建方法
    FunctionCreate  _funcCreate;
    // 类型的标志
    Flags           _flags;
    // 变换类型
    TransformType   _transformType;
    // 类型Id, 由类型管理自动分配
    ushort          _id;
private:
    friend class WDBDBase;
    // 是否已被初始化
    bool    _initialized;
    // 属性需要分配内存的总大小
    size_t  _aTotalMemSize;
    // 存放需要分配内存的属性列表
    AttrDescs _allocatedAttrs;
};


WD_NAMESPACE_END
