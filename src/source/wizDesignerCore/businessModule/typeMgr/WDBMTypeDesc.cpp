#include "WDBMTypeDesc.h"
#include "../../businessModule/WDBDBase.h"
#include "../../businessModule/WDBMBase.h"
#include "../../WDCore.h"
#include "WDBMAttrEnumDictionary.h"

WD_NAMESPACE_BEGIN;

WDBMTypeDesc::WDBMTypeDesc(WDBMBase& bmBase, const std::string_view& name, ushort id)
    : _bmBase(bmBase)
    , _name(name)
    , _id(id)
{
    _flags = F_None;
    _initialized = false;
    _aTotalMemSize = 0;
    _transformType = TransformType::TT_None;
}
WDBMTypeDesc::~WDBMTypeDesc()
{
    _attrDescNameMap.clear();
    _attrDescSNameMap.clear();
    while (!_attrDescs.empty()) 
    {
        if (_attrDescs.back() != nullptr)
            delete _attrDescs.back();
        _attrDescs.pop_back();
    }
}

WDCore& WDBMTypeDesc::core() const
{
    return _bmBase.core();
}

WDBMAttrDesc* WDBMTypeDesc::add(const std::string& name
    , WDBMAttrValueType valueType
    , const std::string& sampleName
    , const WDBMAttrDesc::Flags& flags)
{
    if (this->initialized())
    {
        assert(false && "类型描述已被初始化,无法再进行属性添加!");
        return nullptr;
    }
    if (name.empty())
    {
        assert(false && "属性名称不能为空!");
        return nullptr;
    }
    if (valueType == WDBMAttrValueType::T_Null)
    {
        assert(false && "属性值类型必须保证有效!");
        return nullptr;
    }
    if (this->queryByName(name))
    {
        assert(false && "对应名称的属性已经存在!");
        return nullptr;
    }
    if (!sampleName.empty() && this->queryBySampleName(sampleName))
    {
        assert(false && "对应简称的属性已经存在!");
        return nullptr;
    }
    // 使用下标作为属性描述ID
    ushort attrDescId = static_cast<ushort>(_attrDescs.size());
    // 创建描述对象
    auto pAttr = new WDBMAttrDesc(name, valueType, sampleName, flags, attrDescId);
    // 添加到缓存
    _attrDescs.push_back(pAttr);
    // 添加到缓存, 用于根据名称的快速查找
    _attrDescNameMap[pAttr->name()] = pAttr;
    // 添加到缓存, 用于根据名称简称的快速查找
    if (!pAttr->sampleName().empty())
        _attrDescSNameMap[pAttr->sampleName()] = pAttr;

    return pAttr;
}
WDBMAttrDesc* WDBMTypeDesc::add(const std::string& name
    , WDBMAttrValueType valueType
    , const FunctionAttrGet& fGet
    , const FunctionAttrSet& fSet
    , const std::string& sampleName
    , const WDBMAttrDesc::Flags& flags)
{
    if (this->initialized())
    {
        assert(false && "类型描述已被初始化,无法再进行属性添加!");
        return nullptr;
    }
    if (name.empty())
    {
        assert(false && "属性名称不能为空!");
        return nullptr;
    }
    if (valueType == WDBMAttrValueType::T_Null)
    {
        assert(false && "属性值类型必须保证有效!");
        return nullptr;
    }
    if (this->queryByName(name))
    {
        assert(false && "对应名称的属性已经存在!");
        return nullptr;
    }
    if (!sampleName.empty() && this->queryBySampleName(sampleName))
    {
        assert(false && "对应简称的属性已经存在!");
        return nullptr;
    }
    if (!fGet)
    {
        assert(false && "属性的获取方法无效!");
        return nullptr;
    }
    // 使用下标作为属性描述ID
    ushort attrDescId = static_cast<ushort>(_attrDescs.size());
    // 创建描述对象
    auto pAttr = new WDBMAttrDesc(name, valueType, fGet, fSet, sampleName, flags, attrDescId);
    // 添加到缓存
    _attrDescs.push_back(pAttr);
    // 添加到缓存, 用于根据名称的快速查找
    _attrDescNameMap[pAttr->name()] = pAttr;
    // 添加到缓存, 用于根据名称简称的快速查找
    if (!pAttr->sampleName().empty())
        _attrDescSNameMap[pAttr->sampleName()] = pAttr;

    return pAttr;
}

WDBMAttrDesc* WDBMTypeDesc::get(const std::string_view& name) const
{
    if (name.empty())
        return nullptr;

    // 先从名称中查询
    auto pRet0 = this->queryByName(name);
    if (pRet0 != nullptr)
        return pRet0;

    // 再从简称中查询
    auto pRet1 = this->queryBySampleName(name);
    if (pRet1 != nullptr)
        return pRet1;

    // 没查到
    return nullptr;
}
WDBMAttrDesc* WDBMTypeDesc::get(ushort id) const
{
    if (id < _attrDescs.size())
        return _attrDescs[id];
    return nullptr;
}

const WDBMTypeDesc::Resource * WDBMTypeDesc::findResource(const std::string & resourceName) const
{
    auto fItr = _resources.find(resourceName);
    if (fItr == _resources.end())
        return nullptr;
    return &fItr->second;
}

bool WDBMTypeDesc::addResource(const std::string& resourceName, const Resource& resource)
{
    if (this->findResource(resourceName) != nullptr)
        return false;
    auto rItr = _resources.emplace(resourceName, resource);
    return rItr.second;

}

void WDBMTypeDesc::removeResource(const std::string& resourceName)
{
    auto fItr = _resources.find(resourceName);
    if (fItr != _resources.end())
        _resources.erase(fItr);
}

void WDBMTypeDesc::init()
{
    // 已被初始化，不再做初始化操作
    if (this->initialized())
    {
        assert(false && "重复调用了类型描述的初始化!");
        return;
    }
    _initialized = true;

    // 重置并重新计算内存总大小
    _aTotalMemSize = 0;
    _allocatedAttrs.clear();
    _allocatedAttrs.reserve(_attrDescs.size());
    // 统计所有需要分配内存的属性，并计算每个属性的内存容量
    for (auto pDesc : _attrDescs)
    {
        if (pDesc == nullptr)
        {
            assert(false);
            continue;
        }
        // 查询并关联属性的字典对象
        pDesc->_pEnumDict = _bmBase.attrEnumDictMgr().query(pDesc->_enumDictName);
        // 查询并关联引用属性引用节点所属的模块对象
        if(!pDesc->refNodeModuleName().empty())
            pDesc->_pBMBase = _bmBase.core().getBMBase(pDesc->refNodeModuleName());

        // 获取内存描述对象
        auto pMemDesc = pDesc->getMemDesc();
        // 不需要分配内存
        if (pMemDesc == nullptr)
            continue;
        // 断言未被分配内存
        assert(pMemDesc->size == 0);
        // 计算字段的内存大小
        size_t tSz = WDBMAttrDesc::Sizeof(*pDesc);
        // 当前属性字段需要分配内存的大小
        pMemDesc->size = tSz;
        // 当前属性字段在内存中的首地址偏移量
        pMemDesc->offset = _aTotalMemSize;
        // 记录内存的总大小
        _aTotalMemSize += tSz;
        // 缓存到已分配内存的属性列表中
        _allocatedAttrs.push_back(pDesc);
    }
}

WDBDBase* WDBMTypeDesc::create(WDNode& node)
{
    if (!this->initialized())
    {
        assert(false && "类型描述未初始化, 无法创建业务数据对象!");
        return nullptr;
    }

    WDBDBase* pBase = nullptr;
    if (_funcCreate != nullptr)
    {
        pBase = _funcCreate(node);
        if (pBase == nullptr)
        {
            assert(false && "注意:业务数据类型描述对象的创建方法返回了nullptr!");
            return nullptr;
        }
    }
    else
    {
        pBase = new WDBDBase(node);
    }

    return pBase;
}

const char* WDBMTypeDesc::FlagToStr(Flag type)
{
    switch (type)
    {
    case WD::WDBMTypeDesc::F_AutoGeneration:
        return  "AutoGeneration";
        break;
    case WD::WDBMTypeDesc::F_TriggerUpdate:
        return  "TriggerUpdate";
        break;
    default:
        break;
    }
    return  "";
}
WDBMTypeDesc::Flag WDBMTypeDesc::FlagFromStr(const std::string_view& type)
{
    if (type == "AutoGeneration")
        return  F_AutoGeneration;
    else if (type == "TriggerUpdate")
        return  F_TriggerUpdate;
    else
        return  F_None;
}
const char* WDBMTypeDesc::TransformTypeToStr(TransformType type)
{
    switch (type)
    {
    case WDBMTypeDesc::TT_Pos:
        return "Pos";
        break;
    case WDBMTypeDesc::TT_PosOri:
        return "PosOri";
        break;
    case WDBMTypeDesc::TT_PosOriScale:
        return "PosOriScale";
        break;
    case WDBMTypeDesc::TT_SEPosDrnBangle:
        return "SEPosDrnBangle";
        break;
    case WDBMTypeDesc::TT_SEDrn:
        return "SEDrn";
        break;
    case WDBMTypeDesc::TT_HTPosDir:
        return "HTPosDir";
        break;
    case WDBMTypeDesc::TT_HTPos:
        return "HTPos";
        break;
    case WDBMTypeDesc::TT_PosSjustBangle:
        return "PosSjustBangle";
        break;
    case WDBMTypeDesc::TT_DelposPoslineZdisBangle:
        return "DelposPoslineZdisBangle";
        break;
    case WDBMTypeDesc::TT_DelposZdirBangle:
        return "DelposZdirBangle";
        break;
    default:
        return "";
        break;
    }
}
WDBMTypeDesc::TransformType WDBMTypeDesc::TransformTypeFromStr(const std::string_view& type)
{
    if (type == "Pos")
        return  TT_Pos;
    else if (type == "PosOri")
        return  TT_PosOri;
    else if (type == "PosOriScale")
        return  TT_PosOriScale;
    else if (type == "SEPosDrnBangle")
        return  TT_SEPosDrnBangle;
    else if (type == "SEDrn")
        return  TT_SEDrn;
    else if (type == "HTPosDir")
        return  TT_HTPosDir;
    else if (type == "HTPos")
        return  TT_HTPos;
    else if (type == "PosSjustBangle")
        return  TT_PosSjustBangle;
    else if (type == "DelposPoslineZdisBangle")
        return  TT_DelposPoslineZdisBangle;
    else if (type == "DelposZdirBangle")
        return  TT_DelposZdirBangle;
    else
        return  TT_None;
}

WDBMAttrDesc* WDBMTypeDesc::queryByName(const std::string_view& name) const
{
    auto fItr = _attrDescNameMap.find(name);
    if (fItr == _attrDescNameMap.end())
        return nullptr;
    return fItr->second;
}
WDBMAttrDesc* WDBMTypeDesc::queryBySampleName(const std::string_view& sampleName) const
{
    auto fItr = _attrDescSNameMap.find(sampleName);
    if (fItr == _attrDescSNameMap.end())
        return nullptr;
    return fItr->second;
}

WD_NAMESPACE_END

