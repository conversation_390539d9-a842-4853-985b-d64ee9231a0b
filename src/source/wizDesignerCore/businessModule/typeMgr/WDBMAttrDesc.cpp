#include "WDBMAttrDesc.h"
#include "../WDBDBase.h"
#include "../WDBMBase.h"
#include "WDBMAttrEnumDictionary.h"

WD_NAMESPACE_BEGIN;


WDBMAttrDesc::WDBMAttrDesc(const std::string& name
    , WDBMAttrValueType type
    , const std::string& sampleName
    , const Flags& flags
    , ushort id)
    : _name(name)
    , _type(type)
    , _vMethod(MemDesc())
    , _sampleName(sampleName)
    , _flags(flags)
    , _id(id)
{
    memberInit();
}
WDBMAttrDesc::WDBMAttrDesc(const std::string& name
    , WDBMAttrValueType type
    , const FunctionGet& fGet
    , const FunctionSet& fSet
    , const std::string& sampleName
    , const Flags& flags
    , ushort id)
    : _name(name)
    , _type(type)
    , _vMethod(FGetSetDesc(fGet, fSet))
    , _sampleName(sampleName)
    , _flags(flags)
    , _id(id)
{
    memberInit();
    // 必须指定获取回调
    assert(fGet);
    // 如果设置函数无效，则自动设置为只读属性
    if (!fSet)
        _flags.addFlag(Flag::F_ReadOnly);
}
WDBMAttrDesc::~WDBMAttrDesc()
{
}

bool WDBMAttrDesc::setDefaultValue(const Value& value)
{
    //  判断set函数中的数据类型和设置的类型是否匹配
    if (_type != value.type())
    {
        assert(false && "set函数中的数据类型和设置的类型不匹配!");
        return false;
    }
    _defaultValue = value;
    return true;
}
bool WDBMAttrDesc::setMinimumValue(const Value& minimumValue)
{
    //  判断set函数中的数据类型和设置的类型是否匹配
    if (_type != minimumValue.type())
    {
        assert(false && "set函数中的数据类型和设置的类型不匹配!");
        return false;
    }
    _minimumValue = minimumValue;
    return true;
}
bool WDBMAttrDesc::setMaximumValue(const Value& maximumValue)
{
    //  判断set函数中的数据类型和设置的类型是否匹配
    if (_type != maximumValue.type())
    {
        assert(false && "set函数中的数据类型和设置的类型不匹配!");
        return false;
    }
    _maximumValue = maximumValue;
    return true;
}
bool WDBMAttrDesc::setSingleStep(const Value& singleStep)
{
    //  判断set函数中的数据类型和设置的类型是否匹配
    if (_type != singleStep.type())
    {
        assert(false && "set函数中的数据类型和设置的类型不匹配!");
        return false;
    }
    _singleStep = singleStep;
    return true;
}

WDBMAttrDesc::Value WDBMAttrDesc::value(const WDNode& node) const
{
    switch (_vMethod.index())
    {
    case 1:
    {
        auto pBase = node.getBDBase();
        if (pBase == nullptr)
        {
            assert(false && "节点的业务数据对象无效!");
            return Value();
        }
        // 如果是内存分配的方式，则直接从base获取值
        return pBase->getValue(*this);
    }
    break;
    case 2:
    {
        // 如果是get和set函数的方式，则直接从get函数获取值
        const auto& d = std::get<2>(_vMethod);
        if (!d.fGet)
        {
            assert(false && "属性的获取函数无效!");
            return Value();
        }
        Value value = d.fGet(node);
        // 判断get函数中的数据类型和设置的类型是否匹配
        if (_type != value.type())
        {
            assert(false && "get函数中的数据类型和设置的类型不匹配!");
            return Value();
        }
        return value;
    }
    break;
    default:
        assert(false && "无效的值获取方式!");
        break;
    }
    return Value();
}
bool  WDBMAttrDesc::setValue(WDNode& node, const Value& value) const
{
    //  判断set函数中的数据类型和设置的类型是否匹配
    if (_type != value.type())
    {
        assert(false && "set函数中的数据类型和设置的类型不匹配!");
        return false;
    }

    WDBMAttrValue prevValue = WDBMAttrValue();
    switch (_vMethod.index())
    {
    case 1:
    {
        auto pBase = node.getBDBase();
        if (pBase == nullptr)
        {
            assert(false && "节点的业务数据对象无效!");
            return false;
        }
        prevValue = pBase->getValue(*this);
        // 当前值和之前值一致, 不再重复设置
        if (value == prevValue)
            return true;
        
        // 设置值之前先调用回调
        if (_funcValueSetBefore)
            _funcValueSetBefore(node, value, prevValue, *this);

        // 如果是内存分配的方式，则直接从base设置值
        if (!pBase->setValue(*this, value))
            return false;
    }
    break;
    case 2:
    {
        // 如果是get和set函数的方式，则直接从set函数设置值
        const auto& d = std::get<2>(_vMethod);
        if (!d.fSet)
        {
            assert(false && "属性的设置函数无效!");
            return false;
        }

        // 先获取之前的值
        if (d.fGet)
            prevValue = d.fGet(node);

        // 当前值和之前值一致, 不再重复设置
        if (value == prevValue)
            return true;

        // 设置值之前先调用回调
        if (_funcValueSetBefore)
            _funcValueSetBefore(node, value, prevValue, *this);

        // 设置
        if(!d.fSet(node, value))
            return false;
    }
    break;
    default:
        assert(false && "无效的值设置方式!");
        return false;
        break;
    }

    // 设置编辑标记
    auto flags = node.flags();
    flags.addFlags(WDNode::F_Editted, WDNode::F_EdittedStatus);
    // 如果属性值设置成功，并且属性带有update标志，则自动给节点添加update标志
    if (_flags.hasFlag(F_Update))
        flags.addFlag(WDNode::F_Update);
    node.setFlags(flags);

    // 回调通知值改变
    if (_funcValueSetAfter)
        _funcValueSetAfter(node, value, prevValue, *this);
    // 通知值改变
    node.sendAttributeValueChanged(this->name(), value, prevValue);

    return true;
}
WDBMAttrValueCRef WDBMAttrDesc::valueCRef(const WDNode& node) const
{
    switch (_vMethod.index())
    {
    case 1:
    {
        auto pBase = node.getBDBase();
        if (pBase == nullptr)
        {
            assert(false && "节点的业务数据对象无效!");
            return WDBMAttrValueCRef();
        }
        return pBase->getValueCRef(*this);
    }
    break;
    case 2:
    {
        assert(false && "!注意:当前属性不支持以引用的方式获取!");
        return WDBMAttrValueCRef();
    }
    break;
    default:
        assert(false && "无效的值获取方式!");
        break;
    }
    return WDBMAttrValueCRef();
}
const void* WDBMAttrDesc::valueAddr(const WDNode& node) const
{
    switch (_vMethod.index())
    {
    case 1:
    {
        auto pBase = node.getBDBase();
        if (pBase == nullptr)
        {
            assert(false && "节点的业务数据对象无效!");
            return nullptr;
        }
        // 如果是内存分配的方式，直接获取内存首地址
        return pBase->vAddr(*this);
    }
    break;
    case 2:
    {
        return nullptr;
    }
    break;
    default:
        assert(false && "无效的值获取方式!");
        break;
    }
    return nullptr;
}

size_t WDBMAttrDesc::Sizeof(const WDBMAttrDesc& attr)
{
    WDBMAttrValueType type = attr.type();
    switch (type)
    {
    case WD::WDBMAttrValueType::T_Bool:
        return sizeof(bool);
        break;
    case WD::WDBMAttrValueType::T_Int:
        return sizeof(int);
        break;
    case WD::WDBMAttrValueType::T_Double:
        return sizeof(double);
        break;
    case WD::WDBMAttrValueType::T_String:
        return sizeof(std::string);
        break;
    case WD::WDBMAttrValueType::T_Word:
        return sizeof(WDBMWord);
        break;
    case WD::WDBMAttrValueType::T_DVec2:
        return sizeof(DVec2);
        break;
    case WD::WDBMAttrValueType::T_DVec3:
        return sizeof(DVec3);
        break;
    case WD::WDBMAttrValueType::T_DQuat:
        return sizeof(DQuat);
        break;
    case WD::WDBMAttrValueType::T_Color:
        return sizeof(Color);
        break;
    case WD::WDBMAttrValueType::T_Uuid:
        return sizeof(WDUuid);
        break;
    case WD::WDBMAttrValueType::T_LevelRange:
        return sizeof(WDBMLevelRange);
        break;
    case WD::WDBMAttrValueType::T_NodeRef:
        return sizeof(WDBMNodeRef);
        break;
    case WD::WDBMAttrValueType::T_IntVector:
        return sizeof(std::vector<int>);
        break;
    case WD::WDBMAttrValueType::T_DoubleVector:
        return sizeof(std::vector<double>);
        break;
    case WD::WDBMAttrValueType::T_StringVector:
        return sizeof(std::vector<std::string>);
        break;
    case WD::WDBMAttrValueType::T_NodeRefs:
        return sizeof(WDBMNodeRefs);
        break;
    case WD::WDBMAttrValueType::T_GeometryRef:
        return sizeof(WDBMGeometryRef);
        break;
    default:
        break;
    }
    return 0;
}

const char* WDBMAttrDesc::FlagToStr(Flag type)
{
    switch (type)
    {
    case WDBMAttrDesc::F_ReadOnly:
        return  "ReadOnly";
    case WDBMAttrDesc::F_Hidden:
        return  "Hidden";
    case WDBMAttrDesc::F_Update:
        return  "Update";
    default:
        break;
    }
    return  "";
}
WDBMAttrDesc::Flag WDBMAttrDesc::FlagFromStr(const std::string_view& type)
{
    if (type == "ReadOnly")
        return WDBMAttrDesc::F_ReadOnly;
    else if (type == "Hidden")
        return WDBMAttrDesc::F_Hidden;
    else if (type == "Update")
        return WDBMAttrDesc::F_Update;
    else
        return WDBMAttrDesc::F_None;
}
const char* WDBMAttrDesc::DataFlagToStr(DataFlag type)
{
    switch (type)
    {
    case WDBMAttrDesc::DF_DConnections:
        return "DConnections";
        break;
    default:
        break;
    }
    return "";
}
WDBMAttrDesc::DataFlag WDBMAttrDesc::DataFlagFromStr(const std::string_view& type)
{
    if (type == "DConnections")
        return WDBMAttrDesc::DF_DConnections;
    else
        return WDBMAttrDesc::DF_None;
}

void WDBMAttrDesc::memberInit()
{
    _regexp     = "";
    _decimals   = DefaultDecimals;
    _pEnumDict  = nullptr;
    _pBMBase    = nullptr;
    _dataFlags  = DataFlag::DF_None;
}

WD_NAMESPACE_END
