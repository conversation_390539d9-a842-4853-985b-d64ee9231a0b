#pragma     once

#include "../WDBMCommon.h"
#include "../dataType/WDBMWord.h"
#include "../dataType/WDBMLevelRange.h"
#include "../dataType/WDBMNodeRef.h"
#include "../dataType/WDBMGeometryRef.h"

WD_NAMESPACE_BEGIN

/**
 * @brief  业务数据节点属性值类型
*/
enum WDBMAttrValueType
{
    // 无、未知类型
    T_Null = 0,
    // bool 类型(bool)
    T_Bool,
    // 整数类型(int)
    T_Int,
    // 实数类型(double)
    T_Double,
    // 字符串类型(std::string)
    T_String,
    // 关键字(本质还是字符串)类型(WD::WDBMWord)
    T_Word,
    // 二维向量(WD::DVec2)
    T_DVec2,
    // 三维向量(WD::DVec3)
    T_DVec3,
    // 四元数(WD::DQuat)
    T_DQuat,
    // 颜色类型(WD::Color)
    T_Color,
    // uuid类型(WD::WDUuid)
    T_Uuid,
    // 级别范围(WD::WDBMLevelRange)
    T_LevelRange,
    // 节点引用类型(WD::WDBMNodeRef)
    T_NodeRef,
    // 整形数组(std::vector<int>)
    T_IntVector,
    // 实数数组(std::vector<double>)
    T_DoubleVector,
    // 字符串数组(std::vector<std::string>)
    T_StringVector,
    // 节点引用列表(WD::WDBMNodeRefs)
    T_NodeRefs,
    // 几何对象引用(WD::WDBMGeometryRef)
    T_GeometryRef,
};

/**
 * @brief 业务数据节点属性值的 const 引用, 目的是快速访问属性值而不产生额外的拷贝开销
*/
class WD_API WDBMAttrValueCRef
{
public:
    /**
     * @brief 业务数据节点属性值类型
    */
    using Type = WDBMAttrValueType;
public:
    /**
     * @brief 根据数据类型获取对应的枚举类型
    */
    template <typename T>
    static constexpr Type GetType();
    /**
     * @brief 将属性值转换为字符串类型
     * @param value 属性值
     * @param bOk 是否转换成功,可选项,属性值无效时，转换失败
     * @return 转换结果,如果属性值无效，将转换为空字符串
    */
    static std::string ToString(const WDBMAttrValueCRef& value, bool* bOk = nullptr);
public:
    /**
     * @brief 空构造
    */
    WDBMAttrValueCRef();
    /**
     * @brief 指定 const bool* 值构造
    */
    inline explicit WDBMAttrValueCRef(const bool* pData);
    /**
     * @brief 指定 const int* 值构造
    */
    inline explicit WDBMAttrValueCRef(const int* pData);
    /**
     * @brief 指定 const double* 值构造
    */
    inline explicit WDBMAttrValueCRef(const double* pData);
    /**
     * @brief 指定 const std::string* 值构造
    */
    inline explicit WDBMAttrValueCRef(const std::string* pData);
    /**
     * @brief 指定 const WDBMWord* 值构造
    */
    inline explicit WDBMAttrValueCRef(const WDBMWord* pData);
    /**
     * @brief 指定 const DVec2* 值构造
    */
    inline explicit WDBMAttrValueCRef(const DVec2* pData);
    /**
     * @brief 指定 const DVec3* 值构造
    */
    inline explicit WDBMAttrValueCRef(const DVec3* pData);
    /**
     * @brief 指定 const DQuat* 值构造
    */
    inline explicit WDBMAttrValueCRef(const DQuat* pData);
    /**
     * @brief 指定 const Color* 值构造
    */
    inline explicit WDBMAttrValueCRef(const Color* pData);
    /**
     * @brief 指定 const WDUuid* 值构造
    */
    inline explicit WDBMAttrValueCRef(const WDUuid* pData);
    /**
     * @brief 指定 const WDBMLevelRange*  值构造
    */
    inline explicit WDBMAttrValueCRef(const WDBMLevelRange* pData);
    /**
     * @brief 指定 const WDBMNodeRef* 值构造
    */
    inline explicit WDBMAttrValueCRef(const WDBMNodeRef* pData);
    /**
     * @brief 指定 const std::vector<int>* 值构造
    */
    inline explicit WDBMAttrValueCRef(const std::vector<int>* pData);
    /**
     * @brief 指定 std::vector<double> 值构造
    */
    inline explicit WDBMAttrValueCRef(const std::vector<double>* pData);
    /**
     * @brief 指定 const std::vector<std::string>* 值构造
    */
    inline explicit WDBMAttrValueCRef(const std::vector<std::string>* pData);
    /**
     * @brief 指定 const WDBMNodeRefs* 值构造
    */
    inline explicit WDBMAttrValueCRef(const WDBMNodeRefs* pData);
    /**
     * @brief 指定 const WDBMGeometryRef* 值构造
    */
    inline explicit WDBMAttrValueCRef(const WDBMGeometryRef* pData);
    /**
     * @brief 拷贝构造
    */
    WDBMAttrValueCRef(const WDBMAttrValueCRef& right) = default;
    /**
     * @brief 移动构造
    */
    WDBMAttrValueCRef(WDBMAttrValueCRef&& right) = default;
    /**
     * @brief 赋值 WDBMAttrValueRef
    */
    WDBMAttrValueCRef& operator=(const WDBMAttrValueCRef& right) = default;
    /**
     * @brief 移动赋值 WDBMAttrValue
    */
    WDBMAttrValueCRef& operator=(WDBMAttrValueCRef&& right) = default;
public:
    /**
     * @brief 值是否有效(值无效表明是空引用)
    */
    bool valid() const;
    /**
     * @brief 获取当前值的类型
    */
    constexpr const inline Type type() const
    {
        return  static_cast<Type>(_data.index());
    }
    /**
     * @brief 获取指定类型的值
     * @return 如果值有效且是指定类型，则返回类型的值的指针，否则返回nullptr
    */
    template <typename T>
    inline const T* data() const;
    /**
     * @brief 获取bool类型的值
     * @return 当前值如果是空引用或不是bool类型时, 返回nullptr
    */
    const bool* toBool() const;
    /**
     * @brief 通过引用获取bool类型的值
     * @param outValue 输出的bool值
     * @return 是否获取成功
    */
    bool toBool(bool& outValue) const;
    /**
     * @brief 获取int类型的值
     * @return 当前值如果是空引用或不是int类型时, 返回nullptr
    */
    const int* toInt() const;
    /**
     * @brief 通过引用获取int类型的值
     * @param outValue 输出的int值
     * @return 是否获取成功
    */
    bool toInt(int& outValue) const;
    /**
     * @brief 获取double类型的值
     * @return 当前值如果是空引用或不是double类型时, 返回nullptr
    */
    const double* toDouble() const;
    /**
     * @brief 通过引用获取double类型的值
     * @param outValue 输出的double值
     * @return 是否获取成功
    */
    bool toDouble(double& outValue) const;
    /**
     * @brief 获取std::string类型的值
     * @return 当前值如果是空引用或不是std::string类型时, 返回nullptr
    */
    const std::string* toString() const;
    /**
     * @brief 通过引用获取std::string类型的值
     * @param outValue 输出的std::string值
     * @return 是否获取成功
    */
    bool toString(std::string& outValue) const;
    /**
     * @brief 获取WDBMWord类型的值
     * @return 当前值如果是空引用或不是WDBMWord类型时, 返回nullptr
    */
    const WDBMWord* toWord() const;
    /**
     * @brief 通过引用获取WDBMWord类型的值
     * @param outValue 输出的WDBMWord值
     * @return 是否获取成功
    */
    bool toWord(WDBMWord& outValue) const;
    /**
     * @brief 获取DVec2类型的值
     * @return 当前值如果是空引用或不是DVec2类型时, 返回nullptr
    */
    const DVec2* toDVec2() const;
    /**
     * @brief 通过引用获取DVec2类型的值
     * @param outValue 输出的DVec2值
     * @return 是否获取成功
    */
    bool toDVec2(DVec2& outValue) const;
    /**
     * @brief 获取DVec3类型的值
     * @return 当前值如果是空引用或不是DVec3类型时, 返回nullptr
    */
    const DVec3* toDVec3() const;
    /**
     * @brief 通过引用获取DVec3类型的值
     * @param outValue 输出的DVec3值
     * @return 是否获取成功
    */
    bool toDVec3(DVec3& outValue) const;
    /**
     * @brief 获取DQuat类型的值
     * @return 当前值如果是空引用或不是DQuat类型时, 返回nullptr
    */
    const DQuat* toQuat() const;
    /**
     * @brief 通过引用获取DQuat类型的值
     * @param outValue 输出的DQuat值
     * @return 是否获取成功
    */
    bool toQuat(DQuat& outValue) const;
    /**
     * @brief 获取Color类型的值
     * @return 当前值如果是空引用或不是Color类型时, 返回nullptr
    */
    const Color* toColor() const;
    /**
     * @brief 通过引用获取Color类型的值
     * @param outValue 输出的Color值
     * @return 是否获取成功
    */
    bool toColor(Color& outValue) const;
    /**
     * @brief 获取WDUuid类型的值
     * @return 当前值如果是空引用或不是WDUuid类型时, 返回nullptr
    */
    const WDUuid* toUuid() const;
    /**
     * @brief 通过引用获取WDUuid类型的值
     * @param outValue 输出的WDUuid值
     * @return 是否获取成功
    */
    bool toUuid(WDUuid& outValue) const;
    /**
     * @brief 获取WDBMLevelRange类型的值
     * @return 当前值如果是空引用或不是WDBMLevelRange类型时, 返回nullptr
    */
    const WDBMLevelRange* toLevelRange() const;
    /**
     * @brief 通过引用获取WDBMLevelRange类型的值
     * @param outValue 输出的WDBMLevelRange值
     * @return 是否获取成功
    */
    bool toLevelRange(WDBMLevelRange& outValue) const;
    /**
     * @brief 获取WDBMNodeRef类型的值
     * @return 当前值如果是空引用或不是WDBMNodeRef类型时, 返回nullptr
    */
    const WDBMNodeRef* toNodeRef() const;
    /**
     * @brief 通过引用获取WDBMNodeRef类型的值
     * @param outValue 输出的WDBMNodeRef值
     * @return 是否获取成功
    */
    bool toNodeRef(WDBMNodeRef& outValue) const;
    /**
     * @brief 获取std::vector<int>类型的值
     * @return 当前值如果是空引用或不是 std::vector<int> 类型时, 返回nullptr
    */
    const std::vector<int>* toIntVector() const;
    /**
     * @brief 通过引用获取std::vector<int>类型的值
     * @param outValue 输出的std::vector<int>值
     * @return 是否获取成功
    */
    bool toIntVector(std::vector<int>& outValue) const;
    /**
     * @brief 获取std::vector<double>类型的值
     * @return 当前值如果是空引用或不是std::vector<double>类型时, 返回nullptr
    */
    const std::vector<double>* toDoubleVector() const;
    /**
     * @brief 通过引用获取std::vector<double>类型的值
     * @param outValue 输出的std::vector<double>值
     * @return 是否获取成功
    */
    bool toDoubleVector(std::vector<double>& outValue) const;
    /**
     * @brief 获取std::vector<std::string>类型的值
     * @return 当前值如果是空引用或不是std::vector<std::string>类型时, 返回nullptr
    */
    const std::vector<std::string>* toStringVector() const;
    /**
     * @brief 通过引用获取std::vector<std::string>类型的值
     * @param outValue 输出的std::vector<std::string>值
     * @return 是否获取成功
    */
    bool toStringVector(std::vector<std::string>& outValue) const;
    /**
     * @brief 获取WDBMNodeRefs类型的值
     * @return 当前值如果是空引用或不是WDBMNodeRefs类型时, 返回nullptr
    */
    const WDBMNodeRefs* toNodeRefVector() const;
    /**
     * @brief 通过引用获取WDBMNodeRefs类型的值
     * @param outValue 输出的WDBMNodeRefs值
     * @return 是否获取成功
    */
    bool toNodeRefVector(WDBMNodeRefs& outValue) const;
    /**
     * @brief 获取WDBMGeometryRef类型的值
     * @return 当前值如果是空引用或不是WDBMGeometryRef类型时, 返回nullptr
    */
    const WDBMGeometryRef* toGeometryRef() const;
    /**
     * @brief 通过引用获取WDBMGeometryRef类型的值
     * @param outValue 输出的WDBMGeometryRef值
     * @return 是否获取成功
    */
    bool toGeometryRef(WDBMGeometryRef& outValue) const;

    /**
     * @brief 当前值转换到字符串
     * @param bOk 是否转换成功, 可选项, 如果是空引用时, 将转换失败
     * @return 转换完的字符串, 转换失败返回空字符串
    */
    std::string convertToString(bool* bOk = nullptr) const;
    /**
    * @brief 当前值转换到double类型
     * @param bOk 是否转换成功, 可选项, 如果是空引用或非数值类型时或非数值类型字符串时, 将转换失败
    * @return 转换结果，转换失败返回0.0
    */
    double convertToDouble(bool* bOk = nullptr) const;
    /**
    * @brief 当前值转换到int类型
     * @param bOk 是否转换成功, 可选项, 如果是空引用或非数值类型时或非数值类型字符串时, 将转换失败
    * @return 转换结果，转换失败返回0
    */
    int convertToInt(bool* bOk = nullptr) const;
public:
    /**
     * @brief ==
    */
    bool operator==(const WDBMAttrValueCRef& right) const;
    /**
     * @brief !=
    */
    bool operator!=(const WDBMAttrValueCRef& right) const;
    /**
     * @brief >
    */
    bool operator>(const WDBMAttrValueCRef& right) const;
    /**
     * @brief <
    */
    bool operator<(const WDBMAttrValueCRef& right) const;
    /**
     * @brief >=
    */
    bool operator>=(const WDBMAttrValueCRef& right) const;
    /**
     * @brief <=
    */
    bool operator<=(const WDBMAttrValueCRef& right) const;
private:
    /**
     * @brief 属性的值数据地址
    */
    using Data = std::variant<std::monostate
        , const bool*
        , const int*
        , const double*
        , const std::string*
        , const WDBMWord*
        , const DVec2*
        , const DVec3*
        , const DQuat*
        , const Color*
        , const WDUuid*
        , const WDBMLevelRange*
        , const WDBMNodeRef*
        , const std::vector<int>*
        , const std::vector<double>*
        , const std::vector<std::string>*
        , const WDBMNodeRefs*
        , const WDBMGeometryRef*>;
    // 值数据
    Data _data;
};

/**
 * @brief 业务数据节点属性值
*/
class WD_API WDBMAttrValue
{
public:
    /**
     * @brief 业务数据节点属性值类型
    */
    using Type = WDBMAttrValueType;
public:
    /**
     * @brief 根据数据类型获取对应的枚举类型
    */
    template <typename T>
    static constexpr Type GetType();
    /**
     * @brief 指定值类型，将字符串值转换到对应类型的属性值
     * @param type 值类型
     * @param value 值数据字符串
     * @return 转换结果，如果失败，返回无效的 WDBMAttrValue
    */
    static WDBMAttrValue FromString(Type type, const std::string_view& value, bool* bOk = nullptr);
    /**
     * @brief 将属性值转换为字符串类型
     * @param value 属性值
     * @param bOk 是否转换成功,可选项,属性值无效时，转换失败
     * @return 转换结果,如果属性值无效，将转换为空字符串
    */
    static std::string ToString(const WDBMAttrValue& value, bool* bOk = nullptr);
public:
    /**
     * @brief 构造
    */
	WDBMAttrValue();
	/**
	 * @brief 指定类型构造
	*/
    explicit WDBMAttrValue(Type type);
    /**
     * @brief 指定 bool 值构造
    */
    inline WDBMAttrValue(bool data);
    /**
     * @brief 指定 int 值构造
    */
    inline WDBMAttrValue(int data);
    /**
     * @brief 指定 double 值构造
    */
    inline WDBMAttrValue(double data);
    /**
     * @brief 指定 const char* 值构造, 转换为std::string
    */
    inline WDBMAttrValue(const char* data);
    /**
     * @brief 指定 std::string 值构造
    */
    inline WDBMAttrValue(const std::string& data);
    /**
     * @brief 指定 std::string 值移动构造
    */
    inline WDBMAttrValue(std::string&& data);
    /**
     * @brief 指定 WDBMWord 值构造
    */
    inline WDBMAttrValue(const WDBMWord& data);
    /**
     * @brief 指定 WDBMWord 值移动构造
    */
    inline WDBMAttrValue(WDBMWord&& data);
    /**
     * @brief 指定 DVec2 值构造
    */
    inline WDBMAttrValue(const DVec2& data);
    /**
     * @brief 指定 DVec2 值移动构造
    */
    inline WDBMAttrValue(DVec2&& data);
    /**
     * @brief 指定 DVec3 值构造
    */
    inline WDBMAttrValue(const DVec3& data);
    /**
     * @brief 指定 DVec3 值移动构造
    */
    inline WDBMAttrValue(DVec3&& data);
    /**
     * @brief 指定 DQuat 值构造
    */
    inline WDBMAttrValue(const DQuat& data);
    /**
     * @brief 指定 DQuat 值移动构造
    */
    inline WDBMAttrValue(DQuat&& data);
    /**
     * @brief 指定 Color 值构造
    */
    inline WDBMAttrValue(const Color& data);
    /**
     * @brief 指定 Color 值移动构造
    */
    inline WDBMAttrValue(Color&& data);
    /**
     * @brief 指定 WDUuid 值构造
    */
    inline WDBMAttrValue(const WDUuid& data);
    /**
     * @brief 指定 WDUuid 值移动构造
    */
    inline WDBMAttrValue(WDUuid&& data);
    /**
     * @brief 指定 WDBMLevelRange 值构造
    */
    inline WDBMAttrValue(const WDBMLevelRange& data);
    /**
     * @brief 指定 WDBMLevelRange 值移动构造
    */
    inline WDBMAttrValue(WDBMLevelRange&& data);
    /**
     * @brief 指定 WDBMNodeRef 值构造
    */
    inline WDBMAttrValue(const WDBMNodeRef& data);
    /**
     * @brief 指定 WDBMNodeRef 值移动构造
    */
    inline WDBMAttrValue(WDBMNodeRef&& data);
    /**
     * @brief 指定 std::vector<int> 值构造
    */
    inline WDBMAttrValue(const std::vector<int>& data);
    /**
     * @brief 指定 std::vector<int> 值移动构造
    */
    inline WDBMAttrValue(std::vector<int>&& data);
    /**
     * @brief 指定 std::vector<double> 值构造
    */
    inline WDBMAttrValue(const std::vector<double>& data);
    /**
     * @brief 指定 std::vector<double> 值移动构造
    */
    inline WDBMAttrValue(std::vector<double>&& data);
    /**
     * @brief 指定 std::vector<std::string> 值构造
    */
    inline WDBMAttrValue(const std::vector<std::string>& data);
    /**
     * @brief 指定 std::vector<double> 值移动构造
    */
    inline WDBMAttrValue(std::vector<std::string>&& data);
    /**
     * @brief 指定 WDBMNodeRefs 值构造
    */
    inline WDBMAttrValue(const WDBMNodeRefs& data);
    /**
     * @brief 指定 WDBMNodeRefs 值移动构造
    */
    inline WDBMAttrValue(WDBMNodeRefs&& data);
    /**
     * @brief 指定 WDBMGeometryRef 值构造
    */
    inline WDBMAttrValue(const WDBMGeometryRef& data);
    /**
     * @brief 指定 WDBMGeometryRef 值移动构造
    */
    inline WDBMAttrValue(WDBMGeometryRef&& data);
    /**
     * @brief 指定值引用对象构造
    */
    WDBMAttrValue(const WDBMAttrValueCRef& ref);
    /**
     * @brief 拷贝构造
    */
    WDBMAttrValue(const WDBMAttrValue& right);
    /**
     * @brief 移动构造
    */
    WDBMAttrValue(WDBMAttrValue&& right);
    /**
     * @brief 赋值 WDBMAttrValueRef
    */
    WDBMAttrValue& operator=(const WDBMAttrValueCRef& ref);
	/**
	 * @brief 赋值 WDBMAttrValue
	*/
    WDBMAttrValue& operator=(const WDBMAttrValue& right);
    /**
     * @brief 移动赋值 WDBMAttrValue
    */
    WDBMAttrValue& operator=(WDBMAttrValue&& right);
public:
	/**
	 * @brief 值是否有效
	*/
    inline bool valid() const;
    /**
     * @brief 获取当前值的类型
    */
    constexpr inline Type type() const;

    /**
     * @brief 当前值是否是指定类型
    */
	template <typename T>
    inline bool is() const;

    /**
     * @brief 获取指定类型的值
     * @return 如果值有效且是指定类型，则返回类型的值的指针，否则返回nullptr
    */
	template <typename T>
    inline const T* data() const;

    /**
     * @brief 获取bool类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是bool类型时, 转换失败, 固定返回false
    */
    bool toBool(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取bool类型的值
     * @param outValue 输出的bool值
     * @return 是否获取成功
    */
    bool toBool(bool& outValue) const;
    /**
     * @brief 获取int类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是int类型时, 转换失败, 固定返回0
    */
    int toInt(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取int类型的值
     * @param outValue 输出的int值
     * @return 是否获取成功
    */
    bool toInt(int& outValue) const;
    /**
     * @brief 获取double类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是double类型时, 转换失败, 固定返回0.0
    */
    double toDouble(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取double类型的值
     * @param outValue 输出的double值
     * @return 是否获取成功
    */
    bool toDouble(double& outValue) const;
    /**
     * @brief 获取std::string类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是std::string类型时, 转换失败, 固定返回""
    */
    std::string toString(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取std::string类型的值
     * @param outValue 输出的std::string值
     * @return 是否获取成功
    */
    bool toString(std::string& outValue) const;
    /**
     * @brief 获取WDBMWord类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是WDBMWord类型时, 转换失败, 固定返回WDBMWord("")
    */
    WDBMWord toWord(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取WDBMWord类型的值
     * @param outValue 输出的WDBMWord值
     * @return 是否获取成功
    */
    bool toWord(WDBMWord& outValue) const;
    /**
     * @brief 获取DVec2类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是DVec2类型时, 转换失败, 固定返回DVec2::Zero()
    */
    DVec2 toDVec2(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取DVec2类型的值
     * @param outValue 输出的DVec2值
     * @return 是否获取成功
    */
    bool toDVec2(DVec2& outValue) const;
    /**
     * @brief 获取DVec3类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是DVec3类型时, 转换失败, 固定返回DVec3::Zero()
    */
    DVec3 toDVec3(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取DVec3类型的值
     * @param outValue 输出的DVec3值
     * @return 是否获取成功
    */
    bool toDVec3(DVec3& outValue) const;
    /**
     * @brief 获取DQuat类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是DQuat类型时, 转换失败, 固定返回DQuat::Identity()
    */
    DQuat toQuat(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取DQuat类型的值
     * @param outValue 输出的DQuat值
     * @return 是否获取成功
    */
    bool toQuat(DQuat& outValue) const;
    /**
     * @brief 获取Color类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是Color类型时, 转换失败, 固定返回Color::white
    */
    Color toColor(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取Color类型的值
     * @param outValue 输出的Color值
     * @return 是否获取成功
    */
    bool toColor(Color& outValue) const;
    /**
     * @brief 获取WDUuid类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是WDUuid类型时, 转换失败, 固定返回WDUuid::Null()
    */
    WDUuid toUuid(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取WDUuid类型的值
     * @param outValue 输出的WDUuid值
     * @return 是否获取成功
    */
    bool toUuid(WDUuid& outValue) const;
    /**
     * @brief 获取WDBMLevelRange类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是WDBMLevelRange类型时, 转换失败, 固定返回WDBMLevelRange()
    */
    WDBMLevelRange toLevelRange(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取WDBMLevelRange类型的值
     * @param outValue 输出的WDBMLevelRange值
     * @return 是否获取成功
    */
    bool toLevelRange(WDBMLevelRange& outValue) const;
    /**
     * @brief 获取WDBMNodeRef类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是WDBMNodeRef类型时, 转换失败, 固定返回WDBMNodeRef()
    */
    WDBMNodeRef toNodeRef(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取WDBMNodeRef类型的值
     * @param outValue 输出的WDBMNodeRef值
     * @return 是否获取成功
    */
    bool toNodeRef(WDBMNodeRef& outValue) const;
    /**
     * @brief 获取std::vector<int>类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是std::vector<int>类型时, 转换失败, 固定返回std::vector<int>()
    */
    std::vector<int> toIntVector(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取std::vector<int>类型的值
     * @param outValue 输出的std::vector<int>值
     * @return 是否获取成功
    */
    bool toIntVector(std::vector<int>& outValue) const;
    /**
     * @brief 获取std::vector<double>类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是std::vector<double>类型时, 转换失败, 固定返回std::vector<double>()
    */
    std::vector<double> toDoubleVector(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取std::vector<double>类型的值
     * @param outValue 输出的std::vector<double>值
     * @return 是否获取成功
    */
    bool toDoubleVector(std::vector<double>& outValue) const;
    /**
     * @brief 获取std::vector<std::string>类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是std::vector<std::string>类型时, 转换失败, 固定返回std::vector<std::string>()
    */
    std::vector<std::string> toStringVector(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取std::vector<std::string>类型的值
     * @param outValue 输出的std::vector<std::string>值
     * @return 是否获取成功
    */
    bool toStringVector(std::vector<std::string>& outValue) const;
    /**
     * @brief 获取WDBMNodeRefs类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是WDBMNodeRefs类型时, 转换失败, 固定返回WDBMNodeRefs()
    */
    WDBMNodeRefs toNodeRefVector(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取WDBMNodeRefs类型的值
     * @param outValue 输出的WDBMNodeRefs值
     * @return 是否获取成功
    */
    bool toNodeRefVector(WDBMNodeRefs& outValue) const;
    /**
     * @brief 获取WDBMGeometryRef类型的值
     * @param bOk 可选项, 是否转换成功
     * @return 当前值如果不是WDBMGeometryRef类型时, 转换失败, 固定返回WDBMGeometryRef()
    */
    WDBMGeometryRef toGeometryRef(bool* bOk = nullptr) const;
    /**
     * @brief 通过引用获取WDBMGeometryRef类型的值
     * @param outValue 输出的WDBMGeometryRef值
     * @return 是否获取成功
    */
    bool toGeometryRef(WDBMGeometryRef& outValue) const;

    /**
     * @brief 当前值转换到字符串
     * @param bOk 是否转换成功,可选项,属性值无效时，转换失败
     * @return 转换完的字符串, 转换失败时，返回空字符串
    */
    std::string convertToString(bool* bOk = nullptr) const;
    /**
     * @brief 当前值从字符串转换
     * @param value 字符串值
     * @param bOk 可选项, 是否转换成功
     * @return 当前对象引用
    */
    WDBMAttrValue& convertFromString(const std::string_view& value, bool* bOk = nullptr);
    /**
    * @brief 当前值转换到double类型
    * @return 注意: 此转换要求类型是数值类型或者是数值类型的字符串
    */
    double convertToDouble(bool* bOk = nullptr) const;
    /**
    * @brief 当前值转换到int类型
    * @return 注意: 此转换要求类型是数值类型或者是数值类型的字符串
    */
    int convertToInt(bool* bOk = nullptr) const;
public:
    /**
     * @brief ==
    */
    bool operator==(const WDBMAttrValue& right) const;
    /**
     * @brief !=
    */
    bool operator!=(const WDBMAttrValue& right) const;
    /**
     * @brief >
    */
    bool operator>(const WDBMAttrValue& right) const;
    /**
     * @brief <
    */
    bool operator<(const WDBMAttrValue& right) const;
    /**
     * @brief >=
    */
    bool operator>=(const WDBMAttrValue& right) const;
    /**
     * @brief <=
    */
    bool operator<=(const WDBMAttrValue& right) const;
public:
    /**
     * @brief 将枚举类型转换为字符串表示
    */
    static const char* TypeToStr(Type type);
    /**
     * @brief 将字符串表示转换为枚举类型
    */
    static Type TypeFromStr(const char* type);
    /**
     * @brief 获取某种类型的默认值
    */
    template <typename T>
    static T DefaultValue();
private:
    /**
     * @brief 属性的值数据
    */
    using Data = std::variant<std::monostate
        , bool
        , int
        , double
        , std::string
        , WDBMWord
        , DVec2
        , DVec3
        , DQuat
        , Color
        , WDUuid
        , WDBMLevelRange
        , WDBMNodeRef
        , std::vector<int>
        , std::vector<double>
        , std::vector<std::string>
        , WDBMNodeRefs
        , WDBMGeometryRef>;
    // 值数据
    Data _data;
};

WD_NAMESPACE_END

#include "WDBMAttrValue.inl"

