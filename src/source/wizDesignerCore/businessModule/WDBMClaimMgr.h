#pragma once

#include "../node/WDNode.h"

WD_NAMESPACE_BEGIN

class WDBMBase;

/**
* @brief 申领管理
*/
class WD_API WDBMClaimMgr
{
public:
    /**
     * @brief 节点列表
     */
    using NodeSet = std::set<WDNode::SharedPtr>;
public:
    /**
     * @brief 一个签入项
     */
    struct WD_API CheckItem
    {
        /**
         * @brief 要签入的节点
         */
        WDNode::SharedPtr pNode = nullptr;
        /**
         * @brief 是否递归节点的所有子孙节点进行签入
         */
        bool bRecursionCheckIn = false;
        /**
         * @brief 要申领的节点是否是校审对象节点
         */
        bool isAudit = false;
        /**
         * @brief 构造
         */
        explicit CheckItem(WDNode::SharedPtr pNode
            , bool isAudit
            , bool bRecursionCheckIn = false);
    };
    /**
     * @brief 签入项列表
     */
    using CheckItems = std::vector<CheckItem>;
    /**
     * @brief 签入之前通知, 不用校验具体的属性修改
     * @return 签入成功的item索引
     */
    using FuncCheckInBefore = std::function<std::vector<size_t>(const CheckItems& items, bool isShowMessage)>;
    /**
     * @brief 属性名称列表
     */
    using AttrNames = std::set<std::string>;
    /**
     * @brief 一个属性改变的签入项
     */
    struct WD_API AttrCheckItem
    {
        /**
         * @brief 要签入的节点
         */
        WDNode::SharedPtr pNode = nullptr;
        /**
         * @brief 修改前的属性值以及对应的属性名称
         */
        AttrNames atrrNames;
        /**
         * @brief 被修改了属性值的节点
         *  正常来说(层级节点)，这个节点与pNode是同一个节点
         *  但是由于校审对象的特殊原因
         *  申领的节点(pNode)可能是校审对象类型节点, 而属性改变的节点(pAttrChangedNode)可能是
         *  校审对象类型的子孙节点，因此判断属性的改变需要用该节点来完成
         */
        WDNode::SharedPtr pAttrChangedNode = nullptr;
        /**
         * @brief 要申领的节点是否是校审对象节点
         */
        bool isAudit = false;
        /**
         * @brief 构造
         */
        explicit AttrCheckItem(WDNode::SharedPtr pNode
            , const AttrNames& atrrNames
            , WDNode::SharedPtr pAttrChangedNode
            , bool isAudit);
    };
    /**
     * @brief 属性改变的签入项列表
     */
    using AttrCheckItems = std::vector<AttrCheckItem>;
    /**
     * @brief 签入之前通知, 这里指定了具体的属性修改，需要在回调里处理属性覆盖问题
     */
    using FuncCheckInAttrBefore = std::function<std::vector<size_t>(const AttrCheckItems& items, bool isShowMessage, bool& cancelModify)>;
    /**
     * @brief 签出之前通知
     *  具体需要根据签入签出的文档来顶
     * @param items 签出的项列表
     * @param cancelCheckOut 是否选择了取消签出
     * @return 签出成功的节点列表
     */
    using FuncCheckOutBefore = std::function<std::vector<size_t>(const CheckItems& items, bool isShowMessage, bool& cancelCheckOut)>;
public:
    WDBMClaimMgr(WDBMBase& bmBase);
    ~WDBMClaimMgr();
public:
    /**
    * @brief 获取申领校验是否启用, 默认为禁用
    */
    inline bool enabled() const
    {
        return _enabled;
    }
    /**
    * @brief 设置申领校验是否启用, 默认为禁用
    *  如果未启用, 签入签出总是成功
    */
    inline void setEnabled(bool enabled)
    {
        _enabled = enabled;
    }
    /**
    * @brief 签入之前回调
    */
    inline FuncCheckInBefore& funcCheckInBefore()
    {
        return _funcCheckInBefore;
    }
    /**
    * @brief 签入之前回调, 属性改变
    */
    FuncCheckInAttrBefore& funcCheckInAttrBefore()
    {
        return _funcCheckInAttrBefore;
    }
    /**
    * @brief 签出之前回调
    */
    inline FuncCheckOutBefore& funcCheckOutBefore()
    {
        return _funcCheckOutBefore;
    }
    /**
     * @brief 获取所有已被签入的节点
     */
    inline const NodeSet& nodes() const
    {
        return _nodes;
    }
public:
    /**
     * @brief 添加校验的数据
     */
    struct AddData
    {
        WDNode::SharedPtr pParentNode = nullptr;
        WDNode::SharedPtr pNextNode = nullptr;
        AddData(WDNode::SharedPtr pParentNode, WDNode::SharedPtr pNextNode = nullptr)
            : pParentNode(pParentNode)
            , pNextNode(pNextNode)
        {

        }
    };
    using AddDatas = std::vector<AddData>;
    /**
     * @brief 批量节点创建的签入校验
     *  如果出现部分签入失败, 目前会保留所有签入成功的节点列表
     * @return 只要有一个不能被创建，返回false
     */
    bool checkAdd(const AddDatas& addDatas, bool bShowMessage = true);
    /**
     * @brief 节点创建的签入校验
     *  如果出现部分签入失败, 目前会保留所有签入成功的节点列表
     * @param pParentNode 要创建节点的父节点
     * @param nextNode 后置兄弟节点, 如果节点默认加在末尾时，后置兄弟节点可以指定为nullptr
     * @return 是否可以被创建
     */
    inline bool checkAdd(WDNode::SharedPtr pParentNode, WDNode::SharedPtr pNextNode = nullptr, bool bShowMessage = true)
    {
        assert(pParentNode != nullptr);
        return checkAdd({ AddData(pParentNode, pNextNode) }, bShowMessage);
    }
    /**
     * @brief 属性更新校验的数据
     */
    struct AttrData
    {
        // 更新的节点
        WDNode::SharedPtr pNode = nullptr;
        // 更新的属性名称列表
        AttrNames attrNames;
        AttrData(WDNode::SharedPtr pNode, const AttrNames& attrNames)
            : pNode(pNode)
            , attrNames(attrNames)
        {

        }
    };
    using AttrDatas = std::vector<AttrData>;
    /**
     * @brief 批量节点属性更新的签入校验
     *  如果出现部分签入失败, 目前会保留所有签入成功的节点列表
     * @param attrDatas 属性更新校验的数据列表
     * @param bCancelModify 当要修改的某个属性再服务端已被别人修改之后，这个选项控制是否取消此次的属性修改
     *  也就是说 bCancelModify 返回的值为true时，将使用服务端的数据，调用方因该中断此次属性的修改
     * @return 只要有一个不能被修改属性，返回false
     */
    bool checkUpdate(const AttrDatas& attrDatas, bool& bCancelModify, bool bShowMessage = true);
    /**
     * @brief 节点属性更新的签入校验
     * @param pNode 更新属性的节点
     * @param aValues 修改前的属性值
     * @param bCancelModify 当要修改的某个属性再服务端已被别人修改之后，这个选项控制是否取消此次的属性修改
     *  也就是说 bCancelModify 返回的值为true时，将使用服务端的数据，调用方因该中断此次属性的修改
     * @return 是否可以被修改属性
     */
    inline bool checkUpdate(WDNode::SharedPtr pNode, const AttrNames& attrNames, bool& bCancelModify, bool bShowMessage = true)
    {
        assert(pNode != nullptr && !attrNames.empty());
        return checkUpdate({ AttrData(pNode, attrNames)}, bCancelModify, bShowMessage);
    }
    /**
     * @brief 移动校验的数据
     */
    struct MoveData
    {
        // 发生移动的节点
        WDNode::SharedPtr pNode = nullptr;
        // 目标父节点
        WDNode::SharedPtr pTarParent = nullptr;
        // 目标后置兄弟节点
        WDNode::SharedPtr pTarNext = nullptr;
        // 更新的属性名称列表
        MoveData(WDNode::SharedPtr pNode, WDNode::SharedPtr pTarParent, WDNode::SharedPtr pTarNext)
            : pNode(pNode)
            , pTarParent(pTarParent)
            , pTarNext(pTarNext)
        {

        }
    };
    using MoveDatas = std::vector<MoveData>;
    /**
     * @brief 批量节点移动的签入校验
     * @param pNode 要移动的节点
     * @param pTarParent 目标父节点, 必须传入有效的目标父节点
     * @param pTarNext 目标后置兄弟节点, 当目标位置没有后置兄弟节点(所有子列表的最后一个)时, 传入nullptr
     * @param bShowMessage 是否显示提示信息
     * @return 是否可以被移动
     */
    bool checkMove(const MoveDatas& moveDatas, bool bShowMessage = true);
    /**
     * @brief 批量节点移动的签入校验
     * @param pNode 要移动的节点
     * @param pTarParent 目标父节点
     * @param pTarNext 目标后置兄弟节点
     * @param bShowMessage 是否显示提示信息
     * @return 是否可以被移动
     */
    inline bool checkMove(WDNode::SharedPtr pNode, WDNode::SharedPtr pTarParent, WDNode::SharedPtr pTarNext, bool bShowMessage = true)
    {
        return checkMove({ MoveData(pNode, pTarParent, pTarNext) }, bShowMessage);
    }
    /**
     * @brief 删除校验的数据
     */
    using DelDatas = std::vector<WDNode::SharedPtr>;
    /**
     * @brief 批量节点删除的签入校验
     *  如果出现部分签入失败, 目前会保留所有签入成功的节点列表
     * @param delDatas 要删除的节点列表
     * @return 只要有一个不能被删除，返回false
     */
    bool checkDelete(const DelDatas& delDatas, bool bShowMessage = true);
    /**
     * @brief 节点删除的签入校验
     * @param pNode 要删除的节点
     * @return 是否可以被删除
     */
    inline bool checkDelete(WDNode::SharedPtr pNode, bool bShowMessage = true)
    {
        assert(pNode != nullptr);
        DelDatas dDatas = { pNode };
        return checkDelete(dDatas, bShowMessage);
    }
    /**
     * @brief 签出
     * @param nodes 要签出的节点列表
     * @param bCancelCheckOut 是否选择了取消签出
     * @return 签出成功的节点列表
     */
    WDNode::Nodes checkOut(const WDNode::Nodes& nodes, bool& bCancelCheckOut, bool bShowMessage = true);
    /**
     * @brief 签出所有已被签入的节点
     */
    bool checkOutAll(bool& bCancelCheckOut, bool bShowMessage = true);
public:
    /**
     * @brief 加入一个已被申领的节点
     *  注意: 这里是直接向本地添加, 不会与服务交互
     */
    void add(WDNode::SharedPtr pNode);
    /**
     * @brief 移除一个已被申领的节点
     *  注意: 这里是直接从本地移除, 不会与服务交互
     */
    void remove(WDNode::SharedPtr pNode);
    /**
     * @brief 查询节点是否已被申领
     *  注意: 这里是直接在本地查询, 不会与服务交互
     * @return 已被申领返回true, 未被申领或节点无效返回false
     */
    bool contains(WDNode::SharedPtr pNode) const;
public:
    struct CBDeleteNode
    {
        // 删除节点的父节点rid
        int64_t parRid;
        // 删除节点
        WDNode::SharedPtr pNode;
    };
    using MapCBDeleteNode = std::map<uint64_t, CBDeleteNode>;
    /**
     * @brief 节点集合
     */
    struct WD_API PNodeSet
    {
    public:
        PNodeSet(const WDBMClaimMgr& mgr);
        ~PNodeSet();
    public:
        /**
         * @brief 批量添加
         */
        void add(const WDNode::Nodes& nodes);
        /**
        * @brief 批量添加，递归子孙节点加入
        */
        void addRecursion(const WDNode::Nodes& nodes);
        /**
         * @brief 批量移除
         */
        void remove(const WDNode::Nodes& nodes);
        /**
        * @brief 批量移除
        */
        void removeRecursion(const WDNode::Nodes& nodes);
        /**
         * @brief 清除
         */
        void clear();
        /**
         * @brief 是否包含
         */
        bool contains(WDNode::SharedPtr pNode) const;
        /**
         * @brief 获取所有节点
         */
        const NodeSet& nodes() const;

    private:
        /**
         * @brief 递归操作节点集合，递归当前节点并从节点集合中应用由用户传入的函数对象
         * @param pNode 节点
         * @param funcManage 对节点集合的操作，如nodeSet.erase(node);
         */
        void operateNodeSetRecur(WDNode::SharedPtr pNode, const std::function<void(WD::WDNode::SharedPtr tarNode)>& funcOperation);
        
    private:
        const WDBMClaimMgr& _mgr;
        NodeSet _nodes;
    };
    /**
     * @brief 用于记录新创建的节点集合
     */
    inline const PNodeSet& newCreatedNodes() const 
    {
        return _newCreatedNodes;
    }
    /**
     * @brief 用于记录新创建的节点集合
     */
    PNodeSet& newCreatedNodes()
    {
        return _newCreatedNodes;
    }
    /**
     * @brief 用于记录删除的节点集合
     */
    const MapCBDeleteNode& deletedNodes() const
    {
        return _deletedNodes;
    }
    /**
     * @brief 用于记录删除的节点集合
     */
    MapCBDeleteNode& deletedNodes()
    {
        return _deletedNodes;
    }
private:
    // 只要申领的节点或新建的节点列表中包含该节点，则返回true，否则返回false
    inline bool aContains(WDNode::SharedPtr pNode) const
    {
        if (pNode == nullptr)
            return false;
        return this->contains(pNode) || newCreatedNodes().contains(pNode);
    }
    // 递归判断当前节点(当前节点必须保证是层级节点)以及其所有子孙节点是否均被申领, 均被申领返回true
    bool RecursionCheck(WDNode::SharedPtr pNode);
private:
    // 所属模块
    WDBMBase& _bmBase;
    // 申领校验是否启用
    bool _enabled;
    // 已被申领的节点列表
    NodeSet _nodes;
    // 签入之前通知
    FuncCheckInBefore _funcCheckInBefore;
    // 签入之前通知, 属性改变签入
    FuncCheckInAttrBefore _funcCheckInAttrBefore;
    // 签出之后通知
    FuncCheckOutBefore _funcCheckOutBefore;

    // 新创建的节点缓存
    PNodeSet _newCreatedNodes;
    // 要删除的节点缓存
    MapCBDeleteNode _deletedNodes;
};

WD_NAMESPACE_END