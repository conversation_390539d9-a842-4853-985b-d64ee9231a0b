#include "LoftBuilder.h"

WD_NAMESPACE_BEGIN

MeshStruct LoftBuilder::Mesh(const FVec3& sDirection
    , const FVec3& eDirection
    , const FVec3& plaxisPos
    , const FVec3Vector& loopS
    , const FVec3Vector& loopE
    , const FVec3Vector& curve
    , const MeshLODSelection& lodSelection)
{
    MeshStruct ret;
    // 去除相邻两点之间距离过近的点
    std::vector<FVec3> tCurve;
    tCurve.reserve(curve.size());
    for (int i = 0; i < curve.size(); ++i)
    {
        auto& pt = curve[i];
        if (tCurve.empty())
            tCurve.push_back(pt);
        // 如果距离过近，则抛弃当前点
        else if (FVec3::DistanceSq(tCurve.back(), pt) <= NumLimits<float>::Epsilon)
            continue;
        // 如果超过2个点,且当前点的角度为180度,跳过当前点
        else if (curve.size() > 2
            && (i > 0)
            && (i < curve.size() - 1)
            && FVec3::InTheOppositeDirection(FVec3::Normalize(curve[i - 1] - pt), FVec3::Normalize(curve[i + 1] - pt)))
            continue;
        else
            tCurve.push_back(pt);
    }
    // 至少需要两个点
    if (tCurve.size() < 2)
        return ret;

    FVec2Vector outLoopS;
    if (!BuildLoop(loopS, outLoopS, lodSelection))
        return ret;

    FVec2Vector outLoopE;
    if (!BuildLoop(loopE, outLoopE, lodSelection))
        return ret;

    if (outLoopS.size() != outLoopE.size())
        return ret;

    FVec3Vector tVerticesS;
    tVerticesS.reserve(outLoopS.size());

    FVec3Vector tVerticesE;
    tVerticesE.reserve(outLoopE.size());

    size_t tSideLineCnt = 0;
    for (size_t i = 0; i < outLoopS.size(); ++i)
    {
        // 起始环
        {
            const auto& v = outLoopS[i];
            const auto& vPrev = outLoopS[i == 0 ? outLoopS.size() - 1 : i - 1];
            const auto& vNext = outLoopS[i == outLoopS.size() - 1 ? 0 : i + 1];

            const auto vecPrev = vPrev - v;
            const auto vecNext = vNext - v;
            float tmpAngle = FVec2::Angle(vecPrev, vecNext);

            bool bSideLine = tmpAngle < 160.0f;
            tVerticesS.push_back(FVec3(v, bSideLine ? 1.0f : 0.0f));
            if (bSideLine)
                tSideLineCnt++;
        }
        // 结束环
        {
            const auto& v = outLoopE[i];

            tVerticesE.push_back(FVec3(v));
        }
    }

    // 如果没有生成边线，默认计算四条
    if (tSideLineCnt == 0) 
    {
        tVerticesS[0].z = 1.0f;
        tVerticesS[(tVerticesS.size() * 1) / 4].z = 1.0f;
        tVerticesS[(tVerticesS.size() * 2) / 4].z = 1.0f;
        tVerticesS[(tVerticesS.size() * 3) / 4].z = 1.0f;
    }


    // 对多边形进行三角细分,获取顶点索引
    std::vector<uint> tIndicesS = TEarcut<FVec3, uint>::Exec(tVerticesS);
    std::vector<uint> tIndicesE = TEarcut<FVec3, uint>::Exec(tVerticesE);

    const uint vertCount = static_cast<uint>(tVerticesS.size());
    const uint curveVCount = static_cast<uint>(tCurve.size());
    // 这里给顶点和索引尽可能分配比较大的内存,以加快生成速度
    std::vector<FVec3> tPositions;
    tPositions.reserve(vertCount * tCurve.size()); // 顶面，底面以及侧面
    std::vector<FVec3> tNormals;
    tNormals.reserve(vertCount * tCurve.size()); // 顶面，底面以及侧面
    std::vector<uint>  tTriIndices;
    tTriIndices.reserve(static_cast<uint>(tIndicesS.size()) * 2 + vertCount * 6 * tCurve.size()); // 底面，底面以及侧面
    std::vector<uint>  tSideIndices;
    tSideIndices.reserve(vertCount * 2 + loopS.size() * 3 * tCurve.size()); //顶面，底面，以及侧面(假定loop的每个顶点都带有圆角时，每个顶点都会生成三条边线)
    // 是否需要端面 : 如果顶面和底面距离极近说明两面重合,不需要生成端面
    bool bNeedEnds = FVec3::DistanceSq(tCurve.front(), tCurve.back()) > NumLimits<float>::Epsilon;
    // 顶点位置和法线
    // 这里根据侧面的每一条棱来添加顶点，即一条棱添加一个底部顶点和顶部顶点
    // 但是因为可能存在顶点法线和面法线
    //  所以:
    //      如果是面法线，需要给这条棱上分别添加两次底部顶点和顶部顶点(虽然顶点位置相同，但是法线不同)
    //      如果是顶点法线,只需要给这条棱上添加一次底部顶点和顶部顶点(位置相同，法线也相同)
    // first : 判断每个点是否需要面法线并保存
    // second : 判断每个点是否需要生成边线
    std::vector<std::pair<bool, bool>> bNeedFaceNors;
    bNeedFaceNors.reserve(vertCount);
    // 需要面法线时点会添加两次,这里记录所需要的点的数量,便于查找当前点在下一个面时的的索引
    uint vertSize = 0;
    for (uint i = 0; i < vertCount; ++i)
    {
        // 前一个顶点索引
        uint ptIndexPrev = i == 0 ? vertCount - 1 : i - 1;
        // 下一个顶点索引       
        uint ptIndexNext = i == vertCount - 1 ? 0 : i + 1;
        // 获取顶点
        const FVec3& pt = tVerticesS[i];
        // 使用 当前、前一个、下一个底部顶点构成的两个方向向量的夹角计算该棱上的两个顶点是需要顶点法线还是需要面法线
        // 是否需要面法线, 如果不是面法线则认为是需要顶点法线
        bool bFaceNor = FVec2::Angle(tVerticesS[ptIndexPrev].xy() - pt.xy()
            , tVerticesS[ptIndexNext].xy() - pt.xy()) < 160.0f;
        // 需要面法线，会添加两次顶点
        bNeedFaceNors.push_back(std::make_pair(bFaceNor, pt.z > 0));
        vertSize += bFaceNor ? 2 : 1;
    }
    // 当前已经添加的顶点个数
    uint pointCount = 0;
    for (uint curveIdx = 0; curveIdx < curveVCount; ++curveIdx)
    {
        // 记录端面法向
        FVec3 zDirection = FVec3::AxisZ();
        // 第一个点,即底面
        if (curveIdx == 0)
        {
            zDirection = WD::FVec3::Normalize(tCurve[1] - tCurve[0]);
            FPlane plane(sDirection, tCurve[0]);
            FMat3 mat = FMat3();
            // 计算底面时需要对面进行旋转
            if (curve.size() == 2 && FVec3::DistanceSq(tCurve[0], tCurve[1]) > 0.01)
                // 工字钢
                mat = FMat3::MakeRotationUseDirectionZ(zDirection);
            else
                // 曲线
                mat = FMat3::MakeRotationYZ(FVec3::AxisZ(), zDirection);
            for (auto& each : tVerticesS)
            {
                each = tCurve[0] + mat * FVec3(each.xy(), 0.0);
                FRay ray(each, zDirection);
                auto result = ray.intersect(plane);
                if (!result.first)
                {
                    // 底面有可能相交失败，这里把向量反向重新尝试相交
                    ray = FRay(each, -zDirection);
                    result = ray.intersect(plane);
                    assert(result.first);
                    if (!result.first)
                        return ret;
                }
                each = ray.at(result.second);
            }
            for (auto& each : tVerticesE)
            {
                each = tCurve[0] + mat * FVec3(each.xy(), 0.0);
                FRay ray(each, zDirection);
                auto result = ray.intersect(plane);
                if (!result.first)
                {
                    // 底面有可能相交失败，这里把向量反向重新尝试相交
                    ray = FRay(each, -zDirection);
                    result = ray.intersect(plane);
                    assert(result.first);
                    if (!result.first)
                        return ret;
                }
                each = ray.at(result.second);
            }
            // 计算底面,即曲线起点所在的端面,曲线起点和终点重合(或者及其接近)时，不会生成顶面
            if (bNeedEnds)
            {
                for (size_t vertIdx = 0; vertIdx < vertCount; ++vertIdx)
                {
                    tPositions.push_back(tVerticesS[vertIdx]);
                    tNormals.push_back(sDirection);
                    // 边线索引
                    tSideIndices.push_back(uint(vertIdx));
                    tSideIndices.push_back(uint((vertIdx == vertCount - 1) ? 0 : (vertIdx + 1)));
                }
                //三角面索引
                tTriIndices.insert(tTriIndices.end(), tIndicesS.rbegin(), tIndicesS.rend());
            }
            pointCount = static_cast<uint>(tPositions.size());
        }
        // 最后的点,即顶面
        else if (curveIdx == curveVCount - 1)
        {
            zDirection = eDirection;
            FVec3 preDir = FVec3::Normalize(tCurve[curveIdx - 1] - tCurve[curveIdx]);
            FPlane plane(-eDirection, tCurve[curveIdx]);
            for (size_t i = 0; i < tVerticesS.size(); ++i)
            {
                FRay ray(tVerticesE[i], -preDir);
                auto result = ray.intersect(plane);
                if (!result.first)
                {
                    // 顶面有可能相交失败，这里把向量反向重新尝试相交
                    ray = FRay(tVerticesE[i], preDir);
                    result = ray.intersect(plane);
                    assert(result.first);
                    if (!result.first)
                        return ret;
                }
                tVerticesS[i] = ray.at(result.second);
            }
        }
        else
        {
            FVec3 preDir = FVec3::Normalize(tCurve[curveIdx - 1] - tCurve[curveIdx]);
            zDirection = FVec3::Normalize(tCurve[curveIdx + 1] - tCurve[curveIdx]);
            FVec3 midDir = FVec3::Normalize(preDir + zDirection);
            FVec3 zDir = FVec3::Cross(zDirection, midDir).normalized();
            FPlane plane(FVec3::Normalize(FVec3::Cross(zDir, midDir)), tCurve[curveIdx]);
            for (auto& each : tVerticesS)
            {
                FRay ray(each, -preDir);
                auto result = ray.intersect(plane);
                if (!result.first)
                {
                    // 侧面相交失败属于错误情况，如创建环墙时，墙体厚度大于环墙半径
                    // 这里为了不影响建模，反向重新计算
                    ray = FRay(each, preDir);
                    result = ray.intersect(plane);
                    assert(result.first);
                    if (!result.first)
                        return ret;
                }
                each = ray.at(result.second);
            }
            for (auto& each : tVerticesE)
            {
                FRay ray(each, -preDir);
                auto result = ray.intersect(plane);
                if (!result.first)
                {
                    // 侧面相交失败属于错误情况，如创建环墙时，墙体厚度大于环墙半径
                    // 这里为了不影响建模，反向重新计算
                    ray = FRay(each, preDir);
                    result = ray.intersect(plane);
                    assert(result.first);
                    if (!result.first)
                        return ret;
                }
                each = ray.at(result.second);
            }
        }
        uint idx = 0;
        // 处理每个点
        for (uint vertIdx = 0; vertIdx < vertCount; ++vertIdx)
        {
            // 前一个顶点索引
            uint ptIndexPrev = vertIdx == 0 ? vertCount - 1 : vertIdx - 1;
            // 下一个顶点索引       
            uint ptIndexNext = vertIdx == vertCount - 1 ? 0 : vertIdx + 1;
            // 获取顶点
            const FVec3& pt = tVerticesS[vertIdx];
            // 获取向量
            const FVec3 vecPrev = tVerticesS[ptIndexPrev] - pt;
            const FVec3 vecNext = tVerticesS[ptIndexNext] - pt;
            // 分别计算左边面的法线和右边面的法线
            FVec3 faceNorL = FVec3::Cross(-vecPrev, zDirection).normalized();
            FVec3 faceNorR = FVec3::Cross(vecNext, zDirection).normalized();
            // 底面当前点下标
            uint idxB = curveIdx * vertSize + idx;
            if (bNeedFaceNors[vertIdx].first)
            {
                tPositions.push_back(pt);
                tPositions.push_back(pt);
                tNormals.push_back(faceNorL);
                tNormals.push_back(faceNorR);
                // 添加了两次顶点，这里计数额外加1
                idx++;
                idxB++ ;
            }
            // 需要顶点法线，直接计算当前顶点与前一个、下一个顶点构成的方向向量的角平分线
            else
            {
                FVec3 tNor = (faceNorL + faceNorR).normalized();
                tPositions.push_back(pt);
                tNormals.push_back(tNor);
            }
            idx++;
            // 底面当前点的下一个点下标,如果当前点是最后一个点,则使用第一个点的下标
            uint idxBNext = (idx == vertSize) ? (curveIdx * vertSize) : (idxB + 1);
            // 顶面当前点
            uint idxT = idxB + vertSize;
            // 顶面当前点的下一个点下标,如果当前点是最后一个点,则使用第一个点的下标
            uint idxTNext = idxBNext + vertSize;
            if (curveIdx == curveVCount - 1)
            {
                // 需要底面说明创建的放样体首尾不相连
                if (bNeedEnds)
                {
                    continue;
                }
                // 不需要底面说明创建的放样体首尾相连，这里用第一个面作为当前放样的顶面
                else
                {
                    idxT = idxB - vertSize * curveIdx;
                    idxTNext =  idxBNext - vertSize * curveIdx;
                }
            }
            // 边线
            if (bNeedFaceNors[vertIdx].second)
            {
                tSideIndices.push_back(pointCount + idxB);
                tSideIndices.push_back(pointCount + idxT);
            }

            tTriIndices.push_back(pointCount + idxT);
            tTriIndices.push_back(pointCount + idxB);
            tTriIndices.push_back(pointCount + idxBNext);

            tTriIndices.push_back(pointCount + idxT);
            tTriIndices.push_back(pointCount + idxBNext);
            tTriIndices.push_back(pointCount + idxTNext);
        }
    }
    // 计算顶面,及曲线终点所在的断面,曲线起点和终点重合(或者及其接近)时，不会生成顶面
    if (bNeedEnds)
    {
        // 统计当前的顶点个数
        pointCount = static_cast<uint>(tPositions.size());
        for (size_t vertIdx = 0; vertIdx < vertCount; ++vertIdx)
        {
            tPositions.push_back(tVerticesS[vertIdx]);
            tNormals.push_back(eDirection);
            // 边线索引
            tSideIndices.push_back(uint(pointCount + vertIdx));
            tSideIndices.push_back(uint(pointCount + ((vertIdx == vertCount - 1) ? 0 : (vertIdx + 1))));
        }
        //三角面索引
        for (auto& idx : tIndicesS)
            idx += pointCount;
        tTriIndices.insert(tTriIndices.end(), tIndicesS.begin(), tIndicesS.end());
    }

    ret.positions = std::move(tPositions);
    if (plaxisPos.lengthSq() > NumLimits<float>::Epsilon)
    {
        for (auto& each : ret.positions)
            each += plaxisPos;
    }
    ret.normals = std::move(tNormals);
    //三角面索引
    ret.triangles.setDrawElementUIntData(std::move(tTriIndices));
    ret.triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);
    //边线索引
    ret.wireFrames.setDrawElementUIntData(std::move(tSideIndices));
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);
    return ret;
}

MeshStruct LoftBuilder::SimpleMesh(const FVec3& sDirection
    , const FVec3& eDirection
    , const FVec3& plaxisPos
    , const FVec3Vector& loopS
    , const FVec3Vector& loopE
    , const FVec3Vector& curve
    , const MeshLODSelection& lodSelection)
{
    WDUnused4(sDirection, eDirection, plaxisPos, loopE);
    MeshStruct ret;
    // 去除相邻两点之间距离过近的点
    std::vector<FVec3> tCurve;
    tCurve.reserve(curve.size());
    for (int i = 0; i < curve.size(); ++i)
    {
        auto& pt = curve[i];
        if (tCurve.empty())
            tCurve.push_back(pt);
        // 如果距离过近，则抛弃当前点
        else if (FVec3::DistanceSq(tCurve.back(), pt) <= NumLimits<float>::Epsilon)
            continue;
        // 如果超过2个点,且当前点的角度为180度,跳过当前点
        else if (curve.size() > 2
            && (i > 0)
            && (i < curve.size() - 1)
            && FVec3::InTheOppositeDirection(FVec3::Normalize(curve[i - 1] - pt), FVec3::Normalize(curve[i + 1] - pt)))
            continue;
        else
            tCurve.push_back(pt);
    }
    // 至少需要两个点
    if (tCurve.size() < 2)
        return ret;
    auto curveCnt = tCurve.size();

    FVec2Vector outLoop;
    if (!BuildLoop(loopS, outLoop, lodSelection))
        return ret;

    // bool值用来标记是否生成边线
    std::vector < std::pair<FVec2, bool>> data;
    // 切分为多个凸多面体并得到索引顺序（每三个一组表示一个三角面）
    std::vector<uint> shapeIndices;

    // 计算对应顶点是否生成侧边线
    size_t tSideLineCnt = 0;
    for (size_t i = 0; i < outLoop.size(); ++i)
    {
        // 起始环
        {
            const auto& v = outLoop[i];
            const auto& vPrev = outLoop[i == 0 ? outLoop.size() - 1 : i - 1];
            const auto& vNext = outLoop[i == outLoop.size() - 1 ? 0 : i + 1];

            const auto vecPrev = vPrev - v;
            const auto vecNext = vNext - v;
            float tmpAngle = FVec2::Angle(vecPrev, vecNext);

            // 夹角较大则不需要生成边线
            bool bSideLine = tmpAngle < 160.0f;
            data.push_back({ v, bSideLine });
            if (bSideLine)
                tSideLineCnt++;
        }
    }

    // 如果没有生成边线，默认计算四条
    if (tSideLineCnt == 0)
    {
        data[0].second = true;
        data[data.size() * 1 / 4].second = true;
        data[data.size() * 2 / 4].second = true;
        data[data.size() * 3 / 4].second = true;
    }

    // 对多边形进行三角细分,获取顶点索引
    shapeIndices = TEarcut<FVec2, uint>::Exec(outLoop);

    FVec3 direction;// 面法线
    /// 考虑通过计算线与面的交点获取点(定点 + 方向确定一条线)
    FVec3 viaPoint; // 线经过的点
    FVec3 lineDir; // 线方向
    FVec3 prePoint; // 一条弧上的上一个点

    ret.positions.reserve(outLoop.size() * tCurve.size());
    //计算三维圆面的顶点
    for (const auto& v : outLoop)
    {
        for (int i = 0; i < tCurve.size(); i++)
        {
            // 计算法线与缩放
            const auto& it = tCurve[i];
            //求导线上某一点的切向
            //第一个点的切线，由自身和后面的点来确定；
            if (i == 0)
            {
                const auto& next = tCurve[i + 1];
                direction = FVec3::Normalize(next - it);
                auto quat = FQuat::FromVectors(FVec3::AxisZ(), direction);
                viaPoint = FMat4::FromQuat(quat) * FVec3(v) + it;
                lineDir = direction;
            }
            //最后一个点的切向，由自身和前面的点来确定
            else if (i == tCurve.size() - 1)
            {
                const auto& pre = tCurve[i - 1];
                direction = FVec3::Normalize(it - pre);
                viaPoint = prePoint;
                lineDir = direction;
            }
            //中间点切向，由自身和前后两个点来确定，方向取前半段和后半段的角平分线，半径需要缩放
            else
            {
                const auto& pre = tCurve[i - 1];
                const auto& next = tCurve[i + 1];
                auto dire1 = FVec3::Normalize(it - pre);
                auto dire2 = FVec3::Normalize(next - it);
                direction = FVec3::Normalize(dire1 + dire2);

                viaPoint = prePoint;
                lineDir = dire1;
            }

            // 线与面的交点（通过法线和过定点(it)确定一个面）
            auto temp = FVec3::Dot(it - viaPoint, direction) / FVec3::Dot(lineDir, direction);
            prePoint = viaPoint + lineDir * temp;
            ret.positions.push_back(prePoint);
        }
    }

    // 三角面索引
    auto sz = curveCnt;// 每条弧的顶点数
    auto numArcs = ret.positions.size() / sz;// 弧的条数
    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> tris;
        tris.reserve(shapeIndices.size() * 2 * 3 + numArcs * (sz - 1) * 6);
        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<byte>(shapeIndices[i + 2] * sz));
            tris.push_back(static_cast<byte>(shapeIndices[i + 1] * sz));
            tris.push_back(static_cast<byte>(shapeIndices[i + 0] * sz));
        }

        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<byte>(shapeIndices[i + 0] * sz + sz - 1));
            tris.push_back(static_cast<byte>(shapeIndices[i + 1] * sz + sz - 1));
            tris.push_back(static_cast<byte>(shapeIndices[i + 2] * sz + sz - 1));
        }

        for (int i = 0; i < numArcs; i++)
        {
            auto arcStart = i * sz;
            auto nextStart = (arcStart + sz) % ret.positions.size();
            for (int j = 0; j < sz - 1; j++)
            {
                auto p1 = arcStart + j;
                auto p2 = nextStart + j;
                //p1+1  p2+1   
                //p1    p2   
                tris.push_back(static_cast<byte>(p1 + 1));
                tris.push_back(static_cast<byte>(p2));
                tris.push_back(static_cast<byte>(p2 + 1));

                tris.push_back(static_cast<byte>(p1 + 1));
                tris.push_back(static_cast<byte>(p1));
                tris.push_back(static_cast<byte>(p2));
            }
        }
        ret.triangles.setDrawElementByteData(std::move(tris));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max)
    {
        std::vector<ushort> tris;
        tris.reserve(shapeIndices.size() * 2 * 3 + numArcs * (sz - 1) * 6);
        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<ushort>(shapeIndices[i + 2] * sz));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 1] * sz));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 0] * sz));
        }

        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<ushort>(shapeIndices[i + 0] * sz + sz - 1));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 1] * sz + sz - 1));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 2] * sz + sz - 1));
        }

        for (int i = 0; i < numArcs; i++)
        {
            auto arcStart = i * sz;
            auto nextStart = (arcStart + sz) % ret.positions.size();
            for (int j = 0; j < sz - 1; j++)
            {
                auto p1 = arcStart + j;
                auto p2 = nextStart + j;
                //p1+1  p2+1   
                //p1    p2   
                tris.push_back(static_cast<ushort>(p1 + 1));
                tris.push_back(static_cast<ushort>(p2));
                tris.push_back(static_cast<ushort>(p2 + 1));

                tris.push_back(static_cast<ushort>(p1 + 1));
                tris.push_back(static_cast<ushort>(p1));
                tris.push_back(static_cast<ushort>(p2));
            }
        }
        ret.triangles.setDrawElementUShortData(std::move(tris));
    }
    else
    {
        std::vector<uint> tris;
        tris.reserve(shapeIndices.size() * 2 * 3 + numArcs * (sz - 1) * 6);
        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<uint>(shapeIndices[i + 2] * sz));
            tris.push_back(static_cast<uint>(shapeIndices[i + 1] * sz));
            tris.push_back(static_cast<uint>(shapeIndices[i + 0] * sz));
        }

        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<uint>(shapeIndices[i + 0] * sz + sz - 1));
            tris.push_back(static_cast<uint>(shapeIndices[i + 1] * sz + sz - 1));
            tris.push_back(static_cast<uint>(shapeIndices[i + 2] * sz + sz - 1));
        }

        for (int i = 0; i < numArcs; i++)
        {
            auto arcStart = i * sz;
            auto nextStart = (arcStart + sz) % ret.positions.size();
            for (int j = 0; j < sz - 1; j++)
            {
                auto p1 = arcStart + j;
                auto p2 = nextStart + j;
                //p1+1  p2+1   
                //p1    p2   
                tris.push_back(static_cast<uint>(p1 + 1));
                tris.push_back(static_cast<uint>(p2));
                tris.push_back(static_cast<uint>(p2 + 1));
                                  
                tris.push_back(static_cast<uint>(p1 + 1));
                tris.push_back(static_cast<uint>(p1));
                tris.push_back(static_cast<uint>(p2));
            }
        }
        ret.triangles.setDrawElementUIntData(std::move(tris));
    }
    ret.triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);

    int count = 0;
    for (const auto& d : data)
    {
        if (d.second)
            count++;
    }
    // 边线
    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (int i = 0; i < numArcs; i++)
        {
            wireframes.push_back(static_cast<byte>(sz * i));
            wireframes.push_back(static_cast<byte>(((i + 1) % numArcs) * sz));

            wireframes.push_back(static_cast<byte>(sz * i + sz - 1));
            wireframes.push_back(static_cast<byte>(((i + 1) % numArcs) * sz + sz - 1));

            if (data[i].second)
            {
                // 生成侧边线
                for (int j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<byte>(sz * i + j));
                    wireframes.push_back(static_cast<byte>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementByteData(std::move(wireframes));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max) 
    {
        std::vector<ushort> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (int i = 0; i < numArcs; i++)
        {
            wireframes.push_back(static_cast<ushort>(sz * i));
            wireframes.push_back(static_cast<ushort>(((i + 1) % numArcs) * sz));

            wireframes.push_back(static_cast<ushort>(sz * i + sz - 1));
            wireframes.push_back(static_cast<ushort>(((i + 1) % numArcs) * sz + sz - 1));

            if (data[i].second)
            {
                // 生成侧边线
                for (int j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<ushort>(sz * i + j));
                    wireframes.push_back(static_cast<ushort>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementUShortData(std::move(wireframes));
    }
    else
    {
        std::vector<uint> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (int i = 0; i < numArcs; i++)
        {
            wireframes.push_back(static_cast<uint>(sz * i));
            wireframes.push_back(static_cast<uint>(((i + 1) % numArcs) * sz));

            wireframes.push_back(static_cast<uint>(sz * i + sz - 1));
            wireframes.push_back(static_cast<uint>(((i + 1) % numArcs) * sz + sz - 1));

            if (data[i].second)
            {
                // 生成侧边线
                for (int j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<uint>(sz * i + j));
                    wireframes.push_back(static_cast<uint>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementUIntData(std::move(wireframes));
    }
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);
    return ret;
}

WD_NAMESPACE_END