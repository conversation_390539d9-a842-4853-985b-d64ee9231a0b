#include "TriangularUtil.h"
#include "BasicDef.h"
#include "MathUtil.h"
#include <math.h>
#include "MeshObj.h"
namespace tri
{
	CTriangularUtil::CTriangularUtil(void)
	{
	}


	CTriangularUtil::~CTriangularUtil(void)
	{
	}

	bool CTriangularUtil::MeshDish(int count, FLOAT3D radius, bool isClockWise, const Matrix33 &R, const FLOAT3D3 &T,
		primitive::CMeshObj &mesh, int &vertexIdx, int &faceIdx)
	{
		FLOAT3D3 normal(0.0f, 0.0f, 1.0f);
		if (isClockWise)
		{
			normal.z = -1.0f;
		}
		normal.Normalize();
		normal = R.Inv().Transpose().mul(normal);

		int vertexStartIdx = vertexIdx;
		int vidx = vertexStartIdx;
		mesh.pVertex[vidx] = T;
		mesh.pNormal[vidx] = normal;

		vidx++;
		FLOAT3D3 tmpv(0.0f, 0.0f, 0.0f);
		FLOAT3D beta = 0.0f;
		for (int v = 0; v < count; ++v)
		{
#if 1
			beta = 2.0f * PI * (FLOAT3D)(v) / (FLOAT3D)(count);
			tmpv.x = radius * (float)cos(beta);
			tmpv.y = radius * (float)sin(beta);
			mesh.pVertex[vidx] = R.mul(tmpv) + T;
#else
			mesh.pVertex[vidx] = mesh.pVertex[vCopy + v];
#endif 
			mesh.pNormal[vidx] = normal;
			vidx++;
		}
		vertexIdx = vidx;

		int face_idx = faceIdx;
		for (int v = 0; v < count; ++v)
		{
			int p1 = (int)v;
			int p2 = (p1 + 1) % (int)count;
			p1 = p1 + vertexStartIdx + 1;
			p2 = p2 + vertexStartIdx + 1;
			mesh.pFace[face_idx++] = vertexStartIdx;
			if (isClockWise)
			{
				mesh.pFace[face_idx++] = p2;
				mesh.pFace[face_idx++] = p1;
			}
			else
			{
				mesh.pFace[face_idx++] = p1;
				mesh.pFace[face_idx++] = p2;
			}
		}
		faceIdx = face_idx;
		return true;
	}

	bool CTriangularUtil::MeshRectangle(FLOAT3D width, FLOAT3D height, bool isClockWise, const Matrix33 &R, const FLOAT3D3 &T,
		primitive::CMeshObj &mesh, int &vertexIdx, int &faceIdx)
	{
		FLOAT3D3 normal(0.0f, 0.0f, 1.0f);
		if (isClockWise)
		{
			normal.z = -1.0f;
		}
		normal.Normalize();
		normal = R.Inv().Transpose().mul(normal);

		FLOAT3D halfHeight = height * 0.5f;
		FLOAT3D halfWidth = width * 0.5f;
		int vertexStartIdx = vertexIdx;
		int vidx = vertexStartIdx;
		FLOAT3D3 v;
		v.Set(-halfWidth, -halfHeight, 0.0f);
		mesh.pVertex[vidx] = R.mul(v) + T;
		v.Set(halfWidth, -halfHeight, 0.0f);
		mesh.pVertex[vidx + 1] = R.mul(v) + T;
		v.Set(halfWidth, halfHeight, 0.0f);
		mesh.pVertex[vidx + 2] = R.mul(v) + T;
		v.Set(-halfWidth, halfHeight, 0.0f);
		mesh.pVertex[vidx + 3] = R.mul(v) + T;
		mesh.pNormal[vidx] = normal;
		mesh.pNormal[vidx + 1] = normal;
		mesh.pNormal[vidx + 2] = normal;
		mesh.pNormal[vidx + 3] = normal;

		// update vertexIdx
		vertexIdx = vidx + 4;

		int face_idx = faceIdx;
		if (isClockWise)
		{
			mesh.pFace[face_idx++] = vidx;
			mesh.pFace[face_idx++] = vidx + 3;
			mesh.pFace[face_idx++] = vidx + 2;

			mesh.pFace[face_idx++] = vidx;
			mesh.pFace[face_idx++] = vidx + 2;
			mesh.pFace[face_idx++] = vidx + 1;
		}
		else
		{
			mesh.pFace[face_idx++] = vidx;
			mesh.pFace[face_idx++] = vidx + 1;
			mesh.pFace[face_idx++] = vidx + 2;

			mesh.pFace[face_idx++] = vidx;
			mesh.pFace[face_idx++] = vidx + 2;
			mesh.pFace[face_idx++] = vidx + 3;
		}
		faceIdx = face_idx;
		return true;
	}


	void CTriangularUtil::MeshBoundBox(primitive::CMeshObj &meshObj, const BoundBox &boundBox)
	{
		int vIdx = 0;
		int faceIdx = 0;
		FLOAT3D xLength = boundBox.maxVertex.x - boundBox.minVertex.x;
		FLOAT3D yLength = boundBox.maxVertex.y - boundBox.minVertex.y;
		FLOAT3D zLength = boundBox.maxVertex.z - boundBox.minVertex.z;
		Matrix33 R;
		if (xLength > EPSILON && yLength > EPSILON)
		{
			//top
			CTriangularUtil::MeshRectangle(xLength, yLength, false, R, FLOAT3D3(0.0f, 0.0f, zLength * 0.5f),
				meshObj, vIdx, faceIdx);

			//bottom
			CTriangularUtil::MeshRectangle(xLength, yLength, true, R, FLOAT3D3(0.0f, 0.0f, zLength * -0.5f),
				meshObj, vIdx, faceIdx);
		}
		if (xLength > EPSILON && zLength > EPSILON)
		{
			//front
			R.SetCol(0, 1.0f,  0.0f, 0.0f);
			R.SetCol(1, 0.0f,  0.0f, 1.0f);
			R.SetCol(2, 0.0f, -1.0f, 0.0f);
			CTriangularUtil::MeshRectangle(xLength, zLength, false, R, FLOAT3D3(0.0f, yLength * -0.5f, 0.0f),
				meshObj, vIdx, faceIdx);

			//back
			R.SetCol(0, 1.0f,  0.0f, 0.0f);
			R.SetCol(1, 0.0f,  0.0f, 1.0f);
			R.SetCol(2, 0.0f, -1.0f, 0.0f);
			CTriangularUtil::MeshRectangle(xLength, zLength, true, R, FLOAT3D3(0.0f, yLength * 0.5f, 0.0f),
				meshObj, vIdx, faceIdx);
		}

		if (yLength > EPSILON && zLength > EPSILON)
		{
			//left
			R.SetCol(0, 0.0f, 1.0f, 0.0f);
			R.SetCol(1, 0.0f, 0.0f, 1.0f);
			R.SetCol(2, 1.0f, 0.0f, 0.0f);
			CTriangularUtil::MeshRectangle(yLength, zLength, false, R, FLOAT3D3(xLength * 0.5f, 0.0f, 0.0f),
				meshObj, vIdx, faceIdx);

			//right
			R.SetCol(0, 0.0f, 1.0f, 0.0f);
			R.SetCol(1, 0.0f, 0.0f, 1.0f);
			R.SetCol(2, 1.0f, 0.0f, 0.0f);
			CTriangularUtil::MeshRectangle(yLength, zLength, true, R, FLOAT3D3(xLength * -0.5f, 0.0f, 0.0f),
				meshObj, vIdx, faceIdx);
		}
		return ;
	}
}

