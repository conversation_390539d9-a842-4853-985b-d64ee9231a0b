#include "ExtrusionBuilder.h"

WD_NAMESPACE_BEGIN

MeshStruct ExtrusionBuilder::Mesh(float height
    , const FVec3& direction
    , Justification justification
    , const FVec3Vector& loop
    , const MeshLODSelection& lodSelection)
{
    WDUnused(direction);
    MeshStruct ret;

    // 形状至少有三个点
    if(loop.size() < 3)
        return ret;
    // 高度不能等于0
    if (Abs(height) <= NumLimits<float>::Epsilon)
        return ret;

    //根据对齐方式，改变拉伸体顶点z偏移
    float zOffset = 0.0f;
    switch (justification)
    {
    case WD::ExtrusionBuilder::J_Bottom:
        zOffset = 0.0f;
        break;
    case WD::ExtrusionBuilder::J_Center:
        zOffset = -height * 0.5f;
        break;
    case WD::ExtrusionBuilder::J_Top:
        zOffset = -height;
        break;
    default:
        zOffset = 0.0f;
        break;
    }

    // 生成环顶点
    FVec2Vector outLoop;
    if(!BuildLoop(loop, outLoop, lodSelection))
        return ret;

    FVec3Vector tVertices;
    tVertices.reserve(outLoop.size());
    size_t tSideLineCnt = 0;
    for (size_t i = 0; i < outLoop.size(); ++i)
    {
        const auto& v = outLoop[i];
        const auto& vPrev = outLoop[i == 0 ? outLoop.size() - 1 : i - 1];
        const auto& vNext = outLoop[i == outLoop.size() - 1 ? 0 : i + 1];

        const auto vecPrev = vPrev - v;
        const auto vecNext = vNext - v;
        float tmpAngle = FVec2::Angle(vecPrev, vecNext);

        bool bSideLine = tmpAngle < 160.0f;
        tVertices.push_back(FVec3(v, bSideLine ? 1.0f : 0.0f));
        if (bSideLine)
            tSideLineCnt++;
    }

    // 如果没有生成边线，默认计算四条
    if (tSideLineCnt == 0)
    {
        tVertices[0].z = 1.0f;
        tVertices[(tVertices.size() * 1) / 4].z = 1.0f;
        tVertices[(tVertices.size() * 2) / 4].z = 1.0f;
        tVertices[(tVertices.size() * 3) / 4].z = 1.0f;
    }

    // 对多边形进行三角细分,获取顶点索引
    std::vector<uint> shapeIndices = TEarcut<FVec3, uint>::Exec(tVertices);
    // 计算拉伸体顶点，法线以及面片，边线索引
    uint loopPointCount     = static_cast<uint>(tVertices.size());
    int shapeIndicesCount   = static_cast<int>(shapeIndices.size());
    std::vector<FVec3>  tPositions;
    tPositions.reserve(loopPointCount * 4);
    std::vector<FVec3>  tNormals;
    tNormals.reserve(loopPointCount * 4);
    std::vector<uint>   tTriIndices;
    tTriIndices.reserve(shapeIndicesCount * 2 + loopPointCount * 2);
    std::vector<uint>   tSideIndices;
    tSideIndices.reserve(loopPointCount * 3);
    // 是否向Z正方向拉伸
    bool bPositive = height > 0.0f;
    //计算底面
    {
        //顶点位置和法线
        FVec3 tNormal = bPositive ? FVec3::AxisNZ() : FVec3::AxisZ();
        for (size_t i = 0; i < loopPointCount; ++i)
        {
            FVec3 rPt = FVec3(tVertices[i].xy(), zOffset);
            tPositions.push_back(rPt);
            tNormals.push_back(tNormal);
        }
        //三角面索引
        {
            const std::vector<uint>& bottomShapeIndices = shapeIndices;
            if (bPositive)
                tTriIndices.insert(tTriIndices.end(), bottomShapeIndices.rbegin(), bottomShapeIndices.rend());
            else
                tTriIndices.insert(tTriIndices.end(), bottomShapeIndices.begin(), bottomShapeIndices.end());
        }
        //边线索引
        {
            for (uint i = 0; i < loopPointCount; ++i)
            {
                tSideIndices.push_back(i);
                tSideIndices.push_back(i + 1);
            }
            // 这里最后一个索引应该修改为0,表示最后一个点与第一个点构成的边线
            if (!tSideIndices.empty())
                tSideIndices.back() = uint(0);
        }
    }
    //计算顶面
    {
        // 统计当前的顶点个数
        uint pointCount = static_cast<uint>(tPositions.size());
        //顶点位置和法线
        FVec3 tNormal = bPositive ? FVec3::AxisZ() : FVec3::AxisNZ();
        for (size_t i = 0; i < loopPointCount; ++i)
        {
            FVec3 rPt = FVec3(tVertices[i].xy(), height + zOffset);
            tPositions.push_back(rPt);
            tNormals.push_back(tNormal);
        }
        //三角面索引
        {
            std::vector<uint> topShapeIndices = shapeIndices;
            for (auto& idx : topShapeIndices)
                idx += pointCount;

            if (bPositive)
                tTriIndices.insert(tTriIndices.end(), topShapeIndices.begin(), topShapeIndices.end());
            else
                tTriIndices.insert(tTriIndices.end(), topShapeIndices.rbegin(), topShapeIndices.rend());
        }
        //边线索引
        {
            for (uint i = 0; i < loopPointCount; ++i)
            {
                tSideIndices.push_back(pointCount + i);
                tSideIndices.push_back(pointCount + i + 1);
            }
            // 这里最后一个索引应该修改为pointCount,表示最后一个点与第一个点构成的边线
            if (!tSideIndices.empty())
                tSideIndices.back() = pointCount;
        }
    }
    //计算侧面
    {
        // 当前已经添加的顶点个数
        uint pointCount  = static_cast<uint>(tPositions.size());
        // 顶点位置和法线
        // 这里根据侧面的每一条棱来添加顶点，即一条棱添加一个底部顶点和顶部顶点
        // 但是因为可能存在顶点法线和面法线
        //  所以:
        //      如果是面法线，需要给这条棱上分别添加两次底部顶点和顶部顶点(虽然顶点位置相同，但是法线不同)
        //      如果是顶点法线,只需要给这条棱上添加一次底部顶点和顶部顶点(位置相同，法线也相同)
        for (uint i = 0; i < loopPointCount; ++i)
        {
            // 当前底部顶点索引
            uint ptIndexB           = i;
            // 前一个底部顶点索引
            uint ptIndexBPrev       = i == 0 ? loopPointCount - 1 : i - 1;
            // 下一个顶部顶点索引
            uint ptIndexBNext       = i == loopPointCount - 1 ? 0 : i + 1;
            // 顶部顶点索引
            uint ptIndexT           = ptIndexB + loopPointCount;
            // 获取顶点
            const FVec3 ptB         = tPositions[ptIndexB];
            const FVec3 ptBPrev     = tPositions[ptIndexBPrev];
            const FVec3 ptBNext     = tPositions[ptIndexBNext];
            const FVec3 ptT         = tPositions[ptIndexT];
            // 使用 当前、前一个、下一个底部顶点构成的两个方向向量的夹角计算该棱上的两个顶点是需要顶点法线还是需要面法线
            FVec3 vecPrev           = ptBPrev - ptB;
            FVec3 vecNext           = ptBNext - ptB;
            float angle             = FVec3::Angle(vecPrev, vecNext);
            // 分别计算左边面的法线和右边面的法线
            FVec3 faceNorL          = FTriangle3::Normal(ptBPrev, ptB, ptT);
            FVec3 faceNorR          = FTriangle3::Normal(ptT, ptB, ptBNext);
            if (!bPositive)
            {
                faceNorL = -faceNorL;
                faceNorR = -faceNorR;
            }
            // 是否需要面法线, 如果不是面法线则认为是需要顶点法线
            bool bFaceNor = angle < 160.0f;
            if (bFaceNor) // 面法线
            {
                tPositions.push_back(ptB);
                tPositions.push_back(ptT);
                tNormals.push_back(faceNorL);
                tNormals.push_back(faceNorL);
                tPositions.push_back(ptB);
                tPositions.push_back(ptT);
                tNormals.push_back(faceNorR);
                tNormals.push_back(faceNorR);
            }
            else // 顶点法线
            {
                FVec3 ptNor = FVec3::Normalize(faceNorL + faceNorR);
                tPositions.push_back(ptB);
                tPositions.push_back(ptT);
                tNormals.push_back(ptNor);
                tNormals.push_back(ptNor);
            }
            // 当前棱的底部顶点索引
            uint idxB = static_cast<uint>(tPositions.size() - 2);
            // 当前棱的顶部顶点索引
            uint idxT = static_cast<uint>(tPositions.size() - 1);
            // 三角面索引
            {
                // 下一条棱的底部顶点索引
                uint idxBNext = idxT + 1;
                // 最后一条棱，则下一条棱其实就是第一条棱
                if (i == loopPointCount - 1)
                    idxBNext = pointCount;
                // 下一条棱的顶部顶点索引
                uint idxTNext = idxBNext + 1;
                if (bPositive)
                {
                    tTriIndices.push_back(idxT);
                    tTriIndices.push_back(idxB);
                    tTriIndices.push_back(idxBNext);

                    tTriIndices.push_back(idxT);
                    tTriIndices.push_back(idxBNext);
                    tTriIndices.push_back(idxTNext);
                }
                else
                {
                    tTriIndices.push_back(idxTNext);
                    tTriIndices.push_back(idxBNext);
                    tTriIndices.push_back(idxT);

                    tTriIndices.push_back(idxBNext);
                    tTriIndices.push_back(idxB);
                    tTriIndices.push_back(idxT);
                }
            }
            // 校验是否生成边线，如果是，则生成边线索引
            if (tVertices[i].z > 0.0f)
            {
                tSideIndices.push_back(idxB);
                tSideIndices.push_back(idxT);
            }
        }
    }
    ret.positions   = std::move(tPositions);
    ret.normals     = std::move(tNormals);
    //三角面索引
    ret.triangles.setDrawElementUIntData(std::move(tTriIndices));
    ret.triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);
    //边线索引
    ret.wireFrames.setDrawElementUIntData(std::move(tSideIndices));
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);

    return ret;
}

FKeyPoints ExtrusionBuilder::KeyPoints(float height
    , const FVec3& direction
    , Justification justification
    , const FVec3Vector& loop)
{
    WDUnused(direction);
    FKeyPoints ret;

    // 形状至少有三个点
    if (loop.size() < 3)
        return ret;
    // 高度不能小于0
    if (Abs(height) <= NumLimits<float>::Epsilon)
        return ret;

    //根据对齐方式，改变拉伸体顶点z偏移
    float zOffset = 0.0f;
    switch (justification)
    {
    case WD::ExtrusionBuilder::J_Bottom:
        zOffset = 0.0f;
        break;
    case WD::ExtrusionBuilder::J_Center:
        zOffset = -height * 0.5f;
        break;
    case WD::ExtrusionBuilder::J_Top:
        zOffset = -height;
        break;
    default:
        zOffset = 0.0f;
        break;
    }

    // loop中, x,y代表顶点位置，z代表顶点的圆角半径
    FVec3Vector tLoop;
    tLoop.reserve(loop.size());
    tLoop.push_back(loop.front());
    for (size_t i = 1; i < loop.size(); ++i)
    {
        // 判断俩顶点距离是否过近
        if (FVec2::DistanceSq(tLoop.back().xy(), loop[i].xy()) <= NumLimits<float>::Epsilon)
        {
            // 保留更大的圆角半径
            tLoop.back().z = Max(tLoop.back().z, loop[i].z);
        }
        else
        {
            tLoop.push_back(loop[i]);
        }
    }
    // 形状至少有三个有效点
    if (tLoop.size() < 3)
        return ret;
    // 判断顶点是否为顺时针，若为顺时针，则需要将顶点反向,改为逆时针
    if (FPolygon2::IsCW(tLoop))
    {
        std::reverse(tLoop.begin(), tLoop.end());
    }
    // 根据圆角半径,生成带有圆角的多边形顶点列表
    FVec2Vector tVertices;
    for (size_t i = 0; i < tLoop.size(); ++i)
    {
        // 当前点索引
        const size_t idxCurr    = i;
        // 圆角半径
        const float radius      = tLoop[idxCurr].z;
        // 当前点
        const FVec2 ptCurr      = tLoop[idxCurr].xy();
        // 半径接近0,不生成圆角
        if (Abs(radius) <= NumLimits<float>::Epsilon)
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        // 前一个点的索引
        const size_t idxPrev    = i == 0 ? tLoop.size() - 1 : i - 1;
        // 后一个点的索引
        const size_t idxNext    = i == tLoop.size() - 1 ? 0 : i + 1;
        // 前一个点
        const FVec2 ptPrev  = tLoop[idxPrev].xy();
        // 后一个点
        const FVec2 ptNext  = tLoop[idxNext].xy();
        // 计算圆弧参数
        FVec2 outCenter     = FVec2::Zero();
        FVec2 outStart      = FVec2::Zero();
        FVec2 outEnd        = FVec2::Zero();
        FVec2 outArcCenter  = FVec2::Zero();
        if (!CalcArcParamWithVertex(ptCurr, ptPrev, ptNext, radius, outCenter, outStart, outEnd, &outArcCenter))
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        tVertices.push_back(outStart);
        tVertices.push_back(outArcCenter);
        tVertices.push_back(outEnd);
    }

    FVec3 upDir = height > 0.0f ? FVec3::AxisZ() : FVec3::AxisNZ();
    size_t vCnt = tVertices.size();
    ret.reserve(2 * vCnt);
    for (size_t i = 0; i < tVertices.size(); ++i)
    {
        // 底面关键点
        ret.emplace_back(FKeyPoint(FVec3(tVertices[i], zOffset), -upDir));
        // 顶面关键点
        //ret.emplace_back(FKeyPoint(FVec3(tVertices[i], height + zOffset), upDir));
    }
    return ret;
}
FVec3Vector ExtrusionBuilder::IntersectPoints(float height
    , const FVec3& direction
    , Justification justification
    , const FVec3Vector& loop)
{
    WDUnused(direction);
    FVec3Vector ret;

    // 形状至少有三个点
    if (loop.size() < 3)
        return ret;
    // 高度不能小于0
    if (Abs(height) <= NumLimits<float>::Epsilon)
        return ret;

    //根据对齐方式，改变拉伸体顶点z偏移
    float zOffset = 0.0f;
    switch (justification)
    {
    case WD::ExtrusionBuilder::J_Bottom:
        zOffset = 0.0f;
        break;
    case WD::ExtrusionBuilder::J_Center:
        zOffset = -height * 0.5f;
        break;
    case WD::ExtrusionBuilder::J_Top:
        zOffset = -height;
        break;
    default:
        zOffset = 0.0f;
        break;
    }

    // loop中, x,y代表顶点位置，z代表顶点的圆角半径
    FVec3Vector tLoop;
    tLoop.reserve(loop.size());
    tLoop.push_back(loop.front());
    for (size_t i = 1; i < loop.size(); ++i)
    {
        // 判断俩顶点距离是否过近
        if (FVec2::DistanceSq(tLoop.back().xy(), loop[i].xy()) <= NumLimits<float>::Epsilon)
        {
            // 保留更大的圆角半径
            tLoop.back().z = Max(tLoop.back().z, loop[i].z);
        }
        else
        {
            tLoop.push_back(loop[i]);
        }
    }
    // 形状至少有三个有效点
    if (tLoop.size() < 3)
        return ret;
    // 判断顶点是否为顺时针，若为顺时针，则需要将顶点反向,改为逆时针
    if (FPolygon2::IsCW(tLoop))
    {
        std::reverse(tLoop.begin(), tLoop.end());
    }
    // 根据圆角半径,生成带有圆角的多边形顶点列表
    FVec2Vector tVertices;
    for (size_t i = 0; i < tLoop.size(); ++i)
    {
        // 当前点索引
        const size_t idxCurr    = i;
        // 圆角半径
        const float radius      = tLoop[idxCurr].z;
        // 当前点
        const FVec2 ptCurr      = tLoop[idxCurr].xy();
        // 半径接近0,不生成圆角
        if (Abs(radius) <= NumLimits<float>::Epsilon)
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        // 前一个点的索引
        const size_t idxPrev    = i == 0 ? tLoop.size() - 1 : i - 1;
        // 后一个点的索引
        const size_t idxNext    = i == tLoop.size() - 1 ? 0 : i + 1;
        // 前一个点
        const FVec2 ptPrev  = tLoop[idxPrev].xy();
        // 后一个点
        const FVec2 ptNext  = tLoop[idxNext].xy();
        // 计算圆弧参数
        FVec2 outCenter     = FVec2::Zero();
        FVec2 outStart      = FVec2::Zero();
        FVec2 outEnd        = FVec2::Zero();
        FVec2 outArcCenter  = FVec2::Zero();
        if (!CalcArcParamWithVertex(ptCurr, ptPrev, ptNext, radius, outCenter, outStart, outEnd, &outArcCenter))
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        tVertices.push_back(outStart);
        tVertices.push_back(outArcCenter);
        tVertices.push_back(outEnd);
    }

    FVec3 upDir = height > 0.0f ? FVec3::AxisZ() : FVec3::AxisNZ();
    size_t vCnt = tVertices.size();
    ret.reserve(2 * vCnt);
    for (size_t i = 0; i < tVertices.size(); ++i)
    {
        // 底面关键点
        ret.emplace_back(FVec3(tVertices[i], zOffset));
        // 顶面关键点
        ret.emplace_back(FVec3(tVertices[i], height + zOffset));
    }
    return ret;
}

bool ExtrusionBuilder::SEXTToEXTR(const FVec3& paax
    , const FVec3& paaxPos
    , const FVec3& pbax
    , const float& phei
    , const float& px
    , const float& py
    , const float& pz
    , const VertexDatas& vertexDatas
    , FMeshTransform& outTransform
    , float& outHeight
    , FVec3& outDirection
    , Justification& outJustification
    , FVec3Vector& outLoop)
{
    // 顶点个数应该不小于3
    if (vertexDatas.size() < 3)
    {
        assert(false);
        return false;
    }
    // paax作为SEXT的X轴 pbax作为SEXT的Y轴
    // 校验PAAX与pbax
    // paax与pbax是否零向量
    if (paax.lengthSq() <= NumLimits<float>::Epsilon
        && pbax.lengthSq() <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }

    // 判断是否共线
    if (FVec3::OnTheSameLine(paax, pbax, 0.0001f))
    {
        assert(false);
        return false;
    }
    // 这里处理为两轴垂直
    auto tPaax = paax.normalized();
    auto tPbax = pbax.normalized();
    auto tAxZ = FVec3::Cross(tPaax, tPbax);
    tPaax = FVec3::Cross(tPbax, tAxZ);

    // 生成拉伸面在XOY平面上，拉伸体原点是(0,0,0)，未旋转的拉伸体
    outHeight           = phei;
    outDirection        = FVec3::AxisZ();
    outJustification    = Justification::J_Bottom;
    // 将所有点按照没有旋转来处理，即所有点都在XOY平面上
    outLoop.reserve(vertexDatas.size());
    for (size_t i = 0; i < vertexDatas.size(); i++)
    {
        outLoop.push_back(vertexDatas[i].first);
    }

    // 使用PAAX与PBAX计算拉伸体的旋转
    FMat4   rMat    = FMat3::MakeRotationXY(paax, pbax);
    // 使用PX,PY,PZ计算拉伸体的偏移 
    FVec3   trans   = rMat * FVec3(px, py, pz) + paaxPos;
    // 保存变换信息
    outTransform.translation    = trans;
    outTransform.rotation       = FMat4::ToQuat(rMat);

    return true;
}

MeshStruct ExtrusionBuilder::SideLines(float height
    , const FVec3& direction
    , Justification justification
    , const FVec3Vector& loop)
{
    MeshStruct ret;
    //校验参数有效性

    //形状至少有三个点
    if(loop.size() < 3)
        return ret;
    //方向不能是0向量
    if (direction.lengthSq() <= NumLimits<float>::Epsilon)
        return ret;
    //拉升高度不能为0
    if (Abs(height) <= NumLimits<float>::Epsilon)
        return ret;

    double dis0 = FVec3::DistanceSq(loop[0], loop[1]);
    double dis1 = FVec3::DistanceSq(loop[1], loop[2]);
    double dis2 = FVec3::DistanceSq(loop[2], loop[0]);
    //校验前三个点两两不重合
    if (dis0 <= NumLimits<float>::Epsilon
        || dis1 <= NumLimits<float>::Epsilon
        || dis2 <= NumLimits<float>::Epsilon)
        return ret;

    //使用前三个点生成平面
    FPlane plane(loop[0], loop[1], loop[2]);
    
    //拿到平面法线 并 计算 平面法线 与 拉伸方向向量的点积
    FVec3 tDirection = direction.normalized();
    const FVec3& planeNor = plane.normal; 
    double dt = FVec3::Dot(tDirection, planeNor);
    //如果点积等于0，不能构建拉升体
    if (Abs(dt) <= NumLimits<float>::Epsilon)
        return ret;
    //将所有点投影到前三个点构成的平面上,并且将重合的点剔除
    std::vector<FVec3 > loop3D;
    for (size_t i = 0; i < loop.size(); ++i)
    {
        FVec3 tPrjPt = plane.project(loop[i]);
        if (loop3D.empty())
        {
            loop3D.push_back(tPrjPt);
        }
        else
        {
            bool bAdd = true;
            for(const auto& tPt: loop3D)
            {
                double dis = FVec3::DistanceSq(tPt, tPrjPt);
                if (dis <= NumLimits<float>::Epsilon)
                {
                    bAdd = false;
                    break;
                }
            }
            if (bAdd)
            {
                loop3D.push_back(tPrjPt);
            }
        }
    }

    //将所有点旋转至保证与XOY平面平行
    FMat3 tMat = FMat3::FromQuat(FQuat::FromVectors(planeNor, FVec3(0.0f, 0.0f, 1.0f)));
    //将点计算成二维点的列表
    std::vector<FVec2 > loop2D(loop3D.size());
    for (size_t i = 0; i < loop3D.size(); ++i)
    {
        FVec3 tPt = tMat * loop3D[i];
        loop2D[i] = tPt.xy();
    }
    //使用2D顶点数据实例化2D多边形类
    FPolygon2 polygon(std::move(loop2D));
    //判断拉伸面是否是简单多边形
    /*if (!polygon.isSample())
        return ret;*/
    
    //耳切法需要使用逆时针(右手坐标系,Z朝上时,逆时针为正)
    //判断顶点是否为顺时针，若为顺时针，则需要转向
    if (polygon.isCW())
    {
        polygon.reverse();
        std::reverse(loop3D.begin(), loop3D.end());
    }
    //根据对齐方式，改变拉伸体顶点z偏移
    float zOffset = 0.0f;
    switch (justification)
    {
    case WD::ExtrusionBuilder::J_Bottom:
        zOffset = 0.0f;
        break;
    case WD::ExtrusionBuilder::J_Center:
        zOffset = -height * 0.5f;
        break;
    case WD::ExtrusionBuilder::J_Top:
        zOffset = -height;
        break;
    default:
        zOffset = 0.0f;
        break;
    }
    WD::FVec3 tOffset = WD::FVec3(0.0f, 0.0f, zOffset);
    //还原到3三维点
    std::vector<FVec3> rLoop3D(loop3D.size());
    for (size_t i = 0; i < loop3D.size(); ++i)
    {
        rLoop3D[i] = FVec3(loop3D[i] + tOffset);
    }

    uint loopPointCount = static_cast<uint>(rLoop3D.size());
    std::vector<uint> tSideIndices;
    std::vector<FVec3> tPositions;
    //计算顶面
    {
        //顶点位置
        for (size_t i = 0; i < loopPointCount; ++i)
        {
            FVec3 rPt = rLoop3D[i] + tDirection * height;
            tPositions.push_back(rPt);
        }
        //边线索引
        {
            for (uint i = 1; i < loopPointCount; ++i)
            {
                tSideIndices.push_back(i - 1);
                tSideIndices.push_back(i);
            }
            //最后一个点跟第一个点构成的线段
            tSideIndices.push_back(loopPointCount - 1);
            tSideIndices.push_back(0);
        }
    }

    //计算底面
    {
        uint pointCount = static_cast<uint>(tPositions.size());
        //边线索引
        {
            for (uint i = 1; i < loopPointCount; ++i)
            {
                tSideIndices.push_back(pointCount + i - 1);
                tSideIndices.push_back(pointCount + i);
            }
            //最后一个点跟第一个点构成的线段
            tSideIndices.push_back(pointCount + loopPointCount - 1);
            tSideIndices.push_back(pointCount + 0);
        }
        //顶点位置
        for (size_t i = 0; i < loopPointCount; ++i)
        {
            FVec3 rPt = rLoop3D[i];
            tPositions.push_back(rPt);
        }
    }
    //计算侧面
    {
        for (uint i = 0; i < loopPointCount; ++i)
        {
            // 边线索引
            tSideIndices.push_back(i);
            tSideIndices.push_back(i + loopPointCount);
        }
    }
    
    ret.positions = std::move(tPositions);

    //边线索引
    ret.wireFrames.setDrawElementUIntData(std::move(tSideIndices));
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);

    return ret;
}

MeshStruct ExtrusionBuilder::SimpleMesh(float height, Justification justification, const FVec3Vector& loop, const MeshLODSelection& lodSelection)
{
    MeshStruct ret;
    // 形状至少有三个点
    if (loop.size() < 3)
        return ret;
    // 高度不能等于0
    if (Abs(height) <= NumLimits<float>::Epsilon)
        return ret;

    //根据对齐方式，改变拉伸体顶点z偏移
    float zOffset = 0.0f;
    switch (justification)
    {
    case WD::ExtrusionBuilder::J_Bottom:
        zOffset = 0.0f;
        break;
    case WD::ExtrusionBuilder::J_Center:
        zOffset = -height * 0.5f;
        break;
    case WD::ExtrusionBuilder::J_Top:
        zOffset = -height;
        break;
    default:
        zOffset = 0.0f;
        break;
    }

    std::vector < std::pair<FVec2, bool>> data;

    FVec2Vector realLoop;
    if(!BuildLoop(loop, realLoop, lodSelection))
        return ret;

    // 判断是否需要生成边线
    data.reserve(realLoop.size());
    size_t tSideLineCnt = 0;
    for (size_t i = 0; i < realLoop.size(); ++i)
    {
        const auto& v = realLoop[i];
        const auto& vPrev = realLoop[i == 0 ? realLoop.size() - 1 : i - 1];
        const auto& vNext = realLoop[i == realLoop.size() - 1 ? 0 : i + 1];

        const auto vecPrev = vPrev - v;
        const auto vecNext = vNext - v;
        float tmpAngle = FVec2::Angle(vecPrev, vecNext);

        // 夹角较大则不需要生成边线
        bool bSideLine = tmpAngle < 160.0f;
        data.push_back({ v, bSideLine });
        if (bSideLine)
            tSideLineCnt++;
    }

    // 如果没有生成边线，默认计算四条
    if (tSideLineCnt == 0)
    {
        int span = int(data.size()) / 4;
        data[span * 0].second = true;
        data[span * 1].second = true;
        data[span * 2].second = true;
        data[span * 3].second = true;
    }

    // 对多边形进行三角细分,获取顶点索引
    auto shapeIndices = TEarcut<FVec2, int>::Exec(realLoop);

    ret.positions.reserve(realLoop.size() * 2);
    for (const auto& v : realLoop)
    {
        ret.positions.push_back(FVec3(v, zOffset));
        ret.positions.push_back(FVec3(v, height + zOffset));
    }
    // 三角面索引
    if (shapeIndices.size() % 3 != 0)
    {
        assert(false);
        return ret;
    }

    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> tris;
        tris.reserve(shapeIndices.size() * 2 + ret.positions.size() / 2 * 2 * 3);
        for (auto i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<byte>(shapeIndices[i + 2] * 2));
            tris.push_back(static_cast<byte>(shapeIndices[i + 1] * 2));
            tris.push_back(static_cast<byte>(shapeIndices[i + 0] * 2));

            tris.push_back(static_cast<byte>(shapeIndices[i + 0] * 2 + 1));
            tris.push_back(static_cast<byte>(shapeIndices[i + 1] * 2 + 1));
            tris.push_back(static_cast<byte>(shapeIndices[i + 2] * 2 + 1));
        }

        for (auto i = 0; i < ret.positions.size(); i += 2)
        {
            auto next = (i + 2) < ret.positions.size() ? i + 2 : 0;
            //i+1  next+1   
            //i    next
            tris.push_back(static_cast<byte>(i + 1));
            tris.push_back(static_cast<byte>(next));
            tris.push_back(static_cast<byte>(next + 1));

            tris.push_back(static_cast<byte>(i + 1));
            tris.push_back(static_cast<byte>(i));
            tris.push_back(static_cast<byte>(next));
        }
        ret.triangles.setDrawElementByteData(std::move(tris));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max)
    {
        std::vector<ushort> tris;
        tris.reserve(shapeIndices.size() * 2 + ret.positions.size() / 2 * 2 * 3);
        for (auto i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<ushort>(shapeIndices[i + 2] * 2));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 1] * 2));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 0] * 2));

            tris.push_back(static_cast<ushort>(shapeIndices[i + 0] * 2 + 1));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 1] * 2 + 1));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 2] * 2 + 1));
        }

        for (auto i = 0; i < ret.positions.size(); i += 2)
        {
            auto next = (i + 2) < ret.positions.size() ? i + 2 : 0;
            //i+1  next+1   
            //i    next
            tris.push_back(static_cast<ushort>(i + 1));
            tris.push_back(static_cast<ushort>(next));
            tris.push_back(static_cast<ushort>(next + 1));

            tris.push_back(static_cast<ushort>(i + 1));
            tris.push_back(static_cast<ushort>(i));
            tris.push_back(static_cast<ushort>(next));
        }
        ret.triangles.setDrawElementUShortData(std::move(tris));
    }
    else
    {
        std::vector<uint> tris;
        tris.reserve(shapeIndices.size() * 2 + ret.positions.size() / 2 * 2 * 3);
        for (auto i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<uint>(shapeIndices[i + 2] * 2));
            tris.push_back(static_cast<uint>(shapeIndices[i + 1] * 2));
            tris.push_back(static_cast<uint>(shapeIndices[i + 0] * 2));

            tris.push_back(static_cast<uint>(shapeIndices[i + 0] * 2 + 1));
            tris.push_back(static_cast<uint>(shapeIndices[i + 1] * 2 + 1));
            tris.push_back(static_cast<uint>(shapeIndices[i + 2] * 2 + 1));
        }

        for (auto i = 0; i < ret.positions.size(); i += 2)
        {
            auto next = (i + 2) < ret.positions.size() ? i + 2 : 0;
            //i+1  next+1   
            //i    next
            tris.push_back(static_cast<uint>(i + 1));
            tris.push_back(static_cast<uint>(next));
            tris.push_back(static_cast<uint>(next + 1));

            tris.push_back(static_cast<uint>(i + 1));
            tris.push_back(static_cast<uint>(i));
            tris.push_back(static_cast<uint>(next));
        }
        ret.triangles.setDrawElementUIntData(std::move(tris));
    }
    ret.triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);

    // 边线索引
    uint sz = 2;// 每条弧的顶点数
    auto numArcs = ret.positions.size() / sz;// 弧的条数

    int count = 0;
    for (const auto& d : data)
    {
        if (d.second)
            count++;
    }
    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (auto i = 0; i < numArcs; i++)
        {
            wireframes.push_back(static_cast<byte>(sz * i));
            wireframes.push_back(static_cast<byte>(((i + 1) % numArcs) * sz));

            wireframes.push_back(static_cast<byte>(sz * i + sz - 1));
            wireframes.push_back(static_cast<byte>(((i + 1) % numArcs) * sz + sz - 1));

            if (data[i].second)
            {
                // 生成侧边线
                for (uint j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<byte>(sz * i + j));
                    wireframes.push_back(static_cast<byte>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementByteData(std::move(wireframes));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max)
    {
        std::vector<ushort> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (auto i = 0; i < numArcs; i++)
        {
            wireframes.push_back(static_cast<ushort>(sz * i));
            wireframes.push_back(static_cast<ushort>(((i + 1) % numArcs) * sz));

            wireframes.push_back(static_cast<ushort>(sz * i + sz - 1));
            wireframes.push_back(static_cast<ushort>(((i + 1) % numArcs) * sz + sz - 1));

            if (data[i].second)
            {
                // 生成侧边线
                for (uint j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<ushort>(sz * i + j));
                    wireframes.push_back(static_cast<ushort>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementUShortData(std::move(wireframes));
    }
    else
    {
        std::vector<uint> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (auto i = 0; i < numArcs; i++)
        {
            wireframes.push_back(static_cast<uint>(sz * i));
            wireframes.push_back(static_cast<uint>(((i + 1) % numArcs) * sz));

            wireframes.push_back(static_cast<uint>(sz * i + sz - 1));
            wireframes.push_back(static_cast<uint>(((i + 1) % numArcs) * sz + sz - 1));

            if (data[i].second)
            {
                // 生成侧边线
                for (uint j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<uint>(sz * i + j));
                    wireframes.push_back(static_cast<uint>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementUIntData(std::move(wireframes));
    }
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);

    return ret;
}

WD_NAMESPACE_END

