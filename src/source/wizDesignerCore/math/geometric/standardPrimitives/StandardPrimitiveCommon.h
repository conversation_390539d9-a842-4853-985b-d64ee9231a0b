#pragma once

#include "../WDPrimitiveSet.h"


namespace primitive
{
    class CMeshObj;
}

WD_NAMESPACE_BEGIN

/**
 * @brief 网格附带的变换数据(只包含位置旋转变换)
 * @tparam T 数据类型
*/
template <typename T>
class TMeshTransform
{
public:
    //平移变换
    TVec3<T> translation = TVec3<T>::Zero();
    //旋转变换
    TQuat<T> rotation = TQuat<T>::Identity();
public:
    /**
     * @brief 重置
     *  这里只需要重置变换数据标志即可
    */
    inline void reset();
};
using FMeshTransform = TMeshTransform<float>;
using DMeshTransform = TMeshTransform<double>;

/**
* @brief 网格数据
*/
class WD_API MeshStruct
{
public:
    //位置列表类型
    using Positions     = TVec3Vector<float>;
    //法线列表类型
    using Normals       = TVec3Vector<float>;
    //Uv列表类型
    using Uvs           = TVec2Vector<float>;
    //三角面索引
    using Triangles     = WDPrimitiveSet;
    //线框索引
    using WireFrames    = WDPrimitiveSet;
public:
    /**
    * @brief 顶点位置列表
    */
    Positions positions;
    /**
    * @brief 顶点法线列表
    */
    Normals normals;
    /**
    * @brief 顶点uv列表
    */
    Uvs uvs;
    /**
    * @brief 三角面索引列表
    */
    Triangles triangles;
    /**
    * @brief 线框索引列表
    */
    WireFrames wireFrames;
public:
    /**
    * @brief 从 primitive::CMeshObj 数据源获取数据
    */
    bool fromMesh(const primitive::CMeshObj& mesh);
    /**
    * @brief 设置 三角面索引以及边线索引数据
    * @param vertexCount 顶点个数，用于给索引数据分配合适的内存
    * @param pTriIndices 三角面索引数据首地址(每三个构成一个三角面)
    * @param triIndicesCount 三角面索引数据个数
    * @param pWireFrameIndices 线框索引数据首地址(每两个构成一条线段)
    * @param wireFrameIndicesCount 线框索引数据个数
    */
    template <typename T>
    bool setIndices(size_t vertexCount
        , const T* const pTriIndices
        , size_t triIndicesCount
        , const T* const pWireFrameIndices
        , size_t wireFrameIndicesCount);
    /**
    * @brief 设置 三角面索引以及边线索引数据
    * @param vertexCount 顶点个数，用于给索引数据分配合适的内存
    * @param triIndices 三角面索引数据(每三个构成一个三角面)
    * @param wireFrameIndices 线框索引数据(每两个构成一条线段)
    */
    template <typename T>
    inline bool setIndices(size_t vertexCount
        , const std::vector<T>& triIndices = std::vector<T>()
        , const std::vector<T>& wireFrameIndices = std::vector<T>());
private:
    template <typename T0, class T1>
    std::vector<T1> corvertIndicesT(const T0* const pSrcIndices, size_t srcIndicesCount);
};

/**
* @brief 计算圆弧分段数
* @param radius 圆(或弧所在圆)的半径
* @param arcAngle 弧角度(或圆角度),角度制,取值范围:[0,360]
* @param arcTolerance 角度公差,取值范围:[0.1, 100],如果取值超过该范围，将强制Clamp到该范围内
*   指的是圆弧上的任意两点连接而成的小线段到圆弧的最大距离
* @return 返回通过角度公差计算的近似该圆弧(或圆)的分段数
*/
template <typename T>
uint ArcTravel(T arcAngle
    , T radius
    , T arcTolerance)
{
    static constexpr const uint sOne    = uint(1);
    static constexpr const T minStepF   = T(8);

    //输入值校验
    if (arcAngle <= T(0) || radius <= T(0))
        return uint(0);

    if (arcTolerance >= radius)
    {
        //使用角度范围进行最小分段数 约束
        if (arcAngle <= T(180))
            return uint(4);
        else
            return uint(8);
    }

    arcTolerance = Clamp<T>(arcTolerance, T(0.1), radius);

    T angRatio  = arcAngle / T(360);
    T dt        = radius - arcTolerance;
    T ac        = dt / radius;
    T rValue    = ACos(ac) * T(2);
    T stepV     = static_cast<float>(TwoPI) * angRatio / rValue;
    uint step   = static_cast<uint>(stepV) + sOne;

    //使用角度范围进行最小分段数 约束
    if (arcAngle <= T(180))
        return Clamp<uint>(step, uint(4), uint(128));
    else
        return Clamp<uint>(step, uint(8), uint(128));
}

class WDMD5;
/**
* @brief 决定生成几何体的精细度
*   !注意:这里会自动限定,所有类型返回的分段数值不会超过128
*/
class WD_API MeshLODSelection
{
public:
    /**
    * @brief 类型
    */
    enum Type
    {
        //默认类型, 返回分段数24
        T_Normal = 0,
        //低, 返回分段数16
        T_Low,
        //中, 返回分段数24
        T_Middle,
        //高, 返回分段数40
        T_High,
        //极低, 返回分段数8
        T_VeryLow,
        //极高, 返回分段数64
        T_VeryHigh,
        //使用角度公差计算弧的分段数
        T_ArcTolerance,
        //使用指定的分段数
        T_Segment,
    };
private:
    Type _type;
    float _arcTolerance; //默认值10.0f
    uint _segment; //默认值 16
public:
    inline MeshLODSelection(Type type = T_Normal);
    inline MeshLODSelection(float arcTolerance, Type type = Type::T_ArcTolerance);
    inline MeshLODSelection(uint segment, Type type = Type::T_Segment);
    inline MeshLODSelection(const MeshLODSelection& right);
public:
    /**
    * @brief 设置类型
    */
    inline void setType(Type type);
    /**
     * @brief 获取类型
    */
    inline Type type() const;
    /**
    * @brief 设置角度公差
    */
    inline void setArcTolerance(float arcTolerance);
    /**
    * @brief 获取角度公差
    */
    inline float arcTolerance() const;
    /**
     * @brief 设置分段数
    */
    inline void setSegment(uint segment);
    /**
    * @brief 获取分段数
    *   返回当前设置的类型对应的分段数
    */
    uint getSegment(float arcAngle = 0.0f, float radius = 0.0f) const;
    
    /**
     * @brief 计算MD5值
     * @param md5 md5对象引用
    */
    void calcMd5Key(WD::WDMD5& md5);
public:
    inline MeshLODSelection& operator = (const MeshLODSelection& right);
    inline bool operator == (const MeshLODSelection& right) const;
    inline bool operator != (const MeshLODSelection& right) const;
    inline bool operator < (const MeshLODSelection& right) const;
    inline bool operator <= (const MeshLODSelection& right) const;
    inline bool operator > (const MeshLODSelection& right) const;
    inline bool operator >= (const MeshLODSelection& right) const;
};

/**
 * @brief 指定圆弧圆心、圆弧起始点、圆弧结束点、分段数生成圆弧顶点
 * @tparam T 数据类型
 * @param center 圆弧圆心
 * @param vStart 圆弧圆心到起点的方向向量
 * @param vEnd 圆弧圆心到终点的方向向量
 * @param radius 圆弧半径,必须大于0
 * @param lodSelection 决定圆弧的分段数
 * @param outVertices 输出的圆弧的顶点
*/
template <typename T>
void ArcGeneration(const TVec2<T>& center
    , const TVec2<T>& vStart
    , const TVec2<T>& vEnd
    , T radius
    , const MeshLODSelection& lodSelection
    , TVec2Vector<T>& outVertices);
/**
 * @brief 指定多边形的某个顶点以及其前一个，后一个顶点和圆弧半径，生成圆弧；即给多边形的某个端点指定半径以生成圆角多边形
 *  可以处理半径大于0和半径小于0时的两种情况:
 *      1.如果半径大于0,则使用前一个顶点与当前顶点构成的直线和后一个顶点与当前顶点构成的直线作为切线，结合半径生成圆弧
 *      2.如果半径小于0,则使用当前顶点做为圆心，以当前顶点与前一个顶点构成的直线为圆弧的开始直线，以当前顶点与后一个顶点构成的直线为圆弧的结束直线，结合半径生成圆弧
 * @tparam T 数据类型
 * @param currPoint 多边形的某个顶点(当前顶点)
 * @param prevPoint 当前顶点的前一个顶点
 * @param nextPoint 当前顶点的后一个顶点
 * @param radius 半径, 不能等于0
 * @param outCenter 输出的圆弧圆心
 * @param outStart 输出的圆弧起始点
 * @param outEnd 输出的圆弧结束点
 * @param pOutArcCenter 是否输出的圆弧的中心点,指的是圆弧圆周上的点
 * @return 是否成功生成圆弧
*/
template <typename T>
bool CalcArcParamWithVertex(const TVec2<T>& currPoint
    , const TVec2<T>& prevPoint
    , const TVec2<T>& nextPoint
    , T radius
    , TVec2<T>& outCenter
    , TVec2<T>& outStart
    , TVec2<T>& outEnd
    , TVec2<T>* pOutArcCenter = nullptr);

/**
 * @brief 根据loop中的顶点以及圆角半径, 构建带圆角的loop
 * @param loop 环, x, y代表顶点位置, z代表顶点的圆角半径(圆弧生成规则请参考 ArcGeneration<T>)
 * @param outLoop 输出的环
 * @param lodSelection lod
 * @return 是否输出成功
 */
bool WD_API BuildLoop(const std::vector<FVec3>& loop
    , std::vector<FVec2>& outLoop
    , const MeshLODSelection& lodSelection);

/**
* @brief 生成一个圆弧的顶点(平行于xoy平面）
* @brief 生成的顶点数为segs+1（为了适配angle不是360的情况，同时代码也会简洁一点）
* @param r 半径
* @param angle 角度（角度制）
* @param z z方向上的坐标值
* @return
*/
std::vector<FVec3> MakeArc(float r, uint segs, float z = 0.0F, float angle = 360.0F);
/**
* @brief 生成一个圆的顶点(平行于xoy平面）
* @brief 生成的顶点数为segs
* @param r 半径
* @param segs 分段数
* @param z z方向上的坐标值
* @return
*/
std::vector<FVec3> MakeCircle(float r, uint segs, float z = 0.0F);

WD_NAMESPACE_END

#include "StandardPrimitiveCommon.inl"