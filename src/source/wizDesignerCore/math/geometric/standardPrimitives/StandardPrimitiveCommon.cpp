#include "StandardPrimitiveCommon.h"
#include "triangular/MeshObj.h"
#include "../../../common/WDMD5.h"
#include "../../utils/clipper.hpp"

WD_NAMESPACE_BEGIN

bool MeshStruct::fromMesh(const primitive::CMeshObj& mesh)
{
    if (mesh.vertexCount <= 0)
        return false;

    triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);
    wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);

    //顶点个数
    int vertexCount = mesh.vertexCount;
    //三角面索引个数
    int faceIndicesCount = mesh.faceCount * 3;
    //边线索引个数
    int edgeIndexCount = mesh.edgeCount * 2 + mesh.sideEdgeCount * 2;
    
    //顶点
    this->positions.reserve(vertexCount);
    for (size_t i = 0; i < vertexCount; ++i)
    {
        this->positions.push_back(FVec3(mesh.pVertex[i].x, mesh.pVertex[i].y, mesh.pVertex[i].z));
    }
    //法线
    this->normals.reserve(vertexCount);
    for (size_t i = 0; i < vertexCount; ++i)
    {
        this->normals.push_back(FVec3(mesh.pNormal[i].x, mesh.pNormal[i].y, mesh.pNormal[i].z));
    }
    //uv
    //颜色

    //索引
    if (vertexCount <= NumLimits<byte>::Max)
    {
        //三角面索引
        Triangles::ElementByteData triIndexData;
        triIndexData.reserve(faceIndicesCount);
        for (size_t i = 0; i < mesh.faceCount; ++i)
        {
            triIndexData.push_back((byte)(mesh.pFace[i * 3 + 0]));
            triIndexData.push_back((byte)(mesh.pFace[i * 3 + 1]));
            triIndexData.push_back((byte)(mesh.pFace[i * 3 + 2]));
        }
        triangles.setDrawElementByteData(std::move(triIndexData));
        //边线索引
        Triangles::ElementByteData sideLineIndexData;
        sideLineIndexData.reserve(edgeIndexCount);
        for (int i = 0; i < mesh.edgeCount; ++i)
        {
            sideLineIndexData.push_back((byte)(mesh.pEdge[i * 2 + 0]));
            sideLineIndexData.push_back((byte)(mesh.pEdge[i * 2 + 1]));
        }
        for (int i = 0; i < mesh.sideEdgeCount; ++i)
        {
            sideLineIndexData.push_back((byte)(mesh.pSideEdge[i * 2 + 0]));
            sideLineIndexData.push_back((byte)(mesh.pSideEdge[i * 2 + 1]));
        }
        wireFrames.setDrawElementByteData(std::move(sideLineIndexData));
    }
    else if (vertexCount <= NumLimits<ushort>::Max)
    {
        //三角面索引
        Triangles::ElementUShortData triIndexData;
        triIndexData.reserve(faceIndicesCount);
        for (int i = 0; i < mesh.faceCount; ++i)
        {
            triIndexData.push_back((ushort)(mesh.pFace[i * 3 + 0]));
            triIndexData.push_back((ushort)(mesh.pFace[i * 3 + 1]));
            triIndexData.push_back((ushort)(mesh.pFace[i * 3 + 2]));
        }
        triangles.setDrawElementUShortData(std::move(triIndexData));
        //边线索引
        Triangles::ElementUShortData sideLineIndexData;
        sideLineIndexData.reserve(edgeIndexCount);
        for (int i = 0; i < mesh.edgeCount; ++i)
        {
            sideLineIndexData.push_back((ushort)(mesh.pEdge[i * 2 + 0]));
            sideLineIndexData.push_back((ushort)(mesh.pEdge[i * 2 + 1]));
        }
        for (int i = 0; i < mesh.sideEdgeCount; ++i)
        {
            sideLineIndexData.push_back((ushort)(mesh.pSideEdge[i * 2 + 0]));
            sideLineIndexData.push_back((ushort)(mesh.pSideEdge[i * 2 + 1]));
        }
        wireFrames.setDrawElementUShortData(std::move(sideLineIndexData));
    }
    else if (vertexCount <= NumLimits<uint>::Max)
    {
        //三角面索引
        Triangles::ElementUIntData triIndexData;
        triIndexData.reserve(faceIndicesCount);
        for (int i = 0; i < mesh.faceCount; ++i)
        {
            triIndexData.push_back((uint)(mesh.pFace[i * 3 + 0]));
            triIndexData.push_back((uint)(mesh.pFace[i * 3 + 1]));
            triIndexData.push_back((uint)(mesh.pFace[i * 3 + 2]));
        }
        triangles.setDrawElementUIntData(std::move(triIndexData));
        //边线索引
        Triangles::ElementUIntData sideLineIndexData;
        sideLineIndexData.reserve(edgeIndexCount);
        for (int i = 0; i < mesh.edgeCount; ++i)
        {
            sideLineIndexData.push_back((uint)(mesh.pEdge[i * 2 + 0]));
            sideLineIndexData.push_back((uint)(mesh.pEdge[i * 2 + 1]));
        }
        for (int i = 0; i < mesh.sideEdgeCount; ++i)
        {
            sideLineIndexData.push_back((uint)(mesh.pSideEdge[i * 2 + 0]));
            sideLineIndexData.push_back((uint)(mesh.pSideEdge[i * 2 + 1]));
        }
        wireFrames.setDrawElementUIntData(std::move(sideLineIndexData));
    }
    else
    {
        assert(0 && "vertexCount > NumLimits<uint>::Max");
    }

    return true;
}

uint MeshLODSelection::getSegment(float arcAngle, float radius) const
{
    switch (_type)
    {
    case WD::MeshLODSelection::T_Normal:
        return uint(24);

    case WD::MeshLODSelection::T_Low:
        return uint(16);

    case WD::MeshLODSelection::T_Middle:
        return uint(24);

    case WD::MeshLODSelection::T_High:
        return uint(40);

    case WD::MeshLODSelection::T_VeryLow:
        return uint(8);

    case WD::MeshLODSelection::T_VeryHigh:
        return uint(64);

    case WD::MeshLODSelection::T_ArcTolerance:
        {
            if (arcAngle > 0.0f && radius > 0.0f)
            {
                uint rStep  =   Min<uint>(128, ArcTravel(arcAngle, radius, _arcTolerance));
                // 因为要生成边线，因此这里分段数必须保证是4的倍数
                float fStep =   static_cast<float>(rStep);
                fStep       /=  4.0f;
                fStep       =   std::round(fStep);
                uint iStep  =   static_cast<int>(fStep);
                return iStep * 4;
            }
        }
        break;
    case WD::MeshLODSelection::T_Segment:
        {
            return Min<uint>(128, _segment);
        }
        break;
    default:
        break;
    }
    
    return uint(24);
}

void MeshLODSelection::calcMd5Key(WD::WDMD5& md5)
{
    md5.addData((int)_type);
    md5.addData(_arcTolerance);
    md5.addData(_segment);
}

bool WD_API BuildLoop(const std::vector<FVec3>& loop
    , std::vector<FVec2>& outLoop
    , const MeshLODSelection& lodSelection)
{
    constexpr auto sLimit = 10000.0;
    constexpr auto sLimitInv = 1.0 / sLimit;
    // 形状至少有三个点
    if (loop.size() < 3)
        return false;
    // 顶点去重
    FVec3Vector tLoop;
    tLoop.reserve(loop.size());
    tLoop.push_back(loop.front());
    for (size_t i = 1; i < loop.size(); ++i)
    {
        // 判断俩顶点距离是否过近
        if (FVec2::DistanceSq(tLoop.back().xy(), loop[i].xy()) <= NumLimits<float>::Epsilon)
        {
            // 保留更大的圆角半径
            tLoop.back().x = Max(tLoop.back().z, loop[i].z);
        }
        else
        {
            tLoop.push_back(loop[i]);
        }
    }
    // 形状至少有三个有效点
    if (tLoop.size() < 3)
        return false;
    // 判断顶点是否为顺时针，若为顺时针，则需要将顶点反向,改为逆时针
    if (FPolygon2::IsCW(tLoop))
    {
        std::reverse(tLoop.begin(), tLoop.end());
    }
    // 根据圆角半径,生成带有圆角的多边形顶点列表
    FVec2Vector tLoop2;
    // 预先分配足够大的内存
    tLoop2.reserve(tLoop.size() * 30);
    for (size_t i = 0; i < tLoop.size(); ++i)
    {
        // 当前点索引
        const size_t idxCurr = i;
        // 圆角半径
        float radius = tLoop[idxCurr].z;
        // 当前点
        const FVec2 ptCurr = tLoop[idxCurr].xy();
        // 半径接近0,不生成圆角
        if (Abs(radius) <= NumLimits<float>::Epsilon)
        {
            tLoop2.push_back(ptCurr);
            continue;
        }
        // 前一个点的索引
        const size_t idxPrev = i == 0 ? tLoop.size() - 1 : i - 1;
        // 后一个点的索引
        const size_t idxNext = i == tLoop.size() - 1 ? 0 : i + 1;
        // 前一个点
        const FVec2 ptPrev = tLoop[idxPrev].xy();
        // 后一个点
        const FVec2 ptNext = tLoop[idxNext].xy();

        // 如果当前点与前一个点和后一个点三点共线, 说明当前点不是角上的点, 不生成圆角
        if (FPolygon2::IsCollinear<FVec2>({ ptPrev, ptCurr, ptNext }))
        {
            tLoop2.push_back(ptCurr);
            continue;
        }

        // 计算生成圆弧参数
        FVec2 outCenter = FVec2::Zero();
        FVec2 outStart = FVec2::Zero();
        FVec2 outEnd = FVec2::Zero();
        if (!CalcArcParamWithVertex(ptCurr, ptPrev, ptNext, radius, outCenter, outStart, outEnd))
        {
            // 参数计算失败，直接加入当前顶点
            tLoop2.push_back(ptCurr);
            continue;
        }
        FVec2Vector rPts;
        // 计算圆弧顶点
        ArcGeneration(outCenter
            , (outStart - outCenter).normalized()
            , (outEnd - outCenter).normalized()
            , Abs(radius)
            , lodSelection
            , rPts);
        // 生成的圆弧顶点个数小于3, 说明生成失败, 直接加入当前顶点
        if (rPts.size() < 3)
        {
            tLoop2.push_back(ptCurr);
            continue;
        }
        // 将圆弧追加到顶点列表中
        tLoop2.insert(tLoop2.end(), rPts.begin(), rPts.end());
    }

    ClipperLib::Path path;
    path.reserve(tLoop2.size());
    for (const auto& v : tLoop2)
    {
        auto iX = static_cast<ClipperLib::cInt>(v.x * sLimit);
        auto iY = static_cast<ClipperLib::cInt>(v.y * sLimit);
        path.push_back(ClipperLib::IntPoint(iX, iY));
    }
    ClipperLib::Paths outPaths;
    ClipperLib::SimplifyPolygon(path, outPaths);
    if (outPaths.empty())
        return false;

    // 可能会切分出多个轮廓，这里只需要一个主轮廓
    // 使用面积最大的多边形作为主轮廓
    const ClipperLib::Path* pPath = nullptr;
    double maxArea = 0.0;
    for (const auto& tPath : outPaths) 
    {
        auto tArea = ClipperLib::Area(tPath);
        if (tArea <= maxArea)
            continue;
        maxArea = tArea;
        pPath = &tPath;
    }

    // 没拿到有效路径
    if (pPath == nullptr)
        return false;

    const auto& outPath = *pPath;
    outLoop.reserve(outPath.size());
    for (const auto& v : outPath)
    {
        auto fX = static_cast<float>(static_cast<double>(v.X) * sLimitInv);
        auto fY = static_cast<float>(static_cast<double>(v.Y) * sLimitInv);
        outLoop.push_back(FVec2(fX, fY));
    }

    return outLoop.size() >= 3;
}


std::vector<FVec3> MakeArc(float r, uint segs, float z, float angle)
{
    std::vector<FVec3> verts;
    verts.reserve(segs + 1);
    // 单位角度
    auto angSeg = DegToRad(angle / segs);
    // 计算顶点
    for (uint i = 0; i < segs + 1; i++)
    {
        auto ang = i * angSeg;
        verts.push_back({ r * Cos(ang), r * Sin(ang), z });
    }
    return verts;
}

std::vector<FVec3> MakeCircle(float r, uint segs, float z)
{
    std::vector<FVec3> verts;
    verts.reserve(segs);
    // 单位角度
    auto angSeg = DegToRad(360.0F / segs);
    // 计算顶点
    for (uint i = 0; i < segs; i++)
    {
        auto ang = i * angSeg;
        verts.push_back({ r * Cos(ang), r * Sin(ang), z });
    }
    return verts;
}

WD_NAMESPACE_END

