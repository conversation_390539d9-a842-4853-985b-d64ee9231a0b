#pragma once

#include "StandardPrimitiveCommon.h"

WD_NAMESPACE_BEGIN


template <typename T>
inline void TMeshTransform<T>::reset()
{
    this->translation = TVec3<T>::Zero();
    this->rotation = TQuat<T>::Identity();
}

template <typename T>
bool MeshStruct::setIndices(size_t vertexCount
    , const T* const pTriIndices
    , size_t triIndicesCount
    , const T* const pWireFrameIndices
    , size_t wireFrameIndicesCount)
{
    if (vertexCount == 0)
        return false;

    triangles.setPrimitiveType(Triangles::PT_Triangles);
    wireFrames.setPrimitiveType(Triangles::PT_Lines);

    if (vertexCount <= NumLimits<byte>::Max)
    {
        //三角面索引
        triangles.setDrawElementByteData(corvertIndicesT<size_t, byte>(pTriIndices, triIndicesCount));
        //边线索引
        wireFrames.setDrawElementByteData(corvertIndicesT<size_t, byte>(pWireFrameIndices, wireFrameIndicesCount));
    }
    else if (vertexCount <= NumLimits<ushort>::Max)
    {
        //三角面索引
        triangles.setDrawElementUShortData(corvertIndicesT<size_t, ushort>(pTriIndices, triIndicesCount));
        //边线索引
        wireFrames.setDrawElementUShortData(corvertIndicesT<size_t, ushort>(pWireFrameIndices, wireFrameIndicesCount));
    }
    else if (vertexCount <= NumLimits<uint>::Max)
    {
        //三角面索引
        triangles.setDrawElementUIntData(corvertIndicesT<size_t, uint>(pTriIndices, triIndicesCount));
        //边线索引
        wireFrames.setDrawElementUIntData(corvertIndicesT<size_t, uint>(pWireFrameIndices, wireFrameIndicesCount));
    }
    else
    {
        assert(0 && "vertexCount > NumLimits<uint>::Max");
        return false;
    }

    return true;
}
template <typename T>
inline bool MeshStruct::setIndices(size_t vertexCount
    , const std::vector<T>& triIndices
    , const std::vector<T>& wireFrameIndices)
{
    return setIndices<T>(vertexCount
        , triIndices.empty() ? nullptr : &triIndices.front()
        , triIndices.size()
        , wireFrameIndices.empty() ? nullptr : wireFrameIndices.data()
        , wireFrameIndices.size());
}
template <typename T0, typename T1>
std::vector<T1> MeshStruct::corvertIndicesT(const T0* const pSrcIndices, size_t srcIndicesCount)
{
    std::vector<T1> tarIndices;
    if (pSrcIndices == nullptr || srcIndicesCount == 0)
        return tarIndices;

    tarIndices.reserve(srcIndicesCount);
    for (size_t i = 0; i < srcIndicesCount; ++i)
    {
        tarIndices.push_back(static_cast<T1>(pSrcIndices[i]));
    }
    return tarIndices;
}

inline MeshLODSelection::MeshLODSelection(Type type)
    : _type(type)
    , _arcTolerance(10.0f)
    , _segment(16)
{
}
inline MeshLODSelection::MeshLODSelection(float arcTolerance, Type type)
    : _type(type)
    , _arcTolerance(arcTolerance)
    , _segment(16)
{

}
inline MeshLODSelection::MeshLODSelection(uint segment, Type type)
    : _type(type)
    , _arcTolerance(10.0f)
    , _segment(segment)
{

}
inline MeshLODSelection::MeshLODSelection(const MeshLODSelection& right)
{
    _type = right._type;
    _arcTolerance = right._arcTolerance;
    _segment = right._segment;
}

inline void MeshLODSelection::setType(Type type)
{
    _type = type;
}
inline MeshLODSelection::Type MeshLODSelection::type() const
{
    return _type;
}
inline void MeshLODSelection::setArcTolerance(float arcTolerance)
{
    _arcTolerance = arcTolerance;
}
inline float MeshLODSelection::arcTolerance() const
{
    return _arcTolerance;
}
inline void MeshLODSelection::setSegment(uint segment)
{
    _segment = segment;
}

inline MeshLODSelection& MeshLODSelection::operator = (const MeshLODSelection& right)
{
    _type = right._type;
    _arcTolerance = right._arcTolerance;
    _segment = right._segment;
    return *this;
}
inline bool MeshLODSelection::operator == (const MeshLODSelection& right) const
{
    return  _type == right._type
        && _arcTolerance == right._arcTolerance
        && _segment == right._segment;;
}
inline bool MeshLODSelection::operator != (const MeshLODSelection& right) const
{
    return !((*this) == right);
}
inline bool MeshLODSelection::operator < (const MeshLODSelection& right) const
{
    if (_type == right._type)
    {
        if (_arcTolerance == right._arcTolerance)
        {
            return _segment < right._segment;
        }
        else
        {
            return _arcTolerance < right._arcTolerance;
        }
    }
    else
    {
        return _type < right._type;
    }
}
inline bool MeshLODSelection::operator <= (const MeshLODSelection& right) const
{
    return ((*this) == right) || ((*this) < right);
}
inline bool MeshLODSelection::operator > (const MeshLODSelection& right) const
{
    return !((*this) <= right);
}
inline bool MeshLODSelection::operator >= (const MeshLODSelection& right) const
{
    return !((*this) < right);
}


template <typename T>
void ArcGeneration(const TVec2<T>& center
    , const TVec2<T>& vStart
    , const TVec2<T>& vEnd
    , T radius
    , const MeshLODSelection& lodSelection
    , TVec2Vector<T>& outVertices)
{
    if (vStart.lengthSq() <= NumLimits<T>::Epsilon)
    {
        assert(false && "圆心到圆弧起点的向量无效");
        return;
    }
    if (vEnd.lengthSq() <= NumLimits<T>::Epsilon)
    {
        assert(false && "圆心到圆弧终点的向量无效");
        return;
    }
    if (radius <= T(0))
    {
        assert(false && "半径必须大于0");
        return;
    }
    // 圆弧的圆心角
    const T arcAngle = TVec2<T>::Angle(vStart, vEnd);
    // 生成圆弧的旋转轴
    const TVec3<T> vS3 = TVec3<T>(vStart);
    const TVec3<T> vE3 = TVec3<T>(vEnd);
    const TVec3<T> axis = TVec3<T>::Normalize(TVec3<T>::Cross(vS3, vE3));
    // 使用旋转法生成圆弧
    uint segNumber = lodSelection.getSegment(arcAngle, radius);
    const T segNumberF = static_cast<T>(segNumber);
    for (uint step = 0; step <= segNumber; ++step)
    {
        const T stepAng = arcAngle * static_cast<T>(step) / segNumberF;
        const TMat3<T> rotMat = TMat3<T>::MakeRotation(stepAng, axis);
        TVec2<T> pt = (rotMat * vS3 * radius).xy() + center;
        outVertices.push_back(pt);
    }
}

template <typename T>
bool CalcArcParamWithVertex(const TVec2<T>& currPoint
    , const TVec2<T>& prevPoint
    , const TVec2<T>& nextPoint
    , T radius
    , TVec2<T>& outCenter
    , TVec2<T>& outStart
    , TVec2<T>& outEnd
    , TVec2<T>* pOutArcCenter)
{
    if (TVec2<T>::DistanceSq(currPoint, prevPoint) <= NumLimits<T>::Epsilon
        || TVec2<T>::DistanceSq(currPoint, nextPoint) <= NumLimits<T>::Epsilon
        || TVec2<T>::DistanceSq(prevPoint, nextPoint) <= NumLimits<T>::Epsilon)
    {
        assert(false && "三个点之间不能两两重合");
        return false;
    }
    if (Abs(radius) <= NumLimits<T>::Epsilon)
    {
        assert(false && "半径不能等于0");
        return false;
    }
    // 前一个点与当前点构成的向量
    const TVec2<T> vPrev = prevPoint - currPoint;
    const TVec2<T> nVPrev = vPrev.normalized();
    // 后一个点与当前点构成的向量
    const TVec2<T> vNext = nextPoint - currPoint;
    const TVec2<T> nVNext = vNext.normalized();
    // 计算条边构成向量的夹角
    const T theta = TVec2<T>::Angle(nVPrev, nVNext);
    // 如果夹角是0°或180°，则圆角半径不生效
    if (theta <= NumLimits<float>::Epsilon
        || theta >= 180.0 - NumLimits<float>::Epsilon) 
    {
        return false;
    }
    // 半径小于0的情况
    if (radius < T(0))
    {
        outCenter = currPoint;
        outStart = currPoint + nVPrev * Abs(radius);
        outEnd = currPoint + nVNext * Abs(radius);
        if (pOutArcCenter != nullptr)
        {
            // 计算两条边的角平分线方向
            const TVec2<T> vBisecor = TVec2<T>::Normalize(nVPrev + nVNext);
            // 圆弧周上的中心点
            *pOutArcCenter = currPoint + vBisecor * Abs(radius);
        }
    }
    // 半径大于0的情况
    else
    {
        // 计算圆心点在角平分线方向上离当前顶点的距离
        const T disCen = radius / Sin(DegToRad(theta * 0.5f));
        // 计算圆弧起点和终点到当前顶点的距离(勾股定理)
        const T radius2 = radius * radius;
        const T disCen2 = disCen * disCen;
        const T disPN = Sqrt(disCen2 - radius2);
        // 计算两条边的角平分线方向
        const TVec2<T> vBisecor = TVec2<T>::Normalize(nVPrev + nVNext);
        // 圆心点
        outCenter = currPoint + vBisecor * disCen;
        // 圆弧起点
        outStart = currPoint + nVPrev * disPN;
        // 圆弧终点
        outEnd = currPoint + nVNext * disPN;
        if (pOutArcCenter != nullptr)
        {
            *pOutArcCenter = outCenter - vBisecor * radius;
        }
    }
    return true;
}

WD_NAMESPACE_END