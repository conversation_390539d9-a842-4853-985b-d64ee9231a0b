#include "RevolutionBuilder.h"

WD_NAMESPACE_BEGIN

MeshStruct RevolutionBuilder::Mesh(const FVec3& center
    , const FVec3& axis
    , float angle
    , const FVec3Vector& loop
    , const MeshLODSelection& lodSelection)
{
    WDUnused(center);
    WDUnused(axis);
    MeshStruct ret;
    // 角度不能等于0
    if (Abs(angle) <= NumLimits<float>::Epsilon)
        return ret;
    // 生成环顶点
    FVec2Vector outLoop;
    if (!BuildLoop(loop, outLoop, lodSelection))
        return ret;

    FVec3Vector tVertices;
    tVertices.reserve(outLoop.size());
    size_t tSideLineCnt = 0;
    for (size_t i = 0; i < outLoop.size(); ++i)
    {
        const auto& v = outLoop[i];
        const auto& vPrev = outLoop[i == 0 ? outLoop.size() - 1 : i - 1];
        const auto& vNext = outLoop[i == outLoop.size() - 1 ? 0 : i + 1];

        const auto vecPrev = vPrev - v;
        const auto vecNext = vNext - v;
        float tmpAngle = FVec2::Angle(vecPrev, vecNext);

        bool bSideLine = tmpAngle < 160.0f;
        tVertices.push_back(FVec3(v, bSideLine ? 1.0f : 0.0f));
        if (bSideLine)
            tSideLineCnt++;
    }

    // 如果没有生成边线，默认计算四条
    if (tSideLineCnt == 0)
    {
        tVertices[0].z = 1.0f;
        tVertices[(tVertices.size() * 1) / 4].z = 1.0f;
        tVertices[(tVertices.size() * 2) / 4].z = 1.0f;
        tVertices[(tVertices.size() * 3) / 4].z = 1.0f;
    }

    // 旋转默认为X轴
    FVec3 rotAxis = FVec3::AxisX();

    // 用于计算侧面分段数的半径,因为这里是绕X轴旋转，因此拿到y分量的最大的值即为最大半径
    float maxSegmentRadius = 0.0f;
    for (const auto& v : loop)
    {
        if (v.y > maxSegmentRadius)
            maxSegmentRadius = v.y;
    }
    // 侧面分段数
    const uint sideSegments     = lodSelection.getSegment(Abs(angle), maxSegmentRadius);
    const float sideSegmentsF   = static_cast<float>(sideSegments);

    // 对多边形进行三角细分,获取顶点索引
    std::vector<uint> shapeIndices = TEarcut<FVec3, uint>::Exec(tVertices);
    uint loopPointCount     = static_cast<uint>(tVertices.size());
    uint shapeIndicesCount  = static_cast<uint>(shapeIndices.size());
    // 这里给顶点和索引尽可能分配比较大的内存,以加快生成速度
    std::vector<FVec3> tPositions;
    tPositions.reserve(loopPointCount * (sideSegments + 2)); // 顶面，底面以及侧面
    std::vector<FVec3> tNormals;
    tNormals.reserve(loopPointCount * (sideSegments + 2)); // 顶面，底面以及侧面
    std::vector<uint>  tTriIndices;
    tTriIndices.reserve(shapeIndicesCount * 2 + loopPointCount * 6 * sideSegments); // 底面，底面以及侧面
    std::vector<uint>  tSideIndices;
    tSideIndices.reserve(loopPointCount * 2 + loop.size() * 3 * sideSegments); //顶面，底面，以及侧面(假定loop的每个顶点都带有圆角时，每个顶点都会生成三条边线)
    // 是否绕着X正方向旋转, 即 angle > 0, 如果angle < 0, 表示绕着X反方向旋转
    bool bPositive = angle > 0.0f;
    //计算底面, 如果角度 >= 360°时，不需要生成底面
    if (Abs(angle) < 360.0f)
    {
        //顶点位置和法线
        FVec3 tNormal = bPositive ? FVec3::AxisNZ() : FVec3::AxisZ();
        for (size_t i = 0; i < loopPointCount; ++i)
        {
            FVec3 rPt = FVec3(tVertices[i]);
            tPositions.push_back(rPt);
            tNormals.push_back(tNormal);
        }
        //三角面索引
        {
            const std::vector<uint>& bottomShapeIndices = shapeIndices;
            if (bPositive)
                tTriIndices.insert(tTriIndices.end(), bottomShapeIndices.rbegin(), bottomShapeIndices.rend());
            else
                tTriIndices.insert(tTriIndices.end(), bottomShapeIndices.begin(), bottomShapeIndices.end());
        }
        //边线索引
        {
            for (uint i = 0; i < loopPointCount; ++i)
            {
                tSideIndices.push_back(i);
                tSideIndices.push_back(i + 1);
            }
            // 这里最后一个索引应该修改为0,表示最后一个点与第一个点构成的边线
            if (!tSideIndices.empty())
                tSideIndices.back() = uint(0);
        }
    }
    //计算顶面, 如果角度 > 360°时，不需要生成顶面
    if (Abs(angle) < 360.0f)
    {
        // 统计当前的顶点个数
        uint pointCount = static_cast<uint>(tPositions.size());
        // 顶点位置和法线
        FVec3 tNormal   = bPositive ? FVec3::AxisZ() : FVec3::AxisNZ();
        // 到顶面顶点的旋转矩阵
        FMat3 rMat      = FMat3::MakeRotation(angle, rotAxis);
        for (size_t i = 0; i < loopPointCount; ++i)
        {
            FVec3 rPt   = rMat * FVec3(tVertices[i]);
            tPositions.push_back(rPt);
            FVec3 rNor  = (rMat * tNormal).normalized();
            tNormals.push_back(rNor);
        }
        //三角面索引
        {
            std::vector<uint> topShapeIndices = shapeIndices;
            for (auto& idx : topShapeIndices)
                idx += pointCount;

            if (bPositive)
                tTriIndices.insert(tTriIndices.end(), topShapeIndices.begin(), topShapeIndices.end());
            else
                tTriIndices.insert(tTriIndices.end(), topShapeIndices.rbegin(), topShapeIndices.rend());
        }
        //边线索引
        {
            for (uint i = 0; i < loopPointCount; ++i)
            {
                tSideIndices.push_back(pointCount + i);
                tSideIndices.push_back(pointCount + i + 1);
            }
            // 这里最后一个索引应该修改为pointCount,表示最后一个点与第一个点构成的边线
            if (!tSideIndices.empty())
                tSideIndices.back() = pointCount;
        }
    }
    //计算侧面
    {
        // 当前已经添加的顶点个数
        uint pointCount = static_cast<uint>(tPositions.size());
        // 顶点位置和法线
        // 这里根据侧面的每一条棱来添加顶点，即一条棱添加一个底部顶点和顶部顶点
        // 但是因为可能存在顶点法线和面法线
        //  所以:
        //      如果是面法线，需要给这条棱上分别添加两次底部顶点和顶部顶点(虽然顶点位置相同，但是法线不同)
        //      如果是顶点法线,只需要给这条棱上添加一次底部顶点和顶部顶点(位置相同，法线也相同)
        // 其中 TmpVN::first的 x,y 分量表示顶点位置
        //  z 分量表示是否生成侧面边线
        //  w 分量表示是否与下一个棱生成三角面, 因为如果是面法线时一个顶点位置会被连续加入两次，而这连续的两次顶点之间是不需要生成三角面的
        //  TmpVN::second表示法线
        using TmpVN = std::pair<FVec4, FVec3>;
        using TmpVNs = std::vector<TmpVN>;
        TmpVNs tVNs;
        tVNs.reserve(loopPointCount * 2);
        for (uint i = 0; i < loopPointCount; ++i)
        {
            // 当前顶点索引
            uint ptIndex        = i;
            // 前一个顶点索引
            uint ptIndexPrev    = i == 0 ? loopPointCount - 1 : i - 1;
            // 下一个顶点索引    
            uint ptIndexNext    = i == loopPointCount - 1 ? 0 : i + 1;
            // 获取顶点
            const FVec3& pt     = tVertices[ptIndex];
            const FVec3& ptPrev = tVertices[ptIndexPrev];
            const FVec3& ptNext = tVertices[ptIndexNext];
            // 使用 当前、前一个、下一个底部顶点构成的两个方向向量的夹角计算该棱上的两个顶点是需要顶点法线还是需要面法线
            const FVec2 vecPrev = ptPrev.xy() - pt.xy();
            const FVec2 vecNext = ptNext.xy() - pt.xy();
            float tAng          = FVec2::Angle(vecPrev, vecNext);
            // 是否需要面法线, 如果不是面法线则认为是需要顶点法线
            bool bFaceNor       = tAng < 160.0f;
            // 需要面法线，则分别使用当前顶点与前一个、下一个顶点构成的方向向量与Z叉乘获取面法线
            if (bFaceNor)
            {
                // 分别计算左边面的法线和右边面的法线
                FVec3 faceNorL = FVec3::Cross(FVec3(-vecPrev, 0.0f) , FVec3::AxisZ()).normalized();
                FVec3 faceNorR = FVec3::Cross(FVec3(vecNext, 0.0f), FVec3::AxisZ()).normalized();
                // 加入顶点位置以及法线
                tVNs.emplace_back(FVec4(pt, 0.0f), faceNorL);
                // 第二个顶点不需要生成侧面边线
                tVNs.emplace_back(FVec4(pt.xy(), 0.0f, 1.0f), faceNorR);
            }
            // 需要顶点法线，直接计算当前顶点与前一个、下一个顶点构成的方向向量的角平分线
            else
            {
                FVec2 tNor = -(vecPrev.normalized() + vecNext.normalized()).normalized();
                auto triArea = FTriangle2::Area(ptPrev.xy(), pt.xy(), ptNext.xy());
                // 如果三点构成三角形面积接近0，说明三点共线，需要使用其他方法计算法线
                if (triArea >= -0.0001f && triArea <= 0.0001f)
                    tNor = FVec3::Cross(FVec3::AxisZ(), FVec3(vecPrev.normalized(), 0.0f)).xy();
                // 如果三点构成三角形面积小于0，说明当前顶点是凹顶点，需要把原始计算的法线反向
                else if (triArea < -0.0001f)
                    tNor = -tNor;
                // 加入顶点位置以及法线
                tVNs.emplace_back(FVec4(pt, 1.0f), FVec3(tNor, 0.0f));
            }
        }
        // 处理侧面分段
        for (uint i = 0; i <= sideSegments; ++i)
        {
            const float iF      = static_cast<float>(i);
            const float step    = iF / sideSegmentsF;
            // 到顶面顶点的旋转矩阵
            FMat3 rMat  = FMat3::MakeRotation(angle * step, rotAxis);
            uint vnSz   = static_cast<uint>(tVNs.size());
            for (uint j = 0; j < vnSz; ++j)
            {
                const FVec4& pt     = tVNs[j].first;
                const FVec3& nor    = tVNs[j].second;
                // 旋转顶点
                FVec3 rPt           = rMat * FVec3(pt.x, pt.y, 0.0f);
                tPositions.push_back(rPt);
                // 旋转法线
                FVec3 rNor          = rMat * FVec3(nor);
                tNormals.push_back(rNor.normalized());
            }
            if (i == sideSegments)
                continue;
            // 三角面和边线索引
            for (uint j = 0; j < vnSz; ++j)
            {
                uint idxB       = i * vnSz + j;
                uint idxBNext   = j == vnSz - 1 ? i * vnSz : idxB + 1;
                uint idxT       = idxB + vnSz;
                uint idxTNext   = idxBNext + vnSz;
                // 三角面, 这里根据w分量是否大于0来决定是否与下一条棱生成三角面
                if (tVNs[j].first.w > 0.0f)
                {
                    if (bPositive)
                    {
                        tTriIndices.push_back(pointCount + idxT);
                        tTriIndices.push_back(pointCount + idxB);
                        tTriIndices.push_back(pointCount + idxBNext);

                        tTriIndices.push_back(pointCount + idxT);
                        tTriIndices.push_back(pointCount + idxBNext);
                        tTriIndices.push_back(pointCount + idxTNext);
                    }
                    else
                    {
                        tTriIndices.push_back(pointCount + idxTNext);
                        tTriIndices.push_back(pointCount + idxBNext);
                        tTriIndices.push_back(pointCount + idxT);

                        tTriIndices.push_back(pointCount + idxBNext);
                        tTriIndices.push_back(pointCount + idxB);
                        tTriIndices.push_back(pointCount + idxT);
                    }
                }
                // 边线, 这里根据z分量是否大于0来决定是否生成边线
                if (tVNs[j].first.z > 0.0f)
                {
                    tSideIndices.push_back(pointCount + idxB);
                    tSideIndices.push_back(pointCount + idxT);
                }
            }
        }
    }
    ret.positions   = std::move(tPositions);
    ret.normals     = std::move(tNormals);
    //三角面索引
    ret.triangles.setDrawElementUIntData(std::move(tTriIndices));
    ret.triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);
    //边线索引
    ret.wireFrames.setDrawElementUIntData(std::move(tSideIndices));
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);

    return ret;
}

FKeyPoints RevolutionBuilder::KeyPoints(const FVec3& center
    , const FVec3& axis
    , float angle
    , const FVec3Vector& loop)
{
    WDUnused(center);
    WDUnused(axis);
    FKeyPoints ret;
    // 形状至少有三个点
    if (loop.size() < 3)
        return ret;
    // 角度不能等于0
    if (Abs(angle) <= NumLimits<float>::Epsilon)
        return ret;
    // 旋转默认为X轴
    FVec3 rotAxis = FVec3::AxisX();
    // loop中, x,y代表顶点位置，z代表顶点的圆角半径
    FVec3Vector tLoop;
    tLoop.reserve(loop.size());
    tLoop.push_back(loop.front());
    for (size_t i = 1; i < loop.size(); ++i)
    {
        // 判断俩顶点距离是否过近
        if (FVec2::DistanceSq(tLoop.back().xy(), loop[i].xy()) <= NumLimits<float>::Epsilon)
        {
            // 保留更大的圆角半径
            tLoop.back().z = Max(tLoop.back().z, loop[i].z);
        }
        else
        {
            tLoop.push_back(loop[i]);
        }
    }
    // 形状至少有三个有效点
    if (tLoop.size() < 3)
        return ret;
    // 判断顶点是否为顺时针，若为顺时针，则需要将顶点反向,改为逆时针
    if (FPolygon2::IsCW(tLoop))
    {
        std::reverse(tLoop.begin(), tLoop.end());
    }
    // 根据圆角半径,生成带有圆角的多边形顶点列表
    FVec2Vector tVertices;
    for (size_t i = 0; i < tLoop.size(); ++i)
    {
        // 当前点索引
        const size_t idxCurr = i;
        // 圆角半径
        const float radius = tLoop[idxCurr].z;
        // 当前点
        const FVec2 ptCurr = tLoop[idxCurr].xy();
        // 半径接近0,不生成圆角
        if (Abs(radius) <= NumLimits<float>::Epsilon)
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        // 前一个点的索引
        const size_t idxPrev = i == 0 ? tLoop.size() - 1 : i - 1;
        // 后一个点的索引
        const size_t idxNext = i == tLoop.size() - 1 ? 0 : i + 1;
        // 前一个点
        const FVec2 ptPrev = tLoop[idxPrev].xy();
        // 后一个点
        const FVec2 ptNext = tLoop[idxNext].xy();
        // 计算圆弧参数
        FVec2 outCenter = FVec2::Zero();
        FVec2 outStart = FVec2::Zero();
        FVec2 outEnd = FVec2::Zero();
        FVec2 outArcCenter = FVec2::Zero();
        if (!CalcArcParamWithVertex(ptCurr, ptPrev, ptNext, radius, outCenter, outStart, outEnd, &outArcCenter))
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        tVertices.push_back(outStart);
        tVertices.push_back(outArcCenter);
        tVertices.push_back(outEnd);
    }

    FVec3 upDir = angle > 0.0f ? FVec3::AxisZ() : FVec3::AxisNZ();
    size_t vCnt = tVertices.size();
    ret.resize(2 * vCnt);
    FMat3 rMat = FMat3::MakeRotation(angle, FVec3::AxisX());
    for (size_t i = 0; i < tVertices.size(); ++i)
    {
        // 底面关键点
        ret[i] = FKeyPoint(FVec3(tVertices[i], 0.0f), -upDir);
        // 顶面关键点
        // ret[i + vCnt] = FKeyPoint(rMat * FVec3(tVertices[i], 0.0f), rMat * upDir);
    }
    return ret;
}

FVec3Vector RevolutionBuilder::IntersectPoints(const FVec3& center
    , const FVec3& axis
    , float angle
    , const FVec3Vector& loop)
{
    WDUnused(center);
    WDUnused(axis);
    FVec3Vector ret;
    // 形状至少有三个点
    if (loop.size() < 3)
        return ret;
    // 角度不能等于0
    if (Abs(angle) <= NumLimits<float>::Epsilon)
        return ret;
    // 旋转默认为X轴
    FVec3 rotAxis = FVec3::AxisX();
    // loop中, x,y代表顶点位置，z代表顶点的圆角半径
    FVec3Vector tLoop;
    tLoop.reserve(loop.size());
    tLoop.push_back(loop.front());
    for (size_t i = 1; i < loop.size(); ++i)
    {
        // 判断俩顶点距离是否过近
        if (FVec2::DistanceSq(tLoop.back().xy(), loop[i].xy()) <= NumLimits<float>::Epsilon)
        {
            // 保留更大的圆角半径
            tLoop.back().z = Max(tLoop.back().z, loop[i].z);
        }
        else
        {
            tLoop.push_back(loop[i]);
        }
    }
    // 形状至少有三个有效点
    if (tLoop.size() < 3)
        return ret;
    // 判断顶点是否为顺时针，若为顺时针，则需要将顶点反向,改为逆时针
    if (FPolygon2::IsCW(tLoop))
    {
        std::reverse(tLoop.begin(), tLoop.end());
    }
    // 根据圆角半径,生成带有圆角的多边形顶点列表
    FVec2Vector tVertices;
    for (size_t i = 0; i < tLoop.size(); ++i)
    {
        // 当前点索引
        const size_t idxCurr = i;
        // 圆角半径
        const float radius = tLoop[idxCurr].z;
        // 当前点
        const FVec2 ptCurr = tLoop[idxCurr].xy();
        // 半径接近0,不生成圆角
        if (Abs(radius) <= NumLimits<float>::Epsilon)
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        // 前一个点的索引
        const size_t idxPrev = i == 0 ? tLoop.size() - 1 : i - 1;
        // 后一个点的索引
        const size_t idxNext = i == tLoop.size() - 1 ? 0 : i + 1;
        // 前一个点
        const FVec2 ptPrev = tLoop[idxPrev].xy();
        // 后一个点
        const FVec2 ptNext = tLoop[idxNext].xy();
        // 计算圆弧参数
        FVec2 outCenter = FVec2::Zero();
        FVec2 outStart = FVec2::Zero();
        FVec2 outEnd = FVec2::Zero();
        FVec2 outArcCenter = FVec2::Zero();
        if (!CalcArcParamWithVertex(ptCurr, ptPrev, ptNext, radius, outCenter, outStart, outEnd, &outArcCenter))
        {
            tVertices.push_back(ptCurr);
            continue;
        }
        tVertices.push_back(outStart);
        tVertices.push_back(outArcCenter);
        tVertices.push_back(outEnd);
    }

    FVec3 upDir = angle > 0.0f ? FVec3::AxisZ() : FVec3::AxisNZ();
    size_t vCnt = tVertices.size();
    ret.reserve(2 * vCnt);
    FMat3 rMat = FMat3::MakeRotation(angle, FVec3::AxisX());
    for (size_t i = 0; i < tVertices.size(); ++i)
    {
        // 底面关键点
        ret.emplace_back(FVec3(tVertices[i], 0.0f));
        // 顶面关键点
        ret.emplace_back(rMat * FVec3(tVertices[i], 0.0f));
    }
    return ret;
}

bool RevolutionBuilder::SREVToREVO(const FVec3& paax
    , const FVec3& paaxPos
    , const FVec3& pbax
    , const float& pang
    , const float& px
    , const float& py
    , const float& pz
    , const VertexDatas& vertexDatas
    , FMeshTransform& outTransform
    , FVec3& outCenter
    , FVec3& outAxis
    , float& outAngle
    , FVec3Vector& outLoop)
{
    // 校验顶点参数不能小于3
    if (vertexDatas.size() < 3)
    {
        assert(false);
        return false;
    }
    // paax作为SREV的X轴 pbax作为SREV的Y轴
    // 校验PAAX与pbax
    // paax与pbax是否零向量
    if (paax.lengthSq() <= NumLimits<float>::Epsilon
        && pbax.lengthSq() <= NumLimits<float>::Epsilon)
    {
        assert(false);
        return false;
    }
    // paax与pbax是否垂直 (暂时按照垂直进行判断，如果后面有切变的话再进行修改)
    if (!FVec3::OnPrpendicular(paax, pbax))
    {
        assert(false);
        return false;
    }
    // 将所有点按照没有旋转来处理，即所有点都在XOY平面上
    // 因为现在半径没有使用，默认是0.0f，使用pair.first作为三维顶点
    outCenter   = FVec3::Zero();
    outAxis     = paax;
    outAngle    = pang;
    // 生成旋转面在XOY平面上，旋转体原点是(0,0,0)，未旋转的旋转体
    // 旋转轴为PAAX(详见PDMS基础图元.docx)
    outLoop.reserve(vertexDatas.size());
    for (size_t i = 0; i < vertexDatas.size(); i++)
    {
        outLoop.push_back(vertexDatas[i]);
    }
    // 使用PAAX与PBAX计算旋转体的旋转
    FMat4   rMat    = FMat3::MakeRotationXY(paax, pbax);
    // 使用PX,PY,PZ计算旋转体的偏移 
    FVec3   trans   = FVec3(px, py, pz) + paaxPos;

    // 保存变换信息
    outTransform.rotation       = FMat4::ToQuat(rMat);
    outTransform.translation    = trans;

    return true;
}

MeshStruct RevolutionBuilder::SideLines(const FVec3& center
    , const FVec3& axis
    , float angle
    , const FVec3Vector& loop
    , const MeshLODSelection& lodSelection)
{
    MeshStruct ret;
    // 处理环状点，使其落在旋转轴和环的第一个点所在的平面上
    // 形状至少有三个点
    if(loop.size() < 3)
        return ret;
    // 轴不能是0向量
    if (axis.lengthSq() <= NumLimits<float>::Epsilon)
        return ret;
    // 旋转角度不能为0
    if (Abs(angle) <= NumLimits<float>::Epsilon)
        return ret;
    // 使用归一化后的旋转轴
    auto normalAxis = axis.normalized();

    // 校验前三个点两两不重合
    double dis0 = FVec3::DistanceSq(loop[0], loop[1]);
    double dis1 = FVec3::DistanceSq(loop[1], loop[2]);
    double dis2 = FVec3::DistanceSq(loop[2], loop[0]);
    if (dis0 <= NumLimits<float>::Epsilon
        || dis1 <= NumLimits<float>::Epsilon
        || dis2 <= NumLimits<float>::Epsilon)
        return ret;

    // 使用前三个点生成平面
    FPlane plane(loop[0], loop[1], loop[2]);
    // 获取此平面的法线
    const FVec3& planeNor = plane.normal; 
    // 使用旋转中心与旋转平面的起点构成向量
    FVec3 centerToStartVector = loop[0] - center;
    // 求出旋转轴与旋转中心与起点形成向量的叉乘
    FVec3 tAxis = FVec3::Cross(centerToStartVector ,normalAxis);
    
    //将所有点投影到前三个点构成的平面上,并且将重合的点剔除
    std::vector<FVec3 > loop3D;
    for (size_t i = 0; i < loop.size(); ++i)
    {
        FVec3 tPrjPt = plane.project(loop[i]);
        if (loop3D.empty())
        {
            loop3D.push_back(tPrjPt);
        }
        else
        {
            bool bAdd = true;
            for(const auto& tPt: loop3D)
            {
                double dis = FVec3::DistanceSq(tPt, tPrjPt);
                if (dis <= NumLimits<float>::Epsilon)
                {
                    bAdd = false;
                    break;
                }
            }
            if (bAdd)
            {
                loop3D.push_back(tPrjPt);
            }
        }
    }
    // 将所有点旋转至保证与XOY平面平行
    FMat3 tMat = FMat3::FromQuat(FQuat::FromVectors(planeNor, FVec3(0.0f, 0.0f, 1.0f)));
    // 将点计算成二维点的列表
    std::vector<FVec2 > loop2D(loop3D.size());
    for (size_t i = 0; i < loop3D.size(); ++i)
    {
        FVec3 tPt = tMat * loop3D[i];
        loop2D[i] = tPt.xy();
    }
    //使用2D顶点数据实例化2D多边形类
    FPolygon2 polygon(std::move(loop2D));
    //判断旋转面是否是简单多边形
    /*if (!polygon.isSample())
        return ret;*/
    
    //使用耳切法对多边形进行三角细分,获取顶点索引
    std::vector<size_t> shapeIndices = polygon.triangulate();

    uint loopPointCount     = static_cast<uint>(loop3D.size());
    std::vector<uint>  tSideIndices;
    std::vector<FVec3> tPositions;

    // 计算侧面的顶点要根据分段数目，计算每个分段的顶点
    double radius = 0;
    // 取环上最大旋转半径
    for (size_t i = 0; i < loop3D.size(); i++)
    {
         double tRadius = FVec3::Distance(center, loop3D[i]);
         if (radius < tRadius)
             radius = tRadius;
    }
    if (radius == double(0))
        return ret;

    // 计算底面
    {
        //边线索引
        if (angle != 360.0f)
        {
            for (uint i = 1; i < loopPointCount; ++i)
            {
                tSideIndices.push_back(i - 1);
                tSideIndices.push_back(i);
            }
            //最后一个点跟第一个点构成的线段
            tSideIndices.push_back(loopPointCount - 1);
            tSideIndices.push_back(uint(0));
        }
        //顶点位置
        for (size_t i = 0; i < loopPointCount; ++i)
        {
            tPositions.push_back(loop3D[i]);
        }
    }
    // 计算侧面
    // 获取分段数
    uint segments = lodSelection.getSegment(angle, (float)radius);
    if (segments <= 0)
        return ret;
    // 计算出旋转面旋转一分段后的顶点位置
    //位置偏移矩阵
    WD::FMat4 offMatT       =       WD::FMat4::MakeTranslation(center);
    //位置偏移矩阵的逆
    WD::FMat4 offMatTInv    =       WD::FMat4::MakeTranslation(-center);

    for (size_t n = 1; n <= segments; ++n)
    {
        float tmpAngle = angle * float(n) / float(segments);
        // 每个分段的顶点
        std::vector<FVec3> tSegmentLoopVec;
        for (size_t index = 0; index < loopPointCount; index++)
        {
            const auto& rectVert = loop3D[index];
            //旋转矩阵
            WD::FMat4 offMatR       =       WD::FMat4::MakeRotation(tmpAngle, axis);
            //偏移结果
            WD::FMat4 rOffMat       =       offMatT * offMatR * offMatTInv;
            WD::FVec3 outerVert     =       rOffMat * rectVert;
            // 保存顶点
            tSegmentLoopVec.push_back(outerVert);
        }
        // 当前顶点个数
        uint pointCount = static_cast<uint>(tPositions.size());
        // 遍历旋转后的顶点，计算每个侧面
        for (uint index = 0; index < loopPointCount; index++)
        {
            // 顶点位置
            tPositions.push_back(tSegmentLoopVec[index]);

            //边线索引
            tSideIndices.push_back(pointCount + index);
            tSideIndices.push_back(pointCount - loopPointCount + index);
        }
        // 计算顶面
        // 优化，旋转360度生成环状体不需要绘制顶面
        if (angle != 360.0f && n == segments)
        {
            //边线索引
            {
                pointCount = static_cast<uint>(tPositions.size());
                for (uint i = 1; i < loopPointCount; ++i)
                {
                    tSideIndices.push_back(pointCount + i - loopPointCount - 1);
                    tSideIndices.push_back(pointCount + i - loopPointCount);
                }
                //最后一个点跟第一个点构成的线段
                tSideIndices.push_back(pointCount - 1);
                tSideIndices.push_back(pointCount - loopPointCount);
            }
        }
    }

    ret.positions = std::move(tPositions);
    //边线索引
    ret.wireFrames.setDrawElementUIntData(std::move(tSideIndices));
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);
    return ret;
}

MeshStruct RevolutionBuilder::SimpleMesh(const FVec3& center, const FVec3& axis, float angle, const FVec3Vector& loop, const MeshLODSelection& lodSelection)
{
    WDUnused(axis);
    WDUnused(center);
    MeshStruct ret;
    // 角度不能等于0
    if (Abs(angle) <= NumLimits<float>::Epsilon)
        return ret;

    // 构建顶点
    FVec2Vector realLoop;
    if(!BuildLoop(loop, realLoop, lodSelection))
        return ret;

    // 判断是否需要生成边线
    std::vector < std::pair<FVec2, bool>> data;
    data.reserve(realLoop.size());
    size_t tSideLineCnt = 0;
    for (size_t i = 0; i < realLoop.size(); ++i)
    {
        const auto& v = realLoop[i];
        const auto& vPrev = realLoop[i == 0 ? realLoop.size() - 1 : i - 1];
        const auto& vNext = realLoop[i == realLoop.size() - 1 ? 0 : i + 1];

        const auto vecPrev = vPrev - v;
        const auto vecNext = vNext - v;
        float tmpAngle = FVec2::Angle(vecPrev, vecNext);

        // 夹角较大则不需要生成边线
        bool bSideLine = tmpAngle < 160.0f;
        data.push_back({ v, bSideLine });
        if (bSideLine)
            tSideLineCnt++;
    }

    // 如果没有生成边线，默认计算四条
    if (tSideLineCnt == 0)
    {
        int span = int(data.size()) / 4;
        data[span * 0].second = true;
        data[span * 1].second = true;
        data[span * 2].second = true;
        data[span * 3].second = true;
    }

    // 对多边形进行三角细分,获取顶点索引
    std::vector<int> shapeIndices;
    shapeIndices = TEarcut<FVec2, int>::Exec(realLoop);

    float max = 0;
    for (const auto& p : realLoop)
    {
        if (Abs(p.y) > max)// 实际上不需要加绝对值，y都大于0
            max = Abs(p.y);
    }
    // 根据最远的点设置分段数
    auto segsArc = lodSelection.getSegment(angle, max);

    ret.positions.reserve((segsArc + 1) * realLoop.size());
    for (const auto& p : realLoop)
    {
        for (const auto& v : MakeArc(Abs(p.y), segsArc, p.x, angle))
        {
            ret.positions.push_back({ v.z, v.x, v.y });
        }
    }
    if (shapeIndices.size() % 3 != 0)
    {
        assert(false);
        return ret;
    }
    // 三角面索引
    auto sz = int(segsArc + 1);// 每条弧的顶点数
    auto numArcs = ret.positions.size() / sz;// 弧的条数
    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> tris;
        tris.reserve(shapeIndices.size() * 2 * 3 + numArcs * (sz - 1) * 6);
        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<byte>(shapeIndices[i + 2] * sz));
            tris.push_back(static_cast<byte>(shapeIndices[i + 1] * sz));
            tris.push_back(static_cast<byte>(shapeIndices[i + 0] * sz));
        }

        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<byte>(shapeIndices[i + 0] * sz + sz - 1));
            tris.push_back(static_cast<byte>(shapeIndices[i + 1] * sz + sz - 1));
            tris.push_back(static_cast<byte>(shapeIndices[i + 2] * sz + sz - 1));
        }

        for (auto i = 0; i < numArcs; i++)
        {
            auto arcStart = i * sz;
            auto nextStart = (arcStart + sz) % ret.positions.size();
            for (auto j = 0; j < (sz - 1); j++)
            {
                auto p1 = arcStart + j;
                auto p2 = nextStart + j;
                //p1+1  p2+1   
                //p1    p2   
                tris.push_back(static_cast<byte>(p1 + 1));
                tris.push_back(static_cast<byte>(p2));
                tris.push_back(static_cast<byte>(p2 + 1));

                tris.push_back(static_cast<byte>(p1 + 1));
                tris.push_back(static_cast<byte>(p1));
                tris.push_back(static_cast<byte>(p2));
            }
        }
        ret.triangles.setDrawElementByteData(std::move(tris));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max)
    {
        std::vector<ushort> tris;
        tris.reserve(shapeIndices.size() * 2 * 3 + numArcs * (sz - 1) * 6);
        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<ushort>(shapeIndices[i + 2] * sz));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 1] * sz));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 0] * sz));
        }

        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<ushort>(shapeIndices[i + 0] * sz + sz - 1));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 1] * sz + sz - 1));
            tris.push_back(static_cast<ushort>(shapeIndices[i + 2] * sz + sz - 1));
        }

        for (auto i = 0; i < numArcs; i++)
        {
            auto arcStart = i * sz;
            auto nextStart = (arcStart + sz) % ret.positions.size();
            for (auto j = 0; j < sz - 1; j++)
            {
                auto p1 = arcStart + j;
                auto p2 = nextStart + j;
                //p1+1  p2+1   
                //p1    p2   
                tris.push_back(static_cast<ushort>(p1 + 1));
                tris.push_back(static_cast<ushort>(p2));
                tris.push_back(static_cast<ushort>(p2 + 1));

                tris.push_back(static_cast<ushort>(p1 + 1));
                tris.push_back(static_cast<ushort>(p1));
                tris.push_back(static_cast<ushort>(p2));
            }
        }
        ret.triangles.setDrawElementUShortData(std::move(tris));
    }
    else
    {
        std::vector<uint> tris;
        tris.reserve(shapeIndices.size() * 2 * 3 + numArcs * (sz - 1) * 6);
        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<uint>(shapeIndices[i + 2] * sz));
            tris.push_back(static_cast<uint>(shapeIndices[i + 1] * sz));
            tris.push_back(static_cast<uint>(shapeIndices[i + 0] * sz));
        }

        for (int i = 0; i < shapeIndices.size(); i += 3)
        {
            tris.push_back(static_cast<uint>(shapeIndices[i + 0] * sz + sz - 1));
            tris.push_back(static_cast<uint>(shapeIndices[i + 1] * sz + sz - 1));
            tris.push_back(static_cast<uint>(shapeIndices[i + 2] * sz + sz - 1));
        }

        for (auto i = 0; i < numArcs; i++)
        {
            auto arcStart = i * sz;
            auto nextStart = (arcStart + sz) % ret.positions.size();
            for (auto j = 0; j < sz - 1; j++)
            {
                auto p1 = arcStart + j;
                auto p2 = nextStart + j;
                //p1+1  p2+1   
                //p1    p2   
                tris.push_back(static_cast<uint>(p1 + 1));
                tris.push_back(static_cast<uint>(p2));
                tris.push_back(static_cast<uint>(p2 + 1));

                tris.push_back(static_cast<uint>(p1 + 1));
                tris.push_back(static_cast<uint>(p1));
                tris.push_back(static_cast<uint>(p2));
            }
        }
        ret.triangles.setDrawElementUIntData(std::move(tris));
    }
    ret.triangles.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Triangles);

    int count = 0;
    for (const auto& d : data)
    {
        if (d.second)
            count++;
    }
    if (ret.positions.size() < WD::NumLimits<byte>::Max)
    {
        std::vector<byte> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (int i = 0; i < numArcs; i++)
        {
            // 如果角度大于360则边线中不带有两个截面的线
            if (abs(angle) < 360.0f)
            {
                wireframes.push_back(static_cast<byte>(sz * i));
                wireframes.push_back(static_cast<byte>(((i + 1) % numArcs) * sz));

                wireframes.push_back(static_cast<byte>(sz * i + sz - 1));
                wireframes.push_back(static_cast<byte>(((i + 1) % numArcs) * sz + sz - 1));
            }

            if (data[i].second)
            {
                // 生成侧边线
                for (auto j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<byte>(sz * i + j));
                    wireframes.push_back(static_cast<byte>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementByteData(std::move(wireframes));
    }
    else if (ret.positions.size() < WD::NumLimits<ushort>::Max)
    {
        std::vector<ushort> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (int i = 0; i < numArcs; i++)
        {
            // 如果角度大于360则边线中不带有两个截面的线
            if (abs(angle) < 360.0f)
            {
                wireframes.push_back(static_cast<ushort>(sz * i));
                wireframes.push_back(static_cast<ushort>(((i + 1) % numArcs) * sz));

                wireframes.push_back(static_cast<ushort>(sz * i + sz - 1));
                wireframes.push_back(static_cast<ushort>(((i + 1) % numArcs) * sz + sz - 1));
            }

            if (data[i].second)
            {
                // 生成侧边线
                for (auto j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<ushort>(sz * i + j));
                    wireframes.push_back(static_cast<ushort>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementUShortData(std::move(wireframes));
    }
    else
    {
        std::vector<uint> wireframes;
        wireframes.reserve(numArcs * 4 + count * (sz - 1) * 2);
        for (int i = 0; i < numArcs; i++)
        {
            // 如果角度大于360则边线中不带有两个截面的线
            if (abs(angle) < 360.0f)
            {
                wireframes.push_back(static_cast<uint>(sz * i));
                wireframes.push_back(static_cast<uint>(((i + 1) % numArcs) * sz));

                wireframes.push_back(static_cast<uint>(sz * i + sz - 1));
                wireframes.push_back(static_cast<uint>(((i + 1) % numArcs) * sz + sz - 1));
            }

            if (data[i].second)
            {
                // 生成侧边线
                for (auto j = 0; j < sz - 1; j++)
                {
                    wireframes.push_back(static_cast<uint>(sz * i + j));
                    wireframes.push_back(static_cast<uint>(sz * i + j + 1));
                }
            }
        }
        ret.wireFrames.setDrawElementUIntData(std::move(wireframes));
    }
    ret.wireFrames.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);
    return ret;
}

WD_NAMESPACE_END