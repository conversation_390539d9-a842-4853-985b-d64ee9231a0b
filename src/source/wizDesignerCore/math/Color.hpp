#pragma once

#include "TVec4.hpp"

WD_NAMESPACE_BEGIN

/**
 * @brief rgba颜色
*/
class Color
{
public:
    /**
     * @brief 颜色Key值
    */
    enum Key
    {
        aliceBlue           = 0xF0F8FF,     // 爱丽丝蓝
        antiqueWhite        = 0xFAEBD7,     // 古董白
        aqua                = 0x00FFFF,     // 水
        aquamarine          = 0x7FFFD4,     // 蓝晶
        azure               = 0xF0FFFF,     // 蔚蓝
        beige               = 0xF5F5DC,     // 米
        bisque              = 0xFFE4C4,     // 浓汤
        black               = 0x000000,     // 黑
        blanchedAlmond      = 0xFFEBCD,     // 杏仁白
        blue                = 0x0000FF,     // 蓝
        blueViolet          = 0x8A2BE2,     // 紫罗兰
        brown               = 0xA52A2A,     // 棕
        burlywood           = 0xDEB887,     // 硬木
        cadetBlue           = 0x5F9EA0,     // 少年蓝
        chartreuse          = 0x7FFF00,     // 浅黄绿
        chocolate           = 0xD2691E,     // 巧克力
        coral               = 0xFF7F50,     // 珊瑚
        cornflowerBlue      = 0x6495ED,     // 矢车菊蓝
        cornsilk            = 0xFFF8DC,     // 玉米丝
        crimson             = 0xDC143C,     // 赤红
        cyan                = 0x00FFFF,     // 青
        darkBlue            = 0x00008B,     // 深蓝
        darkCyan            = 0x008B8B,     // 深青
        darkGoldenrod       = 0xB8860B,     // 深金毛
        darkGray            = 0xA9A9A9,     // 深灰
        darkGreen           = 0x006400,     // 深绿
        darkKhaki           = 0xBDB76B,     // 深卡其
        darkMagenta         = 0x8B008B,     // 深洋红
        darkOliveGreen      = 0x556B2F,     // 深橄榄绿
        darkOrange          = 0xFF8C00,     // 深橙
        darkOrchid          = 0x9932CC,     // 深兰花
        darkRed             = 0x8B0000,     // 深红
        darkSalmon          = 0xE9967A,     // 深鲑鱼
        darkSeaGreen        = 0x8FBC8F,     // 深海绿
        darkSlateBlue       = 0x483D8B,     // 深板岩蓝
        darkSlateGray       = 0x2F4F4F,     // 深板岩灰
        darkTurquoise       = 0x00CED1,     // 深绿松石
        darkViolet          = 0x9400D3,     // 深紫罗兰
        deepPink            = 0xFF1493,     // 深粉
        deepSkyBlue         = 0x00BFFF,     // 深天蓝
        dimGray             = 0x696969,     // 暗灰
        dodgerBlue          = 0x1E90FF,     // 道奇蓝
        fireBrick           = 0xB22222,     // 耐火砖
        floralWhite         = 0xFFFAF0,     // 花白
        forestGreen         = 0x228B22,     // 森林绿
        fuchsia             = 0xFF00FF,     // 紫红
        gainsboro           = 0xDCDCDC,     // 甘斯伯勒
        ghostWhite          = 0xF8F8FF,     // 幽灵白
        gold                = 0xFFD700,     // 金
        goldenrod           = 0xDAA520,     // 金毛
        gray                = 0x808080,     // 灰
        green               = 0x008000,     // 绿
        greenYellow         = 0xADFF2F,     // 绿黄 （黄绿）
        honeydew            = 0xF0FFF0,     // 甘露
        hotPink             = 0xFF69B4,     // 亮粉
        indianRed           = 0xCD5C5C,     // 印度红
        indigo              = 0x4B0082,     // 靛青
        ivory               = 0xFFFFF0,     // 象牙
        khaki               = 0xF0E68C,     // 黄褐
        lavender            = 0xE6E6FA,     // 薰衣草
        lavenderBlush       = 0xFFF0F5,     // 薰衣草腮红
        lawnGreen           = 0x7CFC00,     // 草坪绿
        lemonChiffon        = 0xFFFACD,     // 柠檬雪纺
        lightBlue           = 0xADD8E6,     // 浅蓝
        lightCoral          = 0xF08080,     // 亮珊瑚
        lightCyan           = 0xE0FFFF,     // 浅青
        lightGoldenrodYellow= 0xFAFAD2,     // 浅金黄
        lightGray           = 0xD3D3D3,     // 浅灰
        lightGreen          = 0x90EE90,     // 浅绿
        lightPink           = 0xFFB6C1,     // 浅粉红
        lightSalmon         = 0xFFA07A,     // 鲑鱼
        lightSeaGreen       = 0x20B2AA,     // 浅海绿
        lightSkyBlue        = 0x87CEFA,     // 亮天蓝
        lightSlateGray      = 0x778899,     // 浅石灰 （浅灰）
        lightSteelBlue      = 0xB0C4DE,     // 浅钢蓝
        lightYellow         = 0xFFFFE0,     // 浅黄
        lime                = 0x00FF00,     // 酸橙
        limeGreen           = 0x32CD32,     // 柠檬绿
        linen               = 0xFAF0E6,     // 麻布
        magenta             = 0xFF00FF,     // 品红
        maroon              = 0x800000,     // 粟
        mediumAquamarine    = 0x66CDAA,     // 中度海蓝宝石
        mediumBlue          = 0x0000CD,     // 中度蓝
        mediumOrchid        = 0xBA55D3,     // 中度兰花
        mediumPurple        = 0x9370DB,     // 中度紫
        mediumSeaGreen      = 0x3CB371,     // 中度海绿
        mediumSlateBlue     = 0x7B68EE,     // 中度板岩蓝
        mediumSpringGreen   = 0x00FA9A,     // 中度春绿
        mediumTurquoise     = 0x48D1CC,     // 中度绿松石
        mediumVioletRed     = 0xC71585,     // 中度紫红
        midnightBlue        = 0x191970,     // 午夜蓝
        mintCream           = 0xF5FFFA,     // 薄荷奶油
        mistyRose           = 0xFFE4E1,     // 迷雾玫瑰
        moccasin            = 0xFFE4B5,     // 莫卡辛
        navajoWhite         = 0xFFDEAD,     // 纳瓦霍怀特
        navy                = 0x000080,     // 海军
        oldLace             = 0xFDF5E6,     // 旧蕾丝
        olive               = 0x808000,     // 橄榄
        olivedrab           = 0x6B8E23,     // 浅橄榄
        orange              = 0xFFA500,     // 橙子
        orangered           = 0xFF4500,     // 橙红
        orchid              = 0xDA70D6,     // 兰花
        paleGoldenrod       = 0xEEE8AA,     // 淡金毛
        paleGreen           = 0x98FB98,     // 灰绿 (浅绿)
        paleTurquoise       = 0xAFEEEE,     // 淡青绿
        paleVioletRed       = 0xDB7093,     // 浅紫红
        papayaWhip          = 0xFFEFD5,     // 木瓜鞭
        peachPuff           = 0xFFDAB9,     // 桃粉扑
        peru                = 0xCD853F,     // 秘鲁
        pink                = 0xFFC0CB,     // 粉
        plum                = 0xDDA0DD,     // 李子
        powderBlue          = 0xB0E0E6,     // 粉蓝
        purple              = 0x800080,     // 紫
        rebeccapurple       = 0x663399,     // 瑞贝卡紫
        red                 = 0xFF0000,     // 红
        rosyBrown           = 0xBC8F8F,     // 玫瑰红
        royalBlue           = 0x4169E1,     // 宝蓝
        saddleBrown         = 0x8B4513,     // 马鞍棕
        salmon              = 0xFA8072,     // 三文鱼
        sandyBrown          = 0xF4A460,     // 桑迪·布朗
        seaGreen            = 0x2E8B57,     // 海绿
        seashell            = 0xFFF5EE,     // 贝壳
        sienna              = 0xA0522D,     // 黄土
        silver              = 0xC0C0C0,     // 银
        skyblue             = 0x87CEEB,     // 天蓝
        slateBlue           = 0x6A5ACD,     // 板岩蓝
        slateGray           = 0x708090,     // 板岩灰
        snow                = 0xFFFAFA,     // 雪
        springGreen         = 0x00FF7F,     // 春绿
        steelBlue           = 0x4682B4,     // 钢铁蓝
        tan                 = 0xD2B48C,     // 棕黄褐 (黄褐)
        teal                = 0x008080,     // 蓝绿
        thistle             = 0xD8BFD8,     // 蓟
        tomato              = 0xFF6347,     // 番茄
        turquoise           = 0x40E0D0,     // 绿松石
        violet              = 0xEE82EE,     // 蓝紫
        wheat               = 0xF5DEB3,     // 小麦
        white               = 0xFFFFFF,     // 白
        whiteSmoke          = 0xF5F5F5,     // 白烟
        yellow              = 0xFFFF00,     // 黄
        yellowGreen         = 0x9ACD32      // 黄绿
    };                                      
public:                                     
    using SizeType = size_t;                
public:
    /**
    * @brief 数据个数
    */
    static constexpr size_t Size = 4;
public:
    // red 分量
    byte r;
    // green 分量
    byte g;
    // blue 分量
    byte b;
    // alpha 分量
    byte a;
public:
    /**
     * @brief 构造
    */
    inline Color() :
        r(0),
        g(0),
        b(0),
        a(255)
    {
    }
    /**
     * @brief 构造
    */
    inline Color(const Color& right) :
        r(right.r),
        g(right.g),
        b(right.b),
        a(right.a)
    {
    }
    /**
     * @brief 构造
    */
    inline Color(byte r, byte g, byte b, byte a = byte(255)) :
        r(r),
        g(g),
        b(b),
        a(a)
    {
    }
    /**
    * @brief 通过Key值构造颜色
    */
    inline Color(Color::Key key, byte a = byte(255))
    {
        this->r = byte(key >> 16 & 255);
        this->g = byte(key >> 8 & 255);
        this->b = byte(key & 255);
        this->a = a;
    }
    /**
    * @brief 通过十六进制的值构造颜色,需要指定透明度
    *   例如 0xFFFFFF表示为白色
    * @param hex 依次为 r,g,b
    */
    inline Color(int rgbHex, byte a)
    {
        this->r = byte(rgbHex >> 16 & 255);
        this->g = byte(rgbHex >> 8 & 255);
        this->b = byte(rgbHex & 255);
        this->a = a;
    }
    /**
    * @brief 通过十六进制的值构造颜色
    *   例如 0xFFFFFFFF表示为白色且不透明
    * @param hex 依次为 r,g,b,a
    */
    inline Color(int rgbaHex)
    {
        this->r = byte(rgbaHex >> 24 & 255);
        this->g = byte(rgbaHex >> 16 & 255);
        this->b = byte(rgbaHex >> 8 & 255);
        this->a = byte(rgbaHex & 255);
    }
    /**
    * @brief 使用TVec3<byte>构造
    */
    inline Color(const TVec3<byte>& rgb, byte a = byte(255)) :
        r(rgb.x),
        g(rgb.y),
        b(rgb.z),
        a(a)
    {

    }
    /**
    * @brief 使用TVec4<byte>构造
    */
    inline Color(const TVec4<byte>& rgba) :
        r(rgba.x),
        g(rgba.y),
        b(rgba.z),
        a(rgba.w)
    {

    }
    /**
    * @brief 使用 TVec3<float>构造,其中每个分量的值应该在[0,1]范围内
    */
    inline Color(const TVec3<float>& rgb, float a = 1.0f) :
        r(Color::FToB(rgb.x)),
        g(Color::FToB(rgb.y)),
        b(Color::FToB(rgb.z)),
        a(Color::FToB(a))
    {

    }
    /**
    * @brief 使用 TVec4<float>构造,其中每个分量的值应该在[0,1]范围内
    */
    inline Color(const TVec4<float>& rgba) :
        r(Color::FToB(rgba.x)),
        g(Color::FToB(rgba.y)),
        b(Color::FToB(rgba.z)),
        a(Color::FToB(rgba.w))
    {

    }
    /**
     * @brief 析构
    */
    inline ~Color()
    {

    }
public:
    /**
    * @brief 获取归一化的r, 取值范围[0.0f, 1.0f]
    */
    inline float rF() const
    {
        return Color::BToF(this->r);
    }
    /**
    * @brief 设置归一化的r, 取值范围[0.0f, 1.0f]
    */
    inline void setRF(float rf)
    {
        this->r = Color::FToB(rf);
    }
    /**
    * @brief 获取归一化的g, 取值范围[0.0f, 1.0f]
    */
    inline float gF() const
    {
        return Color::BToF(this->g);
    }
    /**
    * @brief 设置归一化的g, 取值范围[0.0f, 1.0f]
    */
    inline void setGF(float gf)
    {
        this->g = Color::FToB(gf);
    }
    /**
    * @brief 获取归一化的b, 取值范围[0.0f, 1.0f]
    */
    inline float bF() const
    {
        return Color::BToF(this->b);
    }
    /**
    * @brief 设置归一化的b, 取值范围[0.0f, 1.0f]
    */
    inline void setBF(float bf)
    {
        this->b = Color::FToB(bf);
    }
    /**
    * @brief 获取归一化的a, 取值范围[0.0f, 1.0f]
    */
    inline float aF() const
    {
        return Color::BToF(this->a);
    }
    /**
    * @brief 设置归一化的a, 取值范围[0.0f, 1.0f]
    */
    inline void setAF(float af)
    {
        this->a = Color::FToB(af);
    }
    /**
    * @brief 获取rgb
    */
    inline TVec3<byte> rgb() const
    {
        return TVec3<byte>(this->r, this->g, this->b);
    }
    /**
    * @brief 获取rgba
    */
    inline TVec4<byte> rgba() const
    {
        return TVec4<byte>(this->r, this->g, this->b, this->a);
    }
    /**
    * @brief 获取16进制表示的颜色
    */
    inline int rgbHex() const
    {
        return (this->r) << 16
            ^ (this->g) << 8
            ^ (this->b) << 0;
    }
    /**
    * @brief 获取16进制表示的颜色,不包含透明度
    */
    inline int rgbaHex() const
    {
        return (this->r) << 24
            ^ (this->g) << 16
            ^ (this->b) << 8
            ^ (this->a) << 0;
    }
    /**
    * @brief 将 [0,255] 范围的颜色 转换为 [0.0f,1.0f] 范围的颜色,不包含透明度
    */
    inline TVec3<float> rgbF() const
    {
        return TVec3<float>(
            Color::BToF(this->r),
            Color::BToF(this->g),
            Color::BToF(this->b)
            );
    }
    /**
    * @brief 将 [0,255] 范围的颜色 转换为 [0.0f,1.0f] 范围的颜色
    */
    inline TVec4<float> rgbaF() const
    {
        return TVec4<float>(
            Color::BToF(this->r),
            Color::BToF(this->g),
            Color::BToF(this->b),
            Color::BToF(this->a)
            );
    }
public:
    /**
    * @brief 下标访问
    */
    inline byte& operator[](size_t i)
    {
        byte* d = (byte*)(&this->r);
        return d[i];
    }
    /**
    * @brief 下标访问
    */
    inline const byte& operator[](size_t i) const
    {
        const byte* d = (const byte*)(&this->r);
        return d[i];
    }
    /**
    * @brief 赋值运算
    */
    inline Color& operator=(const Color & right)
    {
        this->r = right.r;
        this->g = right.g;
        this->b = right.b;
        this->a = right.a;
        return *this;
    }
    /**
    * @brief +=
    */
    inline Color & operator+=(byte s)
    {
        this->r += s;
        this->g += s;
        this->b += s;
        this->a += s;
        return *this;
    }
    /**
    * @brief +=
    */
    inline Color & operator+=(const Color& v)
    {
        this->r += v.r;
        this->g += v.g;
        this->b += v.b;
        this->a += v.a;
        return *this;
    }
    /**
    * @brief -=
    */
    inline Color& operator-=(byte s)
    {
        this->r -= s;
        this->g -= s;
        this->b -= s;
        this->a -= s;
        return *this;
    }
    /**
    * @brief -= 向量
    */
    inline Color& operator-=(const Color& v)
    {
        this->r -= v.r;
        this->g -= v.g;
        this->b -= v.b;
        this->a -= v.a;
        return *this;
    }
    /**
    * @brief *= 标量
    */
    template<class U>
    inline Color& operator*=(U s)
    {
        this->r = byte(this->r * s);
        this->g = byte(this->g * s);
        this->b = byte(this->b * s);
        this->a = byte(this->a * s);
        return *this;
    }
    /**
    * @brief *= 向量
    */
    inline Color& operator*=(const Color& v)
    {
        this->r *= v.r;
        this->g *= v.g;
        this->b *= v.b;
        this->a *= v.a;
        return *this;
    }
    /**
    * @brief /= 标量
    */
    template<class U>
    inline Color& operator/=(U s)
    {
        this->r = byte(this->r / s);
        this->g = byte(this->g / s);
        this->b = byte(this->b / s);
        this->a = byte(this->a / s);
        return *this;
    }
    /**
    * @brief /= 向量
    */
    inline Color& operator/=(const Color& v)
    {
        this->r /= v.r;
        this->g /= v.g;
        this->b /= v.b;
        this->a /= v.a;
        return *this;
    }
    /**
     * @brief ==
    */
    friend inline bool operator==(const Color& left, const Color& right)
    {
        return (left.r == right.r)
            && (left.g == right.g)
            && (left.b == right.b)
            && (left.a == right.a);
    }
    /**
     * @brief !=
    */
    friend inline bool operator!=(const Color& left, const Color& right)
    {
        return (left.r != right.r)
            || (left.g != right.g)
            || (left.b != right.b)
            || (left.a != right.a);
    }
    /**
     * @brief 加法运算
    */
    friend inline Color operator+ (const Color& v, byte s)
    {
        return Color(
            v.r + s,
            v.g + s,
            v.b + s,
            v.a + s);
    }
    /**
     * @brief 加法运算
    */
    friend inline Color operator+ (byte s, const Color& v)
    {
        return Color(
            v.r + s,
            v.g + s,
            v.b + s,
            v.a + s);
    }
    /**
     * @brief 加法运算
    */
    friend inline Color operator+ (const Color& v1, const Color& v2)
    {
        return Color(
            v1.r + v2.r,
            v1.g + v2.g,
            v1.b + v2.b,
            v1.a + v2.a);
    }
    /**
     * @brief 减法运算
    */
    friend inline Color operator- (const Color& v, byte s)
    {
        return Color(
            v.r - s,
            v.g - s,
            v.b - s,
            v.a - s);
    }
    /**
     * @brief 减法运算
    */
    friend inline Color operator- (byte s, const Color& v)
    {
        return Color(
            s - v.r,
            s - v.g,
            s - v.b,
            s - v.a);
    }
    /**
     * @brief 减法运算
    */
    friend inline Color operator- (const Color& v1, const Color& v2)
    {
        return Color(
            v1.r - v2.r,
            v1.g - v2.g,
            v1.b - v2.b,
            v1.a - v2.a);
    }
    /**
     * @brief 乘法运算
    */
    template<class U>
    friend inline Color operator* (const Color& v, U s)
    {
        return Color(
            byte(v.r * s),
            byte(v.g * s),
            byte(v.b * s),
            byte(v.a * s));
    }
    /**
     * @brief 乘法运算
    */
    template<class U>
    friend inline Color operator* (U s, const Color& v)
    {
        return Color(
            byte(s * v.r),
            byte(s * v.g),
            byte(s * v.b),
            byte(s * v.a));
    }
    /**
     * @brief 乘法运算
    */
    friend inline Color operator* (const Color& v1, const Color& v2)
    {
        return Color(
            v1.r * v2.r,
            v1.g * v2.g,
            v1.b * v2.b,
            v1.a * v2.a);
    }
    /**
     * @brief 除法运算
    */
    template<class U>
    friend inline Color operator/ (const Color& v, U s)
    {
        return Color(
            byte(v.r / s),
            byte(v.g / s),
            byte(v.b / s),
            byte(v.a / s));
    }
    /**
     * @brief 除法运算
    */
    template<class U>
    friend inline Color operator/ (U s, const Color& v)
    {
        return Color(
            byte(s / v.r),
            byte(s / v.g),
            byte(s / v.b),
            byte(s / v.a));
    }
    /**
     * @brief 除法运算
    */
    friend inline Color operator/ (const Color& v1, const Color& v2)
    {
        return Color(
            v1.r / v2.r,
            v1.g / v2.g,
            v1.b / v2.b,
            v1.a / v2.a
        );
    }
    /**
     * @brief >
    */
    bool operator>(const Color& right) const
    {
        const Color& left = (*this);
        for (size_t i = 0; i < Color::Size; ++i)
        {
            if (left[i] > right[i])
                return true;
            else if (left[i] < right[i])
                return false;
            else
                continue;
        }
        return false;
    }
    /**
     * @brief <
    */
    bool operator<(const Color& right) const
    {
        const Color& left = (*this);
        for (size_t i = 0; i < Color::Size; ++i)
        {
            if (left[i] < right[i])
                return true;
            else if (left[i] > right[i])
                return false;
            else
                continue;
        }
        return false;
    }
    /**
     * @brief >=
    */
    friend bool operator>= (const Color& left, const Color& right)
    {
        return !(left < right);
    }
    /**
     * @brief <=
    */
    friend bool operator<= (const Color& left, const Color& right)
    {
        return !(left > right);
    }
public:
    /**
    * @brief 线性插值
    */
    template <typename T>
    static inline Color Lerp(const Color& v0, const Color& v1, T t)
    {
        return WD::Lerp<Color, T>(v0, v1, t);
    }
public:
    /**
    * @brief 转换到字符串
    */
    inline char* toString(char* buf) const
    {
        PrintArray1D(buf, this->r, this->g, this->b, this->a);
        return buf;
    }
    /**
    * @brief 转换到字符串
    */
    inline std::string toString() const
    {
        char buf[256] = { 0 };
        return this->toString(buf);
    }
    /**
    * @brief 从字符串转换
    */
    inline Color& fromString(const char* str, bool* bOk = nullptr)
    {
        bool bRet = ScanArray1D(str, this->r, this->g, this->b, this->a);
        SetValueToBooleanPtr(bOk, bRet);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    inline Color& fromString(const std::string& str, bool* bOk = nullptr)
    {
        this->fromString(str.c_str(), bOk);
        return *this;
    }
    /**
    * @brief 从字符串转换
    */
    static inline Color FromString(const char* str, bool* bOk = nullptr)
    {
        Color r;
        r.fromString(str, bOk);
        return r;
    }
    /**
    * @brief 从字符串转换
    */
    static inline Color FromString(const std::string& str, bool* bOk = nullptr)
    {
        return Color::FromString(str.c_str(), bOk);
    }
private:
    static constexpr const float F255 = float(255);

    static inline float BToF(byte b)
    {
        return float(b) / F255;
    }

    static inline byte FToB(float f)
    {
        return byte(f * F255);
    }


};


/**
 * @brief 颜色数组
*/
using ColorVector = std::vector<Color>;
/**
 * @brief 颜色数组
 * @tparam Size 数组大小
*/
template <size_t Size>
using ColorArray = std::array<Color, Size>;


static  const WD::Color ColorTable[] =   
{
    WD::Color::aliceBlue           ,
    WD::Color::antiqueWhite        ,
    WD::Color::aqua                ,
    WD::Color::aquamarine          ,
    WD::Color::azure               ,
    WD::Color::beige               ,
    WD::Color::bisque              ,
    WD::Color::black               ,
    WD::Color::blanchedAlmond      ,
    WD::Color::blue                ,
    WD::Color::blueViolet          ,
    WD::Color::brown               ,
    WD::Color::burlywood           ,
    WD::Color::cadetBlue           ,
    WD::Color::chartreuse          ,
    WD::Color::chocolate           ,
    WD::Color::coral               ,
    WD::Color::cornflowerBlue      ,
    WD::Color::cornsilk            ,
    WD::Color::crimson             ,
    WD::Color::cyan                ,
    WD::Color::darkBlue            ,
    WD::Color::darkCyan            ,
    WD::Color::darkGoldenrod       ,
    WD::Color::darkGray            ,
    WD::Color::darkGreen           ,
    WD::Color::darkKhaki           ,
    WD::Color::darkMagenta         ,
    WD::Color::darkOliveGreen      ,
    WD::Color::darkOrange          ,
    WD::Color::darkOrchid          ,
    WD::Color::darkRed             ,
    WD::Color::darkSalmon          ,
    WD::Color::darkSeaGreen        ,
    WD::Color::darkSlateBlue       ,
    WD::Color::darkSlateGray       ,
    WD::Color::darkTurquoise       ,
    WD::Color::darkViolet          ,
    WD::Color::deepPink            ,
    WD::Color::deepSkyBlue         ,
    WD::Color::dimGray             ,
    WD::Color::dodgerBlue          ,
    WD::Color::fireBrick           ,
    WD::Color::floralWhite         ,
    WD::Color::forestGreen         ,
    WD::Color::fuchsia             ,
    WD::Color::gainsboro           ,
    WD::Color::ghostWhite          ,
    WD::Color::gold                ,
    WD::Color::goldenrod           ,
    WD::Color::gray                ,
    WD::Color::green               ,
    WD::Color::greenYellow         ,
    WD::Color::honeydew            ,
    WD::Color::hotPink             ,
    WD::Color::indianRed           ,
    WD::Color::indigo              ,
    WD::Color::ivory               ,
    WD::Color::khaki               ,
    WD::Color::lavender            ,
    WD::Color::lavenderBlush       ,
    WD::Color::lawnGreen           ,
    WD::Color::lemonChiffon        ,
    WD::Color::lightBlue           ,
    WD::Color::lightCoral          ,
    WD::Color::lightCyan           ,
    WD::Color::lightGoldenrodYellow,
    WD::Color::lightGray           ,
    WD::Color::lightGreen          ,
    WD::Color::lightPink           ,
    WD::Color::lightSalmon         ,
    WD::Color::lightSeaGreen       ,
    WD::Color::lightSkyBlue        ,
    WD::Color::lightSlateGray      ,
    WD::Color::lightSteelBlue      ,
    WD::Color::lightYellow         ,
    WD::Color::lime                ,
    WD::Color::limeGreen           ,
    WD::Color::linen               ,
    WD::Color::magenta             ,
    WD::Color::maroon              ,
    WD::Color::mediumAquamarine    ,
    WD::Color::mediumBlue          ,
    WD::Color::mediumOrchid        ,
    WD::Color::mediumPurple        ,
    WD::Color::mediumSeaGreen      ,
    WD::Color::mediumSlateBlue     ,
    WD::Color::mediumSpringGreen   ,
    WD::Color::mediumTurquoise     ,
    WD::Color::mediumVioletRed     ,
    WD::Color::midnightBlue        ,
    WD::Color::mintCream           ,
    WD::Color::mistyRose           ,
    WD::Color::moccasin            ,
    WD::Color::navajoWhite         ,
    WD::Color::navy                ,
    WD::Color::oldLace             ,
    WD::Color::olive               ,
    WD::Color::olivedrab           ,
    WD::Color::orange              ,
    WD::Color::orangered           ,
    WD::Color::orchid              ,
    WD::Color::paleGoldenrod       ,
    WD::Color::paleGreen           ,
    WD::Color::paleTurquoise       ,
    WD::Color::paleVioletRed       ,
    WD::Color::papayaWhip          ,
    WD::Color::peachPuff           ,
    WD::Color::peru                ,
    WD::Color::pink                ,
    WD::Color::plum                ,
    WD::Color::powderBlue          ,
    WD::Color::purple              ,
    WD::Color::rebeccapurple       ,
    WD::Color::red                 ,
    WD::Color::rosyBrown           ,
    WD::Color::royalBlue           ,
    WD::Color::saddleBrown         ,
    WD::Color::salmon              ,
    WD::Color::sandyBrown          ,
    WD::Color::seaGreen            ,
    WD::Color::seashell            ,
    WD::Color::sienna              ,
    WD::Color::silver              ,
    WD::Color::skyblue             ,
    WD::Color::slateBlue           ,
    WD::Color::slateGray           ,
    WD::Color::snow                ,
    WD::Color::springGreen         ,
    WD::Color::steelBlue           ,
    WD::Color::tan                 ,
    WD::Color::teal                ,
    WD::Color::thistle             ,
    WD::Color::tomato              ,
    WD::Color::turquoise           ,
    WD::Color::violet              ,
    WD::Color::wheat               ,
    WD::Color::white               ,
    WD::Color::whiteSmoke          ,
    WD::Color::yellow              ,
    WD::Color::yellowGreen         ,        
};

static  const char*     ColorName[] =   
{
    "aliceBlue"           ,
    "antiqueWhite"        ,
    "aqua"                ,
    "aquamarine"          ,
    "azure"               ,
    "beige"               ,
    "bisque"              ,
    "black"               ,
    "blanchedAlmond"      ,
    "blue"                ,
    "blueViolet"          ,
    "brown"               ,
    "burlywood"           ,
    "cadetBlue"           ,
    "chartreuse"          ,
    "chocolate"           ,
    "coral"               ,
    "cornflowerBlue"      ,
    "cornsilk"            ,
    "crimson"             ,
    "cyan"                ,
    "darkBlue"            ,
    "darkCyan"            ,
    "darkGoldenrod"       ,
    "darkGray"            ,
    "darkGreen"           ,
    "darkKhaki"           ,
    "darkMagenta"         ,
    "darkOliveGreen"      ,
    "darkOrange"          ,
    "darkOrchid"          ,
    "darkRed"             ,
    "darkSalmon"          ,
    "darkSeaGreen"        ,
    "darkSlateBlue"       ,
    "darkSlateGray"       ,
    "darkTurquoise"       ,
    "darkViolet"          ,
    "deepPink"            ,
    "deepSkyBlue"         ,
    "dimGray"             ,
    "dodgerBlue"          ,
    "fireBrick"           ,
    "floralWhite"         ,
    "forestGreen"         ,
    "fuchsia"             ,
    "gainsboro"           ,
    "ghostWhite"          ,
    "gold"                ,
    "goldenrod"           ,
    "gray"                ,
    "green"               ,
    "greenYellow"         ,
    "honeydew"            ,
    "hotPink"             ,
    "indianRed"           ,
    "indigo"              ,
    "ivory"               ,
    "khaki"               ,
    "lavender"            ,
    "lavenderBlush"       ,
    "lawnGreen"           ,
    "lemonChiffon"        ,
    "lightBlue"           ,
    "lightCoral"          ,
    "lightCyan"           ,
    "lightGoldenrodYellow",
    "lightGray"           ,
    "lightGreen"          ,
    "lightPink"           ,
    "lightSalmon"         ,
    "lightSeaGreen"       ,
    "lightSkyBlue"        ,
    "lightSlateGray"      ,
    "lightSteelBlue"      ,
    "lightYellow"         ,
    "lime"                ,
    "limeGreen"           ,
    "linen"               ,
    "magenta"             ,
    "maroon"              ,
    "mediumAquamarine"    ,
    "mediumBlue"          ,
    "mediumOrchid"        ,
    "mediumPurple"        ,
    "mediumSeaGreen"      ,
    "mediumSlateBlue"     ,
    "mediumSpringGreen"   ,
    "mediumTurquoise"     ,
    "mediumVioletRed"     ,
    "midnightBlue"        ,
    "mintCream"           ,
    "mistyRose"           ,
    "moccasin"            ,
    "navajoWhite"         ,
    "navy"                ,
    "oldLace"             ,
    "olive"               ,
    "olivedrab"           ,
    "orange"              ,
    "orangered"           ,
    "orchid"              ,
    "paleGoldenrod"       ,
    "paleGreen"           ,
    "paleTurquoise"       ,
    "paleVioletRed"       ,
    "papayaWhip"          ,
    "peachPuff"           ,
    "peru"                ,
    "pink"                ,
    "plum"                ,
    "powderBlue"          ,
    "purple"              ,
    "rebeccapurple"       ,
    "red"                 ,
    "rosyBrown"           ,
    "royalBlue"           ,
    "saddleBrown"         ,
    "salmon"              ,
    "sandyBrown"          ,
    "seaGreen"            ,
    "seashell"            ,
    "sienna"              ,
    "silver"              ,
    "skyblue"             ,
    "slateBlue"           ,
    "slateGray"           ,
    "snow"                ,
    "springGreen"         ,
    "steelBlue"           ,
    "tan"                 ,
    "teal"                ,
    "thistle"             ,
    "tomato"              ,
    "turquoise"           ,
    "violet"              ,
    "wheat"               ,
    "white"               ,
    "whiteSmoke"          ,
    "yellow"              ,
    "yellowGreen"         
};

static  std::pair<const char*,WD::Color>    NamedColorTable[]   =   
{
    {   "aliceBlue"           , Color::aliceBlue           },
    {   "antiqueWhite"        , Color::antiqueWhite        },
    {   "aqua"                , Color::aqua                },
    {   "aquamarine"          , Color::aquamarine          },
    {   "azure"               , Color::azure               },
    {   "beige"               , Color::beige               },
    {   "bisque"              , Color::bisque              },
    {   "black"               , Color::black               },
    {   "blanchedAlmond"      , Color::blanchedAlmond      },
    {   "blue"                , Color::blue                },
    {   "blueViolet"          , Color::blueViolet          },
    {   "brown"               , Color::brown               },
    {   "burlywood"           , Color::burlywood           },
    {   "cadetBlue"           , Color::cadetBlue           },
    {   "chartreuse"          , Color::chartreuse          },
    {   "chocolate"           , Color::chocolate           },
    {   "coral"               , Color::coral               },
    {   "cornflowerBlue"      , Color::cornflowerBlue      },
    {   "cornsilk"            , Color::cornsilk            },
    {   "crimson"             , Color::crimson             },
    {   "cyan"                , Color::cyan                },
    {   "darkBlue"            , Color::darkBlue            },
    {   "darkCyan"            , Color::darkCyan            },
    {   "darkGoldenrod"       , Color::darkGoldenrod       },
    {   "darkGray"            , Color::darkGray            },
    {   "darkGreen"           , Color::darkGreen           },
    {   "darkKhaki"           , Color::darkKhaki           },
    {   "darkMagenta"         , Color::darkMagenta         },
    {   "darkOliveGreen"      , Color::darkOliveGreen      },
    {   "darkOrange"          , Color::darkOrange          },
    {   "darkOrchid"          , Color::darkOrchid          },
    {   "darkRed"             , Color::darkRed             },
    {   "darkSalmon"          , Color::darkSalmon          },
    {   "darkSeaGreen"        , Color::darkSeaGreen        },
    {   "darkSlateBlue"       , Color::darkSlateBlue       },
    {   "darkSlateGray"       , Color::darkSlateGray       },
    {   "darkTurquoise"       , Color::darkTurquoise       },
    {   "darkViolet"          , Color::darkViolet          },
    {   "deepPink"            , Color::deepPink            },
    {   "deepSkyBlue"         , Color::deepSkyBlue         },
    {   "dimGray"             , Color::dimGray             },
    {   "dodgerBlue"          , Color::dodgerBlue          },
    {   "fireBrick"           , Color::fireBrick           },
    {   "floralWhite"         , Color::floralWhite         },
    {   "forestGreen"         , Color::forestGreen         },
    {   "fuchsia"             , Color::fuchsia             },
    {   "gainsboro"           , Color::gainsboro           },
    {   "ghostWhite"          , Color::ghostWhite          },
    {   "gold"                , Color::gold                },
    {   "goldenrod"           , Color::goldenrod           },
    {   "gray"                , Color::gray                },
    {   "green"               , Color::green               },
    {   "greenYellow"         , Color::greenYellow         },
    {   "honeydew"            , Color::honeydew            },
    {   "hotPink"             , Color::hotPink             },
    {   "indianRed"           , Color::indianRed           },
    {   "indigo"              , Color::indigo              },
    {   "ivory"               , Color::ivory               },
    {   "khaki"               , Color::khaki               },
    {   "lavender"            , Color::lavender            },
    {   "lavenderBlush"       , Color::lavenderBlush       },
    {   "lawnGreen"           , Color::lawnGreen           },
    {   "lemonChiffon"        , Color::lemonChiffon        },
    {   "lightBlue"           , Color::lightBlue           },
    {   "lightCoral"          , Color::lightCoral          },
    {   "lightCyan"           , Color::lightCyan           },
    {   "lightGoldenrodYellow", Color::lightGoldenrodYellow},
    {   "lightGray"           , Color::lightGray           },
    {   "lightGreen"          , Color::lightGreen          },
    {   "lightPink"           , Color::lightPink           },
    {   "lightSalmon"         , Color::lightSalmon         },
    {   "lightSeaGreen"       , Color::lightSeaGreen       },
    {   "lightSkyBlue"        , Color::lightSkyBlue        },
    {   "lightSlateGray"      , Color::lightSlateGray      },
    {   "lightSteelBlue"      , Color::lightSteelBlue      },
    {   "lightYellow"         , Color::lightYellow         },
    {   "lime"                , Color::lime                },
    {   "limeGreen"           , Color::limeGreen           },
    {   "linen"               , Color::linen               },
    {   "magenta"             , Color::magenta             },
    {   "maroon"              , Color::maroon              },
    {   "mediumAquamarine"    , Color::mediumAquamarine    },
    {   "mediumBlue"          , Color::mediumBlue          },
    {   "mediumOrchid"        , Color::mediumOrchid        },
    {   "mediumPurple"        , Color::mediumPurple        },
    {   "mediumSeaGreen"      , Color::mediumSeaGreen      },
    {   "mediumSlateBlue"     , Color::mediumSlateBlue     },
    {   "mediumSpringGreen"   , Color::mediumSpringGreen   },
    {   "mediumTurquoise"     , Color::mediumTurquoise     },
    {   "mediumVioletRed"     , Color::mediumVioletRed     },
    {   "midnightBlue"        , Color::midnightBlue        },
    {   "mintCream"           , Color::mintCream           },
    {   "mistyRose"           , Color::mistyRose           },
    {   "moccasin"            , Color::moccasin            },
    {   "navajoWhite"         , Color::navajoWhite         },
    {   "navy"                , Color::navy                },
    {   "oldLace"             , Color::oldLace             },
    {   "olive"               , Color::olive               },
    {   "olivedrab"           , Color::olivedrab           },
    {   "orange"              , Color::orange              },
    {   "orangered"           , Color::orangered           },
    {   "orchid"              , Color::orchid              },
    {   "paleGoldenrod"       , Color::paleGoldenrod       },
    {   "paleGreen"           , Color::paleGreen           },
    {   "paleTurquoise"       , Color::paleTurquoise       },
    {   "paleVioletRed"       , Color::paleVioletRed       },
    {   "papayaWhip"          , Color::papayaWhip          },
    {   "peachPuff"           , Color::peachPuff           },
    {   "peru"                , Color::peru                },
    {   "pink"                , Color::pink                },
    {   "plum"                , Color::plum                },
    {   "powderBlue"          , Color::powderBlue          },
    {   "purple"              , Color::purple              },
    {   "rebeccapurple"       , Color::rebeccapurple       },
    {   "red"                 , Color::red                 },
    {   "rosyBrown"           , Color::rosyBrown           },
    {   "royalBlue"           , Color::royalBlue           },
    {   "saddleBrown"         , Color::saddleBrown         },
    {   "salmon"              , Color::salmon              },
    {   "sandyBrown"          , Color::sandyBrown          },
    {   "seaGreen"            , Color::seaGreen            },
    {   "seashell"            , Color::seashell            },
    {   "sienna"              , Color::sienna              },
    {   "silver"              , Color::silver              },
    {   "skyblue"             , Color::skyblue             },
    {   "slateBlue"           , Color::slateBlue           },
    {   "slateGray"           , Color::slateGray           },
    {   "snow"                , Color::snow                },
    {   "springGreen"         , Color::springGreen         },
    {   "steelBlue"           , Color::steelBlue           },
    {   "tan"                 , Color::tan                 },
    {   "teal"                , Color::teal                },
    {   "thistle"             , Color::thistle             },
    {   "tomato"              , Color::tomato              },
    {   "turquoise"           , Color::turquoise           },
    {   "violet"              , Color::violet              },
    {   "wheat"               , Color::wheat               },
    {   "white"               , Color::white               },
    {   "whiteSmoke"          , Color::whiteSmoke          },
    {   "yellow"              , Color::yellow              },
    {   "yellowGreen"         , Color::yellowGreen         },
};

static  inline  std::optional<Color>   ColorFromName(const char* name)
{
    for (size_t i = 0; i < sizeof(NamedColorTable) / sizeof(NamedColorTable[0]); i++)
    {
        if (_stricmp(NamedColorTable[i].first,name) == 0)
        {
            return  NamedColorTable[i].second;
        }
    }
    return   std::nullopt;
}

WD_NAMESPACE_END
