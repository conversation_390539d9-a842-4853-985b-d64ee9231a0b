#include "WDEditSingleAxisMoveRotate.h"
#include "../viewer/primitiveRender/WDLineRender.h"
#include "../viewer/primitiveRender/WDStdPriRender.h"
#include "../viewer/primitiveRender/WDTextRender.h"
#include "../viewer/WDViewer.h"
#include "../math/DirectionParser.h"


WD_NAMESPACE_BEGIN;

//计算射线是否与某个四边形面有交点
extern bool GetRayRectaceInsPt(const DRay& ray, DVec3 rect[4], DVec3& retPt); 

class WDEditSingleAxisMoveRotatePrivate
{
    const static constexpr double distMax = 6.0;

public:

    using EAxis = WDEditSingleAxisMoveRotate::Axis;
    using EHandleType = WDEditSingleAxisMoveRotate::HandleType;
    using EAxisPart = WDEditSingleAxisMoveRotate::AxisPart;
    //
    WDEditSingleAxisMoveRotate& _d;
    friend class WDEditSingleAxisMoveRotate;

    // 移动轴的正轴部分
    std::array<DVec3, 2> _pAxes;
    // 移动轴的负轴部分
    std::array<DVec3, 2> _nAxes;

    // 旋转轴的弧线数据,有绕正轴旋转和绕负轴旋转两部分, 点的个数根据分段数而定
    std::array<DVec3Vector, 2> _rAxesArc;
    // 旋转轴的轴线数据
    std::array<DVec3, 2> _rAxes;
    //包围盒
    DAabb3 _aabb;

    // 线绘制对象
    WDLineRender _lineRender;
    // 坐标轴线常规样式
    WDLineRender::Param _normalLineParam;
    // 坐标轴线高亮样式
    WDLineRender::Param _hoverLineParam;
    // 箭头绘制对象
    WDStdPriRender _coneRender;
    // 文字绘制对象
    WDText3DRender _textRender;

    // 高亮的轴
    EAxis _hoveredAxis;
    // 选中的轴
    EAxis _selectedAxis;
    // 当前正在进行的编辑类型
    EHandleType _handleType;
    // 选中的弧线部分
    EAxisPart _axisPart;

    // 鼠标按钮按下
    bool _bMouseDown;
    // 开始时的鼠标位置
    IVec2 _startMousePos;
    // 当前鼠标的位置
    IVec2 _currentMousePos;

    // 记录鼠标按下时轴的世界坐标
    DMat4 _originTransform;
    // 保存当前位置的临时变量
    DVec3 _currentPos;
    // 鼠标当前位置所在轴的约束线
    std::optional<WDSingleAxisMRConstraint::ConstraintLine> _currentLine;
    // 鼠标当前位置所在轴的约束线
    std::optional<WDSingleAxisMRConstraint::ConstraintLine> _mouseConstLine;
    // 鼠标按下时起始位置所在轴的约束线
    std::optional<WDSingleAxisMRConstraint::ConstraintLine> _originLine;

    // 鼠标右键按下
    IVec2 _rDownPos;
    WDEditSingleAxisMoveRotate::Axis _rDownAxis = WDEditSingleAxisMoveRotate::Axis::A_Null;

    // 鼠标按下到当前的移动偏移
    DVec3 _mOffset;
    // 鼠标按下到当前的角度偏移
    double _rAngle;

    // 移动通知
    WDEditSingleAxisMoveRotate::MDelegate _delegate;
    // 右键菜单通知
    WDEditSingleAxisMoveRotate::MContextMenuDelegate _cxtMenuDelegate;
    // 鼠标单击通知
    WDEditSingleAxisMoveRotate::MClickedDelegate _clickedDelegate;
    // 鼠标双击通知
    WDEditSingleAxisMoveRotate::MDBClickedDelegate _dbClickedDelegate;

    // 移动步长，默认值:1
    real _stepLength = 1.0;
    // 每次旋转的角度步长
    real _stepAngle = 1.0;

    // 是否开启约束
    bool bConstraint = false;
    WDSingleAxisMRConstraint constraint;
    double nodeThick = 0.0;

    // 预览高亮的轴
    EAxis _previewHoveredAxis;
    // 预览正在进行的编辑类型
    EHandleType _previewHandleType;
    // 预览开始时的原始位置
    DVec3 _previewBasePosition;
    DVec3 _previewBaseZ;
public:
    WDEditSingleAxisMoveRotatePrivate(WDEditSingleAxisMoveRotate& d) :_d(d)
    {
        _hoveredAxis    = EAxis::A_Null;
        _selectedAxis   = EAxis::A_Null;
        _handleType     = EHandleType::HT_None;
        _axisPart       = EAxisPart::AP_None;
        _previewHoveredAxis = EAxis::A_Null;
        _previewHandleType = EHandleType::HT_None;

        _aabb = DAabb3::Null();

        _bMouseDown = false;
        _startMousePos = IVec2(0);
        _currentMousePos = IVec2(0);
        
        _normalLineParam.lineWidth  = 2.0f;
        _normalLineParam.color      = Color(220, 0, 220, 255);

        _hoverLineParam.lineWidth   = 2.0f;
        _hoverLineParam.color       = Color(240, 160, 240, 255);

        _mOffset = DVec3::Zero();
        _rAngle = 0.0;
    }
public:
    bool bConstraintEnabled()
    {
        return constraint.valid() && bConstraint;
    }
protected:
    void update(WDContext& context)
    {
        _aabb = DAabb3::Null();
        _lineRender.reset();
        _coneRender.reset();
        _textRender.reset();

        const WDCamera& camera = context.camera();
        const auto unitF = camera.pixelU(_d.position(), camera.viewSizeT<int>());
        // 移动轴线长度
        const auto size = unitF * 100.0;

        /**** 轴心点 *****/
        const auto center = _d.position();
        _aabb.expandByPoint(center);
        /**** 移动轴 *****/
        DVec3 axisP = _d.axisX();
        DVec3 axisTips = _d.axisY();
        DVec3 rotAxis = _d.axisZ();
        if (_currentLine)
        {
            auto dirOpt = _currentLine.value().dir();
            if (dirOpt && !DVec3::OnTheSameLine(dirOpt.value(), axisP))
            {
                auto mat = DMat4::FromQuat(DQuat::FromVectors(axisP, dirOpt.value()));
                axisP = dirOpt.value();
                axisTips = mat * axisTips;
                rotAxis  = mat * rotAxis;
            }
        }

        const auto axis = axisP * size;
        // 正轴
        _pAxes[0] = center;
        _pAxes[1] = center + axis;
        _aabb.expandByPoint(axis);
        // 负轴
        _nAxes[0] = center;
        _nAxes[1] = center - axis;
        _aabb.expandByPoint(-axis);
        /**** 旋转轴 *****/

        auto rAxis = rotAxis * size * 1.5;
        _rAxes[0] = center;
        _rAxes[1] = center + rAxis;
        _aabb.expandByPoint(rAxis);
        // 起始角度和角度范围, X表示起始的旋转角度，Y表示角度的范围
        DVec2 angleSRA = DVec2(0, -30);
        DVec2 angleSRB = DVec2(0, 30);
        // 弧线分段数
        const auto segments = static_cast<uint>(abs(angleSRA.y / 5.0));

        auto& rotAxisArcPartA = _rAxesArc[0];
        auto& rotAxisArcPartB = _rAxesArc[1];
        rotAxisArcPartA.resize(segments + 1);
        rotAxisArcPartB.resize(segments + 1);

        for (uint j = 0; j <= segments; ++j)
        {
            const auto step = static_cast<double>(j) / static_cast<double>(segments);
            const auto angleA = angleSRA.x + angleSRA.y * step;
            const auto quatA = DQuat::Rotate(angleA, axis);
            rotAxisArcPartA[j] = center + quatA * rAxis;

            const auto angleB = angleSRB.x + angleSRB.y * step;
            const auto quatB = DQuat::Rotate(angleB, axis);
            rotAxisArcPartB[j] = center + quatB * rAxis;
        } 
        _aabb.expandByPoint(rotAxisArcPartA.front());
        _aabb.expandByPoint(rotAxisArcPartB.back());

        /**** 箭头参数 *****/
        const float arrowDiameter   = static_cast<float>(size * 0.12);
        const float arrowLength     = static_cast<float>(size * 0.22);

        /**** 添加到绘制 *****/

        // 移动轴
        auto flags = this->mAxisDF();
        const auto dir  = axisP;
        // 轴线
        if (flags.hasFlag(MDF_AxisShown)) 
        {
            const auto& param = flags.hasFlag(MDF_AxisHeighlight) ? _hoverLineParam : _normalLineParam;
            _lineRender.addLineSeg(FVec3(_pAxes[0]), FVec3(_pAxes[1]), param);
            _lineRender.addLineSeg(FVec3(_nAxes[0]), FVec3(_nAxes[1]), param);
        }
        // 箭头
        if (flags.hasFlag(MDF_AxisArrowShown))
        {
            const auto& color = flags.hasFlag(MDF_AxisArrowHeighlight) ? _hoverLineParam.color : _normalLineParam.color;
            DMat4 tsfm = DMat4::Compose(_pAxes[1], DQuat::FromVectors(DVec3::AxisZ(), dir));
            _coneRender.addCone(arrowDiameter, arrowLength, color, FMat4(tsfm));
            DMat4 nTsfm = DMat4::Compose(_nAxes[1], DQuat::FromVectors(DVec3::AxisZ(), -dir));
            _coneRender.addCone(arrowDiameter, arrowLength, color, FMat4(nTsfm));
        }
        // 轴末端小球
        if (flags.hasFlag(MDF_AxisSphereShown)) 
        {
            const auto& color = _normalLineParam.color;
            DMat4 tsfm = DMat4::MakeTranslation(_pAxes[1]);
            _coneRender.addSphere(arrowDiameter, color, FMat4(tsfm));
            DMat4 bTsfm = DMat4::MakeTranslation(_nAxes[1]);
            _coneRender.addSphere(arrowDiameter, color, FMat4(bTsfm));            
        }
        // 轴向文字
        if (flags.hasFlag(MDF_AxisDirTextShown)) 
        {
            std::string rStr = DirectionParserENU::OutputStringByDirection(dir, 4);
            _textRender.addFixedDirection(stringToWString(rStr)
                , FVec3(center + dir * size * 1.1)
                , Color::white
                , 14
                , WDTextRender::HA_Left
                , WDTextRender::VA_Bottom
                , FVec2(0.0f)
                , true);
        }
        // 旋转轴
        RAxisDFs rDF = this->rAxisDF();
        // 显示轴
        if (rDF.hasFlag(RDF_AxisShown))
        {
            const auto& param = rDF.hasFlag(RDF_AxisHeighlight) ? _hoverLineParam : _normalLineParam;
            _lineRender.addLineSeg(FVec3(_rAxes[0]), FVec3(_rAxes[1]), param);
        }
        // 轴向文字
        if (rDF.hasFlag(RDF_AxisDirTextShown)) 
        {
            auto rDir = DVec3::Normalize(_rAxes[1] - _rAxes[0]);
            std::string rStr = DirectionParserENU::OutputStringByDirection(rDir, 4);
            _textRender.addFixedDirection(stringToWString(rStr)
                , FVec3(center + (_rAxes[1] - _rAxes[0]) * 1.1)
                , Color::white
                , 14
                , WDTextRender::HA_Left
                , WDTextRender::VA_Bottom
                , FVec2(0.0f)
                , true);
        }
        FVec3Vector tArcVs;
        for (size_t i = 0; i < _rAxesArc.size(); ++i) 
        {
            const auto& arcVs = _rAxesArc[i];
            tArcVs.clear();
            tArcVs.reserve(arcVs.size());
            for (const auto& v : arcVs)
                tArcVs.push_back(FVec3(v));

            // 显示轴
            if (rDF.hasFlag(RDF_AxisShown))
            {
                const auto& param = rDF.hasFlag(RDF_AxisHeighlight) ? _hoverLineParam : _normalLineParam;
                _lineRender.addLineStrip(tArcVs, param);
            }
            // 显示箭头
            if (rDF.hasFlag(RDF_AxisArrowShown)) 
            {
                const auto& color = rDF.hasFlag(RDF_AxisArrowHeighlight) ? _hoverLineParam.color : _normalLineParam.color;

                FMat4 tsfm1 = FMat4::Compose(FVec3(tArcVs.back())
                    , FQuat::FromVectors(FVec3::AxisZ(), (tArcVs[tArcVs.size() - 1] - tArcVs[tArcVs.size() - 2]).normalize()));
                _coneRender.addCone(arrowDiameter, arrowLength, color, tsfm1);

            }
        }

        // 辅助线样式
        auto lineParam = _normalLineParam;
        lineParam.style = WDLineRender::Style::DashLine;
        // 处理当前正在移动 / 旋转时 显示的辅助线 和值
        switch (this->getHType())
        {
        case EHandleType::HT_Move:
            {
                // 根据移动偏移量计算按下时的轴心位置
                DVec3 pt0 = _d.position() - _mOffset;
                // 当前轴心位置
                DVec3 pt1 = _d.position();

                // 移动偏移长度
                double len = 0.0;
                if (_currentLine && _currentLine.value().dir())
                {
                    if (WD::DVec3::Dot((pt1 - pt0), _currentLine.value().dir().value()) < 0)
                        len = -len;
                    switch (_axisPart)
                    {
                    case EAxisPart::AP_PDPart:
                        pt0 = _currentLine.value().posStart;
                        break;
                    case EAxisPart::AP_NDPart:
                        pt0 = _currentLine.value().posEnd;
                        break;
                    default:
                        break;
                    }
                    len = (pt1 - pt0).length();
                }
                else
                {
                    len = (pt1 - pt0).length();
                    if (DVec3::Dot(_mOffset, axisP) < 0)
                        len = -len;
                }

                if (fabs(len) >= NumLimits<float>::Epsilon)
                {
                    // 当前轴心点和按下轴心点的小方块
                    std::array<std::vector<FVec3>, 2> rects = { 
                          ScreenRect(camera, pt0, DVec2(size * 0.02)) 
                        , ScreenRect(camera, pt1, DVec2(size * 0.02)) };
                    for (const auto& rect : rects) 
                    {
                        for (size_t i = 0; i < rect.size(); i += 2) 
                        {
                            _lineRender.addLineLoop(rect, _normalLineParam);
                        }
                    }
                    wchar_t tText[1024] = { 0 };
                    swprintf_s(tText, 1024, L"%.2lf", len);

                    DVec3 offset = axisTips * size * 0.5;
                    DVec3 ptS = pt0;
                    DVec3 ptE = pt1;

                    _lineRender.addLineSeg(FVec3(ptS), FVec3(ptS + offset), lineParam);
                    _lineRender.addLineSeg(FVec3(ptE), FVec3(ptE + offset), lineParam);
                    // 连线
                    _lineRender.addLineSeg(FVec3(ptS + offset * 0.75), FVec3(ptE + offset * 0.75), lineParam);
                    // 距离文本
                    _textRender.addFixedDirection(tText
                        , FVec3((ptS + ptE) * 0.5 + offset * 0.75)
                        , Color::white
                        , 14
                        , WDTextRender::HA_Left
                        , WDTextRender::VA_Bottom
                        , FVec2(0.0f)
                        , true);
                }
            }
            break;
        case EHandleType::HT_Rotate:
            {
                // 当前轴心位置
                DVec3 pt1 = _d.position();
                if (Abs(_rAngle) >= NumLimits<float>::Epsilon)
                {
                    const DVec3& p0  = _rAxes[0];
                    const DVec3& p1  = _rAxes[1];
                    const auto v    = p1 - p0;
                    // 旋转法计算旋转弧线
                    uint segment    = static_cast<uint>(Abs(_rAngle / _stepAngle));
                    const auto tV1  = v * 0.75;
                    auto quat       = DQuat::Rotate(-_rAngle, axisP);
                    const auto tV0  = quat * tV1;
                    std::vector<FVec3> tRotArc;
                    tRotArc.reserve(segment + 1);
                    tRotArc.push_back(FVec3(p0 + tV0));
                    for (size_t i = 1; i < segment; ++i)
                    {
                        const auto angle = static_cast<double>(i) / static_cast<double>(segment) * _rAngle;
                        quat = DQuat::Rotate(angle, axisP);
                        tRotArc.push_back(FVec3(p0 + quat * tV0));
                    }
                    tRotArc.push_back(FVec3(p0 + tV1));
                    // 鼠标按下时轴心点到当前轴心点的连线
                    _lineRender.addLineStrip(tRotArc, lineParam);
                    // 旋转起点处画一个小方块
                    const auto rect = ScreenRect(camera, DVec3(tRotArc.front()), DVec2(size * 0.02));
                    _lineRender.addLineLoop(rect, _normalLineParam);
                    // 显示旋转角度
                    wchar_t tBuf[1024] = { 0 };
                    swprintf_s(tBuf, 1024, L"%.2lf", _rAngle);
                    _textRender.addFixedDirection(tBuf
                        , tRotArc[tRotArc.size() / 2]
                        , Color::white
                        , 14
                        , WDTextRender::HA_Left
                        , WDTextRender::VA_Bottom
                        , FVec2(0.0f)
                        , true);
                }
            }
            break;
        default:
            break;
        }

    }
    void render(WDContext& context)
    {
        _coneRender.render(context, false);
        _lineRender.render(context, false);
        _textRender.render(context, false);
    }
public:
    // 计算移动/旋转
    bool calc(WDContext& context
        , DVec4& outRelativeOffset
        , DVec4& outAbsoluteOffset
        , WDEditSingleAxisMoveRotate::HandleType& outHType)
    {
        outHType = WDEditSingleAxisMoveRotate::HandleType::HT_None;
        DVec3 oldMOffset = _mOffset;
        double oldRAngle = _rAngle;

        auto axis = _d.axisX();
        if (_currentLine)
        {
            auto axisOpt = _currentLine.value().dir();
            if (axisOpt && !DVec3::OnTheSameLine(axisOpt.value(), axis))
                axis = axisOpt.value();
        }

        switch (_selectedAxis)
        {
        case WD::WDEditSingleAxisMoveRotate::A_MAxis:
            {
                auto ret = Move(context, _d, axis, _startMousePos, _currentMousePos);
                if (ret)
                {
                    auto value = lengthWidthStep(ret.value());
                    _mOffset = axis * value;
                }
                outHType = WDEditSingleAxisMoveRotate::HandleType::HT_Move;
            }
            break;
        case WD::WDEditSingleAxisMoveRotate::A_RAxis:
            {
                auto ret = Rotate(context, _d, axis, _startMousePos, _currentMousePos);
                if (ret) 
                {
                    _rAngle = angleWidthStep(ret.value());
                }
                outHType = WDEditSingleAxisMoveRotate::HandleType::HT_Rotate;
            }
            break;
        default:
            break;
        }

        switch (outHType)
        {
        case WD::WDEditSingleAxisMoveRotate::HT_Move:
            {
                const auto& tAV = _mOffset;
                auto tAVLen = tAV.length();
                outAbsoluteOffset = DVec4(tAV.normalized(), tAVLen);
                outRelativeOffset = DVec4(WD::DVec3::Normalize(_mOffset - oldMOffset), WD::DVec3::Distance(_mOffset, oldMOffset));

                return true;
            }
            break;
        case WD::WDEditSingleAxisMoveRotate::HT_Rotate:
            {
                auto tA = _rAngle - oldRAngle;
                outRelativeOffset = DVec4(axis, tA);
                outAbsoluteOffset = DVec4(axis, _rAngle);
                return true;
            }
            break;
        default:
            break;
        }

        return false;
    }

    void execute(WDContext& context, WDEditSingleAxisMoveRotate::EditStatus statu)
    {
        DVec4 relativeROffset = DVec4::Zero();
        DVec4 absoluteROffset = DVec4::Zero();
        DVec4 relativeMOffset = DVec4::Zero();
        DVec4 absoluteMOffset = DVec4::Zero();
        if (_selectedAxis == WD::WDEditSingleAxisMoveRotate::A_MAxis && _d.constraintEnabled() && _d.constraint().valid()
            && _originLine && _currentLine)
        {
            std::optional<WDSingleAxisMRConstraint::ConstraintLine> tempCurrentLine = _currentLine;
            IVec2 tempStartMousePos = _startMousePos;

            auto& camera = context.camera();
            auto startDir = _originLine.value().dir();
            auto currDir = _currentLine.value().dir();
            if (startDir && currDir)
            {
                std::optional<DVec3> mouseDir = std::nullopt;
                if (_mouseConstLine && _mouseConstLine != _currentLine)
                    mouseDir = _mouseConstLine.value().dir();
                if (mouseDir)
                {
                    if (!DVec3::InTheSameDirection(startDir.value(), mouseDir.value()))
                    {
                        auto absQuat = DQuat::FromVectors(startDir.value(), mouseDir.value());
                        absoluteROffset = DVec4(absQuat.axis(), absQuat.angle());

                        if (!DVec3::InTheSameDirection(currDir.value(), mouseDir.value()))
                        {
                            auto mat = DMat4::FromQuat(DQuat::FromVectors(startDir.value(), currDir.value()));
                            auto quat = DMat4::ToQuat((DMat4::FromQuat(absQuat) * mat.invert()));
                            relativeROffset = DVec4(quat.axis(), quat.angle());
                        }
                    }
                    else if (!DVec3::InTheSameDirection(currDir.value(), mouseDir.value()))
                    {
                        auto quat = DQuat::FromVectors(currDir.value(), mouseDir.value());
                        relativeROffset = DVec4(quat.axis(), quat.angle());
                    }
                    tempCurrentLine = _mouseConstLine;
                    tempStartMousePos = IVec2(camera.worldToScreen(_mouseConstLine.value().result));
                }
                else
                {
                    auto absQuat = DQuat::FromVectors(startDir.value(), currDir.value());
                    absoluteROffset = DVec4(absQuat.axis(), absQuat.angle());
                }
            }

            DVec3 rangeStart = tempCurrentLine.value().posStart;
            DVec3 rangeEnd = tempCurrentLine.value().posEnd;

            if (DVec3::Distance(rangeStart, rangeEnd) > nodeThick)
            {
                auto dir = DVec3::Normalize(rangeEnd - rangeStart);
                rangeStart = rangeStart + dir * (nodeThick / 2.0);
                rangeEnd = rangeEnd - dir * (nodeThick / 2.0);
            }

            const IVec2 startScenePosS = IVec2(camera.worldToScreen(rangeStart));
            const IVec2 startScenePosE = IVec2(camera.worldToScreen(rangeEnd));
            const IVec2 startPos = IVec2(camera.worldToScreen(tempCurrentLine.value().result));
            auto mAxis = tempCurrentLine.value().dir();
            if (mAxis)
            {
                auto ret = Move(context, _d, mAxis.value(), tempStartMousePos, _currentMousePos, std::array<IVec2, 3>{startScenePosS, startScenePosE, startPos});
                if (ret)
                {
                    if (statu == WDEditSingleAxisMoveRotate::EditStatus::ES_Start)
                        _handleType = WDEditSingleAxisMoveRotate::HandleType::HT_Move;
                    _currentLine = tempCurrentLine;
                    _startMousePos = tempStartMousePos;
                    auto value = lengthWidthStep(ret.value());
                    _mOffset = mAxis.value() * value;
                    auto resultPos = _currentLine.value().result + _mOffset;
                    if (_currentPos != resultPos)
                    {
                        relativeMOffset.setXyz(DVec3::Normalize(resultPos - _currentPos));
                        relativeMOffset.w = DVec3::Distance(resultPos, _currentPos);
                        _currentPos = resultPos;
                    }
                    const auto& originPosition = _originLine.value().result;
                    if (originPosition != resultPos)
                    {
                        absoluteMOffset.setXyz(DVec3::Normalize(resultPos - originPosition));
                        absoluteMOffset.w = DVec3::Distance(resultPos, originPosition);
                    }
                    _delegate(statu
                        , WDEditSingleAxisMoveRotate::HandleType::HT_Move
                        , relativeMOffset
                        , absoluteMOffset
                        , relativeROffset
                        , absoluteROffset
                        , originPosition
                        , _d);
                }
            }
        }
        else
        {
            EHandleType hType = EHandleType::HT_None;
            switch (_selectedAxis)
            {
            case WD::WDEditSingleAxisMoveRotate::A_MAxis:
                {
                    if (calc(context, relativeMOffset, absoluteMOffset, hType))
                    {
                        if (statu == WDEditSingleAxisMoveRotate::EditStatus::ES_Start)
                            _handleType = hType;
                        _delegate(statu
                            , hType
                            , relativeMOffset
                            , absoluteMOffset
                            , relativeROffset
                            , absoluteROffset
                            , _d.position()
                            , _d);
                    }
                }
                break;
            case WD::WDEditSingleAxisMoveRotate::A_RAxis:
                {
                    if (calc(context, relativeROffset, absoluteROffset, hType))
                    {
                        if (statu == WDEditSingleAxisMoveRotate::EditStatus::ES_Start)
                            _handleType = hType;
                        _delegate(statu
                            , hType
                            , relativeMOffset
                            , absoluteMOffset
                            , relativeROffset
                            , absoluteROffset
                            , _d.position()
                            , _d);
                    }
                }
                break;
            default:
                break;
            }
        }
    }
    //高亮轴
    WDEditSingleAxisMoveRotate::Axis hoverAxis(WDContext& context, const IVec2& pos)
    {
        WDEditSingleAxisMoveRotate::Axis oldHovered = _hoveredAxis;

        _hoveredAxis = pickAxis(context, pos);

        if (oldHovered != _hoveredAxis)
            _d.sendHoveredDelegate();

        return _hoveredAxis;
    }
public:
    static WDEditSingleAxisMoveRotate::HandleType HandleType(EAxis axis)
    {
        switch (axis)
        {
        case WDEditSingleAxisMoveRotate::A_MAxis:
            return WDEditSingleAxisMoveRotate::HT_Move;
            break;
        case WDEditSingleAxisMoveRotate::A_RAxis:
            return WDEditSingleAxisMoveRotate::HT_Rotate;
            break;
        default:
            break;
        }
        return WDEditSingleAxisMoveRotate::HT_None;

    }
private:
    EAxis getHoveredAxis() const 
    {
        return _hoveredAxis;
    }
    EHandleType getHType() const
    {
        return _handleType;
    }

    // 移动轴的绘制标记
    enum MAxisDF
    {
        // 无
        MDF_None                    = 0,
        // 显示轴线
        MDF_AxisShown               = 1 << 0,
        // 轴线高亮
        MDF_AxisHeighlight          = 1 << 1,
        // 显示轴线箭头
        MDF_AxisArrowShown          = 1 << 2,
        // 轴线箭头高亮
        MDF_AxisArrowHeighlight     = 1 << 3,
        // 显示轴线末端小球
        MDF_AxisSphereShown         = 1 << 4,
        // 显示轴标签
        MDF_AxisLabelShown          = 1 << 5,
        // 轴向文字
        MDF_AxisDirTextShown        = 1 << 6,
    };
    using MAxisDFs = WDFlags<MAxisDF, uint>;

    // 获取移动轴的显示和高亮的标志
    MAxisDFs mAxisDF()
    {
        MAxisDFs flag = MDF_None;

        flag.setFlag(MDF_AxisShown);
        switch (this->getHType())
        {
        case EHandleType::HT_None:
        case EHandleType::HT_Move:
            {
                flag.setFlag(MDF_AxisArrowShown);
                flag.setFlag(MDF_AxisLabelShown);
                flag.setFlag(MDF_AxisDirTextShown);
                flag.setFlag(MDF_AxisHeighlight,        this->getHoveredAxis() == EAxis::A_MAxis);
                flag.setFlag(MDF_AxisArrowHeighlight,   this->getHoveredAxis() == EAxis::A_MAxis);
            }
            break;
        case EHandleType::HT_Rotate:
            {
                flag.setFlag(MDF_AxisSphereShown, true);
            }
            break;
        default:
            break;
        }

        return flag;
    }

    // 旋转轴弧线的绘制标记
    enum RAxisDF
    {
        // 无
        RDF_None = 0,
        // 显示轴线
        RDF_AxisShown = 1 << 0,
        // 轴线高亮
        RDF_AxisHeighlight = 1 << 1,
        // 显示轴线箭头
        RDF_AxisArrowShown = 1 << 2,
        // 轴线箭头高亮
        RDF_AxisArrowHeighlight = 1 << 3,
        // 轴向文字
        RDF_AxisDirTextShown    = 1 << 4,
    };
    using RAxisDFs = WDFlags<RAxisDF, uint>;
    RAxisDFs rAxisDF()
    {
        RAxisDFs rFlag = RDF_None;
        switch (this->getHType())
        {
        case EHandleType::HT_None:
            {
                rFlag.setFlag(RDF_AxisShown);
                rFlag.setFlag(RDF_AxisArrowShown);
                rFlag.setFlag(RDF_AxisDirTextShown,     this->getHoveredAxis() == WDEditSingleAxisMoveRotate::A_RAxis);
                rFlag.setFlag(RDF_AxisHeighlight,       this->getHoveredAxis() == WDEditSingleAxisMoveRotate::A_RAxis);
                rFlag.setFlag(RDF_AxisArrowHeighlight,  this->getHoveredAxis() == WDEditSingleAxisMoveRotate::A_RAxis);
            }
            break;
        case EHandleType::HT_Move:
            break;
        case EHandleType::HT_Rotate:
            {
                rFlag.setFlag(RDF_AxisShown);
                rFlag.setFlag(RDF_AxisArrowShown);
                rFlag.setFlag(RDF_AxisDirTextShown);
            }
            break;
        default:
            break;
        }


        return rFlag;
    }
    // 拾取轴
    WDEditSingleAxisMoveRotate::Axis pickAxis(WDContext& context, const IVec2& screen)
    {
        WDCamera& camera = context.camera();
        const DVec3 mouse(screen.x, screen.y, 0);

        _axisPart = EAxisPart::AP_None;

        // 1. 先拾取移动轴
        {
            std::array<EAxisPart, 2> flags = {EAxisPart::AP_PDPart, EAxisPart::AP_NDPart};
            std::array<std::array<DVec3, 2>, 2> axiss = {_pAxes, _nAxes};
            for (size_t i = 0; i < 2; i++)
            {
                const auto& axis = axiss[i];
                const auto sPt0 = camera.worldToScreen(axis[0]);
                const auto sPt1 = camera.worldToScreen(axis[1]);
                const auto rPt = DSegment3::ClosestPointToPoint(mouse, DVec3(sPt0), DVec3(sPt1));
                if (DVec3::Distance(rPt, mouse) < distMax)
                {
                    _axisPart = flags[i];
                    return WDEditSingleAxisMoveRotate::A_MAxis;
                }
            }
        }
        // 2. 再拾取旋转轴的弧线
        {
            real dist[2] = { NumLimits<float>::Max
                , NumLimits<float>::Max};
            {
                const auto sPt0 = camera.worldToScreen(_rAxes[0]);
                const auto sPt1 = camera.worldToScreen(_rAxes[1]);
                const auto rPt = DSegment3::ClosestPointToPoint(mouse, DVec3(sPt0), DVec3(sPt1));
                if (DVec3::Distance(rPt, mouse) < distMax)
                    return WDEditSingleAxisMoveRotate::A_RAxis;
            }

            for (size_t i = 0; i < 2; i++)
            {
                const auto& arc = _rAxesArc[i];
                for (size_t j = 0; j < (arc.size() - 1); j += 2)
                {
                    const auto sPt0 = camera.worldToScreen(arc[j + 0]);
                    const auto sPt1 = camera.worldToScreen(arc[j + 1]);
                    const auto rPt = DSegment3::ClosestPointToPoint(mouse, DVec3(sPt0), DVec3(sPt1));
                    if (DVec3::Distance(rPt, mouse) < distMax)
                        return WDEditSingleAxisMoveRotate::A_RAxis;
                }
            }
        }

        // 没有拾取到任何轴
        return  WDEditSingleAxisMoveRotate::A_Null;
    }

    // 使用步长重新计算长度
    inline double lengthWidthStep(double len) const
    {
        int r = static_cast<int>(len / _stepLength);
        return _stepLength * static_cast<double>(r);
    }
    // 使用步长重新计算角度
    inline double angleWidthStep(double angle) const
    {
        if (angle < 0.0)
        {
            int r = static_cast<int>(angle / _stepAngle - 0.5);
            return _stepAngle * static_cast<double>(r);
        }
        else
        {
            int r = static_cast<int>(angle / _stepAngle + 0.5);
            return _stepAngle * static_cast<double>(r);
        }
    }
private:
    static std::optional<double> Move(WDContext& context
        , const WDEditSingleAxisMoveRotate& d
        , const DVec3& axis
        , const IVec2& start
        , const IVec2& end
        , const std::optional<std::array<IVec2, 3>>& rangeConstraint = std::nullopt)
    {
        if (start == end)
            return 0.0;

        WDCamera& camera = context.camera();
        DVec3 faceNor = camera.frontDir();
        if (!d.calcAxisPlaneNormalize(camera, axis, faceNor))
            return std::nullopt;

        DRay rayStart = camera.createRayFromScreen(DVec2(start), camera.viewSizeT<int>());
        DRay rayEnd = camera.createRayFromScreen(DVec2(end), camera.viewSizeT<int>());

        DPlane plane(faceNor, d.position());
        auto r0 = rayStart.intersect(plane);
        auto r1 = rayEnd.intersect(plane);

        if (r0.first && r1.first)
        {
            DVec3 retPt0 = rayStart.at(r0.second);
            DVec3 retPt1 = rayEnd.at(r1.second);

            DVec3 retV = retPt1 - retPt0;
            real len = DVec3::Dot(axis, retV);
            // 如果有限制移动范围,这里重新计算移动的距离
            if (rangeConstraint)
            {
                DRay rayOrigin = camera.createRayFromScreen(DVec2(rangeConstraint.value()[2]), camera.viewSizeT<int>());
                auto rOrigin = rayOrigin.intersect(plane);
                if (rOrigin.first)
                {
                    auto retOrigin = rayOrigin.at(rOrigin.second);
                    if (len > 0)
                    {
                        DRay rayRangeEnd = camera.createRayFromScreen(DVec2(rangeConstraint.value()[1]), camera.viewSizeT<int>());
                        auto rRange1 = rayRangeEnd.intersect(plane);
                        if (rRange1.first)
                        {
                            DVec3 retRangeEnd = rayRangeEnd.at(rRange1.second);
                            DVec3 retRV = retRangeEnd - retOrigin;
                            len = std::min(fabs(len), fabs(DVec3::Dot(axis, retRV)));
                        }
                    }
                    else
                    {
                        DRay rayRangeStart = camera.createRayFromScreen(DVec2(rangeConstraint.value()[0]), camera.viewSizeT<int>());
                        auto rRange0 = rayRangeStart.intersect(plane);
                        if (rRange0.first)
                        {
                            DVec3 retRangeStart = rayRangeStart.at(rRange0.second);
                            DVec3 retRV = retOrigin - retRangeStart;
                            len = -std::min(fabs(len), fabs(DVec3::Dot(axis, retRV)));
                        }
                    }
                }
            }
            return len;
        }
        return std::nullopt;
    }

    static std::optional<double> Rotate(WDContext& context
        , const WDEditSingleAxisMoveRotate& d
        , const DVec3& axis
        , const IVec2& start, const IVec2& end)
    {
        if (start == end)
            return 0.0;

        WDCamera& camera = context.camera();

        DVec3 faceNor   = axis.normalized();

        DRay rayStart   = camera.createRayFromScreen(DVec2(start), camera.viewSizeT<int>());
        DRay rayEnd     = camera.createRayFromScreen(DVec2(end), camera.viewSizeT<int>());

        DPlane plane(faceNor, d.position());
        auto r0 = rayStart.intersect(plane);
        auto r1 = rayEnd.intersect(plane);
        if (r0.first && r1.first)
        {
            DVec3 retPt0 = rayStart.at(r0.second);
            DVec3 retPt1 = rayEnd.at(r1.second);

            DVec3 v0 = retPt0 - d.position();
            DVec3 v1 = retPt1 - d.position();

            if (v0.lengthSq() <= NumLimits<float>::Epsilon
                || v1.lengthSq() <= NumLimits<float>::Epsilon)
                return std::nullopt;
            auto rAngle = DVec3::Angle(v0, v1);
            if (DVec3::Dot(axis, DVec3::Cross(v0, v1)) < 0.0) 
                rAngle = -rAngle;

            return rAngle;
        }

        return std::nullopt;
    }


    static std::vector<FVec3> ScreenRect(const WDCamera& camera, const DVec3& pos, const DVec2& size, double angle = 45.0) 
    {
        Quat quat = Quat::Rotate(angle, camera.frontDir());
        const auto rightDir = quat * camera.rightDir(); 
        const auto upDir = quat * camera.upDir();
        return 
        {
              FVec3(pos + rightDir * size.x + upDir * size.y)
            , FVec3(pos - rightDir * size.x + upDir * size.y)
            , FVec3(pos - rightDir * size.x - upDir * size.y)
            , FVec3(pos + rightDir * size.x - upDir * size.y)
        };
    }
};

std::optional<WDSingleAxisMRConstraint::ConstraintLine> WDSingleAxisMRConstraint::bWorldPointIsConstraint(const DVec3& point
    , double errorRange) const
{
    double minDis = NumLimits<double>::Max;
    ConstraintLine ret;
    bool bOk = false;
    for (int i = 0; i < constraintLines.size(); ++i)
    {
        const auto& eachLine = constraintLines[i];
        const auto seg = DSegment3(eachLine.posStart, eachLine.posEnd);
        auto closestPoint = seg.closestPointToPoint(point);

        auto error = errorRange;
        if (eachLine.maxErrorRange)
            error = std::fmax(eachLine.maxErrorRange.value(), errorRange);
        auto distance = DVec3::Distance(closestPoint, point);
        if (distance > error || distance >= minDis)
            continue;
        bOk = true;
        minDis = distance;
        ret = eachLine;
        ret.result = closestPoint;
    }
    if (!bOk)
        return std::nullopt;

    return ret;
}
std::optional<WDSingleAxisMRConstraint::ConstraintLine> WDSingleAxisMRConstraint::bScreenPointIsConstraint(const DVec2& point
    , std::function<DVec2(const DVec3&)> trsFunc
    , std::function<double(const double)> errorTrsFunc
    , double errorRange) const
{
    double minDis = NumLimits<double>::Max;
    ConstraintLine ret;
    bool bOk = false;
    for (int i = 0; i < constraintLines.size(); ++i)
    {
        const auto& eachLine = constraintLines[i];
        const auto seg = DSegment3(DVec3(trsFunc(eachLine.posStart)), DVec3(trsFunc(eachLine.posEnd)));
        auto closestPoint = seg.closestPointToPoint(DVec3(point));

        auto error = errorRange;
        if (eachLine.maxErrorRange)
            error = errorTrsFunc(eachLine.maxErrorRange.value());
        auto distance = DVec3::Distance(closestPoint, DVec3(point));
        if (distance > error || distance >= minDis)
            continue;
        bOk = true;
        minDis = distance;
        ret = eachLine;
        if (closestPoint == seg.start)
        {
            ret.result = eachLine.posStart;
        }
        else if (closestPoint == seg.end)
        {
            ret.result = eachLine.posEnd;
        }
        else
        {
            ret.result = eachLine.posStart
                + (eachLine.posEnd - eachLine.posStart) * ((closestPoint - seg.start).length() / (eachLine.posEnd - eachLine.posStart).length());
        }
    }
    if (!bOk)
        return std::nullopt;

    return ret;
}

std::optional<WDSingleAxisMRConstraint::ConstraintLine> WDSingleAxisMRConstraint::bWorldLineIsConstraint(const DVec3& pointS
    , const DVec3& pointE
    , double errorRange) const
{
    const double distance = DVec3::Distance(pointS, pointE);
    if (distance < NumLimits<double>::Epsilon)
        return std::nullopt;
    return bWorldLineIsConstraint((pointS + pointE) / 2, distance, errorRange);
}
std::optional<WDSingleAxisMRConstraint::ConstraintLine> WDSingleAxisMRConstraint::bWorldLineIsConstraint(const DVec3& center
    , const double& length
    , double errorRange) const
{
    auto ret = bWorldPointIsConstraint(center, errorRange);
    if (ret && length > NumLimits<double>::Epsilon)
    {
        auto retLength = DVec3::Distance(ret.value().posStart, ret.value().posEnd);
        if (retLength < length)
            return std::nullopt;
        auto halfLength = length / 2.0;
        if (DVec3::Distance(ret.value().result, ret.value().posStart) < halfLength)
            ret.value().result = ret.value().posStart + (ret.value().posEnd - ret.value().posStart).normalized() * halfLength;
        else if (DVec3::Distance(ret.value().result, ret.value().posEnd) < halfLength)
            ret.value().result = ret.value().posEnd - (ret.value().posEnd - ret.value().posStart).normalized() * halfLength;
    }
    return ret;
}
std::optional<WDSingleAxisMRConstraint::ConstraintLine> WDSingleAxisMRConstraint::bScreenLineIsConstraint(const DVec2& pointS
    , const DVec2& pointE
    , std::function<DVec2(const DVec3&)> trsFunc
    , std::function<double(const double)> errorTrsFunc
    , double errorRange) const
{
    const double distance = DVec2::Distance(pointS, pointE);
    if (distance < NumLimits<double>::Epsilon)
        return std::nullopt;
    return bScreenLineIsConstraint((pointS + pointE) / 2, distance, trsFunc, errorTrsFunc, errorRange);
};
std::optional<WDSingleAxisMRConstraint::ConstraintLine> WDSingleAxisMRConstraint::bScreenLineIsConstraint(const DVec2& center
    , const double& length
    , std::function<DVec2(const DVec3&)> trsFunc
    , std::function<double(const double)> errorTrsFunc
    , double errorRange) const
{
    auto ret = bScreenPointIsConstraint(center, trsFunc, errorTrsFunc, errorRange);
    if (ret && length > NumLimits<double>::Epsilon)
    {
        auto retLength = DVec3::Distance(ret.value().posStart, ret.value().posEnd);
        if (retLength < length)
            return std::nullopt;
        auto halfLength = length / 2.0;
        if (DVec3::Distance(ret.value().result, ret.value().posStart) < halfLength)
            ret.value().result = ret.value().posStart + (ret.value().posEnd - ret.value().posStart).normalized() * halfLength;
        else if (DVec3::Distance(ret.value().result, ret.value().posEnd) < halfLength)
            ret.value().result = ret.value().posEnd - (ret.value().posEnd - ret.value().posStart).normalized() * halfLength;
    }
    return ret;
}

WDEditSingleAxisMoveRotate::WDEditSingleAxisMoveRotate(): WDEditAxis(EAT_MoveRotate)
{
    _p = new WDEditSingleAxisMoveRotatePrivate(*this);
}
WDEditSingleAxisMoveRotate::~WDEditSingleAxisMoveRotate()
{
    delete _p;
    _p = nullptr;
}

WDEditSingleAxisMoveRotate::MDelegate& WDEditSingleAxisMoveRotate::mDelegate()
{
    return _p->_delegate;
}
WDEditSingleAxisMoveRotate::MContextMenuDelegate& WDEditSingleAxisMoveRotate::mContextMenuDelegate()
{
    return _p->_cxtMenuDelegate;
}
WDEditSingleAxisMoveRotate::MClickedDelegate& WDEditSingleAxisMoveRotate::mClickedDelegate()
{
    return _p->_clickedDelegate;
}
WDEditSingleAxisMoveRotate::MDBClickedDelegate& WDEditSingleAxisMoveRotate::mDBClickedDelegate()
{
    return _p->_dbClickedDelegate;
}

void WDEditSingleAxisMoveRotate::setObjectThickness(const double& thickness)
{
    if (thickness <= NumLimits<double>::Epsilon)
        return;
    _p->nodeThick = thickness;
}
const double& WDEditSingleAxisMoveRotate::objectThickness() const
{
    return _p->nodeThick;
}
void WDEditSingleAxisMoveRotate::setStepLength(double step)
{
    _p->_stepLength = step;
}
double WDEditSingleAxisMoveRotate::stepLength() const
{
    return _p->_stepLength;
}
void WDEditSingleAxisMoveRotate::setStepAngle(double step)
{
    _p->_stepAngle = step;
}
double WDEditSingleAxisMoveRotate::stepAngle() const
{
    return _p->_stepAngle;
}

void WDEditSingleAxisMoveRotate::exec(Axis axis, double offset)
{
    if (fabs(offset) <= NumLimits<double>::Epsilon)
        return;
    HandleType hType = HandleType::HT_None;
    DVec4 offM = DVec4::Zero();
    DVec4 offR = DVec4::Zero();
    switch (axis)
    {
    case WD::WDEditSingleAxisMoveRotate::A_MAxis:
        {
            if (_p->_originLine && _p->_originLine.value().dir())
            {
                const DVec3 dir = _p->_originLine.value().dir().value();
                const auto& currentPosition = _p->_originLine.value().result;
                const auto& pS = _p->_originLine.value().posStart;
                const auto& pE = _p->_originLine.value().posEnd;
                if (offset > 0)
                {
                    offset = std::min(DVec3::Distance(currentPosition, pE), offset);
                }
                else
                {
                    offset = -std::min(DVec3::Distance(currentPosition, pS), fabs(offset));
                }
                offM = DVec4(dir, offset);
                hType = HandleType::HT_Move;
            }
            else
            {
                offM = DVec4(this->axisX(), offset);
                hType = HandleType::HT_Move;
            }
        }
        break;
    case WD::WDEditSingleAxisMoveRotate::A_RAxis:
        {
            if (_p->_originLine && _p->_originLine.value().dir())
            {
                const DVec3 dir = _p->_originLine.value().dir().value();
                offR = DVec4(dir, offset);
                hType = HandleType::HT_Rotate;
            }
            else
            {
                offR = DVec4(this->axisX(), offset);
                hType = HandleType::HT_Rotate;
            }
        }
        break;
    default:
        break;
    }
    _p->_delegate(EditStatus::ES_Command, hType, DVec4(0.0), offM, DVec4(0.0), offR, transform().extractTranslation(), *this);
}

WDEditSingleAxisMoveRotate::Axis WDEditSingleAxisMoveRotate::hoveredAxis() const
{
    return _p->_hoveredAxis;
}
WDEditSingleAxisMoveRotate::Axis WDEditSingleAxisMoveRotate::selectedAxis() const
{
    return _p->_selectedAxis;
}
void WDEditSingleAxisMoveRotate::beginPreviewAlign(Axis axis)
{
    _p->_mOffset            = DVec3::Zero();
    _p->_previewHoveredAxis = axis;
    _p->_previewHandleType  = HT_None;
    switch (axis)
    {
    case WD::WDEditSingleAxisMoveRotate::A_MAxis:
        _p->_previewHandleType  = HT_Move;
        break;
    case WD::WDEditSingleAxisMoveRotate::A_RAxis:
        _p->_previewHandleType  = HT_Rotate;
        break;
    default:
        break;
    }
    _p->_previewBasePosition = this->position();
    _p->_previewBaseZ = axisZ();
    if (_p->_originLine && _p->_originLine.value().dir())
    {
        const DVec3 dir = _p->_originLine.value().dir().value();
        if (!DVec3::OnTheSameLine(dir, axisX()))
        {
            auto mat = DMat4::FromQuat(DQuat::FromVectors(axisX(), dir));
            _p->_previewBaseZ  = mat * _p->_previewBaseZ;
        }
    }

}
DVec4 WDEditSingleAxisMoveRotate::calcPreviewAlign(const std::optional<DVec3>& vec)
{
    _p->_mOffset    = DVec3::Zero();
    _p->_rAngle     = 0.0;

    if (_p->_previewHoveredAxis == Axis::A_Null)
        return DVec4(this->axisX(), 0.0);

    if (!vec)
        return DVec4(this->axisX(), 0.0);

    switch (_p->_previewHoveredAxis)
    {
    case A_MAxis:
        {
            auto v = vec.value() - _p->_previewBasePosition;
            if (v.lengthSq() <= NumLimits<float>::Epsilon)
                return DVec4(this->axisX(), 0.0);

            if (_p->_originLine && _p->_originLine.value().dir())
            {
                const DVec3 dir = _p->_originLine.value().dir().value();
                auto offset = DVec3::Dot(dir, v);
                const auto& currentPosition = _p->_originLine.value().result;
                const auto& pS = _p->_originLine.value().posStart;
                const auto& pE = _p->_originLine.value().posEnd;
                if (offset > 0)
                    offset = std::min(DVec3::Distance(currentPosition, pE), offset);
                else
                    offset = -std::min(DVec3::Distance(currentPosition, pS), fabs(offset));

                _p->_mOffset = dir * offset;
                return DVec4(dir, offset);
            }
        }
        break;
    case A_RAxis:
        {
            if (_p->_originLine && _p->_originLine.value().dir())
            {
                const DVec3 dir = _p->_originLine.value().dir().value();
                // 如果朝向和轴平行，则不做对齐
                DPlane plane(dir, DVec3::Zero());
                DVec3 tDir  = plane.project(vec.value());
                if (tDir.lengthSq() <= NumLimits<float>::Epsilon)
                    return DVec4(this->axisX(), 0.0);

                tDir.normalize();
                const DVec3& rotAxis = _p->_previewBaseZ;

                auto angle  = DVec3::Angle(rotAxis, tDir);
                auto vc     = DVec3::Normalize(DVec3::Cross(rotAxis, tDir));
                if (DVec3::Dot(vc, dir) <= 0.0)
                    angle   = -angle;

                _p->_rAngle = angle;
                return DVec4(dir, angle);
            }
        }
        break;
    default:
        break;
    }
    return DVec4(this->axisX(), 0.0);
}
void WDEditSingleAxisMoveRotate::endPreviewAlign()
{
    _p->_previewHandleType  = HT_None;
    _p->_previewHoveredAxis = A_Null;
    _p->_mOffset            = DVec3::Zero();
    _p->_rAngle             = 0.0;
}

const WDSingleAxisMRConstraint& WDEditSingleAxisMoveRotate::constraint() const
{
    return _p->constraint;
}
void WDEditSingleAxisMoveRotate::setConstraint(const Constraint& constraint)
{
    _p->constraint = constraint;
    if (!_p->bConstraintEnabled())
    {
        _p->_originLine = std::nullopt;
        _p->_currentLine = std::nullopt;
    }
}
void WDEditSingleAxisMoveRotate::setConstraintEnabled(bool bEnabled)
{
    _p->bConstraint = bEnabled;
    if (!_p->bConstraintEnabled())
    {
        _p->_originLine = std::nullopt;
        _p->_currentLine = std::nullopt;
    }
}
const bool& WDEditSingleAxisMoveRotate::constraintEnabled() const
{
    return _p->bConstraint;
}

std::optional<WDEditSingleAxisMoveRotate::Constraint::ConstraintLine> WDEditSingleAxisMoveRotate::currentConstraintLine()
{
    return _p->_currentLine;
}

bool WDEditSingleAxisMoveRotate::isAxisHovered() const
{
    return _p->_hoveredAxis != Axis::A_Null;
}
bool WDEditSingleAxisMoveRotate::isAxisSelected() const
{
    return _p->_selectedAxis != Axis::A_Null;
}
void WDEditSingleAxisMoveRotate::cancelHovered()
{
    Axis oldHovered = _p->_hoveredAxis;
    _p->_hoveredAxis = WDEditSingleAxisMoveRotate::Axis::A_Null;
    if (oldHovered != _p->_hoveredAxis)
    {
        this->sendHoveredDelegate();
    }
}
void WDEditSingleAxisMoveRotate::cancelSelected()
{
    Axis oldSelected = _p->_selectedAxis;
    _p->_selectedAxis = WDEditSingleAxisMoveRotate::Axis::A_Null;
    if (oldSelected != _p->_selectedAxis)
    {
        this->sendSelectedDelegate();
    }
}

bool WDEditSingleAxisMoveRotate::mouseButtonPress(WDContext& context, const IVec2& pos, MouseButton btn)
{
    WDUnused(context);
    
    _p->_clickedDelegate(_p->_hoveredAxis, pos, btn, *this);

    switch (btn)
    {
    case WD::WDEditAxis::MB_None:
        break;
    case WD::WDEditAxis::MB_Left:
        {
            _p->_mouseConstLine     = std::nullopt;
            _p->_startMousePos      = pos;
            _p->_originTransform    = _transform;
            if (_p->_currentLine)
                _p->_currentPos     = _p->_currentLine.value().result;
            else
                _p->_currentPos     = _p->_originTransform.extractTranslation();
            _p->_currentMousePos    = pos;
            _p->_bMouseDown         = true;
            _p->_handleType         = HandleType::HT_None;
            Axis oldSelected    = _p->_selectedAxis;
            _p->_selectedAxis   = _p->_hoveredAxis;

            if (oldSelected != _p->_selectedAxis)
            {
                this->sendSelectedDelegate();
            }


            if (isAxisSelected())
            {
                _p->execute(context, EditStatus::ES_Start);
                return true;
            }

            return false;
        }
        break;
    case WD::WDEditAxis::MB_Middle:
        break;
    case WD::WDEditAxis::MB_Right:
        {
            _p->_handleType = HandleType::HT_None;
            _p->_rDownPos   = pos;
            _p->_rDownAxis  = _p->_hoveredAxis;

            if (_p->_rDownAxis != A_Null)
            {
                _p->_handleType = WDEditSingleAxisMoveRotatePrivate::HandleType(_p->_rDownAxis);

                return true;
            }
        }
        break;
    default:
        break;
    }

    return false;
}
bool WDEditSingleAxisMoveRotate::mouseButtonRelease(WDContext& context, const IVec2& pos, MouseButton btn)
{
    WDUnused(context);

    switch (btn)
    {
    case WD::WDEditAxis::MB_None:
        break;
    case WD::WDEditAxis::MB_Left:
        {
            bool bRet = false;
            if (isAxisSelected())
            {
                _p->execute(context, EditStatus::ES_End);

                _p->_mOffset = DVec3::Zero();
                _p->_rAngle = 0.0;

                Axis oldSelected = _p->_selectedAxis;
                _p->_selectedAxis = A_Null;
                if (oldSelected != _p->_selectedAxis)
                {
                    this->sendSelectedDelegate();
                }

                bRet = true;
            }

            _p->_bMouseDown         = false;
            _p->_startMousePos      = pos;
            _p->_originTransform    = _transform;
            _p->_currentMousePos    = pos;
            _p->_handleType         = HandleType::HT_None;

            return bRet;
        }
        break;
    case WD::WDEditAxis::MB_Middle:
        break;
    case WD::WDEditAxis::MB_Right:
        {
            // 右键菜单
            auto tRDownAxis = _p->_rDownAxis;
            _p->_rDownAxis  = A_Null;
            _p->_handleType = HandleType::HT_None;
            if (DVec2::Distance(DVec2(_p->_rDownPos), DVec2(pos)) <= 8.0
                && tRDownAxis != A_Null)
            {
                _p->_cxtMenuDelegate(tRDownAxis, pos, *this);

                return true;
            }
        }
        break;
    default:
        break;
    }

    return false;
}
bool WDEditSingleAxisMoveRotate::mouseMove(WDContext& context, const IVec2& pos)
{
    bool bRet = false;
    if (isAxisSelected())
    {
        _p->_currentMousePos = pos;
        _p->_mouseConstLine = std::nullopt;
        auto& camera = context.camera();
        if (_p->_currentLine)
        {
            _p->_mouseConstLine = constraint().bScreenLineIsConstraint(WD::DVec2(pos), _p->nodeThick,
                [&camera](const DVec3& point) ->DVec2
            {
                return camera.worldToScreen(point);
            }, [&](const double value)->double
            {
                const DVec3 p0 = position();
                const DVec3 p1 = p0 + axisX() * value;
                const DVec2 s0 = camera.worldToScreen(p0);
                const DVec2 s1 = camera.worldToScreen(p1);
                return DVec2::Distance(s0, s1);
            }, _p->distMax);
        }
        _p->execute(context, EditStatus::ES_Editting);

        bRet = true;
    }
    else if (_p->_rDownAxis != A_Null)
    {
        bRet = true;
    }
    else if (!_p->_bMouseDown)
    {
        _p->hoverAxis(context, pos);

        bRet = false;
    }

    return bRet;
}
bool WDEditSingleAxisMoveRotate::mouseDoubleClicked(WDContext& context, const IVec2& pos, MouseButton btn)
{
    WDUnused(context);

    _p->_dbClickedDelegate(_p->_hoveredAxis, pos, btn, *this);

    return (_p->_hoveredAxis != Axis::A_Null);
}

void WDEditSingleAxisMoveRotate::update(WDContext& context)
{
    if ((_p->_previewHoveredAxis == A_Null) && constraintEnabled() && _p->constraint.valid() && _p->getHType() == HT_None)
    {
        auto consLine = constraint().bWorldLineIsConstraint(_transform.extractTranslation(), _p->nodeThick);
        if (consLine)
        {
            auto consDir = consLine.value().dir();
            if (consDir)
            {
                if (!WD::DVec3::OnTheSameLine(axisX(), consDir.value(), 0.01)
                    && !WD::DVec3::OnTheSameLine(axisY(), consDir.value(), 0.01)
                    && !WD::DVec3::OnTheSameLine(axisZ(), consDir.value(), 0.01))
                {
                    consLine = std::nullopt;
                }
            }
        }
        _p->_originLine = consLine;
        _p->_currentLine = _p->_originLine;
    }
    _p->update(context);
}
void WDEditSingleAxisMoveRotate::render(WDContext& context)
{
    if (!_internalFlags.hasFlag(InteralFlag_Visible))
        return;
    _p->render(context);
}

WD_NAMESPACE_END
