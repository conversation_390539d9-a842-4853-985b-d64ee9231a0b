#pragma once
/**
* @file WDEditAxisMove.h
* @brief 定义移动编辑坐标轴
* <AUTHOR>
* @date xxxx-xx-xx
*/
#include "WDAxis.h"
#include "node/WDNode.h"
WD_NAMESPACE_BEGIN

class WDEditSingleAxisMoveRotatePrivate;

/**
 * @brief 单轴移动旋转编辑轴
 */
class WD_API WDEditSingleAxisMoveRotate: public WDEditAxis
{
public:
    struct Constraint
    {
    public:
        // 一条约束线
        struct ConstraintLine
        {
            DVec3 posStart;
            DVec3 posEnd;
            DVec3 result;
            WD::WDNode::WeakPtr prevNode;
            std::optional<double> maxErrorRange;
        public:
            const std::optional<DVec3> dir() const
            {
                if (posStart == posEnd)
                {
                    assert(false);
                    return std::nullopt;
                }
                return DVec3::Normalize(posEnd - posStart);
            }
        public:
            bool operator==(const ConstraintLine& right)const
            {
                if (WD::DVec3::Distance(this->posStart, right.posStart) < 0.1
                    && WD::DVec3::Distance(this->posEnd, right.posEnd) < 0.1)
                    return true;
                return false;
            }
            bool operator!=(const ConstraintLine& right)const
            {
                return !(*this == right);
            }
        };
        std::vector<ConstraintLine> constraintLines;
    public:
        /**
        * @brief 判断一个世界坐标点是否是在约束范围内
        * 注意,本约束类中保存的约束信息坐标都是世界坐标
        * @return 点在约束范围内的位置(约束会允许一定范围的误差),值为空时说明点不在约束范围内
        */
        std::optional<ConstraintLine> bWorldPointIsConstraint(const DVec3& point
            , double errorRange = 1.0) const;
        /**
        * @brief 判断一个屏幕点是否在约束范围内
        * @param trsFunc 由于保存的数据是世界坐标,所以如果要判断屏幕点的话要提供转换函数
        * @return 点在约束范围内的位置(约束会允许一定范围的误差),值为空时说明点不在约束范围内,注意,返回值为世界坐标
        */
        std::optional<ConstraintLine> bScreenPointIsConstraint(const DVec2& point
            , std::function<DVec2(const DVec3&)> trsFunc
            , std::function<double(const double)> errorTrsFunc
            , double errorRange = 1.0) const;
        /**
        * @brief 判断一个世界坐标线段是否是在约束范围内
        * 注意,本约束类中保存的约束信息坐标都是世界坐标
        * @return 点在约束范围内的位置(约束会允许一定范围的误差),值为空时说明点不在约束范围内
        */
        std::optional<ConstraintLine> bWorldLineIsConstraint(const DVec3& pointS
            , const DVec3& pointE
            , double errorRange = 1.0) const;
        /**
         * @brief 判断一个世界坐标线段是否是在约束范围内
         * @param center 线段中点
         * @param length 线长
        */
        std::optional<ConstraintLine> bWorldLineIsConstraint(const DVec3& center
            , const double& length
            , double errorRange = 1.0) const;
        /**
        * @brief 判断一个世界坐标线段是否是在约束范围内
        * 注意,本约束类中保存的约束信息坐标都是世界坐标
        * @return 点在约束范围内的位置(约束会允许一定范围的误差),值为空时说明点不在约束范围内
        */
        std::optional<ConstraintLine> bScreenLineIsConstraint(const DVec2& pointS
            , const DVec2& pointE
            , std::function<DVec2(const DVec3&)> trsFunc
            , std::function<double(const double)> errorTrsFunc
            , double errorRange = 1.0) const;
        /**
        * @brief 判断一个世界坐标线段是否是在约束范围内
        * @param center 线段中点
        * @param length 线长
        */
        std::optional<ConstraintLine> bScreenLineIsConstraint(const DVec2& center
            , const double& length
            , std::function<DVec2(const DVec3&)> trsFunc
            , std::function<double(const double)> errorTrsFunc
            , double errorRange = 1.0) const;
    public:
        inline bool valid() const
        {
            return !constraintLines.empty();
        }

    };
public:
    /**
    * @brief 轴标志
    */
    enum Axis
    {
        // 无
        A_Null = 0,
        // 移动轴
        A_MAxis,
        // 旋转轴
        A_RAxis,
    };
    /**
    * @brief 操作类型
    */
    enum HandleType 
    {
        // 无
        HT_None = 0,
        // 移动操作
        HT_Move,
        // 旋转操作
        HT_Rotate,
    };
    /**
    * @brief 选中轴的部分
    */
    enum AxisPart
    {
        // 无
        AP_None = 0,
        // 沿轴正向移动的部分
        AP_PDPart,
        // 沿轴反向移动的部分
        AP_NDPart,
    };
    /**
    * @brief 移动/旋转通知
    * @param status 编辑状态
    * @param hType 操作类型, 用于判断是移动操作还是旋转操作
    * @param relativeOffset 相对偏移量(世界坐标)
    *   - 如果是移动操作, relativeOffset.xyz()代表移动方向, relativeOffset.w 代表在这个方向上的移动距离
    *   - 如果是旋转操作, relativeOffset.xyz()代表旋转轴, relativeOffset.w 代表绕着这个轴旋转的角度
    *   - 当EditStatus为 EditStart时: 该参数取值总是 DVec4(0)
    *   - 当EditStatus为 Editing时: 该参数取值为前一次鼠标位置与当前鼠标位置对应的世界坐标移动偏移量
    *   - 当EditStatus为 EditEnd时:  该参数取值总是 DVec4(0) 
    * @param absoluteOffset 绝对偏移量(世界坐标)
    *   - 如果是移动操作, absoluteOffset.xyz()代表移动方向, absoluteOffset.w 代表在这个方向上的移动距离
    *   - 如果是旋转操作, absoluteOffset.xyz()代表旋转轴, absoluteOffset.w 代表绕着这个轴旋转的角度
    *   - 当EditStatus为 EditStart时: 该参数取值总是 DVec4(0)
    *   - 当EditStatus为 Editing时: 该参数取值为从鼠标按下开始到当前鼠标的所有移动偏移量的累加
    *   - 当EditStatus为 EditEnd时:  该参数取值为从鼠标按下开始到鼠标结束后所有移动偏移量的累加
    * @param sender 调用通知的轴对象
    */
    using MDelegate = WDTMultiDelegate<void(EditStatus status
        , HandleType hType
        , const DVec4& relativeMOffset
        , const DVec4& absoluteMOffset
        , const DVec4& relativeROffset
        , const DVec4& absoluteROffset
        , const DVec3& rotateCenter
        , WDEditSingleAxisMoveRotate& sender)>;
    /**
     * @brief 右键菜单回调
     * @param axis 轴标志 @see ref Axis
     * @param screenPos 鼠标按下的屏幕坐标位置
    * @param sender 调用通知的轴对象
    */
    using MContextMenuDelegate = WDTMultiDelegate<void(int axis
        , const IVec2& screenPos
        , WDEditSingleAxisMoveRotate& sender)>;
    /**
     * @brief 鼠标点击通知
     * @param axis 轴标志 @see ref Axis
     * @param screenPos 鼠标按下的屏幕坐标位置
     * @param btn 鼠标按键
     * @param sender 调用通知的轴对象
    */
    using MClickedDelegate = WDTMultiDelegate<void(int axis
        , const IVec2& screenPos
        , WDEditAxis::MouseButton btn
        , WDEditSingleAxisMoveRotate& sender)>;
    /**
     * @brief 鼠标双击通知
     * @param axis 轴标志 @see ref Axis
     * @param screenPos 鼠标按下的屏幕坐标位置
     * @param btn 鼠标按键
     * @param sender 调用通知的轴对象
    */
    using MDBClickedDelegate = WDTMultiDelegate<void(int axis
        , const IVec2& screenPos
        , WDEditAxis::MouseButton btn
        , WDEditSingleAxisMoveRotate& sender)>;
private:
    WDEditSingleAxisMoveRotatePrivate* _p;
    friend class WDEditSingleAxisMoveRotatePrivate;
public:
    WDEditSingleAxisMoveRotate();
    ~WDEditSingleAxisMoveRotate();
public:
    /**
    * @brief 移动操作回调,当轴发生移动操作时，触发该回调
    */
    MDelegate& mDelegate();
    /**
     * @brief 右键菜单回调
    */
    MContextMenuDelegate& mContextMenuDelegate();
    /**
     * @brief 鼠标点击回调
    */
    MClickedDelegate& mClickedDelegate();
    /**
     * @brief 鼠标双击回调
    */
    MDBClickedDelegate& mDBClickedDelegate();
    /**
     * @brief 设置操作对象厚度,用以计算约束 值不能小于等于0
    */
    void setObjectThickness(const double& thickness);
    /**
    * @brief 获取操作对象厚度
    */
    const double& objectThickness() const;
    /**
    * @brief 设置移动步长(距离)
    */
    void setStepLength(double step);
    /**
    * @brief 获取移动步长(距离)
    */
    double stepLength() const;
    /**
    * @brief 设置旋转步长(角度)
    */
    void setStepAngle(double step);
    /**
    * @brief 获取旋转步长(角度)
    */
    double stepAngle() const;
    /**
    * @brief 执行一次移动或旋转命令, 当前轴对象将触发通知移动命令
    * @param axis 确定移动轴向的轴
    * @param offset 在该轴向上的移动偏移量或旋转角度
    *  如果指定的是移动轴，则offset代表移动的距离
    *  如果指定的是旋转轴，则offset代表旋转的角度
    */
    void exec(Axis axis, double offset);
public:
    /**
    * @brief 获取当前已经被高亮的轴
    * @return 返回高亮的轴索引
    */
    Axis hoveredAxis() const;
    /**
    * @brief 获取当前已经被选中的轴
    * @return 返回选中的轴索引
    */
    Axis selectedAxis() const;
public:
    /**
    * @brief 预览对齐
    * @param axis 指定对齐轴
    */
    void    beginPreviewAlign(Axis axis);
    /**
    * @brief 计算对齐偏移量
    * @param vec 对齐向
    *  如果是位置对齐，则为坐标
    *  如果是朝向对齐，则为方向向量
    *  如果为std::nullopt，表示将对齐偏移量置为0
    * @return 对齐偏移量
    *  如果是位置对齐 .xyz()表示方向， w表示该方向的偏移距离
    *  如果是朝向对齐 .xyz()表示旋转轴, w表示旋转角度
    */
    DVec4   calcPreviewAlign(const std::optional<DVec3>& vec = std::nullopt);
    /**
    * @brief 结束预览对齐
    */
    void    endPreviewAlign();
public:
    /**
     * @brief 获取约束
    */
    const Constraint& constraint() const;
    /**
    * @brief 设置约束
    */
    void setConstraint(const Constraint& constraint);
    /**
    * @brief 设置约束是否生效
    */
    void setConstraintEnabled(bool bEnabled);
    /**
    * @brief 约束是否生效
    */
    const bool& constraintEnabled() const;

    std::optional<Constraint::ConstraintLine> currentConstraintLine();
public:
    /**
    * @brief 是否有轴被高亮
    * @return 如果有轴被高亮，则返回true,否则返回false
    */
    virtual bool isAxisHovered() const override;
    /**
    * @brief 是否有轴被选中
    * @return 如果有轴被选中,则返回true,否则返回false
    */
    virtual bool isAxisSelected() const override;
    /**
    * @brief 取消轴的高亮状态
    */
    virtual void cancelHovered() override;
    /**
    * @brief 取消轴的选中状态
    */
    virtual void cancelSelected()override;
public:
    virtual bool mouseButtonPress(WDContext& context, const IVec2& pos, MouseButton btn) override;
    virtual bool mouseButtonRelease(WDContext& context, const IVec2& pos, MouseButton btn) override;
    virtual bool mouseMove(WDContext& context, const IVec2& pos) override;
    virtual bool mouseDoubleClicked(WDContext& context, const IVec2& pos, MouseButton btn) override;

    virtual void update(WDContext& context) override;
    virtual void render(WDContext& context) override;
protected:
    /**
    * @brief 判断某个值是否是X轴的枚举值,子类实现
    */
    virtual bool isEnumAxisX(int) const override
    {
        return false;
    }
    /**
    * @brief 判断某个值是否是X轴的枚举值,子类实现
    */
    virtual bool isEnumAxisY(int) const override
    {
        return false;
    }
    /**
    * @brief 判断某个值是否是X轴的枚举值,子类实现
    */
    virtual bool isEnumAxisZ(int) const override
    {
        return false;
    }
};


using WDSingleAxisMRConstraint = WDEditSingleAxisMoveRotate::Constraint;
WD_NAMESPACE_END


