
#include "WDSceneNodeRenderDefault.h"
#include "../../WDCore.h"
#include "../../material/WDDrawHelpter.h"

#include "../../common/WDRenderLayer.h"
#include "../../graphable/WDGraphableInterface.h"
#include "../../viewer/primitiveRender/WDTextRender.h"
#include "../../viewer/primitiveRender/WDLineRender.h"

#include "../../geometry/standardPrimitives/WDGeometryCone.h"
#include "../../geometry/standardPrimitives/WDGeometryCylinder.h"

#include "../../selections/WDNodeSelection.h"

#include "../../nodeTree/WDNodeTree.h"

#include "../../WDBlockingTask.h"
#include "../../common/WDTimestamp.hpp"
#include "../../log/WDLoggerPort.h"

#include "../../businessModule/WDBDBase.h"
#include "../../businessModule/design/WDBMDesign.h"
#include "../../businessModule/WDBMColorIndexMgr.h"

#include "../../viewer/WDViewer.h"

#include <chrono>

WD_NAMESPACE_BEGIN

// 透明度判定阈值
static constexpr const byte AlphaMinB = 5;
static constexpr const byte AlphaMaxB = 250;
// 判断alpha通道是否为半透明
static bool IsSubTransparent(byte alpha)
{
    return alpha > AlphaMinB && alpha < AlphaMaxB;
}
// 判断alpha通道是否为全透明
static bool IsTransparent(byte alpha)
{
    return alpha <= AlphaMinB;
}

InstanceAttrs GetInstAttr(const WDNode& node, bool bGlobalClip)
{
    InstanceAttrs rAttrs = InstanceAttr::IA_None;

    rAttrs.setFlag(IA_Clip, node.flags().hasFlag(WDNode::Flag::F_Clip) || bGlobalClip);
    rAttrs.setFlag(IA_Selected, node.flags().hasFlag(WDNode::Flag::F_Selected));
    rAttrs.setFlag(IA_ShadowCast, node.flags().hasFlag(WDNode::Flag::F_ShadowCast));
    rAttrs.setFlag(IA_ShadowRecv, node.flags().hasFlag(WDNode::Flag::F_ShadowRecv));
    rAttrs.setFlag(IA_MirrowCast, node.flags().hasFlag(WDNode::Flag::F_MirrowCast));
    rAttrs.setFlag(IA_MirrowRecv, node.flags().hasFlag(WDNode::Flag::F_MirrowRecv));
    rAttrs.setFlag(IA_Bloom, node.flags().hasFlag(WDNode::Flag::F_Bloom));
    rAttrs.setFlag(IA_Mirror, node.flags().hasFlag(WDNode::Flag::F_Mirror));

    return rAttrs;
}


// 绘制节点的点集
class KeyPointsRender
{
private:
    //
    WDSceneNodeRenderDefault& _d;
    // 关键点 map
    std::unordered_map<const WDKeyPoints*, const WDNode*> _map;

    // 是否绘制点集
    bool _bRender = false;
    // 是否绘制点集文本
    bool _bRenderText = false;
    // 是否只绘制具有选中标志节点的点集
    bool _bRenderOnlySelected = false;
    // 绘制关键点线段部分的长度,箭头的尺寸根据该长度自动变化
    float _lineLen = 30.0f;

    // 绘制关键点文本
    WDText2DRender  _textRender;
    // 绘制点集箭头的instance列表
    WDInstances _insts;

    // 箭头直线部分
    WDMesh _meshArrowLine;
    // 绘制箭头直线的材质
    WDMaterialColor _materialArrowLine;
    // 箭头圆锥部分
    WDMesh _meshArrowCone;
    // 绘制箭头圆锥的材质
    WDMaterialPhong _materialArrowCone;

    // 是否需要更新
    bool _bNeedUpdate = false;

    // 文本查找表
    std::wstring _numberZero = L"0";
    std::map<int, std::wstring> _textTable;
public:
    KeyPointsRender(WDSceneNodeRenderDefault& d):_d(d)
    {
        // 点集文本绘制方式
        _textRender.setRenderType(WDText2DRender::RT_BillBoard);
        // 点集文本颜色
        _textRender.setBlendColor(Color::white);
        // 绘制箭头圆锥的材质
        _materialArrowCone.setDiffuse(Color::red);
        _materialArrowCone.addStates({ WDRenderStateDepthTest::MakeShared(false)  // 禁用深度测试
            , WDRenderStateCullFace::MakeShared(true) }// 启用背面裁剪
        );
        // 绘制箭头直线的材质
        _materialArrowLine.setColor(Color::red);
        _materialArrowLine.addStates({ WDRenderStateDepthTest::MakeShared(false)  // 禁用深度测试
            , WDRenderStateCullFace::MakeShared(true) }// 启用背面裁剪
        );
        // 绘制箭头的几何体
        updateArrowVertices();

        auto& cfgItemScene      = _d.app().cfg().get<std::string>("scene").setDisplayText("Scene");
        auto& cfgItemKeyPoint   = cfgItemScene.get<std::string>("keyPoint").setDisplayText("Point sets");
        // 是否绘制点集对象
        cfgItemKeyPoint.get<bool>("keyPoint.visible", _bRender)
            .setDisplayText("Whether to draw point sets")
            .bindFunction({ this, &KeyPointsRender::onRender });
        // 是否绘制点集文本
        cfgItemKeyPoint.get<bool>("keyPoint.text.visible", _bRenderText)
            .setDisplayText("Whether to draw point sets text")
            .bindFunction({ this, &KeyPointsRender::onRenderText });
        // 点集颜色
        cfgItemKeyPoint.get<Color>("keyPoint.color", _materialArrowLine.color())
            .setDisplayText("The color of point sets")
            .bindFunction({ this, &KeyPointsRender::onColor });
        // 点集文本颜色
        cfgItemKeyPoint.get<Color>("keyPoint.text.color", _textRender.blendColor())
            .setDisplayText("The color of point sets text")
            .bindFunction({ this, &KeyPointsRender::onTextColor });
        // 点集对象箭头线长度
        cfgItemKeyPoint.get<float>("keyPoint.lineLength", _lineLen)
            .setDisplayText("Point set axis length")
            .bindFunction({ this, &KeyPointsRender::onLineLength })
            .setMinimum(1.0f)
            .setMaximum(1000000.0f);
    }
    ~KeyPointsRender()
    {

    }
public:
    bool add(const WDKeyPoints* pKeyPoints, const WDNode* pNode)
    {
        if (pNode == nullptr || pKeyPoints == nullptr || pKeyPoints->empty())
            return false;

        auto  fItr = _map.find(pKeyPoints);
        if (fItr == _map.end())
        {
            _map.emplace(pKeyPoints, pNode);
        }
        else
        {
            fItr->second = pNode;
        }

        _bNeedUpdate = true;
        return true;
    }
    bool remove(const WDKeyPoints* pKeyPoints, const WDNode* pNode)
    {
        WDUnused(pNode);
        if (pKeyPoints == nullptr)
            return false;

        auto  fItr = _map.find(pKeyPoints);
        if (fItr == _map.end())
            return false;

        assert(fItr->second == pNode);
        _map.erase(fItr);

        _bNeedUpdate = true;
        return true;
    }
    void clear()
    {
        _map.clear();
        _bNeedUpdate = true;

        // 清除时，释放所有缓存
        _textTable.clear();
        // 这里的目的是彻底释放std::vector中分配的内存
        WDInstances().swap(_insts);
    }
    bool needUpdate() const
    {
        return _bNeedUpdate;
    }
    void setNeedUpdate()
    {
        _bNeedUpdate = true;
    }

    const std::unordered_map<const WDKeyPoints*, const WDNode*>& map() const
    {
        return _map;
    }
    bool emptyRenderData() const
    {
        return _textRender.empty() && _insts.empty();
    }
public:
    void onUpdate(WDContext& context)
    {
        WDUnused(context);
        if (!_bRender)
            return;
        // 检查是否需要更新
        if (!_bNeedUpdate)
            return;
        _bNeedUpdate = false;

        _textRender.reset();
        _insts.clear();


        // 是否全局剖切
        const auto& clipFlags = _d.app().viewer().clip().flags();
        bool bGlobalClip = false;
        if (clipFlags.hasFlag(WDClip::F_Enabled) && !clipFlags.hasFlag(WDClip::F_Local))
            bGlobalClip = true;

        size_t nCount = 0;
        // 收集要绘制的关键点的数量
        for (auto& var : _map)
        {
            auto pKps   = var.first;
            if (pKps == nullptr || pKps->empty())
                continue;
            auto pNode  = var.second;
            if (pNode == nullptr)
                continue;
            // 不可见
            if (!pNode->flags().hasFlag(WDNode::F_Visible))
                continue;
            // 是否只绘制具有选中标志节点的关键点
            if (_bRenderOnlySelected && !pNode->flags().hasFlag(WDNode::F_Selected))
                continue;
            // P0点 + P1~PN点
            nCount += pKps->size() + 1;
        }

        _insts.reserve(nCount);

        // 一个关键点的绘制实例对象
        WDInstance inst;
        inst._color = Color::white;
        wchar_t buf[64] = { 0 };
        for (auto& var : _map)
        {
            auto pKps   = var.first;
            if (pKps == nullptr || pKps->empty())
                continue;
            auto pNode  = var.second;
            if (pNode == nullptr)
                continue;
            // 不可见
            if (!pNode->flags().hasFlag(WDNode::F_Visible))
                continue;
            // 只绘制具有选中标志的关键点
            if (_bRenderOnlySelected && !pNode->flags().hasFlag(WDNode::F_Selected))
                continue;
            const auto& gMat = pNode->globalTransform();
            DVec3 gPos = pNode->globalTranslation();
            DMat3 gRMat = DMat3::FromQuat(pNode->globalRotation());
            // 实例属性
            auto instAttr = GetInstAttr(*pNode, bGlobalClip);
            inst._instanceAttr = instAttr.data();
            // P0点
            {
                DMat4 tMat = DMat4::Compose(gMat * DVec3::Zero(), DMat3::ToQuat(gRMat));

                inst._local = FMat4(tMat);

                _insts.emplace_back(inst);
                // 文本
                if (_bRenderText)
                {
                    _textRender.add(_numberZero
                        , FVec3(gPos)
                        , Color::white
                        , 12
                        , WD::WDTextRender::HA_Left
                        , WD::WDTextRender::VA_Top
                        , FVec2(0.0f)
                        , 0.0f
                        , instAttr);
                }
            }
            // P1~PN点
            for (const auto& kp : (*pKps))
            {
                const DVec3& dir = kp.direction;
                const DVec3& pos = kp.position;

                DQuat tRot = DQuat::FromVectors(DVec3::AxisZ(), dir);
                DMat4 tMat = DMat4::Compose(gMat * pos, DMat3::ToQuat(gRMat * DMat3::FromQuat(tRot)));

                inst._local = FMat4(tMat);

                _insts.emplace_back(inst);
                // 文本
                if (_bRenderText)
                {
                    // 关键点编号
                    int number = kp.numb();
                    // 关键点的世界坐标位置
                    DVec3 kGPos = gMat * pos;
                    // 添加文本对象
                    auto pText = this->getText(number, buf);
                    if (pText != nullptr)
                    {
                        _textRender.add(*pText
                            , FVec3(kGPos)
                            , Color::white
                            , 12
                            , WD::WDTextRender::HA_Left
                            , WD::WDTextRender::VA_Top
                            , FVec2(0.0f)
                            , 0.0f
                            , instAttr);
                    }
                }
            }
        }
    }
    void onRender(WDContext& context)
    {
        if (!_bRender)
            return;

        // 点集箭头
        if (!_insts.empty())
        {
            // 箭头线段部分
            {
                WDDrawHelpter::Guard dg(context, _materialArrowLine);
                dg.drawInstance(_insts, _meshArrowLine, WDMesh::Solid);
            }
            // 箭头锥部
            {
                WDDrawHelpter::Guard dg(context, _materialArrowCone);
                dg.drawInstance(_insts, _meshArrowCone, WDMesh::Solid);
            }
        }

        // 点集文本
        if (_bRenderText)
            _textRender.render(context, false);
    }
private:
    // 获取文本
    const std::wstring* getText(int number, wchar_t* buf)
    {
        // 从编号文本的查找表中查找文本对象
        auto fItr = _textTable.find(number);
        if (fItr != _textTable.end())
        {
            return &(fItr->second);
        }
        else
        {
            // 未查找到，则转换此编号到文本，并缓存到查找表中
            swprintf(buf, sizeof(buf) / sizeof(wchar_t), L"%d", number);
            auto rItr = _textTable.emplace(number, buf);
            assert(rItr.second);
            if (rItr.second)
                return &(rItr.first->second);
            else
                return nullptr;
        }
    }
    // 根据箭头直线长度，更新几何体顶点
    void updateArrowVertices()
    {
        if (_lineLen < 1.0f)
            return;
        // 圆锥箭头
        {
            _meshArrowCone.positions().clear();
            _meshArrowCone.normals().clear();
            _meshArrowCone.clearPrimitiveSet(WDMesh::Solid);
            const float coneBottomDiam = _lineLen * 0.26f;
            const float coneHeight = _lineLen * 0.45f;
            WDGeometryCone tCone(0.0f, coneBottomDiam, coneHeight);
            if (tCone.mesh() != nullptr)
            {
                _meshArrowCone.setPositions(tCone.mesh()->positions());
                _meshArrowCone.setNormals(tCone.mesh()->normals());
                const auto& pris = tCone.mesh()->primitiveSets(WDMesh::Solid);
                for (const auto& pri : pris)
                {
                    _meshArrowCone.addPrimitiveSet(pri, WDMesh::Solid);
                }
                const float zOffset = _lineLen + coneHeight * 0.5f;
                for (auto& pt : _meshArrowCone.positions())
                {
                    pt = FMat4(tCone.transform()) * pt;
                    // 将圆锥的顶点位置向上抬升
                    pt.z += zOffset;
                }
            }
        }
        // 圆锥直线部分
        {
            _meshArrowLine.positions().clear();
            _meshArrowLine.clearPrimitiveSet(WDMesh::Solid);

            _meshArrowLine.setPositions({ FVec3::Zero(), FVec3(0.0f, 0.0f, _lineLen) });
            _meshArrowLine.addPrimitiveSet(WDPrimitiveSet::FromData(0, 2, WDPrimitiveSet::PrimitiveType::PT_Lines)
                , WDMesh::Solid);
        }
    }
private:
    void onRender(const WDConfig::Item& item)
    {
        auto p = item.value<bool>();
        if (p == nullptr)
            return;
        if (_bRender == *p)
            return;
        _bRender = *p;
        _bNeedUpdate = true;
    }
    void onRenderText(const WDConfig::Item& item)
    {
        auto p = item.value<bool>();
        if (p == nullptr)
            return;
        if (_bRenderText == *p)
            return ;
        _bRenderText = *p;
        _bNeedUpdate = true;
    }
    void onColor(const WDConfig::Item& item)
    {
        auto p = item.value<Color>();
        if (p == nullptr)
            return;
        _materialArrowCone.setDiffuse(*p);
        _materialArrowLine.setColor(*p);
    }
    void onLineLength(const WDConfig::Item& item)
    {
        auto p = item.value<float>();
        if (p == nullptr)
            return;
        _lineLen = *p;
        updateArrowVertices();
    }
    void onTextColor(const WDConfig::Item& item)
    {
        auto p = item.value<Color>();
        if (p == nullptr)
            return;
        _textRender.setBlendColor(*p);
    }
};

// 绘制节点的PLine集
class PLinesRender
{
private:
    //
    WDSceneNodeRenderDefault& _d;
    // PLine Map
    std::unordered_map<const WDPLines*, const WDNode*> _map;

    // 是否绘制PLine
    bool _bRender = false;
    // 是否绘制PLine文本
    bool _bRenderText = false;
    // 是否只绘制具有选中标志节点的点集
    bool _bRenderOnlySelected = false;
    // 绘制PLine箭头线段部分的长度,箭头的尺寸根据该长度自动变化
    float _lineLen = 30.0f;

    // 绘制PLine文本
    WDText2DRender _textRender;
    // 绘制PLine的instance列表
    WDInstances _insts;

    // 箭头直线部分
    WDMesh _meshArrowLine;
    // 绘制箭头直线的材质
    WDMaterialColor _materialArrowLine;
    // 箭头圆锥部分
    WDMesh _meshArrowCone;
    // 绘制箭头圆锥的材质
    WDMaterialPhong _materialArrowCone;

    // 是否需要更新
    bool _bNeedUpdate = false;
    // 文本查找表
    std::map<std::string, std::wstring> _textTable;
public:
    PLinesRender(WDSceneNodeRenderDefault& d) :_d(d)
    {
        // 文本绘制方式
        _textRender.setRenderType(WDText2DRender::RT_BillBoard);
        // 文本颜色
        _textRender.setBlendColor(Color::greenYellow);
        // 绘制箭头圆锥的材质
        _materialArrowCone.setDiffuse(Color::greenYellow);
        _materialArrowCone.addStates({ WDRenderStateDepthTest::MakeShared(false)  // 禁用深度测试
            , WDRenderStateCullFace::MakeShared(true) }// 启用背面裁剪
        );
        // 绘制箭头直线的材质
        _materialArrowLine.setColor(Color::greenYellow);
        _materialArrowLine.addStates({ WDRenderStateDepthTest::MakeShared(false)  // 禁用深度测试
            , WDRenderStateCullFace::MakeShared(true) }// 启用背面裁剪
        );
        // 绘制箭头的几何体
        updateArrowVertices();

        auto& cfgItemScene = _d.app().cfg().get<std::string>("scene").setDisplayText("Scene");
        auto& cfgItemPLine = cfgItemScene.get<std::string>("pLine").setDisplayText("PLine");
        // 是否绘制PLine对象
        cfgItemPLine.get<bool>("pLine.visible", _bRender)
            .setDisplayText("Whether to draw PLine")
            .bindFunction({ this, &PLinesRender::onRender });
        // 是否绘制PLine文本
        cfgItemPLine.get<bool>("pLine.text.visible", _bRenderText)
            .setDisplayText("Whether to draw PLine text")
            .bindFunction({ this, &PLinesRender::onRenderText });
        // PLine颜色
        cfgItemPLine.get<Color>("pLine.color", _materialArrowLine.color())
            .setDisplayText("PLine color")
            .bindFunction({ this, &PLinesRender::onColor });
        // PLine文本颜色
        cfgItemPLine.get<Color>("pLine.text.color", _textRender.blendColor())
            .setDisplayText("PLine text color")
            .bindFunction({ this, &PLinesRender::onTextColor });
        // PLine对象箭头线长度
        cfgItemPLine.get<float>("pLine.lineLength", _lineLen)
            .setDisplayText("PLine axis length")
            .bindFunction({ this, &PLinesRender::onLineLength })
            .setMinimum(1.0f)
            .setMaximum(1000000.0f);
    }
    ~PLinesRender()
    {

    }
public:
    bool add(const WDPLines* pLines, const WDNode* pNode)
    {
        if (pNode == nullptr || pLines == nullptr || pLines->empty())
            return false;

        auto  fItr = _map.find(pLines);
        if (fItr == _map.end())
        {
            _map.emplace(pLines, pNode);
        }
        else
        {
            fItr->second = pNode;
        }
        _bNeedUpdate = true;
        return true;
    }
    bool remove(const WDPLines* pLines, const WDNode* pNode)
    {
        WDUnused(pNode);
        if (pLines == nullptr)
            return false;

        auto  fItr      = _map.find(pLines);
        if (fItr == _map.end())
            return false;

        assert(fItr->second == pNode);
        _map.erase(fItr);

        _bNeedUpdate = true;
        return true;
    }
    void clear()
    {
        _map.clear();
        _bNeedUpdate = true;

        // 清除时，释放所有缓存
        _textTable.clear();
        // 这里的目的是彻底释放std::vector中分配的内存
        WDInstances().swap(_insts);
    }
    bool needUpdate() const
    {
        return _bNeedUpdate;
    }
    void setNeedUpdate()
    {
        _bNeedUpdate = true;
    }

    const std::unordered_map<const WDPLines*, const WDNode*>& map() const
    {
        return _map;
    }
    bool emptyRenderData() const
    {
        return _textRender.empty() && _insts.empty();
    }
public:
    void onUpdate(WDContext& context)
    {
        WDUnused(context);
        if (!_bRender)
            return;
        // 检查是否需要更新
        if (!_bNeedUpdate)
            return;
        _bNeedUpdate = false;

        _textRender.reset();
        _insts.clear();
        _textTable.clear();

        // 是否全局剖切
        const auto& clipFlags = _d.app().viewer().clip().flags();
        bool bGlobalClip = false;
        if (clipFlags.hasFlag(WDClip::F_Enabled) && !clipFlags.hasFlag(WDClip::F_Local))
            bGlobalClip = true;

        size_t nCount = 0;
        // 收集要绘制的PLine的数量
        for (auto& var : _map)
        {
            auto pPls   = var.first;
            if (pPls == nullptr || pPls->empty())
                continue;
            auto pNode  = var.second;
            if (pNode == nullptr)
                continue;
            // 不可见
            if (!pNode->flags().hasFlag(WDNode::F_Visible))
                continue;
            // 是否只绘制具有选中标志节点的Plines
            if (_bRenderOnlySelected && !pNode->flags().hasFlag(WDNode::F_Selected))
                continue;

            nCount += pPls->size();
        }

        _insts.reserve(nCount);

        // 一个PLine的绘制实例对象
        WDInstance inst;
        inst._color = Color::white;
        for (auto& var : _map)
        {
            auto pPls   = var.first;
            if (pPls == nullptr || pPls->empty())
                continue;
            auto pNode  = var.second;
            if (pNode == nullptr)
                continue;
            // 不可见
            if (!pNode->flags().hasFlag(WDNode::F_Visible))
                continue;
            // 只绘制具有选中标志的关键点
            if (_bRenderOnlySelected && !pNode->flags().hasFlag(WDNode::F_Selected))
                continue;
            // 节点变换矩阵
            const auto& gMat = pNode->globalTransform();
            DMat3 gRMat = DMat3::FromQuat(pNode->globalRotation());
            // 实例属性
            auto instAttr = GetInstAttr(*pNode, bGlobalClip);
            inst._instanceAttr = instAttr.data();
            // 
            for (const auto& pl : (*pPls))
            {
                const DVec3& pos = pl.sPosition;
                const DVec3& dir = pl.sDirection;

                DQuat tRot = DQuat::FromVectors(DVec3::AxisZ(), dir);
                DMat4 tMat = DMat4::Compose(gMat * pos, DMat3::ToQuat(gRMat * DMat3::FromQuat(tRot)));

                inst._local = FMat4(tMat);
                _insts.emplace_back(inst);
                // 文本
                if (_bRenderText)
                {
                    DVec3 gPos = gMat * pos;
                    auto pText = this->getText(pl.key());
                    if (pText != nullptr)
                    {
                        _textRender.add(*pText
                            , FVec3(gPos)
                            , Color::white
                            , 12
                            , WD::WDTextRender::HA_Left
                            , WD::WDTextRender::VA_Top
                            , FVec2(0.0f)
                            , 0.0f
                            , instAttr);
                    }
                }
            }
        }
    }
    void onRender(WDContext& context)
    {
        if (!_bRender)
            return;

        // 点集箭头
        if (!_insts.empty())
        {
            // 箭头线段部分
            {
                WDDrawHelpter::Guard dg(context, _materialArrowLine);
                dg.drawInstance(_insts, _meshArrowLine, WDMesh::Solid);
            }
            // 箭头锥部
            {
                WDDrawHelpter::Guard dg(context, _materialArrowCone);
                dg.drawInstance(_insts, _meshArrowCone, WDMesh::Solid);
            }
        }

        // 点集文本
        if (_bRenderText)
            _textRender.render(context, false);
    }
private:
    // 获取文本
    const std::wstring* getText(const std::string& key)
    {
        // 从编号文本的查找表中查找文本对象
        auto fItr = _textTable.find(key);
        if (fItr != _textTable.end())
        {
            return &(fItr->second);
        }
        else
        {
            // 未查找到，则转换此文本，并缓存到查找表中
            std::wstring wKey = stringToWString(key);
            auto rItr = _textTable.emplace(key, std::move(wKey));
            assert(rItr.second);
            if (rItr.second)
                return &(rItr.first->second);
            else
                return nullptr;
        }
    }
    // 根据箭头直线长度，更新几何体顶点
    void updateArrowVertices()
    {
        if (_lineLen < 1.0f)
            return;
        // 圆锥箭头
        {
            _meshArrowCone.positions().clear();
            _meshArrowCone.normals().clear();
            _meshArrowCone.clearPrimitiveSet(WDMesh::Solid);
            const float coneBottomDiam = _lineLen * 0.26f;
            const float coneHeight = _lineLen * 0.45f;
            WDGeometryCone tCone(0.0f, coneBottomDiam, coneHeight);
            if (tCone.mesh() != nullptr)
            {
                _meshArrowCone.setPositions(tCone.mesh()->positions());
                _meshArrowCone.setNormals(tCone.mesh()->normals());
                const auto& pris = tCone.mesh()->primitiveSets(WDMesh::Solid);
                for (const auto& pri : pris)
                {
                    _meshArrowCone.addPrimitiveSet(pri, WDMesh::Solid);
                }
                const float zOffset = _lineLen + coneHeight * 0.5f;
                for (auto& pt : _meshArrowCone.positions())
                {
                    pt = FMat4(tCone.transform()) * pt;
                    // 将圆锥的顶点位置向上抬升
                    pt.z += zOffset;
                }
            }
        }
        // 圆锥直线部分
        {
            _meshArrowLine.positions().clear();
            _meshArrowLine.clearPrimitiveSet(WDMesh::Solid);

            _meshArrowLine.setPositions({ FVec3::Zero(), FVec3(0.0f, 0.0f, _lineLen) });
            _meshArrowLine.addPrimitiveSet(WDPrimitiveSet::FromData(0, 2, WDPrimitiveSet::PrimitiveType::PT_Lines)
                , WDMesh::Solid);
        }
    }
private:
    void onRender(const WDConfigItem& item)
    {
        auto p = item.value<bool>();
        if (p == nullptr)
            return ;
        if (_bRender == *p)
            return ;
        _bRender = *p;
        _bNeedUpdate = true;
    }
    void onRenderText(const WDConfigItem& item)
    {
        auto p = item.value<bool>();
        if (p == nullptr)
            return ;
        if (_bRenderText == *p)
            return ;
        _bRenderText = *p;
        _bNeedUpdate = true;
    }
    void onColor(const WDConfigItem& item)
    {
        auto p = item.value<Color>();
        if (p == nullptr)
            return;
        _materialArrowCone.setDiffuse(*p);
        _materialArrowLine.setColor(*p);
    }
    void onLineLength(const WDConfigItem& item)
    {
        auto p = item.value<float>();
        if (p == nullptr)
            return;
        _lineLen = *p;
        updateArrowVertices();
    }
    void onTextColor(const WDConfigItem& item)
    {
        auto p = item.value<Color>();
        if (p == nullptr)
            return;
        _textRender.setBlendColor(*p);
    }
};

// 绘制节点的GLines
class GLinesRender 
{
private:
    //
    WDSceneNodeRenderDefault& _d;
    bool _bNeedUpdate = false;
    std::unordered_map<const WDGraphableLines*, const WDNode*> _map;
    WDLineRender _lineRender;
public:
    GLinesRender(WDSceneNodeRenderDefault& d) :_d(d)
    {

    }
    ~GLinesRender() 
    {

    }
public:
    bool add(const WDGraphableLines* pGLines, const WDNode* pNode)
    {
        if (pNode == nullptr || pGLines == nullptr || pGLines->empty())
            return false;

        auto fItr = _map.find(pGLines);
        if (fItr == _map.end())
        {
            _map.emplace(pGLines, pNode);
        }
        else
        {
            fItr->second = pNode;
        }

        _bNeedUpdate = true;
        return true;
    }
    bool remove(const WDGraphableLines* pGLines, const WDNode* pNode)
    {
        WDUnused(pNode);
        if (pGLines == nullptr)
            return false;

        auto  fItr = _map.find(pGLines);
        if (fItr == _map.end())
            return false;

        assert(pNode == fItr->second);
        _map.erase(fItr);

        _bNeedUpdate = true;
        return true;
    }
    void clear()
    {
        _map.clear();
        _bNeedUpdate = true;
    }
    bool needUpdate() const
    {
        return _bNeedUpdate;
    }
    void setNeedUpdate()
    {
        _bNeedUpdate = true;
    }
  
    const std::unordered_map<const WDGraphableLines*, const WDNode*>& map() const
    {
        return _map;
    }
    bool emptyRenderData() const 
    {
        return _lineRender.empty();
    }
public:
    void onUpdate(WDContext& context)
    {
        WDUnused(context);
        // 检查是否需要更新
        if (!_bNeedUpdate)
            return;

        // 选中时的线参数
        WDLineRender::Param selectedLineParam;
        selectedLineParam.color = _d._selectedWireframeColor;
        selectedLineParam.lineWidth = _d._selectedLineWidth;

        _bNeedUpdate = false;

        _lineRender.reset();

        // 是否全局剖切
        const auto& clipFlags = _d.app().viewer().clip().flags();
        bool bGlobalClip = false;
        if (clipFlags.hasFlag(WDClip::F_Enabled) && !clipFlags.hasFlag(WDClip::F_Local))
            bGlobalClip = true;

        // 获取第一个点作为其他点的变换点，以保证减小绘制的坐标范围
        FVec3 transPt = FVec3::Zero();
        bool bTransPt = false;
        for (const auto& var : _map)
        {
            auto pGLines = var.first;
            if (pGLines == nullptr || pGLines->empty())
                continue;
            auto pNode = var.second;
            if (pNode == nullptr)
                continue;
            // 不可见
            if (!pNode->flags().hasFlag(WDNode::F_Visible))
                continue;
            // 
            FMat4 gMat = FMat4(pNode->globalTransform());
            const auto& gLines = (*pGLines);
            for (const auto& line : gLines)
            {
                if (line.points.empty())
                    continue;
                transPt     = gMat * line.points.front();
                bTransPt    = true;
                break;
            }
            if (bTransPt)
                break;
        }

        FVec3Vector tPts;
        WDLineRender::Param rParam;
        for (const auto& var : _map)
        {
            auto pGLines = var.first;
            if (pGLines == nullptr || pGLines->empty())
                continue;
            auto pNode = var.second;
            if (pNode == nullptr)
                continue;
            // 不可见
            if (!pNode->flags().hasFlag(WDNode::F_Visible))
                continue;
            // 
            FMat4 gMat = FMat4(pNode->globalTransform());
            // 场景中的选中标志
            bool bSelected = pNode->flags().hasFlag(WDNode::Flag::F_Selected);
            // 模型树上的选中标志
            bool bTreeSelected = pNode->flags().hasFlag(WDNode::Flag::F_TreeSelected);

            // instance attribute
            auto instAttr = GetInstAttr(*pNode, bGlobalClip);

            const auto& gLines = (*pGLines);
            for (const auto& line : gLines)
            {
                // 根据节点矩阵，变换顶点
                tPts = line.points;
                for (auto& pt : tPts)
                {
                    pt = gMat * pt - transPt;
                }
                // 根据是否选中来指定线参数
                if (bSelected)
                {
                    rParam = selectedLineParam;
                }
                else
                {
                    rParam = WDLineRender::Param(line.color, line.lineWidth, line.style);;
                    if (bTreeSelected)
                        rParam.color = _d._selectedColor;
                }
                rParam.attributes = instAttr;
                // 添加线顶点
                switch (line.vMode)
                {
                case WD::WDGraphableLine::VM_Lines:
                    {
                        _lineRender.addLineSeg(tPts, rParam);
                    }
                    break;
                case WD::WDGraphableLine::VM_Loop:
                    {
                        _lineRender.addLineLoop(tPts, rParam);
                    }
                    break;
                case WD::WDGraphableLine::VM_Strip:
                    {
                        _lineRender.addLineStrip(tPts, rParam);
                    }
                    break;
                default:
                    break;
                }
            }
        }

        // 设置变换矩阵
        _lineRender.setTransform(FMat4::MakeTranslation(transPt));
    }
    void onRender(WDContext& context)
    {
        // 判断是否在视锥范围外，如果在视锥范围外，则不做绘制
        auto aabb = _lineRender.aabbTransformed();
        if (!context._frustum.intersectsAabb(aabb.min, aabb.max))
            return;

        _lineRender.render(context);
    }
};

// 绘制节点的GTexts
class GTextsRender
{
private:
    //
    WDSceneNodeRenderDefault& _d;
    bool _bNeedUpdate = false;
    std::unordered_map<const WDGraphableTexts*, const WDNode*> _map;
    WDText3DRender _textRender;
public:
    GTextsRender(WDSceneNodeRenderDefault& d) :_d(d)
    {

    }
    ~GTextsRender()
    {
    }
public:
    bool add(const WDGraphableTexts* pGTexts, const WDNode* pNode)
    {
        if (pNode == nullptr || pGTexts == nullptr || pGTexts->empty())
            return false;

        auto fItr = _map.find(pGTexts);
        if (fItr == _map.end())
        {
            _map.emplace(pGTexts, pNode);
        }
        else
        {
            fItr->second = pNode;
        }

        _bNeedUpdate = true;
        return true;
    }
    bool remove(const WDGraphableTexts* pGTexts, const WDNode* pNode)
    {
        WDUnused(pNode);
        if (pGTexts == nullptr)
            return false;

        auto  fItr = _map.find(pGTexts);
        if (fItr == _map.end())
            return false;

        assert(pNode == fItr->second);
        _map.erase(fItr);

        _bNeedUpdate = true;
        return true;
    }
    void clear()
    {
        _map.clear();
        _bNeedUpdate = true;
    }
    bool needUpdate() const
    {
        return _bNeedUpdate;
    }
    void setNeedUpdate()
    {
        _bNeedUpdate = true;
    }

    const std::unordered_map<const WDGraphableTexts*, const WDNode*>& map() const
    {
        return _map;
    }
    bool emptyRenderData() const
    {
        return _textRender.empty();
    }
public:
    void onUpdate(WDContext& context)
    {
        WDUnused(context);
        // 检查是否需要更新
        if (!_bNeedUpdate)
            return;
        _bNeedUpdate = false;

        _textRender.reset();

        // 是否全局剖切
        const auto& clipFlags = _d.app().viewer().clip().flags();
        bool bGlobalClip = false;
        if (clipFlags.hasFlag(WDClip::F_Enabled) && !clipFlags.hasFlag(WDClip::F_Local))
            bGlobalClip = true;

        for (const auto& var : _map)
        {
            auto pGTexts = var.first;
            if (pGTexts == nullptr || pGTexts->empty())
                continue;
            auto pNode = var.second;
            if (pNode == nullptr)
                continue;
            // 不可见
            if (!pNode->flags().hasFlag(WDNode::F_Visible))
                continue;
            // 
            FMat4 gMat = FMat4(pNode->globalTransform());
            // instance attribute
            auto instAttrs = GetInstAttr(*pNode, bGlobalClip);

            for (const auto& text : (*pGTexts))
            {
                if (text.rightDir && text.upDir)
                {
                    _textRender.add(text.text
                        , gMat * text.position
                        , text.rightDir.value()
                        , text.upDir.value()
                        , text.color
                        , text.fontSize
                        , (WDTextRender::HAlign)(text.hAlign)
                        , (WDTextRender::VAlign)(text.vAlign)
                        , text.spacing
                        , text.fixedPixel
                        , std::vector<int>()
                        , std::vector<int>()
                        , instAttrs);
                }
                else
                {
                    _textRender.addFixedDirection(text.text
                        , gMat * text.position
                        , text.color
                        , text.fontSize
                        , (WDTextRender::HAlign)(text.hAlign)
                        , (WDTextRender::VAlign)(text.vAlign)
                        , text.spacing
                        , text.fixedPixel
                        , std::vector<int>()
                        , std::vector<int>()
                        , instAttrs);
                }
            }
        }
    }
    void onRender(WDContext& context)
    {
        _textRender.render(context);
    }
};

class GMeshRender 
{
private:
    //
    WDSceneNodeRenderDefault& _d;
    // 是否需要更新
    bool _bNeedUpdate = false;

    // 白色实体材质，用于未指定材质的节点或者使用Instance指定颜色时的对象绘制
    WDMaterialPhong _whiteSolidMat;
    // 白色线框材质，用于未指定材质的节点或者使用Instance指定颜色时的对象绘制
    WDMaterialColor _whiteWireframeMat;

    // 选中时的线宽绘制状态
    WDRenderStateLineWidth::SharedPtr _pRStateLineWidth;
    // 虚线模式
    WDRenderStateLineStipple::SharedPtr _pRStateLineStipple;
    // 节点 Set
    using NodeUSet = std::unordered_set<const WDNode*>;
    // 使用一个几何体的所有节点
    using GeomUMap = std::unordered_map<const WDGeometry*, NodeUSet>;
    // 用于标识数组下标存放的几何体的类型
    enum AGType
    {
        // 正几何体
        AGT_Geom = 0,
        // 负几何体
        AGT_NGeom,
        // 保温几何体
        AGT_Insu,
        // 硬碰撞或软碰撞几何体
        AGT_CObst,
        // 类型计数
        AGT_Count,

        // 无效
        AGT_Invalid = NumLimits<int>::Max,
    };
    // 这里使用一个数组存放不同类型的几何体, 其中:
    //  下标[0]存放: 正几何体
    //  下标[1]存放: 负几何体
    //  下标[2]存放: 保温几何体
    //  下标[3]存放: 软碰撞几何体
    using GeomUMaps = std::array<GeomUMap, AGT_Count>;
    // 一个Mesh对应的所有几何体 
    using MeshUMap  = std::unordered_map<const WDMesh*, GeomUMaps>;
    //添加的几何体Mesh列表
    MeshUMap _map;
    /**
     * @brief mesh 对应的实例列表
    */
    struct MeshInsts
    {
        // Mesh对象
        const WDMesh* pMesh = nullptr;
        // 需要绘制的实例列表
        WDInstances insts;
    public:
        bool empty()
        {
            return insts.empty();
        }
    };
    using MeshsInsts = std::vector<MeshInsts>;
    /**
     * @brief mesh对应的实例以及包围盒到相机的距离(用于简单的透明排序)
    */
    struct MeshTransparentInsts
    {
        // 距离
        double distance = WD::NumLimits<double>::Max;
        // 使用节点和几何体矩阵变换后的Mesh包围盒, 目前是使用包围盒中心点粗略排序
        DAabb3 gMeshAabb = DAabb3::Null();
        // 需要绘制半透明实体的实例
        WDInstance solidInst;
        // Mesh对象
        const WDMesh* pMesh = nullptr;
    };
    using MeshsTransparentInsts = std::vector<MeshTransparentInsts>;

    // 实体(包含:选中、自定义高亮、激活、高亮或无材质(节点的material为nullptr)时的实例列表(不包含半透明)
    MeshsInsts _solidInsts;
    // 负实体(使用负实体专用的线框绘制)
    MeshsInsts _nSolidInsts;
    // 选中线框(使用选中状态的颜色和线宽绘制)
    MeshsInsts _selectedWireframeInsts;
    // 非选中线框(使用节点的基本色和常规线宽绘制)
    MeshsInsts _normalWireframeInsts;
    // 具有半透明的实例列表
    MeshsTransparentInsts _transparentInsts;

    // 这里存放一个类型对应的颜色属性描述索引表，为了渲染加速
    std::array<const WDBMAttrDesc*, 2000> _typeColorAttrDesc;
    //std::unordered_map<const WDBMTypeDesc*, const WDBMAttrDesc*> _typeColorAttrDescMap;
public:
    GMeshRender(WDSceneNodeRenderDefault& d) :_d(d)
    {
        // 白色实体材质，用于未指定材质的节点或者使用Instance指定颜色时的对象绘制
        _whiteSolidMat.setDiffuse(Color::white);
        // 白色线框材质，用于未指定材质的节点或者使用Instance指定颜色时的对象绘制
        _whiteWireframeMat.setColor(Color::white);
        _pRStateLineWidth   = WDRenderStateLineWidth::MakeShared(1.0f);
        _whiteWireframeMat.addState(_pRStateLineWidth);
        _pRStateLineStipple = WDRenderStateLineStipple::Get(WDRenderStateLineStipple::Style::SolidLine);
        _whiteWireframeMat.addState(_pRStateLineStipple);

        std::fill(_typeColorAttrDesc.begin(), _typeColorAttrDesc.end(), nullptr);
    }
    ~GMeshRender()
    {
    }
public:
    bool add(const WDGeometries* pGeoms, const WDNode* pNode)
    {
        if (pNode == nullptr || pGeoms == nullptr || pGeoms->empty())
            return false;

        for (auto pGeom : (*pGeoms)) 
        {
            if (pGeom == nullptr)
                continue;

            AGType aType = GetAGType(*pGeom);
            if (aType == AGT_Invalid)
                continue;

            if(this->addNodeGeom(pNode, pGeom.get(), aType))
                _bNeedUpdate = true;
        }

        return _bNeedUpdate;
    }
    bool remove(const WDGeometries* pGeoms, const WDNode* pNode)
    {
        if (pNode == nullptr || pGeoms == nullptr || pGeoms->empty())
            return false;

        for (auto pGeom : (*pGeoms))
        {
            if (pGeom == nullptr)
                continue;

            AGType aType = GetAGType(*pGeom);
            if (aType == AGT_Invalid)
                continue;
            if (this->removeNodeGeom(pNode, pGeom.get(), aType))
                _bNeedUpdate = true;
        }

        // 已被全部移除, 彻底释放数组分配的内存
        if (_map.empty())
            this->freeVectorMem();

        return _bNeedUpdate;
    }
    void clear()
    {
        // 彻底释放数组分配的内存
        this->freeVectorMem();
        // 清除几何体
        this->clearNodeGeom();

        _bNeedUpdate = true;
    }
    bool needUpdate() const
    {
        return _bNeedUpdate;
    }
    void setNeedUpdate()
    {
        _bNeedUpdate = true;
    }
public:
    void onUpdate(WDContext& context, WDScene::RMode rm)
    {
        auto startTm = std::chrono::high_resolution_clock::now();
        long long updateFrameTime = 0;
        WDUnused(context);

        // 对于具有透明的物体，需要透明排序
        transparentInstsSort(context);

        // 检查是否需要更新
        if (!_bNeedUpdate)
            return;
        _bNeedUpdate = false;

        // 是否开启保温模型
        byte alphaInsu = static_cast<byte>(Clamp(_d._alphaInsu, 0.0f, 1.0f) * 255.0f);
        bool bInsuEnabled = !IsTransparent(alphaInsu);

        // 是否开启软碰撞体模型
        byte alphaObstruction = static_cast<byte>(Clamp(_d._alphaObstruction, 0.0f, 1.0f) * 255.0f);
        bool bObstructionEnabled = !IsTransparent(alphaObstruction);

        // 是否开启了孔洞
        bool bHolesDrawn = _d.app().holesDrawn().first;

        // 是否线框模式
        bool bWireframeMode = (rm == WDScene::RM_WireFrame);

        // 实体(包含:选中、自定义高亮、激活、高亮或无材质(节点的material为nullptr)时的实例列表(不包含半透明)
        _solidInsts.clear();
        // 负实体(使用负实体专用的线框绘制)
        _nSolidInsts.clear();
        // 选中线框(使用选中状态的颜色和线宽绘制)
        _selectedWireframeInsts.clear();
        // 非选中线框(使用节点的基本色和常规线宽绘制)
        _normalWireframeInsts.clear();
        // 具有半透明的实例列表
        _transparentInsts.clear();

        // 是否全局剖切
        const auto& clipFlags = _d.app().viewer().clip().flags();
        bool bGlobalClip = false;
        if (clipFlags.hasFlag(WDClip::F_Enabled) && !clipFlags.hasFlag(WDClip::F_Local))
            bGlobalClip = true;

        // 开始收集instance
        for (auto itr = _map.begin(); itr != _map.end(); ++itr)
        {
            const WDMesh* pMesh = itr->first;
            if (pMesh == nullptr)
                continue;
            // 不同类型几何体的数组
            const auto& gArray = itr->second;

            // 正几何体
            const auto& gsStd = gArray[AGT_Geom];
            // 负几何体
            const auto& gsNeg = gArray[AGT_NGeom];
            // 软碰撞几何体
            const auto& gsCObst = gArray[AGT_CObst];
            // 保温何体
            const auto& gsInsu = gArray[AGT_Insu];

            // 统计实体个数
            size_t solidCnt = gsStd.size();
            if (bObstructionEnabled)
                solidCnt += gsCObst.size();
            if (bInsuEnabled)
                solidCnt += gsInsu.size();
            // 统计负实体个数
            size_t nSolidCnt = 0;
            // 开孔时, 不会绘制负实体
            if (!bHolesDrawn) 
                nSolidCnt = gsNeg.size();

            // 如果正负实体个数均为0，则表明当前mesh没有需要绘制的数据
            if (solidCnt == 0 && nSolidCnt == 0)
                continue;

            // 实体列表, 预分配内存
            MeshInsts solidInsts;
            solidInsts.pMesh = pMesh;
            solidInsts.insts.reserve(solidCnt);
            // 选中线框列表, 预分配内存
            MeshInsts selectedWireframeInsts;
            selectedWireframeInsts.pMesh = pMesh;
            selectedWireframeInsts.insts.reserve(solidCnt);
            // 常规线框列表, 预分配内存
            MeshInsts normalWireframeInsts;
            normalWireframeInsts.pMesh = pMesh;
            normalWireframeInsts.insts.reserve(solidCnt);
            // 负实体线框列表, 预分配内存
            MeshInsts nSolidInsts;
            // 开孔时, 不会绘制负实体
            if (!bHolesDrawn)
            {
                nSolidInsts.pMesh = pMesh;
                nSolidInsts.insts.reserve(nSolidCnt);
            }

            // 标准模型
            collectMeshInsts(context
                , pMesh
                , gsStd
                , solidInsts
                , selectedWireframeInsts
                , normalWireframeInsts
                , _transparentInsts
                , bWireframeMode
                , bGlobalClip);
            // 负实体模型, 开孔时, 不会绘制负实体
            if (!bHolesDrawn)
            {
                collectMeshInsts(gsNeg, nSolidInsts, bWireframeMode, bGlobalClip);
            }
            // 保温模型
            if (bInsuEnabled)
            {
                collectMeshInsts(context
                    , pMesh
                    , gsInsu
                    , solidInsts
                    , selectedWireframeInsts
                    , normalWireframeInsts
                    , _transparentInsts
                    , bWireframeMode
                    , &alphaInsu);
            }
            // 软碰撞体模型
            if (bObstructionEnabled)
            {
                collectMeshInsts(context
                    , pMesh
                    , gsCObst
                    , solidInsts
                    , selectedWireframeInsts
                    , normalWireframeInsts
                    , _transparentInsts
                    , bWireframeMode
                    , &alphaObstruction);
            }

            if (!solidInsts.empty())
                _solidInsts.push_back(std::move(solidInsts));

            if (!nSolidInsts.empty())
                _nSolidInsts.push_back(std::move(nSolidInsts));

            if (!selectedWireframeInsts.empty())
                _selectedWireframeInsts.push_back(std::move(selectedWireframeInsts));

            if (!normalWireframeInsts.empty())
                _normalWireframeInsts.push_back(std::move(normalWireframeInsts));
        }

        // 对于具有透明的物体，需要透明排序
        transparentInstsSort(context);

        // 计算一帧更新使用的时间
        auto endTm = std::chrono::high_resolution_clock::now();
        auto durationTm = std::chrono::duration_cast<std::chrono::milliseconds>(endTm - startTm);
        updateFrameTime = durationTm.count();
        WDUnused(updateFrameTime);
    }
    void onRender(WDContext& context)
    {
        auto startTm = std::chrono::high_resolution_clock::now();

        /*******************    1.绘制实体   ***********************/ 
        {
            WDDrawHelpter::Guard dg(context, _whiteSolidMat);
            for (const auto& mInsts : _solidInsts)
            {
                if (mInsts.insts.empty())
                    continue;
                dg.drawInstance(mInsts.insts, *(mInsts.pMesh), WDMesh::Solid);
            }
        }
        /*******************    2.绘制负实体线框   ***********************/
        {
            // 负实体线框线宽
            _pRStateLineWidth->setValue(1.0f);
            // 使用虚线绘制
            auto param = WDRenderStateLineStipple::GetParam(WDRenderStateLineStipple::DotLine);
            _pRStateLineStipple->setPattern(param.first);
            _pRStateLineStipple->setRepeat(param.second);
            {
                // 绘制
                WDDrawHelpter::Guard dg(context, _whiteWireframeMat);
                for (const auto& mInsts : _nSolidInsts)
                {
                    if (mInsts.insts.empty())
                        continue;
                    dg.drawInstance(mInsts.insts, *(mInsts.pMesh), WDMesh::WireFrame);
                }
            }
            // 还原成实线
            param = WDRenderStateLineStipple::GetParam(WDRenderStateLineStipple::SolidLine);
            _pRStateLineStipple->setPattern(param.first);
            _pRStateLineStipple->setRepeat(param.second);
        }
        /*******************    3.绘制选中线框  ***********************/
        {
            // 选中线框线宽
            _pRStateLineWidth->setValue(_d._selectedLineWidth);
            WDDrawHelpter::Guard dg(context, _whiteWireframeMat);
            for (const auto& mInsts : _selectedWireframeInsts)
            {
                if (mInsts.insts.empty())
                    continue;
                dg.drawInstance(mInsts.insts, *(mInsts.pMesh), WDMesh::WireFrame);
            }
        }
        /*******************    4.绘制常规线框  ***********************/
        {
            // 常规线框线宽
            _pRStateLineWidth->setValue(_d._normalLineWidth);
            WDDrawHelpter::Guard dg(context, _whiteWireframeMat);
            for (const auto& mInsts : _normalWireframeInsts)
            {
                if (mInsts.insts.empty())
                    continue;
                dg.drawInstance(mInsts.insts, *(mInsts.pMesh), WDMesh::WireFrame);
            }

        }
        /*******************    4.绘制半透明    ***********************/
        {
            WDDrawHelpter::Guard dg(context, _whiteSolidMat);
            for (auto& mInst : _transparentInsts)
            {
                dg.drawInstance({ mInst.solidInst }, *(mInst.pMesh), WDMesh::Solid);
            }
        }

        // 计算一帧绘制使用的时间
        auto endTm = std::chrono::high_resolution_clock::now();
        auto durationTm = std::chrono::duration_cast<std::chrono::milliseconds>(endTm - startTm);
        auto renderFrameTime = durationTm.count();
        WDUnused(renderFrameTime);
    }
private:
    static AGType GetAGType(const WDGeometry& geom) 
    {
        const auto& flags = geom.gFlags();
        // 负实体
        if (flags.hasFlag(WDGeometry::GF_Negative))
        {
            return AGT_NGeom;
        }
        // 保温几何体
        else if (flags.hasFlag(WDGeometry::GF_Insu))
        {
            // 对于保温几何体，只有带有实体/中心线标志时，才会被绘制
            if (flags.hasAnyOfFlags(WDGeometry::GF_Entity, WDGeometry::GF_CenterLine))
                return AGT_Insu;
            else
                return AGT_Invalid;
        }
        // 正实体
        else if (flags.hasFlag(WDGeometry::GF_Entity))
        {
            return AGT_Geom;
        }
        // 硬碰撞或软碰撞体
        else if (flags.hasAnyOfFlags(WDGeometry::GF_CSoft, WDGeometry::GF_CHard))
        {
            return AGT_CObst;
        }
        else
        {
            return AGT_Invalid;
        }
    }
private:
    bool addNodeGeom(const WDNode* pNode, const WDGeometry* pGeom, AGType type)
    {
        if (pNode == nullptr || pGeom == nullptr || pGeom->mesh() == nullptr)
            return false;
        WDMesh* pMesh = pGeom->mesh().get();
        auto fMeshItr = _map.find(pMesh);
        if (fMeshItr != _map.end())
        {
            GeomUMap& geoms = fMeshItr->second[type];
            auto fGeomItr = geoms.find(pGeom);
            if (fGeomItr != geoms.end())
            {
                NodeUSet& nodes = fGeomItr->second;
                auto fNodeItr = nodes.find(pNode);
                if (fNodeItr == nodes.end())
                    nodes.emplace(pNode);
                return true;
            }
            NodeUSet nodes;
            nodes.emplace(pNode);
            geoms.emplace(pGeom, std::move(nodes));

            return true;
        }
        NodeUSet nodes;
        nodes.emplace(pNode);
        GeomUMap geoms;
        geoms.emplace(pGeom, std::move(nodes));
        GeomUMaps geomsArray;
        geomsArray[type] = std::move(geoms);
        _map.emplace(pMesh, std::move(geomsArray));

        return true;
    }
    bool removeNodeGeom(const WDNode* pNode, const WDGeometry* pGeom, AGType type)
    {
        if (pNode == nullptr || pGeom == nullptr || pGeom->mesh() == nullptr)
            return false;
        WDMesh* pMesh = pGeom->mesh().get();
        auto fMeshItr = _map.find(pMesh);
        if (fMeshItr == _map.end())
            return false;
        GeomUMaps& geomsArray = fMeshItr->second;
        GeomUMap& geoms = geomsArray[type];
        auto fGeomItr = geoms.find(pGeom);
        if (fGeomItr == geoms.end())
            return false;
        NodeUSet& nodes = fGeomItr->second;
        auto fNodeItr = nodes.find(pNode);
        if (fNodeItr == nodes.end())
            return false;
        nodes.erase(fNodeItr);

        if (nodes.empty())
        {
            geoms.erase(fGeomItr);
            if (geoms.empty())
            {
                // 保证一个Mesh对应的几何体为空时，就可以移除该Mesh对象了
                bool bEmpty = true;
                for (size_t i = 0; i < geomsArray.size(); ++i)
                {
                    if (i == type)
                        continue;
                    if (!geomsArray[i].empty())
                    {
                        bEmpty = false;
                        break;
                    }
                }
                if (bEmpty)
                    _map.erase(fMeshItr);
            }
        }

        return true;
    }
    void clearNodeGeom()
    {
        _map.clear();
    }
private:
    // 彻底释放数组分配的内存
    void freeVectorMem() 
    {    
        // 实体(包含:选中、自定义高亮、激活、高亮或无材质(节点的material为nullptr)时的实例列表(不包含半透明)
        MeshsInsts().swap(_solidInsts);
        // 负实体(使用负实体专用的线框绘制)
        MeshsInsts().swap(_nSolidInsts);
        // 选中线框(使用选中状态的颜色和线宽绘制)
        MeshsInsts().swap(_selectedWireframeInsts);
        // 非选中线框(使用节点的基本色和常规线宽绘制)
        MeshsInsts().swap(_normalWireframeInsts);
        // 具有半透明的实例列表
        MeshsTransparentInsts().swap(_transparentInsts);
    }
    // 收集一个mesh的instance
    void collectMeshInsts(WDContext& context
        , const WDMesh* pMesh
        , const GeomUMap& gms
        , MeshInsts& outSolidInsts
        , MeshInsts& outSelectedWireframeInsts
        , MeshInsts& outNormalWireframeInsts
        , MeshsTransparentInsts& outTransparentInsts
        , bool bWireframeMode
        , bool bGlobalClip
        , byte* globalAlpha = nullptr)
    {
        const std::string* pNodeColorIndex = nullptr;

        for (const auto& gm : gms)
        {
            auto pGeom = gm.first;
            if (pGeom == nullptr)
                continue;
            const auto& nodes = gm.second;
            for (auto pNode : nodes)
            {
                if (pNode == nullptr)
                    continue;
                auto nodeFlag = pNode->flags();
                // 不可见
                if (!nodeFlag.hasFlag(WDNode::F_Visible))
                    continue;
                // 是否线框模式绘制
                bool bWireFrame = bWireframeMode || nodeFlag.hasFlag(WDNode::Flag::F_WireFrame);

                std::optional<Color> solidColor     = std::nullopt;
                std::optional<Color> wireframeColor = std::nullopt;
                // 是否自定义颜色高亮
                if (nodeFlag.hasFlag(WDNode::Flag::F_CustomHightlight))
                {
                    if (bWireFrame)
                        wireframeColor = pNode->customHighlightColor();
                    else
                        solidColor = pNode->customHighlightColor();
                }
                // 是否激活
                else if (nodeFlag.hasFlag(WDNode::Flag::F_Active))
                {
                    if (bWireFrame)
                        wireframeColor = _d._activedColor;
                    else
                        solidColor = _d._activedColor;
                }
                // 是否高亮
                else if (nodeFlag.hasFlag(WDNode::Flag::F_Highlight))
                {
                    if (bWireFrame)
                        wireframeColor = _d._highlightColor;
                    else
                        solidColor = _d._highlightColor;
                }
                // 使用模型颜色或基本颜色进行绘制
                else 
                {
                    // 获取模型颜色
                    auto pTmpNode = pNode;
                    bool bSetColor = false;
                    if (!context._app.getBMDesign().colorIndexMgr().getColorTable().empty())
                    {
                        while (pTmpNode != nullptr && pTmpNode->parent() != nullptr)
                        {
                            // 获取节点的类型描述
                            auto pTypeDesc = pTmpNode->getTypeDesc();
                            if (pTypeDesc == nullptr)
                            {
                                assert(false);
                                break;
                            }
                            if (pTypeDesc->id() >= _typeColorAttrDesc.size())
                            {
                                assert(false);
                                break;
                            }
                            auto& vDesc = _typeColorAttrDesc[pTypeDesc->id()];
                            if (vDesc != nullptr)
                            {
                                pNodeColorIndex = vDesc->valueCRef(*pTmpNode).toString();
                            }
                            else 
                            {
                                // 查找并缓存类型描述
                                auto pAttrDesc = pTypeDesc->get("ModelColorIndex");
                                if (pAttrDesc != nullptr)
                                {
                                    vDesc = pAttrDesc;
                                    pNodeColorIndex = pAttrDesc->valueCRef(*pTmpNode).toString();
                                }
                                else
                                {
                                    pNodeColorIndex = nullptr;
                                }
                            }
                            if (pNodeColorIndex == nullptr || pNodeColorIndex->empty())
                            {
                                pTmpNode = pTmpNode->parent().get();
                                continue;
                            }
                            auto rColor = pTmpNode->getBMBase()->colorIndexMgr().find(*pNodeColorIndex);
                            if (!rColor)
                            {
                                pTmpNode = pTmpNode->parent().get();
                                continue;
                            }

                            if (bWireFrame)
                                wireframeColor = rColor.value();
                            else
                                solidColor = rColor.value();

                            bSetColor = true;
                            break;
                        }
                    }

                    //  使用节点的常规颜色绘制
                    if (!bSetColor)
                    {
                        if (bWireFrame)
                            wireframeColor = pNode->getAttribute("AutoColor").toColor();
                        else
                            solidColor = pNode->getAttribute("AutoColor").toColor();
                    }
                }

                // 是否在树上选中
                if (nodeFlag.hasFlag(WDNode::Flag::F_TreeSelected))
                {
                    if (bWireFrame)
                    {
                        wireframeColor = _d._selectedColor;
                    }
                    else
                    {
                        solidColor = _d._selectedColor;
                        // 这里给它加一个黑色线框，保证立体感
                        wireframeColor = Color(0, 0, 0, 100);
                    }
                }
                // 是否选中
                bool bSelected = nodeFlag.hasFlag(WDNode::Flag::F_Selected);
                // 是否具有选中状态
                if (bSelected)
                {
                    wireframeColor = _d._selectedWireframeColor;
                }

                if (globalAlpha != nullptr)
                {
                    if (solidColor)
                        solidColor.value().a = *globalAlpha;
                    if (wireframeColor)
                        wireframeColor.value().a = *globalAlpha;
                }

                if (solidColor && !IsTransparent(solidColor.value().a))
                {
                    // 非半透明实体
                    if (!IsSubTransparent(solidColor.value().a))
                    {
                        outSolidInsts.insts.emplace_back(WDInstance());
                        auto& inst          = outSolidInsts.insts.back();
                        inst._color         = solidColor.value();
                        inst._instanceId    = 0;
                        inst._instanceAttr  = GetInstAttr(*pNode, bGlobalClip).data();
                        inst._local         = FMat4(pNode->globalTransform() * pGeom->transform());
                        inst._user          = 0;
                    }
                    // 半透明实体, 需要加入到半透明绘制中
                    else
                    {
                        MeshTransparentInsts tInsts;

                        auto gTrans         = pNode->globalTransform() * pGeom->transform();
                        tInsts.pMesh        = pMesh;
                        tInsts.distance     = 0.0;
                        // 获取到mesh的包围盒，然后用节点以及几何体矩阵变换，最后用于透明排序
                        tInsts.gMeshAabb    = pMesh->aabb();
                        tInsts.gMeshAabb.transform(gTrans);

                        auto& inst          = tInsts.solidInst;
                        inst._color         = solidColor.value();
                        inst._instanceId    = 0;
                        inst._instanceAttr  = GetInstAttr(*pNode, bGlobalClip).data();
                        inst._local         = FMat4(gTrans);
                        inst._user          = 0;

                        outTransparentInsts.push_back(std::move(tInsts));
                    }
                }
                if (wireframeColor && !IsTransparent(wireframeColor.value().a))
                {
                    auto pInsts = &outSelectedWireframeInsts;
                    if (!bSelected)
                        pInsts = &outNormalWireframeInsts;

                    pInsts->insts.emplace_back(WDInstance());
                    auto& inst          = pInsts->insts.back();
                    inst._color         = wireframeColor.value();
                    inst._instanceId    = 0;
                    inst._instanceAttr  = GetInstAttr(*pNode, bGlobalClip).data();
                    inst._local         = FMat4(pNode->globalTransform() * pGeom->transform());
                    inst._user          = 0;
                }
            }
        }
    }
    void collectMeshInsts(const GeomUMap& gms
        , MeshInsts& outNSolidInsts
        , bool bWireframeMode
        , bool bGlobalClip)
    {
        for (const auto& gm : gms)
        {
            auto pGeom = gm.first;
            if (pGeom == nullptr)
                continue;
            const auto& nodes = gm.second;
            for (auto pNode : nodes)
            {
                if (pNode == nullptr)
                    continue;
                auto nodeFlag = pNode->flags();
                // 不可见
                if (!nodeFlag.hasFlag(WDNode::F_Visible))
                    continue;
                // 颜色
                Color color = Color(0, 0, 0, 180);
                if (bWireframeMode)
                {
                    color = pNode->basicColor();
                    // 是否自定义颜色高亮
                    if (nodeFlag.hasFlag(WDNode::Flag::F_CustomHightlight))
                        color = pNode->customHighlightColor();
                    // 是否激活
                    else if (nodeFlag.hasFlag(WDNode::Flag::F_Active))
                        color = _d._activedColor;
                    // 是否高亮
                    else if (nodeFlag.hasFlag(WDNode::Flag::F_Highlight))
                        color = _d._highlightColor;

                    // 是否在树上选中
                    if (nodeFlag.hasFlag(WDNode::Flag::F_TreeSelected))
                        color = _d._selectedColor;

                    // 是否选中
                    if (nodeFlag.hasFlag(WDNode::Flag::F_Selected))
                        color = _d._selectedWireframeColor;
                }

                outNSolidInsts.insts.emplace_back(WDInstance());
                auto& inst          = outNSolidInsts.insts.back();
                inst._color         = color;
                inst._instanceId    = 0;
                inst._instanceAttr  = GetInstAttr(*pNode, bGlobalClip).data();
                inst._local         = FMat4(pNode->globalTransform() * pGeom->transform());
                inst._user          = 0;
            }
        }
    }
    // 处理透明排序
    void transparentInstsSort(WDContext& cxt) 
    {
        // 对于具有透明的物体，需要透明排序
        if (_transparentInsts.empty())
            return;
        auto eye = cxt.camera().eye(); // 视点
        auto dir = cxt.camera().frontDir(); // 视线方向
        // 使用方向和位置构造一个平面
        DPlane plane(dir, eye);
        for (auto& tInst : _transparentInsts)
        {
            // 计算包围盒中心点到平面的距离
            tInst.distance = plane.distance(tInst.gMeshAabb.center());
        }
        // 根据距离排序
        std::sort(_transparentInsts.begin(), _transparentInsts.end()
            , [](const MeshTransparentInsts& left, const MeshTransparentInsts& right)
            {
                // 如果两个包围盒中心点到相机视点平面的距离相等，则按照包围盒尺寸排序
                if (Abs(left.distance - right.distance) <= NumLimits<float>::Epsilon) 
                {
                    // 先画包围盒尺寸更小的Mesh
                    return left.gMeshAabb.size() < right.gMeshAabb.size();
                }
                else
                {
                    // 先画距离更远的Mesh
                    return left.distance > right.distance;
                }
            });
        
    }
};

class WDSceneNodeRenderDefaultPrivate: public WDGraphableInterface::GObserver
{
private:
    WDSceneNodeRenderDefault& _d;
public:
    WDSceneNodeRenderDefaultPrivate(WDSceneNodeRenderDefault& d) :_d(d)
    {
        _d.app().nodeTree().noticeCurrentNodeChanged() += {this, & WDSceneNodeRenderDefaultPrivate::onTreeCurrentNodeChanged};
    }
    ~WDSceneNodeRenderDefaultPrivate()
    {
        _d.app().nodeTree().noticeCurrentNodeChanged() -= {this, & WDSceneNodeRenderDefaultPrivate::onTreeCurrentNodeChanged};
    }
private:
    /***********************可图形化对象观察者相关*********************/
    virtual void onRemoveBefore(WDGraphableInterface& sender) override 
    {
        const auto& node = sender.ownerNode();
        // 几何体集合
        {
            const auto pGeoms = sender.gRenderGeoms();
            if (pGeoms != nullptr)
            {
                if (_d._pGMeshRender->remove(pGeoms, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // 关键点集合
        {
            const auto pKeyPoints = sender.gKeyPoints();
            if (pKeyPoints != nullptr)
            {
                if (_d._pKeyPointsRender->remove(pKeyPoints, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // PLine线集合
        {
            const auto pPLines = sender.gPLines();
            if (pPLines != nullptr)
            {
                if (_d._pLinesRender->remove(pPLines, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // 线集合
        {
            const auto pGLines = sender.gLines();
            if (pGLines != nullptr)
            {
                if (_d._pGLinesRender->remove(pGLines, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // 文本集合
        {
            const auto pGTexts = sender.gTexts();
            if (pGTexts != nullptr)
            {
                if (_d._pGTextsRender->remove(pGTexts, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
    }
    virtual void onAddAfter(WDGraphableInterface& sender) override 
    {
        const auto& node = sender.ownerNode();
        // 几何体集合
        {
            const auto pGeoms = sender.gRenderGeoms();
            if (pGeoms != nullptr)
            {
                if (_d._pGMeshRender->add(pGeoms, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // 关键点集合
        {
            const auto pKeyPoints = sender.gKeyPoints();
            if (pKeyPoints != nullptr) 
            {
                if (_d._pKeyPointsRender->add(pKeyPoints, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // PLine线集合
        {
            const auto pPLines = sender.gPLines();
            if (pPLines != nullptr)
            {
                if (_d._pLinesRender->add(pPLines, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // 线集合
        {
            const auto pGLines = sender.gLines();
            if (pGLines != nullptr)
            {
                if (_d._pGLinesRender->add(pGLines, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
        // 文本集合
        {
            const auto pGTexts = sender.gTexts();
            if (pGTexts != nullptr)
            {
                if (_d._pGTextsRender->add(pGTexts, &node))
                    _d._updateFlags.setFlag(WDSceneNodeRenderDefault::UpdateFlag::UF_Aabb, true);
            }
        }
    }
private:
    /**
     * @brief 模型树当前节点改变，需要触发重绘
    */
    void onTreeCurrentNodeChanged(WDNode::SharedPtr pCurrNode, WDNode::SharedPtr pPrevNode, WDNodeTree& sender)
    {
        WDUnused(pCurrNode);
        WDUnused(pPrevNode);
        WDUnused(sender);

        _d._pKeyPointsRender->setNeedUpdate(); 
        _d._pLinesRender->setNeedUpdate();
        _d._pGLinesRender->setNeedUpdate();
        _d._pGTextsRender->setNeedUpdate();
        _d._pGMeshRender->setNeedUpdate();
    }
};

WDSceneNodeRenderDefault::WDSceneNodeRenderDefault(WDCore& app)
    :WDSceneNodeRender(app, "WDSceneNodeRenderDefault") 
{
    _p = new WDSceneNodeRenderDefaultPrivate(*this);

    // 点集绘制对象
    _pKeyPointsRender   =   new KeyPointsRender(*this);
    // PLine绘制对象
    _pLinesRender       =   new PLinesRender(*this);
    // GLines绘制对象
    _pGLinesRender      =   new GLinesRender(*this);
    // GTexts绘制对象
    _pGTextsRender      =   new GTextsRender(*this);
    // 
    _pGMeshRender       =   new GMeshRender(*this);

    _updateFlags        =   UpdateFlag::UF_None;

    auto& cfgItemScene = app.cfg().get<std::string>("scene").setDisplayText("Scene");
    // 选中实体颜色配置
    _selectedColor = Color(255, 255, 0);
    cfgItemScene.get<Color>("selectedNode.color", _selectedColor)
        .setDisplayText("The color of selected entity")
        .bindValuePtr(&_selectedColor)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });
    // 选中线框颜色配置
    _selectedWireframeColor = Color(0, 255, 126);
    cfgItemScene.get<Color>("selectedNode.wireframe.color", _selectedWireframeColor)
        .setDisplayText("The color of selected wireframe")
        .bindValuePtr(&_selectedWireframeColor)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });
    // 选中线框粗细配置
    _selectedLineWidth = 2.0f;
    cfgItemScene.get<float>("selectedNode.wireframe.width", _selectedLineWidth)
        .setDisplayText("The thickness of selected wireframe")
        .bindValuePtr(&_selectedLineWidth)
        .setMinimum(1.0f)
        .setMaximum(10.0f)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });
    // 激活时的颜色配置
    _activedColor   = Color(255, 126, 0);
    cfgItemScene.get<Color>("activedNode.color", _activedColor)
        .setDisplayText("Activation color")
        .bindValuePtr(&_activedColor)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });
    // 高亮时的颜色配置
    _highlightColor = Color(255, 0, 0);
    cfgItemScene.get<Color>("hightlightNode.color", _highlightColor)
        .setDisplayText("Highlight color")
        .bindValuePtr(&_highlightColor)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });
    // 常规线框粗细配置
    _normalLineWidth = 1.0f;
    cfgItemScene.get<float>("normalNode.wireframe.width", _normalLineWidth)
        .setDisplayText("The thickness of normal wireframe")
        .bindValuePtr(&_normalLineWidth)
        .setMinimum(1.0f)
        .setMaximum(10.0f)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });

    // 管道保温透明度配置
    _alphaInsu = 0.0f;
    cfgItemScene.get<float>("pipe.insulation", _alphaInsu)
        .setDisplayText("Pipe insulation transparency")
        .bindValuePtr(&_alphaInsu)
        .setMinimum(0.0f)
        .setMaximum(1.0f)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });

    // 空间占有级别(Obstruction)透明度配置
    _alphaObstruction = 0.0f;
    cfgItemScene.get<float>("model.obstruction", _alphaObstruction)
        .setDisplayText("Transparency of space occupation levels")
        .bindValuePtr(&_alphaObstruction)
        .setMinimum(0.0f)
        .setMaximum(1.0f)
        .bindFunction({ this, &WDSceneNodeRenderDefault::onSceneCfgValueChanged });

}
WDSceneNodeRenderDefault::~WDSceneNodeRenderDefault()
{
    if (_p != nullptr)
    {
        delete _p;
        _p = nullptr;
    }
    if (_pKeyPointsRender != nullptr)
    {
        delete _pKeyPointsRender;
        _pKeyPointsRender = nullptr;
    }
    if (_pLinesRender != nullptr)
    {
        delete _pLinesRender;
        _pLinesRender = nullptr;
    }
    if (_pGLinesRender != nullptr)
    {
        delete _pGLinesRender;
        _pGLinesRender = nullptr;
    }
    if (_pGTextsRender != nullptr)
    {
        delete _pGTextsRender;
        _pGTextsRender = nullptr;
    }
    if (_pGMeshRender != nullptr) 
    {
        delete _pGMeshRender;
        _pGMeshRender = nullptr;
    }
}

void WDSceneNodeRenderDefault::onNodeAdd(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;

    auto pBDBase = pNode->getBDBase();
    if (pBDBase == nullptr)
        return;

    auto pGraphable = pBDBase->graphableSupporter();
    if (pGraphable == nullptr)
        return;

    // 计算当前新加入的节点 开启/关闭孔洞
    bool bHolesDrawn = this->app().holesDrawn().first;
    float arcTolerance = this->app().holesDrawn().second;
    if (pGraphable->hasHoles()) 
    {
        auto aHoles = pGraphable->gRenderGeomsAlreadyHoles();
        if (aHoles)
        {
            // 需要开孔但节点模型未开孔
            if (bHolesDrawn && !aHoles.value().first)
                pNode->update(true);   // 更新节点模型，已便于给节点开孔
            // 不需要开孔但节点模型已开孔 
            else if (!bHolesDrawn && aHoles.value().first)
                pNode->update(true);  // 更新节点模型，已便于给节点开孔
            // 需要开孔，且节点模型已开孔，但是开孔的角度公差不一致
            else if (bHolesDrawn && aHoles.value().first && (arcTolerance != aHoles.value().second))
                pNode->update(true);
        }
    }
    //添加
    if (this->addGraphable(pNode.get(), pGraphable))
    {
        //添加节点
        auto fItr = _nodes.find(pNode.get());
        if (fItr == _nodes.end())
        {
            _nodes.insert(pNode.get());
            _updateFlags.setFlag(UpdateFlag::UF_Aabb, true);
        }
    }
}
void WDSceneNodeRenderDefault::onNodeRemove(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;

    if (pNode->getBDBase() == nullptr)
        return;

    auto pGraphable = pNode->getBDBase()->graphableSupporter();
    if (pGraphable == nullptr)
        return;

    //移除
    this->removeGraphable(pNode.get(), pGraphable);

    //移除节点
    auto fItr = _nodes.find(pNode.get());
    if (fItr != _nodes.end())
    {
        _nodes.erase(fItr);
        _updateFlags.setFlag(UpdateFlag::UF_Aabb, true);
    }
}
void WDSceneNodeRenderDefault::onNodeUpdate(WDNode::SharedPtr pNode)
{
    WDUnused(pNode);

    _pKeyPointsRender->setNeedUpdate();
    _pLinesRender->setNeedUpdate();
    _pGLinesRender->setNeedUpdate();
    _pGTextsRender->setNeedUpdate();
    _pGMeshRender->setNeedUpdate();

    _updateFlags.setFlag(UpdateFlag::UF_Aabb, true);
}
void WDSceneNodeRenderDefault::onNodeClear()
{
    this->_pGTextsRender->clear();
    this->_pGLinesRender->clear();
    this->_pKeyPointsRender->clear();
    this->_pLinesRender->clear();
    this->_pGMeshRender->clear();
    this->clearGraphable();
    _nodes.clear();

    _updateFlags.setFlag(UpdateFlag::UF_Aabb, true);
}

bool WDSceneNodeRenderDefault::empty() const
{
    return _nodes.empty();
}
bool WDSceneNodeRenderDefault::containsRelatives(const WDNode& node) const
{
    for (auto pTNode : _nodes)
    {
        if (pTNode == nullptr)
        {
            assert(false && "无效的节点对象！");
            continue;
        }
        // 是否与加入节点相同
        if (pTNode == &node)
            return true;
        // 是否直系祖先或后代
        if (node.isAncestor(*pTNode) || pTNode->isAncestor(node))
            return true;
    }
    return false;
}

void WDSceneNodeRenderDefault::updateAabb(WDContext& context, const WDScene& scene)
{
    WDUnused(context);
    WDUnused(scene);
    // 更新包围盒
    this->updateAabb();
}
void WDSceneNodeRenderDefault::update(WDContext& context, const WDScene& scene)
{
    WDUnused(context);

    // 更新点集绘制
    _pKeyPointsRender->onUpdate(context);
    // 更新PLine绘制
    _pLinesRender->onUpdate(context);
    // 更新GLines绘制
    _pGLinesRender->onUpdate(context);
    // 更新GTexts绘制
    _pGTextsRender->onUpdate(context);
    // 更新GMeshs绘制
    _pGMeshRender->onUpdate(context, scene.rMode());
}
void WDSceneNodeRenderDefault::render(WDContext& context, const WDScene& scene)
{
    WDUnused(scene);

    if (this->empty())
        return;

    WD::WDRLFlag renderLayer = context._renderLayer;
    if (!renderLayer.hasFlag(RL_Scene))
        return;

    // 绘制PLine
    _pLinesRender->onRender(context);
    // 绘制关键点
    _pKeyPointsRender->onRender(context);
    // GLines绘制
    _pGLinesRender->onRender(context);
    // GTexts绘制
    _pGTextsRender->onRender(context);
    // GMesh绘制
    _pGMeshRender->onRender(context);
}

bool WDSceneNodeRenderDefault::pickup(const WDPickupParam& param, WDNodeSelection& selection, WDNodePickupResult& outResult) const
{
    bool bRet = false;

    if (param.intersectAabb(this->aabb()))
        bRet = selection.pickup(_nodes.begin(), _nodes.end(), param, outResult);

    // 发生了拾取动作，表明节点的选中状态可能发生了改变, 需要触发更新, 以便能正常显示节点的选中状态
    _pKeyPointsRender->setNeedUpdate();
    _pLinesRender->setNeedUpdate();
    _pGLinesRender->setNeedUpdate();
    _pGTextsRender->setNeedUpdate();
    _pGMeshRender->setNeedUpdate();

    return bRet;
}
bool WDSceneNodeRenderDefault::frameSelect(const WDFrameSelectParam& param, WDNodeSelection& selection, std::vector<WDNode::SharedPtr>& outNodes) const
{
    bool bRet = false;

    auto rInterAabb = param.intersectAabb(this->aabb());
    switch (rInterAabb)
    {
    case WD::WDFrameSelectParam::R_InvaildData:
    case WD::WDFrameSelectParam::R_WhollyWithout:
        bRet = false;
        break;
    case WD::WDFrameSelectParam::R_PartiallyWithin:
    case WD::WDFrameSelectParam::R_WhollyWithin:
        bRet = selection.frameSelect(_nodes.begin(), _nodes.end(), param, outNodes);
        break;
    default:
        bRet = false;
        break;
    }

    // 发生了框选动作，表明节点的选中状态可能发生了改变,需要触发更新, 以便能正常显示节点的选中状态
    _pKeyPointsRender->setNeedUpdate();
    _pLinesRender->setNeedUpdate();
    _pGLinesRender->setNeedUpdate();
    _pGTextsRender->setNeedUpdate();
    _pGMeshRender->setNeedUpdate();

    return bRet;
}

void WDSceneNodeRenderDefault::onHolesDrawnChanged()
{
    this->updateHolesNodes();
}
void WDSceneNodeRenderDefault::needUpdate()
{
    // 包围盒更新标志
    _updateFlags.addFlag(UF_Aabb);
    // 触发重绘
    _pKeyPointsRender->setNeedUpdate();
    _pLinesRender->setNeedUpdate();
    _pGLinesRender->setNeedUpdate();
    _pGTextsRender->setNeedUpdate();
    _pGMeshRender->setNeedUpdate();
}

void WDSceneNodeRenderDefault::updateAabb()
{
    if (!_updateFlags.hasFlag(UF_Aabb))
        return;
    _updateFlags.removeFlag(UF_Aabb);

    DAabb3& aabb = this->aabbRef();
    aabb = DAabb3::Null();
    for (auto pNode : _nodes)
    {
        if (pNode == nullptr)
            continue;
        aabb.unions(pNode->aabb());
    }
}

bool WDSceneNodeRenderDefault::addGraphable(WDNode* pNode, WDGraphableInterface* pGraphable)
{
    if (pNode == nullptr || pGraphable == nullptr)
        return false;

    //添加几何体对象
    this->_pGMeshRender->add(pGraphable->gRenderGeoms(), pNode);
    //添加点集对象
    this->_pKeyPointsRender->add(pGraphable->gKeyPoints(), pNode);
    //添加PLine对象
    this->_pLinesRender->add(pGraphable->gPLines(), pNode);
    // 添加GLines对象
    this->_pGLinesRender->add(pGraphable->gLines(), pNode);
    // 添加GTexts对象
    this->_pGTextsRender->add(pGraphable->gTexts(), pNode);

    //设置观察者
    pGraphable->addGObserver(_p);

    return true;
}
bool WDSceneNodeRenderDefault::removeGraphable(WDNode* pNode, WDGraphableInterface* pGraphable)
{
    if (pNode == nullptr || pGraphable == nullptr)
        return false;

    //移除观察者
    pGraphable->removeGObserver(_p);

    //移除几何体对象
    this->_pGMeshRender->remove(pGraphable->gRenderGeoms(), pNode);
    //移除点集对象
    this->_pKeyPointsRender->remove(pGraphable->gKeyPoints(), pNode);
    //移除PLine对象
    this->_pLinesRender->remove(pGraphable->gPLines(), pNode);
    // 移除GLines对象
    this->_pGLinesRender->remove(pGraphable->gLines(), pNode);
    // 移除GTexts对象
    this->_pGTextsRender->remove(pGraphable->gTexts(), pNode);

    return true;
}
void WDSceneNodeRenderDefault::clearGraphable()
{
    //移除所有已添加节点的 图形对象观察者
    for (auto pNode : _nodes)
    {
        //遍历节点组件，获取图形对象
        if (pNode->getBDBase() == nullptr)
            continue;
        auto pGraphable = pNode->getBDBase()->graphableSupporter();
        if (pGraphable == nullptr)
            continue;
        pGraphable->removeGObserver(_p);
    }
}

void WDSceneNodeRenderDefault::updateHolesNodes()
{
    bool bHolesDrawn = this->app().holesDrawn().first;
    float arcTolerance = this->app().holesDrawn().second;
    auto pBar = this->app().blockingTask();
    if (pBar != nullptr && !pBar->isRunning())
    {
        WDBlockingTask::Config cfg;
        cfg.closable = true;
        cfg.progress = true;
        cfg.decimals = 4;
        pBar->setConfig(cfg);

        pBar->start([this, bHolesDrawn, arcTolerance](WDBlockingTask& bar)
            {
                const char* tStrU8 = this->app().holesDrawn().first ? "开启" : "关闭";
                const char* tStr = this->app().holesDrawn().first ? "开启" : "关闭";

                char buf[64] = { 0 };
                sprintf_s(buf, sizeof(buf), "%s孔洞...", tStrU8);
                bar.setProgressText(buf, 0.0f, 0);

                WDTimestamp tm;
                double prevTm = tm.getElapsedSecond();
                LOG_INFO << "开始" << tStr << "孔洞";

                double cnt = 0.0;
                double allCnt = static_cast<double>(_nodes.size());
                size_t holesCnt = 0;

                for (auto pNode : _nodes)
                {
                    // 检测退出标志
                    if (bar.existFlag())
                        break;
                    // 设置进度
                    cnt += 1.0;
                    float progress = static_cast<float>(cnt / allCnt);
                    bar.setProgress(progress);

                    if (pNode == nullptr)
                        continue;
                    auto pData = pNode->getBDBase();
                    if (pData == nullptr)
                        continue;
                    auto pGraphable = pData->graphableSupporter();
                    if (pGraphable == nullptr)
                        continue;
                    // 如果没有孔洞，则不做更新
                    if (!pGraphable->hasHoles())
                        continue;

                    // 根据是否已开孔决定是否跳过开孔
                    auto aHoles = pGraphable->gRenderGeomsAlreadyHoles();
                    if (aHoles)
                    {
                        // 如果需要开孔且节点已被开孔并且角度公差一致，则跳过
                        if (bHolesDrawn && aHoles.value().first && (arcTolerance == aHoles.value().second))
                            continue;
                        // 如果不需要开孔且节点未开孔，则跳过
                        if (!bHolesDrawn && !aHoles.value().first)
                            continue;
                    }
                    // 移除
                    this->removeGraphable(pNode, pGraphable);
                    // 更新节点
                    // ！注意：
                    //  1. 这里不能使用triggerUpdate, 因为场景中添加有直管节点
                    //      如果使用triggerUpdate，会调用到分支更新，从而重新生成直管节点，会导致场景中现有的直管节点被释放，导致崩溃
                    //      而且会产生一些不必要的更新，从而导致过程变慢
                    //      bool运算重算时，只需要将场景中现添加的所有节点(模型节点)直接update即可，不需要考虑触发其父节点的更新
                    //  2. 这里不能直接使用modelUpdate,因为孔洞更新后，模型的包围盒可能发生了改变，这里要使用新的包围盒来计算节点包围盒
                    //      使用modelUpdate时,并未更新到节点的包围盒
                    pNode->update(true);
                    // 重新添加
                    this->addGraphable(pNode, pGraphable);

                    holesCnt++;
                }

                double tmpTm = tm.getElapsedSecond() - prevTm;

                if (bar.existFlag())
                    LOG_INFO << "用户自行取消了" << tStr << "孔洞, 进行开孔的节点个数: " << holesCnt << ", 耗时: " << tmpTm << "秒!";
                else
                    LOG_INFO << "结束" << tStr << "孔洞, 进行开孔的节点个数: " << holesCnt << ", 耗时: " << tmpTm << "秒!";

                bar.setProgress(1.0f);
            }
        );
    }
    else
    {
        const char* tStr = this->app().holesDrawn().first ? "开启" : "关闭";

        WDTimestamp tm;
        double prevTm = tm.getElapsedSecond();
        LOG_INFO << "开始" << tStr << "孔洞";

        size_t holesCnt = 0;

        for (auto pNode : _nodes)
        {
            if (pNode == nullptr)
                continue;
            auto pData = pNode->getBDBase();
            if (pData == nullptr)
                continue;
            auto pGraphable = pData->graphableSupporter();
            if (pGraphable == nullptr)
                continue;
            // 如果没有孔洞，则不做更新
            if (!pGraphable->hasHoles())
                continue;

            // 根据是否已开孔决定是否跳过开孔
            auto aHoles = pGraphable->gRenderGeomsAlreadyHoles();
            if (aHoles)
            {
                // 如果需要开孔且节点已被开孔并且角度公差一致，则跳过
                if (bHolesDrawn && aHoles.value().first && (arcTolerance == aHoles.value().second))
                    continue;
                // 如果不需要开孔且节点未开孔，则跳过
                if (!bHolesDrawn && !aHoles.value().first)
                    continue;
            }
            // 移除
            this->removeGraphable(pNode, pGraphable);
            // 更新节点
            // ！注意：
            //  1. 这里不能使用triggerUpdate, 因为场景中添加有直管节点
            //      如果使用triggerUpdate，会调用到分支更新，从而重新生成直管节点，会导致场景中现有的直管节点被释放，导致崩溃
            //      而且会产生一些不必要的更新，从而导致过程变慢
            //      bool运算重算时，只需要将场景中现添加的所有节点(模型节点)直接update即可，不需要考虑触发其父节点的更新
            //  2. 这里不能直接使用modelUpdate,因为孔洞更新后，模型的包围盒可能发生了改变，这里要使用新的包围盒来计算节点包围盒
            //      使用modelUpdate时,并未更新到节点的包围盒
            pNode->update(true);
            // 重新添加
            this->addGraphable(pNode, pGraphable);

            holesCnt++;
        }
        
        double tmpTm = tm.getElapsedSecond() - prevTm;
        LOG_INFO << "结束" << tStr << "孔洞, 进行开孔的节点个数: " << holesCnt << ", 耗时: " << tmpTm << "秒!";
    }
}

WD_NAMESPACE_END