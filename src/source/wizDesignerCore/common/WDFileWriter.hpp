#pragma once

#include    "WDUtils.h"
#include    "WDFlags.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 写入三类载体：磁盘文件、虚拟文件（只计算数据大小不写内容）、内存
 */
class   WDWriter
{
public:
    WDWriter()
    {}
    virtual ~WDWriter()
    {}
    /// <summary>
    /// 设置用户数据
    /// </summary>
    virtual void    setUser(void* user) = 0;
    /// <summary>
    /// 获取用户数据
    /// </summary>
    virtual void*   getUser() = 0;
    /// <summary>
    /// 重置
    /// </summary>
    virtual void    resset() = 0;
    /// <summary>
    /// 缓冲区大小
    /// </summary>
    virtual size_t  bufferSize() = 0;
    /// <summary>
    /// 获取位置
    /// </summary>
    virtual  size_t tell() const = 0;
    /// <summary>
    /// 到制定位置
    /// </summary>
    virtual void    seek(size_t pos) = 0;
    /// <summary>
    /// 跳过
    /// </summary>
    virtual void    skip(size_t nSize) 
    {
        seek(tell() + nSize);
    }
    /// <summary>
    /// 判断是否满
    /// </summary>
    virtual bool    isFull() const = 0;
    /// <summary>
    /// 写数据,返回写的内存大小 == size
    /// </summary>
    virtual size_t  writeBuffer(const void* buf, size_t size) = 0;
    /// <summary>
    /// write是否为虚拟的，是，只计算所需的内存大小，不写内容
    /// 还是要先序列化对象，再计算数据所占内存大小
    /// </summary>
    virtual bool    isVirtual() const = 0;
    /// <summary>
    /// 可以被重写
    /// </summary>
    virtual void*   dataPtr()
    {
        return  0;
    }
public:
    size_t     writeString(const std::string& data)
    {
        size_t  nStart  =   tell();
        uint    nCnt    =   uint(data.size());
        writeBuffer(&nCnt,sizeof(nCnt));
        if (nCnt)
        {
            writeBuffer(data.data(),    nCnt);
        }
        return  tell() - nStart;
    }
    template<typename T>
    size_t     writeArray(const std::vector<T>& data)
    {
        size_t  nStart  =   tell();
        uint    nCnt    =   uint(data.size());
        writeBuffer(&nCnt,sizeof(nCnt));
        if (nCnt)
        {
            writeBuffer(data.data(),    nCnt * sizeof(T));
        }
        return  tell() - nStart;
    }
};

/**
 * @brief 写入内存
 */
class   WDMemoryWriter :public WDWriter
{
public:
    char*       _memory;
    size_t      _size;
    size_t      _cur;
    void*       _user;
public:
    WDMemoryWriter(char* memory, size_t size)
        :_memory(memory)
        , _size(size)
        , _cur(0)
        , _user(0)
    {}
    virtual ~WDMemoryWriter()
    {}

    virtual void*   current()
    {
        return  (char*)_memory + _cur;
    }
    virtual void    setUser(void* user) override
    {
        _user = user;
    }
    virtual void*   getUser() override
    {
        return  _user;
    }

    virtual void    resset() override
    {
        _cur = 0;
    }
    virtual size_t  bufferSize() override
    {
        return  _size;
    }
    virtual  size_t tell() const override
    {
        return  _cur;
    }
    virtual void    seek(size_t pos) override
    {
        _cur = pos;
    }
    virtual bool    isFull() const override
    {
        return  _cur >= _size;
    }
    virtual size_t  writeBuffer(const void* buf, size_t size) override
    {
        char*   pData = _memory + _cur;
        memcpy(pData, buf, size);
        _cur += size;
        return  size;
    }
    virtual bool    isVirtual() const override
    {
        return  false;
    }
    /// <summary>
    /// 可以被重写
    /// </summary>
    virtual void*   dataPtr() override
    {
        return  _memory;
    }
};

/**
 * @brief 写入虚拟文件（只计算数据大小不写内容）
 */
class   WDVirtualWriter :public WDWriter
{
public:
    size_t  _cur;
    void*   _user;
public:
    WDVirtualWriter()
        :_cur(0)
        , _user(0)
    {}
    /// <summary>
    /// 设置用户数据
    /// </summary>
    virtual void    setUser(void* user) override
    {
        _user = user;
    }
    /// <summary>
    /// 获取用户数据
    /// </summary>
    virtual void*   getUser() override
    {
        return  _user;
    }
    /// <summary>
    /// 重置
    /// </summary>
    virtual void    resset() override
    {
        _cur = 0;
    }
    /// <summary>
    /// 缓冲区大小
    /// </summary>
    virtual size_t  bufferSize() override
    {
        return  0xFFFFFFFF;
    }
    /// <summary>
    /// 获取位置
    /// </summary>
    virtual  size_t tell() const override
    {
        return  _cur;
    }
    /// <summary>
    /// 到制定位置
    /// </summary>
    virtual void    seek(size_t pos) override
    {
        _cur = pos;
    }
    /// <summary>
    /// 判断是否满
    /// </summary>
    virtual bool    isFull() const override
    {
        return  false;
    }
    /// <summary>
    /// 写数据,返回写的内存大小 == size
    /// </summary>
    virtual size_t  writeBuffer(const void*, size_t size) override
    {
        _cur += size;
        return  size;
    }
    virtual bool    isVirtual() const override
    {
        return true;
    }
};

/**
 * @brief 写入磁盘文件
 */
class   WDFileWriter :public WDWriter
{

public:
    void*   _user;
    FILE*   _file;
public:
    WDFileWriter(FILE* pFile)
        :_file(pFile)
        , _user(0)
    {}
    /// <summary>
    /// 设置用户数据
    /// </summary>
    virtual void    setUser(void* user) override
    {
        _user = user;
    }
    /// <summary>
    /// 获取用户数据
    /// </summary>
    virtual void*   getUser() override
    {
        return  _user;
    }
    /// <summary>
    /// 重置
    /// </summary>
    virtual void    resset() override
    {
        #if WD_PLATFORM == WD_PLATFORM_WIN32
                _fseeki64(_file, 0, SEEK_SET);
        #elif WD_PLATFORM == WD_PLATFORM_LINUX
                fseeko64(_file, 0, SEEK_SET);
        #endif
    }
    virtual size_t  writeBuffer(const void* data, size_t size) override
    {
        if (_file == 0)
        {
            assert(_file != 0);
            return  0;
        }
        return  fwrite(data, 1, size, _file);
        //return  size;
    }
    /// <summary>
    /// 返回缓冲区大小
    /// </summary>
    virtual size_t  bufferSize() override
    {
        return  0xFFFFFFFF;
    }
    /// <summary>
    /// 返回缓写入的位置
    /// </summary>
    virtual  size_t tell() const override
    {
#if WD_PLATFORM == WD_PLATFORM_WIN32
        return  _ftelli64(_file);
#elif WD_PLATFORM == WD_PLATFORM_LINUX
        return  ftello64(_file);
#elif WD_PLATFORM == WD_PLATFORM_APPLE
        return ftello(_file);
#endif
    }
    virtual void    seek(size_t pos) override
    {
        #if WD_PLATFORM == WD_PLATFORM_WIN32
                _fseeki64(_file, pos, SEEK_SET);
        #elif WD_PLATFORM == WD_PLATFORM_LINUX
                fseeko64(_file, pos, SEEK_SET);
        #elif WD_PLATFORM == WD_PLATFORM_LINUX
                fseeko(_file, pos, SEEK_SET);
        #endif
    }
    virtual bool    isFull() const override
    {
        return  false;
    }
    virtual bool    isVirtual() const override
    {
        return  false;
    }
};


WD_NAMESPACE_END