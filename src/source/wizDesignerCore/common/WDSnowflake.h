#pragma once
#include "WDUtils.h"

WD_NAMESPACE_BEGIN

/**
* @brief 雪花id
*/
class WD_API WDSnowflake
{
public:
    /**
    * @brief 获取空的雪花id
    */
    static const WDSnowflake& Null();
public:
    int64_t    data;
public:
    inline  WDSnowflake()
        : data(0)
    {
    }
    inline  WDSnowflake(int64_t data)
        : data(data)
    {
    }
    inline  WDSnowflake(const WDSnowflake& right)
        : data(right.data)
    {
    }
    inline  ~WDSnowflake()
    {
    }
public:
    /**
    * @brief 是否是空的uuid
    */
    inline bool isNull() const
    {
        return data == 0;
    }
public:
    /**
    * @brief 赋值运算
    */
    inline WDSnowflake& operator=(const WDSnowflake& right)
    {
        if (this == &right)
            return *this;
        data = right.data;
        return *this;
    }
    /**
    * @brief 相等运算
    */
    inline bool operator==(const WDSnowflake& right) const
    {
        return data == right.data;
    }
    /**
    * @brief 不相等运算
    */
    inline bool operator!=(const WDSnowflake& right) const
    {
        return data != right.data;
    }
    /**
    * @brief 大小比较,大于
    */
    bool operator>(const WDSnowflake& right) const
    {
        return data > right.data;
    }
    /**
    * @brief 大小比较,小于
    */
    bool operator<(const WDSnowflake& right) const
    {
        return data < right.data;
    }
public:
    /**
    * @brief 创建雪花id
    */
    static WDSnowflake Create();
};

inline bool operator<=(const WDSnowflake& left, const WDSnowflake& right)
{
    return !(right > left);
}

inline bool operator>=(const WDSnowflake& left, const WDSnowflake& right)
{
    return !(left < right);
}


WD_NAMESPACE_END


