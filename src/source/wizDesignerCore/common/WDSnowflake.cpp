#include "WDSnowflake.h"
#include "private/snowflake.hpp"
#include "WDUuid.h"

WD_NAMESPACE_BEGIN

// 起始时间戳基准点
static constexpr int64_t Twepoch = 1534832906275L;
using Snowflake = snowflake<Twepoch, std::mutex>;
static Snowflake  SnowflakeMaker;

const WDSnowflake& WDSnowflake::Null()
{
    static const WDSnowflake null;
    return null;
}

WDSnowflake WDSnowflake::Create()
{
	try
	{
        auto id = SnowflakeMaker.nextid();
		return WDSnowflake(id);
	}
	catch (const std::exception&)
	{
		return Null();
	}
}

WD_NAMESPACE_END