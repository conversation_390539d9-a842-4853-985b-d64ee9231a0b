#pragma once
#include <cstdint>
#include <mutex>
#include <thread>
#include <chrono>


#ifdef _WIN32
#include <winsock2.h>
#include <iphlpapi.h>
#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")
#else
#ifdef __APPLE__
#include <ifaddrs.h>
#include <net/if_dl.h>
#include <sys/socket.h>
#else
#include <ifaddrs.h>
#include <netpacket/packet.h>
#include <net/if.h>
#endif
#endif


class snowflake_nonlock
{
public:
    void lock()
    {
    }
    void unlock()
    {
    }
};

struct NetworkInterface
{
    std::string name;
    std::string macAddress;
};
std::vector<NetworkInterface> getNetworkInterfaces();

template<int64_t Twepoch, typename Lock = snowflake_nonlock>
class snowflake
{
    using lock_type = Lock;
    static constexpr int64_t TWEPOCH = Twepoch;
    static constexpr int64_t WORKER_ID_BITS = 5L;
    static constexpr int64_t DATACENTER_ID_BITS = 5L;
    static constexpr int64_t MAX_WORKER_ID = (1 << WORKER_ID_BITS) - 1;
    static constexpr int64_t MAX_DATACENTER_ID = (1 << DATACENTER_ID_BITS) - 1;
    static constexpr int64_t SEQUENCE_BITS = 12L;
    static constexpr int64_t WORKER_ID_SHIFT = SEQUENCE_BITS;
    static constexpr int64_t DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    static constexpr int64_t TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;
    static constexpr int64_t SEQUENCE_MASK = (1 << SEQUENCE_BITS) - 1;

    using time_point = std::chrono::time_point<std::chrono::steady_clock>;

    time_point start_time_point_ = std::chrono::steady_clock::now();
    int64_t start_millsecond_ = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();

    int64_t last_timestamp_ = -1;
    int64_t workerid_ = 0;
    int64_t datacenterid_ = 0;
    int64_t sequence_ = 0;
    lock_type lock_;
public:
    snowflake(int64_t workid, int64_t datacenterid)
    {
        if (workid > MAX_WORKER_ID || workid < 0) {
            throw std::runtime_error("Given WorkId is Illegal!");
        }
        if (datacenterid > MAX_DATACENTER_ID || datacenterid < 0)
        {
            throw std::runtime_error("Given DatacenterId is Illegal!");
        }
        workerid_ = workid;
        datacenterid_ = datacenterid;
    }
    // 使用mac地址进行初始化
    snowflake()
    {
        workerid_ = getWorkIByPId();
        datacenterid_ = getDataCentIdByMac();
    }

    snowflake(const snowflake&) = delete;

    snowflake& operator=(const snowflake&) = delete;

    int64_t nextid()
    {
        std::lock_guard<lock_type> lock(lock_);
        //std::chrono::steady_clock  cannot decrease as physical time moves forward
        auto timestamp = millsecond();
        if (last_timestamp_ == timestamp)
        {
            sequence_ = (sequence_ + 1) & SEQUENCE_MASK;
            if (sequence_ == 0)
            {
                timestamp = wait_next_millis(last_timestamp_);
            }
        }
        else
        {
            sequence_ = 0;
        }

        last_timestamp_ = timestamp;

        return ((timestamp - TWEPOCH) << TIMESTAMP_LEFT_SHIFT)
            | (datacenterid_ << DATACENTER_ID_SHIFT)
            | (workerid_ << WORKER_ID_SHIFT)
            | sequence_;
    }

private:
    int64_t millsecond() const noexcept
    {
        auto diff = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start_time_point_);
        return start_millsecond_ + diff.count();
    }

    int64_t wait_next_millis(int64_t last) const noexcept
    {
        auto timestamp = millsecond();
        while (timestamp <= last)
        {
            timestamp = millsecond();
        }
        return timestamp;
    }
    // 通过mac地址生成一个datacenterid
    int64_t getDataCentIdByMac()
    {
        auto macs = getNetworkInterfaces();
        if (macs.empty())
            return 0;
        auto macAddr = macs[0].macAddress;
        std::string mac;
        mac.reserve(macAddr.size());
        for (char c : macAddr)
        {
            if (c != '-' && c != ':' && c != '.')
            {
                mac.append(1, char(std::toupper(c)));
            }
        }
        if (mac.length() != 12)
        {
            return 0;
        }
        // 使用std::hash计算哈希值
        std::hash<std::string> hasher;
        auto hash_value = hasher(mac);
        // 映射到datacenter_id范围
        return static_cast<int64_t>(hash_value % (static_cast<size_t>(MAX_DATACENTER_ID) + 1));
    }
    int64_t getWorkIByPId()
    {
#if WD_PLATFORM == WD_PLATFORM_LINUX
        auto pid = _getpid();
#else
        auto pid = getpid();
#endif
        auto tid = std::this_thread::get_id();
        // 将进程 ID 和线程 ID 组合成一个字符串
        std::string combinedId = std::to_string(pid) + std::to_string(std::hash<std::thread::id>()(tid));

        // 使用 std::hash 计算哈希值
        std::hash<std::string> hasher;
        auto hash_value = hasher(combinedId);
        // 映射到 workid 范围
        return static_cast<int64_t>(hash_value % (static_cast<size_t>(MAX_WORKER_ID) + 1));
    }

};


std::vector<NetworkInterface> getNetworkInterfaces()
{
    std::vector<NetworkInterface> interfaces;

#ifdef _WIN32
    // Windows implementation
    ULONG outBufLen = 0;
    GetAdaptersAddresses(AF_UNSPEC, 0, NULL, NULL, &outBufLen);

    std::vector<unsigned char> buffer(outBufLen);
    IP_ADAPTER_ADDRESSES* pAddresses =
        reinterpret_cast<IP_ADAPTER_ADDRESSES*>(buffer.data());

    if (GetAdaptersAddresses(AF_UNSPEC, 0, NULL, pAddresses, &outBufLen) == NO_ERROR)
    {
        for (IP_ADAPTER_ADDRESSES* pAdapter = pAddresses; pAdapter != NULL; pAdapter = pAdapter->Next)
        {
            if (pAdapter->PhysicalAddressLength > 0)
            {
                NetworkInterface ni;

                // 兼容不同Windows SDK版本的友好名称获取
#ifdef IP_ADAPTER_FRIENDLY_NAME
                ni.name = pAdapter->FriendlyName;
#else
                ni.name = pAdapter->AdapterName;
#endif

                char mac[18] = { 0 };
                sprintf_s(mac, sizeof(mac),
                    "%02X:%02X:%02X:%02X:%02X:%02X",
                    static_cast<unsigned int>(pAdapter->PhysicalAddress[0]),
                    static_cast<unsigned int>(pAdapter->PhysicalAddress[1]),
                    static_cast<unsigned int>(pAdapter->PhysicalAddress[2]),
                    static_cast<unsigned int>(pAdapter->PhysicalAddress[3]),
                    static_cast<unsigned int>(pAdapter->PhysicalAddress[4]),
                    static_cast<unsigned int>(pAdapter->PhysicalAddress[5]));

                ni.macAddress = mac;
                interfaces.push_back(ni);
            }
        }
    }
#else
    // Linux/macOS implementation
    struct ifaddrs* ifAddrStruct = nullptr;
    struct ifaddrs* ifa = nullptr;

    if (getifaddrs(&ifAddrStruct) == 0)
    {
        for (ifa = ifAddrStruct; ifa != nullptr; ifa = ifa->ifa_next)
        {
            if (ifa->ifa_addr == nullptr)
                continue;

#ifdef __APPLE__
            if (ifa->ifa_addr->sa_family == AF_LINK)
            {
                struct sockaddr_dl* sdl = reinterpret_cast<struct sockaddr_dl*>(ifa->ifa_addr);
                if (sdl->sdl_alen > 0)
                {
                    NetworkInterface ni;
                    ni.name = ifa->ifa_name;

                    unsigned char* mac = reinterpret_cast<unsigned char*>(LLADDR(sdl));
                    char macStr[18] = { 0 };
                    sprintf(macStr, "%02X:%02X:%02X:%02X:%02X:%02X",
                        mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

                    ni.macAddress = macStr;
                    interfaces.push_back(ni);
                }
            }
#else
            if (ifa->ifa_addr->sa_family == AF_PACKET)
            {
                struct sockaddr_ll* s = reinterpret_cast<struct sockaddr_ll*>(ifa->ifa_addr);
                if (s->sll_halen > 0)
                {
                    NetworkInterface ni;
                    ni.name = ifa->ifa_name;

                    char macStr[18] = { 0 };
                    sprintf(macStr, "%02X:%02X:%02X:%02X:%02X:%02X",
                        s->sll_addr[0], s->sll_addr[1],
                        s->sll_addr[2], s->sll_addr[3],
                        s->sll_addr[4], s->sll_addr[5]);

                    ni.macAddress = macStr;
                    interfaces.push_back(ni);
                }
            }
#endif
        }
        freeifaddrs(ifAddrStruct);
    }
#endif

    return interfaces;
}