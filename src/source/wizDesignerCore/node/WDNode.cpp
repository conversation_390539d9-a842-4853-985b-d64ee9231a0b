#include    "WDNode.h"
#include    "../WDTranslate.h"
#include    "../graphable/WDGraphableInterface.h"
#include    "../WDCore.h"
#include    "../businessModule/typeMgr/WDBMTypeMgr.h"
#include    "../businessModule/typeMgr/WDBMTypeDesc.h"
#include    "../businessModule/WDBMBase.h"
#include    "../businessModule/WDBDBase.h"
#include    "../common/WDSnowflake.h"

WD_NAMESPACE_BEGIN

WDNode::WDNode()
{
    _remoteId   =   WDSnowflake::Create().data;
    _flags      =   { Flag::F_Visible, Flag::F_Update};
    _parent.reset();
    _aabb       =   Aabb3::Null();

    _pTypeDesc  =   nullptr;
    _pBDBase    =   nullptr;
    _pageId     =   ushort(-1);
    _attrAddr   =   uint(-1);

    _basicColor     =   Color(238, 153, 0);
    _customHighlightColor =   Color(255, 255, 0);
}
WDNode::WDNode(const std::string& name) : WDNode()
{
    this->setName(name);
}
WDNode::~WDNode()
{
    //删除子节点
    while (!_children.empty())
    {
        WDNode::SharedPtr pChild = _children.back();
        _children.pop_back();
        if (pChild != nullptr)
        {
            //先将父节点置空
            pChild->_parent.reset();
            //手动置空，如果其他地方没有对该指针强引用，这句调完之后就会释放pChild节点
            pChild = nullptr;
        }
    }

    // 销毁业务数据对象
    this->destroyBD();

    //通知观察者
    //！注意这里需要临时拷贝一份观察者列表进行通知
    //因为在通知过程中，可能会触发移除观察者
    Observers tmpObservers = this->observers();
    while (!tmpObservers.empty())
    {
        auto pObs = tmpObservers.back();
        if (pObs == nullptr)
            continue;

        pObs->onNodeDestroyAfter(this);

        tmpObservers.pop_back();
    }
}

std::string WDNode::name() const
{
    // 只有带业务数据对象的节点，才做
    auto pBMBase = this->getBMBase();
    if (pBMBase == nullptr)
        return this->srcName();
    // 已命名节点，直接返回名称
    if (this->isNamed())
        return this->srcName();
    // 父节点无效，返回空
    if (this->parent() == nullptr)
        return "";

    // 缓存一个流水号索引表
    static std::vector<std::string> numberIdxTable;
    if (numberIdxTable.empty())
    {
        static constexpr size_t idxCnt = 1000;
        numberIdxTable.reserve(idxCnt);
        for (int i = 0; i < idxCnt; ++i)
            numberIdxTable.push_back(ToString(i));
    }

    // 获取父节点，依次按规则拼接名称
    std::string rName;
    rName.reserve(128);
    // 先计算整个名称需要的内存大小
    auto pTNode = this;
    while (pTNode != nullptr)
    {
        auto strType = pTNode->type();
        // 根节点导出时用 * 代替名称
        if (strType == "WORL")
        {
            // 类型前缀 + 一个空格 + 一个正斜杠('/') + 节点名称
            rName += strType;
            rName += " /*";
            break;
        }
        const auto& sName = pTNode->srcName();
        if (sName.empty())
        {
            // 计算流水号
            int number = 1;
            if (pTNode->parent() != nullptr)
            {
                for (auto pChild : pTNode->parent()->children())
                {
                    // 直到当前节点
                    if (pChild.get() == pTNode)
                        break;
                    // 已命名节点，跳过
                    if (pChild->isNamed())
                        continue;
                    // 根据相同类型编号
                    if (pChild->type() == strType)
                        number++;
                }
            }

            // 类型前缀占 + 一个空格 + 流水号
            rName += strType;
            rName += " ";
            if (number < numberIdxTable.size() - 1)
                rName += numberIdxTable[number];
            else
                rName += ToString(number);
            // 一个空格 + 一个of字符串("of") + 一个空格
            rName += " of ";
        }
        else
        {
            // 类型前缀 + 一个空格 + 一个正斜杠('/') + 节点名称
            rName += strType;
            rName += " /";
            rName += sName;
            break;
        }
        pTNode = pTNode->parent().get();
    }
    return rName;
}

bool WDNode::setType(const WDBMBase& bmBase, const std::string_view& typeName)
{
#if WDNODE_BMBASE_ASSERT
    assert(_pTypeDesc == nullptr);
#endif
    if (typeName.empty())
    {
        assert(false && "类型名称无效!");
        return false;
    }
    // 类型相同，直接返回
    auto pTmpTypeDesc = bmBase.typeMgr().get(typeName);
    if (pTmpTypeDesc == nullptr)
    {
        assert(false && "类型名称无效!");
        return false;
    }
    if (pTmpTypeDesc == _pTypeDesc)
        return true;

    if (_pTypeDesc != nullptr)
    {
        // 类型不相同，无法重新设置
        assert(false && "节点已指定类型，无法重新设置类型!");
        return false;
    }
    else
    {
        // 设置类型描述
        _pTypeDesc = pTmpTypeDesc;
        return true;
    }
}
std::string_view WDNode::type() const
{
    if (_pTypeDesc == nullptr)
        return "";
    return _pTypeDesc->name();
}
ushort WDNode::typeId() const
{
    if (_pTypeDesc == nullptr)
        return NumLimits<ushort>::Max;
    return _pTypeDesc->id();
}
WDBMBase* WDNode::getBMBase() const
{
#if WDNODE_BMBASE_ASSERT
    assert(_pTypeDesc != nullptr);
#endif // WDNODE_BMBASE_ASSERT
    if (_pTypeDesc == nullptr)
        return nullptr;
    return &(_pTypeDesc->bmBase());
}

WDBDBase* WDNode::createBD()
{
    if (_pBDBase != nullptr)
    {
        delete _pBDBase;
        _pBDBase = nullptr;
    }

    if (_pTypeDesc == nullptr)
    {
        assert(false && "类型描述无效");
        return nullptr;
    }

    // 创建
    _pBDBase = _pTypeDesc->create(*this);

    return _pBDBase;
}
void WDNode::destroyBD()
{
    if (_pBDBase != nullptr)
    {
        delete _pBDBase;
        _pBDBase = nullptr;
    }
}

const WDBMAttrDesc* WDNode::getAttrDesc(const std::string_view& name) const
{
    auto pTDesc = this->getTypeDesc();
    if (pTDesc == nullptr)
        return nullptr;
    return pTDesc->get(name);
}

bool WDNode::setAttribute(const std::string_view& name, const WDBMAttrValue& value)
{
    auto pDesc = this->getTypeDesc();
    if (pDesc == nullptr)
    {
        assert(false);
        return false;
    }
    auto pAttr = pDesc->get(name);
    if (pAttr == nullptr)
    {
        //assert((!name.empty() ?  false : true) && "!注意: 指定了非空的属性名称，但是未查询到对应属性，可能是属性名称指定错误!");
        return false;
    }
    return pAttr->setValue(*this, value);
}
WDBMAttrValue WDNode::getAttribute(const std::string_view& name) const
{
    auto pTypeDesc = this->getTypeDesc();
    if (pTypeDesc == nullptr)
    {
        return WDBMAttrValue();
    }
    auto pAttrDesc = pTypeDesc->get(name);
    if (pAttrDesc == nullptr)
    {
        //assert((!name.empty() ? false : true) && "!注意: 指定了非空的属性名称，但是未查询到对应属性，可能是属性名称指定错误!");
        return WDBMAttrValue();
    }
    auto tValue = pAttrDesc->value(*this);
    return tValue;
}
WDBMAttrValueCRef WDNode::getAttributeCRef(const std::string_view& name) const
{
    auto pTypeDesc = this->getTypeDesc();
    if (pTypeDesc == nullptr)
    {
        return WDBMAttrValueCRef();
    }
    auto pAttrDesc = pTypeDesc->get(name);
    if (pAttrDesc == nullptr)
    {
        //assert((!name.empty() ? false : true) && "!注意: 指定了非空的属性名称，但是未查询到对应属性，可能是属性名称指定错误!");
        return WDBMAttrValueCRef();
    }
    auto tValue = pAttrDesc->valueCRef(*this);
    return tValue;
}

void WDNode::setDynamicAttribute(const std::string& name
    , const std::string& value)
{
    auto pBase = this->getBDBase();
    if (pBase == nullptr)
        return;
    pBase->setDynamicAttr(name, value);
}
std::string WDNode::dynamicAttribute(const std::string& name) const
{
    auto pBase = this->getBDBase();
    if (pBase == nullptr)
        return "";
    return pBase->dynamicAttr(name);
}
bool WDNode::containsDynamicAttribute(const std::string& name) const
{
    auto pBase = this->getBDBase();
    if (pBase == nullptr)
        return false;
    return pBase->containsDynamicAttr(name);
}

void WDNode::removeDynamicAttribute(const std::string& name)
{
    auto pBase = this->getBDBase();
    if (pBase == nullptr)
        return;
    return pBase->removeDynamicAttr(name);
}

void WDNode::clearDynamicAttributes()
{
    auto pBase = this->getBDBase();
    if (pBase == nullptr)
        return;
    return pBase->clearDynamicAttrs();
}
const WDNode::DynamicAttrs& WDNode::dynamicAttributes() const
{
    static const WDBDBase::DynamicAttrs sAttrs;
    auto pBase = this->getBDBase();
    if (pBase == nullptr)
        return sAttrs;
    return pBase->dynamicAttrs();
}

void WDNode::setParent(WDNode::SharedPtr pParent)
{
    auto pTParent = _parent.lock();
    if (pParent == pTParent)
        return;

    //先从之前的父节点移除
    if (pTParent != nullptr)
    {
        pTParent->removeChild(ToShared(this));
    }

    //添加到新的父节点
    if (pParent != nullptr)
    {
        pParent->addChild(ToShared(this));
    }
}
void WDNode::addChild(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr || this->isParent(*pNode))
        return;
    //通知观察者
    for (auto itr = this->_observers.begin(); itr != this->_observers.end(); ++itr)
    {
        (*itr)->onNodeAddChildBefore(ToShared(this), pNode);
    }

    pNode->_parent  = ToShared(this);
    SharedPtr pChildNode = pNode;
    _children.push_back(pChildNode);

    //通知观察者
    for (Observers::iterator itr = this->_observers.begin(); itr != this->_observers.end(); ++itr)
    {
        (*itr)->onNodeAddChildAfter(ToShared(this), pNode);
    }
}
void WDNode::insertChild(WDNode::SharedPtr pNode, WDNode::SharedPtr pNextNode)
{
    if (pNode == nullptr || this->isParent(*pNode))
        return;

    if (pNextNode == nullptr)
        return this->addChild(pNode);

    //查找插入索引
    int insertIndex = -1;
    for (size_t i = 0; i < this->childCount(); ++i)
    {
        if (pNextNode == this->childAt(i))
        {
            insertIndex = static_cast<int>(i);
            break;
        }
    }
    //索引无效，直接向后追加
    if (insertIndex < 0 || insertIndex >= this->childCount())
        return this->addChild(pNode);

    //通知观察者
    for (auto itr = this->_observers.begin(); itr != this->_observers.end(); ++itr)
    {
        (*itr)->onNodeInsertChildBefore(ToShared(this), pNode, pNextNode);
    }

    pNode->_parent  = WDNode::ToShared(this);
    WDNode::SharedPtr pChildNode = pNode;
    _children.insert(_children.begin() + insertIndex, pChildNode);

    //通知观察者
    for (Observers::iterator itr = this->_observers.begin(); itr != this->_observers.end(); ++itr)
    {
        (*itr)->onNodeInsertChildAfter(ToShared(this), pNode, pNextNode);
    }
}
void WDNode::removeChild(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr || !this->isParent(*pNode))
        return;

    for (NodesItr nodeItr = _children.begin(); nodeItr != _children.end(); ++nodeItr)
    {
        //保持节点再 removeChild调用完成前不被释放
        SharedPtr pTmpNode = (*nodeItr);

        if (pTmpNode != pNode)
            continue;

        //通知观察者
        for (auto itr = this->_observers.begin()
            ; itr != this->_observers.end()
            ; ++itr)
        {
            (*itr)->onNodeRemoveChildBefore(ToShared(this), pNode);
        }

        pNode->_parent.reset();
        nodeItr = _children.erase(nodeItr);

        //通知观察者
        for (auto itr = this->_observers.begin()
            ; itr != this->_observers.end()
            ; ++itr)
        {
            (*itr)->onNodeRemoveChildAfter(ToShared(this), pNode);
        }
        break;
    }

    // 因为子被移除了，因此需要重新计算父节点以及祖先节点的包围盒
    auto pParent = this;
    while(pParent != nullptr)
    {
        pParent->_aabb = pParent->transformdAabbSrc();
        for(auto pChild: pParent->_children)
        {
             pParent->_aabb.unions(pChild->aabb());
        }
        pParent = pParent->parent().get();
    }
}
bool WDNode::isAncestor(const WDNode& ancestor) const
{
    if (&ancestor == this)
        return false;
    auto pParent = ancestor.parent();
    while (pParent != nullptr)
    {
        if (pParent.get() == this)
            return true;
        pParent = pParent->parent();
    }
    return false;
}

WDNode::SharedPtr WDNode::prevBrother() const
{
    auto pTParent = _parent.lock();
    if (pTParent == nullptr)
        return nullptr;
    for (size_t i = 1; i < pTParent->_children.size(); ++i)
    {
        auto pNode = pTParent->_children[i];
        if (pNode.get() == this)
            return pTParent->_children[i - 1];
    }
    return nullptr;
}
WDNode::SharedPtr WDNode::nextBrother() const
{
    auto pTParent = _parent.lock();
    if (pTParent == nullptr)
        return nullptr;
    for (size_t i = 0; i < pTParent->_children.size() - 1; ++i)
    {
        auto pNode = pTParent->_children[i];
        if (pNode.get() == this)
            return pTParent->_children[i + 1];
    }
    return nullptr;
}

void WDNode::setMaterial(MaterialSPtr pMaterial)
{
    if (_pBDBase != nullptr)
        _pBDBase->_pMaterial = pMaterial;
}
WDNode::MaterialSPtr WDNode::material() const
{
    if (_pBDBase == nullptr)
        return nullptr;
    return _pBDBase->_pMaterial;
}

int WDNode::depth() const
{
    int depth = 0;
    auto pNode = this->parent();
    while (pNode != nullptr)
    {
        depth++;
        pNode = pNode->parent();
    }
    return depth;
}

bool WDNode::reorder(std::function<Nodes(const Nodes& children, WDNode& sender)> func)
{
    if (_children.empty())
        return true;

    auto nodes = func(_children, *this);

    //
    if (nodes.size() != _children.size())
    {
        assert(false && "注意,这里不允许修改子节点列表的内容,只能修改子节点列表的顺序!!!");
        return false;
    }
    // 如果节点顺序未被修改,则直接返回
    bool bChanged = false;
    for (int i = 0; i < _children.size(); ++i)
    {
        if (_children[i] != nodes[i])
        {
            bChanged = true;
            break;
        }
    }
    if (!bChanged)
        return true;
    // 如果节点内容被修改(即有增删原本的节点列表)返回false
    std::set<WDNode::SharedPtr> tempNodes;
    for (const auto& each : nodes)
        tempNodes.emplace(each);
    for (const auto& each : _children)
    {
        if (tempNodes.find(each) == tempNodes.end())
        {
            assert(false && "注意,这里不允许修改子节点列表的内容,只能修改子节点列表的顺序!!!");
            return false;
        }
    }

    std::swap(_children, nodes);

    for (auto& each : _observers)
    {
        if (each != nullptr)
            each->onNodeChildrenReordered(ToShared(this));
    }
    return true;
}

WDNodeTransform* WDNode::transformObject()
{
    if (_pBDBase == nullptr)
        return nullptr;
    return _pBDBase->transformSupporter();
}
const WDNodeTransform* WDNode::transformObject() const
{
    if (_pBDBase == nullptr)
        return nullptr;
    return _pBDBase->transformSupporter();
}

void WDNode::move(const DVec3& offset)
{
    auto p = transformObject();
    if (p == nullptr)
        return;

    auto pP = this->parent() != nullptr ? this->parent()->transformObject() : nullptr;
    p->move(*this, offset, pP);
    this->_flags.addFlag(F_Update);

    // 如果是空变换则继续变换当前节点的子节点
    if (p->isEmpty())
    {
        for (auto pChild : _children)
        {
            if (pChild == nullptr)
                continue;
            pChild->move(offset);
        }
    }
}
void WDNode::rotate(const DVec3& axis, double angle)
{
    auto p = transformObject();
    if (p == nullptr)
        return;

    auto pP = this->parent() != nullptr ? this->parent()->transformObject() : nullptr;
    p->rotate(*this, axis, angle, pP);
    this->_flags.addFlag(F_Update);

    // 如果是空变换则继续变换当前节点的子节点
    if (p->isEmpty())
    {
        for (auto pChild : _children)
        {
            if (pChild == nullptr)
                continue;
            pChild->rotate(axis, angle);
        }
    }
}
void WDNode::rotate(const DVec3& axis, double angle, const DVec3& center)
{
    auto p = transformObject();
    if (p == nullptr)
        return;

    auto pP = this->parent() != nullptr ? this->parent()->transformObject() : nullptr;
    p->rotate(*this, axis, angle, center, pP);
    this->_flags.addFlag(F_Update);

    // 如果是空变换则继续变换当前节点的子节点
    if (p->isEmpty())
    {
        for (auto pChild : _children)
        {
            if (pChild == nullptr)
                continue;
            pChild->rotate(axis, angle, center);
        }
    }
}
void RecursionMirror(WD::WDNode& node, const DVec3& normal, const DVec3& center)
{
    auto p = node.transformObject();
    if (p == nullptr)
        return;

    // 当前节点使用center做镜像
    p->mirror(node, normal, center);
    // 镜像需要继续变换当前节点的子节点
    // 当前节点变换不为空时子节点使用相对父节点坐标系的坐标原点作为center
    if (p->isEmpty())
    {
        for (auto pChild : node.children())
        {
            if (pChild == nullptr)
                continue;
            RecursionMirror(*pChild, normal, center);
        }
    }
    else
    {
        for (auto pChild : node.children())
        {
            if (pChild == nullptr)
                continue;
            RecursionMirror(*pChild, normal, DVec3::Zero());
        }
    }
}
void WDNode::mirror(const DVec3& normal, const DVec3& center)
{
    auto p = transformObject();
    if (p == nullptr)
        return;

    WD::DMat4 mMat = WD::DMat4::MakeMirror(normal);
    // 这里效仿PDMS的做法,先镜像然后反转x轴生成新矩阵并将节点旋转到目标矩阵,再固定以x为法线的面做递归镜像操作
    auto orientation = this->getAttribute("Orientation WRT World");
    if (orientation.valid())
    {
        DMat3 gMat = DMat3::FromQuat(orientation.toQuat());
        auto axisX = DVec3::Normalize(gMat * DVec3::AxisX());
        auto axisZ = DVec3::Normalize(gMat * DVec3::AxisZ());
        auto newAxisX = DVec3::Normalize(-(mMat * axisX));
        auto newAxisZ = DVec3::Normalize(mMat * axisZ);
        if (!DVec3::InTheSameDirection(axisX, newAxisX, 0.000001)
            || !DVec3::InTheSameDirection(axisZ, newAxisZ, 0.000001))
        {
            auto rQuat = DMat3::MakeRotationXZ(newAxisX, newAxisZ).toQuat();
            this->setAttribute("Orientation WRT World", rQuat);
        }
    }
    else if (!p->isEmpty())
    {
        DMat3 gMat = DMat4::ToMat3(globalRSTransform());
        auto axisX = DVec3::Normalize(gMat * DVec3::AxisX());
        auto axisZ = DVec3::Normalize(gMat * DVec3::AxisZ());
        auto newAxisX = DVec3::Normalize(-(mMat * axisX));
        auto newAxisZ = DVec3::Normalize(mMat * axisZ);
        if (!DVec3::InTheSameDirection(axisX, newAxisX, 0.000001)
            || !DVec3::InTheSameDirection(axisZ, newAxisZ, 0.000001))
        {
            auto rQuat = DMat3::MakeRotationXZ(newAxisX, newAxisZ).toQuat();
            this->rotate(rQuat.axis(), rQuat.angle(), center);
        }
    }

    DVec3 tCenter = center;
    DVec3 tNormal = normal;
    auto pParent = this->parent();
    while (pParent != nullptr)
    {
        auto pParentTransFormObj = pParent->transformObject();
        if (pParentTransFormObj->isEmpty())
        {
            pParent = pParent->parent();
            continue;
        }
        tCenter = pParent->globalTransform().inverse() * center;
        tNormal = (pParent->globalRSTransform().inverse() * tNormal).normalized();
        break;
    }

    // 
    RecursionMirror(*this, tNormal, tCenter);
}
void WDNode::scale(const DVec3& scaling)
{
    auto p = transformObject();
    if (p == nullptr)
        return;

    p->scale(*this, scaling);
    this->_flags.addFlag(F_Update);

    // 如果是空变换则继续变换当前节点的子节点
    if (p->isEmpty())
    {
        for (auto pChild : _children)
        {
            if (pChild == nullptr)
                continue;
            pChild->scale(scaling);
        }
    }
}

void WDNode::moveSelf(const DVec3& offset)
{
    auto p = transformObject();
    if (p == nullptr)
        return;
    if (p->isEmpty())
        return;

    // 正向移动自身，并逆向移动所有子节点
    {
        auto pP = this->parent() != nullptr ? this->parent()->transformObject() : nullptr;
        p->move(*this, offset, pP);
        this->_flags.addFlag(F_Update);
    }
    for (NodesItr itr = _children.begin(); itr != _children.end(); ++itr)
    {
        auto pChild = (*itr);
        if (pChild != nullptr)
        {
            pChild->move(-offset);
        }
    }
}
void WDNode::rotateSelf(const DVec3& axis, double angle)
{
    auto p = transformObject();
    if (p == nullptr)
        return;
    if (p->isEmpty())
        return;

    // 正向旋转自身，并逆向旋转所有子节点
    {
        auto pP = this->parent() != nullptr ? this->parent()->transformObject() : nullptr;
        p->rotate(*this, axis, angle, pP);
        this->_flags.addFlag(F_Update);
    }
    for (NodesItr itr = _children.begin(); itr != _children.end(); ++itr)
    {
        auto pChild = (*itr);
        if (pChild != nullptr)
        {
            pChild->rotate(axis, -angle, this->globalTranslation());
        }
    }
}
void WDNode::rotateSelf(const DVec3& axis, double angle, const DVec3& center)
{
    auto p = transformObject();
    if (p == nullptr)
        return;
    if (p->isEmpty())
        return;

    // 正向旋转自身，并逆向旋转所有子节点
    {
        auto pP = this->parent() != nullptr ? this->parent()->transformObject() : nullptr;
        p->rotate(*this, axis, angle, center, pP);
        this->_flags.addFlag(F_Update);
    }
    for (NodesItr itr = _children.begin(); itr != _children.end(); ++itr)
    {
        auto pChild = (*itr);
        if (pChild != nullptr)
        {
            pChild->rotate(axis, -angle, center);
        }
    }
}

void WDNode::update(bool bForceUpdate)
{
    //检查更新标记
    if (!bForceUpdate && !this->_flags.hasFlag(F_Update))
    {
        return;
    }

    //保存更新之前的aabb
    DAabb3 childOldAabb = this->aabb();

    //先更新自己包括自己的所有子节点(所有被更新节点的包围盒信息也会被更新)
    this->updateDataRecursion();

    //移除更新标记
    this->_flags.removeFlag(F_Update);

    // 再去更新父的包围盒,直至更新到根节点
    this->updateParentAabbData(childOldAabb);
}
void WDNode::triggerUpdate(bool bForceUpdate)
{
    //检查更新标记
    if (!bForceUpdate && !this->_flags.hasFlag(F_Update))
        return;
    // 获取到要触发更新的节点
    auto& node = this->getTriggerUpdateNode();
    // 前面已经检查了更新标记，所以这里总是强制更新
    node.update(true);
    //移除更新标记
    this->_flags.removeFlag(F_Update);
}
void WDNode::updateTransform()
{
    //更新节点矩阵数据
    this->updateMatrixData();
}
void WDNode::updateModel()
{
    if (_pBDBase != nullptr)
        _pBDBase->onModelUpdate(WD::Core(), *this);
}
void WDNode::updateParentAabbData()
{
    auto pParent = parent();
    while (pParent != nullptr)
    {
        // 更新父节点的aabb
        // 如果更新之后和之前的一样
        // 则不需要往上更新了
        auto preAabb = pParent->_aabb;// 之前的aabb
        pParent->_aabb = pParent->transformdAabbSrc();
        for (auto pChild : pParent->children())
        {
            if (pChild == nullptr)
                continue;
            pParent->_aabb.unions(pChild->aabb());
        }
        if (preAabb == pParent->_aabb)
        {
            // 说明当前节点aabb的改变没有影响
            break;
        }
        pParent = pParent->parent();
    }
}
WDNode& WDNode::getTriggerUpdateNode()
{
    if (_pBDBase == nullptr)
        return *this;
    else
        return _pBDBase->getTriggerUpdateNode(*this);
}

const WDKeyPoints* WDNode::keyPoints() const
{
    if (_pBDBase != nullptr)
        return _pBDBase->keyPoints();
    return nullptr;
}
const WDKeyPoint* WDNode::keyPoint(int number) const
{
    if (_pBDBase != nullptr)
        return _pBDBase->keyPoint(number);
    return nullptr;
}
const WDPLines* WDNode::pLines() const
{
    if (_pBDBase != nullptr)
        return _pBDBase->pLines();
    return nullptr;
}
const WDPLine* WDNode::pLine(const std::string_view& key) const
{
    if (_pBDBase != nullptr)
        return _pBDBase->pLine(key);
    return nullptr;
}

void WDNode::copy(const WDObject* pSrcObject)
{
    const WDNode* pSrcNode = dynamic_cast<const WDNode*>(pSrcObject);
    if (pSrcNode == nullptr)
        return;
    //父类拷贝
    WDObject::copy(pSrcObject);

    // 类型描述不一致
    if (this->_pTypeDesc != pSrcNode->_pTypeDesc)
    {
        // 重新指定类型描述
        this->_pTypeDesc = pSrcNode->_pTypeDesc;
        // 删除业务数据对象
        if (_pBDBase != nullptr)
        {
            delete _pBDBase;
            _pBDBase = nullptr;
        }
        // 重新克隆业务数据对象
        if (pSrcNode->_pBDBase != nullptr)
        {
            _pBDBase = pSrcNode->_pBDBase->clone(*this);
        }
    }
    else
    {
        if (_pBDBase != nullptr)
        {
            if (pSrcNode->_pBDBase != nullptr)
            {
                _pBDBase->copy(*(pSrcNode->_pBDBase));
            }
            else
            {
                delete _pBDBase;
                _pBDBase = nullptr;
            }
        }
        else
        {
            if (pSrcNode->_pBDBase != nullptr)
            {
                _pBDBase = pSrcNode->_pBDBase->clone(*this);
            }
        }
    }

    // 复制自身属性
    this->_aabb = pSrcNode->_aabb;

    // 节点的基础颜色
    this->_basicColor = pSrcNode->_basicColor;
    // 节点的自定义高亮颜色
    this->_customHighlightColor = pSrcNode->_customHighlightColor;

    this->_flags.addFlag(F_Update);
}
WDObject::SharedPtr WDNode::clone() const
{
    auto pNewNode = WDNode::MakeShared();
    pNewNode->copy(this);
    return pNewNode;
}
void WDNode::sendAttributeValueChanged(const std::string_view& name
    , const WDBMAttrValue& currValue
    , const WDBMAttrValue& prevValue)
{
    if (!flags().hasFlag(F_Editted))
        return;

    //通知观察者
    for (auto itr = this->_observers.begin()
        ; itr != this->_observers.end()
        ; ++itr)
    {
        (*itr)->onNodeAttributeValueChanged(name, currValue, prevValue, ToShared(this));
    }
}
void WDNode::sendDestroy()
{
    // 发送子节点的销毁通知
    for (auto pChild : _children)
    {
        if (pChild == nullptr)
            continue;
        pChild->sendDestroy();
    }

    //通知观察者
    //！注意这里需要临时拷贝一份观察者列表进行通知
    //因为在通知过程中，可能会触发移除观察者
    Observers tmpObservers = this->observers();
    while (!tmpObservers.empty())
    {
        auto pObs = tmpObservers.back();
        if (pObs == nullptr)
            continue;

        pObs->onNodeDestroyBefore(ToShared(this));

        tmpObservers.pop_back();
    }
}

Aabb3 WDNode::aabbSrc() const
{
    Aabb3 srcAabb = Aabb3::Null();
    if (_pBDBase != nullptr)
    {
        auto pGraphable = _pBDBase->graphableSupporter();
        if (pGraphable != nullptr)
        {
            DAabb3 aabb = pGraphable->gRenderAabb();
            srcAabb.unions(aabb);
        }
    }
    return srcAabb;
}
void WDNode::onNodeUpdateBefore()
{
}
void WDNode::onNodeUpdateAfter()
{
}

void WDNode::updateDataRecursion()
{
    //通知观察者
    for (auto itr = this->_observers.begin()
        ; itr != this->_observers.end()
        ; ++itr)
    {
        (*itr)->onNodeUpdateBefore(ToShared(this));
    }

    //更新之前通知子类
    this->onNodeUpdateBefore();

    // 
    if (_pBDBase != nullptr)
        _pBDBase->onModelUpdateBeforeChildren(WD::Core(), *this);

    // 更新变换矩阵
    this->updateTransform();

    // 更新完矩阵之后，计算行列式，通过行列式是否小于0来判断节点是否带有镜像数据
    auto pTsfmObj = this->transformObject();
    if (pTsfmObj != nullptr)
    {
        const auto det = DMat4::ToMat3(pTsfmObj->globalTransform()).determinant();
        _flags.setFlag(Flag::F_Mirror, det < 0.0);
    }

    //递归更新子节点，同时使用子节点的包围盒重新计算父节点包围盒
    this->_aabb = DAabb3::Null();
    for (NodesItr itr = _children.begin(); itr != _children.end(); ++itr)
    {
        auto pNode = (*itr);
        if (pNode != nullptr)
        {
            pNode->_flags.addFlag(F_Update);
            pNode->updateDataRecursion();
            pNode->_flags.removeFlag(F_Update);
            this->_aabb.unions(pNode->aabb());
        }
    }

    // 调用业务对象的模型更新, 这里需要保证子节点的模型更新完成之后再去更新
    // 因为某些父节点更新模型时，会依赖子节点的模型
    //  比如: 
    //      分支更新计算直管时，需要保证分支下管件的所有模型已被更新
    //      开孔时，需要子节点(负实体/负型集)模型生成完成后，再从父节点获取子节点的负实体进行开孔
    this->updateModel();

    // 再将自己的包围盒合并进去
    this->_aabb.expandByAabb(this->transformdAabbSrc());

    // 更新之后通知子类
    this->onNodeUpdateAfter();

    //通知观察者
    for (auto itr = this->_observers.begin()
        ; itr != this->_observers.end()
        ; ++itr)
    {
        (*itr)->onNodeUpdateAfter(ToShared(this));
    }
}
void WDNode::updateMatrixData()
{
    auto p = transformObject();
    if (p == nullptr)
        return;
    p->update(*this);
}
DAabb3 WDNode::transformdAabbSrc() const
{
    DAabb3 rAabb = this->aabbSrc();
    if (!rAabb.isNull())
        rAabb.transform(this->globalTransform());
    return rAabb;
}
void WDNode::updateParentAabbData(const DAabb3& aabbBeforeUpdate)
{
    // 如果ToShared(this)出现崩溃，请先检查节点是否以智能指针的方式创建的，如果不是，请将节点调整为以智能指针的方式创建
    auto pThis   = ToShared(this); 
    auto pParent = pThis->parent();

    Aabb3 childAabbBefore = aabbBeforeUpdate;
    while (pParent != nullptr)
    {
        const auto& childAabb = pThis->aabb();
        // 包围盒重新计算之前的包围盒一致，不需要继续往上算了
        if (childAabb == childAabbBefore)
            break;
        // 保留父计算之前的Aabb
        childAabbBefore = pParent->aabb();
        // 重新计算父的Aabb
        pParent->_aabb  = pParent->transformdAabbSrc();
        for (auto pChild : pParent->_children)
        {
            if (pChild == nullptr)
                continue;
            pParent->_aabb.unions(pChild->aabb());
        }

        pThis   = pParent;
        pParent = pThis->parent();
    }
}

WD_NAMESPACE_END
