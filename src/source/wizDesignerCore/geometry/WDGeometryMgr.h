#pragma once
#include "../common/WDSharedObjectPool.h"
#include "standardPrimitives/WDGeometryBox.h"
#include "standardPrimitives/WDGeometryCircularTorus.h"
#include "standardPrimitives/WDGeometryCone.h"
#include "standardPrimitives/WDGeometryCylinder.h"
#include "standardPrimitives/WDGeometryDish.h"
#include "standardPrimitives/WDGeometryEllipsoid.h"
#include "standardPrimitives/WDGeometryPyramid.h"
#include "standardPrimitives/WDGeometryRectangularTorus.h"
#include "standardPrimitives/WDGeometrySlopedCylinder.h"
#include "standardPrimitives/WDGeometrySnout.h"
#include "standardPrimitives/WDGeometrySphere.h"
#include "standardPrimitives/WDGeometryExtrusion.h"
#include "standardPrimitives/WDGeometryRevolution.h"
#include "standardPrimitives/WDGeometryLofting.h"
#include "WDGeometryPolyhedron.h"

WD_NAMESPACE_BEGIN

class WDCore;
class WDGeometryMgrPrivate;
/**
* @brief 几何体管理
*/
class WD_API WDGeometryMgr : public WDObject
{
    WD_DECL_OBJECT(WDGeometryMgr)
public:
    /**
     * @brief 几何体标志
     */
    using GFlag = WDGeometry::GFlag;
    /**
     * @brief 几何体标志
     */
    using GFlags = WDGeometry::GFlags;
public:
    WDGeometryMgr(WDCore& core);
    ~WDGeometryMgr();
public:
    /**
     * @brief 创建Box
     * @param xLength 
     * @param yLength 
     * @param zLength 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryBox::SharedPtr createBox(float xLength
        , float yLength
        , float zLength
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max));
    /**
     * @brief 创建带有位置旋转变换的Box
     * @param position 
     * @param rotation 
     * @param xLength 
     * @param yLength 
     * @param zLength 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryBox::SharedPtr createBox(const DVec3& position
        , const DQuat& rotation
        , float xLength
        , float yLength
        , float zLength
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max));
    /**
     * @brief 创建CircularTorus
     * @param insideRadius 
     * @param outsideRadius 
     * @param angle 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryCircularTorus::SharedPtr createCircularTorus(float insideRadius 
        , float outsideRadius
        , float angle
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的CircularTorus
     * @param position 
     * @param rotation 
     * @param insideRadius 
     * @param outsideRadius 
     * @param angle 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryCircularTorus::SharedPtr createCircularTorus(const DVec3& position
        , const DQuat& rotation
        , float insideRadius
        , float outsideRadius
        , float angle
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Cone
     * @param topDiameter 
     * @param bottomDiameter 
     * @param height 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryCone::SharedPtr createCone(float topDiameter
        , float bottomDiameter
        , float height
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Cone
     * @param position 
     * @param rotation 
     * @param topDiameter 
     * @param bottomDiameter 
     * @param height 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryCone::SharedPtr createCone(const DVec3& position
        , const DQuat& rotation
        , float topDiameter
        , float bottomDiameter
        , float height
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Cylinder
     * @param diameter 
     * @param height 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryCylinder::SharedPtr createCylinder(float diameter
        , float height
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Cylinder
     * @param position 
     * @param rotation 
     * @param diameter 
     * @param height 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryCylinder::SharedPtr createCylinder(const DVec3& position
        , const DQuat& rotation
        , float diameter
        , float height
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Dish
     * @param diameter 
     * @param radius 
     * @param height 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryDish::SharedPtr createDish(float diameter
        , float radius
        , float height
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Dish
     * @param position 
     * @param rotation 
     * @param diameter 
     * @param radius 
     * @param height 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryDish::SharedPtr createDish(const DVec3& position
        , const DQuat& rotation
        , float diameter
        , float radius
        , float height
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Ellipsoid
     * @param xDiameter 
     * @param yDiameter 
     * @param zDiameter 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryEllipsoid::SharedPtr createEllipsoid(float xDiameter
        , float yDiameter
        , float zDiameter
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Ellipsoid
     * @param position 
     * @param rotation 
     * @param xDiameter 
     * @param yDiameter 
     * @param zDiameter 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryEllipsoid::SharedPtr createEllipsoid(const DVec3& position
        , const DQuat& rotation
        , float xDiameter
        , float yDiameter
        , float zDiameter
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Extrusion
     * @param height 
     * @param direction 
     * @param justification 
     * @param loop 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryExtrusion::SharedPtr createExtrusion(float height
        , const FVec3& direction
        , const WDGeometryExtrusion::Justification justification
        , const std::vector<FVec3>& loop
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Extrusion
     * @param position 
     * @param rotation 
     * @param height 
     * @param direction 
     * @param justification 
     * @param loop 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryExtrusion::SharedPtr createExtrusion(const DVec3& position
        , const DQuat& rotation
        , const float height
        , const FVec3& direction
        , const WDGeometryExtrusion::Justification justification
        , const std::vector<FVec3>& loop
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Lofting
     * @param sDirection 
     * @param eDirection 
     * @param plaxisPos 
     * @param loopS 
     * @param loopE 
     * @param curve 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryLofting::SharedPtr createLofting(const FVec3& sDirection
        , const FVec3& eDirection
        , const FVec3& plaxisPos
        , const FVec3Vector& loopS
        , const FVec3Vector& loopE
        , const FVec3Vector& curve
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Lofting
     * @param position 
     * @param rotation 
     * @param sDirection 
     * @param eDirection 
     * @param plaxisPos 
     * @param loopS 
     * @param loopE 
     * @param curve 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryLofting::SharedPtr createLofting(const DVec3& position
        , const DQuat& rotation
        , const FVec3& sDirection
        , const FVec3& eDirection
        , const FVec3& plaxisPos
        , const FVec3Vector& loopS
        , const FVec3Vector& loopE
        , const FVec3Vector& curve
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Pyramid
     * @param xTop 
     * @param yTop 
     * @param xBottom 
     * @param yBottom 
     * @param height 
     * @param xOffset 
     * @param yOffset 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryPyramid::SharedPtr createPyramid(float xTop
        , float yTop
        , float xBottom
        , float yBottom
        , float height
        , float xOffset
        , float yOffset
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max));
    /**
     * @brief 创建带有位置旋转变换的Pyramid
     * @param position 
     * @param rotation 
     * @param xTop 
     * @param yTop 
     * @param xBottom 
     * @param yBottom 
     * @param height 
     * @param xOffset 
     * @param yOffset 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryPyramid::SharedPtr createPyramid(const DVec3& position
        , const DQuat& rotation
        , float xTop
        , float yTop
        , float xBottom
        , float yBottom
        , float height
        , float xOffset
        , float yOffset
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max));
    /**
     * @brief 创建RectangularTorus
     * @param insideRadius 
     * @param outsideRadius 
     * @param height 
     * @param angle 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryRectangularTorus::SharedPtr createRectangularTorus(float insideRadius
        , float outsideRadius
        , float height
        , float angle
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的RectangularTorus
     * @param position 
     * @param rotation 
     * @param insideRadius 
     * @param outsideRadius 
     * @param height 
     * @param angle 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryRectangularTorus::SharedPtr createRectangularTorus(const DVec3& position
        , const DQuat& rotation
        , float insideRadius
        , float outsideRadius
        , float height
        , float angle
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Revolution
     * @param center 
     * @param axis 
     * @param angle 
     * @param loop 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryRevolution::SharedPtr createRevolution(const FVec3& center
        , const FVec3& axis
        , const float angle
        , const std::vector<FVec3>& loop
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Revolution
     * @param position 
     * @param rotation 
     * @param center 
     * @param axis 
     * @param angle 
     * @param loop 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryRevolution::SharedPtr createRevolution(const DVec3& position
        , const DQuat& rotation
        , const FVec3& center
        , const FVec3& axis
        , const float angle
        , const std::vector<FVec3>& loop
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建SlopedCylinder
     * @param diameter 
     * @param height 
     * @param topXShear 
     * @param topYShear 
     * @param bottomXShear 
     * @param bottomYShear 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometrySlopedCylinder::SharedPtr createSlopedCylinder(float diameter
        , float height
        , float topXShear
        , float topYShear
        , float bottomXShear
        , float bottomYShear
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的SlopedCylinder
     * @param position 
     * @param rotation 
     * @param diameter 
     * @param height 
     * @param topXShear 
     * @param topYShear 
     * @param bottomXShear 
     * @param bottomYShear 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometrySlopedCylinder::SharedPtr createSlopedCylinder(const DVec3& position
        , const DQuat& rotation
        , float diameter
        , float height
        , float topXShear
        , float topYShear
        , float bottomXShear
        , float bottomYShear
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Snout
     * @param topDiameter 
     * @param bottomDiameter 
     * @param height 
     * @param xOffset 
     * @param yOffset 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometrySnout::SharedPtr createSnout(float topDiameter
        , float bottomDiameter
        , float height
        , float xOffset
        , float yOffset
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Snout
     * @param position 
     * @param rotation 
     * @param topDiameter 
     * @param bottomDiameter 
     * @param height 
     * @param xOffset 
     * @param yOffset 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometrySnout::SharedPtr createSnout(const DVec3& position
        , const DQuat& rotation
        , float topDiameter
        , float bottomDiameter
        , float height
        , float xOffset
        , float yOffset
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建Sphere
     * @param diameter 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometrySphere::SharedPtr createSphere(float diameter
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());
    /**
     * @brief 创建带有位置旋转变换的Sphere
     * @param position 
     * @param rotation 
     * @param diameter 
     * @param lodSelection 
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometrySphere::SharedPtr createSphere(const DVec3& position
        , const DQuat& rotation
        , float diameter
        , GFlags flags = GFlag::GF_None
        , const IVec2& level = IVec2(NumLimits<int>::Min, NumLimits<int>::Max)
        , const MeshLODSelection& lodSelection = MeshLODSelection());


public:
    /**
     * @brief 指定多面体网格, 创建多面体并添加到几何库中
     * @param pMesh 多面体网格
     * @return 成功返回对应对象指针，失败返回nullptr
     */
    WDGeometryPolyhedron::SharedPtr addPolyhedron(WDMesh::SharedPtr pMesh);
    /**
     * @brief 指定多面体网格，直接创建多面体
     * @param pMesh 多面体网格
     * @return 返回多面体指针
    */
    WDGeometryPolyhedron::SharedPtr createPolyhedron(WDMesh::SharedPtr pMesh);
    /**
     * @brief 更新多面体查询缓存
    */
    void updatePolyhedronQueryCatch();
    /**
     * @brief 根据Guid, 从缓存中查询多面体对象
     * @param uuid Guid
     * @param removeFromStrongRefSet 查询到结果后, 是否从强引用列表中移除, 这里移除就代表外部要强引用该多面体对象
    */
    WDGeometry::SharedPtr queryPolyhedronFromCatch(const WDUuid& uuid
        , bool removeFromStrongRefSet = true);
    /**
     * @brief 清除多面体查询缓存
    */
    void clearPolyhedronQueryCatch();

    /**
     * @brief 清空所有缓存(谨慎调用)
     *  包括所有类型基本体(以及多面体)的共享对象池
    */
    void clear();
public:
    /**
     * @brief 进度报告回调
    * @param progress 进度值
    * @param text 进度文本
    * @return 是否中断此次加载并退出
    */
    using FuncProgress = std::function<bool(double progress, const char* text)>;
    /**
     * @brief 写入几何体数据
     *  !注意: 目前只写入了多面体
     * @param writer 
     * @return 写入字节数，如果返回0，表示没有数据被写入
    */
    size_t write(WDWriter& writer);
    /**
     * @brief 写入几何体数据
     *  !注意: 目前只写入了多面体
     * @param file 文件全路径
     * @return 写入字节数，如果返回0，表示没有数据被写入
    */
    size_t save(const std::string_view& file);
    /**
     * @brief 读取几何体数据
     *  !注意: 目前只写入了多面体
     * @param writer
     * @return 读取字节数，如果返回0，表示没有数据被读取
    */
    size_t read(WDReader& reader, FuncProgress funcProgress = FuncProgress());
    /**
     * @brief 读取几何体数据
     *  !注意: 目前只写入了多面体
     * @param file 文件全路径
     * @param progressCallback 进度回调
     * @return 读取字节数，如果返回0，表示没有数据被读取
    */
    size_t load(const std::string_view& file, FuncProgress funcProgress = FuncProgress());
private:
    WDGeometryMgrPrivate* _p;
};

WD_NAMESPACE_END


