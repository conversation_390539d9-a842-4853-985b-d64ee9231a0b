#include "WDGeometryMgr.h"
#include "../WDObjectCreator.h"
#include "../WDCore.h"
#include "../WDTranslate.h"

WD_NAMESPACE_BEGIN;

struct alignas(8) GeometryFileHeader 
{
public:
    static constexpr std::uint64_t SMagicNumber = std::numeric_limits<std::uint64_t>::max();
public:
    // 魔数 0xFFFFFFFF
    std::uint64_t magicNumber;
    // 文件版本
    std::uint64_t version;
    // 文件大小, 包含文件头的大小
    std::uint64_t fileSize;
    // 最后一次生成文件的系统名称
    char systemName[16];
    // 预留
    char reserve[64];
public:
    GeometryFileHeader() 
    {
        magicNumber = SMagicNumber;
        version = 1;
        fileSize = 0;
        // 初始化整个数组为0
        memset(systemName, 0, sizeof(systemName));
        sprintf_s(systemName, sizeof(systemName), "Unknown");
#if WD_PLATFORM == WD_PLATFORM_WIN32
        sprintf_s(systemName, sizeof(systemName), "Windows");
#elif WD_PLATFORM == WD_PLATFORM_LINUX
        sprintf_s(systemName, sizeof(systemName), "Linux");
#elif WD_PLATFORM == WD_PLATFORM_APPLE
        sprintf_s(systemName, sizeof(systemName), "MacOS");
#endif
        memset(reserve, 0, sizeof(reserve));
    }
    /**
     * @brief 校验魔数
     */
    inline bool checkMagicNumber() const 
    {
        return magicNumber == SMagicNumber;
    }
};

/**
 * @brief 参数化基本体共享对象池
 * @tparam GType 基本体类型
 * @tparam ...Params 基本体参数表
 */
template <typename GType, typename ...Params>
class GPoolT 
{
    static_assert(std::is_base_of_v<WDGeometry, GType>, "GType is not a subclass of WDGeometry!");
private:
    using GFlags    = WDGeometry::GFlags;
    using Key       = std::tuple<uint, IVec2, Params...>;
    using TRKey     = std::tuple<DVec3, DQuat, uint, IVec2, Params...>;
    using Pool      = WDSharedObjectPoolT<Key, GType>;
    using TRPool    = WDSharedObjectPoolT<TRKey, GType>;
public:
    std::shared_ptr<GType> create(GFlags flags, const IVec2& level, const Params& ...params)
    {
        Key key(flags.data(), level, params...);
        auto ret = _pool.query(key);
        if (ret != nullptr)
            return ret;
        auto pGeom = GType::MakeShared(params...);
        pGeom->gFlags() = flags;
        pGeom->setLevel(level);
        // 缓存到共享池中
        _pool.add(std::move(key), pGeom);
        return pGeom;
    }
    std::shared_ptr<GType> create(const DVec3& translation
        , const DQuat& rotation
        , GFlags flags
        , const IVec2& level
        , const Params& ...params)
    {
        TRKey key(translation, rotation, flags.data(), level, params...);
        auto ret = _trPool.query(key);
        if (ret != nullptr)
            return ret;
        auto pGeom = GType::MakeShared(params...);
        pGeom->setPosition(translation);
        pGeom->setRotation(rotation);
        pGeom->gFlags() = flags;
        pGeom->setLevel(level);
        // 缓存到共享池中
        _trPool.add(std::move(key), pGeom);
        return pGeom;
    }
    void clear()
    {
        _pool.clear();
    }
private:
    Pool _pool;
    TRPool _trPool;
};

class WDGeometryMgrPrivate 
{
public:
    WDCore& _core;
    WDGeometryMgr& _d;

    GPoolT<WDGeometryBox
        , float, float, float> _boxPool;

    GPoolT<WDGeometryCircularTorus
        , float, float, float, MeshLODSelection> _circularTorusPool;
    
    GPoolT<WDGeometryCone
        , float, float, float, MeshLODSelection> _conePool;

    GPoolT<WDGeometryCylinder
        , float, float, MeshLODSelection> _cylinderPool;

    GPoolT<WDGeometryDish
        , float, float, float, MeshLODSelection> _dishPool;

    GPoolT<WDGeometryEllipsoid
        , float, float, float, MeshLODSelection> _ellipsoidPool;

    GPoolT<WDGeometryExtrusion
        , float, FVec3, WDGeometryExtrusion::Justification, FVec3Vector, MeshLODSelection> _extrusionPool;

    GPoolT<WDGeometryLofting
        , FVec3, FVec3, FVec3, FVec3Vector, FVec3Vector, FVec3Vector, MeshLODSelection> _loftingPool;

    GPoolT<WDGeometryPyramid
        , float, float, float, float, float, float, float> _pyramidPool;

    GPoolT<WDGeometryRectangularTorus
        , float, float, float, float, MeshLODSelection> _rectangularTorusPool;

    GPoolT<WDGeometryRevolution
        , FVec3, FVec3, float, FVec3Vector, MeshLODSelection> _revolutionPool;

    GPoolT<WDGeometrySlopedCylinder
        , float, float, float, float, float, float, MeshLODSelection> _slopedCylinderPool;

    GPoolT<WDGeometrySnout
        , float, float, float, float, float, MeshLODSelection> _snoutPool;

    GPoolT<WDGeometrySphere
        , float, MeshLODSelection> _spherePool;

    // 多面体对象共享池(库),来源于第三方的网格数据,存储在这里,需要跟随项目进行序列化
    using GPolyhedronPool = WDSharedObjectPoolT<WDMD5::Key, WDGeometryPolyhedron>;
    GPolyhedronPool _polyhedronPool;
    // 查询缓存
    std::map<WDUuid, WDGeometryPolyhedron::WeakPtr> _polyhedronQueryCatch;
    // 强引用列表, 当几何体(一般是多面体)从文件读入之后，由于添加到对象池中时，对象池为弱引用
    // 为了防止该几何体被自动释放,这里暂时使用一个强引用保存,当这个几何体被查找到时,将从这个强引用列表中移除
    std::set<WDGeometryPolyhedron::SharedPtr> _polyhedronStrongRefGeos;
    // 额外的映射表, 当没有边线的多面体在加载时自动生成边线后，如果已经存在相同的数据，则做一次额外映射
    // 以保证数据能正常合并且引用该数据的节点还能正常引用到正确数据
    std::map<WDUuid, WDGeometryPolyhedron::WeakPtr> _polyhedronMap;
public:
    WDGeometryMgrPrivate(WDCore& core, WDGeometryMgr& d) : _core(core), _d(d) 
    {

    }
};

WDGeometryMgr::WDGeometryMgr(WDCore& core)
{
    _p = new WDGeometryMgrPrivate(core, *this);
}
WDGeometryMgr::~WDGeometryMgr()
{
    clear();

    delete _p;
    _p = nullptr;
}

WDGeometryBox::SharedPtr WDGeometryMgr::createBox(float xLength, float yLength, float zLength
    , GFlags flags
    , const IVec2& level)
{
    return _p->_boxPool.create(flags, level, xLength, yLength, zLength);
}
WDGeometryBox::SharedPtr WDGeometryMgr::createBox(const DVec3& position
    , const DQuat& rotation
    , float xLength
    , float yLength
    , float zLength
    , GFlags flags
    , const IVec2& level)
{
    return _p->_boxPool.create(position, rotation, flags, level, xLength, yLength, zLength);
}

WDGeometryCircularTorus::SharedPtr WDGeometryMgr::createCircularTorus(float insideRadius
    , float outsideRadius
    , float angle
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_circularTorusPool.create(flags, level, insideRadius, outsideRadius, angle, lodSelection);
}
WDGeometryCircularTorus::SharedPtr WDGeometryMgr::createCircularTorus(const DVec3& position
    , const DQuat& rotation
    , float insideRadius
    , float outsideRadius
    , float angle
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_circularTorusPool.create(position, rotation, flags, level, insideRadius, outsideRadius, angle, lodSelection);
}

WDGeometryCone::SharedPtr WDGeometryMgr::createCone(float topDiameter
    , float bottomDiameter
    , float height
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_conePool.create(flags, level, topDiameter, bottomDiameter, height, lodSelection);
}
WDGeometryCone::SharedPtr WDGeometryMgr::createCone(const DVec3& position
    , const DQuat& rotation
    , float topDiameter
    , float bottomDiameter
    , float height
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_conePool.create(position, rotation, flags, level, topDiameter, bottomDiameter, height, lodSelection);
}

WDGeometryCylinder::SharedPtr WDGeometryMgr::createCylinder(float diameter
    , float height
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_cylinderPool.create(flags, level, diameter, height, lodSelection);
}
WDGeometryCylinder::SharedPtr WDGeometryMgr::createCylinder(const DVec3& position
    , const DQuat& rotation
    , float diameter
    , float height
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_cylinderPool.create(position, rotation, flags, level, diameter, height, lodSelection);
}


WDGeometryDish::SharedPtr WDGeometryMgr::createDish(float diameter
    , float radius
    , float height
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_dishPool.create(flags, level, diameter, radius, height, lodSelection);
}
WDGeometryDish::SharedPtr WDGeometryMgr::createDish(const DVec3& position
    , const DQuat& rotation
    , float diameter
    , float radius
    , float height
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_dishPool.create(position, rotation, flags, level, diameter, radius, height, lodSelection);
}

WDGeometryEllipsoid::SharedPtr WDGeometryMgr::createEllipsoid(float xDiameter
    , float yDiameter
    , float zDiameter
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_ellipsoidPool.create(flags, level, xDiameter, yDiameter, zDiameter, lodSelection);
}
WDGeometryEllipsoid::SharedPtr WDGeometryMgr::createEllipsoid(const DVec3& position
    , const DQuat& rotation
    , float xDiameter
    , float yDiameter
    , float zDiameter
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_ellipsoidPool.create(position, rotation, flags, level, xDiameter, yDiameter, zDiameter, lodSelection);
}

WDGeometryExtrusion::SharedPtr WDGeometryMgr::createExtrusion(float height
    , const FVec3& direction
    , const WDGeometryExtrusion::Justification justification
    , const std::vector<FVec3>& loop
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_extrusionPool.create(flags, level, height, direction, justification, loop, lodSelection);
}
WDGeometryExtrusion::SharedPtr WDGeometryMgr::createExtrusion(const DVec3& position
    , const DQuat& rotation
    , const float height
    , const FVec3& direction
    , const WDGeometryExtrusion::Justification justification
    , const std::vector<FVec3>& loop
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_extrusionPool.create(position, rotation, flags, level, height, direction, justification, loop, lodSelection);
}

WDGeometryLofting::SharedPtr WDGeometryMgr::createLofting(const FVec3& sDirection
    , const FVec3& eDirection
    , const FVec3& plaxisPos
    , const FVec3Vector& loopS
    , const FVec3Vector& loopE
    , const FVec3Vector& curve
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_loftingPool.create(flags, level, sDirection, eDirection, plaxisPos, loopS, loopE, curve, lodSelection);
}
WDGeometryLofting::SharedPtr WDGeometryMgr::createLofting(const DVec3& position
    , const DQuat& rotation
    , const FVec3& sDirection
    , const FVec3& eDirection
    , const FVec3& plaxisPos
    , const FVec3Vector& loopS
    , const FVec3Vector& loopE
    , const FVec3Vector& curve
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_loftingPool.create(position, rotation, flags, level, sDirection, eDirection, plaxisPos, loopS, loopE, curve, lodSelection);
}

WDGeometryPyramid::SharedPtr WDGeometryMgr::createPyramid(float xTop
    , float yTop
    , float xBottom
    , float yBottom
    , float height
    , float xOffset
    , float yOffset
    , GFlags flags
    , const IVec2& level)
{
    return _p->_pyramidPool.create(flags
        , level
        , xTop, yTop
        , xBottom, yBottom
        , height
        , xOffset, yOffset);
}
WDGeometryPyramid::SharedPtr WDGeometryMgr::createPyramid(const DVec3& position
    , const DQuat& rotation
    , float xTop
    , float yTop
    , float xBottom
    , float yBottom
    , float height
    , float xOffset
    , float yOffset
    , GFlags flags
    , const IVec2& level)
{
    return _p->_pyramidPool.create(position, rotation
        , flags
        , level
        , xTop, yTop
        , xBottom, yBottom
        , height
        , xOffset, yOffset);
}

WDGeometryRectangularTorus::SharedPtr WDGeometryMgr::createRectangularTorus(float insideRadius
    , float outsideRadius
    , float height
    , float angle
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_rectangularTorusPool.create(flags, level, insideRadius, outsideRadius, height, angle, lodSelection);
}
WDGeometryRectangularTorus::SharedPtr WDGeometryMgr::createRectangularTorus(const DVec3& position
    , const DQuat& rotation
    , float insideRadius
    , float outsideRadius
    , float height
    , float angle
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_rectangularTorusPool.create(position, rotation, flags, level, insideRadius, outsideRadius, height, angle, lodSelection);
}

WDGeometryRevolution::SharedPtr WDGeometryMgr::createRevolution(const FVec3& center
    , const FVec3& axis
    , const float angle
    , const std::vector<FVec3>& loop
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_revolutionPool.create(flags, level, center, axis, angle, loop, lodSelection);
}
WDGeometryRevolution::SharedPtr WDGeometryMgr::createRevolution(const DVec3& position
    , const DQuat& rotation
    , const FVec3& center
    , const FVec3& axis
    , const float angle
    , const std::vector<FVec3>& loop
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_revolutionPool.create(position, rotation, flags, level, center, axis, angle, loop, lodSelection);
}

WDGeometrySlopedCylinder::SharedPtr WDGeometryMgr::createSlopedCylinder(float diameter
    , float height
    , float topXShear
    , float topYShear
    , float bottomXShear
    , float bottomYShear
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_slopedCylinderPool.create(flags
        , level
        , diameter, height
        , topXShear, topYShear
        , bottomXShear, bottomYShear
        , lodSelection);
}
WDGeometrySlopedCylinder::SharedPtr WDGeometryMgr::createSlopedCylinder(const DVec3& position
    , const DQuat& rotation
    , float diameter
    , float height
    , float topXShear
    , float topYShear
    , float bottomXShear
    , float bottomYShear
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_slopedCylinderPool.create(position, rotation
        , flags
        , level
        , diameter, height
        , topXShear, topYShear
        , bottomXShear, bottomYShear
        , lodSelection);
}

WDGeometrySnout::SharedPtr WDGeometryMgr::createSnout(float topDiameter
    , float bottomDiameter
    , float height
    , float xOffset
    , float yOffset
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_snoutPool.create(flags
        , level
        , topDiameter, bottomDiameter
        , height
        , xOffset, yOffset
        , lodSelection);
}
WDGeometrySnout::SharedPtr WDGeometryMgr::createSnout(const DVec3& position
    , const DQuat& rotation
    , float topDiameter
    , float bottomDiameter
    , float height
    , float xOffset
    , float yOffset
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_snoutPool.create(position, rotation
        , flags
        , level
        , topDiameter, bottomDiameter
        , height
        , xOffset, yOffset
        , lodSelection);
}

WDGeometrySphere::SharedPtr WDGeometryMgr::createSphere(float diameter
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_spherePool.create(flags, level, diameter, lodSelection);
}
WDGeometrySphere::SharedPtr WDGeometryMgr::createSphere(const DVec3& position
    , const DQuat& rotation
    , float diameter
    , GFlags flags
    , const IVec2& level
    , const MeshLODSelection& lodSelection)
{
    return _p->_spherePool.create(position, rotation
        , flags
        , level
        , diameter
        , lodSelection);
}

WDGeometryPolyhedron::SharedPtr WDGeometryMgr::addPolyhedron(WDMesh::SharedPtr pMesh)
{
    auto pPolyhedron = WDGeometryPolyhedron::MakeShared(pMesh);
    // 如果网格的aabb为空，则自动计算aabb
    if (pMesh != nullptr && pMesh->aabb().isNull())
        pMesh->computeAabb();
    // 获取数据的md5
    auto md5Key = pPolyhedron->md5Key();
    // 从先从共享池中根据md5查找
    auto pRet = _p->_polyhedronPool.query(md5Key);
    if (pRet != nullptr)
        return pRet;
    // 未查找到，添加到共享池中
    _p->_polyhedronPool.add(md5Key, pPolyhedron);
    return pPolyhedron;
}

WDGeometryPolyhedron::SharedPtr WDGeometryMgr::createPolyhedron(WDMesh::SharedPtr pMesh)
{
    auto pPolyhedron = WDGeometryPolyhedron::MakeShared(pMesh);
    return pPolyhedron;
}

void WDGeometryMgr::updatePolyhedronQueryCatch()
{
    clearPolyhedronQueryCatch();

    _p->_polyhedronQueryCatch.clear();
    const auto& catchMap = _p->_polyhedronPool.getCatch();
    for (auto itr = catchMap.begin(); itr != catchMap.end(); ++itr) 
    {
        auto pGeom = WDGeometryPolyhedron::ToShared(itr->second);
        if (pGeom == nullptr)
            continue;
        _p->_polyhedronQueryCatch.emplace(pGeom->uuid(), pGeom);
    }
}
WDGeometry::SharedPtr WDGeometryMgr::queryPolyhedronFromCatch(const WDUuid& uuid
    , bool removeFromStrongRefSet)
{
    if (_p->_polyhedronQueryCatch.empty())
        return nullptr;

    WDGeometryPolyhedron::SharedPtr pRGeom = nullptr;
    auto fItr = _p->_polyhedronQueryCatch.find(uuid);
    if (fItr != _p->_polyhedronQueryCatch.end())
        pRGeom = fItr->second.lock();

    if (pRGeom == nullptr)
    {
        // 去临时的映射表查找
        auto fTItr = _p->_polyhedronMap.find(uuid);
        if (fTItr != _p->_polyhedronMap.end())
        {
            pRGeom = fTItr->second.lock();
            assert(pRGeom != nullptr);
        }
    }

    if (removeFromStrongRefSet && pRGeom != nullptr)
    {
        auto fTItr = _p->_polyhedronStrongRefGeos.find(pRGeom);
        if (fTItr != _p->_polyhedronStrongRefGeos.end())
            _p->_polyhedronStrongRefGeos.erase(fTItr);
    }

    return pRGeom;
}
void WDGeometryMgr::clearPolyhedronQueryCatch()
{
    _p->_polyhedronQueryCatch.clear();
}

void WDGeometryMgr::clear()
{
    // 清除强引用列表
    _p->_polyhedronStrongRefGeos.clear();
    // 清除额外的映射表
    _p->_polyhedronMap.clear();
    // 清除查询缓存
    clearPolyhedronQueryCatch();
    // 清空对象池
    _p->_boxPool.clear();
    _p->_circularTorusPool.clear();
    _p->_conePool.clear();
    _p->_cylinderPool.clear();
    _p->_dishPool.clear();
    _p->_ellipsoidPool.clear();
    _p->_extrusionPool.clear();
    _p->_loftingPool.clear();
    _p->_pyramidPool.clear();
    _p->_rectangularTorusPool.clear();
    _p->_revolutionPool.clear();
    _p->_slopedCylinderPool.clear();
    _p->_snoutPool.clear();
    _p->_spherePool.clear();
    _p->_polyhedronPool.clear();
}

size_t WDGeometryMgr::write(WDWriter& writer)
{
    // 获取多面体缓冲池
    const auto& tCatch = _p->_polyhedronPool.getCatch();
    // 获取需要写入的几何体个数
    std::uint64_t cnt = static_cast<std::uint64_t>(tCatch.size());
    if (cnt == 0)
        return 0;

    auto nStart = writer.tell();
    GeometryFileHeader header;
    // 先写入文件头，占位
    writer.writeBuffer(&header, sizeof(header));
    // 写入几何体个数
    writer.writeBuffer(&cnt, sizeof(cnt));
    // 将所有多面体写入
    for (auto cItr = tCatch.begin(); cItr != tCatch.end(); ++cItr)
    {
        const auto& md5 = cItr->first;
        auto pGeo = cItr->second;
        if (pGeo == nullptr)
            continue;
        // 写入MD5
        writer.writeBuffer(&md5, sizeof(md5));
        // 写入ClassId
        const auto& clsId = pGeo->classId();
        writer.writeBuffer(&clsId, sizeof(clsId));
        // 写入几何体
        pGeo->toStream(writer);
    }

    // 重新填充并写入文件头
    header.fileSize = writer.tell() - nStart;
    writer.seek(0);
    writer.writeBuffer(&header, sizeof(header));

    return header.fileSize;
}
size_t WDGeometryMgr::save(const std::string_view& file)
{
    // 这里判断如果多面体个数是0,则不写文件
    const auto& tCatch = _p->_polyhedronPool.getCatch();
    uint cnt = static_cast<uint>(tCatch.size());
    if (cnt == 0)
        return 0;

    FILE* fp = fopen(file.data(), "wb");
    WDFileWriter fWriter(fp);
    size_t nSize = this->write(fWriter);
    fclose(fp);
    return nSize;
}

size_t WDGeometryMgr::read(WDReader& reader, FuncProgress funcProgress)
{
    auto& pool = _p->_polyhedronPool;

    // 记录开始读取的位置
    auto seekStartPos = reader.tell();
    // 计算大小
    reader.seekToEnd();
    auto fileLength = reader.tell();
    reader.seek(seekStartPos);

    bool bExit = false;
    // 校验是否可以用文件头的方式读取
    if (fileLength > sizeof(GeometryFileHeader))
    {
        GeometryFileHeader header;
        reader.readBuffer(&header, sizeof(header));
        // 校验魔数
        if (header.checkMagicNumber())
        {
            // 读取几何体个数
            std::uint64_t cnt = 0;
            reader.readBuffer(&cnt, sizeof(cnt));
            std::vector<std::pair<WDMD5::Key, WDGeometryPolyhedron::SharedPtr> > rGeoms ;
            rGeoms.reserve(cnt);
            for (uint i = 0; i < cnt; ++i)
            {
                // 报告进度
                if (funcProgress)
                    bExit = funcProgress(static_cast<double>(i + 1) / static_cast<double>(cnt)
                        , WD::WDTs("mainFunc", "parse geometry data").c_str());
                // 检测退出标志
                if (bExit)
                    break;

                // 读取MD5
                WDMD5::Key md5;
                reader.readBuffer(&md5, sizeof(md5));
                // 读取ClassId
                WDUuid clsId;
                reader.readBuffer(&clsId, sizeof(clsId));
                // 创建几何体对象
                auto pGeo = WDGeometryPolyhedron::MakeShared();
                if (pGeo == nullptr)
                {
                    assert(false);
                    continue;
                }
                // 读取几何数据
                pGeo->fromStream(reader);

                // 检查多面体是否带有线框，如果不带线框，则自动生成线框,并重新生成md5
                auto pMesh = pGeo->mesh();
                if (pMesh != nullptr)
                {
                    auto& pris = pMesh->primitiveSets(WDMesh::WireFrame);
                    if (pris.empty())
                    {
                        auto rPri = WDMesh::GenerateTriangleWireframesWithMeshSolid(*pMesh);
                        pris.push_back(rPri);
                        // 重新生成MD5
                        md5 = pGeo->md5Key();
                    }
                }

                rGeoms.push_back(std::make_pair(md5, pGeo));
            }
            if (funcProgress)
                bExit = funcProgress(1.00, WD::WDTs("mainFunc", "parse complete").c_str());
            // 检测退出标志
            if (bExit)
                return 0;

            // 这里最后再将结果几何体添加到池中，防止中途退出
            for (const auto& gm : rGeoms) 
            {
                const auto& md5 = gm.first;
                const auto& pGeo = gm.second;
                if (pGeo == nullptr)
                    continue;
                bool bExist = false;
                if (pool.add(md5, pGeo, &bExist))
                {
                    if (!bExist)
                    {
                        _p->_polyhedronStrongRefGeos.insert(pGeo);
                    }
                    else
                    {
                        // 表明对应md5的数据已经存在
                        const auto& tmpCatch = pool.getCatch();
                        auto tmpFItr = tmpCatch.find(md5);
                        if (tmpFItr != tmpCatch.end())
                        {
                            // !注意: 这里找到了相同的md5, 所以池中有存在与当前对象完全相同的数据
                            //  所以当前对象将被抛弃, 但是某些节点还被当前对象所引用，所以额外建立一个映射表
                            _p->_polyhedronMap[pGeo->uuid()] = WDGeometryPolyhedron::ToShared(tmpFItr->second);
                        }
                        else
                        {
                            // 理论不会进入到这里来
                            assert(false);
                        }
                    }
                }
            }

            return reader.tell() - seekStartPos;
        }
        // 未读取成功，设置游标到开始，使用其他方式读取
        reader.seek(seekStartPos);
    }

    // 读取几何体个数
    uint cnt = 0;
    reader.readBuffer(&cnt, sizeof(cnt));
    std::vector<std::pair<WDMD5::Key, WDGeometryPolyhedron::SharedPtr> > rGeoms;
    rGeoms.reserve(cnt);
    for (uint i = 0; i < cnt; ++i)
    {
        // 报告进度
        if (funcProgress)
            bExit = funcProgress(static_cast<double>(i + 1) / static_cast<double>(cnt), WD::WDTs("mainFunc", "parse geometry data").c_str());

        // 检测退出标志
        if (bExit)
            break;

        // 读取MD5
        WDMD5::Key md5;
        reader.readBuffer(&md5, sizeof(md5));
        // 读取ClassId
        WDUuid clsId;
        reader.readBuffer(&clsId, sizeof(clsId));
        // 创建几何体对象
        auto pGeo = WDGeometryPolyhedron::MakeShared();
        if (pGeo == nullptr)
        {
            assert(false);
            continue;
        }
        // 读取几何数据
        pGeo->fromStream(reader);

        // 检查多面体是否带有线框，如果不带线框，则自动生成线框,并重新生成md5
        auto pMesh = pGeo->mesh();
        if (pMesh != nullptr)
        {
            auto& pris = pMesh->primitiveSets(WDMesh::WireFrame);
            if (pris.empty()) 
            {
                auto rPri = WDMesh::GenerateTriangleWireframesWithMeshSolid(*pMesh);
                pris.push_back(rPri);
                // 重新生成MD5
                md5 = pGeo->md5Key();
            }
        }

        rGeoms.push_back(std::make_pair(md5, pGeo));

    }
    if (funcProgress)
        bExit = funcProgress(1.00, WD::WDTs("mainFunc", "parse complete").c_str());
    // 检测退出标志
    if (bExit)
        return 0;

    // 这里最后再将结果几何体添加到池中，防止中途退出
    for (const auto& gm : rGeoms)
    {
        const auto& md5 = gm.first;
        const auto& pGeo = gm.second;
        if (pGeo == nullptr)
            continue;
        bool bExist = false;
        if (pool.add(md5, pGeo, &bExist))
        {
            if (!bExist)
            {
                _p->_polyhedronStrongRefGeos.insert(pGeo);
            }
            else
            {
                // 表明对应md5的数据已经存在
                const auto& tmpCatch = pool.getCatch();
                auto tmpFItr = tmpCatch.find(md5);
                if (tmpFItr != tmpCatch.end())
                {
                    // !注意: 这里找到了相同的md5, 所以池中有存在与当前对象完全相同的数据
                    //  所以当前对象将被抛弃, 但是某些节点还被当前对象所引用，所以额外建立一个映射表
                    _p->_polyhedronMap[pGeo->uuid()] = WDGeometryPolyhedron::ToShared(tmpFItr->second);
                }
                else
                {
                    // 理论不会进入到这里来
                    assert(false);
                }
            }
        }
    }

    return reader.tell() - seekStartPos;
}
size_t WDGeometryMgr::load(const std::string_view& file, FuncProgress funcProgress)
{
    // 报告进度
    if (funcProgress)
    {
        bool bExit = funcProgress(0.00, WD::WDTs("mainFunc", "the file is being opened").c_str());
        if (bExit)
            return 0;
    }

    size_t nSize = 0;
    WDFileReader fReader;
    if (!fReader.open(file.data()))
        return nSize;
    nSize = this->read(fReader, funcProgress);
    fReader.close();
    return nSize;
}

WD_NAMESPACE_END
