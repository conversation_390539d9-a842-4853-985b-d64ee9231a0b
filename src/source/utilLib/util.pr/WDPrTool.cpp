#include    "WDPrTool.h"
#include    <filesystem>
#include    <fstream>
#include    <shared_mutex>
#include    "core/message/WDMessage.h"
#include    "core/apiDelegate/WDApiDelegate.h"
#include    "core/graphable/WDGraphableInterface.h"
#include    "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include    "core/businessModule/WDBDBase.h"
#include    "core/businessModule/design/WDBMDesign.h"

WD_NAMESPACE_BEGIN

void    WDPrTool::createNode(SDK::PR::PRReadHelper& helper, Objects& objs, const char* file, const ProgressEvt& evt)
{
    using   MapNodes    =   std::map<uint, WDNode::SharedPtr>;
    using   MapMat      =   std::map<uint, MaterialConvertor::MaterialData>;

    /// 创建所有节点信息
    if (evt)
        evt(63.0f, "创建节点");

    // 先将所有PR Item创建为PR节点
    // 同时关联父子节点
    auto nodeMap = ItemConvertor::ParseNodesFromItems(_core, helper._items, objs);

    if (evt)
        evt(70.0f, "创建几何对象");

    // 创建所有几何体节点
    MapNodes geomNodeMap;
    for (auto itr : helper._geos)
    {
        // 每个pr geo对应创建一个节点
        // 之后挂到pr geo对应的节点上
        auto pGeomNode  =   GeometryConvertor::CreateNodeFromPRGeo(_core, itr.second);
        if (pGeomNode == nullptr)
        {
            assert(false);
            continue;
        }
        geomNodeMap[itr.first] = pGeomNode;
    }

    if (evt)
        evt(80.0f, "创建材质对象");

    // 创建所有材质
    MapMat matMap;
    for (auto& mat : helper._materials)
    {
        int     matId   =   mat.second->id();
        auto    ret     =   MaterialConvertor::PrMaterialToWDMaterial(_core, mat.second, file);
        if (ret)
            matMap[matId]   =   ret.value();
    }

    if (evt)
        evt(85.0f, "创建实例对象");
    // 读取instance信息
    for (auto& inst : helper._instances)
    {
        auto itr = nodeMap.find(inst._itemId); // inst._itemId标记改inst所属节点
        if (itr == nodeMap.end())
        {
            assert(false);
            continue;   
        }
        auto pNode = itr->second;
        if (pNode == nullptr)
        {
            assert(false);
            continue;   
        }

        // 匹配几何体节点以及设置材质
        auto fGeoItr = geomNodeMap.find(inst._geoId); // inst._geoId标记inst对应的几何体
        if (fGeoItr != geomNodeMap.end())
        {
            auto pGeoNode = fGeoItr->second->cloneT<WDNode>();
            {
                // 获取pr文件的单位并设置到缩放中
                float unit = helper._file.getUnit();
                // player中默认单位为m，设计器中为mm
                // 所以拿到单位后先转为本地单位
                // (从设计器导出Pr时，单位默认就是设置的mm，所以导出时不用额外处理)
                float realUnit = 1000.0f * unit;
                inst._scale *= realUnit;
            }
            InstanceConvertor::ReadPrInst(pGeoNode, inst); // 读取inst信息并写到节点
            pNode->addChild(pGeoNode);

            // 匹配材质
            auto fMatItr = matMap.find(inst._matId); //读取inst对应的材质
            if (fMatItr != matMap.end())
            {
                const auto& matData = fMatItr->second;
                if (matData.basicColor)
                    pGeoNode->setAttribute("AutoColor", matData.basicColor.value());
                if (matData.pMaterial != nullptr)
                    pGeoNode->setMaterial(matData.pMaterial);
            }
        }
    }
    if (evt)
        evt(100.0f, "创建节点完成！");
}


// 收集节点信息
void    WDPrTool::collectionNodeInfo( const WDNode::SharedPtr& pNode
                                    , const Mat4& nodeMat
                                    , uint pid
                                    , PRWriterHelper& helper
                                    , uint version, WD::XMLDoc& doc)
{

    if (pNode == nullptr || (nodeFilter() && !nodeFilter()(pNode)))
        return ;
    uint    treeNodeId;
    uint    flag;
    if (!CheckBackToInstance(*pNode))
    {
        treeNodeId  =   ++_treeNodeCnt; // 相当于节点id
        // 1. item
        auto pItem  =   ItemConvertor::GetPrItem(*pNode, treeNodeId, pid, typesNeedCodeAndFlag());
        helper._items.emplace(treeNodeId, pItem);
        flag = pItem->_flag;

        if (_innerCallback && _innerCallback())
        {
            writeAttributXml(pNode, doc, pItem->_id, pItem->_parentId, pItem->_name);
    }
    }
    else// 要退化的节点不产生item
    {  
        treeNodeId  =   pid;
        flag   =   helper._items.find(treeNodeId)->second->_flag;
    }

    auto pBase      =   pNode->getBDBase();
    WDGraphableInterface* pGraphable = nullptr;
    if (pBase)
        pGraphable =   pBase->graphableSupporter();

    if (pGraphable != nullptr) // 有图形化对象，材质/几何/inst才有意义
    {
        // 2. 材质
        uint nMatId = _matConvertor.serialMaterial(*pNode, PrVersionToStr(PrVersion(version)), helper);
        SDK::PR::Material::Type t;
        if (version == Version303)
            t = SDK::PR::Material::MT_Color;
        else if (version == Version304 || version == Version305)
            t= SDK::PR::Material::MT_Common;
        else
            return ;

        auto matId = nMatId | (uint(t) << 24);

        auto geoms = pGraphable->gGeoms(WDGraphableInterface::GGT_Basic);
        for (const auto& pGeom : geoms)
        {
            if (pGeom == nullptr || pGeom->mesh() == nullptr)
                continue;
            // 这里导出时，只对有多面体索引的几何体进行导出
            // 因为在打开bool运算后，可能生成了边线几何体，这些边线几何体需要过滤掉，这里就是过滤掉这些边线几何体
            if (pGeom->mesh()->primitiveSets(WDMesh::Solid).empty())
                continue;

            Mat4 geoTrans;
            // 3.geometry
            uint meshId = _geoConvertor.parseGeoParams(helper, *pGeom, geoTrans, PrVersionToStr(PrVersion(version)));
            if (meshId == 0xFFFFFFFF)
                continue;

            // 4.inst
            auto inst = InstanceConvertor::GetPrInst(nodeMat, geoTrans, treeNodeId, meshId, matId, flag);
            helper._instances.emplace_back(inst);
        }
    }

    //递归子节点
    for (size_t i = 0; i < pNode->childCount(); ++i)
    {
        auto pChild = pNode->childAt(i);
        if (pChild == nullptr)
            continue;
        auto childNodeMat = nodeMat * pChild->localTransform();
        collectionNodeInfo(pChild, childNodeMat, treeNodeId, helper, version, doc);
    }
}


// 暂不实现，之前的接口是基于文件的，要支持流的话改动会较大
// 考虑先将流保存为一个临时文件，然后走打开Pr文件的流程，之后删除文件
// 后续有空再优化
size_t  WDPrTool::readFromStream(const Buffer& stream, Nodes& nodes, ProgressEvt evt)
{
    if(stream.empty())
        return 0;
    // 保存到临时文件
    std::string tempFile = WDUuid::Create().toString() + ".pr";
    FILE* pFile = fopen(tempFile.c_str(), "wb");
    if (pFile == nullptr)
        return  0;
    fwrite(&stream.front(), 1, stream.size(), pFile);
    fclose(pFile);
    readFromFile(tempFile.c_str(), nodes, evt);

    //删除临时文件
    std::filesystem::remove(tempFile);
    return nodes.size();
}

size_t  WDPrTool::readFromFile(const char* file, Nodes& nodes, ProgressEvt evt)
{
    if (evt)
        evt(0.0f, "读取并分析文件数据");

    SDK::PR::PRFile verFile;
    if(!verFile.open(file))
        return  0;
    SDK::PR::PRFile*    pFile   =   nullptr;
    // 从303开始支持，301，302不支持
    if (strcmp(PR_V304,verFile.version()) == 0)
        pFile   =   new SDK::PR::PRFileV304();
    else if (strcmp(PR_V303,verFile.version()) == 0)
        pFile   =   new SDK::PR::PRFileV303();
    else if (strcmp(PR_V305,verFile.version()) == 0)
        pFile   =   new SDK::PR::PRFileV305();
    else
    {
        // 切到主线程并弹出窗口(只能在主线程弹出)
        std::shared_mutex mtx;
        mtx.lock();
        _core.apiDelegate()->sendRunable(
            [&mtx](void*, void*)->void*
            {
                //====
                WD_WARN("该版本PR目前不支持读取！");
                //通知辅助线程可以往下执行
                mtx.unlock();
                //====
                return nullptr;
            }
        , nullptr, nullptr);
        //需要考虑等待主线程调用完成之后再执行之后的代码
        mtx.lock();//阻塞等待主线程执行结束
    }

    if (pFile == nullptr)
        return  0;

    SDK::PR::PRReadHelper   helper(*pFile);
    Objects objs;
    do
    {
        if(!pFile->open(file))
            break;

        ProgressEvt parseEvt =   [&](float val,const char* msg)
        {
            if (evt)
                evt(3.0f + val * 57.0f, msg); // 3<=内部进度值<=60
        };
        if (!helper.parse(parseEvt))
            break;
        /// 创建节点
        createNode(helper, objs, file, evt);
    } while (false);

    delete  pFile;

    for (auto obj : objs)
    {
        nodes.push_back(obj->toPtr<WDNode>());
    }

    return  nodes.size();
}

size_t  WDPrTool::writeToStream(const Nodes& nodes, Buffer& stream, PrVersion version, ProgressEvt evt)
{
    // 统一到一个接口的实现中
    ArrayNodeMats nodeMats;
    nodeMats.reserve(nodes.size());
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        nodeMats.push_back({ pNode, pNode->globalTransform() });
    }
    return writeToStream(nodeMats, stream, version, evt);
}

size_t WDPrTool::writeToStream(const ArrayNodeMats& nodeMats, Buffer& stream, PrVersion version, ProgressEvt evt)
{
    // helper
    SDK::PR::Observer   obs;
    SDK::PR::PRWriterHelper helper(obs);
    parseHelperData(nodeMats, version, helper);
    // 预算
    size_t numBytes = calcRequiredStreamLen(helper, version);
    if (numBytes == 0)
        return 0;
    stream.resize(numBytes);

    // 实际写入
    if (version == Version303)
    {
        return helper.saveToStreamV303(&stream.front(), (uint)stream.size());
    }
    else if (version == Version304)
    {
        return helper.saveToStreamV304(&stream.front(), (uint)stream.size());
    }
    else if (version == Version305)
    {
        return helper.saveToStreamV305(&stream.front(), (uint)stream.size());
    }
    else
    {
        assert(false && "暂未提供该版本！");
        return 0;
    }
}

bool  WDPrTool::writeToFile(const Nodes& nodes, const char* prFileName, PrVersion version, ProgressEvt evt)
{
    // 统一到一个接口的实现中
    ArrayNodeMats nodeMats;
    nodeMats.reserve(nodes.size());
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        nodeMats.push_back({ pNode, pNode->globalTransform() });
    }
    return writeToFile(nodeMats, prFileName, version, evt);
}

bool WDPrTool::writeToFile(const ArrayNodeMats& nodeMats, const char* prFileName, PrVersion version, ProgressEvt evt)
{
    // helper
    SDK::PR::Observer   obs;
    SDK::PR::PRWriterHelper helper(obs);
    // 获取属性xml的全路径
    std::string attrXmlFilePath = prFileName;
    attrXmlFilePath.append(".xml");
    parseHelperData(nodeMats, version, helper, attrXmlFilePath);

    // 没有写入任何节点，直接返回
    if (helper._items.empty())
    {
        assert(false && "没有写入任何节点！");
        return false;
    }

    // 实际写入
    if (version == Version303)
    {
        return helper.saveV303(prFileName, evt);
    }
    else if (version == Version304)
    {
        return helper.saveV304(prFileName, evt);
    }
    else if (version == Version305)
    {
        return helper.saveV305(prFileName, evt);
    }
    else
    {
        assert(false && "暂未提供该版本！");
        return false;
    }
}


size_t  WDPrTool::calcRequiredStreamLen(PRWriterHelper& helper, PrVersion version)
{
    size_t numBytes = 0;
    if (version == Version303)
    {
        numBytes = helper.calcDataBytesV303();
    }
    else if(version == Version304)
    {
        numBytes = helper.calcDataBytesV304();
    }
    else if(version == Version305)
    {
        numBytes = helper.calcDataBytesV305();
    }
    else
    {
        assert(false && "暂未提供该版本！");
    }

    return  numBytes;
}

void    WDPrTool::parseHelperData(const ArrayNodeMats& nodeMats, PrVersion version, PRWriterHelper& helper, std::string attrXmlFilePath)
{
    _matConvertor.init();
    _geoConvertor.init();

    // 初始化置空
    _treeNodeCnt = 0;
    uint pid = 0;

    size_t count = nodeMats.size();
    for (auto itr : nodeMats)
    {
        if (itr.first == nullptr || (nodeFilter() && !nodeFilter()(itr.first)))
            count--;
    }
    // 如果有多个节点，则创建一个根
    // 将所有节点挂到该根下
    WD::XMLDoc doc;
    // 添加格式说明
    WD::XMLNode* xmlinfo = doc.allocate_node(rapidxml::node_pi, "xml version='1.0' encoding='UTF-8'");
    doc.append_node(xmlinfo);
    // 创建Objects对象
    WD::XMLNode* Objects = doc.allocate_node(rapidxml::node_element, "Objects");
    doc.append_node(Objects);

    if (count > 1)
    {
        _treeNodeCnt = 1;
        // 将所有目标节前的父节点设为现在新建的节点(item)
        pid = 1;

        auto pItem          =   std::make_shared<SDK::PR::Item>();
        pItem->_name        =   "/*";
        pItem->_id          =   _treeNodeCnt;
        pItem->_parentId    =   0;

        helper._items.emplace(pItem->_id, pItem);
        if (_innerCallback && _innerCallback())
        {
            // 写根节点对象
            WD::XMLNode* Object = doc.allocate_node(rapidxml::node_element, "Object");
            WD::XMLAttr* ObjectIdAttr = doc.allocate_attribute("id", doc.allocate_string(std::to_string(pItem->_id).c_str()));
            Object->append_attribute(ObjectIdAttr);
            WD::XMLAttr* ObjectNameAttr = doc.allocate_attribute("name", doc.allocate_string(pItem->_name.c_str()));
            Object->append_attribute(ObjectNameAttr);
            WD::XMLAttr* ObjectParentAttr = doc.allocate_attribute("parent", doc.allocate_string(std::to_string(pItem->_parentId).c_str()));
            Object->append_attribute(ObjectParentAttr);

            Objects->append_node(Object);
        }
    }

    for (auto itr : nodeMats)
    {
        auto pNode = itr.first;
        if (pNode == nullptr)
            continue;
        collectionNodeInfo(pNode, itr.second, pid, helper, version, doc);
    }

    if (_innerCallback && _innerCallback())
    {
        std::ofstream outfile;
        try
        {
            // 以写的方式打开文件，如果存在则清空内容
            outfile.open(attrXmlFilePath, std::ios::out | std::ios::trunc);
            // 文件打开失败
            if (!outfile)
            {
                assert(false && "file open faliure!");
                throw std::runtime_error("Failed to open file");
            }
            // 将xml数据直接写到文件中
            rapidxml::print(std::ostream_iterator<char>(outfile), doc, 0);
            outfile.close();
        }
        catch (const std::exception& e)
        {
            std::string error = "生成属性文件失败，仅导出pr文件。异常：";
            error.append(e.what());
            WD_ERROR(error);

            if(outfile.is_open())
                outfile.close();

            // 释放内容占用的内存
            doc.clear();
            
            // 写属性xml异常后移除未正确生成的文件
            if(std::remove(attrXmlFilePath.c_str()) != 0)
            {
                assert(false && "Error, delete export Pr Attribute xml");
            }
        }
    }
}

void WDPrTool::writeAttributXml(const WDNode::SharedPtr& pNode, WD::XMLDoc& doc, unsigned id, unsigned parentid, std::string name)
{
    auto infoObject = doc.first_node();
    if (infoObject != nullptr)
    {
        auto Objects = infoObject->next_sibling();
        if (Objects != nullptr)
        {
            // 添加节点对象
            WD::XMLNode* Object = doc.allocate_node(rapidxml::node_element, "Object");

            WD::XMLAttr* ObjectIdAttr = doc.allocate_attribute("id", doc.allocate_string(std::to_string(id).c_str()));
            Object->append_attribute(ObjectIdAttr);
            WD::XMLAttr* ObjectNameAttr = doc.allocate_attribute("name", doc.allocate_string(name.c_str()));
            Object->append_attribute(ObjectNameAttr);
            WD::XMLAttr* ObjectParentAttr = doc.allocate_attribute("parent", doc.allocate_string(std::to_string(parentid).c_str()));
            Object->append_attribute(ObjectParentAttr);

            Objects->append_node(Object);

            // 添加节点的属性对象
            const auto pTypeDesc = pNode->getTypeDesc();
            if (pTypeDesc != nullptr)
            {
                const auto& attrDesc = pTypeDesc->attrDescs();
                for (auto pAttrDesc : attrDesc)
                {
                    if (pAttrDesc == nullptr)
                        continue;
                    // 跳过隐藏属性
                    if (pAttrDesc->flags().hasFlag(WD::WDBMAttrDesc::F_Hidden))
                        continue;

                    WD::XMLNode* Attr = doc.allocate_node(rapidxml::node_element, "Attribute");

                    WD::XMLAttr* keyAttr = doc.allocate_attribute("key", doc.allocate_string(pAttrDesc->name().c_str()));
                    Attr->append_attribute(keyAttr);
                    WD::XMLAttr* valueAttr;
                    if (pAttrDesc->name() == "Type")
                    {
                        auto valueStr = WD::WDBMAttrValue::ToString(pAttrDesc->value(*pNode));
                        valueAttr = doc.allocate_attribute("value", doc.allocate_string(_core.getBMDesign().trT(valueStr).c_str()));
                    }
                    else
                    {
                        valueAttr = doc.allocate_attribute("value", doc.allocate_string(WD::WDBMAttrValue::ToString(pAttrDesc->value(*pNode)).c_str()));
                    }
                    Attr->append_attribute(valueAttr);

                    Object->append_node(Attr);
                }
            }
        }
    }
}
WD_NAMESPACE_END


