#pragma     once
#include    "core/math/Math.hpp"
#include    "WDSvgToolDefine.h"

WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgTransform
{
private:
    double _data[6];
public:
    WDSvgTransform();
    ~WDSvgTransform();
private:
    /**
     * @brief 平移
     * @param x 
     * @param y 
    */
    void    move(double x, double y);
    /**
     * @brief 旋转
     * @param degree 
    */
    void    rotate(double degree);
    /**
     * @brief 缩放
     * @param x 
     * @param y 
    */
    void    scale(double x, double y);
    /**
     * @brief x上斜切
     * @param degree 
    */
    void    skewX(double degree);
    /**
     * @brief y上斜切
     * @param degree 
    */
    void    skewY(double degree);
public:
    inline double* data()
    {
        return  _data;
    }
    inline const double* data() const 
    {
        return  _data;
    }
    static WDSvgTransform  Multiply(const WDSvgTransform& t, const WDSvgTransform& s);
public:
    double  operator [] (size_t index) const
    {
        return  _data[index];
    }
    double& operator [] (size_t index) 
    {
        return  _data[index];
    }
    Vec2    operator * (const Vec2& pt);
    WDSvgTransform& operator *= (const WDSvgTransform& pt);
    friend  WDSvgTransform  operator * (const WDSvgTransform& t, const WDSvgTransform & s)
    {
        return  Multiply(t, s);
    }
    friend  Vec2 operator * (const WDSvgTransform& trans, const Vec2 & pt)
    {
        double  x = pt.x * trans[0] + pt.y * trans[2] + trans[4];
        double  y = pt.x * trans[1] + pt.y * trans[3] + trans[5];
        return  Vec2(x, y);
    }
    /**
     * @brief 转化为svg文本格式，用于序列化
     * @return 
    */
    std::string toString() const;
    /**
     * @brief 从字符串读取变换信息
     * @param attrValue 
     * @return 
    */
    bool        fromString(const char* attrValue);
};

WD_NAMESPACE_END