#pragma once
#include    "WDSvgObject.h"

WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgRect : public WDSvgObject
{
public:
    double x    =   0.0f;
    double y    =   0.0f;
    double w    =   1.0f;
    double h    =   1.0f;
    double rx   =   0.0f;
    double ry   =   0.0f;
public:
    WDSvgRect();
public:
    virtual bool        fromSvg(XMLDoc&, XMLNode*) override final;
    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;
private:
    void    buildRect(double x, double y, double w, double h, double rx, double ry);
};

WD_NAMESPACE_END
