#include "WDSvgSvgRoot.h"
#include "WDSvgXmlCreator.h"

WD_NAMESPACE_BEGIN

WDSvgSvgRoot::WDSvgSvgRoot()
    :WDSvgObject(T_SvgRoot)
{}

bool WDSvgSvgRoot::fromSvg(XMLDoc& doc, XMLNode* node)
{
    _attr.fromSvgAttrs(*node);
    mapRootAttrs.clear();
    // <svg>不需要读取固定属性
    // 全是自身单独属性
    for (auto pAttr = node->first_attribute(); pAttr != nullptr; pAttr = pAttr->next_attribute())
    {
        mapRootAttrs[pAttr->name()] = pAttr->value();
    }

    for (XMLNode* item = node->first_node(); item != nullptr; item = item->next_sibling())
    {
        auto child = ParseSvgNode(doc, item);
        if (child == nullptr)
            continue;
        addChild(child);
    }

    return  true;
}


XMLNode* WDSvgSvgRoot::toSvg(XMLDoc& doc, XMLNode* parent) const
{
    auto nd = createSvg(doc, parent);
    // 写入一些必要的属性，否则浏览器可能无法打开
    WDSvgXmlCreator::Node node(doc, nd);
    for (const auto& [name, value] : mapRootAttrs)
    {
        node.attr(name.c_str(), value);
    }

    for (const auto& child : children())
    {
        if (child == nullptr)
            continue;
        child->toSvg(doc, nd);
    }

    return  nd;
}


WD_NAMESPACE_END