#pragma once
#include    "WDSvgObject.h"
WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgPolyLine : public WDSvgObject
{
public:
    WDSvgPolyLine();
public:
    /**
    *   转化成可绘制数据
    */
    virtual size_t  toData(std::vector<FVec3>&, int) const override final;
    /**
    *   与计算需要的元素空间个数，用于提前分配内存，避免频繁计算
    */
    virtual size_t  computeDataLength(int) const override final;

    virtual bool    fromSvg(XMLDoc&, XMLNode*) override final;
    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;
};
WD_NAMESPACE_END
