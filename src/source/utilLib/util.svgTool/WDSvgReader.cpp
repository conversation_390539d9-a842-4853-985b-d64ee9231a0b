
#include "WDSvgReader.h"
#include "core/common/WDFileReader.hpp"
#include "WDSvgXmlCreator.h"

WD_NAMESPACE_BEGIN

WDSvgReadWriter::WDSvgReadWriter()
{
}

WDSvgReadWriter::~WDSvgReadWriter()
{
    if (_root != nullptr)
    {
        delete _root;
        _root = nullptr;
    }
}

bool    WDSvgReadWriter::load(const char* fileName)
{
    // 获取XML根节点
    WDFileReader file(fileName);
    if (file.isBad())
        return  false;

    file.readAll();
    if (file.length() == 0)
        return false;

    return loadFromData((char*)file.data());
}

bool    WDSvgReadWriter::loadFromData(const char* svgData)
{
    setRoot(nullptr);

    XMLDoc doc;
    doc.parse<0>((char*)svgData);

    XMLNode* svg    =   doc.first_node("svg");
    if (svg == nullptr)
        return false;

    auto svgRoot = WDSvgObject::ParseSvgNode(doc, svg);

    _root = dynamic_cast<WDSvgSvgRoot*>(svgRoot);
    return _root != nullptr;
}


bool    WDSvgReadWriter::save(const char* fileName)
{
    if (_root == nullptr)
        return false;

    FILE* pFile =   fopen(fileName, "wb");
    if (pFile == nullptr)
        return  false;

    WDSvgXmlCreator doc;
    _root->toSvg(doc.doc(), &(doc.doc()));

    std::string xml =   doc.toString();
    bool result     =   fwrite(xml.c_str(), 1, xml.size(), pFile) == xml.size();

    fclose(pFile);
    return  result;
}

WDSvgSvgRoot* WDSvgReadWriter::root()
{
    return _root;
}

const WDSvgSvgRoot* WDSvgReadWriter::root() const
{
    return _root;
}

void WDSvgReadWriter::setRoot(WDSvgSvgRoot* svgRoot)
{
    if (_root == svgRoot)
        return ;
    if (_root != nullptr)
        delete _root;
    _root = svgRoot;
}

WD_NAMESPACE_END