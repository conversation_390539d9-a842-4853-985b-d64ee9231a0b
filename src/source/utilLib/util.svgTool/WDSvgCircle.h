#pragma once
#include    "WDSvgObject.h"
WD_NAMESPACE_BEGIN

class   SVT_TOOL_API  WDSvgCircle : public WDSvgObject
{
public:
    double cx   =   0;
    double cy   =   0;
    double r    =   1;
public:
    WDSvgCircle();
public:
    /**
    *   解析函数
    */
    virtual bool        fromSvg(XMLDoc&, XMLNode*) override final;
    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;
private:
    /**
     * @brief 通过参数构建顶点
     * @param cx 
     * @param cy 
     * @param r 
    */
    void    buildCircle(double cx = 0.0f, double cy = 0.0f, double r = 1.0f);
};

WD_NAMESPACE_END
