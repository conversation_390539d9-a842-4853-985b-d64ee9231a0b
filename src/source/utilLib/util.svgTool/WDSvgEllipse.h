#pragma once
#include    "WDSvgObject.h"
WD_NAMESPACE_BEGIN

class   SVT_TOOL_API  WDSvgEllipse : public WDSvgObject
{
public:
    double cx   =   0;
    double cy   =   0;
    double rx   =   1;
    double ry   =   1;
public:
    WDSvgEllipse();
public:
    /**
    *   解析函数
    */
    virtual bool        fromSvg(XMLDoc&, XMLNode*) override final;
    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;
private:
    /**
     * @brief 根据参数构建椭圆
     * @param cx
     * @param cy 
     * @param rx 
     * @param ry 
    */
    void    buildEllipse(double cx, double cy, double rx, double ry);
};

WD_NAMESPACE_END
