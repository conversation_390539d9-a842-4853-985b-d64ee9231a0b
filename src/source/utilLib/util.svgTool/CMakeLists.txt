set(TARGET_NAME util.svgTool)

set(HEADER_FILES
	"WDSvgObject.h"
	"WDSvgReader.h"
	"WDSvgToolDefine.h"
	"WDSvgTransform.h"
	"WDSvgCircle.h"
	"WDSvgEllipse.h"
	"WDSvgLine.h"
	"WDSvgPath.h"
	"WDSvgPolygon.h"
	"WDSvgPolyLine.h"
	"WDSvgRect.h"
	"WDSvgText.h"
	"WDSvgGroup.h"
	"WDSvgSvgRoot.h"
	"WDSvgXmlCreator.h"
	"WDSvgHeaders.h"
)

set(SOURCE_FILES
	"WDSvgObject.cpp"
	"WDSvgReader.cpp"
	"WDSvgTransform.cpp"
	"WDSvgCircle.cpp"
	"WDSvgEllipse.cpp"
	"WDSvgLine.cpp"
	"WDSvgPath.cpp"
	"WDSvgPolygon.cpp"
	"WDSvgPolyLine.cpp"
	"WDSvgRect.cpp"
	"WDSvgText.cpp"
	"WDSvgGroup.cpp"
	"WDSvgSvgRoot.cpp"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

target_compile_definitions(${TARGET_NAME} PRIVATE
	-DSVG_TOOL_EXPORTS
)

target_link_libraries(${TARGET_NAME} PRIVATE wizDesignerCore util.rapidxml)

CopyImportedRuntimeDependency(${TARGET_NAME})