#pragma once
#include    "WDSvgObject.h"
WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgSvgRoot : public WDSvgObject
{
public:
    WDSvgSvgRoot();
public:
    std::map<std::string, std::string> mapRootAttrs;
public:
    /**
    *   解析函数
    */
    virtual bool        fromSvg(XMLDoc&, XMLNode*) override final;
    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;
};

WD_NAMESPACE_END
