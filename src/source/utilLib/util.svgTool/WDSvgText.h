#pragma once
#include    "WDSvgObject.h"
WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgText : public WDSvgObject
{
public:
    double      x  =   0.0f;
    double      y  =   0.0f;
    std::string text;
    double      fontSize;
    std::string style;
public:
    WDSvgText();
public:
    virtual bool        fromSvg(XMLDoc&, XMLNode*) override final;
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;
};

WD_NAMESPACE_END
