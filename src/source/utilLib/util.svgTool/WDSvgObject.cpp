#include    "WDSvgCircle.h"
#include    "WDSvgLine.h"
#include    "WDSvgEllipse.h"
#include    "WDSvgPath.h"
#include    "WDSvgRect.h"
#include    "WDSvgText.h"
#include    "WDSvgPolygon.h"
#include    "WDSvgPolyLine.h"
#include    "WDSvgGroup.h"
#include    "WDSvgSvgRoot.h"
#include    "WDSvgXmlCreator.h"
#include    <regex>
#include    <sstream>

WD_NAMESPACE_BEGIN

// 透明
Color SvgAttr::Color4None = { 255, 255, 255, 0 };
Color SvgAttr::Color4NotSet = { 0, 0, 0, 255 };

//读取颜色
//svg中定义颜色有很多种形式
//这里基本考虑完全
static std::optional<Color> ReadSvgColorAttr(const char* colorData)
{
    if (colorData == nullptr)
        return std::nullopt;
    std::string colorStr(colorData);
    // 将逗号和"/"替换为空格
    colorStr = StringReplace(colorStr, ",", " ");
    colorStr = StringReplace(colorStr, "/", " ");// 可能存在于透明度前面
    // 去掉头尾的空格
    colorStr = StringTrimHead(colorStr, " ");
    colorStr = StringTrimTail(colorStr, " ");
    // 转为小写
    colorStr = StringToLower(colorStr);

    Color result;
    // 检查特殊值
    if (colorStr == "none" || colorStr == "transparent" || colorStr == "currentcolor")
        return SvgAttr::ColorForNone();// 返回默认颜色（currentcolor的处理目前暂时这样）
    if (colorStr == "inherit")
        return std::nullopt;// 表示继承父

    // 尝试十六进制形式
    if (colorStr[0] == '#')
    {
        std::string hex = colorStr.substr(1);
        hex = StringTrim(hex, " ");// 去掉内部空格（健壮性处理）
        if (hex.length() == 3) {
            // 简写格式 #rgb
            result.r = byte(stoi(std::string(1, hex[0]) + std::string(1, hex[0]), nullptr, 16));
            result.g = byte(stoi(std::string(1, hex[1]) + std::string(1, hex[1]), nullptr, 16));
            result.b = byte(stoi(std::string(1, hex[2]) + std::string(1, hex[2]), nullptr, 16));
        }
        else if (hex.length() == 4)
        {
            // 简写带透明度格式 #rgba
            result.r = byte(stoi(std::string(1, hex[0]) + std::string(1, hex[0]), nullptr, 16));
            result.g = byte(stoi(std::string(1, hex[1]) + std::string(1, hex[1]), nullptr, 16));
            result.b = byte(stoi(std::string(1, hex[2]) + std::string(1, hex[2]), nullptr, 16));
            result.a = byte(stoi(std::string(1, hex[3]) + std::string(1, hex[3]), nullptr, 16));
        }
        else if (hex.length() == 6) {
            // 完整格式 #rrggbb
            result.r = byte(stoi(hex.substr(0, 2), nullptr, 16));
            result.g = byte(stoi(hex.substr(2, 2), nullptr, 16));
            result.b = byte(stoi(hex.substr(4, 2), nullptr, 16));
        }
        else if (hex.length() == 8) {
            // 带透明度的格式 #rrggbbaa
            result.r = byte(stoi(hex.substr(0, 2), nullptr, 16));
            result.g = byte(stoi(hex.substr(2, 2), nullptr, 16));
            result.b = byte(stoi(hex.substr(4, 2), nullptr, 16));
            result.a = byte(stoi(hex.substr(6, 2), nullptr, 16));
        }
        else
        {
            return std::nullopt;
        }
        return result;
    }

    // 尝试解析函数式颜色 (rgb/rgba/hsl/hsla)
    std::smatch match;
    // 考虑了"rgb  (...)"这种带空格的情况
    std::regex func_regex(R"(^(rgba?|hsla?)\s*\(\s*([^)]*?)\s*\)$)");
    if (std::regex_match(colorStr, match, func_regex))
    {
        std::string func = match[1];
        std::string params = match[2];
        // 读取参数
        StringVector ps;
        std::istringstream iss(params);
        std::string param;
        while (iss >> param)
        {
            ps.push_back(param);
        }
        // 不需要区分rgb和rgba
        // 看的是参数，rgb(r, g, b, a)或rgba(r, g, b)也合法
        if (func == "rgb" || func == "rgba")
        {
            if (ps.size() == 3 || ps.size() == 4)
            {
                // 读取透明度
                if (ps.size() == 4)
                {
                    float alpha;
                    if (ps[3].find('%') != std::string::npos)
                    {
                        if (sscanf(ps[3].data(), "%f%%", &alpha))
                            result.setAF(alpha / 100.0f);
                        else
                            return std::nullopt;
                    }
                    else if (sscanf(ps[3].data(), "%f", &alpha))
                        result.setAF(alpha);
                    else
                        return std::nullopt;
                }
                // 读取r,g,b
                // 有两种形式：整数和百分比
                // 查找是否存在百分号
                if (ps[0].find('%') != std::string_view::npos)
                {
                    // 百分比形式
                    float ratio;
                    if (sscanf(ps[0].data(), "%f%%", &ratio))
                        result.setRF(ratio / 100.0f);
                    else
                        return std::nullopt;
                    if (sscanf(ps[1].data(), "%f%%", &ratio))
                        result.setGF(ratio / 100.0f);
                    else
                        return std::nullopt;
                    if (sscanf(ps[2].data(), "%f%%", &ratio))
                        result.setBF(ratio / 100.0f);
                    else
                        return std::nullopt;
                    return result;
                }
                else
                {
                    float R, G, B;
                    if (sscanf(ps[0].data(), "%f", &R) &&
                        sscanf(ps[1].data(), "%f", &G) &&
                        sscanf(ps[2].data(), "%f", &B))
                    {
                        result.setRF(R / 255.0f);
                        result.setGF(G / 255.0f);
                        result.setBF(B / 255.0f);
                        return result;
                    }
                }
            }
            else
                return std::nullopt;
        }
        // 色相、饱和度、亮度、透明度
        else if (func == "hsl" || func == "hsls")
        {
            if (ps.size() == 3 || ps.size() == 4)
            {
                if (ps.size() == 4)
                {
                    float alpha;
                    if (ps[3].find('%') != std::string::npos)
                    {
                        if (sscanf(ps[3].data(), "%f%%", &alpha))
                            result.setAF(alpha / 100.0f);
                        else
                            return std::nullopt;
                    }
                    else if (sscanf(ps[3].data(), "%f", &alpha))
                        result.setAF(alpha);
                    else
                        return std::nullopt;
                }
                float H, S, L;
                // 形如0, 100%, 50%, 100%或0, 100%, 50%, 0.5
                if (sscanf(ps[0].data(), "%f", &H) &&
                    sscanf(ps[1].data(), "%f%%", &S) &&
                    sscanf(ps[2].data(), "%f%%", &L))
                {
                    // 转为rgb:
                    S /= 100.0f;
                    L /= 100.0f;
                    // 参数校验
                    H = std::fmod(std::fmod(H, 360.0f) + 360.0f, 360.0f); // 确保 h ∈ [0,360)
                    S = std::clamp(S, 0.0f, 1.0f);
                    L = std::clamp(L, 0.0f, 1.0f);

                    // 计算中间变量
                    float c = (1 - std::abs(2 * L - 1)) * S; // 色度
                    float x = c * float(1 - std::abs(std::fmod(H / 60.0f, 2) - 1));
                    float m = L - c / 2;

                    float r, g, b;
                    if (H < 60) { r = c; g = x; b = 0; }
                    else if (H < 120) { r = x; g = c; b = 0; }
                    else if (H < 180) { r = 0; g = c; b = x; }
                    else if (H < 240) { r = 0; g = x; b = c; }
                    else if (H < 300) { r = x; g = 0; b = c; }
                    else { r = c; g = 0; b = x; }

                    result.setRF(r + m);
                    result.setGF(g + m);
                    result.setBF(b + m);

                    return result;
                }
            }
            else
                return std::nullopt;
        }
        else
            return std::nullopt;
    }

    // 尝试"red"形式
    return ColorFromName(colorStr.data());
}

void SvgAttr::toSvgAttrs(XMLDoc& doc, XMLNode& xmlNode) const
{
    // 这里之后考虑传入父对象
    // 如果属性与父对象一致，则不需要单独再导出
    WDSvgXmlCreator::Node node(doc, &xmlNode);
    if (strokeWidth)
        node.attr("stroke-width", strokeWidth.value());

    if (stroke)
        if (stroke == ColorForNone())
            node.attr("stroke", std::string("none"));
        else
            node.attr("stroke", stroke.value());

    if (fill)
        if (fill == ColorForNone())
            node.attr("fill", std::string("none"));
        else
            node.attr("fill", fill.value());

    if (transform)
        node.attr("transform", transform.value().toString());
}

void SvgAttr::fromSvgAttrs(const XMLNode& xmlNode)
{
    for (XMLAttr* attr = xmlNode.first_attribute(); attr != nullptr; attr = attr->next_attribute())
    {
        if (_stricmp(attr->name(), "fill") == 0)
        {
            fill = ReadSvgColorAttr(attr->value());
        }
        else if (_stricmp(attr->name(), "display") == 0)
        {
        }
        else if (_stricmp(attr->name(), "opacity") == 0)
        {
        }
        else if (_stricmp(attr->name(), "fill-opacity") == 0)
        {
        }
        else if (_stricmp(attr->name(), "stroke") == 0)
        {
            stroke = ReadSvgColorAttr(attr->value());
        }
        else if (_stricmp(attr->name(), "stroke-width") == 0)
        {
            strokeWidth = float(atof(attr->value()));
        }
        else if (_stricmp(attr->name(), "stroke-opacity") == 0)
        {
        }
        /// 这里解析还有问题,目前只是解析单个函数，还不支持多个级联:
        /// like: transform="rotate(-10 50,100) translate(10 20) skewX(4) scale(1,0.5)"
        /// skewX() ,x轴的倾斜度数
        /// skewY() ,y轴的倾斜度数
        /// scale() ,x,y轴的缩放
        /// rotate(), 绕给定点的旋转角度
        /// translate(), 将对象移动x,y,如果么没有提供y,不提供，则为0
        /// matrix() 六个值的矩阵信息
        /// transform="rotate(-10 50,100) translate(10 20) skewX(4) scale(1,0.5)"
        else if (_stricmp(attr->name(), "transform") == 0)
        {
            transform.emplace();
            transform.value().fromString(attr->value());
        }
        else if (_stricmp(attr->name(), "offset") == 0)
        {
        }
    }
}

SvgAttr SvgAttr::mergeParent(const SvgAttr& parent) const
{
    SvgAttr resAttr;

    if (fill)
        resAttr.fill = fill;
    else if (parent.fill)
        resAttr.fill = parent.fill;

    if (stroke)
        resAttr.stroke = stroke;
    else if (parent.stroke)
        resAttr.stroke = parent.stroke;

    if (strokeWidth)
        resAttr.strokeWidth = strokeWidth;
    else if (parent.strokeWidth)
        resAttr.strokeWidth = parent.strokeWidth;

    if (transform)
    {
        if (parent.transform)
            resAttr.transform = parent.transform.value() * transform.value();
        else
            resAttr.transform = transform;
    }
    else if (parent.transform)
        resAttr.transform = parent.transform;

    return resAttr;
}

WDSvgObject::WDSvgObject(Type type)
    :_type(type)
{
};

WDSvgObject::~WDSvgObject()
{
    for (auto pChild : _children)
    {
        if (pChild == nullptr)
            continue;
        delete pChild;
    }
    _children.clear();
};

bool WDSvgObject::addChild(WDSvgObject* child)
{
    if (child == nullptr)
        return false;
    _children.push_back(child);
    return true;
}

bool WDSvgObject::lineTo(const Vec3& v)
{
    if (_points.empty())
        return  false;

    const Vec3& prev    =   _points.back();
    const Vec3& dF      =   v - prev;
    Vec3 p0             =   prev;

    p0.x += dF.x / 3.0f;
    p0.y += dF.y / 3.0f;

    Vec3 p1 =   v;
    p1.x    -=  dF.x / 3.0f;
    p1.y    -=  dF.y / 3.0f;

    _points.push_back(p0);
    _points.push_back(p1);
    _points.push_back(v);

    return  true;
}

bool WDSvgObject::cubicBezierTo(const Vec3& ctrl1, const Vec3& ctrl2, const Vec3& pt)
{
    if (_points.empty())
        return  false;
    _points.push_back(ctrl1);
    _points.push_back(ctrl2);
    _points.push_back(pt);
    return  true;
}

size_t  WDSvgObject::toData(std::vector<FVec3>& data, int segs) const
{
    size_t  cnt =   1;
    data.push_back(FVec3(_points.front()));
    for (size_t i = 0; i < _points.size() - 1; i += 3)
    {
        cnt +=  segs;
        const auto pData = &(_points[i]);

        for (int s = 0; s < segs; ++s)
        {
           // 贝塞尔曲线公式里的't'
            double  t   =   double(s) / double(segs);
            double  k   =   1.0 - t;

            double  x   = 1.0 * pData[0].x * k * k * k
                + 3.0 * pData[1].x * t * k * k
                + 3.0 * pData[2].x * t * t * k
                + 1.0 * pData[3].x * t * t * t;

            double  y   = 1.0 * pData[0].y * k * k * k
                + 3.0 * pData[1].y * t * k * k
                + 3.0 * pData[2].y * t * t * k
                + 1.0 * pData[3].y * t * t * t;

            data.push_back(FVec3((float)x, (float)y, 0));
        }
    }
    return  cnt;
}
size_t  WDSvgObject::computeDataLength(int segs) const
{
    size_t  cnt =   1;
    for (size_t i = 0; i < _points.size() - 1;  i += 3)
    {
        cnt +=  segs;
    }
    return  cnt;
}

XMLNode* WDSvgObject::createSvg(XMLDoc& doc, XMLNode* parent, const char* value) const
{
    WDSvgXmlCreator::Node node(doc, parent);
    auto nd = node.append(TypeToString(_type), value);
    // 序列化属性
    attr().toSvgAttrs(doc, *nd.native());

    return nd.native();
}

WDSvgObject* WDSvgObject::ParseSvgNode(XMLDoc& doc, XMLNode* node)
{
    WDSvgObject* pObj = nullptr;

    switch (WDSvgObject::TypeFromString(node->name()))
    {
    case WDSvgObject::T_Group:
        pObj = new WDSvgGroup;
        break;
    case WDSvgObject::T_Rect:
        pObj = new WDSvgRect;
        break;
    case WDSvgObject::T_Circle:
        pObj = new WDSvgCircle;
        break;
    case WDSvgObject::T_Ellipse:
        pObj = new WDSvgEllipse;
        break;
    case WDSvgObject::T_Text:
        pObj = new WDSvgText;
        break;
    case WDSvgObject::T_Path:
        pObj = new WDSvgPath;
        break;
    case WDSvgObject::T_Line:
        pObj = new WDSvgLine;
        break;
    case WDSvgObject::T_PolyLine:
        pObj = new WDSvgPolyLine;
        break;
    case WDSvgObject::T_Polygon:
        pObj = new WDSvgPolygon;
        break;
    case WDSvgObject::T_SvgRoot:
        pObj = new WDSvgSvgRoot;
        break;
    default:
        break;
    }

    if (pObj == nullptr || !pObj->fromSvg(doc, node))
        return nullptr;

    return pObj;
}

const char* WDSvgObject::ParseNumber(const char* s, char* it)
{
    const int   last    =   63;
    int         i       =   0;

    while (*s && (isspace(*s) || (*s == ',')) )
        s++;
    // sign
    if (*s == '-' || *s == '+') 
    {
        if (i < last) 
            it[i++] = *s;
        s++;
    }
    // integer part
    while (*s && isdigit(*s))
    {
        if (i < last) 
            it[i++] = *s;
        s++;
    }
    if (*s == '.') 
    {
        // decimal point
        if (i < last) 
            it[i++] = *s;
        s++;
        // fraction part
        while (*s && isdigit(*s)) 
        {
            if (i < last) 
                it[i++] = *s;
            s++;
        }
    }
    // exponent
    if ((*s == 'e' || *s == 'E') && (s[1] != 'm' && s[1] != 'x')) 
    {
        if (i < last) 
            it[i++] = *s;
        s++;
        if (*s == '-' || *s == '+') 
        {
            if (i < last) it[i++] = *s;
                s++;
        }
        while (*s && isdigit(*s)) 
        {
            if (i < last) it[i++] = *s;
                s++;
        }
    }
    it[i] = '\0';

    return s;
}

std::string  WDSvgObject::TypeToString(Type t)
{
    switch (t)
    {
    case T_Group:   return "g";
    case T_Rect:    return "rect";
    case T_Circle:  return "circle";
    case T_Ellipse: return "ellipse";
    case T_Text:    return "text";
    case T_Path:    return "path";
    case T_Line:    return "line";
    case T_PolyLine:return "polyline";
    case T_Polygon: return "polygon";
    case T_SvgRoot: return "svg";
    default:        return "NONE";
    }
}
WDSvgObject::Type WDSvgObject::TypeFromString(const std::string& s)
{
    if (s == "g")       return T_Group;
    if (s == "rect")    return T_Rect;
    if (s == "circle")  return T_Circle;
    if (s == "ellipse") return T_Ellipse;
    if (s == "text")    return T_Text;
    if (s == "path")    return T_Path;
    if (s == "line")    return T_Line;
    if (s == "polyline")return T_PolyLine;
    if (s == "polygon") return T_Polygon;
    if (s == "svg")     return T_SvgRoot;
    else                return T_None;
}

WD_NAMESPACE_END