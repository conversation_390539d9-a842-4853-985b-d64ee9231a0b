#pragma once
#include    "WDSvgObject.h"
WD_NAMESPACE_BEGIN

class SVT_TOOL_API WDSvgPath : public WDSvgObject
{
public:
    using   ArrayVec2   =   std::vector<Vec2>;
    enum    PathCMD
    {
        /// move to
        PC_moveTo       =   'M',
        /// line to
        PC_lineTo       =   'L',
        /// horizontal line to
        PC_hTo          =   'H',
        /// vertical line to
        PC_vTo          =   'V',
        /// smooth curve to
        /// 三次贝塞尔曲线
        PC_smoothTo     =   'S',
        /// curve to
        /// 三次贝塞尔曲线
        PC_cubicTo      =   'C',
        /// smooth quadratic bezier curve to
        /// 二次贝塞尔曲线
        PC_smoothQuadTo =   'Q',
        /// quadratic bezier curve to
        ///二次贝塞尔曲线
        PC_quadTo       =   'T',
        /// close path
        PC_Z            =   'Z',
    };
    struct  CMDData
    {
        PathCMD     cmd;
        ArrayVec2   datas;
    };
    using   ArrayCmd    =   std::vector<CMDData>;
public:
    ArrayCmd cmds;
public:
    WDSvgPath();
public:
    /**
    *   @brief  设置颜色信息
    *
    *   下面的命令可用于路径数据：
    *   M = moveto
    *   L = lineto
    *   H = horizontal lineto
    *   V = vertical lineto
    *   C = curveto
    *   S = smooth curveto
    *   Q = quadratic Bézier curve
    *   T = smooth quadratic Bézier curveto
    *   A = elliptical Arc
    *   Z = closepath
    *   注意：以上所有命令均允许小写字母。大写表示绝对定位，小写表示相对定位。
    */
    virtual bool        fromSvg(XMLDoc&, XMLNode*) override final;

    /**
    *   数据转换成XMLNode,返回创建的节点
    */
    virtual XMLNode*    toSvg(XMLDoc&, XMLNode*) const override final;
private:
    /**
    *   @brief  设置颜色信息
    *   标准中，点与点直接有的用',',有的是' ';,需要统一
    */
    const char* buildPath(const char*);

    const char* pathMoveTo(const char* data, Vec3& cp, bool ref);
    const char* pathLineTo(const char* data, Vec3& cp, bool ref);
    const char* pathHLineTo(const char* data, Vec3& cp, bool ref);
    const char* pathVLineTo(const char* data, Vec3& cp, bool ref);

    // 贝塞尔曲线
    const char* pathcubicBezierTo(const char* data, Vec3& cp0, Vec3& cp1, bool ref);
    const char* pathCubicBezierShortTo(const char* data, Vec3& cp0, Vec3& cp1, bool ref);
    const char* pathQuadBezierTo(const char* data, Vec3& cp0, Vec3& cp1, bool ref);
    const char* pathQuadBezierShortTo(const char* data, Vec3& cp0, Vec3& cp1, bool ref);

    const char* pathArcTo(const char* data, Vec3& cp0, bool ref);

    static const char*  NextCMD(const char* src);
    static int  IsDigit(char c);
    static bool IsCmd(char ch);
};

WD_NAMESPACE_END
