#include    "WDSvgTransform.h"
#include    <sstream>

WD_NAMESPACE_BEGIN

WDSvgTransform::WDSvgTransform()
{
    _data[0] = 1.0f;    _data[1] = 0.0f;
    _data[2] = 0.0f;    _data[3] = 1.0f;
    _data[4] = 0.0f;    _data[5] = 0.0f;
}
WDSvgTransform::~WDSvgTransform()
{
}

void    WDSvgTransform::move(double x, double y)
{
    _data[0] = 1.0f;    _data[1] = 0.0f;
    _data[2] = 0.0f;    _data[3] = 1.0f;
    _data[4] = x;       _data[5] = y;
}

void    WDSvgTransform::rotate(double degree)
{
    double  rad =   DegToRad(degree);
    double  cs  =   cos(rad);
    double  sn  =   sin(rad);
    _data[0] =  cs;     _data[1] = sn;
    _data[2] =  -sn;    _data[3] = cs;
    _data[4] =  0.0f;   _data[5] = 0.0f;
}

void    WDSvgTransform::scale(double x, double y)
{
    _data[0] = x;       _data[1] = 0.0f;
    _data[2] = 0.0f;    _data[3] = y;
    _data[4] = 0.0f;    _data[5] = 0.0f;
}

void    WDSvgTransform::skewX(double degree)
{
    double  rad =   DegToRad(degree);
    _data[0] = 1.0f;        _data[1] = 0.0f;
    _data[2] = tan(rad);    _data[3] = 1.0f;
    _data[4] = 0.0f;        _data[5] = 0.0f;
}

void    WDSvgTransform::skewY(double degree)
{
    double  rad =   DegToRad(degree);
    _data[0] = 1.0f;    _data[1] = tan(rad);
    _data[2] = 0.0f;    _data[3] = 1.0f;
    _data[4] = 0.0f;    _data[5] = 0.0f;
}

Vec2    WDSvgTransform::operator*(const Vec2& pt)
{
    double  x = pt.x * _data[0] + pt.y * _data[2] + _data[4];
    double  y = pt.x * _data[1] + pt.y * _data[3] + _data[5];
    return  Vec2(x, y);
}

WDSvgTransform& WDSvgTransform::operator*= (const WDSvgTransform& pt)
{
    *this = Multiply(*this, pt);
    return  *this;
}


std::string WDSvgTransform::toString() const
{
    static const char* fmt = "matrix(%f %f %f %f %f %f)";
    char buf[256];
    sprintf(buf, fmt, _data[0], _data[1], _data[2], _data[3], _data[4], _data[5]);
    return buf;
}

struct Operator {
    std::string name;
    std::vector<std::string> params;
};
static std::vector<Operator> ReadOperations(const char* str)
{
    // 解析SVG transform字符串
    std::vector<Operator> opts;
    std::string trimmed_str = str;
    // 将所有逗号替换为空格
    trimmed_str = StringReplace(trimmed_str, ",", " ");
    // 转为小写
    trimmed_str = StringToLower(trimmed_str);
    // 定义正则表达式模式
    // 匹配函数名（可能后跟多个空格）和参数部分（括号内）
    std::regex func_pattern(R"(\s*(\w+)\s*\(\s*([^)]*?)\s*\)\s*)");
    // 使用正则表达式迭代器查找所有匹配的函数
    auto words_begin = std::sregex_iterator(trimmed_str.begin(), trimmed_str.end(), func_pattern);
    auto words_end = std::sregex_iterator();

    for (std::sregex_iterator i = words_begin; i != words_end; ++i)
    {
        std::smatch match = *i;
        Operator t;
        t.name = match[1].str();
        // 处理参数部分
        std::string param_str = match[2].str();
        // 使用stringstream分割参数（按空格）
        std::istringstream iss(param_str);
        std::string param;
        while (iss >> param)
        {
            t.params.push_back(param);
        }
        opts.push_back(t);
    }
    return opts;
}

// 解析角度字符串
// 转化为角度制输出
static std::optional<double> ParseAngle(const std::string& str) {
    // 正则表达式模式：匹配数值部分和可选的单位后缀
    static const std::regex pattern(
        R"(^\s*([+-]?\d*\.?\d+)\s*(deg|rad|grad|turn)?\s*$)",
        std::regex::icase
    );

    std::smatch match;
    if (std::regex_match(str, match, pattern)) {
        // 提取数值部分
        double value = std::stod(match[1].str());
        // 提取单位部分（默认为度）
        std::string unitStr = match[2].str();
        std::transform(unitStr.begin(), unitStr.end(), unitStr.begin(),
            [](unsigned char c) { return std::tolower(c); });

        if (unitStr == "rad")
        {
            return RadToDeg(value);
        }
        else if (unitStr == "grad")
        {
            return 0.9 * value;
        }
        else if (unitStr == "turn")
        {
            return value * 360.0;
        }
        return value;
    }
    return std::nullopt;
}

bool WDSvgTransform::fromString(const char* attrValue)
{
    if (attrValue == nullptr)
        return false;
    WDSvgTransform transform;
    // 读取操作和参数
    const auto opts = ReadOperations(attrValue);
    for (const auto& opt : opts)
    {
        WDSvgTransform localTransform;
        const auto& optName = opt.name;
        const auto& optParams = opt.params;
        // 根据操作生成变换矩阵
        if (optName == "matrix" && optParams.size() == 6)
        {
            for (int i = 0; i < optParams.size(); i++)
            {
                if (!sscanf(optParams[i].data(), "%lf", &(localTransform.data()[i])))
                    return false;
            }
        }
        else if (optName == "translate")
        {
            /// translate(x)
            /// translate(x,y)
            double x, y;
            if (optParams.size() == 1)
            {
                y = 0.0f;
            }
            else if (optParams.size() == 2)
            {
                if (!sscanf(optParams[1].data(), "%lf", &y))
                    return false;
            }
            else
                return false;

            if (!sscanf(optParams[0].data(), "%lf", &x))
                return false;
            localTransform.move(x, y);
        }
        else if (optName == "scale")
        {
            /// scale(x)
            /// scale(x,y)
            double x;
            double y;
            if (optParams.size() == 1 || optParams.size() == 2)
            {
                if (!sscanf(optParams[0].data(), "%lf", &x))
                    return false;
                if (optParams.size() == 2)
                {
                    if (!sscanf(optParams[1].data(), "%lf", &y))
                        return false;
                }
                else
                    y = x;
            }
            else
                return false;

            localTransform.scale(x, y);
        }
        else if (optName == "rotate")
        {
            /// rotate(degree)
            /// rotate(degree x, y)
            double degree;
            double x = 0.0f;
            double y = 0.0f;
            if (optParams.size() == 1 || optParams.size() == 3)
            {
                // 读取角度
                auto deg = ParseAngle(optParams[0]);
                degree = deg.value();
                if (optParams.size() == 3)
                {
                    if (!sscanf(optParams[1].data(), "%lf", &x) ||
                        !sscanf(optParams[2].data(), "%lf", &y))
                        return false;
                }
                WDSvgTransform  transNeg;
                WDSvgTransform  transPos;
                WDSvgTransform  rot;
                transNeg.move(-x, -y);
                transPos.move(+x, +y);
                rot.rotate(degree);
                localTransform = transNeg * rot * transPos;
            }
            else
                return false;
        }
        else if (optName == "skewx")
        {
            if (optParams.size() != 1)
                return false;
            auto deg = ParseAngle(optParams[0]);
            if (deg)
                localTransform.skewX(deg.value());
        }
        else if (opt.name == "skewY")
        {
            if (optParams.size() != 1)
                return false;
            auto deg = ParseAngle(optParams[0]);
            if (deg)
                localTransform.skewY(deg.value());
        }
        else
            return false;

        transform = localTransform * transform;
    }
    *this = transform;
    return true;
}

WDSvgTransform WDSvgTransform::Multiply(const WDSvgTransform& t, const WDSvgTransform& s)
{
    WDSvgTransform    r;
    double  t0 = t[0] * s[0] + t[1] * s[2];
    double  t2 = t[2] * s[0] + t[3] * s[2];
    double  t4 = t[4] * s[0] + t[5] * s[2] + s[4];
    r[1] = t[0] * s[1] + t[1] * s[3];
    r[3] = t[2] * s[1] + t[3] * s[3];
    r[5] = t[4] * s[1] + t[5] * s[3] + s[5];
    r[0] = t0;
    r[2] = t2;
    r[4] = t4;
    return  r;
}


WD_NAMESPACE_END