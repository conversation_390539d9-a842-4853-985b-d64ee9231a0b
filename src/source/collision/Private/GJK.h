#pragma once

#include "core/math/Math.hpp"

WD_NAMESPACE_BEGIN


/**
 * @brief GJK 碰撞检查算法 <PERSON>-<PERSON>-<PERSON>erthi
 *  具体原理请查看GJK算法
 * @tparam TSimplex 单纯形类型
 *  如果是 2D 单纯形，则检测的是凸多边形
 *  如果是 3D 单纯形，则检测的是凸多面体
 * @tparam T 数值类型
*/
template <typename TSimplex>
class GJK
{
public:
    using T = double;
public:
    /**
     * @brief 计算的结果状态
    */
    enum RStatus 
    {
        // 计算失败，达到最大迭代次数, 可能是数据不规范
        RS_Failed = 0,
        // 计算成功, 未发生碰撞
        RS_Valid,
        // 计算成功, 并且发生了碰撞
        RS_Inside,
    };
public:
    GJK(T minDistance = T(0.00001), int maxIterations = 128)
        : _minDistance(minDistance)
        , _maxIterations(maxIterations)
    {
    }
    ~GJK()
    {

    }
public:
    /**
     * @brief 执行算法
     * @tparam TShapeA 凸形状类型A, 需要实现成员方法  TVec3<T> support(const TVec3<T>& dir) const;
     * @tparam TShapeB 凸形状类型B, 需要实现成员方法  TVec3<T> support(const TVec3<T>& dir) const;
     * @param shapeA 凸形状A
     * @param shapeB 凸形状B
     * @param guess 初始猜测方向，一般指定两个形状中心点的连线(可以不用单位化), 也可以不指定
     * @return 执行结果
    */
    template <typename TShapeA, typename TShapeB>
    RStatus evaluate(const TShapeA& shapeA
        , const TShapeB& shapeB
        , const TVec3<T>& guess = TVec3<T>::AxisX())
    {
        RStatus rStatus = RStatus::RS_Valid;

        _simplex.reset();

        // 得到第一个闵可夫斯基差点,并加入到单纯形,做为单纯形的第一个顶点
        TVec3<T> dir = MinkowskiDiffPoint<TShapeA, TShapeB>(shapeA, shapeB, guess);
        _simplex.pushBack(dir, dir.normalized());
        // 根据第一个单纯形顶点到原点的方向，获取到第二个搜索方向
        dir = -dir;
        // 迭代计数器
        int itrCount = 0;
        // 
        //T alpha = T(0);
        // 开始GJK算法迭代
        do
        {
            // 校验0向量
            const auto dirLen = dir.length();
            if (dirLen < _minDistance)
            {// 接触或者在内部
                rStatus = RStatus::RS_Inside;
                break;
            }
            // 计算为单位向量
            const auto nDir = dir / dirLen;
            // 根据搜索方向，查询新的闵可夫斯基差点
            auto tPoint = MinkowskiDiffPoint<TShapeA, TShapeB>(shapeA, shapeB, nDir);
            // 由于这里的搜索方向始终是从旧的单纯形开始，向原点的方向(注1)
            //  因此，这里出现以下两种情况时, 说明图形是分离的(说明单纯形可能已经是离原点最近的形状并且单纯形在搜索方向上没有跨越原点)
            //  情况a: tPoint点与单纯形的某个顶点重合
            //bool bFound = false;
            //for (int i = 0; i < _simplex.count(); ++i)
            //{
            //    const auto disSq = TVec3<T>::DistanceSq(_simplex.point(i), tPoint);
            //    if (disSq < _minDistance)
            //    {
            //        bFound = true;
            //        break;
            //    }
            //}
            //if (bFound)
            //{
            //    rStatus = RStatus::RS_Valid;
            //    break;
            //}
            //  情况b: tPoint是否更靠近原点，如果不是，表明找到了最近点/最近边，跳出
            //const T	omega = TVec3<T>::Dot(-dir, tPoint) / dirLen;
            //alpha = Max(omega, alpha);
            //if (((dirLen - alpha) - (_minDistance * dirLen)) <= 0)
            //{
            //    rStatus = RStatus::RS_Valid;
            //    break;
            //}
            // 加入到单纯形
            _simplex.pushBack(tPoint, dir / dirLen);
            // 单纯形是否包含原点，如果包含，证明两个形状接触了，直接跳出
            if (_simplex.contains(TVec3<T>::Zero(), _minDistance))
            {
                rStatus = RStatus::RS_Inside;
                break;
            }
            // 获取下一次迭代方向，如果获取成功，继续迭代，如果获取失败，表明已经找到了两个形状的最近点，直接跳出
            if (!_simplex.nextDirection(_minDistance))
            {
                rStatus = RStatus::RS_Valid;
                break;
            }
            // 根据权重计算下一次的迭代方向
            dir = TVec3<T>::Zero();
            for (int i = 0; i < _simplex.count(); ++i)
            {
                dir += _simplex.point(i) * _simplex.weight(i);
            }
            // 迭代次数
            itrCount++;
            // 是否超过最大迭代次数, 如果超过最大迭代次数，说明可能是数据不规范, 返回失败
            if (itrCount >= _maxIterations)
            {
                rStatus = RStatus::RS_Failed;
                break;
            }
        } while (true);

        return rStatus;
    }
    /**
     * @brief 获取单纯形
    */
    inline const TSimplex& simplex() const 
    {
        return _simplex;
    }
    /**
     * @brief 获取单纯形
    */
    inline TSimplex& simplex()
    {
        return _simplex;
    }
public:
    /**
     * @brief 指定两个形状以及查询方向，获取一个闵可夫斯基差点
     * @tparam TShapeA 凸形状类型A, 需要实现成员方法  TVec3<T> support(const TVec3<T>& dir) const;
     * @tparam TShapeB 凸形状类型B, 需要实现成员方法  TVec3<T> support(const TVec3<T>& dir) const;
     * @param shapeA 凸形状A
     * @param shapeB 凸形状B
     * @param dir 查询方向
     * @return 闵可夫斯基差点
    */
    template <typename TShapeA, typename TShapeB>
    static inline TVec3<T> MinkowskiDiffPoint(const TShapeA& shapeA, const TShapeB& shapeB, const TVec3<T>& dir)
    {
        const auto p0 = shapeA.support(dir);
        const auto p1 = shapeB.support(-dir);
        return p0 - p1;
    }
private:
    // 单纯形
    TSimplex _simplex;
    // 最小距离值,控制算法精度
    T _minDistance;
    // GJK算法的最大迭代次数, 超过最大迭代次数后，仍然未找到结果，将返回计算失败
    int _maxIterations;
};

/**
 * @brief GJK2D单纯型, 三角面
*/
class GJKSimplex2D
{
public:
    using T = double;
public:
    /**
     * @brief 维度
    */
    static constexpr int Dimensionality() 
    {
        return 2;
    }
public:
    /**
     * @brief 重置
    */
    inline void reset()
    {
        _count = 0;
    }
    /**
     * @brief 添加点以及该点的查询向量以及查询权重
    */
    inline void pushBack(const TVec3<T>& pt, const TVec3<T>& dir)
    {
        assert(_count < 3);
        _vs[_count] = pt;
        _dirs[_count] = dir;
        _count++;
    }
    /**
     * @brief 获取当前顶点的个数
    */
    inline int count() const
    {
        return _count;
    }
    /**
     * @brief 根据下标获取顶点
     *
     */
    inline const TVec3<T>& point(size_t index) const
    {
        assert(index < _count);
        return _vs[index];
    }
    /**
     * @brief 根据下标获取顶点的查询方向
     *
     */
    inline const TVec3<T>& dir(size_t index) const
    {
        assert(index < _count);
        return _dirs[index];
    }
    /**
     * @brief 权重，用于反算最近点
    */
    inline const T& weight(size_t index) const 
    {
        assert(index < _count);
        return _ws[index];
    }
    /**
     * @brief 判断是否包含指定点
     *  包含返回true,不包含返回false
    */
    bool contains(const TVec3<T>& pt, T epsilon = NumLimits<T>::Epsilon) const;
    /**
     * @brief 获取下一次的迭代方向
     * @return 如果获取到下一次迭代方向，返回true； 否则表示已经找到了最近点, 不用继续迭代， 返回false
    */
    bool nextDirection(T epsilon = NumLimits<T>::Epsilon);
private:
    // 顶点
    TVec3Array<T, 3> _vs = { TVec3<T>::Zero() };
    // 顶点的查询向量
    TVec3Array<T, 3> _dirs = { TVec3<T>::Zero() };
    // 权重，用于反算最近点 (weights)
    std::array<T, 3> _ws = { T(0) };
    // 索引
    int _count = 0;
};

/**
 * @brief GJK3D单纯型, 四面体
*/
class GJKSimplex3D
{
public:
    using T = double;
public:
    /**
     * @brief 维度
    */
    static constexpr int Dimensionality()
    {
        return 3;
    }
public:
    /**
     * @brief 重置
    */
    inline void reset()
    {
        _count = 0;
    }
    /**
     * @brief 添加点以及该点的查询向量以及查询权重
    */
    inline void pushBack(const TVec3<T>& pt, const TVec3<T>& dir)
    {
        assert(_count < 4);
        _vs[_count] = pt;
        _dirs[_count] = dir;
        _count++;
    }
    /**
     * @brief 获取当前顶点的个数
    */
    inline int count() const
    {
        return _count;
    }
    /**
     * @brief 根据下标获取顶点
     *
     */
    inline const TVec3<T>& point(size_t index) const
    {
        assert(index < _count);
        return _vs[index];
    }
    /**
     * @brief 根据下标获取顶点的查询方向
     *
     */
    inline const TVec3<T>& dir(size_t index) const
    {
        assert(index < _count);
        return _dirs[index];
    }
    /**
     * @brief 权重，用于反算最近点
    */
    inline const T& weight(size_t index) const
    {
        assert(index < _count);
        return _ws[index];
    }
    /**
     * @brief 判断是否包含指定点
    */
    bool contains(const TVec3<T>& pt, T epsilon = NumLimits<T>::Epsilon) const;
    /**
     * @brief 获取下一次的迭代方向
     * @return 如果获取到下一次迭代方向，返回true； 否则表示已经找到了最近点, 不用继续迭代， 返回false
    */
    bool nextDirection(T epsilon = NumLimits<T>::Epsilon);
private:
    // 顶点
    TVec3Array<T, 4> _vs = { TVec3<T>::Zero() };
    // 顶点的查询向量
    TVec3Array<T, 4> _dirs = { TVec3<T>::Zero() };
    // 权重，用于反算最近点 (weights)
    std::array<T, 4> _ws = { T(0) };
    // 索引
    int _count = 0;
};

/**
 * @brief 2D GJK 碰撞检查算法
*/
using GJK2D = GJK<GJKSimplex2D>;
/**
 * @brief 3D GJK 碰撞检查算法
*/
using GJK3D = GJK<GJKSimplex3D>;

WD_NAMESPACE_END