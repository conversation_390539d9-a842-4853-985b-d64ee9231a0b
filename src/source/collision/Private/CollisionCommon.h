#pragma once

#include "core/WDCore.h"
#include "core/geometry/WDGeometryMgr.h"

#include "BulletCollision/NarrowPhaseCollision/btGjkEpa2.h"
#include "BulletCollision/CollisionShapes/btConvexHullShape.h"
#include "BulletCollision/CollisionShapes/btBoxShape.h"
#include "BulletCollision/CollisionShapes/btCylinderShape.h"
#include "BulletCollision/CollisionShapes/btSphereShape.h"

#include "BulletCollision/CollisionShapes/btTriangleMesh.h"
#include "BulletCollision/CollisionDispatch/btDefaultCollisionConfiguration.h"
#include "BulletCollision/Gimpact/btGImpactShape.h"
#include "BulletCollision/Gimpact/btGImpactCollisionAlgorithm.h"

//#define COLLISION_ASSERT_ENABLED

#ifdef COLLISION_ASSERT_ENABLED
#define COLLISION_ASSERT(expression) assert(expression)
#else
#define COLLISION_ASSERT(expression) ((void)0)
#endif

WD_NAMESPACE_BEGIN

/**
 * @brief WD Vec3 转换到 bullet Vec3
*/
template <typename T = btScalar>
static inline btVector3 Vec3ToBt(const TVec3<T>& v)
{
    return btVector3(v.x, v.y, v.z);
}
/**
 * @brief bullet Vec3 转换到 WD Vec3
*/
template <typename T = btScalar>
static inline TVec3<T> Vec3FromBt(const btVector3& v)
{
    return TVec3<T>(v.getX(), v.getY(), v.getZ());
}
/**
 * @brief WD Quat 转换到 bullet Quat
*/
template <typename T = btScalar>
static inline TQuat<T> QuatToBt(const TQuat<T>& q)
{
    return btQuaternion(q.x(), q.y(), q.z(), q.w());
}
/**
 * @brief bullet Quat 转换到 WD Quat
*/
template <typename T = btScalar>
static inline btQuaternion QuatFromBt(const btQuaternion& q)
{
    return TQuat<T>(q.getW(), q.getX(), q.getY(), q.getZ());
}
/**
 * @brief WD Mat4 转换到 bullet btTransform
 */
template <typename T = btScalar>
static inline btTransform Mat4ToBt(const TMat4<T>& mat) 
{
    TMat3<T> m3 = TMat4<T>::ToMat3(mat);
    return btTransform(btMatrix3x3(m3[0][0], m3[0][1], m3[0][2]
        , m3[1][0], m3[1][1], m3[1][2]
        , m3[2][0], m3[2][1], m3[2][2])
        , Vec3ToBt(mat.extractTranslation()));
}


/**
 * @brief 形状构建器
*/
class ShapeBuilder
{
public:
    class ConvexShapeWrapper
    {
    private:
        btConvexHullShape* _pShape;
    public:
        explicit ConvexShapeWrapper(btConvexHullShape* pShape = nullptr)
        {
            _pShape = pShape;
        }
        ConvexShapeWrapper(const ConvexShapeWrapper& right) = delete;
        ConvexShapeWrapper(ConvexShapeWrapper&& right)
            : _pShape(nullptr)
        {
            std::swap(_pShape, right._pShape);
        }
        ConvexShapeWrapper& operator=(const ConvexShapeWrapper& right) = delete;
        ConvexShapeWrapper& operator=(ConvexShapeWrapper&& right)
        {
            if (this == &right)
                return *this;
            std::swap(_pShape, right._pShape);
            return *this;
        }
        ~ConvexShapeWrapper()
        {
            if (_pShape != nullptr)
            {
                delete _pShape;
                _pShape = nullptr;
            }
        }
    public:
        inline btConvexHullShape* shape() const
        {
            return _pShape;
        }
    };

    class GImpactShapeWrapper 
    {
    private:
        btTriangleMesh* _pTriMesh;
        btGImpactMeshShape* _pShape;
    public:
        explicit GImpactShapeWrapper(btTriangleMesh* pTriMesh = nullptr)
            : _pTriMesh(pTriMesh)
            , _pShape(nullptr)
        {
            if (_pTriMesh != nullptr)
            {
                _pShape = new btGImpactMeshShape(_pTriMesh);
                _pShape->updateBound();
            }
        }
        GImpactShapeWrapper(const GImpactShapeWrapper& right) = delete;
        GImpactShapeWrapper(GImpactShapeWrapper&& right)
            : _pTriMesh(nullptr)
            , _pShape(nullptr)
        {
            std::swap(_pTriMesh, right._pTriMesh);
            std::swap(_pShape, right._pShape);
        }
        GImpactShapeWrapper& operator=(const GImpactShapeWrapper& right) = delete;
        GImpactShapeWrapper& operator=(GImpactShapeWrapper&& right)
        {
            if (this == &right)
                return *this;
            std::swap(_pTriMesh, right._pTriMesh);
            std::swap(_pShape, right._pShape);
            return *this;
        }
        ~GImpactShapeWrapper()
        {
            if (_pShape != nullptr)
            {
                delete _pShape;
                _pShape = nullptr;
            }
            if (_pTriMesh != nullptr)
            {
                delete _pTriMesh;
                _pTriMesh = nullptr;
            }
        }
    public:
        inline btGImpactMeshShape* shape() const
        {
            return _pShape;
        }
    };
public:
    /**
     * @brief 获取变换几何体顶点的矩阵, 如果是标准基本体，则不包含缩放
     */
    static DMat4 GetGeomVertexTransform(const WDGeometry& geom, const DMat4& gTransform);
    /**
     * @brief 根据几何体类型，快速判断几何体体是否是凸多面体
     *  部分标准基本体可能不是凸多面体类型，例如: 方环，圆环，拉伸体，旋转体，放样体...
     * @param geom 几何体
     * @return 是凸多面体则返回true
     */
    static bool IsConvexHullShape(const WDGeometry& geom);
    /**
     * @brief 指定几何体创建凸碰撞体
     *  如果是标准基本体的凸多面体，则创建，否则返回nullptr
     *  这个接口能否创建可以用 IsConvexHullShape来判断
     * @param geom 几何体对象
     * @param vTransform 变换几何体顶点的矩阵
     * @return 碰撞体结果,创建失败返回nullptr
    */
    static ConvexShapeWrapper BuildConvexHullShape(const WDGeometry& geom
        , const DMat4& vTransform);
    /**
     * @brief 指定几何体创建三角面碰撞体
     * @param geom 几何体对象
     * @param vTransform 变换几何体顶点的矩阵
     * @return 碰撞体结果,创建失败返回nullptr
     */
    static GImpactShapeWrapper BuildGImpactShape(const WDGeometry& geom
        , const DMat4& vTransform);
    /**
     * @brief 校验几何体是否是凸多面体, 这个算法比较慢
     *  根据凸多面体的定义来判断: 如果多面体在他们每一面决定的平面的同一侧，则称此多面体为凸多面体
     * @return 如果为凸多面体或者顶点或者三角面个数为0，返回true,否则返回false
    */
    static bool IsConvexGeometry(const WDGeometry& geom);
private:
    /**
     * @brief 指定顶点列表，创建凸碰撞形状
     * @param points 顶点列表
     * @param transform 顶点的变换矩阵
     * @return 碰撞形状
    */
    static btConvexHullShape* CreateConvexHullShape(const FVec3Vector& points, const DMat4& transform);
};



WD_NAMESPACE_END