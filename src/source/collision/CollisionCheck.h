#pragma once

#include "core/WDCore.h"
#include "core/geometry/WDGeometry.h"

WD_NAMESPACE_BEGIN

class CollisionCheckPrivate;
/**
* @brief 碰撞检测
*/
class CollisionCheck
{
public:
    CollisionCheck();
    ~CollisionCheck();
public:
    /**
    * @brief 两个包围盒碰撞检测
    * @param sAabb 主体包围盒
    * @param oAabb 客体包围盒
    * @param expansion 包围盒扩充(碰撞间隙/碰撞误差), 最小检测距离, 当两个几何体未接触且最短距离小于该距离时，仍然认为是碰撞了
    * @return 是否碰撞
    */
    bool exec(const Aabb3& sAabb, const Aabb3& oAabb, double expansion);
    /**
    * @brief 两个包围盒碰撞检测
    * @param sAabb 主体包围盒
    * @param sTransform 主体包围盒变换矩阵
    * @param oAabb 客体包围盒
    * @param oTransform 客体包围盒变换矩阵
    * @param expansion 包围盒扩充(碰撞间隙/碰撞误差), 最小检测距离, 当两个几何体未接触且最短距离小于该距离时，仍然认为是碰撞了
    * @return 是否碰撞
    */
    bool exec(const Aabb3& sAabb
        , const Mat4& sTransform
        , const Aabb3& oAabb
        , const Mat4& oTransform
        , double expansion);
    /**
     * @brief 碰撞返回代码
    */
    enum CollisionRetCode
    {
        /**
         * @brief 未知错误
        */
        CRC_Error = 0,
        /**
         * @brief 检测成功，结果可用
        */
        CRC_Success = 1,
        /**
         * @brief 无效的碰撞主体几何体对象数据
        */
        CRC_InvalidSGeo,
        /**
         * @brief 无效的碰撞客体几何体对象数据
        */
        CRC_InvalidOGeo,
        /**
         * @brief 无效数据,导致无法完成碰撞计算
        */
        CRC_InvalidData,
    };
    /**
     * @brief 碰撞结果数据
    */
    struct CollisionRetData
    {
        // 碰撞点
        std::array<DVec3, 2>  witnesses;
        // 法线
        DVec3 normal;
        //两个几何体的距离，未嵌入时为正值，嵌入时为负值
        real distance;
        CollisionRetData() 
        {
            witnesses[0]    = DVec3::Zero();
            witnesses[1]    = DVec3::Zero();
            normal          = DVec3::Zero();
            distance        = NumLimits<double>::Max;
        }
    };
    /**
    * @brief 两个几何体对象碰撞检测
    * @param sGeo 主体几何体
    * @param sTransform 主体几何体global变换矩阵, 这个矩阵一般是几何体所属节点的 globalTransform(), 不包含该几何体自身的Transform
    * @param oGeo 客体几何体
    * @param oTransform 客体几何体global变换矩阵, 这个矩阵一般是几何体所属节点的 globalTransform(), 不包含该几何体自身的Transform
    * @param minDistance 最小检测距离, 当两个几何体未接触且最短距离小于该距离时，仍然输出碰撞结果(用于最短距离测量和误差碰撞计算)
    * @param outRet 输出结果
    * @return 结果代码
    */
    CollisionRetCode exec(const WDGeometry& sGeo
        , const Mat4& sTransform
        , const WDGeometry& oGeo
        , const Mat4& oTransform
        , double minDistance
        , CollisionRetData& outRet);

private:
    CollisionCheckPrivate* _p;
    friend class CollisionCheckPrivate;
};

WD_NAMESPACE_END