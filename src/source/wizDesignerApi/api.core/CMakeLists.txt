set(TARGET_NAME api.core)

set(HEADER_FILES
	"WDEnvConfigApi.h"
	"WDNodeApi.h"
)

set(SOURCE_FILES
	"WDEnvConfigApi.cpp"
	"WDNodeApi.cpp"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

target_compile_definitions(${TARGET_NAME} PRIVATE
	-DCOREAPI_EXPORTS
)

target_link_libraries(${TARGET_NAME} PRIVATE wizDesignerCore util.rapidjson)

CopyImportedRuntimeDependency(${TARGET_NAME})