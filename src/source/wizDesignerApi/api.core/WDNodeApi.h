#pragma     once

#include    "core/WDCore.h"
#include    "core/node/WDNode.h"
#include    "core/nodeTree/WDNodeTree.h"


#ifdef WIN32
    #ifdef COREAPI_EXPORTS
    #define CORE_API _declspec(dllexport)
#else
    #define CORE_API _declspec(dllimport)
#endif
#else
    #define CORE_API
#endif



using   Core                =   void*;
using   ObjectPtr           =   void*;
using   BOOL                =   int;
using   LPWDNode            =   void*;
using   LPWDNodeTree        =   void*;
using   LPCSTR              =   const char*;
using   LPWDBMAttrValue     =   void*;
using   LPNodeCallBack      =   void*;
using   LPTreeCallBack      =   void*;
struct  WDDouble2
{
    double  x,y;
};
struct  WDDouble3
{
    double  x,y,z;
};
struct  WDDouble4
{
    double  x,y,z,w;
};
struct  WDQuat
{
    double  x,y,z,w;
};

struct  WDMat4
{
    double  data[16];
};

struct  WDMat3
{
    double  data[9];
};

/**
* @brief 添加子节点之前通知
* @param pNode 当前节点
* @param pChild 添加的子节点
*/
typedef void (*NotifyNodeAddChildBefore)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 添加子节点之后通知
* @param pNode 当前节点
* @param pChild 添加的子节点
*/
typedef void (*NotifyNodeAddChildAfter)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 添加子节点之前通知
* @param pNode 当前节点
* @param pChild 插入的子节点
* @param pNextChild 子节点的插入位置(即当前子节点插入在哪个节点之前)
*/
typedef void (*NotifyNodeInsertChildBefore)(LPWDNode pNode, LPWDNode pChild, LPWDNode pNextChild);
/**
* @brief 添加子节点之后通知
* @param pNode 当前节点
* @param pChild 插入的子节点
* @param pNextChild 子节点的插入位置(即当前子节点插入在哪个节点之前)
*/
typedef void (*NotifyNodeInsertChildAfter)(LPWDNode pNode, LPWDNode pChild, LPWDNode pNextChild);
/**
* @brief 移除子节点之前通知
* @param pNode 当前节点
* @param pChild 移除的子节点
*/
typedef void (*NotifyNodeRemoveChildBefore)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 移除子节点之后通知
* @param pNode 当前节点
* @param pChild 移除的子节点
*/
typedef void (*NotifyNodeRemoveChildAfter)(LPWDNode pNode, LPWDNode pChild);
/**
* @brief 节点更新之前通知
* @param pNode 当前更新的节点
*/
typedef void (*NotifyNodeUpdateBefore)(LPWDNode pNode);
/**
* @brief 节点更新之后通知
* @param pNode 当前更新的节点
*/
typedef void (*NotifyNodeUpdateAfter)(LPWDNode pNode);

/**
* @brief 节点销毁之前通知
* @param pNode 当前销毁的节点
*/
typedef void (*NotifyNodeDestroyBefore)(LPWDNode pNode) ;
/**
* @brief 节点销毁之后通知
*  此时节点已经调用到析构的最后一步，所以这里只能使用节点的裸指针，不建议再使用节点的数据或方法
* @param pNode 当前销毁的节点
*/
typedef void (*NotifyNodeDestroyAfter)(const LPWDNode pNode) ;
/**
* @brief 节点属性值改变通知
* @param name 属性名称
* @param currValue 当前值
* @param prevValue 改变前的值
* @param pNode 发送者
*/
typedef void (*NotifyNodeAttributeValueChanged)(  const char* name
                                            , const LPWDBMAttrValue currValue
                                            , const LPWDBMAttrValue prevValue
                                            , LPWDNode pNode);
/**
* @brief 节点的子节点重新排序通知
* @param pNode 发送者
*/
typedef void (*NotifyNodeChildrenReordered)(LPWDNode pNode);

/// NodeTree 通知
/**
* @brief 当前节点改变通知
* @param pCurrNode 当前节点
* @param pPrevNode 前一个节点
* @param sender 发送者
*/
typedef   void(*NoticeCurrentNodeChanged)(LPWDNode pCurrNode, LPWDNode pPrevNode, LPWDNodeTree sender);
/**
* @brief 添加根节点通知
*/
typedef   void(*NoticeAddTopLevelNode)(LPWDNode pNode,LPWDNodeTree sender);
/**
* @brief 移除根节点通知
*/
typedef   void(*NoticeRemoveTopLevelNode)(LPWDNode pNode, LPWDNodeTree sender);

struct  NodeNotify
{

    NotifyNodeAddChildBefore        _NotifyNodeAddChildBefore;
    
    NotifyNodeAddChildAfter         _NotifyNodeAddChildAfter;
    
    NotifyNodeInsertChildBefore     _NotifyNodeInsertChildBefore;
    
    NotifyNodeInsertChildAfter      _NotifyNodeInsertChildAfter;
    
    NotifyNodeRemoveChildBefore     _NotifyNodeRemoveChildBefore;
    
    NotifyNodeRemoveChildAfter      _NotifyNodeRemoveChildAfter; 
    
    NotifyNodeUpdateBefore          _NotifyNodeUpdateBefore;
    
    NotifyNodeUpdateAfter           _NotifyNodeUpdateAfter;
    
    NotifyNodeDestroyBefore         _NotifyNodeDestroyBefore;
    
    NotifyNodeDestroyAfter          _NotifyNodeDestroyAfter;
    
    NotifyNodeAttributeValueChanged _NotifyNodeAttributeValueChanged;

    NotifyNodeChildrenReordered     _NotifyNodeChildrenReordered;
};


struct  NodeTreeNotify
{
    NoticeCurrentNodeChanged    _NoticeCurrentNodeChanged;
    NoticeAddTopLevelNode       _NoticeAddTopLevelNode;
    NoticeRemoveTopLevelNode    _NoticeRemoveTopLevelNode;
};

extern "C"
{
    /**
    * @brief 获取设计树节点
    * @param exeDirPath 程序工作目录
    */
    CORE_API LPWDNodeTree   wdGetTree(Core pCore);
    /// <summary>
    /// 获取树节点下子节点的数量
    /// </summary>
    /// <param name="pTree"></param>
    /// <returns></returns>
    CORE_API size_t         wdGetTreeNodeCount(LPWDNodeTree pTree);
    /// <summary>
    /// 根据索引获取子节点
    /// </summary>
    /// <param name="pTree"></param>
    /// <param name="index"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdGetTreeNode(LPWDNodeTree pTree,size_t index);
    /// <summary>
    /// 获取节点的子节点数量
    /// </summary>
    /// <param name="pNode"></param>
    /// <returns></returns>
    CORE_API size_t         wdGetNodeChildCount(LPWDNode pNode);
    /// <summary>
    /// 获取子节点
    /// </summary>
    /// <param name="parent"></param>
    /// <param name="index"></param>
    /// <returns></returns>
    CORE_API LPWDNode       wdGetNodeChild(LPWDNode parent,size_t index);

    /// <summary>
    /// 获取节点名称字段的长度
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API size_t         wdGetNodeNameSize(LPWDNode object);

    /// <summary>
    /// 获取节点名称
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API LPCSTR         wdGetNodeName(LPWDNode object,char* name,size_t nBuf);
    /// <summary>
    /// 获取节点类型名称
    /// </summary>
    /// <param name="object"></param>
    /// <returns></returns>
    CORE_API LPCSTR         wdGetNodeType(LPWDNode object);
    /// <summary>
    /// 获取节点位置
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldPosition(LPWDNode object,    WDDouble3*);
    /// <summary>
    /// 获取节点的缩放
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldScale(LPWDNode object,    WDDouble3*);
    /// <summary>
    /// 获取节点的旋转信息
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldRotate(LPWDNode object,   WDQuat*);
    /// <summary>
    /// 获取节点的矩阵
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool           wdGetNodeWorldTransform(LPWDNode object,WDMat4*);

    /// <summary>
    /// 设置回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API LPNodeCallBack wdSetNodeCallback(LPWDNode object,NodeNotify* notify);
    /// <summary>
    /// 移除回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool          wdRemoveNodeCallback(LPWDNode object,LPNodeCallBack cb);


    /// <summary>
    /// 设置NodeTree回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API LPTreeCallBack wdSetNodeTreeCallback(LPWDNodeTree object,NodeTreeNotify* notify);
    /// <summary>
    /// 移除NodeTree回调
    /// </summary>
    /// <param name="object"></param>
    /// <param name=""></param>
    /// <returns></returns>
    CORE_API bool          wdRemoveNodeTreeCallback(LPWDNodeTree object,LPTreeCallBack cb);
    

}
/// using System;
/// using System.Runtime.InteropServices;
/// 
/// public class CallbackExample {
///     // 定义一个与C++中回调函数签名相匹配的委托
///     [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
///     public delegate void CallbackDelegate(int value);
/// 
///     // P/Invoke声明
///     [DllImport("MyFunctions.dll", CallingConvention = CallingConvention.Cdecl)]
///     public static extern void MyFunction(CallbackDelegate callback);
/// }
/// CallbackExample.MyFunction(new CallbackExample.CallbackDelegate(Callback));