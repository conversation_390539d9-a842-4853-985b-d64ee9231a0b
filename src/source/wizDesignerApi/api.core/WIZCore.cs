using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.InteropServices;
using System.Runtime.Remoting.Contexts;
using System.Security.Cryptography;


namespace WD
{
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct int2
    {
        public int x;
        public int y;
    }
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct WDDouble2
    {
        double x, y;
    };
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct WDDouble3
    {
        double x, y, z;
    };
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct WDDouble4
    {
        double x, y, z, w;
    };
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct WDQuat
    {
        double x, y, z, w;
    };
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct WDMat4
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
        public double[] _data;
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct WDMat3
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 9)]
        public double[] data;
    };

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct NodeNotifyCS
    {
       IntPtr   _NotifyNodeAddChildBefore       ;//Marshal.GetFunctionPointerForDelegate(NodeNotifyDelegate.NotifyNodeAddChildBefore);
       IntPtr   _NotifyNodeAddChildAfter        ;
       IntPtr   _NotifyNodeInsertChildBefore    ;   
       IntPtr   _NotifyNodeInsertChildAfter     ;   
       IntPtr   _NotifyNodeRemoveChildBefore    ;   
       IntPtr   _NotifyNodeRemoveChildAfter     ;   
       IntPtr   _NotifyNodeUpdateBefore         ;   
       IntPtr   _NotifyNodeUpdateAfter          ;   
       IntPtr   _NotifyNodeDestroyBefore        ;   
       IntPtr   _NotifyNodeDestroyAfter         ;   
       IntPtr   _NotifyNodeAttributeValueChanged;   
       IntPtr   _NotifyNodeChildrenReordered    ;
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 8)]
    public struct NodeTreeNotifyCS
    {
        IntPtr  _NoticeCurrentNodeChanged;
        IntPtr  _NoticeAddTopLevelNode;
        IntPtr  _NoticeRemoveTopLevelNode;
    }
    /// <summary>
    ///  通知定义代理
    /// </summary>
    public partial class NodeNotifyDelegate
    {
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeAddChildBefore(IntPtr pNode, IntPtr pChild);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeAddChildAfter(IntPtr pNode, IntPtr pChild);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeInsertChildBefore(IntPtr pNode, IntPtr pChild, IntPtr pNextChild);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeInsertChildAfter(IntPtr pNode, IntPtr pChild, IntPtr pNextChild);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeRemoveChildBefore(IntPtr pNode, IntPtr pChild);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeRemoveChildAfter(IntPtr pNode, IntPtr pChild);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeUpdateBefore(IntPtr pNode);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeUpdateAfter(IntPtr pNode);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeDestroyBefore(IntPtr pNode);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeDestroyAfter(IntPtr pNode);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeAttributeValueChanged(string name
                                                            , IntPtr currValue
                                                            , IntPtr prevValue
                                                            , IntPtr pNode);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NotifyNodeChildrenReordered(IntPtr pNode);
    }
    /// <summary>
    /// 节点数代理定义
    /// </summary>
    public partial class NodeTreeNotifyDelegate
    {
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NoticeCurrentNodeChanged(IntPtr pCurrNode, IntPtr pPrevNode, IntPtr sender);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NoticeAddTopLevelNode(IntPtr pNode, IntPtr sender);
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void NoticeRemoveTopLevelNode(IntPtr pNode, IntPtr* sender);
    }
}