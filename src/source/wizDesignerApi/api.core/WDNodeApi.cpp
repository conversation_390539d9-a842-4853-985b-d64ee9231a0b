#include    "WDNodeApi.h"
#include    "businessModule/design/WDBMDesign.h"
#include    "businessModule/catalog/WDBMCatalog.h"
#include    "extension/WDExtensionMgr.h"
#include    "WDRapidjson.h"
#include    "core/log/WDLoggerPort.h"
#include    "businessModule/WDBMAuditObjectMgr.h"
#include    "businessModule/WDBMPermissionMgr.h"
#include    "log/WDLogger.h"
#include    "core/businessModule/typeMgr/WDBMAttrValue.h"



class   CAPIWDNodeObserver :public WD::WDNodeObserver
{
public:
    NodeNotify  _notify =   {};
public:
/**
    * @brief 添加子节点之前通知
    * @param pNode 当前节点
    * @param pChild 添加的子节点
    */
    virtual void onNodeAddChildBefore(NodeSPtr pNode, NodeSPtr pChild)
    {
        if (_notify._NotifyNodeAddChildBefore)
        {
            _notify._NotifyNodeAddChildBefore(pNode.get(),pChild.get());
        }
    }
    /**
    * @brief 添加子节点之后通知
    * @param pNode 当前节点
    * @param pChild 添加的子节点
    */
    virtual void onNodeAddChildAfter(NodeSPtr pNode, NodeSPtr pChild)
    {
        if (_notify._NotifyNodeAddChildAfter)
        {
            _notify._NotifyNodeAddChildAfter(pNode.get(),pChild.get());
        }
    }
    /**
    * @brief 添加子节点之前通知
    * @param pNode 当前节点
    * @param pChild 插入的子节点
    * @param pNextChild 子节点的插入位置(即当前子节点插入在哪个节点之前)
    */
    virtual void onNodeInsertChildBefore(NodeSPtr pNode, NodeSPtr pChild, NodeSPtr pNextChild)
    {
        if (_notify._NotifyNodeInsertChildBefore)
        {
            _notify._NotifyNodeInsertChildBefore(pNode.get(),pChild.get(),pNextChild.get());
        }
    }
    /**
    * @brief 添加子节点之后通知
    * @param pNode 当前节点
    * @param pChild 插入的子节点
    * @param pNextChild 子节点的插入位置(即当前子节点插入在哪个节点之前)
    */
    virtual void onNodeInsertChildAfter(NodeSPtr pNode, NodeSPtr pChild, NodeSPtr pNextChild)
    {
        if (_notify._NotifyNodeInsertChildAfter)
        {
            _notify._NotifyNodeInsertChildAfter(pNode.get(),pChild.get(),pNextChild.get());
        }
    }
    /**
    * @brief 移除子节点之前通知
    * @param pNode 当前节点
    * @param pChild 移除的子节点
    */
    virtual void onNodeRemoveChildBefore(NodeSPtr pNode, NodeSPtr pChild)
    {
        if (_notify._NotifyNodeRemoveChildBefore)
        {
            _notify._NotifyNodeRemoveChildBefore(pNode.get(),pChild.get());
        }
    }
    /**
    * @brief 移除子节点之后通知
    * @param pNode 当前节点
    * @param pChild 移除的子节点
    */
    virtual void onNodeRemoveChildAfter(NodeSPtr pNode, NodeSPtr pChild)
    {
        if (_notify._NotifyNodeRemoveChildAfter)
        {
            _notify._NotifyNodeRemoveChildAfter(pNode.get(),pChild.get());
        }
    }
    /**
    * @brief 节点更新之前通知
    * @param pNode 当前更新的节点
    */
    virtual void onNodeUpdateBefore(NodeSPtr pNode)
    {
        if (_notify._NotifyNodeUpdateBefore)
        {
            _notify._NotifyNodeUpdateBefore(pNode.get());
        }
    }
    /**
    * @brief 节点更新之后通知
    * @param pNode 当前更新的节点
    */
    virtual void onNodeUpdateAfter(NodeSPtr pNode)
    {
        if (_notify._NotifyNodeUpdateAfter)
        {
            _notify._NotifyNodeUpdateAfter(pNode.get());
        }
    }

    /**
     * @brief 节点销毁之前通知
     * @param pNode 当前销毁的节点
    */
    virtual void onNodeDestroyBefore(NodeSPtr pNode) 
    {
        if (_notify._NotifyNodeDestroyBefore)
        {
            _notify._NotifyNodeDestroyBefore(pNode.get());
        }
    }
    /**
     * @brief 节点销毁之后通知
     *  此时节点已经调用到析构的最后一步，所以这里只能使用节点的裸指针，不建议再使用节点的数据或方法
     * @param pNode 当前销毁的节点
    */
    virtual void onNodeDestroyAfter(const WD::WDNode* pNode) 
    {
        if (_notify._NotifyNodeDestroyAfter)
        {
            _notify._NotifyNodeDestroyAfter((void*)pNode);
        }
    }
    /**
     * @brief 节点属性值改变通知
     * @param name 属性名称
     * @param currValue 当前值
     * @param prevValue 改变前的值
     * @param pNode 发送者
     */
    virtual void onNodeAttributeValueChanged(const std::string_view& name
        , const WD::WDBMAttrValue& currValue
        , const WD::WDBMAttrValue& prevValue
        , NodeSPtr pNode)
    {
        if (_notify._NotifyNodeAttributeValueChanged)
        {
            _notify._NotifyNodeAttributeValueChanged(name.data(),(void*)&currValue,(void*)&prevValue,pNode.get());
        }
    }
    /**
    * @brief 节点的子节点重新排序通知
    * @param pNode 发送者
    */
    virtual void onNodeChildrenReordered(NodeSPtr pNode)
    {
        if (_notify._NotifyNodeChildrenReordered)
        {
            _notify._NotifyNodeChildrenReordered(pNode.get());
        }
    }
};


class   CAPITreeObserver
{
public:
    NodeTreeNotify  _notify =   {};
public:
    // 模型树当前节点改变通知 响应
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender)
    {
        if (_notify._NoticeCurrentNodeChanged)
        {
            _notify._NoticeCurrentNodeChanged(pCurrNode.get(),pPrevNode.get(),&sender);
        }
    }

    void    onNoticeAddTopLevelNode(WD::WDNode::SharedPtr pNode, WD::WDNodeTree& sender)
    {
        if (_notify._NoticeAddTopLevelNode)
        {
            _notify._NoticeAddTopLevelNode(pNode.get(),&sender);
        }
    }

    void    onNoticeRemoveTopLevelNode(WD::WDNode::SharedPtr pNode, WD::WDNodeTree& sender)
    {
        if (_notify._NoticeRemoveTopLevelNode)
        {
            _notify._NoticeRemoveTopLevelNode(pNode.get(),&sender);
        }
    }
};

CORE_API LPWDNodeTree   wdGetTree(Core )
{
    return  &WD::Core().nodeTree();
}


CORE_API size_t     wdGetTreeNodeCount(LPWDNodeTree )
{
    return  WD::Core().nodeTree().topLevelNodeCount();
}

CORE_API LPWDNode   wdGetTreeNode(LPWDNodeTree ,size_t index)
{
    return  WD::Core().nodeTree().topLevelNode(index).get();
}

CORE_API size_t     wdGetNodeChildCount(LPWDNode object)
{
    WD::WDNode* pNode   =   (WD::WDNode* )object;
    if (pNode == nullptr)
        return  0;
    else
        return  pNode->childCount();
}

CORE_API LPWDNode   wdGetNodeChild(LPWDNode parent,size_t index)
{
    WD::WDNode* pNode   =   (WD::WDNode* )parent;
    if (pNode == nullptr)
        return  nullptr;
    else
        return  pNode->childAt(index).get();
}

/// <summary>
/// 获取节点名称字段的长度
/// </summary>
/// <param name="object"></param>
/// <returns></returns>
CORE_API size_t wdGetNodeNameSize(LPWDNode object)
{
    WD::WDObject*   pObj    =   (WD::WDObject* )object;
    WD::WDNode*     pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        auto    name    =   pNode->name();
        return  name.size();
    }
    else
    {
        return  0;
    } 
}


CORE_API LPCSTR wdGetNodeName(LPWDNode object,char* pBuf,size_t nBuf)
{
    auto    pObj    =   (WD::WDObject* )object;
    auto    pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        auto    name    =   pNode->name();
        strncpy(pBuf,name.c_str(),nBuf - 1);
        return  pBuf;
    }
    else
    {
        return  nullptr;
    } 
}

CORE_API LPCSTR         wdGetNodeType(LPWDNode object)
{
    auto    pObj    =   (WD::WDObject* )object;
    auto    pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        return  pNode->type().data();
    }
    else
    {
        return  "";
    } 
}

CORE_API bool   wdGetNodeWorldPosition(LPWDNode object, WDDouble3* result)
{
    auto    pObj    =   (WD::WDObject* )object;
    auto    pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        auto    data    =   pNode->globalTranslation();
        result->x       =   data.x;
        result->y       =   data.y;
        result->z       =   data.z;
        return  true;
    }
    else
    {
        return  false;
    } 
}
CORE_API bool   wdGetNodeWorldScale(LPWDNode object,    WDDouble3* result)
{
    auto    pObj    =   (WD::WDObject* )object;
    auto    pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        auto    data    =   pNode->globalScaling();
        result->x       =   data.x;
        result->y       =   data.y;
        result->z       =   data.z;
        return  true;
    }
    else
    {
        return  false;
    }
}
CORE_API bool   wdGetNodeWorldRotate(LPWDNode object,   WDQuat* result)
{
    auto    pObj    =   (WD::WDObject* )object;
    auto    pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        auto    data    =   pNode->globalRotation();
        result->x       =   data.x;
        result->y       =   data.y;
        result->z       =   data.z;
        result->w       =   data.w;
        return  true;
    }
    else
    {
        return  false;
    }
}
CORE_API bool   wdGetNodeWorldTransform(LPWDNode object,WDMat4* result)
{
    auto    pObj    =   (WD::WDObject* )object;
    auto    pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        auto    data    =   pNode->globalTransform();
        memcpy(result->data,data.data(),sizeof(data));
        return  true;
    }
    else
    {
        return  false;
    }
}


CORE_API LPNodeCallBack wdSetNodeCallback(LPWDNode object,NodeNotify* notify)
{
    auto    pObj    =   (WD::WDObject* )object;
    auto    pNode   =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {
        auto    callback    =   new CAPIWDNodeObserver();
        callback->_notify   =   *notify;
        pNode->observers().push_back(callback);
        return  callback;
    }
    return  nullptr;
}

CORE_API bool   wdRemoveNodeCallback(LPWDNode object,LPNodeCallBack cb)
{
    auto    pObj        =   (WD::WDObject* )object;
    auto    pNode       =   dynamic_cast<WD::WDNode*>(pObj);
    if (pNode)
    {   
        auto    pObs    =   (CAPIWDNodeObserver*)cb;
        pNode->observers().remove(pObs);
        delete  pObs;
        return  true;
    }
    return  false;
}


CORE_API LPTreeCallBack wdSetNodeTreeCallback(LPWDNodeTree object,NodeTreeNotify* notify)
{
    auto   pObj    =   (WD::WDObject* )object;
    auto   pTree   =   dynamic_cast<WD::WDNodeTree*>(pObj);
    if (pTree && notify)
    {
        auto    callback    =   new CAPITreeObserver();
        callback->_notify   =   *notify;

        pTree->noticeCurrentNodeChanged()   +=  {callback,&CAPITreeObserver::onNodeTreeCurrentNodeChanged};
        pTree->noticeAddTopLevelNode()      +=  {callback,&CAPITreeObserver::onNoticeAddTopLevelNode};
        pTree->noticeRemoveTopLevelNode()   +=  {callback,&CAPITreeObserver::onNoticeRemoveTopLevelNode};

        return  callback;
    }
    return  nullptr;
}

CORE_API bool          wdRemoveNodeTreeCallback(LPWDNodeTree object,LPTreeCallBack cb)
{
    auto   pObj    =   (WD::WDObject* )object;
    auto   pTree   =   dynamic_cast<WD::WDNodeTree*>(pObj);
    if (pTree)
    {
        auto    callback    =   (CAPITreeObserver*)(cb);

        pTree->noticeCurrentNodeChanged()   -=  {callback,&CAPITreeObserver::onNodeTreeCurrentNodeChanged};
        pTree->noticeAddTopLevelNode()      -=  {callback,&CAPITreeObserver::onNoticeAddTopLevelNode};
        pTree->noticeRemoveTopLevelNode()   -=  {callback,&CAPITreeObserver::onNoticeRemoveTopLevelNode};

        delete  callback;
        return  true;
    }
    return  nullptr;
}