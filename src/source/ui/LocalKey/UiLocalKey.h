#pragma     once
#include    <QWidget>
#include    <QPushButton>
#include    <QFileDialog>
#include    <QDebug>
#include    <QIcon>
#include    <QWidget>
#include    <QPainter>
#include    <QSpacerItem>
#include    <QByteArray>
#include    <QPaintEvent>
#include    "qrencode/qrencode.h"
#include    <QPushButton>
#include    <QGridLayout>
#include    <QMessageBox>
#include    <QFileDialog>
#include    <QLabel>
#include    <QtWebSockets/QWebSocket>
#include    "sdk.license.h"
#include    "licennse.ws.msg.h"
#include    <filesystem>

class   WebSocket :public QObject
{
public:
    using   RecvEvent   =    std::function<void(bool,const char*,const char*,size_t)>;
public:
    Q_OBJECT
public:
    std::string     _data;
    std::string     _addr;
    std::string     _path;
    bool            _conn   =   false;
    RecvEvent       _evtRecvLicense;
    int             _seed   =   0;
public:
    WebSocket(const std::string& lcData,const std::string& licPath,const std::string& addr = DEF_LICENSE_ADDR)
    {
        /// const char* p   =   "aaaAAA@AAzzzz";
        /// auto        sm  =   MsgId::CheckSum(p,strlen(p),0);
        _data       =   lcData;
        _addr       =   addr;
        _path       =   licPath;
        _websocket  =   new QWebSocket();
        QObject::connect(_websocket, &QWebSocket::textMessageReceived,  this,&WebSocket::onTextMessageReceived);
        // 连接信号槽，处理二进制消息
        QObject::connect(_websocket, &QWebSocket::binaryMessageReceived, this,&WebSocket::onBinaryMessageReceived);

        // 连接信号槽，处理connect
        QObject::connect(_websocket, &QWebSocket::connected,    this,   &WebSocket::onConnect);
        // 连接信号槽，处理disconnect
        QObject::connect(_websocket, &QWebSocket::disconnected, this,   &WebSocket::onDisconnect);

        /// 连接信号槽，处理错误信号
        /// QObject::connect(_websocket,  SIGNAL(error(QAbstractSocket::SocketError)), this,  SLOT(onError(QAbstractSocket::SocketError)));
    }
    void    connectTo(const char* addr = DEF_LICENSE_ADDR)
    {
        std::string     strAddr =   DEF_LICENSE_ADDR;
        if (addr == nullptr)
            strAddr   = _addr;
        else
            strAddr   = addr;
        if (!_conn)
        {
            QUrl serverAddress(strAddr.c_str());
            _websocket->abort();
            // 连接到服务器
            _websocket->open(serverAddress);
            printf("connect to %s\n",strAddr.c_str());
        }
        else
        {
            
            printf("sendHeartbeat \n");
            sendHeartbeat();
        }
    }

    qint64  sendText(const char* )
    {
        if (_websocket)
        {
            // 发送文本消息
            _websocket->sendTextMessage("Hello, Server!");
        }
    }
    /// <summary>
    /// 发送数据包，不关注打包过程,协议提前组装好
    /// </summary>
    /// <param name="data"></param>
    /// <param name="nlen"></param>
    /// <returns></returns>
    qint64  sendBinary(const void* data,size_t nlen)
    {
        // 发送二进制消息
        QByteArray binaryMessage;
        binaryMessage.resize(static_cast<int>(nlen));
        memcpy(binaryMessage.data(),data,nlen);
        return  _websocket->sendBinaryMessage(binaryMessage);
    }
    /// <summary>
    /// 发送数据包，不关注打包过程,协议提前组装好
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    qint64  sendBinary(QByteArray& data)
    {
        if (_websocket)
            return  _websocket->sendBinaryMessage(data);
        else
            return  0;
    }
    /// <summary>
    /// 发送心跳
    /// </summary>
    void    sendHeartbeat()
    { 
        MSGHeatbeat     msg;
        QByteArray      binary;
        /// checksum计算,使用种子
        msg.checkSum    =   _seed;
        /// 长度计算
        msg.msgLen      =   sizeof(msg);
        binary.resize(msg.msgLen);
        memcpy(binary.data(),   &msg,   sizeof(msg));
        sendBinary(binary);
    }
    /// <summary>
    /// 发情请求license数据
    /// 成功链接到服务器后调用一次
    /// </summary>
    void    sendMSGLicenseReq()
    {
        MSGLicenseReq   msg;
        QByteArray      binary;
        /// checksum计算
        msg.checkSum    =   MsgId::CheckSum(_data.data(),_data.size(),_seed);

        printf("checksum = %d  && _seed = %d \n",msg.checkSum,_seed);

        /// 长度计算
        msg.msgLen      =   (uint32_t)(_data.size()) + sizeof(msg);
        binary.resize(msg.msgLen);
        memcpy(binary.data(),               &msg,           sizeof(msg));
        memcpy(binary.data() + sizeof(msg), _data.data(),   _data.size());
        sendBinary(binary);
    }
protected:
    void    onConnect()
    {
        _conn   =   true;
        _seed   =   0;
        printf("connect ok \n");
    }
    void    onDisconnect()
    {
        printf("onDisconnect !\n");
        _conn   =   false;
    }
    void    onError(QAbstractSocket::SocketError )
    {
        _conn   =   false;
        _seed   =   0;
    }
    void    onTextMessageReceived(const QString &message)
    {
        printf("text message received : %s\n",message.toLocal8Bit().data());

    }
    void    onBinaryMessageReceived(const QByteArray &message)
    {
        MsgId*  pMsg    =   (MsgId*)message.data();

        if (static_cast<uint32_t>(message.size()) != pMsg->msgLen)
        {
            printf("error：msg length error\n");
            return;
        }
        if (!pMsg->isValidMagic(_seed))
        {
            printf("error：isValidMagic()\n");
            return;
        }
        switch (pMsg->msgId)
        {
            /// <summary>
            /// 心跳
            /// </summary>
        case MSG_Heatbeat:
            onHeatbeat(pMsg);
            printf("recv:MSG_Heatbeat\n");
            break;
            /// <summary>
            /// 服务器发给客户端的license key信息
            /// </summary>
        case MSG_LicenseRes:
            printf("recv:MSG_LicenseRes\n");
            onLicenseRes(pMsg);
            break;
            /// <summary>
            /// 返回当前用户绑定的license信息
            /// </summary>
        case MSG_LicenseInfRes:
            onLicenseInfRes(pMsg);
            break;
            /// <summary>
            /// 解除给定机器的授权结果
            /// </summary>
        case MSG_UnbindRes:
            onUnbindRes(pMsg);
            break;
            /// <summary>
            /// 销户结果
            /// </summary>
        case MSG_LogOffUserRes:
            onLogoffUserRes(pMsg);
            break;
        case MSG_CheckSumSeed:
            onCheckSumSeed(pMsg);
            break;
        default:
            break;
        }
    }
    
protected:
    void    onHeatbeat(const MsgId* )
    {}
    /// <summary>
    /// 接收license数据，并保存到给定的目录下
    /// </summary>
    /// <param name="msg"></param>
    void    onLicenseRes(const MsgId* msg)
    {
        MSGLicenseRes*  pMsg    =   (MSGLicenseRes*)msg;
        std::string     file    =   _path;

        printf(u8"MSG_LicenseRes:data：%s\n",(char*)pMsg->data());

        std::string     fileTmp =   _path + ".tmp";
        FILE*           pFile   =   fopen(fileTmp.c_str(),"wb");
        if (pFile == nullptr)
        {
            if(_evtRecvLicense)
                _evtRecvLicense(false,fileTmp.c_str(),(const char*)pMsg->data(),pMsg->dataLenth());
            assert(0!=0);
            return ;
        }
            
        fwrite(pMsg->data(),pMsg->dataLenth(),1,pFile);
        fclose(pFile);
        /// 新文件
        std::filesystem::remove(file);
        std::filesystem::rename(fileTmp,file);
        /// 通知写入文件成功，应用程可以退出了。
        if(_evtRecvLicense)
            _evtRecvLicense(true,file.c_str(),(const char*)pMsg->data(),pMsg->dataLenth());
    }

    void    onLicenseInfRes(const MsgId* /*msg*/)
    {
        //MSGLicenseInfRes*   pMsg    =   (MSGLicenseInfRes*)msg;
    }

    void    onUnbindRes(const MsgId* /*msg*/)
    {
        //MSGUnbindRes*       pMsg    =   (MSGUnbindRes*)msg;
    }

    void    onLogoffUserRes(const MsgId* /*msg*/)
    {
        //MSGLogOffUserRes*   pMsg    =   (MSGLogOffUserRes*)msg;
    }
    void    onCheckSumSeed(const MsgId* msg)
    {

        MSGCheckSumSeed*   pMsg    =   (MSGCheckSumSeed*)msg;
        _seed   =   pMsg->seed;


        printf("_seed = %d\n",_seed);

        /// 发送更新请求，有可能升级license时间()
        printf("sendMSGLicenseReq  \n");
        sendMSGLicenseReq();
    }
protected:
    QWebSocket* _websocket;
};

class   LocalKeyWidget : public QWidget
{
    Q_OBJECT
public:
    enum QR_MODE
    {
        MODE_NUL        =   QR_MODE_NUL,
        MODE_NUM        =   QR_MODE_NUM,
        MODE_AN         =   QR_MODE_AN,
        MODE_8          =   QR_MODE_8,
        MODE_KANJI      =   QR_MODE_KANJI,
        MODE_STRUCTURE  =   QR_MODE_STRUCTURE,
        MODE_ECI        =   QR_MODE_ECI,
        MODE_FNC1FIRST  =   QR_MODE_FNC1FIRST,
        MODE_FNC1SECOND =   QR_MODE_FNC1SECOND
    };

    enum QR_LEVEL 
    {
        LEVEL_L = QR_ECLEVEL_L,
        LEVEL_M = QR_ECLEVEL_M,
        LEVEL_Q = QR_ECLEVEL_Q,
        LEVEL_H = QR_ECLEVEL_H
    };
public:
    LocalKeyWidget(QString licensePath,QString product,QString version,QString httpSvr = DEF_LICENSE_HOST);

    ~LocalKeyWidget();
public:
    virtual void    setupUi();
    virtual void    updateData();
    virtual void    setMargin(const int &tMargin) 
    {
        this->margin = tMargin;
        this->repaint ();
    }
    virtual void    paintEvent (QPaintEvent *event) override;
public slots:
    void onClickedPushButton();
    void onTimer();
private:
    QString         _licensePath;
    QTimer*         _timer;
    WebSocket*      _webSocket;
    QString         _httpSvr ;
    QString         _product;
    QString         _version;
    QGridLayout*    _gridLayout;
    QWidget*        _widget;
    QPushButton*    _saveBtn;
    bool            casesen;
    int             margin;
    QIcon           icon;
    qreal           percent;
    QByteArray      text;
    QColor          foreground;
    QColor          background;
    QR_MODE         mode;
    QR_LEVEL        level;
};
