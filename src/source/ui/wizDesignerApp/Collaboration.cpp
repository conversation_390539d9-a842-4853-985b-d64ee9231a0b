#include "Collaboration.h"

#include <gflags/gflags.h>

#include "WDRapidxml.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/common/WDStringConvert.h"
#include "core/WDLicense.h"

#include "common/DesignGlog.h"
#include "service/DesignKafkaService.h"
#include "rpc/client/DesignServiceConnectionPool.h"
#include "rpc/client/DesignServiceClient.h"
#include "service/ConfigParser.h"
#include "rpc/minio/MinioTaskState.h"
#include "rpc/minio/MinioDownloader.h"
#include "store/db_store/DbStore.h"
#include "service/DesignTLVFile.h"
#include "service/DesignQueue2RpcService.h"
#include "serialize/WDNodeCache.h"
#include "core/WDBlockingTask.h"

static constexpr const char* XmlFileName = "config-DC.xml";
static constexpr const char* Cfg_Key_Db = "Cfg_Key_Db";
static constexpr const char* Cfg_Key_Grpc = "Cfg_Key_Grpc";
static constexpr const char* Cfg_Key_Active = "Cfg_Key_Active";
static constexpr const char* Cfg_Key_Immediately_Pull = "Cfg_Key_Immediately_Pull";

namespace NSNodeTreeRecord
{
    /**
     * @brief 父节点rid
     * @param record record对象
     */
    static int64_t Parent(const design::NodeTreeRecord& record)
    {
        return record.id();
    }

    /**
    * @brief 获取子节点列表中的子节点rid列表
    * @param vec 子节点列表
    */
    static std::vector<int64_t> Children(const design::NodeTreeRecord& record)
    {
        std::vector<int64_t> children;
        const auto& vec = record.children();

        for (size_t i = 0; i < vec.vec_int64_size(); ++i)
        {
            children.push_back(vec.vec_int64((int)i));
        }
        return children;
    }

    static void Log(const design::NodeTreeRecord& record)
    {
        std::string str = "TreeAction : ";
        str += std::to_string(record.id());
        str += "(";
        const auto& vec = record.children();
        for (size_t i = 0; i < vec.vec_int64_size(); ++i)
        {
            str += std::to_string(vec.vec_int64(int(i)));
            str += ", ";
        }
        str += ")";
        LOG(INFO) << str;
    }
};

/**
 * @brief 根据节点所属模块获取队列节点类型
 * @param node 节点
 * @return 队列节点类型
 */
static wiz::DesignNode2Queue::NodeType NodeBMType(WD::WDNode& node)
{
    if (node.getBMSub<WD::WDBMDesign>() != nullptr)
        return wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (node.getBMSub<WD::WDBMCatalog>() != nullptr)
        return wiz::DesignNode2Queue::NodeType::CATALOG;

    assert(false);
    // 理论上不存在这种情况，出现视为DESIGN
    return wiz::DesignNode2Queue::NodeType::DESIGN;
}

Collaboration::Collaboration(WD::WDCore& core)
    : _core(core)
{
    _pLoginServer = new LoginServer(core, *this);
    _pModelServer = new ModelServer(core, *this);

    // 默认不激活协同服务
    _isCollaboration = false;

    // 读取DC配置
    this->readConfig();

    // 初始化
    this->init();
}

Collaboration::~Collaboration()
{
    if (_pLoginServer != nullptr)
    {
        delete _pLoginServer;
        _pLoginServer = nullptr;
    }
    if (_pModelServer != nullptr)
    {
        delete _pModelServer;
        _pModelServer = nullptr;
    }
}

void Collaboration::init()
{
    // 初始化日志
    auto logPath = std::string(_core.exeDirPath()) + "/logs";
    wiz::initGoogleLogging("Collaboration", logPath, true, 7);

    // 初始化grpc服务
    auto& pool = wiz::DesignServiceConnectionPool::getInstance();
    pool.initialize(_cfgInfo.grpc);
}

static void ParseXMLDoc(WD::XMLDoc& doc, char* data)
{
    try
    {
        doc.parse<0>(data);
    }
    catch (const rapidxml::parse_error&)
    {
        assert(false);
    }
    catch (...)
    {
        assert(false);
    }
}

void Collaboration::readConfig()
{
    std::string filePath = std::string(_core.dataDirPath()) + std::string(XmlFileName);
    WD::WDFileReader file(filePath);
    if (file.isBad())
        return;
    file.readAll();
    if (file.length() == 0)
        return;

    WD::XMLDoc doc;
    ParseXMLDoc(doc, (char*)file.data());
    WD::XMLNode* pXmlNodeRoot = doc.first_node("Root");
    if (pXmlNodeRoot == nullptr)
        return;
    for (WD::XMLNode* pXmlNode = pXmlNodeRoot->first_node("item")
         ; pXmlNode
         ; pXmlNode = pXmlNode->next_sibling())
    {
        auto pKeyAttr = pXmlNode->first_attribute("key");
        if (pKeyAttr == nullptr)
            continue;
        std::string key = pKeyAttr->value();

        std::string value;
        auto pValueAttr = pXmlNode->first_attribute("value");
        if (pValueAttr != nullptr)
        {
            value = pValueAttr->value();
        }

        if (key == Cfg_Key_Db)
        {
            _cfgInfo.db = value;
        }
        else if (key == Cfg_Key_Grpc)
        {
            _cfgInfo.grpc = value;
        }
        else if (key == Cfg_Key_Active)
        {
            bool bOk = false;
            auto realValue = WD::FromString<bool>(value, &bOk);
            if (bOk)
            {
                _isCollaboration = realValue;
            }
        }
        else if (key == Cfg_Key_Immediately_Pull)
        {
            bool bOk = false;
            auto realValue = WD::FromString<bool>(value, &bOk);
            if (bOk)
            {
                _cfgInfo.immediately_Pull = realValue;
            }
        }
    }
}


LoginServer::LoginServer(WD::WDCore& core, Collaboration& collaboration)
    : _core(core)
      , _collaboration(collaboration)
      , _nodeCache(WD::WDNodeCacheByType::getInstance())
{
}

LoginServer::~LoginServer()
{
}

LoginServer::LoginUserConfig LoginServer::login(std::string_view userName, std::string_view password) const
{
    LoginUserConfig userConfig;
    // 客户端服务
    auto& service = wiz::DesignServiceClient::getInstance();

    design::UserInfo user;
    user.set_user(userName);
    user.set_pwd(password);

    // 先获取公钥
    auto userConfigStr = service.LoginChain(user);
    if (!userConfigStr)
    {
        LOG(INFO) << "登录失败";
        return userConfig;
    }
    LOG(INFO) << userConfigStr.value();

    // 登录成功缓存用户账号密码
    _collaboration.userInfo().id = userName;
    _collaboration.userInfo().password = password;

    // 解析登录用户配置
    userConfig = this->parseUserConfig(userConfigStr.value());

    return userConfig;
}

// 下载minio文件并导入本地store
void LoginServer::downloadFromMinioAndUpsetDB(wiz::ServiceConfig serviceConfig,
                                              WD::store::IStore& store) const
{
    auto progressCallback = [](const wiz::TaskProgress& progress)
    {
        static int i = 0;

        LOG_IF(
            INFO, i%10==0
            || progress.state == wiz::MinioTaskState::COMPLETED
            || progress.state == wiz::MinioTaskState::FAILED
        ) << "\rDownloading " << progress.objectName
                    << ": " << std::fixed << std::setprecision(2) << progress.getPercentage() << "% "
                    << "(" << progress.bytesDownloaded << "/" << progress.totalBytes << " bytes) "
                    << std::setprecision(2) << (progress.speed / 1024.0 / 1024.0) << " MB/s";
        ++i;
    };

    std::vector<std::tuple<std::string, std::string, std::string>> tasks;
    for (const auto& sn : serviceConfig.minio->nodeSnapshotBasePath)
    {
        tasks.push_back(std::make_tuple(
            serviceConfig.minio->bucketName,
            sn.fileName,
            "/tmp/" + sn.fileName
        ));
    }
    wiz::MinioDownloader downloader(
        serviceConfig.minio->endpoint,
        serviceConfig.minio->accessKey,
        serviceConfig.minio->secretKey,
        false,
        4
    );
    auto taskIds = downloader.addTasks(tasks, progressCallback);
    downloader.waitForCompletion(5000);
    // 导入本地
    for (const auto& t : tasks)
    {
        auto tvf = wiz::DesignTLVFile(std::get<2>(t));
        tvf.updateStore(store);
    }
}

// 生成硬件ID哈希
static std::string GenerateHardwareHash()
{
    return WD::WDLicense::DataMd5().toString();
}

bool LoginServer::openProject(const UserCfgProj& project, std::string_view userRole)
{
    WDUnused(userRole);

    // 启动项目成功则缓存项目信息
    _collaboration.projectInfo().code = project.code;
    _collaboration.projectInfo().name = project.name;
    _collaboration.projectInfo().id = project.id;

    auto& service = wiz::DesignServiceClient::getInstance();
    // 设置项目信息
    design::UserInfo user;
    user.set_user(_collaboration.userInfo().id);
    user.set_pwd(_collaboration.userInfo().password);
    // TODO: SERVER (目前没有关联角色)
    // user.set_role(_collaboration.userInfo().roleCode);
    design::ProjectInfo projectInfo;
    projectInfo.set_user(user.user());
    projectInfo.set_projectcode(project.code);

    // 获取服务配置信息
    auto configJson = service.getConfig(projectInfo);
    // LOG(INFO) << "Raw config from service: " << configJson;
    auto serviceConfig = wiz::ConfigParser::parseFromJson(configJson);
    auto mv = serviceConfig.minio->getLatestSnapshotTime();
    LOG(INFO) << "server config:" << serviceConfig.toJson() << "|last snapshot time:" << mv.first << " - " << mv.second
        << " \ndb path:" << this->projDbPath();
    auto fileMaxUpdateTime = mv.second;
    auto dbPrefix = std::to_string(serviceConfig.projectInfo->id);
    auto commonStore = std::make_shared<WD::store::CommonStore>(this->projDbPath());

    auto storeUPtr = std::make_unique<WD::store::DbStore>(this->projDbPath(), dbPrefix, 10); // 10个表，这里的参数不要改动
    // 将store注入queue，这样消费数据及时保存
    auto& queue = wiz::DesignNode2Queue::getInstance();
    // 初始化节点队列
    queue.initialize(projectInfo, commonStore, &_core.getBMDesign(), &_core.getBMCatalog(), 0);
    queue.setStore(std::move(storeUPtr));
    auto& store = queue.getStore();
    // 初始化节点缓存
    _nodeCache.initialize(&store, &queue.getSerializer(design::NodeType::DESIGN),
                          &queue.getSerializer(design::NodeType::CATALOG));
    // 监测队列，发往服务端
    auto& rpcService = wiz::DesignQueue2RpcService::getInstance();
    rpcService.initialize(projectInfo);
    rpcService.start();
    rpcService.self().detach();

    auto& kafkaService = wiz::DesignKafkaService::getInstance();
    const auto& kafkaConfig = serviceConfig.kafka.value();
    // todo 这里的group id 需要保持唯一
    auto hardwareHash = GenerateHardwareHash();
    const std::string groupId = "dc-" + projectInfo.projectcode() + user.user() + hardwareHash;
    kafkaService.initialize(kafkaConfig.bootstrapServers, kafkaConfig.topic, groupId, 0);

    // 对比本地时间与远程时间； // 模型类型可以只填DESIGN； CATALOG因长期不变 导致差异过大
    auto localMaxUpdateTime = store.fetchMaxUpdateTime(WD::store::NodeType::DESIGN);
    if (serviceConfig.minio.value().nodeSnapshotBasePath.empty())
    {
        // 没有文件，项目未实现初始化；直接消费kafka; 等待导入
        kafkaService.start();
    }
    else
    {
        if (fileMaxUpdateTime - localMaxUpdateTime > 2 * 24 * 60 * 60 * 1000)
        {
            LOG(INFO) << "begin downloading file:" << fileMaxUpdateTime << " - " << localMaxUpdateTime;
            // 时间差异过大，直接下载;

            // todo 对比 offset 差异是否过大
            downloadFromMinioAndUpsetDB(serviceConfig, store);
            localMaxUpdateTime = fileMaxUpdateTime; // 更新完之后时间就与远程文件一致
            // localMaxUpdateTime 可能不变，导致拉取; fileMaxUpdateTime 是镜像的生成时间
            // 构建一个虚拟节点0,将其中的时间设置为最后的时间fileMaxUpdateTime
            design::NodeAttrsRecord root;
            root.set_id(0);
            auto info = root.mutable_additionalinfo();
            info->set_time(fileMaxUpdateTime);
            store.upsetNodeAttrsRecord(root, "00000000-0000-0000-0000-000000000000", design::NodeType::DESIGN,
                                       WD::store::IStore::FromServer);
        }
        LOG(INFO) << "will start consumer from time:" << localMaxUpdateTime;
        auto partitionOffset = kafkaService.
            getOffsetByTimestamp(localMaxUpdateTime, {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11});
        // logging 用于定位问题
        for (auto& [topic, partition, offset] : partitionOffset)
        {
            LOG(INFO) << "start consumer from " << topic << " " << partition << " " << offset;
        }
        kafkaService.startFromTopicPartitions(partitionOffset);
    }

    return true;
}

static void RecursionCacheTopLevelNode(WD::WDBMBase& mgr, WD::WDNode::SharedPtr pParent, WD::WDNodeCache& cache)
{
    static std::set<int64_t> visited;
    if (pParent == nullptr)
        return;
    auto parRid = pParent->getRemoteId();
    const auto childrenRids = cache.getChildrenIdsFromStoreNoLock(parRid);
    if (childrenRids.empty())
        return;
    const auto children = cache.getNodes(childrenRids);
    for (const auto& child : children)
    {
        auto cid = child->getRemoteId();
        if (visited.find(cid) != visited.end())
        {
            LOG(WARNING) << "visited: " << cid << ", pid:" << parRid
                << ", children size: " << childrenRids.size() << "|" << children.size();
            continue;
        }
        visited.insert(cid);
        // 此时先不更新，后续统一更新
        mgr.setParent(child, pParent, false);
        RecursionCacheTopLevelNode(mgr, child, cache);
    }
}

static WD::WDNode::Nodes CacheAllNodes(WD::WDBMBase& mgr, WD::WDNodeCache& cache)
{
    WD::WDNode::Nodes result;
    const auto topLevelRids = cache.getChildrenIdsFromStoreNoLock(0);
    result = cache.getNodes(topLevelRids);

    for (const auto& pTopLevel : result)
    {
        RecursionCacheTopLevelNode(mgr, pTopLevel, cache);
    }

    return result;
}

void LoginServer::load() const
{
    // 本地数据库中的数据转化为节点预挂载在树上
    auto loginDesiNodes = CacheAllNodes(_core.getBMDesign(), _nodeCache.getCache(design::NodeType::DESIGN));
    auto loginCataNodes = CacheAllNodes(_core.getBMCatalog(), _nodeCache.getCache(design::NodeType::CATALOG));

    // 先清除根节点的所有子节点
    auto& cataMgr = _core.getBMCatalog();
    auto pCataRoot = cataMgr.root();
    if (pCataRoot != nullptr)
    {
        auto& children = pCataRoot->children();
        cataMgr.destroy(children, false);

        // 再将这些节点挂到根节点上
        for (const auto& pNode : loginCataNodes)
        {
            if (pNode == nullptr)
                continue;
            cataMgr.setParent(pNode, pCataRoot, false);
        }
        pCataRoot->update(true);
    }
    // 先清除根节点的所有子节点
    auto& desiMgr = _core.getBMDesign();
    auto pDesiRoot = desiMgr.root();
    if (pDesiRoot != nullptr)
    {
        auto& children = pDesiRoot->children();
        desiMgr.destroy(children, false);

        // 再将这些节点挂到根节点上
        for (const auto& pNode : loginDesiNodes)
        {
            if (pNode == nullptr)
                continue;
            desiMgr.setParent(pNode, pDesiRoot, false);
        }
        pDesiRoot->update(true);
    }
}

LoginServer::LoginUserConfig LoginServer::parseUserConfig(std::string_view str) const
{
    LoginUserConfig userConfig;

    WD::JsonDoc doc;
    doc.Parse((char*)str.data(), str.size());
    if (doc.HasParseError())
        return userConfig;
    if (!doc.IsObject())
        return userConfig;

    // TODO: SERVER 目前的数据结构不是最终形态

    // 获取角色列表
    std::vector<UserCfgRole> userRoles;
    WD::JsonValue objRoles = GetJsonValueSafely<WD::JsonValue>(doc, "role");
    if (objRoles.IsArray() && !objRoles.Empty())
    {
        for (auto& objRole : objRoles.GetArray())
        {
            auto& backEle = userRoles.emplace_back();
            backEle.name = GetJsonValueSafely<std::string>(objRole, "name");
            backEle.code = GetJsonValueSafely<std::string>(objRole, "code");
        }
    }
    // 获取项目列表
    WD::JsonValue objProjs = GetJsonValueSafely<WD::JsonValue>(doc, "projects");
    if (objProjs.IsArray() && !objProjs.Empty())
    {
        for (auto& objProj : objProjs.GetArray())
        {
            auto& backEle = userConfig.emplace_back();
            backEle.id = GetJsonValueSafely<uint64_t>(objProj, "id");
            backEle.name = GetJsonValueSafely<std::string>(objProj, "name");
            backEle.code = GetJsonValueSafely<std::string>(objProj, "code");
            backEle.roles = userRoles;
        }
    }

    return userConfig;
}

std::string LoginServer::projDbPath()
{
    auto ret = _core.dataDirPath() + _collaboration.cfgInfo().db + _collaboration.projectInfo().code;
    return ret;
}


/**
 * @brief 将节点保存到本地db
 * @param queue 节点队列
 * @param pNodes 节点列表
 * @return 是否保存成功
 *  true 如果所有节点都保存成功
 *  false 如果有任何节点保存失败
 */
static bool UpsetNodeAttrs(wiz::DesignNode2Queue& queue, const WD::WDNode::Nodes& pNodes)
{
    auto& store = queue.getStore();
    for (const auto& pNode : pNodes)
    {
        if (pNode == nullptr)
            continue;
        auto nodeType = NodeBMType(*pNode);

        const auto& serializer = queue.getSerializer(nodeType);
        design::NodeAttrsRecord record;
        if (serializer.serializeNode(pNode, record))
            return false;

        store.upsetNodeAttrsRecord(record, pNode->uuid().toString(), nodeType, WD::store::IStore::FromLocal);
    }

    return true;
}

/**
* @brief 将节点关系保存到本地db
* @param queue 节点队列
* @param nodeType 队列节点类型
* @param treeAction 节点树数据
* @return 是否保存成功
*/
static bool UpsetNodeTree(wiz::DesignNode2Queue& queue
                          , const wiz::DesignNode2Queue::NodeType& nodeType
                          , const design::NodeTreeAction& treeAction)
{
    auto& store = queue.getStore();
    design::NodeTreeRecord record;
    WDUnused(treeAction);
    // TODO: NEWDC treeAction数据转换为record
    store.upsetNodeTreeRecord(record, nodeType, WD::store::IStore::FromLocal);

    return true;
}


static const size_t ChunkSize = 10000;
/**
* @brief 批量处理元素（支持数据结构vector）
* @tparam Element 元素类型
* @param elments 全部元素
* @param func 处理过程
* @return 是否处理成功
*/
template <typename Element>
static bool BatchExec(const std::vector<Element>& elments, std::function<bool(const std::vector<Element>&)> func)
{
    // 分批上传
    for (size_t i = 0; i < elments.size(); i += ChunkSize)
    {
        // 计算当前 chunk 的结束位置
        size_t end = i + ChunkSize;
        // 越界则到数组末尾为止
        if (end > elments.size())
            end = elments.size();

        // 创建一个新的 vector 来存储当前 chunk
        std::vector<Element> chunk(elments.begin() + i, elments.begin() + end);

        // 有任何一次提交失败就认为失败
        if (!func(chunk))
            return false;
    }
    return true;
}

ModelServer::ModelServer(WD::WDCore& core, Collaboration& collaboration)
    : _core(core)
      , _collaboration(collaboration)
{
}

ModelServer::~ModelServer()
{
}

bool ModelServer::projectInitialed() const
{
    // TODO: NEWDC
    return false;
}

bool ModelServer::push(WD::WDBMBase& mgr, const MSActions& msActions, bool toServer) const
{
    WDUnused(toServer);
    auto& queue = wiz::DesignNode2Queue::getInstance();
    wiz::DesignNode2Queue::NodeType nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    if (mgr.name() == "Design")
        nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (mgr.name() == "Catalog")
        nodeType = wiz::DesignNode2Queue::NodeType::CATALOG;

    // 要推送的节点
    std::map<WD::WDUuid, WD::WDNode::SharedPtr> mapPushNode;
    for (const auto& [pWCloselyNodes, action] : msActions)
    {
        if (pWCloselyNodes.empty())
            continue;
        switch (action)
        {
        case NTA_Add:
            {
                // 节点数据
                for (const auto& pWCloselyNode : pWCloselyNodes)
                {
                    auto pNode = pWCloselyNode.lock();
                    if (pNode == nullptr)
                        continue;

                    mapPushNode[pNode->uuid()] = pNode;
                }
            }
            break;
        case NTA_Modify:
            {
                // 节点数据
                for (const auto& pWCloselyNode : pWCloselyNodes)
                {
                    auto pNode = pWCloselyNode.lock();
                    if (pNode == nullptr)
                        continue;

                    mapPushNode[pNode->uuid()] = pNode;
                }
            }
            break;
        case NTA_Remove:
            {
            }
            break;
        default:
            break;
        }
    }

    // 先推送节点，以生成remoteId
    WD::WDNode::Nodes pushNodes;
    for (const auto& [uuid, pNode] : mapPushNode)
    {
        pushNodes.push_back(pNode);
    }
    if (!BatchExec<WD::WDNode::SharedPtr>(pushNodes, [&queue, nodeType](const WD::WDNode::Nodes& nodes)-> bool
    {
        return queue.pushNodes(nodes, nodeType);
    }))
        return false;

    // 要推送的关系
    std::vector<design::NodeTreeAction> pushActions;
    for (const auto& [pWCloselyNodes, action] : msActions)
    {
        if (pWCloselyNodes.empty())
            continue;
        switch (action)
        {
        case NTA_Add:
            {
                // 节点关系
                design::NodeTreeAction treeAction;
                int64_t parentRid = 0;
                int64_t leftRid = 0;
                auto pFront = pWCloselyNodes.front().lock();
                if (pFront != nullptr)
                {
                    // 父id
                    auto pParent = pFront->parent();
                    if (pParent != nullptr)
                        parentRid = pParent->getRemoteId();

                    // left id
                    const auto& pLeft = pFront->prevBrother();
                    if (pLeft != nullptr)
                        leftRid = pLeft->getRemoteId();
                }
                treeAction.set_parentid(parentRid);
                treeAction.set_leftsiblingid(leftRid);
                for (const auto& pWNode : pWCloselyNodes)
                {
                    auto pNode = pWNode.lock();
                    if (pNode == nullptr)
                        continue;

                    treeAction.add_siblings(pNode->getRemoteId());
                }
                treeAction.set_flag(design::TreeActionFlag::TAF_INSERT);
                pushActions.push_back(treeAction);
            }
            break;
        case NTA_Modify:
            {
            }
            break;
        case NTA_Remove:
            {
                // 节点关系
                design::NodeTreeAction treeAction;
                int64_t parentRid = 0;
                auto pFront = pWCloselyNodes.front().lock();
                if (pFront != nullptr)
                {
                    // 父id
                    auto pParent = pFront->parent();
                    if (pParent != nullptr)
                        parentRid = pParent->getRemoteId();
                }
                treeAction.set_parentid(parentRid);
                for (const auto& pWNode : pWCloselyNodes)
                {
                    auto pNode = pWNode.lock();
                    if (pNode == nullptr)
                        continue;

                    treeAction.add_siblings(pNode->getRemoteId());
                }
                treeAction.set_flag(design::TreeActionFlag::TAF_DELETE);
                pushActions.push_back(treeAction);
            }
            break;
        default:
            break;
        }
    }

    // 推送节点关系
    if (!BatchExec<design::NodeTreeAction>(pushActions,
                                           [&queue, nodeType](const std::vector<design::NodeTreeAction>& trees)-> bool
                                           {
                                               return queue.pushNodeTreeActions(trees, nodeType);
                                           }))
        return false;

    // 推送完成后取消节点的F_Created和F_EdittedStatus标记
    for (const auto& pNode : pushNodes)
    {
        if (pNode == nullptr)
            continue;

        pNode->setFlags(pNode->flags().removeFlag(WD::WDNode::Flag::F_Created));
        pNode->setFlags(pNode->flags().removeFlag(WD::WDNode::Flag::F_EdittedStatus));
    }

    return true;
}

bool ModelServer::push(bool toServer) const
{
    LOG(INFO) << "commit begin";
    WDUnused(toServer);
    auto pBM = _core.currentBM();
    if (pBM == nullptr)
        return false;
    auto& queue = wiz::DesignNode2Queue::getInstance();
    auto pRoot = pBM->root();
    if (pRoot == nullptr)
        return false;
    const auto nodeType = NodeBMType(*pRoot);
    // 获取阻塞任务对象
    auto pBlockTask = _core.blockingTask();
    if (pBlockTask == nullptr)
        return false;

    // 要推送的节点
    WD::WDNode::Nodes pushNodes;
    this->getPushNodesRecursion(pRoot, pushNodes);
    LOG(INFO) << "pushNodes begin（count:" << pushNodes.size() << "）";
    double progress = 0.0;
    auto progFactor = ChunkSize / pushNodes.size();
    pBlockTask->setProgressText(WD::WDTs("ModelServerApi", "commit"), progress, 0);
    // 先推送节点，以生成remoteId
    if (!BatchExec<WD::WDNode::SharedPtr>(pushNodes, [&queue, nodeType, pBlockTask, &progress, progFactor](const WD::WDNode::Nodes& nodes)-> bool
    {
        auto bOk = queue.pushNodes(nodes, nodeType);
        progress += 0.9 * progFactor;
        pBlockTask->setProgress(progress, 0);
        return bOk;
    }))
        return false;
    LOG(INFO) << "pushNodes end";
    // 要推送的关系
    std::vector<design::NodeTreeAction> pushActions;
    this->getPushTreeActionsRecursion(pRoot, pushActions);
    // 暂时从申领管理中获取删除的节点
    auto& pClaimMgr = pBM->claimMgr();
    for (const auto& CBDeleteNode : pClaimMgr.deletedNodes())
    {
        const auto& pNode = CBDeleteNode.second.pNode;
        if (pNode == nullptr)
            continue;
        design::NodeTreeAction treeAction;
        auto pParent = pNode->parent();
        if (pParent == nullptr)
            continue;
        treeAction.set_parentid(pParent->getRemoteId());
        treeAction.add_siblings(pNode->getRemoteId());
        treeAction.set_flag(design::TreeActionFlag::TAF_DELETE);
        pushActions.push_back(treeAction);
    }
    pBlockTask->setProgress(0.9, 0);
    LOG(INFO) << "pushTrees begin（count:" << pushActions.size() << "）";
    // 推送节点关系
    if (!BatchExec<design::NodeTreeAction>(pushActions,
                                           [&queue, nodeType](const std::vector<design::NodeTreeAction>& trees)-> bool
                                           {
                                               return queue.pushNodeTreeActions(trees, nodeType);
                                           }))
        return false;
    LOG(INFO) << "pushTrees end";

    // 推送完成后取消节点的F_Created和F_EdittedStatus标记
    for (const auto& pNode : pushNodes)
    {
        if (pNode == nullptr)
            continue;

        pNode->setFlags(pNode->flags().removeFlag(WD::WDNode::Flag::F_Created));
        pNode->setFlags(pNode->flags().removeFlag(WD::WDNode::Flag::F_EdittedStatus));
    }
    // 清空申领管理中的删除节点列表
    pClaimMgr.deletedNodes().clear();

    // TODO: 移动的节点暂时未处理（应该分离成新增和删除）

    LOG(INFO) << "commit end";
    pBlockTask->setProgress(1.0, 0);
    return true;
}

bool ModelServer::pull()
{
    auto& queue = wiz::DesignNode2Queue::getInstance();
    bool needUpdate = false;

    // 获取批量消息
    std::vector<wiz::KafkaMessageWrapperPtr> messages;
    auto& kafkaService = wiz::DesignKafkaService::getInstance();
    kafkaService.popMessages(messages, 100); // 一次最多获取100条消息
    for (auto& m : messages)
    {
        auto tlv = m->tlv;
        if (tlv.getVersion() == 0)
        {
            auto vs = tlv.parseV0(m->message.data(), m->message.size());
            for (const auto& v : vs)
            {
                if (TLVHandler(v.first, v.second, tlv.getType(), tlv.getNodeType()))
                    needUpdate = true;
            }
        }
        else
        {
            if (TLVHandler(tlv.getData(), tlv.getLength(), tlv.getType(), tlv.getNodeType()))
                needUpdate = true;
        }
    }
    // todo 这里的返回值做什么用？
    return needUpdate;
}

void ModelServer::updateLocalCahche()
{
    std::lock_guard<std::mutex> lock(_mutex);
    auto pBM = _core.currentBM();
    if (pBM == nullptr)
        return;
    auto pRoot = pBM->root();
    if (pRoot == nullptr)
        return;

    // 节点对应协同端id
    std::map<int64_t, WD::WDNode::SharedPtr> allNodes;
    WD::WDNode::RecursionHelpter(*pRoot, [&allNodes](WD::WDNode& node)
    {
        allNodes[node.getRemoteId()] = WD::WDNode::ToShared(&node);
    });
    // 更新到缓存
    // 此时的消息已由服务器保证顺序，保持数据最终一致性
    for (auto relationItr = _relations.begin(); relationItr != _relations.end();)
    {
        const auto& [parentRid, chidrenRids] = *relationItr;
        // 父节点
        WD::WDNode::SharedPtr pParent = nullptr;
        if (auto it = allNodes.find(parentRid); it != allNodes.end())
            pParent = it->second;
        if (pParent == nullptr)
        {
            relationItr = _relations.erase(relationItr);
            continue;
        }
        auto pCurBM = pParent->getBMBase();
        if (pCurBM == nullptr)
        {
            relationItr = _relations.erase(relationItr);
            continue;
        }

        // 收集父节点于当前场景树上的子节点列表(pair-second是用来标识有没有在新的子节点列表中找到，便于删除节点)
        std::map<uint64_t, std::pair<WD::WDNode::SharedPtr, bool>> oldChilren;
        for (const auto& pChild : pParent->children())
        {
            if (pChild == nullptr)
                continue;
            oldChilren[pChild->getRemoteId()] = std::make_pair(pChild, false);
        }

        // 为了使goto使用编译通过，所以在使用之前进行初始化
        WD::WDNode::SharedPtr pNext = nullptr;

        // 优先处理增删，以便接下来设置正确的节点顺序
        for (const auto& newChild : chidrenRids)
        {
            // 在当前子节点列表中没有找到，即为新增的节点
            if (auto it = oldChilren.find(newChild); it == oldChilren.end())
            {
                // 在缓存的更新节点中查询新增节点
                auto addNodeItr = _updateNode.find(newChild);
                if (addNodeItr != _updateNode.end())
                {
                    // 先将新增节点挂载父节点下，此时不排序
                    pCurBM->setParent(addNodeItr->second, pParent);
                    addNodeItr->second->update();

                    allNodes[addNodeItr->first] = addNodeItr->second;

                    // 当前缓存的更新节点已处理，从缓存中移除
                    _updateNode.erase(addNodeItr);
                }
                else
                {
                    // 没有在缓存中找到新增节点数据，直接继续下一次循环
                    goto ContinueThisTime;
                }
            }
            // 若找到了则标识，且更新节点数据
            else
            {
                it->second.second = true;

                // 找到则更新节点数据
                auto updateNodeItr = _updateNode.find(newChild);
                if (updateNodeItr != _updateNode.end())
                {
                    // 更新节点
                    it->second.first->copy(updateNodeItr->second.get());
                    it->second.first->update();

                    // 当前缓存的更新节点已处理，从缓存中移除
                    _updateNode.erase(updateNodeItr);
                }
                else
                {
                    // 没有在缓存中找到更新节点数据，直接继续下一次循环
                    //goto ContinueThisTime;
                }
            }
        }
        // 遍历当前场景中的子节点列表，标识为未找到的即是需要删除的节点
        for (const auto& oldChild : oldChilren)
        {
            if (!oldChild.second.second)
            {
                pCurBM->destroy(oldChild.second.first);
                allNodes.erase(oldChild.first);
            }
        }

        // 此处为降低复杂度，蛮力设置所有子节点的关系。TODO: 提供效率更高的排序算法
        for (auto itr = chidrenRids.rbegin(); itr != chidrenRids.rend(); ++itr)
        {
            WD::WDNode::SharedPtr pNode = nullptr;
            if (auto it = allNodes.find(*itr); it != allNodes.end())
            {
                pNode = it->second;
            }
            else
            {
                // 没有在缓存中找到节点数据，直接继续下一次循环
                goto ContinueThisTime;
            }

            pCurBM->setParent(pNode, pParent, pNext);

            pNext = pNode;
        }
        // 更新父节点
        pParent->triggerUpdate();

        relationItr = _relations.erase(relationItr);
        continue;

        // 直接继续下一次循环
    ContinueThisTime :
        relationItr++;
    }

    // 如果缓存的更新节点还有，则直接更新节点
    for (auto itr = _updateNode.begin(); itr != _updateNode.end();)
    {
        if (auto it = allNodes.find(itr->first); it != allNodes.end())
        {
            const auto& pNode = it->second;
            if (pNode != nullptr)
            {
                // 更新节点
                pNode->copy(itr->second.get());
                pNode->update();

                // 当前缓存的更新节点已处理，从缓存中移除
                itr = _updateNode.erase(itr);
                continue;
            }
        }
        itr++;
    }

    _core.needRepaint();
}

bool ModelServer::clear(std::string_view moduleName)
{
    if (moduleName.empty())
    {
        auto pBM = _core.currentBM();
        if (pBM != nullptr)
            moduleName = pBM->name();
    }
    wiz::DesignNode2Queue::NodeType nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    if (moduleName == "Design")
        nodeType = wiz::DesignNode2Queue::NodeType::DESIGN;
    else if (moduleName == "Catalog")
        nodeType = wiz::DesignNode2Queue::NodeType::CATALOG;

    auto& queue = wiz::DesignNode2Queue::getInstance();

    // 组织节点关系
    design::NodeTreeAction treeAction;
    treeAction.set_parentid(0);
    treeAction.set_flag(design::TreeActionFlag::TAF_UPDATE_ALL);
    return queue.pushNodeTreeAction(treeAction, nodeType);
}

void ModelServer::initClaimCallback()
{
    auto pBM = _core.currentBM();
    if (pBM != nullptr)
    {
        auto& claimMgrV1 = pBM->claimMgr();
        claimMgrV1.funcCheckInBefore() = std::bind(&ModelServer::checkInBefore, this, std::placeholders::_1,
                                                   std::placeholders::_2);
        claimMgrV1.funcCheckInAttrBefore() = std::bind(&ModelServer::checkInAttrBefore, this, std::placeholders::_1,
                                                       std::placeholders::_2, std::placeholders::_3);
        claimMgrV1.funcCheckOutBefore() = std::bind(&ModelServer::checkOutBefore, this, std::placeholders::_1,
                                                    std::placeholders::_2, std::placeholders::_3);
    }
}

void ModelServer::getPushNodesRecursion(WD::WDNode::SharedPtr pNode, WD::WDNode::Nodes& result) const
{
    if (pNode == nullptr)
        return;

    if (pNode->flags().hasFlag(WD::WDNode::Flag::F_Created))
    {
        WD::WDNode::RecursionHelpter(*pNode, [&result](WD::WDNode& node)
        {
            result.push_back(WD::WDNode::ToShared(&node));
        });
        return;
    }
    if (pNode->flags().hasFlag(WD::WDNode::Flag::F_EdittedStatus))
    {
        result.push_back(pNode);
    }

    // 递归子节点
    const auto& pChildren = pNode->children();
    for (const auto& pChild : pChildren)
    {
        getPushNodesRecursion(pChild, result);
    }
}

void ModelServer::getPushTreeActionsRecursion(WD::WDNode::SharedPtr pNode,
                                              std::vector<design::NodeTreeAction>& result) const
{
    if (pNode == nullptr)
        return;

    if (pNode->flags().hasFlag(WD::WDNode::Flag::F_Created))
    {
        // 添加新增pNode的节点关系
        {
            // 节点关系
            design::NodeTreeAction treeAction;
            int64_t parentRid = 0;
            int64_t leftRid = 0;
            // 父id
            auto pParent = pNode->parent();
            if (pParent != nullptr)
                parentRid = pParent->getRemoteId();
            // left id
            const auto& pLeft = pNode->prevBrother();
            if (pLeft != nullptr)
                leftRid = pLeft->getRemoteId();
            treeAction.set_parentid(parentRid);
            treeAction.set_leftsiblingid(leftRid);
            treeAction.add_siblings(pNode->getRemoteId());
            treeAction.set_flag(design::TreeActionFlag::TAF_INSERT);
            result.push_back(treeAction);
        }
        // 递归添加新增的节点关系
        {
            WD::WDNode::RecursionHelpter(*pNode, [&result](WD::WDNode& node)
            {
                const auto& pChildren = node.children();
                if (pChildren.empty())
                    return;
                // 节点关系
                design::NodeTreeAction treeAction;
                int64_t parentRid = node.getRemoteId();
                int64_t leftRid = 0;
                treeAction.set_parentid(parentRid);
                treeAction.set_leftsiblingid(leftRid);
                for (const auto& pChild : pChildren)
                {
                    if (pChild == nullptr)
                        continue;
                    treeAction.add_siblings(pChild->getRemoteId());
                }
                treeAction.set_flag(design::TreeActionFlag::TAF_INSERT);
                result.push_back(treeAction);
            });
        }
        return;
    }

    // 递归子节点
    const auto& pChildren = pNode->children();
    for (const auto& pChild : pChildren)
    {
        getPushTreeActionsRecursion(pChild, result);
    }
}

bool ModelServer::TLVHandler(const void* data, int size, int msgType, design::NodeType nodeType)
{
    std::lock_guard<std::mutex> lock(_mutex);
    bool needUpdate = false;
    auto & queue = wiz::DesignNode2Queue::getInstance();
    if (msgType == 1)
    {
        // 节点数据
        design::NodeAttrsRecord record;
        auto ok = record.ParseFromArray(data, size);
        if (!ok) return false;
        // 反序列化成 WDNode
        auto pNode = WD::WDNode::MakeShared();
        queue.deserializeNode(pNode, nodeType, record);
        auto uuid = pNode->uuid().toString();
        LOG(INFO) << pNode->name() << "|" << uuid << "|traceId: " << record.traceid();

        // 保存数据库
        queue.getStore().upsetNodeAttrsRecord(record, uuid, nodeType, WD::store::IStore::StoreFrom::FromServer);

        // 根据userId区分是否需要同时更新到场景
        const auto& userId = record.additionalinfo().user();
        if (userId != _collaboration.userInfo().id)
        {
            _updateNode[pNode->getRemoteId()] = pNode;
            needUpdate = true;
        }
    }
    else
    {
        // 节点关系
        design::NodeTreeRecord treeRecord;
        auto ok = treeRecord.ParseFromArray(data, size);
        if (!ok) return false;

        NSNodeTreeRecord::Log(treeRecord);

        // 保存数据库
        queue.getStore().upsetNodeTreeRecord(treeRecord, nodeType, WD::store::IStore::StoreFrom::FromServer);

        // 根据userId区分是否需要同时更新到场景
        const auto& userId = treeRecord.additionalinfo().user();
        if (userId != _collaboration.userInfo().id)
        {
            auto pRid = NSNodeTreeRecord::Parent(treeRecord);
            _relations[pRid] = NSNodeTreeRecord::Children(treeRecord);
            needUpdate = true;
        }
    }

    return needUpdate;
}

std::vector<size_t> ModelServer::checkInBefore(const WD::WDBMClaimMgr::CheckItems& items
                                               , bool bShowMessage)
{
    WDUnused(bShowMessage);
    std::vector<size_t> result;
    size_t index = 0;
    for (index; index < items.size(); ++index)
        result.push_back(index);
    return result;
    // TODO: NEWDC
}

std::vector<size_t> ModelServer::checkInAttrBefore(const WD::WDBMClaimMgr::AttrCheckItems& items
                                                   , bool bShowMessage
                                                   , bool& bCancelModify)
{
    WDUnused2(bShowMessage, bCancelModify);
    std::vector<size_t> result;
    size_t index = 0;
    for (index; index < items.size(); ++index)
        result.push_back(index);
    return result;
    // TODO: NEWDC
}

std::vector<size_t> ModelServer::checkOutBefore(const WD::WDBMClaimMgr::CheckItems& items
                                                , bool bShowMessage
                                                , bool& bCancelCheckOut)
{
    WDUnused2(bShowMessage, bCancelCheckOut);
    std::vector<size_t> result;
    size_t index = 0;
    for (index; index < items.size(); ++index)
        result.push_back(index);
    return result;
    // TODO: NEWDC
}
