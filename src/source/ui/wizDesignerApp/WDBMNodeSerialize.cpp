#include    "WDBMNodeSerialize.h"
#include    "core/businessModule/WDBMRefCollector.h"
#include    "core/WDCore.h"
#include    "core/businessModule/WDBMBase.h"
#include    "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include    "core/common/WDStringConvert.h"

WD_NAMESPACE_BEGIN

WDBMNodeSerialize::WDBMNodeSerialize(WDBMBase& bmBase)
    : _bmBase(bmBase)
    , _attrSerial(_bmBase)
{

}
WDBMNodeSerialize::~WDBMNodeSerialize()
{

}

void WDBMNodeSerialize::saveToJson(const Nodes& nodes
    , JsonDoc& jsonDoc
    , JsonValue& object
    , BASFlags flags) const
{
    if (!object.IsArray())
    {
        assert(false && "Object must is Array!");
        return ;
    }
    // 递归写入节点以及其子节点的所有属性
    this->jsonWriteRecursion(jsonDoc, object, nodes, flags);
}
WDBMNodeSerialize::Nodes WDBMNodeSerialize::loadFromJson(JsonDoc& jsonDoc
    , const JsonValue& object
    , BASFlags flags,FuncProgress funcProgress) const
{
    // 递归读取节点以及其子节点的所有属性
    return this->jsonReadRecursion(jsonDoc, object, flags, funcProgress);
}

void WDBMNodeSerialize::jsonWriteRecursion(JsonDoc& jsonDoc
    , JsonValue& jsonParentArray
    , const Nodes& nodes
    , BASFlags flags) const
{
    for (auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        // 检测类型是否具有自动生成标志，如果有该标志，则不做保存
        auto pDesc = pNode->getTypeDesc();
        if (pDesc != nullptr && pDesc->flags().hasFlag(WDBMTypeDesc::F_AutoGeneration))
            continue;

        auto& a = jsonDoc.GetAllocator();
        // 准备一个Object类型的json对象
        jsonParentArray.PushBack(rapidjson::kObjectType, a);
        JsonValue& obj = jsonParentArray[jsonParentArray.Size() - 1];
        // 写入node的属性
        FormatJsonWriter writer(jsonDoc, obj);
        _attrSerial.writeFromNode(*pNode, writer, flags);
        // 写入node子节点
        auto& chidren = pNode->children();
        if (!chidren.empty())
        {
            this->jsonWriteRecursionP(jsonDoc, obj, chidren, flags);
        }
    }
}
void WDBMNodeSerialize::jsonWriteRecursionP(JsonDoc& jsonDoc
    , JsonValue& jsonParentObj
    , const Nodes& nodes
    , BASFlags flags) const
{
    if (nodes.empty())
        return;
    
    auto& a = jsonDoc.GetAllocator();
    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(static_cast<rapidjson::SizeType>(nodes.size()), a);

    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        // 检测类型是否具有自动生成标志，如果有该标志，则不做保存
        auto pDesc = pNode->getTypeDesc();
        if (pDesc != nullptr && pDesc->flags().hasFlag(WDBMTypeDesc::F_AutoGeneration))
            continue;

        arr.PushBack(rapidjson::kObjectType, a);
        JsonValue& obj = arr[arr.Size() - 1];
        FormatJsonWriter writer(jsonDoc, obj);
        _attrSerial.writeFromNode(*pNode, writer, flags);
        // 递归写入子节点属性
        this->jsonWriteRecursionP(jsonDoc, obj, pNode->children(), flags);
    }

    if (!arr.Empty())
        jsonParentObj.AddMember(JsonValue("children", a), arr, a);
}
WDBMNodeSerialize::Nodes WDBMNodeSerialize::jsonReadRecursion(JsonDoc& jsonDoc
    , const JsonValue& jsonParentObj
    , BASFlags flags, FuncProgress funcProgress) const
{
    WDBMNodeSerialize::Nodes rNodes;

    if (!jsonParentObj.IsArray())
        return rNodes;

    const auto& arr = jsonParentObj.GetArray();
    rNodes.reserve(arr.Size());
    //暂时报进度的的起始值写死的，如果出现进度条显示一半回退的情况，再处理
    double start = 0.1;
    double stop  = 0.6;
    double step  = (stop- start) / arr.Size();
    for (const auto& arrV: arr)
    {
        // 报告进度
        if (funcProgress)
            funcProgress(start, WD::WDTs("mainFunc", "parse the data file of the module").c_str());
        start += step;

        if (!arrV.IsObject())
            continue;
        // 创建并读取节点属性
        WDNode::SharedPtr pNode = WDNode::MakeShared();

        FormatJsonReader reader(arrV);
        _attrSerial.readToNode(*pNode, reader, flags);
        if (arrV.HasMember("children"))
        {
            // 递归读取子节点以及其属性
            auto children = this->jsonReadRecursion(jsonDoc, arrV["children"], flags);
            // 设置子节点列表
            for (auto pChild : children)
            {
                if (pChild == nullptr)
                    continue;
                pNode->addChild(pChild);
            }
        }
        rNodes.push_back(pNode);
    }
    return rNodes;
}

/**
 * @brief 大小端转换
*/
static std::string ReverseEndian(const std::string& str)
{
    std::string result = str;

    // 确保字符串长度为偶数
    if (result.size() % 2 != 0)
        return result;

    size_t  size    =   result.size();
    // 指针遍历字符串，以两个字符为一组进行逆序
    char*   start   =   &result[0];
    char*   end     =   start + size - 2;

    char    temp;
    // 交换每对字符直到中间
    while (start < end)
    {
        // 交换两个字符
        temp        =   start[0];
        start[0]    =   end[0];
        end[0]      =   temp;

        temp        =   start[1];
        start[1]    =   end[1];
        end[1]      =   temp;

        // 移动指针到下一对字符
        start       +=  2;
        end         -=  2;
    }

    return result;
}
template <typename T>
bool JsonNumbValCastGet(const JsonValue& value, T& out)
{
    //if (!value.IsNumber())
    //    return false;

    if (value.IsInt())
    {
        out = static_cast<T>(value.GetInt());
        return true;
    }
    else if (value.IsUint())
    {
        out = static_cast<T>(value.GetUint());
        return true;
    }
    else if (value.IsInt64())
    {
        out = static_cast<T>(value.GetInt64());
        return true;
    }
    else if (value.IsUint64())
    {
        out = static_cast<T>(value.GetUint64());
        return true;
    }
    else if (value.IsDouble())
    {
        out = static_cast<T>(value.GetDouble());
        return true;
    }
    else if (value.IsString())
    {
        if constexpr (std::is_same<T, double>())
        {
            std::string tValue = value.GetString();
            if (tValue.substr(0, 2) == "0x")
            {
                auto lsStr = ReverseEndian(tValue.erase(0, 2));
                out = FromHexString<T>("0x" + lsStr);
            }
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        assert(false);
        return false;
    }
}

FormatJsonReader::FormatJsonReader(const JsonValue& object)
    : _object(object)
{

}
bool FormatJsonReader::readBool(const char* name, bool& value)
{
    if (!_object.HasMember(name))
        return false;
    const auto& member = _object[name];
    if (!member.IsBool())
        return false;
    value = member.GetBool();
    return true;
}
bool FormatJsonReader::readInt(const char* name, int& value)
{
    if (!_object.HasMember(name))
        return false;
    const auto& member = _object[name];
    return JsonNumbValCastGet(member, value);
}
bool FormatJsonReader::readInt64(const char* name, int64_t& value)
{
    if (!_object.HasMember(name))
        return false;
    const auto& member = _object[name];
    return JsonNumbValCastGet(member, value);
}
bool FormatJsonReader::readUInt(const char* name, unsigned int& value)
{
    if (!_object.HasMember(name))
        return false;
    const auto& member = _object[name];
    return JsonNumbValCastGet(member, value);
}
bool FormatJsonReader::readDouble(const char* name, double& value)
{
    if (!_object.HasMember(name))
        return false;
    const auto& member = _object[name];
    return JsonNumbValCastGet(member, value);
}
bool FormatJsonReader::readString(const char* name, std::string& value)
{
    if (!_object.HasMember(name))
    {
        value = std::string();
        return true;
    }
    const auto& member = _object[name];
    if (!member.IsString())
        return false;
    value = member.GetString();
    return true;
}
bool FormatJsonReader::readWord(const char* name, WDBMWord& value)
{
    if (!_object.HasMember(name))
    {
        value = WDBMWord();
        return true;
    }
    const auto& member = _object[name];
    if (!member.IsString())
        return false;
    value = member.GetString();
    return true;
}
bool FormatJsonReader::readVec2(const char* name, DVec2& value)
{
    if (!_object.HasMember(name))
        return false;
    if (!_object[name].IsArray())
        return false;
    const auto& arr = _object[name].GetArray();
    if (arr.Size() != 2)
        return false;
    if (!JsonNumbValCastGet(arr[0], value.x))
        return false;
    if (!JsonNumbValCastGet(arr[1], value.y))
        return false;
    return true;
}
bool FormatJsonReader::readVec3(const char* name, DVec3& value)
{
    if (!_object.HasMember(name))
        return false;
    if (!_object[name].IsArray())
        return false;
    const auto& arr = _object[name].GetArray();
    if (arr.Size() != 3)
        return false;
    if (!JsonNumbValCastGet(arr[0], value.x))
        return false;
    if (!JsonNumbValCastGet(arr[1], value.y))
        return false;
    if (!JsonNumbValCastGet(arr[2], value.z))
        return false;
    return true;
}
bool FormatJsonReader::readQuat(const char* name, DQuat& value)
{
    if (!_object.HasMember(name))
        return false;
    if (!_object[name].IsArray())
        return false;
    const auto& arr = _object[name].GetArray();
    if (arr.Size() != 4)
        return false;
    if (!JsonNumbValCastGet(arr[0], value.w))
        return false;
    if (!JsonNumbValCastGet(arr[1], value.x))
        return false;
    if (!JsonNumbValCastGet(arr[2], value.y))
        return false;
    if (!JsonNumbValCastGet(arr[3], value.z))
        return false;
    return true;
}
bool FormatJsonReader::readColor(const char* name, Color& value)
{
    if (!_object.HasMember(name))
        return false;
    if (!_object[name].IsArray())
        return false;
    const auto& arr = _object[name].GetArray();
    if (arr.Size() != 4)
        return false;
    if (!JsonNumbValCastGet(arr[0], value.r))
        return false;
    if (!JsonNumbValCastGet(arr[1], value.g))
        return false;
    if (!JsonNumbValCastGet(arr[2], value.b))
        return false;
    if (!JsonNumbValCastGet(arr[3], value.a))
        return false;
    return true;
}
bool FormatJsonReader::readUuid(const char* name, WDUuid& value)
{
    if (!_object.HasMember(name))
    {
        value = WDUuid::Null();
        return true;
    }
    const auto& member = _object[name];
    if (!member.IsString())
        return false;
    std::string strId = member.GetString();
    value = WDUuid::FromString(strId);
    return true;
}
bool FormatJsonReader::readLevelRange(const char* name, WDBMLevelRange& value)
{
    if (!_object.HasMember(name))
        return false;
    if (!_object[name].IsArray())
        return false;
    const auto& arr = _object[name].GetArray();
    if (arr.Size() != 2)
        return false;
    IVec2 minMax;
    if (!JsonNumbValCastGet(arr[0], minMax.x))
        return false;
    if (!JsonNumbValCastGet(arr[1], minMax.y))
        return false;
    value.setMinMax(minMax);
    return true;
}
bool FormatJsonReader::readNodeRef(const char* name, WDBMNodeRef& value)
{
    if (!_object.HasMember(name))
    {
        value = WDBMNodeRef();
        return true;
    }
    const auto& member = _object[name];
    if (!member.IsString())
        return false;
    std::string strId = member.GetString();
    WDUuid id = WDUuid::FromString(strId);
    value.setRefNodeId(id);
    return true;
}
bool FormatJsonReader::readIntVector(const char* name, std::vector<int>& value)
{
    if (!_object.HasMember(name))
    {
        value = std::vector<int>();
        return true;
    }
    if (!_object[name].IsArray())
        return false;

    const auto& arr = _object[name].GetArray();
    value.clear();
    value.reserve(arr.Size());
    int tmpV = 0;
    for (const auto& arrV : arr)
    {
        if (!JsonNumbValCastGet(arrV, tmpV))
            continue;
        value.push_back(tmpV);
    }
    return true;
}
bool FormatJsonReader::readDoubleVector(const char* name, std::vector<double>& value)
{
    if (!_object.HasMember(name))
    {
        value = std::vector<double>();
        return true;
    }
    if (!_object[name].IsArray())
        return false;

    const auto& arr = _object[name].GetArray();
    value.clear();
    value.reserve(arr.Size());
    double tmpV = 0;
    for (const auto& arrV : arr)
    {
        if (!JsonNumbValCastGet(arrV, tmpV))
            continue;
        value.push_back(tmpV);
    }
    return true;
}
bool FormatJsonReader::readStringVector(const char* name, std::vector<std::string>& value)
{
    if (!_object.HasMember(name))
    {
        value = std::vector<std::string>();
        return true;
    }
    if (!_object[name].IsArray())
        return false;
    const auto& arr = _object[name].GetArray();
    value.clear();
    value.reserve(arr.Size());
    for (const auto& arrV : arr)
    {
        if (!arrV.IsString())
            continue;
        value.push_back(arrV.GetString());
    }
    return true;
}
bool FormatJsonReader::readNodeRefVector(const char* name, WDBMNodeRefs& value)
{
    if (!_object.HasMember(name))
    {
        value = WDBMNodeRefs();
        return true;
    }
    if (!_object[name].IsArray())
        return false;
    const auto& arr = _object[name].GetArray();
    value.clear();
    value.reserve(arr.Size());
    for (const auto& arrV : arr)
    {
        if (!arrV.IsString())
            continue;
        WDUuid id = WDUuid::FromString(arrV.GetString());
        if (id.isNull())
            continue;
        value.push_back(WDBMNodeRef());
        value.back().setRefNodeId(id);
    }
    return true;
}
bool FormatJsonReader::readGeometryRef(const char* name, WDBMGeometryRef& value)
{
    if (!_object.HasMember(name))
    {
        value = WDBMGeometryRef();
        return true;
    }
    const auto& member = _object[name];
    if (!member.IsString())
        return false;
    std::string strId = member.GetString();
    WDUuid id = WDUuid::FromString(strId);
    value.setRefGeoId(id);
    return true;
}

FormatJsonWriter::FormatJsonWriter(JsonDoc& doc, JsonValue& object)
    : _doc(doc), _object(object)
{
}
bool FormatJsonWriter::writeBool(const char* name, const bool& value)
{
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value), a);
    return true;
}
bool FormatJsonWriter::writeInt(const char* name, const int& value)
{
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value), a);
    return true;
}
bool FormatJsonWriter::writeInt64(const char* name, const int64_t& value)
{
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value), a);
    return true;
}
bool FormatJsonWriter::writeUInt(const char* name, const unsigned int& value)
{
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value), a);
    return true;
}
bool FormatJsonWriter::writeDouble(const char* name, const double& value)
{
    if (IsNan(value) || IsInf(value))
    {
        assert(false);
        return false;
    }
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(ToHexString(value).c_str(), _doc.GetAllocator()), a);
    return true;
}
bool FormatJsonWriter::writeString(const char* name, const std::string_view& value)
{
    // 如果字符串是空，则不用写入
    if (value.empty())
        return true;
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value.data(), a), a);
    return true;
}
bool FormatJsonWriter::writeWord(const char* name, const WDBMWord& value)
{
    // 如果字符串是空，则不用写入
    if (value.empty())
        return true;
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;
    _object.AddMember(tName, JsonValue(value.c_str(), a), a);
    return true;
}
bool FormatJsonWriter::writeVec2(const char* name, const DVec2& value)
{
    if (IsNan(value.x) || IsInf(value.x)
        || IsNan(value.y) || IsInf(value.y))
    {
        assert(false);
        return false;
    }

    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(2, a);
    arr.PushBack(JsonValue(ToHexString(value.x).c_str(), a), a);
    arr.PushBack(JsonValue(ToHexString(value.y).c_str(), a), a);
    _object.AddMember(tName, arr, a);
    return true;
}
bool FormatJsonWriter::writeVec3(const char* name, const DVec3& value)
{
    if (IsNan(value.x) || IsInf(value.x)
        || IsNan(value.y) || IsInf(value.y)
        || IsNan(value.z) || IsInf(value.z))
    {
        assert(false);
        return false;
    }
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;
    
    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(3, a);
    arr.PushBack(JsonValue(ToHexString(value.x).c_str(), a), a);
    arr.PushBack(JsonValue(ToHexString(value.y).c_str(), a), a);
    arr.PushBack(JsonValue(ToHexString(value.z).c_str(), a), a);
    _object.AddMember(tName, arr, a);
    return true;
}
bool FormatJsonWriter::writeQuat(const char* name, const DQuat& value)
{
    if (IsNan(value.w) || IsInf(value.w)
        || IsNan(value.x) || IsInf(value.x)
        || IsNan(value.y) || IsInf(value.y)
        || IsNan(value.z) || IsInf(value.z))
    {
        assert(false);
        return false;
    }
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;
    
    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(4, a);
    arr.PushBack(JsonValue(ToHexString(value.w).c_str(), a), a);
    arr.PushBack(JsonValue(ToHexString(value.x).c_str(), a), a);
    arr.PushBack(JsonValue(ToHexString(value.y).c_str(), a), a);
    arr.PushBack(JsonValue(ToHexString(value.z).c_str(), a), a);
    _object.AddMember(tName, arr, a);
    return true;
}
bool FormatJsonWriter::writeColor(const char* name, const Color& value)
{
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(4, a);
    arr.PushBack(static_cast<int>(value.r), a);
    arr.PushBack(static_cast<int>(value.g), a);
    arr.PushBack(static_cast<int>(value.b), a);
    arr.PushBack(static_cast<int>(value.a), a);
    _object.AddMember(tName, arr, a);

    return true;
}
bool FormatJsonWriter::writeUuid(const char* name, const WDUuid& value)
{
    // 如果引用节点的Id是空，则不用写入
    if (value.isNull())
        return true;
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value.toString().c_str(), a), a);

    return true;
}
bool FormatJsonWriter::writeLevelRange(const char* name, const WDBMLevelRange& value)
{
    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(2, a);
    arr.PushBack(value.minMax().x, a);
    arr.PushBack(value.minMax().y, a);
    _object.AddMember(tName, arr, a);

    return true;
}
bool FormatJsonWriter::writeNodeRef(const char* name, const WDBMNodeRef& value)
{
    // 如果引用对象的Guid是空，则不用写入
    if (value.refNodeId().isNull())
        return true;

    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value.refNodeId().toString().c_str(), a), a);

    char tmpName[1024] = { 0 };
    sprintf_s(tmpName, sizeof(tmpName), "%s_refName", name);
    JsonValue tmpRefName = JsonValue(tmpName, a);
    
    std::string nodeName = "";
    if (value.refNode() != nullptr)
        nodeName = value.refNode()->name();
    _object.AddMember(tmpRefName, JsonValue(nodeName.c_str(), a), a);

    return true;
}
bool FormatJsonWriter::writeIntVector(const char* name, const std::vector<int>& value)
{
    // 如果列表是空，则不用写入
    if (value.empty())
        return true;

    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;
    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(static_cast<rapidjson::SizeType>(value.size()), a);
    for (const auto& v : value)
    {
        arr.PushBack(v, a);
    }
    _object.AddMember(tName, arr, a);

    return true;
}
bool FormatJsonWriter::writeDoubleVector(const char* name, const std::vector<double>& value)
{
    // 如果列表是空，则不用写入
    if (value.empty())
        return true;

    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;
    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(static_cast<rapidjson::SizeType>(value.size()), a);
    for (const auto& v : value)
    {
        if (IsNan(v) || IsInf(v))
        {
            assert(false);
            return false;
        }
        arr.PushBack(JsonValue(ToHexString(v).c_str(), a), a);
    }
    _object.AddMember(tName, arr, a);

    return true;
}
bool FormatJsonWriter::writeStringVector(const char* name, const std::vector<std::string>& value)
{
    // 如果列表是空，则不用写入
    if (value.empty())
        return true;

    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;
    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(static_cast<rapidjson::SizeType>(value.size()), a);
    for (const auto& v : value)
    {
        arr.PushBack(JsonValue(v.c_str(), a), a);
    }
    _object.AddMember(tName, arr, a);

    return true;
}
bool FormatJsonWriter::writeNodeRefVector(const char* name, const WDBMNodeRefs& value)
{
    // 如果列表是空，则不用写入
    if (value.empty())
        return true;

    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    JsonValue arr(rapidjson::kArrayType);
    arr.Reserve(static_cast<rapidjson::SizeType>(value.size()), a);

    JsonValue nameArr(rapidjson::kArrayType);
    nameArr.Reserve(static_cast<rapidjson::SizeType>(value.size()), a);

    for (const auto& v : value)
    {
        arr.PushBack(JsonValue(v.refNodeId().toString().c_str(), a), a);

        std::string nodeName = "";
        if (v.refNode() != nullptr)
            nodeName = v.refNode()->name();
        nameArr.PushBack(JsonValue(nodeName.c_str(), a), a);
    }
    _object.AddMember(tName, arr, a);

    char tmpName[1024] = { 0 };
    sprintf_s(tmpName, sizeof(tmpName), "%s_refName", name);
    JsonValue tmpRefName = JsonValue(tmpName, a);
    _object.AddMember(tmpRefName, nameArr, a);

    return true;
}
bool FormatJsonWriter::writeGeometryRef(const char* name, const WDBMGeometryRef& value)
{
    // 如果引用的几何体对象ID是空，则不用写入
    if (value.refGeoId().isNull())
        return true;

    auto& a = _doc.GetAllocator();
    JsonValue tName = JsonValue(name, a);
    if (_object.HasMember(tName))
        return false;

    _object.AddMember(tName, JsonValue(value.refGeoId().toString().c_str(), a), a);

    return true;
}

WD_NAMESPACE_END


