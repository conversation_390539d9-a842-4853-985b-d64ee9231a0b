#include    "wizDesignerMainWindow.h"
#include    <QtWidgets/QApplication>
#include    <QString>
#include	<QDir>
#include	<QFileInfo>
#include    <QTranslator>
#include	<QPropertyAnimation>
#include    "core/WDTranslate.h"
#include    "LicenseCheckDialog.h"
#include    "XMLDataLoader.h"
#include    "core/viewer/WDViewer.h"
#include    "core/log/WDLoggerPort.h"
#include    "core/common/WDMD5.h"
#include	"core/common/WDConfig.h"
#include    <filesystem>
#include    "core/businessModule/catalog/WDBMCatalog.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "core/businessModule/admin/WDBMAdmin.h"
#include	"core/businessModule/WDBMAuditObjectMgr.h"
#include	"core/businessModule/WDBMPermissionMgr.h"
#include	"Collaboration.h"
#include	"preWidget/WizSplashScreen.h"
#include	"core/message/WDMessage.h"
#include	"core/businessModule/WDBMClaimMgr.h"
#include	<QMessageBox>
#include	<QtXml>
#include	<QDomDocument>
#include	"ProjectDataLoadThread.h"

WD_NAMESPACE_USE

int main(int argc, char* argv[])
{
	// 这里的目的是为了能够在生成exe时，自动将QtXml相关的动态库引入
	{
		QDomDocument qXmlDoc;
		WDUnused(qXmlDoc);
	}

	QCoreApplication::setAttribute(Qt::AA_UseDesktopOpenGL, true);
	QCoreApplication::setAttribute(Qt::AA_ShareOpenGLContexts, true);
	QCoreApplication::setAttribute(Qt::AA_DontCreateNativeWidgetSiblings, true);

	qputenv("QTWEBENGINE_REMOTE_DEBUGGING", "9223");

	/****** 创建 QApp对象 *******/
	QApplication a(argc, argv);

	/****** 打开日志文件，用于记录程序运行日志  *******/

	// 判断当前程序文件夹是否具有写权限
	QString exePath = a.applicationDirPath();
	QFileInfo info(exePath);
	if (!info.isWritable())
	{
		QMessageBox::warning(nullptr,"Error", "failed to run WIZ Designer 2024 3DDS, please run the progranm as an administrator");
		return 0;
	}

	// 创建此次运行的日志文件
	{
		// 判断是否存在log目录
		QString logPath = exePath + QString("/log/");
		QDir dir;
		if (!dir.exists(logPath))
		{
			// 创建log目录
			auto mkdirFlag = dir.mkdir(logPath);
			if (!mkdirFlag)
			{
				assert(false && "创建日志log文件夹失败!");
			}
		}

		// 使用当前时间作为此次日志文件的名称
		QDateTime dt;
		QString tmStr = dt.currentDateTime().toString("yyyyMMdd_hh_mm_ss_zzz");
		QString fileName = QString("%1/%2.log").arg(logPath).arg(tmStr);
		if (!WD::WDLogger::getInstance()->open(fileName.toLocal8Bit().data()))
		{
			assert(false && "打开日志文件夹失败!");
		}
		char buf[1024] = { 0 };
		// 日志打印产品名称
		sprintf_s(buf, sizeof(buf), "产品名称: %s %d", WIZDesigner_NAME
			, WIZDesigner_YEAR_VERSION);
		LOG_INFO << buf;
		// 日志打印版本号
		sprintf_s(buf, sizeof(buf), "版本号：%d.%d.%s"
			, WIZDesigner_MAJOR_VERSION
			, WIZDesigner_MINOR_VERSION
			, WIZDesigner_PATCH_VERSION);
		LOG_INFO << buf;
	}

	/****** 初始化Ribbon风格  *******/
	RibbonStyle* ribbonStyle = new RibbonStyle;
	ribbonStyle->setTheme(OfficeStyle::Office2016White);
	a.setStyle(ribbonStyle);
	// 这里先不指定程序名称，防止标题栏出现英文,等程序名称想好了再加
	a.setApplicationName(QString::fromLocal8Bit("WIZ Designer 2024 3DDS"));
	a.setOrganizationName(QString::fromLocal8Bit("WIZ Designer 2024 3DDS"));
	a.setWindowIcon(QIcon(":/wizDesignerApp/logo"));

	/****** 加载界面翻译以及切换语言  *******/
	// 翻译QT控件
	QTranslator translator(0);
	/*bool flag = */translator.load(a.applicationDirPath() + QString("/../data/translate/zh_cn/qt_zh_CN.qm"));
	a.installTranslator(&translator);
	// 翻译文件目录
	QString translatePath = a.applicationDirPath() + QString("/../data/translate");
	// 加载翻译文件,这里应该扫描目录，但是现在暂时写死使用一个文件
	WD::StringVector tsFiles;

	QString tsZhCnColorTable = translatePath + QString("/zh_cn/color_table_zh_cn.xml");
	tsFiles.push_back(tsZhCnColorTable.toLocal8Bit().data());
	QString tsZhCnQtUi = translatePath + QString("/zh_cn/q_ui_zh_cn.xml");
	tsFiles.push_back(tsZhCnQtUi.toLocal8Bit().data());
	QString tsZhCnMainW = translatePath + QString("/zh_cn/MainWindow/zh_cn.xml");
	tsFiles.push_back(tsZhCnMainW.toLocal8Bit().data());

	/****** 创建WCore对象  *******/
	WD::WDCore coreApp(a.applicationDirPath().toLocal8Bit().data());

	// 设置启动配置的默认数据
	auto& cfgItemStartup = coreApp.cfg().get<std::string>("startup");
	// 是否全屏
	bool isFullScreen = true;
	cfgItemStartup.get<bool>("isFullScreen", isFullScreen)
		.bindValuePtr(&isFullScreen);
	int defaultWeight = 1920;
	cfgItemStartup.get<int>("defaultWeight", defaultWeight)
		.bindValuePtr(&defaultWeight);
	int defaultHeight = 1080;
	cfgItemStartup.get<int>("defaultHeight", defaultHeight)
		.bindValuePtr(&defaultHeight);
	bool titlebar = true;
	cfgItemStartup.get<bool>("titlebar", titlebar)
		.bindValuePtr(&titlebar);
	bool isResize = false;
	cfgItemStartup.get<bool>("isResize", isResize)
		.bindValuePtr(&isResize);

	/****** 初始化元件模块管理对象以及设计模块管理对象  *******/
	//初始化元件模块管理
	if (!coreApp.getBMCatalog().init())
	{
		LogWarn("初始化元件模块管理失败!");
		assert(false && "元件模块管理初始化失败！！！");
	}
	else
	{
		QString catalogZhCnConfig = translatePath + QString("/zh_cn/Catalog/Ui/Config/zh_cn.xml");
		tsFiles.push_back(catalogZhCnConfig.toLocal8Bit().data());
		QString catalogZhCnMenuBar = translatePath + QString("/zh_cn/Catalog/Ui/MenuBar/zh_cn.xml");
		tsFiles.push_back(catalogZhCnMenuBar.toLocal8Bit().data());
		LOG_INFO << "初始化元件模块管理成功!";
	}
	//初始化设计模块管理
	if (!coreApp.getBMDesign().init())
	{
		LogWarn("初始化设计模块管理失败!");
		assert(false && "设计模块管理初始化失败！！！");
	}
	else
	{
		QString tsZhCnConfig = translatePath + QString("/zh_cn/Design/Ui/Config/zh_cn.xml");
		tsFiles.push_back(tsZhCnConfig.toLocal8Bit().data());
		QString designZhCnMenuBar = translatePath + QString("/zh_cn/Design/Ui/MenuBar/zh_cn.xml");
		tsFiles.push_back(designZhCnMenuBar.toLocal8Bit().data());
		LOG_INFO << "初始化设计模块管理成功!";
	}
	//初始化管理模块管理
	if (!coreApp.getBMAdmin().init())
	{
		LogWarn("初始化管理模块管理失败!");
		assert(false && "管理模块管理初始化失败！！！");
	}
	else
	{
		QString adminZhCnConfig = translatePath + QString("/zh_cn/Admin/Ui/Config/zh_cn.xml");
		tsFiles.push_back(adminZhCnConfig.toLocal8Bit().data());
		QString adminZhCnMenuBar = translatePath + QString("/zh_cn/Admin/Ui/MenuBar/zh_cn.xml");
		tsFiles.push_back(adminZhCnMenuBar.toLocal8Bit().data());
		LOG_INFO << "初始化管理模块管理成功!";
	}
	// 加载中文翻译
	WD::WDTranslate::Instance()->load(tsFiles);
	LOG_INFO << "加载翻译成功";

	//切换语言
	if (!WD::WDTranslate::Instance()->languages().empty())
	{
		WD::WDTranslate::Instance()->setCurrentLanguage(WD::WDTranslate::Instance()->languages().front());
	}

	/****** 授权校验  *******/
	if (!CheckLicense())
		return 0;

	// 启动页
	WizSplashScreen wizSplashScreen(isFullScreen, defaultWeight, defaultHeight, isResize, titlebar);
	wizSplashScreen.lanchSplah();

	/******* 网络以及服务相关接口 *************/
	Collaboration collaboration(coreApp);

	/****** 验证登录  *******/
	if (collaboration.actived())
	{
		// 启用设计模块权限
		//coreApp.getBMDesign().permissionMgr().setEnabled(true);
		// 启用管理模块权限
		//coreApp.getBMAdmin().permissionMgr().setEnabled(true);
		// 启用设计模块权限
		coreApp.getBMDesign().claimMgr().setEnabled(true);
		// 启用管理模块权限
		coreApp.getBMAdmin().claimMgr().setEnabled(true);
		// 启用设计模块校审对象模式
		coreApp.getBMDesign().auditObjectMgr().setEnabled(true);
	loginLoop:
		// 用户登陆页
		wizSplashScreen.loginUser(coreApp, collaboration);
		// 检测退出标志
		if (wizSplashScreen.isExit())
			return -1;

		// 选择项目界面
		int result = wizSplashScreen.selectProject(coreApp, collaboration);
		// 检测退出标志
		if (wizSplashScreen.isExit())
			return -1;
		//回退至登录
		if(result == 1)
			goto loginLoop;
	}
	else
	{
		// 选择项目界面
		wizSplashScreen.selectProject(coreApp, collaboration);
		// 检测退出标志
		if (wizSplashScreen.isExit())
			return -1;
	}

	LOG_INFO << "验证通过,登录成功";
	LOG_INFO << "项目名：" << QString::fromLocal8Bit(collaboration.projectInfo().name.c_str()).toUtf8().data();

	// 加载项目界面
	wizSplashScreen.dataLoad();

	/****** 创建主窗口  *******/
	wizDesignerMainWindow* pMainWindow = new wizDesignerMainWindow(coreApp, wizSplashScreen, collaboration, nullptr);

	pMainWindow->setWindowIcon(QIcon(":/wizDesignerApp/logo"));
	const auto& userName = collaboration.userInfo().name;
	const auto& userRole = collaboration.userInfo().roleCode;
	const auto& userDepa = collaboration.projectInfo().name;
	{
		QString suffix;
		if (!userName.empty())
		{
			suffix += "-" + QString::fromLocal8Bit(userName.data());
		}
		if (!userRole.empty())
		{
			suffix += "-" + QString::fromUtf8(userRole.c_str());
		}
		QString title = QString("[") + QString::fromLocal8Bit(userDepa.data()) + suffix + "]";
		pMainWindow->setWindowTitle(pMainWindow->windowTitle() + title);
	}
	wizSplashScreen.setProgressVisuabel(true);
	

	/****** 根据选择的项目数据加载本地数据文件, 这里用一个线程异步加载 *******/
	// 加载项目,注意,模块根节点在此处创建,完成项目加载后可以通过类型管理创建节点
	QEventLoop prjLoadEvtLoop;
	ProjectDataLoadThread prjLoadThread(prjLoadEvtLoop, coreApp, collaboration, wizSplashScreen);
	prjLoadThread.start();
	prjLoadEvtLoop.exec();

	// 检测退出标志
	if (wizSplashScreen.isExit())
		return -1;

	// 初始化登陆时权限uuid
	//collaboration.modelServer().initLoginGovern();
	// 如果是协同模式，则用协同登录获取到的节点来替换（后续该流程直接在打开项目是完成，无需此步骤）
	if (collaboration.actived())
	{
		collaboration.loginServer().load();
	}

	wizSplashScreen.setProgressVisuabel(false);

	auto& nodeTree = coreApp.nodeTree();
	/****** 根据模块来将根节点管理到模型树上  *******/
	wizSplashScreen.showMsg(QString::fromUtf8(WD::WDTs("mainFunc", "initialize the nodeTre").c_str()));
	if (pMainWindow->moduleType() == "Design")
	{
		nodeTree.addTopLevelNode(coreApp.getBMDesign().root());
	}
	else if (pMainWindow->moduleType() == "Admin")
	{
		nodeTree.addTopLevelNode(coreApp.getBMAdmin().root());
	}
	else if (pMainWindow->moduleType() == "Catalog")
	{
		//添加元件模块根节点到模型树
		nodeTree.addTopLevelNode(coreApp.getBMCatalog().root());
	}

	/****** 根据模块区分菜单和ui组件配置文件，来初始化主界面  *******/
	wizSplashScreen.showMsg(QString::fromUtf8(WD::WDTs("mainFunc", "load the component and initialize the interface").c_str()));
	if (pMainWindow->moduleType() == "Design")
	{
		QString cfgPath = a.applicationDirPath() + "/" + "../data/ribbon/cfg.design.xml";
		pMainWindow->init(cfgPath);
	}
	else if (pMainWindow->moduleType() == "Admin")
	{
		QString cfgPath = a.applicationDirPath() + "/" + "../data/ribbon/cfg.admin.xml";
		pMainWindow->init(cfgPath);
	}
	else if (pMainWindow->moduleType() == "Catalog")
	{
		QString cfgPath = a.applicationDirPath() + "/" + "../data/ribbon/cfg.catalog.xml";
		pMainWindow->init(cfgPath);
	}
	// 检测退出标志
	if (wizSplashScreen.isExit())
		return -1;

	wizSplashScreen.showMsg(QString::fromUtf8(WD::WDTs("mainFunc", "wait for the interface to start").c_str()));
	pMainWindow->showMaximized();
	wizSplashScreen.finish(pMainWindow);
	// 发送主窗口show结束的通知
	pMainWindow->sendShowFinishedNotice();

	int r = a.exec();

	/****** 清空场景 *******/
	coreApp.scene().clear();
	/****** 将模型树上的节点清空 *******/
	nodeTree.setCurrentNode(nullptr);
	nodeTree.removeTopLevelNodes();
	LOG_INFO << "卸载项目";
	/****** 删除主窗口 *******/
	delete pMainWindow;
	LOG_INFO << "删除主窗口";
	/****** 卸载项目 *******/
	coreApp.getBMDesign().uninit();
	coreApp.getBMCatalog().uninit();

	/****** 卸载app *******/
	coreApp.destroy();

	LOG_INFO << "卸载app";
	/****** 退出日志 *******/
	WD::WDLogger::getInstance()->close();
	return r;
}

