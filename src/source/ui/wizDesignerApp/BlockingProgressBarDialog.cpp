#include "BlockingProgressBarDialog.h"
#include "wizDesignerMainWindow.h"
#include "core/viewer/WDViewer.h"
#include "core/WDTranslate.h"

BlockingProgressBarDialog::BlockingProgressBarDialog(wizDesignerMainWindow& mWindow, QWidget* parent)
    : QDialog(parent)
    , _mWindow(mWindow)
{
    ui.setupUi(this);

    this->setWindowTitle(QString::fromUtf8(WD::WDTs("BlockingProgressBarDialog", "progress").c_str()));

    auto tFlags = this->windowFlags();
    tFlags.setFlag(Qt::WindowType::WindowContextHelpButtonHint, false);
    this->setWindowFlags(tFlags);

    connect(this, &BlockingProgressBarDialog::sigStart, this, [=](const CallBack& callback)
        {
            this->_mWindow.core().viewer().setEnabled(false);
            this->_mWindow.setEnabled(false);
            if (callback)
                callback(*this);
        });

    connect(this, &BlockingProgressBarDialog::sigShowProgressBar, this, [=]()
        {
            auto& cfg = this->config();
            const auto& idDecimals = cfg.idDecimals;

            auto tFlags = this->windowFlags();
            tFlags.setFlag(Qt::WindowType::WindowCloseButtonHint, cfg.closable);
            this->setWindowFlags(tFlags);

            //auto pLayout = this->layout();
            // 先把之前的进度条移除
            while (!_datas.empty()) 
            {
                // 移除进度条对象并delete
                if (_datas.back().pProgressBar != nullptr)
                {
                    _datas.back().pProgressBar->hide();
                    _datas.back().pProgressBar->setParent(nullptr);
                    _datas.back().pProgressBar->deleteLater();
                }
                _datas.pop_back();
            }

            _datas.clear();

            // 默认的进度条显示样式
            std::string defalutFmt = ProgressBarFmt(cfg.decimals);
            // 进度条个数, 最少得有一个
            int barCnt = std::max(cfg.progressBarCount, 1);
            _datas.resize(barCnt);

            int yOffset = 0;
            for (int i = 0; i < static_cast<int>(_datas.size()); ++i)
            {
                auto& dt = _datas[i];

                auto fItr = idDecimals.find(i);
                if (fItr != idDecimals.end())
                    dt.fmt = ProgressBarFmt(fItr->second);
                else
                    dt.fmt = defalutFmt;

                dt.pProgressBar = new QProgressBar(this);
                dt.pProgressBar->setAlignment(Qt::AlignCenter);
                yOffset += 4;
                dt.pProgressBar->setGeometry(4, yOffset, this->width() - 8, dt.pProgressBar->height());
                yOffset += dt.pProgressBar->height();
                dt.pProgressBar->show();
            }

            this->setFixedHeight(yOffset + 4);
            this->resize(this->width(), yOffset + 4);

            if (!cfg.progress)
            {
                for (auto& dt : _datas) 
                {
                    dt.pProgressBar->setMinimum(0);
                    dt.pProgressBar->setMaximum(0);
                }
            }
            else
            {
                char buf[1024] = { 0 };
                for (auto& dt : _datas) 
                {
                    dt.pProgressBar->setMinimum(0);
                    dt.pProgressBar->setMaximum(100); 

                    dt.pProgressBar->setValue(0);
                    sprintf_s(buf, sizeof(buf), dt.fmt.c_str(), "", 0.0, "%");
                    dt.pProgressBar->setFormat(QString::fromUtf8(buf));
                }
            }

            this->exec();
        });

    connect(this, &BlockingProgressBarDialog::sigHideProgressBar, this, [=]()
        {
            this->accept();
        });

    connect(this, &BlockingProgressBarDialog::sigEnd, this, [=](const CallBack& callback)
        {
            this->_mWindow.setEnabled(true);
            this->_mWindow.core().viewer().setEnabled(true);

            if (callback)
                callback(*this);
        });

    connect(this, &BlockingProgressBarDialog::sigProgress, this, [=](int id)
        {
            if (id >= _datas.size())
                return;
            const auto& dt = _datas[id];
            // 获取进度值和文本
            double dValue = this->progress(id) * 100.0;
            const std::string& text = this->text(id);
            // 设置进度值和文本到进度条界面
            char buf[1024] = { 0 };
            sprintf_s(buf, sizeof(buf), dt.fmt.c_str(), text.c_str(), dValue, "%");
            dt.pProgressBar->setValue(static_cast<int>(dValue));
            dt.pProgressBar->setFormat(QString::fromUtf8(buf));

        });
}
BlockingProgressBarDialog::~BlockingProgressBarDialog()
{
}

void BlockingProgressBarDialog::closeEvent(QCloseEvent* e)
{
    // 这里设置退出标记
    this->setExistFlag();
    // 这里忽略关闭事件，需要等待退出通知, 而不是直接在这里关闭
    e->ignore();
}
void BlockingProgressBarDialog::resizeEvent(QResizeEvent* e)
{
    for (auto& dt: _datas)
    {
        dt.pProgressBar->resize(e->size().width() - 8, dt.pProgressBar->height());
    }
}

void BlockingProgressBarDialog::onStart(const CallBack& callback)
{
    emit sigStart(callback);
}
void BlockingProgressBarDialog::onShowProgressBar()
{
    emit sigShowProgressBar();
}
void BlockingProgressBarDialog::onHideProgressBar()
{
    emit sigHideProgressBar();
}
void BlockingProgressBarDialog::onEnd(const CallBack& callback)
{
    emit sigEnd(callback);
}
void BlockingProgressBarDialog::onProgress(int id)
{
    emit sigProgress(id);
}