//
// Created for DesignService client implementation
//

#include "DesignServiceClient.h"
#include <stdexcept>
#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <openssl/err.h>
#include <openssl/bio.h>
#include "document.h"

namespace wiz
{
    // RSA公钥加密函数
    std::string encrypt(const std::string& str, const std::string& publicKey)
    {
        // Base64解码公钥
        BIO* b64 = BIO_new(BIO_f_base64());
        BIO* bio = BIO_new_mem_buf(publicKey.c_str(), -1);
        bio = BIO_push(b64, bio);
        BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL); // 没有换行符

        std::vector<unsigned char> buffer(publicKey.size());
        int decoded_size = BIO_read(bio, buffer.data(), publicKey.size());
        BIO_free_all(bio);
        buffer.resize(decoded_size);

        if (decoded_size <= 0)
        {
            throw std::runtime_error("Base64解码失败");
        }

        // 使用EVP接口创建公钥
        EVP_PKEY* pkey = NULL;
        const unsigned char* key_data = buffer.data();

        // 尝试使用d2i_PUBKEY（替代d2i_RSA_PUBKEY）
        pkey = d2i_PUBKEY(NULL, &key_data, decoded_size);

        if (!pkey)
        {
            // 如果直接解析失败，尝试从BIO创建
            BIO* key_bio = BIO_new_mem_buf(buffer.data(), decoded_size);
            pkey = PEM_read_bio_PUBKEY(key_bio, NULL, NULL, NULL);
            BIO_free(key_bio);

            if (!pkey)
            {
                throw std::runtime_error("无法加载公钥: " + std::string(ERR_error_string(ERR_get_error(), NULL)));
            }
        }

        // 创建加密上下文
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new(pkey, NULL);
        if (!ctx)
        {
            EVP_PKEY_free(pkey);
            throw std::runtime_error("创建加密上下文失败: " + std::string(ERR_error_string(ERR_get_error(), NULL)));
        }

        // 初始化加密操作
        if (EVP_PKEY_encrypt_init(ctx) <= 0)
        {
            EVP_PKEY_CTX_free(ctx);
            EVP_PKEY_free(pkey);
            throw std::runtime_error("初始化加密操作失败: " + std::string(ERR_error_string(ERR_get_error(), NULL)));
        }

        // 设置RSA填充模式
        if (EVP_PKEY_CTX_set_rsa_padding(ctx, RSA_PKCS1_PADDING) <= 0)
        {
            EVP_PKEY_CTX_free(ctx);
            EVP_PKEY_free(pkey);
            throw std::runtime_error("设置RSA填充模式失败: " + std::string(ERR_error_string(ERR_get_error(), NULL)));
        }

        // 确定加密后的数据长度
        size_t outlen;
        if (EVP_PKEY_encrypt(ctx, NULL, &outlen, (const unsigned char*)str.c_str(), str.size()) <= 0)
        {
            EVP_PKEY_CTX_free(ctx);
            EVP_PKEY_free(pkey);
            throw std::runtime_error("确定加密数据长度失败: " + std::string(ERR_error_string(ERR_get_error(), NULL)));
        }

        // 执行加密操作
        std::vector<unsigned char> encrypted(outlen);
        if (EVP_PKEY_encrypt(ctx, encrypted.data(), &outlen, (const unsigned char*)str.c_str(), str.size()) <= 0)
        {
            EVP_PKEY_CTX_free(ctx);
            EVP_PKEY_free(pkey);
            throw std::runtime_error("加密失败: " + std::string(ERR_error_string(ERR_get_error(), NULL)));
        }

        // 释放资源
        EVP_PKEY_CTX_free(ctx);
        EVP_PKEY_free(pkey);

        // Base64编码加密结果
        BIO* b64_out = BIO_new(BIO_f_base64());
        BIO* mem = BIO_new(BIO_s_mem());
        BIO* bio_out = BIO_push(b64_out, mem);
        BIO_set_flags(bio_out, BIO_FLAGS_BASE64_NO_NL); // 没有换行符

        BIO_write(bio_out, encrypted.data(), outlen);
        BIO_flush(bio_out);

        BUF_MEM* bptr;
        BIO_get_mem_ptr(bio_out, &bptr);
        std::string encoded(bptr->data, bptr->length);
        BIO_free_all(bio_out);

        return encoded;
    }

    design::LoginResponse DesignServiceClient::Login(const design::UserInfo& userInfo, int timeoutMs)
    {
        return executeWithRetry<std::function<design::LoginResponse(design::DesignService::Stub*, int)>,
                                design::LoginResponse>(
            [this,&userInfo](design::DesignService::Stub* stub, int timeout)
            {
                // 创建响应对象
                design::LoginResponse response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->Login(context.get(), userInfo, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "Login"));
                }

                // 返回登录响应
                return response;
            },
            "Login",
            timeoutMs
        );
    }

    std::optional<std::string> DesignServiceClient::LoginChain(const design::UserInfo& userInfo, int timeoutMs)
    {
        try
        {
            auto user = userInfo;
            auto& grpcConfig = DesignServiceConfig::getInstance();
            grpcConfig.setAuthorization(""); // 清空token
            user.set_flag(design::LoginFlag::PUBLIC_KEY);
            auto response = Login(userInfo, timeoutMs);
            if (response.code() != 1)
            {
                LOG(INFO) << "Login error:" << response.code() << "|" << response.message()
                    << "|" << response.result();
                return {};
            }
            // 登录
            user.set_flag(design::LoginFlag::LOGIN);
            std::string encryptedPwd = encrypt(userInfo.pwd(), response.result());
            user.set_pwd(encryptedPwd);
            auto response2 = Login(user);
            if (response2.code() != 1)
            {
                LOG(INFO) << "Login error:" << response2.code() << "|" << response2.message()
                    << "|" << response2.result();
                return std::nullopt;
            }
            // json 结果 解析 token
            rapidjson::Document doc;
            doc.Parse(response2.result().c_str());
            std::string tokenHeader = doc["tokenHeader"].GetString();
            auto token = doc["token"].GetString();
            LOG(INFO) << tokenHeader << " " << token;
            grpcConfig.setAuthorization(tokenHeader + " " + token);
            return response2.result();
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Login 加密失败: " << e.what();
        }
        return std::nullopt;
    }

    design::ProjectNodesTree DesignServiceClient::CheckInOut(const design::ProjectNodesTree& projectNodesTree,
                                                             int timeoutMs)
    {
        return executeWithRetry<std::function<design::ProjectNodesTree(design::DesignService::Stub*, int)>,
                                design::ProjectNodesTree>(
            [this, &projectNodesTree](design::DesignService::Stub* stub, int timeout)
            {
                // 创建响应对象
                design::ProjectNodesTree response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->CheckInOut(context.get(), projectNodesTree, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "CheckInOut"));
                }

                // 返回登录响应
                return response;
            },
            "CheckInOut",
            timeoutMs
        );
    }

    std::string DesignServiceClient::getConfig(
        const design::ProjectInfo& projectInfo,
        int timeoutMs)
    {
        return executeWithRetry<std::function<std::string(design::DesignService::Stub*, int)>, std::string>(
            [this, &projectInfo](design::DesignService::Stub* stub, int timeout)
            {
                // 创建响应对象
                design::ProjectConfigInfo response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->GetConfig(context.get(), projectInfo, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "getConfig"));
                }

                // 返回JSON配置
                return response.json();
            },
            "getConfig",
            timeoutMs
        );
    }


    std::future<std::string> DesignServiceClient::getConfigAsync(
        const design::ProjectInfo& projectInfo,
        int timeoutMs)
    {
        return std::async(std::launch::async, [this, projectInfo, timeoutMs]()
        {
            return getConfig(projectInfo, timeoutMs);
        });
    }

    std::map<std::string, int64_t> DesignServiceClient::acquireDictionaryCode(
        const design::ProjectInfo& projectInfo,
        const std::map<std::string, int64_t>& keyCodes,
        int timeoutMs)
    {
        return executeWithRetry<std::function<std::map<std::string, int64_t>(design::DesignService::Stub*, int)>,
                                std::map<std::string, int64_t>>(
            [this, &projectInfo, &keyCodes](
            design::DesignService::Stub* stub, int timeout)
            {
                // 创建请求
                design::ProjectCodes request;
                request.mutable_projectinfo()->CopyFrom(projectInfo);
                // 添加键值对到请求
                for (const auto& [key, code] : keyCodes)
                {
                    design::KeyCode* keyCode = request.add_key_code();
                    keyCode->set_key(key);
                    keyCode->set_code(code);
                }

                // 创建响应对象
                design::ProjectCodes response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->AcquireDictionaryCode(context.get(), request, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "acquireDictionaryCode"));
                }
                // 转换响应为map
                std::map<std::string, int64_t> result;
                for (const auto& keyCode : response.key_code())
                {
                    DLOG(INFO) << "key: " << keyCode.key() << " code: " << keyCode.code();
                    result[keyCode.key()] = keyCode.code();
                }

                return result;
            },
            "acquireDictionaryCode",
            timeoutMs
        );
    }


    std::future<std::map<std::string, int64_t>> DesignServiceClient::acquireDictionaryCodeAsync(
        const design::ProjectInfo& projectInfo,
        const std::map<std::string, int64_t>& keyCodes,
        int timeoutMs)
    {
        return std::async(std::launch::async, [this, projectInfo, keyCodes, timeoutMs]()
        {
            return acquireDictionaryCode(projectInfo, keyCodes, timeoutMs);
        });
    }


    std::map<std::string, int64_t> DesignServiceClient::fetchAllDictionaryCode(
        const design::ProjectInfo& projectInfo,
        int64_t maxOffset,
        int timeoutMs)
    {
        return executeWithRetry<std::function<std::map<std::string, int64_t>(design::DesignService::Stub*, int)>,
                                std::map<std::string, int64_t>>(
            [this, &projectInfo, maxOffset](
            design::DesignService::Stub* stub, int timeout)
            {
                // 创建请求
                design::MaxOffset request;
                request.mutable_projectinfo()->CopyFrom(projectInfo);
                request.set_maxoffset(maxOffset);

                // 创建响应对象
                design::ProjectCodes response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->FetchAllDictionaryCode(context.get(), request, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "fetchAllDictionaryCode"));
                }

                // 转换响应为map
                std::map<std::string, int64_t> result;
                for (const auto& keyCode : response.key_code())
                {
                    result[keyCode.key()] = keyCode.code();
                }

                return result;
            },
            "fetchAllDictionaryCode",
            timeoutMs
        );
    }


    std::future<std::map<std::string, int64_t>> DesignServiceClient::fetchAllDictionaryCodeAsync(
        const design::ProjectInfo& projectInfo,
        int64_t maxOffset,
        int timeoutMs)
    {
        return std::async(std::launch::async,
                          [this, projectInfo, maxOffset, timeoutMs]()
                          {
                              return fetchAllDictionaryCode(projectInfo, maxOffset, timeoutMs);
                          });
    }


    design::ProjectNodesResult DesignServiceClient::updateNodes(
        const design::ProjectInfo& projectInfo,
        const std::vector<design::NodeAttrsRecord>& nodes,
        int32_t action,
        int timeoutMs)
    {
        return executeWithRetry<std::function<design::ProjectNodesResult
                                    (design::DesignService::Stub*, int)>, design::ProjectNodesResult>(
            [this, &projectInfo, &nodes, action](
            design::DesignService::Stub* stub, int timeout)
            {
                // 创建请求
                design::ProjectNodes request;
                request.mutable_projectinfo()->CopyFrom(projectInfo);

                // 添加节点到请求
                for (const auto& node : nodes)
                {
                    *request.add_nodes() = node;
                }

                // 设置操作类型
                request.set_action(action);

                // 创建响应对象
                design::ProjectNodesResult response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->UpdateNodes(context.get(), request, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "updateNodes"));
                }

                return response;
            },
            "updateNodes",
            timeoutMs
        );
    }


    std::future<design::ProjectNodesResult> DesignServiceClient::updateNodesAsync(
        const design::ProjectInfo& projectInfo,
        const std::vector<design::NodeAttrsRecord>& nodes,
        int32_t action,
        int timeoutMs)
    {
        return std::async(std::launch::async,
                          [this, projectInfo, nodes, action, timeoutMs]()
                          {
                              return updateNodes(projectInfo, nodes, action, timeoutMs);
                          });
    }

    design::ProjectNodesResult DesignServiceClient::updateNodeTree(
        const design::ProjectInfo& projectInfo,
        const std::vector<design::NodeTreeAction>& actions,
        int timeoutMs)
    {
        return executeWithRetry<std::function<design::ProjectNodesResult
                                    (design::DesignService::Stub*, int)>, design::ProjectNodesResult>(
            [this, &projectInfo, &actions](
            design::DesignService::Stub* stub, int timeout)
            {
                // 创建请求
                design::NodeTreeActions request;
                request.mutable_projectinfo()->CopyFrom(projectInfo);
                auto epoch = std::chrono::steady_clock::now().time_since_epoch().count();
                request.set_traceid(epoch);
                // 添加操作到请求
                LOG(INFO) << "updateNodeTree: traceid: " << epoch << ",actions:" << actions.size();
                for (const auto& action : actions)
                {
                    // LOG(INFO) << "action:" << action.parentid() << "," << action.flag() << "," << action.siblings_size();
                    *request.add_actions() = action;
                }

                // 创建响应对象
                design::ProjectNodesResult response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->UpdateNodeTree(context.get(), request, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "updateNodeTree"));
                }

                return response;
            },
            "updateNodeTree",
            timeoutMs
        );
    }

    std::future<design::ProjectNodesResult> DesignServiceClient::updateNodeTreeAsync(
        const design::ProjectInfo& projectInfo,
        const std::vector<design::NodeTreeAction>& actions,
        int timeoutMs)
    {
        return std::async(std::launch::async, [this, projectInfo, actions, timeoutMs]()
        {
            return updateNodeTree(projectInfo, actions, timeoutMs);
        });
    }

    design::ProjectInfo DesignServiceClient::ImportProjectProgress(const design::ProjectInfo& projectInfo,
                                                                   int timeoutMs)
    {
        return executeWithRetry<std::function<design::ProjectInfo(design::DesignService::Stub*, int)>,
                                design::ProjectInfo>(
            [this, &projectInfo](design::DesignService::Stub* stub, int timeout)
            {
                // 创建响应对象
                design::ProjectInfo response;

                // 创建上下文
                auto context = createContext(timeout);

                // 执行RPC调用
                grpc::Status status = stub->ImportProjectProgress(context.get(), projectInfo, &response);

                // 检查调用是否成功
                if (!status.ok())
                {
                    std::rethrow_exception(CreateExceptionFromStatus(status, "ImportProjectProgress"));
                }

                // 返回登录响应
                return response;
            },
            "ImportProjectProgress",
            timeoutMs
        );
    }
} // namespace wiz
