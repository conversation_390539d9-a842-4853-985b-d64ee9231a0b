//
// Created for DesignService client implementation
//

#ifndef DESIGN_SERVICE_CLIENT_H
#define DESIGN_SERVICE_CLIENT_H

#include <memory>
#include <string>
#include <future>
#include <functional>
#include <chrono>
#include <thread>
#include <glog/logging.h>
#include <grpcpp/grpcpp.h>
#include "proto/service.grpc.pb.h"
#include "DesignServiceError.h"
#include "DesignServiceConfig.h"
#include "DesignServiceConnectionPool.h"

namespace wiz
{
    /**
     * 设计服务客户端类
     *
     * 该类提供了与DesignService gRPC服务交互的方法，包括：
     * - 同步和异步调用方式
     * - 自动重试机制
     * - 超时控制
     * - 错误处理
     * - 连接池管理
     *
     * 使用示例：
     * ```cpp
     * // 获取单例实例
     * auto& client = DesignServiceClient::getInstance();
     *
     * // 初始化客户端
     * client.initialize("localhost:50051");
     *
     * try {
     *     // 创建ProjectInfo对象
     *     design::ProjectInfo projectInfo;
     *     projectInfo.set_projectcode("project123");
     *     projectInfo.set_classification("design");
     *     projectInfo.set_subtopic("attr_key");
     *     projectInfo.set_user("user001");
     *
     *     // 调用同步方法
     *     auto config = client.getConfig(projectInfo);
     *
     *     // 调用异步方法
     *     auto future = client.getConfigAsync(projectInfo);
     *     auto result = future.get();
     * } catch (const DesignServiceError& e) {
     *     // 处理错误
     * }
     * ```
     */
    class DesignServiceClient
    {
    public:
        DesignServiceClient(const DesignServiceClient&) = delete;
        /**
         * 获取客户端单例实例
         *
         * @return 客户端单例引用
         */
        static DesignServiceClient& getInstance()
        {
            static DesignServiceClient instance;
            return instance;
        }

        /**
         * 用户登录
         * @param userInfo
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return
         */
        design::LoginResponse Login(const design::UserInfo& userInfo, int timeoutMs = 0);
        /**
         * 用户登录链
         *    包含 获取密钥 加密密码 登录 解析token
         * @param userInfo  用户信息，用户名/密码必须
         * @param timeoutMs 超时时间
         * @return 用户登录后的相关信息；json结构
         */
        std::optional<std::string> LoginChain(const design::UserInfo& userInfo, int timeoutMs = 0);
        /**
         * 签入签出
         * @param projectNodesTree
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return
         */
        design::ProjectNodesTree CheckInOut(const design::ProjectNodesTree& projectNodesTree, int timeoutMs = 0);

        /**
         * 获取项目配置信息
         *
         * @param projectInfo 项目信息
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 项目配置（JSON字符串）
         * @throws DesignServiceError 如果gRPC调用失败
         */
        std::string getConfig(const design::ProjectInfo& projectInfo, int timeoutMs = 0);

        /**
         * 异步获取项目配置信息
         *
         * @param projectInfo 项目信息
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 包含结果的future对象
         */
        std::future<std::string> getConfigAsync(const design::ProjectInfo& projectInfo, int timeoutMs = 0);

        /**
         * 获取字典编码
         *
         * @param projectInfo 项目信息
         * @param keyCodes 键值对映射（值为0表示需要新编码）
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 更新后的键值对映射
         * @throws DesignServiceError 如果gRPC调用失败
         */
        std::map<std::string, int64_t> acquireDictionaryCode(
            const design::ProjectInfo& projectInfo,
            const std::map<std::string, int64_t>& keyCodes,
            int timeoutMs = 0);


        /**
         * 异步获取字典编码
         *
         * @param projectInfo 项目信息
         * @param keyCodes 键值对映射
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 包含结果的future对象
         */
        std::future<std::map<std::string, int64_t>> acquireDictionaryCodeAsync(
            const design::ProjectInfo& projectInfo,
            const std::map<std::string, int64_t>& keyCodes,
            int timeoutMs = 0);


        /**
         * 获取所有字典编码
         *
         * @param projectInfo 项目信息
         * @param maxOffset 最大偏移量
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 键值对映射
         * @throws DesignServiceError 如果gRPC调用失败
         */
        std::map<std::string, int64_t> fetchAllDictionaryCode(
            const design::ProjectInfo& projectInfo,
            int64_t maxOffset,
            int timeoutMs = 0);


        /**
         * 异步获取所有字典编码
         *
         * @param projectInfo 项目信息
         * @param maxOffset 最大偏移量
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 包含结果的future对象
         */
        std::future<std::map<std::string, int64_t>> fetchAllDictionaryCodeAsync(
            const design::ProjectInfo& projectInfo,
            int64_t maxOffset,
            int timeoutMs = 0);


        /**
         * 更新节点
         *
         * @param projectInfo 项目信息
         * @param nodes 要更新的节点
         * @param action 操作类型（1-notice, 2-checkpoint）
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 节点ID到偏移信息的映射
         * @throws DesignServiceError 如果gRPC调用失败
         */
        design::ProjectNodesResult updateNodes(
            const design::ProjectInfo& projectInfo,
            const std::vector<design::NodeAttrsRecord>& nodes,
            int32_t action = 2,
            int timeoutMs = 0);


        /**
         * 异步更新节点
         *
         * @param projectInfo 项目信息
         * @param nodes 要更新的节点
         * @param action 操作类型（1-notice, 2-checkpoint）
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 包含结果的future对象
         */
        std::future<design::ProjectNodesResult> updateNodesAsync(
            const design::ProjectInfo& projectInfo,
            const std::vector<design::NodeAttrsRecord>& nodes,
            int32_t action = 2,
            int timeoutMs = 0);


        /**
         * 更新节点树结构
         *
         * @param projectInfo 项目信息
         * @param actions 要执行的树操作
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 节点ID到偏移信息的映射
         * @throws DesignServiceError 如果gRPC调用失败
         */
        design::ProjectNodesResult updateNodeTree(
            const design::ProjectInfo& projectInfo,
            const std::vector<design::NodeTreeAction>& actions,
            int timeoutMs = 0);


        /**
         * 异步更新节点树结构
         *
         * @param projectInfo 项目信息
         * @param actions 要执行的树操作
         * @param timeoutMs 超时时间（毫秒），如果为0则使用默认值
         * @return 包含结果的future对象
         */
        std::future<design::ProjectNodesResult> updateNodeTreeAsync(
            const design::ProjectInfo& projectInfo,
            const std::vector<design::NodeTreeAction>& actions,
            int timeoutMs = 0);

        design::ProjectInfo ImportProjectProgress(const design::ProjectInfo& projectInfo, int timeoutMs = 0);
        /**
         * 关闭客户端
         */
        static void shutdown()
        {
            DesignServiceConnectionPool::getInstance().shutdown();
            LOG(INFO) << "DesignServiceClient 已关闭";
            // 注意：不在这里关闭glog，应该在主程序结束时关闭
        }

    private:
        // 私有构造函数，防止直接实例化
        DesignServiceClient()
        {
        }

        // 禁止拷贝和赋值

        DesignServiceClient& operator=(const DesignServiceClient&) = delete;

        /**
         * 执行带重试的RPC调用
         *
         * @param func 要执行的函数
         * @param methodName 方法名（用于日志）
         * @param timeoutMs 超时时间
         * @return 函数返回值
         */
        template <typename Func, typename ReturnType>
        ReturnType executeWithRetry(Func func, const std::string& methodName, int timeoutMs)
        {
            auto& config = DesignServiceConfig::getInstance();
            int actualTimeout = (timeoutMs > 0) ? timeoutMs : config.getDefaultTimeoutMs();
            int maxRetries = config.getMaxRetries();
            int retryInterval = config.getRetryIntervalMs();

            DLOG(INFO) << methodName << " 开始调用，超时: " << actualTimeout << "ms";

            for (int attempt = 0; attempt <= maxRetries; ++attempt)
            {
                if (attempt > 0)
                {
                    LOG(INFO) << methodName << " 重试 #" << attempt;
                    std::this_thread::sleep_for(std::chrono::milliseconds(retryInterval));
                }

                try
                {
                    // 获取连接并执行函数
                    auto stub = DesignServiceConnectionPool::getInstance().getConnection();
                    return func(stub.get(), actualTimeout);
                }
                catch (const DesignServiceConnectionError& e)
                {
                    // 连接错误可以重试
                    LOG(WARNING) << methodName << " 连接错误: " << e.what();
                    if (attempt == maxRetries)
                    {
                        LOG(ERROR) << methodName << " 达到最大重试次数";
                        std::rethrow_exception(std::current_exception());
                    }
                } catch (const DesignServiceTimeoutError& e)
                {
                    // 超时错误可以重试
                    LOG(WARNING) << methodName << " 超时错误: " << e.what();
                    if (attempt == maxRetries)
                    {
                        LOG(ERROR) << methodName << " 达到最大重试次数";
                        std::rethrow_exception(std::current_exception());
                    }
                } catch (const DesignServiceError& e)
                {
                    // 其他错误不重试
                    LOG(ERROR) << methodName << " 错误: " << e.what();
                    std::rethrow_exception(std::current_exception());
                }
            }

            // 不应该到达这里
            throw DesignServiceError(methodName + " 未知错误");
        }

        /**
         * 创建带超时的gRPC上下文
         */
        static std::unique_ptr<grpc::ClientContext> createContext(int timeoutMs)
        {
            auto context = std::make_unique<grpc::ClientContext>();
            if (timeoutMs > 0)
            {
                context->set_deadline(std::chrono::system_clock::now() +
                    std::chrono::milliseconds(timeoutMs));
            }
            const auto& auth = DesignServiceConfig::getInstance().getAuthorization();
            if (!auth.empty())
            {
                context->AddMetadata("authorization", auth);
            }
            return context;
        }
    };
} // namespace wiz

#endif // DESIGN_SERVICE_CLIENT_H
