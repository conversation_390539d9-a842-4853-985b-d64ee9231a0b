//
// Created by everpan on 25-5-13.
//

#include <type_traits>
#include <algorithm>
#include "writer.h"
#include "stringbuffer.h"
#include <glog/logging.h>
#include "DesignTLV.h"
#include "DesignTLVFile.h"
#include "document.h"

namespace wiz
{
    /**
     * 字节转换
     */
    template <typename T>
    typename std::enable_if<std::is_integral<T>::value, T>::type
    byteSwap(T value)
    {
        static_assert(std::is_integral<T>::value, "byteSwap requires integral type");
        union
        {
            T value;
            unsigned char bytes[sizeof(T)];
        } src{}, dst{};

        src.value = value;
        std::reverse_copy(src.bytes, src.bytes + sizeof(T), dst.bytes);
        return dst.value;
    }

    template <typename T>
    T convertEndian(T value, bool fromLittleEndian, bool toLittleEndian)
    {
        if (fromLittleEndian == toLittleEndian)
        {
            return value; // 无需转换
        }
        return byteSwap(value); // 调用模板化转换函数
    }

    bool isLittleEndian()
    {
        const uint32_t value = 0x01234567;
        const auto* bytes = reinterpret_cast<const uint8_t*>(&value);
        return bytes[0] == 0x67; // 小端：低位在前
    }

    // 网络序（大端）与主机序互转
    template <typename T>
    T networkToHost(T netValue)
    {
        return convertEndian(netValue, false, isLittleEndian());
    }

    template <typename T>
    T hostToNetwork(T hostValue)
    {
        return convertEndian(hostValue, isLittleEndian(), false);
    }

    std::string DesignTLVFile::Header::toJson() const
    {
        rapidjson::Document doc;
        doc.SetObject();
        rapidjson::Document::AllocatorType& allocator = doc.GetAllocator();

        doc.AddMember("blockSize", blockSize, allocator);
        doc.AddMember("version", version, allocator);
        doc.AddMember("moduleType", moduleType, allocator);
        doc.AddMember("recordType", recordType, allocator);
        time_t t = lastTime / 1000;
        const auto tm = localtime(&t);
        if (tm == nullptr)
        {
            LOG(ERROR) << "invalid time" << lastTime;
        }
        else
        {
            char timeStr[32];
            std::strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", tm);
            doc.AddMember("lastTimeStr", rapidjson::Value(timeStr, allocator), allocator);
        }

        doc.AddMember("lastTime", lastTime, allocator);
        doc.AddMember("projectId", projectId, allocator);

        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        doc.Accept(writer);

        return buffer.GetString();
    }

    class RandomAccessFile;
    /**
     *
     * @param fName
     */
    DesignTLVFile::DesignTLVFile(const std::string& fName)
    {
        _file = std::make_unique<RandomAccessFile>(fName, RandomAccessFile::OpenMode::READ_WRITE, true);
        auto size = _file->getSize();
        if (size < sizeof(_header))
        {
            throw std::runtime_error("file:" + fName + " size is too small");
        }
        _file->read(reinterpret_cast<char*>(&_header), sizeof(_header), 0);
        if (_header.blockSize <= 0)
        {
            throw std::runtime_error("file:" + fName + " block size is invalid: " + std::to_string(_header.blockSize));
        }
        try
        {
            _header.blockSize = networkToHost(_header.blockSize);
            _header.lastTime = networkToHost(_header.lastTime);
            _header.projectId = networkToHost(_header.projectId);
            if (_header.blockSize <= 0)
            {
                throw std::runtime_error(
                    "file:" + fName + " block size is invalid: " + std::to_string(_header.blockSize));
            }
            _maxBlockSize = (size + _header.blockSize - 1) / _header.blockSize;
            LOG(INFO) << "DesignTLVFile " << fName << " opened, file size: " << size
                << ", max block size: " << _maxBlockSize;
            LOG(INFO) << _header.toJson();
        }
        catch (const std::exception& e)
        {
            LOG(INFO) << e.what();
            _isvalid = false;
            return;
        }
        _isvalid = true;
    }

    const DesignTLVFile::Header& DesignTLVFile::getHeader() const
    {
        return _header;
    }

    bool DesignTLVFile::isValid() const
    {
        return _isvalid;
    }

    uint32_t DesignTLVFile::maxBlockSize() const
    {
        return _maxBlockSize;
    }

    bool DesignTLVFile::fetchBlockData(int block, std::string& buffer) const
    {
        if (block >= _maxBlockSize) return false;
        auto off = static_cast<uint64_t>(_header.blockSize) * block;
        buffer.resize(_header.blockSize);
        auto r = _file->read(buffer.data(), _header.blockSize, off);
        return r > 0;
    }

    std::vector<DesignTLV> DesignTLVFile::fetchBlockTLV(int block, std::string& buffer) const
    {
        if (!fetchBlockData(block, buffer))
        {
            return {};
        }
        if (block == 0)
        {
            return DesignTLV::parseMultiple(reinterpret_cast<const uint8_t*>(buffer.data()) + sizeof(Header),
                                            buffer.size());
        }
        return DesignTLV::parseMultiple(reinterpret_cast<const uint8_t*>(buffer.data()), buffer.size());
    }

    int DesignTLVFile::updateStore(WD::store::IStore& store) const
    {
        std::string buffer;
        design::NodeAttrsRecord attrs;
        design::NodeTreeRecord trees;
        int count = 0;
        auto NodeType = _header.moduleType == 1 ? design::NodeType::DESIGN : design::NodeType::CATALOG;
        for (int i = 0; i < maxBlockSize(); ++i)
        {
            fetchBlockData(i, buffer);
            if (buffer.empty()) break;
            auto tlvs = fetchBlockTLV(i, buffer);
            count += tlvs.size();
            //LOG(INFO) << "load block:" << i << ",tlv:" << tlvs.size();
            if (_header.recordType == 1) // attrs
            {
                for (auto& tlv : tlvs)
                {
                    attrs.ParseFromArray(tlv.getData(), tlv.getLength());
                    store.upsetNodeAttrsRecord(attrs, "", NodeType, WD::store::IStore::StoreFrom::FromServer);
                }
            }
            else
            {
                for (auto& tlv : tlvs)
                {
                    trees.ParseFromArray(tlv.getData(), tlv.getLength());
                    store.upsetNodeTreeRecord(trees, NodeType, WD::store::IStore::StoreFrom::FromServer);
                }
            }
        }
        return count;
    }
}
