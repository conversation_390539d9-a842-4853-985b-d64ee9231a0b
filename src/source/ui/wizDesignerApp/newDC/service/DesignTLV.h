#pragma once

#include <cstdint>
#include <vector>
#include <string>
#include <memory>
#include "proto/def.h"

namespace wiz
{
    /**
     * @brief TLV (Type-Length-Value) 消息封装格式的实现
     *
     * 该类实现了TLV格式的消息封装，其中：
     * - Type: 占1个字节，表示消息类型
     * - Length: 占2个字节（大端序），表示Value的长度
     * - Value: 长度为Length的字节缓冲区
     *
     * 注意：该类不会复制数据，而是持有数据指针。因此，必须确保数据在TLV对象的生命周期内保持有效。
     */
    class DesignTLV
    {
    public:
        /**
         * @brief 默认构造函数，创建一个空的TLV消息
         */
        DesignTLV();

        /**
         * @brief 析构函数
         */
        ~DesignTLV() = default;

        /**
         * @brief 从字节缓冲区解析TLV消息
         *
         * @param buffer 包含TLV消息的字节缓冲区（不会被复制，必须保持有效）
         * @param length 缓冲区长度
         * @return true 如果解析成功
         * @return false 如果解析失败
         */
        bool parse(const uint8_t* buffer, size_t length);
        /**
         * 解析版本v0的数据，包含多个同类信息 具体看文档 2025-06-11
         * @param buffer
         * @param length
         * @return 多个数据地址，信息长度对
         */
        std::vector<std::pair<void*, int>> parseV0(const uint8_t* buffer, size_t length);
        int getVersion() const
        {
            return _version;
        }

        /**
         * @brief 从字节缓冲区解析多个TLV消息
         *
         * @param buffer 包含多个TLV消息的字节缓冲区（不会被复制，必须保持有效）
         * @param length 缓冲区长度
         * @return std::vector<DesignTLV> 解析出的TLV消息列表
         */
        static std::vector<DesignTLV> parseMultiple(const uint8_t* buffer, size_t length);

        /**
         * @brief 获取消息类型
         *
         * @return uint8_t 消息类型
         */
        int getType() const;

        /**
         * @brief 设置消息类型
         *
         * @param type 消息类型
         */
        void setType(uint8_t type);

        /**
         * tlv中 _module_type 与 design::NodeType 有对应关系
         * 避免手动转换出错
         * @return 设计节点类型
         */
        design::NodeType getNodeType() const
        {
            if (_module_type == 1)
            {
                return design::NodeType::DESIGN;
            }
            return design::NodeType::CATALOG;
        }

        /**
         * @brief 获取消息值的长度
         *
         * @return uint16_t 消息值的长度
         */


        uint16_t getLength() const;

        /**
         * @brief 获取消息值的指针
         *
         * @return const void* 消息值的指针
         */
        const uint8_t* getData() const;

        /**
         * @brief 设置消息值
         *
         * @param data 消息值的指针（不会被复制，必须保持有效）
         * @param length 消息值的长度
         */
        void setData(const uint8_t* data, uint16_t length);

        /**
         * @brief 序列化TLV消息到指定的字节缓冲区
         *
         * @param buffer 目标字节缓冲区
         * @param offset 缓冲区中的偏移量
         * @return size_t 写入的字节数
         */
        size_t serialize(uint8_t* buffer, size_t offset = 0) const;

        /**
         * @brief 获取序列化后的总长度（Type + Length + Value）
         *
         * @return size_t 序列化后的总长度
         */
        size_t getTotalLength() const;

        /**
         * @brief 检查TLV消息是否有效
         *
         * @return true 如果消息有效
         * @return false 如果消息无效
         */
        bool isValid() const;

    private:
        uint8_t _version;
        uint8_t _type; // 消息类型（1字节）
        uint8_t _module_type; // 消息分类（1字节）
        uint8_t _count; // 消息个数
        uint16_t _length; // 消息值的长度（2字节）
        const uint8_t* _data; // 消息值的指针（不复制数据）
        bool _valid; // 消息是否有效
    };
} // namespace wiz
