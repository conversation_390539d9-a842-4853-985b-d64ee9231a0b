#include "DesignNode2Queue.h"

// 在实现文件中包含实际的头文件
#include "core/node/WDNode.h"
#include "serialize/WDBMProtobufSerialize.h"
#include "CodePairFactory.h"
#include "rpc/client/DesignServiceConnectionPool.h"
#include <glog/logging.h>
#include <future>

namespace wiz
{
    DesignNode2Queue& DesignNode2Queue::getInstance()
    {
        static DesignNode2Queue instance;
        return instance;
    }

    DesignNode2Queue::DesignNode2Queue()
        : _nodeTreeActionQueue(NodeType::NodeTypeSize)
          , _nodeAttrsRecordQueue(NodeType::NodeTypeSize)
          , _dbNodeAttrsRecordQueue(NodeType::NodeTypeSize)
    {
        std::thread dbThread([this]() {
            while (!_dbNodeAttrsRecordQueue.empty())
            {
                bool processedItems = false;
                // 处理DBNodeAttrsRecords
                if (this->getDBNodeAttrsRecordQueueSize(NodeType::CATALOG) > 0)
                {
                    this->processDBNodeAttrsRecords(NodeType::CATALOG, 100);
                    processedItems = true;
                }
                if (this->getDBNodeAttrsRecordQueueSize(NodeType::DESIGN) > 0)
                {
                    this->processDBNodeAttrsRecords(NodeType::DESIGN, 100);
                    processedItems = true;
                }

                // 如果没有处理项目，睡眠一小段时间
                if (!processedItems)
                {
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
            }
            });
        dbThread.detach();
    }

    DesignNode2Queue::~DesignNode2Queue()
    {
        shutdown();
    }

    WD::WDBMProtobufSerializeInterface& DesignNode2Queue::getSerializer(NodeType type) const
    {
        return *_serializers[type];
    }

    void DesignNode2Queue::setStore(WD::store::IStoreUPtr store)
    {
        _store = std::move(store);
    }

    void DesignNode2Queue::initialize(const design::ProjectInfo& projectInfo,
                                      const CommonStoreSPtr& commonStore,
                                      WD::WDBMBase* designBase,
                                      WD::WDBMBase* catalogBase, int maxSize)
    {
        _serializers.resize(NodeType::NodeTypeSize);
        auto client = DesignServiceConnectionPool::getInstance().getConnection();
        _serializers[NodeType::CATALOG] = WD::WDBMProtobufSerialize::CreateGrpcSerialize(
            projectInfo, client, commonStore, NodeType::CATALOG, catalogBase);
        _serializers[NodeType::DESIGN] = WD::WDBMProtobufSerialize::CreateGrpcSerialize(
            projectInfo, client, commonStore, NodeType::DESIGN, designBase);
        setQueueSize(maxSize);
    }

    void DesignNode2Queue::setQueueSize(int maxSize)
    {
        _nodeAttrsRecordQueue[NodeType::CATALOG].setMaxSize(maxSize);
        _nodeAttrsRecordQueue[NodeType::DESIGN].setMaxSize(maxSize);
        _dbNodeAttrsRecordQueue[NodeType::CATALOG].setMaxSize(maxSize);
        _dbNodeAttrsRecordQueue[NodeType::DESIGN].setMaxSize(maxSize);
        _nodeTreeActionQueue[NodeType::CATALOG].setMaxSize(maxSize);
        _nodeTreeActionQueue[NodeType::DESIGN].setMaxSize(maxSize);
    }

    void DesignNode2Queue::shutdown()
    {
        // 清空队列
        clearQueues();
    }

    bool DesignNode2Queue::pushNodeTreeAction(const design::NodeTreeAction& action, NodeType type)
    {
        return _nodeTreeActionQueue[type].push(action);
    }

    bool DesignNode2Queue::pushNodeTreeAction(design::NodeTreeAction&& action, NodeType type)
    {
        return _nodeTreeActionQueue[type].push(std::move(action));
    }

    bool DesignNode2Queue::pushNodeTreeActions(const std::vector<design::NodeTreeAction>& actions, NodeType type)
    {
        return _nodeTreeActionQueue[type].pushBatch(actions);
    }

    bool DesignNode2Queue::pushNodeTreeActions(std::vector<design::NodeTreeAction>&& actions, NodeType type)
    {
        return _nodeTreeActionQueue[type].pushBatch(std::move(actions));
    }

    bool DesignNode2Queue::pushNodeAttrsRecord(const design::NodeAttrsRecord& record, NodeType type)
    {
        return _nodeAttrsRecordQueue[type].push(record);
    }

    bool DesignNode2Queue::pushNodeAttrsRecord(design::NodeAttrsRecord&& record, NodeType type)
    {
        return _nodeAttrsRecordQueue[type].push(std::move(record));
    }

    bool DesignNode2Queue::pushNodeAttrsRecords(const std::vector<design::NodeAttrsRecord>& records, NodeType type)
    {
        return _nodeAttrsRecordQueue[type].pushBatch(records);
    }

    bool DesignNode2Queue::pushNodeAttrsRecords(std::vector<design::NodeAttrsRecord>&& records, NodeType type)
    {
        return _nodeAttrsRecordQueue[type].pushBatch(std::move(records));
    }

    size_t DesignNode2Queue::getNodeTreeActionQueueSize(NodeType type) const
    {
        return _nodeTreeActionQueue[type].size();
    }

    size_t DesignNode2Queue::getNodeAttrsRecordQueueSize(NodeType type) const
    {
        return _nodeAttrsRecordQueue[type].size();
    }

    size_t DesignNode2Queue::getDBNodeAttrsRecordQueueSize(NodeType type) const
    {
        return _dbNodeAttrsRecordQueue[type].size();
    }

    size_t DesignNode2Queue::popNodeTreeActions(std::vector<design::NodeTreeAction>& actions, NodeType type,
                                                size_t maxItems, unsigned long waitMs)
    {
        return _nodeTreeActionQueue[type].popBatch(actions, maxItems, waitMs);
    }

    size_t DesignNode2Queue::popNodeAttrsRecords(std::vector<design::NodeAttrsRecord>& records, NodeType type,
                                                 size_t maxItems, unsigned long waitMs)
    {
        return _nodeAttrsRecordQueue[type].popBatch(records, maxItems, waitMs);
    }

    size_t DesignNode2Queue::popDBNodeAttrsRecords(std::vector<DBNodeAttrsRecord>& records, NodeType type,
        size_t maxItems, unsigned long waitMs)
    {
        return _dbNodeAttrsRecordQueue[type].popBatch(records, maxItems, waitMs);
    }

    std::string DesignNode2Queue::fetchUUID(design::NodeAttrsRecord& record) const
    {
        static int64_t id_key = -1;
        static WD::WDUuid uuid;
        if (id_key == -1)
        {
            auto coder = getSerializer(NodeType::CATALOG).getCodeContainer();
            id_key = coder->FetchCodePair(CodePairFactory::Code_Attr_key)->AcquireCode("__uuid__");
            if (id_key == -1)
            {
                LOG(ERROR) << "Failed to fetch id_key of uuid" << std::endl;
                throw std::runtime_error("Failed to fetch id_key");
                assert(false);
            }
        }
        auto& men = record.attrs().attr_map().at(id_key).val_string();
        uuid.fromMemory(men.data());
        return uuid.toString();
    }

    void DesignNode2Queue::batchFetchUUIDCodes(const std::vector<WDNodePtr>& nodes, NodeType type) const
    {
        auto coder = getSerializer(type).getCodeContainer();
        // 批量 1000个uuid获取对应的 code
        std::map<std::string, int64_t> uuids;

        int maxSize = 0;
        static constexpr auto LEN = offsetof(WD::WDUuid, data4) + 8;
        auto it = nodes.begin();
        while (it != nodes.end())
        {
            auto& node = *it;
            if (node == nullptr)
            {
                ++it;
                continue;
            }

            uuids.emplace(string(reinterpret_cast<const char*>(&node->uuid()), LEN), 0);
            ++maxSize;
            ++it;
            if (maxSize >= 500)
            {
                coder->FetchCodePair(CodePairFactory::Code_UUID)->AcquireCodes(uuids);
                uuids.clear();
                maxSize = 0;
            }
        } // end while
        if (!uuids.empty())
        {
            coder->FetchCodePair(CodePairFactory::Code_UUID)->AcquireCodes(uuids);
        }
    }

    void DesignNode2Queue::processDBNodeAttrsRecords(NodeType type, size_t maxItems)
    {
        // 从队列中获取项目
        std::vector<wiz::DesignNode2Queue::DBNodeAttrsRecord> records;
        size_t count = this->popDBNodeAttrsRecords(records, type, maxItems, 100);

        if (count == 0)
        {
            return; // 没有项目需要处理
        }

        if (_store != nullptr)
        {
            auto future = std::async(std::launch::async,
                [this, &records, type]()
                {
                    for (const auto& [record, uuid] : records)
                    {
                        _store->upsetNodeAttrsRecord(record, uuid, type, WD::store::IStore::StoreFrom::FromQueue);
                    }
                });
        }
    }

    void DesignNode2Queue::clearQueues()
    {
        _nodeTreeActionQueue.clear();
        _nodeAttrsRecordQueue.clear();
        _dbNodeAttrsRecordQueue.clear();
    }

    bool DesignNode2Queue::deserializeNode(WDNodePtr& node, NodeType type, const design::NodeAttrsRecord& record,
                                           const WD::BASFlags& flags) const
    {
        return _serializers[type]->deserializeNode(*node, record, flags);
    }


    bool DesignNode2Queue::serNodeAndStoreAsFromQueue(const WDNodePtr& node,
                                                      NodeType type, int action, const WD::BASFlags& flags,
                                                      design::NodeAttrsRecord& record)
    {
        bool success = _serializers[type]->serializeNode(node, record, flags);
        auto id = node->getRemoteId();

        const auto uuid = node->uuid().toString();
        if (!success)
        {
            LOG(WARNING) << "Failed to serialize node: " << uuid << ", id: " << id;
            return false; // 序列化失败
        }
        record.set_action(action);
        // json 日志，效率慢，json信息受限于proto
        // std::string json_string;
        // proto_message2json(record, json_string);
        LOG(INFO) << "ser:" << id << "|" << uuid << "|" << node->name()
            << "|" << node->type() << "|" << record.type()
            << "|" << action << "|" << node->getTraceId() << "|" << record.type();
        // 添加到记录数组
        DBNodeAttrsRecord dbRecord;
        dbRecord.record = record;
        dbRecord.uuid = uuid;
        _dbNodeAttrsRecordQueue[type].push(std::move(dbRecord));
        return true;
    }

    bool DesignNode2Queue::pushNode(const WDNodePtr& node, NodeType type, int action, const WD::BASFlags& flags)
    {
        // 创建NodeAttrsRecord对象
        design::NodeAttrsRecord record;

        if (serNodeAndStoreAsFromQueue(node, type, action, flags, record))
        {
            // 推送到队列
            return pushNodeAttrsRecord(std::move(record), type);
        }
        return false;
    }


    bool DesignNode2Queue::pushNodes(const std::vector<WDNodePtr>& nodes, NodeType type, int action,
                                     const WD::BASFlags& flags)
    {
        // 先批量获取下uuid的coder
        //batchFetchUUIDCodes(nodes,type);
        // 创建NodeAttrsRecord对象数组
        std::vector<design::NodeAttrsRecord> records;
        records.reserve(nodes.size());
        // 遍历所有节点
        for (const auto& node : nodes)
        {
            if (!node)
            {
                continue; // 跳过无效节点
            }
            // 创建NodeAttrsRecord
            design::NodeAttrsRecord record;

            // 使用WDBMProtobufSerialize将WDNode转换为NodeAttrs
            if (serNodeAndStoreAsFromQueue(node, type, action, flags, record))
            {
                records.push_back(record);
            }
            //continue; // 跳过无效节点
        }

        // 如果没有有效记录，返回false
        if (records.empty())
        {
            return false;
        }

        // 推送到队列
        return pushNodeAttrsRecords(std::move(records), type);
    }

    bool DesignNode2Queue::pushNodesRecursive(const std::vector<WDNodePtr>& nodes, NodeType type,
                                              const int action,
                                              const WD::BASFlags& flags)
    {
        pushNodes(nodes, type, action, flags);
        for (const auto& node : nodes)
        {
            if (node->childCount() > 0)
            {
                pushNodesRecursive(node->children(), type, action, flags);
            }
        }
        return true;
    }
} // namespace wiz
