#include "DesignKafkaService.h"
#include <chrono>
#include <stdexcept>
#include <glog/logging.h>
#include <librdkafka/rdkafkacpp.h>
#include <thread>
#include "proto/message2json.h"

namespace wiz
{
    DesignKafkaService::DesignKafkaService()
        : _messageQueue(0)
          , _running(false)
          , _initialized(false)
          , _consumedMessageCount(0)
          , _failedMessageCount(0)
    {
    }

    DesignKafkaService::~DesignKafkaService()
    {
        DesignKafkaService::shutdown();
    }

    void DesignKafkaService::initialize(const std::string& bootstrapServers,
                                        const std::string& topic,
                                        const std::string& groupId,
                                        size_t maxQueueSize)
    {
        std::lock_guard<std::mutex> lock(_mutex);

        try
        {
            // 检查参数
            if (bootstrapServers.empty())
            {
                throw std::runtime_error("Bootstrap servers cannot be empty");
            }

            if (topic.empty())
            {
                throw std::runtime_error("Topic cannot be empty");
            }

            if (groupId.empty())
            {
                throw std::runtime_error("Group ID cannot be empty");
            }

            // 存储配置
            _bootstrapServers = bootstrapServers;
            _topic = topic;
            _groupId = groupId;

            // 设置队列大小
            _messageQueue.setMaxSize(maxQueueSize);

            // 重置统计信息
            _consumedMessageCount = 0;
            _failedMessageCount = 0;

            _initialized = true;
            LOG(INFO) << "DesignKafkaService initialized with bootstrap servers: " << bootstrapServers
                << ", topic: " << topic
                << ", group ID: " << groupId;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during initialization: " << e.what();
        }
    }

    bool DesignKafkaService::start()
    {
        std::lock_guard<std::mutex> lock(_mutex);

        try
        {
            // 检查服务状态
            checkInitializedAndNotRunning("start", false); // 只检查初始化状态，不检查运行状态

            if (_running)
            {
                LOG(WARNING) << "DesignKafkaService already running";
                return true; // 已经运行，返回成功
            }

            // 创建Kafka配置
            auto conf = createKafkaConfig(_bootstrapServers, _groupId);
            if (!conf)
            {
                throw std::runtime_error("Failed to create Kafka configuration");
            }

            // 创建Kafka消费者
            std::string errstr;

            // 创建KafkaConsumer
            _consumer.reset(RdKafka::KafkaConsumer::create(conf.get(), errstr));
            if (!_consumer)
            {
                throw std::runtime_error("Failed to create Kafka consumer: " + errstr);
            }

            // 订阅topic
            std::vector<std::string> topics = {_topic};
            RdKafka::ErrorCode err = _consumer->subscribe(topics);
            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to subscribe to topic " + _topic + ": " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;
                _consumer.reset();
                throw std::runtime_error(errorMsg);
            }

            // 启动消费者线程
            _running = true;
            _consumerThread = std::thread(&DesignKafkaService::consumerThread, this);

            LOG(INFO) << "DesignKafkaService started, consuming from topic: " << _topic;
            return true;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during Kafka consumer start: " << e.what();
            _consumer.reset();
            _running = false;
            return false;
        }
    }

    std::unique_ptr<RdKafka::TopicPartition> DesignKafkaService::createTopicPartition(
        const std::string& topic, int32_t partition, int64_t offset) const
    {
        return std::unique_ptr<RdKafka::TopicPartition>(RdKafka::TopicPartition::create(topic, partition, offset));
    }

    void DesignKafkaService::checkInitializedAndNotRunning(const std::string& methodName, bool checkRunningState) const
    {
        if (!_initialized)
        {
            std::string errorMsg = "DesignKafkaService not initialized when calling " + methodName;
            LOG(ERROR) << errorMsg;
            throw std::runtime_error(errorMsg);
        }

        if (checkRunningState && _running)
        {
            std::string errorMsg = "DesignKafkaService already running when calling " + methodName + ", stop it first";
            LOG(WARNING) << errorMsg;
            throw std::runtime_error(errorMsg);
        }
    }

    void DesignKafkaService::checkRunning(const std::string& methodName) const
    {
        if (!_running)
        {
            std::string errorMsg = "DesignKafkaService not running when calling " + methodName;
            LOG(ERROR) << errorMsg;
            throw std::runtime_error(errorMsg);
        }
    }

    void DesignKafkaService::checkConsumerCreated(const std::string& methodName) const
    {
        if (!_consumer)
        {
            std::string errorMsg = "Kafka consumer not created when calling " + methodName;
            LOG(ERROR) << errorMsg;
            throw std::runtime_error(errorMsg);
        }
    }

    bool DesignKafkaService::startFromTopicPartitions(
        const std::vector<std::tuple<std::string, int32_t, int64_t>>& topicPartitions)
    {
        std::lock_guard<std::mutex> lock(_mutex);

        try
        {
            // 检查服务状态
            checkInitializedAndNotRunning("startFromTopicPartitions");

            // 创建TopicPartition对象
            std::vector<std::unique_ptr<RdKafka::TopicPartition>> rdkafkaTopicPartitions;
            for (const auto& tp : topicPartitions)
            {
                const auto& [topic, partition, offset] = tp;
                rdkafkaTopicPartitions.push_back(createTopicPartition(topic, partition, offset));
            }

            // 启动消费者并设置偏移量
            bool result = startConsumerWithPartitions(rdkafkaTopicPartitions);

            if (result)
            {
                LOG(INFO) << "DesignKafkaService started from " << topicPartitions.size() << " topic partitions";
            }

            return result;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during Kafka consumer start from topic partitions: " << e.what();
            _consumer.reset();
            _running = false;
            return false;
        }
    }

    bool DesignKafkaService::startFromTimestamp(int64_t timestamp, int32_t partition)
    {
        std::lock_guard<std::mutex> lock(_mutex);

        try
        {
            // 检查服务状态
            checkInitializedAndNotRunning("startFromTimestamp");

            // 创建Kafka配置
            auto conf = createKafkaConfig(_bootstrapServers, _groupId);
            if (!conf)
            {
                throw std::runtime_error("Failed to create Kafka configuration");
            }

            // 创建Kafka消费者
            std::string errstr;
            _consumer.reset(RdKafka::KafkaConsumer::create(conf.get(), errstr));
            if (!_consumer)
            {
                throw std::runtime_error("Failed to create Kafka consumer: " + errstr);
            }

            // 创建TopicPartition对象并设置时间戳
            std::vector<std::unique_ptr<RdKafka::TopicPartition>> topicPartitions;
            auto topicPartition = createTopicPartition(_topic, partition);
            topicPartition->set_offset(timestamp); // 在查询偏移量时，偏移量字段用于存储时间戳
            topicPartitions.push_back(std::move(topicPartition));

            // 查询时间戳对应的偏移量
            // 创建RdKafka::TopicPartition*数组用于offsetsForTimes API
            std::vector<RdKafka::TopicPartition*> rawTopicPartitions;
            for (const auto& tp : topicPartitions)
            {
                rawTopicPartitions.push_back(tp.get());
            }

            RdKafka::ErrorCode err = _consumer->offsetsForTimes(rawTopicPartitions, 5000);
            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to query offsets for timestamp " +
                    std::to_string(timestamp) + ": " +
                    RdKafka::err2str(err);
                LOG(ERROR) << errorMsg;
                _consumer.reset();
                throw std::runtime_error(errorMsg);
            }

            // 检查查询结果
            int64_t resultOffset = topicPartitions[0]->offset();
            if (resultOffset < 0)
            {
                std::string errorMsg = "Failed to find offset for timestamp " +
                    std::to_string(timestamp) +
                    ", error code: " + std::to_string(resultOffset);
                LOG(ERROR) << errorMsg;
                _consumer.reset();
                throw std::runtime_error(errorMsg);
            }

            LOG(INFO) << "Found offset " << resultOffset << " for timestamp " << timestamp;

            // 使用找到的偏移量创建TopicPartition
            std::vector<std::unique_ptr<RdKafka::TopicPartition>> assignTopicPartitions;
            assignTopicPartitions.push_back(createTopicPartition(_topic, partition, resultOffset));

            // 启动消费者并设置偏移量
            bool result = startConsumerWithPartitions(assignTopicPartitions);

            if (result)
            {
                LOG(INFO) << "DesignKafkaService started from timestamp " << timestamp
                    << " (offset " << resultOffset << ") for partition " << partition;
            }

            return result;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during Kafka consumer start from timestamp: " << e.what();
            _consumer.reset();
            _running = false;
            return false;
        }
    }

    bool DesignKafkaService::startFromTimestamps(
        const std::vector<std::tuple<std::string, int32_t, int64_t>>& topicPartitionsWithTimestamp)
    {
        std::lock_guard<std::mutex> lock(_mutex);

        try
        {
            // 检查服务状态
            checkInitializedAndNotRunning("startFromTimestamps");

            // 创建Kafka配置
            auto conf = createKafkaConfig(_bootstrapServers, _groupId);
            if (!conf)
            {
                throw std::runtime_error("Failed to create Kafka configuration");
            }

            // 创建Kafka消费者
            std::string errorMsg;
            _consumer.reset(RdKafka::KafkaConsumer::create(conf.get(), errorMsg));
            if (!_consumer)
            {
                throw std::runtime_error("Failed to create Kafka consumer: " + errorMsg);
            }

            // 创建TopicPartition对象并设置时间戳
            std::vector<std::unique_ptr<RdKafka::TopicPartition>> topicPartitions;
            for (const auto& tp : topicPartitionsWithTimestamp)
            {
                const auto& [topic, partition, timestamp] = tp;
                auto topicPartition = createTopicPartition(topic, partition);
                topicPartition->set_offset(timestamp); // 在查询偏移量时，偏移量字段用于存储时间戳
                topicPartitions.push_back(std::move(topicPartition));
            }

            // 查询时间戳对应的偏移量
            // 创建RdKafka::TopicPartition*数组用于offsetsForTimes API
            std::vector<RdKafka::TopicPartition*> rawTopicPartitions;
            for (const auto& tp : topicPartitions)
            {
                rawTopicPartitions.push_back(tp.get());
            }

            RdKafka::ErrorCode err = _consumer->offsetsForTimes(rawTopicPartitions, 5000);
            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to query offsets for timestamps: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;
                _consumer.reset();
                throw std::runtime_error(errorMsg);
            }

            // 使用找到的偏移量创建TopicPartition用于订阅
            std::vector<std::unique_ptr<RdKafka::TopicPartition>> assignTopicPartitions;
            for (size_t i = 0; i < topicPartitions.size(); ++i)
            {
                const auto& tp = topicPartitions[i];
                int64_t resultOffset = tp->offset();
                if (resultOffset < 0)
                {
                    LOG(WARNING) << "Failed to find offset for timestamp on topic " << tp->topic()
                        << ", partition " << tp->partition()
                        << ", error code: " << resultOffset;
                    continue; // 跳过这个分区
                }

                LOG(INFO) << "Found offset " << resultOffset << " for timestamp on topic "
                    << tp->topic() << ", partition " << tp->partition();

                assignTopicPartitions.push_back(createTopicPartition(tp->topic(), tp->partition(), resultOffset));
            }

            if (assignTopicPartitions.empty())
            {
                std::string errorMsg = "No valid offsets found for any timestamps";
                LOG(ERROR) << errorMsg;
                _consumer.reset();
                throw std::runtime_error(errorMsg);
            }

            // 启动消费者并设置偏移量
            bool result = startConsumerWithPartitions(assignTopicPartitions);

            if (result)
            {
                LOG(INFO) << "DesignKafkaService started from timestamps for "
                    << assignTopicPartitions.size() << " partitions";
            }

            return result;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during Kafka consumer start from timestamps: " << e.what();
            _consumer.reset();
            _running = false;
            return false;
        }
    }

    std::vector<std::tuple<std::string, int32_t, int64_t>>
    DesignKafkaService::getOffsetByTimestamp(int64_t timestamp,
                                             const std::vector<int>& partitions,
                                             int timeout_ms) const
    {
        auto conf = createKafkaConfig(_bootstrapServers, "");
        if (!conf)
        {
            throw std::runtime_error("Failed to create Kafka configuration");
        }
        std::string errorMsg;
        std::unique_ptr<RdKafka::Producer> producer(RdKafka::Producer::create(conf.get(), errorMsg));
        if (producer == nullptr)
        {
            LOG(ERROR) << "Failed to create Kafka producer: " << errorMsg;
            throw std::runtime_error(errorMsg);
        }
        // 创建TopicPartition对象并设置时间戳
        std::vector<std::unique_ptr<RdKafka::TopicPartition>> topicPartitions;
        for (const auto& p : partitions)
        {
            auto topicPartition = createTopicPartition(_topic, p);
            topicPartition->set_offset(timestamp); // 在查询偏移量时，偏移量字段用于存储时间戳
            topicPartitions.push_back(std::move(topicPartition));
        }

        std::vector<RdKafka::TopicPartition*> rawTopicPartitions;
        for (const auto& tp : topicPartitions)
        {
            rawTopicPartitions.push_back(tp.get());
        }

        RdKafka::ErrorCode err = producer->offsetsForTimes(rawTopicPartitions, 5000);
        if (err != RdKafka::ERR_NO_ERROR)
        {
            errorMsg = "Failed to query offsets for timestamps: " +
                std::string(RdKafka::err2str(err)) + " for topic " + _topic;
            LOG(ERROR) << errorMsg;
            throw std::runtime_error(errorMsg);
        }
        std::vector<std::tuple<std::string, int32_t, int64_t>> result;
        for (const auto& tp : topicPartitions)
        {
            int64_t resultOffset = tp->offset();
            if (resultOffset == timestamp) continue;
            result.emplace_back(tp->topic(), tp->partition(), resultOffset);
        }
        return result;
    }

    bool DesignKafkaService::startConsumerWithPartitions(
        const std::vector<std::unique_ptr<RdKafka::TopicPartition>>& topicPartitions)
    {
        try
        {
            // 如果消费者还没有创建，则创建它
            if (!_consumer)
            {
                auto conf = createKafkaConfig(_bootstrapServers, _groupId);
                if (!conf)
                {
                    throw std::runtime_error("Failed to create Kafka configuration");
                }

                std::string errstr;

                // 创建KafkaConsumer
                _consumer.reset(RdKafka::KafkaConsumer::create(conf.get(), errstr));
                if (!_consumer)
                {
                    throw std::runtime_error("Failed to create Kafka consumer: " + errstr);
                }

                // 仅创建KafkaConsumer即可
            }

            // 创建RdKafka::TopicPartition*数组用于assign API
            std::vector<RdKafka::TopicPartition*> rawTopicPartitions;
            for (const auto& tp : topicPartitions)
            {
                rawTopicPartitions.push_back(tp.get());
            }

            // 设置偏移量
            RdKafka::ErrorCode err = _consumer->assign(rawTopicPartitions);
            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to assign partitions: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;
                _consumer.reset();
                throw std::runtime_error(errorMsg);
            }

            // 启动消费者线程
            _running = true;
            _consumerThread = std::thread(&DesignKafkaService::consumerThread, this);

            LOG(INFO) << "DesignKafkaService started with custom partition assignment";
            return true;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during Kafka consumer start with partitions: " << e.what();
            _consumer.reset();
            _running = false;
            return false;
        }
    }

    void DesignKafkaService::stop()
    {
        std::lock_guard<std::mutex> lock(_mutex);

        try
        {
            if (!_running)
            {
                LOG(INFO) << "DesignKafkaService already stopped";
                return; // 已经停止
            }

            // 停止消费者线程
            _running = false;

            // 等待线程结束
            if (_consumerThread.joinable())
            {
                _consumerThread.join();
            }

            // 关闭Kafka消费者
            if (_consumer)
            {
                _consumer->close();
                _consumer.reset();
            }

            LOG(INFO) << "DesignKafkaService stopped";
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during stop: " << e.what();
            // 即使出现异常，也要确保消费者被重置
            _consumer.reset();
            _running = false;
        }
    }

    void DesignKafkaService::shutdown()
    {
        try
        {
            // 先停止服务
            stop();

            std::lock_guard<std::mutex> lock(_mutex);

            // 清空队列
            clearQueue();

            // 重置统计信息
            _consumedMessageCount = 0;
            _failedMessageCount = 0;

            _initialized = false;
            LOG(INFO) << "DesignKafkaService shut down";
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception during shutdown: " << e.what();
            // 即使出现异常，也要确保服务被重置
            _consumer.reset();
            _running = false;
            _initialized = false;
        }
    }

    bool DesignKafkaService::popMessage(KafkaMessageWrapperPtr& message, unsigned long waitMs)
    {
        return _messageQueue.pop(message, waitMs);
    }

    size_t DesignKafkaService::popMessages(std::vector<KafkaMessageWrapperPtr>& messages,
                                           size_t maxMessages,
                                           unsigned long waitMs)
    {
        return _messageQueue.popBatch(messages, maxMessages, waitMs);
    }

    size_t DesignKafkaService::getQueueSize() const
    {
        return _messageQueue.size();
    }

    void DesignKafkaService::clearQueue()
    {
        _messageQueue.clear();
    }

    size_t DesignKafkaService::getConsumedMessageCount() const
    {
        return _consumedMessageCount;
    }

    size_t DesignKafkaService::getFailedMessageCount() const
    {
        return _failedMessageCount;
    }

    std::vector<KafkaPartitionInfo> DesignKafkaService::getTopicPartitionInfo(
        const std::string& topic, int timeout_ms) const
    {
        // std::lock_guard<std::mutex> lock(_mutex);
        std::vector<KafkaPartitionInfo> result;

        try
        {
            std::string errorMsg;
            auto conf = createKafkaConfig(_bootstrapServers, "");
            std::unique_ptr<RdKafka::Producer> producer(RdKafka::Producer::create(conf.get(), errorMsg));
            if (producer == nullptr)
            {
                LOG(ERROR) << "Failed to create Kafka producer: " << errorMsg;
                throw std::runtime_error(errorMsg);
            }
            std::string topicName = topic.empty() ? _topic : topic;

            // 获取主题元数据
            RdKafka::Metadata* metadata = nullptr;
            RdKafka::ErrorCode err = producer->metadata(false, nullptr, &metadata, timeout_ms);

            if (err != RdKafka::ERR_NO_ERROR)
            {
                errorMsg = "Failed to get metadata: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;
                throw std::runtime_error(errorMsg);
            }

            std::unique_ptr<RdKafka::Metadata> metadataPtr(metadata); // 确保自动释放

            // 遍历主题
            const RdKafka::Metadata::TopicMetadataVector* topics = metadataPtr->topics();
            for (const auto& topicMetadata : *topics)
            {
                // 如果指定了主题，只处理该主题
                if (topicMetadata->topic() != topicName)
                    continue;

                // 遍历分区
                const RdKafka::TopicMetadata::PartitionMetadataVector* partitions = topicMetadata->partitions();
                for (const auto& partitionMetadata : *partitions)
                {
                    KafkaPartitionInfo partInfo;
                    partInfo.topic = topicMetadata->topic();
                    partInfo.partition = partitionMetadata->id();
                    partInfo.leader = partitionMetadata->leader();

                    // 获取副本列表
                    const std::vector<int32_t>* replicas = partitionMetadata->replicas();
                    partInfo.replicas.assign(replicas->begin(), replicas->end());

                    // 获取同步副本列表
                    const std::vector<int32_t>* isrs = partitionMetadata->isrs();
                    partInfo.inSyncReplicas.assign(isrs->begin(), isrs->end());

                    // 获取偏移量信息
                    std::unique_ptr<RdKafka::TopicPartition> topicPartition =
                        createTopicPartition(partInfo.topic, partInfo.partition);

                    // 获取最小偏移量
                    std::vector<RdKafka::TopicPartition*> lowOffsetParts = {topicPartition.get()};
                    err = producer->query_watermark_offsets(partInfo.topic, partInfo.partition,
                                                            &partInfo.lowOffset, &partInfo.highOffset,
                                                            timeout_ms);
                    if (err != RdKafka::ERR_NO_ERROR)
                    {
                        LOG(WARNING) << "Failed to query watermark offsets for " << partInfo.topic
                            << " [" << partInfo.partition << "]: " << RdKafka::err2str(err);
                        partInfo.lowOffset = -1;
                        partInfo.highOffset = -1;
                    }

                    // 获取日志起始偏移量
                    // 注意：librdkafka C++ API没有直接的query_log_start_offset方法
                    // 我们使用最小偏移量作为日志起始偏移量
                    partInfo.logStartOffset = partInfo.lowOffset;

                    // 如果最小偏移量无效，设置为-1
                    if (partInfo.logStartOffset < 0)
                    {
                        LOG(WARNING) << "Failed to determine log start offset for " << partInfo.topic
                            << " [" << partInfo.partition << "]";
                        partInfo.logStartOffset = -1;
                    }

                    // 获取最后稳定偏移量
                    partInfo.lastStableOffset = partInfo.highOffset; // 默认使用高水位偏移量

                    result.push_back(partInfo);
                }
            }

            return result;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception in getTopicPartitionInfo: " << e.what();
            return result;
        }
    }

    KafkaConsumerGroupInfo DesignKafkaService::getConsumerGroupInfo(int timeout_ms) const
    {
        std::lock_guard<std::mutex> lock(_mutex);
        KafkaConsumerGroupInfo result;

        try
        {
            // 检查消费者是否创建
            checkConsumerCreated("getConsumerGroupInfo");

            result.groupId = _groupId;

            // 获取消费组元数据
            // 注意：librdkafka的C++客户端没有直接的API来获取消费组信息
            // 这里我们只能提供有限的信息

            // 获取当前分配信息
            std::vector<RdKafka::TopicPartition*> partitions;
            RdKafka::ErrorCode err = _consumer->assignment(partitions);

            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to get assignment: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;
                throw std::runtime_error(errorMsg);
            }

            // 添加分配信息
            for (const auto& tp : partitions)
            {
                result.assignments.push_back(std::make_tuple(tp->topic(), tp->partition(), tp->offset()));
                delete tp; // 释放内存
            }

            // 设置成员数量
            result.memberCount = 1; // 当前只能获取到自己
            result.members.push_back("current-consumer"); // 当前消费者

            // 设置状态
            result.state = "Stable"; // 默认状态

            // 设置协调者
            RdKafka::Metadata* metadata = nullptr;
            err = _consumer->metadata(true, nullptr, &metadata, timeout_ms);

            if (err == RdKafka::ERR_NO_ERROR && metadata)
            {
                std::unique_ptr<RdKafka::Metadata> metadataPtr(metadata);
                const RdKafka::Metadata::BrokerMetadataVector* brokers = metadataPtr->brokers();
                if (!brokers->empty())
                {
                    const RdKafka::BrokerMetadata* broker = brokers->front();
                    result.coordinator = broker->host() + ":" + std::to_string(broker->port());
                }
            }

            return result;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception in getConsumerGroupInfo: " << e.what();
            return result;
        }
    }

    std::vector<std::tuple<std::string, int32_t, int64_t>> DesignKafkaService::getConsumerAssignment(
        int timeout_ms) const
    {
        std::lock_guard<std::mutex> lock(_mutex);
        std::vector<std::tuple<std::string, int32_t, int64_t>> result;

        try
        {
            // 检查消费者是否创建
            checkConsumerCreated("getConsumerAssignment");

            // 获取当前分配信息
            std::vector<RdKafka::TopicPartition*> partitions;
            RdKafka::ErrorCode err = _consumer->assignment(partitions);

            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to get assignment: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;
                throw std::runtime_error(errorMsg);
            }

            // 获取当前位置
            err = _consumer->position(partitions);
            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to get position: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;

                // 清理内存
                for (auto& tp : partitions)
                {
                    delete tp;
                }

                throw std::runtime_error(errorMsg);
            }

            // 添加分配信息
            for (const auto& tp : partitions)
            {
                result.push_back(std::make_tuple(tp->topic(), tp->partition(), tp->offset()));
                delete tp; // 释放内存
            }

            return result;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception in getConsumerAssignment: " << e.what();
            return result;
        }
    }

    std::vector<std::tuple<std::string, int32_t, int64_t>> DesignKafkaService::getCommittedOffsets(int timeout_ms) const
    {
        std::lock_guard<std::mutex> lock(_mutex);
        std::vector<std::tuple<std::string, int32_t, int64_t>> result;

        try
        {
            // 检查消费者是否创建
            checkConsumerCreated("getCommittedOffsets");

            // 首先获取当前分配的分区
            std::vector<RdKafka::TopicPartition*> assignedPartitions;
            RdKafka::ErrorCode err = _consumer->assignment(assignedPartitions);

            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to get assignment: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;
                throw std::runtime_error(errorMsg);
            }

            // 如果没有分配的分区，直接返回空结果
            if (assignedPartitions.empty())
            {
                LOG(INFO) << "No partitions assigned to this consumer";
                // 释放内存
                for (auto& tp : assignedPartitions)
                {
                    delete tp;
                }
                return result;
            }

            // 获取已提交的偏移量
            err = _consumer->committed(assignedPartitions, timeout_ms);
            if (err != RdKafka::ERR_NO_ERROR)
            {
                std::string errorMsg = "Failed to get committed offsets: " +
                    std::string(RdKafka::err2str(err));
                LOG(ERROR) << errorMsg;

                // 释放内存
                for (auto& tp : assignedPartitions)
                {
                    delete tp;
                }

                throw std::runtime_error(errorMsg);
            }

            // 处理结果
            for (const auto& tp : assignedPartitions)
            {
                // 检查是否有错误
                if (tp->err() == RdKafka::ERR_NO_ERROR)
                {
                    // 只添加有效的偏移量（不是特殊偏移量）
                    int64_t offset = tp->offset();
                    if (offset >= 0) // 有效的偏移量
                    {
                        result.push_back(std::make_tuple(tp->topic(), tp->partition(), offset));
                    }
                    else
                    {
                        LOG(INFO) << "Partition " << tp->topic() << " [" << tp->partition()
                            << "] has no committed offset or special offset: " << offset;
                    }
                }
                else
                {
                    LOG(WARNING) << "Failed to get committed offset for " << tp->topic()
                        << " [" << tp->partition() << "]: " << RdKafka::err2str(tp->err());
                }
            }

            // 释放内存
            for (auto& tp : assignedPartitions)
            {
                delete tp;
            }

            return result;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception in getCommittedOffsets: " << e.what();
            return result;
        }
    }

    void DesignKafkaService::consumerThread()
    {
        LOG(INFO) << "Kafka consumer thread started";
        int timeoutMsTotal = 0;
        while (_running)
        {
            try
            {
                int timeoutMs = 100;
                // 使用传统consume方法，因为consume_callback方法实现复杂
                // 从池中获取消息对象或创建新的
                std::unique_ptr<RdKafka::Message> msg(_consumer->consume(timeoutMs));
                // 处理消息
                if (msg->err() == RdKafka::ERR_NO_ERROR)
                {
                    // 处理有效消息
                    if (!processMessage(*msg))
                    {
                        ++_failedMessageCount;
                    }
                    ++_consumedMessageCount;
                }
                else if (msg->err() == RdKafka::ERR__TIMED_OUT)
                {
                    // 超时，继续
                    timeoutMsTotal += timeoutMs;
                    // 10分钟记录一次日志
                    if (timeoutMsTotal > 1000 * 60 * 10)
                    {
                        LOG(INFO) << "Reached partition for " << _topic << " timeout, timeoutMsTotal: " <<
                            timeoutMsTotal;
                        timeoutMsTotal = 0;
                    }
                    continue;
                }
                else if (msg->err() == RdKafka::ERR__PARTITION_EOF)
                {
                    // 到达分区末尾，继续
                    LOG(INFO) << "Reached end of partition for topic "
                        << msg->topic_name() << " [" << msg->partition() << "]";
                    // 等待一小段时间，减少CPU使用率和日志刷屏
                    std::this_thread::sleep_for(std::chrono::milliseconds(50));
                    continue;
                }
                else
                {
                    // 其他错误
                    LOG(WARNING) << "Kafka consumer error: " << msg->errstr();
                    ++_failedMessageCount;

                    // 如果是严重错误，可能需要重新连接
                    if (msg->err() == RdKafka::ERR__TRANSPORT)
                    {
                        LOG(WARNING) << "Transport error, attempting to reconnect...";
                        // 短暂等待后继续
                        std::this_thread::sleep_for(std::chrono::seconds(1));
                    }
                }
            }
            catch (const std::exception& e)
            {
                LOG(WARNING) << "Exception in Kafka consumer thread: " << e.what();
                ++_failedMessageCount;
                // 短暂等待后继续
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
        }

        LOG(INFO) << "Kafka consumer thread stopped";
    }

    bool DesignKafkaService::processMessage(RdKafka::Message& message)
    {
        try
        {
            // 提取消息元数据
            auto wrapper = std::make_shared<KafkaMessageWrapper>();
            wrapper->metadata.topic = message.topic_name();
            wrapper->metadata.partition = message.partition();
            wrapper->metadata.offset = message.offset();
            wrapper->metadata.timestamp = message.timestamp().timestamp;

            // 提取消息键 目前未用到
            // if (message.key())
            // {
            //     wrapper->metadata.key = std::string(static_cast<const char*>(message.key_pointer()),
            //                                message.key_len());
            // }

            // 提取消息内容
            if (message.len() == 0)
            {
                LOG(WARNING) << "Empty message received from topic " << wrapper->metadata.topic;
                return false;
            }

            // 解析消息内容为MessageQueuesPackage
            const char* payload = static_cast<const char*>(message.payload());
            size_t len = message.len();
            wrapper->message.assign(payload, payload + len);
            wrapper->tlv.parse(wrapper->message.data(), wrapper->message.size());
            // LOG(INFO) << wrapper->metadata.topic << "|" << wrapper->metadata.partition << "|" << wrapper->metadata.offset
            //     << "|tlv type: " << wrapper->tlv.getType() << "|" << wrapper->tlv.getLength();


            // 将消息放入队列
            if (!_messageQueue.push(wrapper))
            {
                LOG(ERROR) << "Failed to push message to queue, queue might be full";
                return false;
            }

            return true;
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "Exception while processing Kafka message: " << e.what();
            return false;
        }
    }

    std::unique_ptr<RdKafka::Conf> DesignKafkaService::createKafkaConfig(
        const std::string& bootstrapServers, const std::string& groupId)
    {
        std::string errstr;
        std::unique_ptr<RdKafka::Conf> conf(RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL));

        // 设置Kafka服务器地址
        if (conf->set("bootstrap.servers", bootstrapServers, errstr) != RdKafka::Conf::CONF_OK)
        {
            LOG(ERROR) << "Failed to set bootstrap.servers: " << errstr;
            return nullptr;
        }
        if (groupId.empty())
        {
            // 生产者不需要group相关的设置
            return conf;
        }
        // 设置消费者组ID
        if (conf->set("group.id", groupId, errstr) != RdKafka::Conf::CONF_OK)
        {
            LOG(ERROR) << "Failed to set group.id: " << errstr;
            return nullptr;
        }

        // 设置自动提交
        if (conf->set("enable.auto.commit", "true", errstr) != RdKafka::Conf::CONF_OK)
        {
            LOG(ERROR) << "Failed to set enable.auto.commit: " << errstr;
            return nullptr;
        }

        // 设置自动提交间隔
        if (conf->set("auto.commit.interval.ms", "5000", errstr) != RdKafka::Conf::CONF_OK)
        {
            LOG(ERROR) << "Failed to set auto.commit.interval.ms: " << errstr;
            return nullptr;
        }

        // 设置自动偏移量重置策略（如果没有偏移量，从最早的消息开始消费）
        if (conf->set("auto.offset.reset", "earliest", errstr) != RdKafka::Conf::CONF_OK)
        {
            LOG(ERROR) << "Failed to set auto.offset.reset: " << errstr;
            return nullptr;
        }

        return conf;
    }
} // namespace wiz
