#pragma once

#include <memory>
#include <vector>

#include "queue/FIFOQueue.h"
#include "proto/def.h"
#include "core/node/WDNode.h"
#include "store/IStore.h"
#include "serialize/WDBMProtobufSerialize.h"

// 定义一个类型别名，避免使用不完整类型
typedef std::shared_ptr<WD::WDNode> WDNodePtr;

namespace wiz
{
    /**
     * @brief 一个管理design::NodeTreeAction和design::NodeAttrsRecord队列的单例类
     *
     * 该类创建三个队列：
     * 1. 一个用于design::NodeTreeAction对象的队列
     * 2. 一个用于design::NodeAttrsRecord对象的队列
     * 3. 一个用于DBNodeAttrsRecord对象的队列
     *
     * 其他服务可以通过该单例与队列交互，而不需要直接与DesignQueueService交互。
     */
    class DesignNode2Queue
    {
    public:
        using NodeType = design::NodeType;

        struct DBNodeAttrsRecord
        {
            design::NodeAttrsRecord record;
            std::string uuid;
        };

        /**
         * @brief 获取单例实例
         *
         * @return DesignQueueManager& 单例实例的引用
         */
        static DesignNode2Queue& getInstance();
        // 禁止拷贝和赋值
        DesignNode2Queue(const DesignNode2Queue&) = delete;
        DesignNode2Queue& operator=(const DesignNode2Queue&) = delete;
        /**
         * 初始化
         * @param projectInfo 项目信息
         * @param designBase design WDBMBase
         * @param catalogBase catalog WDBMBase
         * @param maxSize 队列大小 0 表示不限制
         */
        void initialize(const design::ProjectInfo& projectInfo,
                        const CommonStoreSPtr& commonStore,
                        WD::WDBMBase* designBase,
                        WD::WDBMBase* catalogBase, int maxSize);

        WD::WDBMProtobufSerializeInterface& getSerializer(NodeType type) const;
        void setStore(WD::store::IStoreUPtr store);

        WD::store::IStore& getStore() const
        {
            return *_store;
        }

        /**
         * @brief 关闭队列管理器
         */
        void shutdown();

        /**
         * @brief 将NodeTreeAction推送到队列
         *
         * @param action 要推送的NodeTreeAction
         * @param type 节点类型
         * @return true 如果操作成功推送
         * @return false 如果队列已满
         */
        bool pushNodeTreeAction(const design::NodeTreeAction& action, NodeType type);
        bool pushNodeTreeAction(design::NodeTreeAction&& action, NodeType type);

        /**
         * @brief 将多个NodeTreeAction推送到队列
         *
         * @param actions 要推送的NodeTreeAction集合
         * @param type 节点类型
         * @return true 如果所有操作都成功推送
         * @return false 如果队列已满
         */
        bool pushNodeTreeActions(const std::vector<design::NodeTreeAction>& actions, NodeType type);
        bool pushNodeTreeActions(std::vector<design::NodeTreeAction>&& actions, NodeType type);

        /**
         * @brief 将NodeAttrsRecord推送到队列
         *
         * @param record 要推送的NodeAttrsRecord
         * @return true 如果记录成功推送
         * @return false 如果队列已满
         */
        bool pushNodeAttrsRecord(const design::NodeAttrsRecord& record, NodeType type);
        bool pushNodeAttrsRecord(design::NodeAttrsRecord&& record, NodeType type);
        /**
         * @brief 将多个NodeAttrsRecord推送到队列
         *
         * @param records 要推送的NodeAttrsRecord集合
         * @return true 如果所有记录都成功推送
         * @return false 如果队列已满
         */
        bool pushNodeAttrsRecords(const std::vector<design::NodeAttrsRecord>& records, NodeType type);
        bool pushNodeAttrsRecords(std::vector<design::NodeAttrsRecord>&& records, NodeType type);

        /**
         * @brief 获取NodeTreeAction队列的大小
         *
         * @return size_t 队列中的项目数量
         */
        size_t getNodeTreeActionQueueSize(NodeType type) const;

        /**
         * @brief 获取NodeAttrsRecord队列的大小
         *
         * @return size_t 队列中的项目数量
         */
        size_t getNodeAttrsRecordQueueSize(NodeType type) const;

        /**
        * @brief 获取DBNodeAttrsRecord队列的大小
        *
        * @return size_t 队列中的项目数量
        */
        size_t getDBNodeAttrsRecordQueueSize(NodeType type) const;

        /**
         * @brief 清空两个队列
         */
        void clearQueues();

        bool deserializeNode(WDNodePtr& node, NodeType type, const design::NodeAttrsRecord& record,
                             const WD::BASFlags& flags = WD::BASFlag::BASF_All) const;
        bool serNodeAndStoreAsFromQueue(const WDNodePtr& node, NodeType type, int action, const WD::BASFlags& flags,
                                        design::NodeAttrsRecord& record);
        /**
         * @brief 将WDNode节点推送到队列
         *
         * @param node 要推送的WDNode节点
         * @param type 节点类型
         * @param action 节点操作类型 1-notice 通知， 2-checkpoint 持久化
         * @param flags 序列化标志
         * @return true 如果推送成功
         * @return false 如果推送失败
         */
        bool pushNode(const WDNodePtr& node, NodeType type, int action = 2,
                      const WD::BASFlags& flags = WD::BASFlag::BASF_All);

        /**
         * @brief 将多个WDNode节点推送到队列
         *
         * @param nodes 要推送的WDNode节点数组
         * @param type 节点类型
         * @param flags 序列化标志
         * @param action 节点操作类型 1-notice 通知， 2-checkpoint 持久化
         * @return true 如果所有节点都推送成功
         * @return false 如果有任何节点推送失败
         */
        bool pushNodes(const std::vector<WDNodePtr>& nodes, NodeType type, int action = 2,
                       const WD::BASFlags& flags = WD::BASFlag::BASF_All);
        bool pushNodesRecursive(const std::vector<WDNodePtr>& nodes, NodeType type, int action = 2,
                                const WD::BASFlags& flags = WD::BASFlag::BASF_All);
        void setQueueSize(int maxSize);

        /**
         * @brief 从NodeTreeAction队列中弹出批量项目
         *
         * @param actions 用于存储弹出项目的向量
         * @param maxItems 要弹出的最大项目数（0表示所有可用项目）
         * @param waitMs 等待时间（毫秒）
         * @return size_t 弹出的项目数量
         */
        size_t popNodeTreeActions(std::vector<design::NodeTreeAction>& actions, NodeType type,
                                  size_t maxItems = 0, unsigned long waitMs = 0);

        /**
         * @brief 从NodeAttrsRecord队列中弹出批量项目
         *
         * @param records 用于存储弹出项目的向量
         * @param maxItems 要弹出的最大项目数（0表示所有可用项目）
         * @param waitMs 等待时间（毫秒）
         * @return size_t 弹出的项目数量
         */
        size_t popNodeAttrsRecords(std::vector<design::NodeAttrsRecord>& records, NodeType type,
                                   size_t maxItems = 0, unsigned long waitMs = 0);

        /**
        * @brief 从DBNodeAttrsRecord队列中弹出批量项目
        *
        * @param records 用于存储弹出项目的向量
        * @param maxItems 要弹出的最大项目数（0表示所有可用项目）
        * @param waitMs 等待时间（毫秒）
        * @return size_t 弹出的项目数量
        */
        size_t popDBNodeAttrsRecords(std::vector<DBNodeAttrsRecord>& records, NodeType type,
            size_t maxItems = 0, unsigned long waitMs = 0);
        /**
         * @brief 从NodeAttrsRecord中解析uuid
         * @param record
         * @return
         */
        std::string fetchUUID(design::NodeAttrsRecord& record) const;

        void batchFetchUUIDCodes(const std::vector<WDNodePtr>& nodes, NodeType type) const;

    private:
        /**
        * @brief 处理队列中的DBNodeAttrsRecords
        *
        * @param maxItems 要处理的最大项目数（0表示处理所有可用项目）
        */
        void processDBNodeAttrsRecords(NodeType type,size_t maxItems = 0);

    private:
        /**
         * @brief 构造函数（私有，防止直接实例化）
         */
        DesignNode2Queue();

        /**
         * @brief 析构函数
         */
        ~DesignNode2Queue();

        // 队列
        std::vector<FIFOQueue<design::NodeTreeAction>> _nodeTreeActionQueue;
        std::vector<FIFOQueue<design::NodeAttrsRecord>> _nodeAttrsRecordQueue;
        std::vector<FIFOQueue<DBNodeAttrsRecord>> _dbNodeAttrsRecordQueue;

        // 序列化器
        std::vector<WD::SerializeInterfaceUPtr> _serializers;
        // 本地保存
        WD::store::IStoreUPtr _store;
    };
} // namespace wiz
