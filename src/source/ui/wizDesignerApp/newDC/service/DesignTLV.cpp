#include "DesignTLV.h"
#include <cstring>
#include <algorithm>
#include <glog/logging.h>

namespace wiz
{
#define DesignTLV_HEADER_SIZE 4

    DesignTLV::DesignTLV()
        : _version(255), _type(0)
          , _count(0), _module_type(0)
          , _length(0)
          , _data(nullptr)
          , _valid(false)
    {
    }


    bool DesignTLV::parse(const uint8_t* buffer, size_t length)
    {
        // 检查缓冲区是否足够大
        if (buffer == nullptr || length < DesignTLV_HEADER_SIZE) // 至少需要Type(1) + Length(2)
        {
            LOG(ERROR) << "TLV buffer too small or null: " << (buffer == nullptr ? "null" : std::to_string(length));
            _valid = false;
            return false;
        }

        // 解析Type
        _type = buffer[0];
        if (_type == 0)
        {
            // 采用新协议
            _version = buffer[0];
            return true;
        }
        _version = 255; // 防止二次利用出错
        _module_type = buffer[1];
        // 解析Length（2字节，大端序）
        _length = (static_cast<uint16_t>(buffer[DesignTLV_HEADER_SIZE - 2]) << 8) | static_cast<uint16_t>(buffer[
            DesignTLV_HEADER_SIZE - 1]);
        // 检查缓冲区是否足够大
        if (length < DesignTLV_HEADER_SIZE + _length)
        {
            LOG(ERROR) << "TLV buffer too small for value: expected " << (DesignTLV_HEADER_SIZE + _length) <<
                ", but got " << length;
            _valid = false;
            return false;
        }

        // 设置数据指针
        _data = buffer + DesignTLV_HEADER_SIZE;

        _valid = true;
        return true;
    }

    std::vector<std::pair<void*, int>> DesignTLV::parseV0(const uint8_t* buffer, size_t length)
    {
        _valid = false;
        // 检查缓冲区是否足够大
        if (buffer == nullptr || length < DesignTLV_HEADER_SIZE) // v0 恰好也需要4个
        {
            LOG(ERROR) << "TLV buffer too small or null: " << (buffer == nullptr ? "null" : std::to_string(length));
            _valid = false;
            return {};
        }

        // 解析Type
        _version = buffer[0];
        _type = buffer[1];
        if (_type == 0)
        {
            LOG(ERROR) << "TLV type is error," << static_cast<int>(_type);
            return {};
        }
        _module_type = buffer[2];
        _count = buffer[3];
        auto data = buffer + DesignTLV_HEADER_SIZE;
        std::vector<std::pair<void*, int>> result(_count);
        for (int i = 0; i < _count; i++)
        {
            // ReSharper disable once CppCStyleCast
            auto len = static_cast<uint16_t>(data[0] << 8) | static_cast<uint16_t>(data[1]);
            result[i].first = (void*)(data + 2);
            result[i].second = len;
            data += 2 + len;
        }
        _valid = true;
        return result;
    }

    std::vector<DesignTLV> DesignTLV::parseMultiple(const uint8_t* buffer, size_t length)
    {
        std::vector<DesignTLV> result;
        size_t offset = 0;

        while (offset + DesignTLV_HEADER_SIZE <= length) // 至少需要Type(1) + Length(2)
        {
            DesignTLV tlv;
            if (tlv.parse(buffer + offset, length - offset))
            {
                // 移动到下一个TLV
                offset += DesignTLV_HEADER_SIZE + tlv.getLength();
                result.push_back(tlv);
            }
            else
            {
                break;
            }
        }
        return result;
    }

    int DesignTLV::getType() const
    {
        return _type;
    }

    void DesignTLV::setType(uint8_t type)
    {
        _type = type;
        _valid = true;
    }

    uint16_t DesignTLV::getLength() const
    {
        return _length;
    }

    const uint8_t* DesignTLV::getData() const
    {
        return _data;
    }

    void DesignTLV::setData(const uint8_t* data, uint16_t length)
    {
        if (data == nullptr && length > 0)
        {
            LOG(ERROR) << "TLV value data is null but length is " << length;
            _valid = false;
            return;
        }

        _data = data;
        _length = length;
        _valid = true;
    }

    size_t DesignTLV::serialize(uint8_t* buffer, size_t offset) const
    {
        if (buffer == nullptr)
        {
            LOG(ERROR) << "TLV serialization buffer is null";
            return 0;
        }

        // 写入Type
        buffer[offset] = _type;
        buffer[offset + 1] = _module_type;
        // 写入Length（2字节，大端序）
        buffer[offset + 2] = (_length >> 8) & 0xFF; // 高字节
        buffer[offset + 3] = _length & 0xFF; // 低字节

        // 写入Value
        if (_length > 0 && _data != nullptr)
        {
            std::memcpy(buffer + offset + DesignTLV_HEADER_SIZE, _data, _length);
        }

        return DesignTLV_HEADER_SIZE + _length;
    }

    size_t DesignTLV::getTotalLength() const
    {
        return DesignTLV_HEADER_SIZE + _length; // Type(1) + Length(2) + Value
    }

    bool DesignTLV::isValid() const
    {
        return _valid;
    }
} // namespace wiz
