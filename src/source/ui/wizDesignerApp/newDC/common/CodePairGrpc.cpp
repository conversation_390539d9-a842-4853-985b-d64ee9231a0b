//
// Created by everpan on 25-3-29.
//

#include "CodePairGrpc.h"
#include <glog/logging.h>

#include "proto/message2json.h"

int64_t CodePairGrpc::AcquireCode(const string& key)
{
    auto it = _lr_map.find(key);
    if (it != _lr_map.end())
    {
        return it->second;
    }
    auto m = LRMapT{{key, 0}};
    AcquireCodes(m);
    auto code = m[key];
    if (code <= 0)
    {
        LOG(ERROR) << "AcquireCode: " << _project_info.projectcode()
            << "|" << _project_info.classification() << "|" << _project_info.subtopic()
            << " " << key << " failed";
        throw std::runtime_error("AcquireCode: key:" + key + " failed");
    }
    return code;
}

void CodePairGrpc::AcquireCodes(CodePairInterface<string, int64_t>::LRMapT& reqMap)
{
    design::ProjectCodes req, rsp;
    req.mutable_projectinfo()->CopyFrom(_project_info);
    auto& kc_list = *req.mutable_key_code();
    for (const auto& it : reqMap)
    {
        design::KeyCode* kc = req.add_key_code();
        kc->set_key(it.first);
        kc->set_code(it.second);
    }
    grpc::ClientContext context;
    auto resp = _client->AcquireDictionaryCode(&context, req, &rsp);
    if (resp.ok())
    {
        for (const auto& it : rsp.key_code())
        {
            reqMap[it.key()] = it.code();
            // _lr_map[it.key()] = it.code();
            auto r = _lr_map.insert({it.key(), it.code()});
            _rl_map.insert({it.code(), std::cref(r.first->first)});
        }
        return;
    }
    std::string json_str;
    proto_message2json(req, json_str);
    LOG(ERROR) << "AcquireCode: " << json_str << "｜error: "
        << resp.error_code() << " " << resp.error_details();
    throw std::runtime_error("grpc call failed : " + resp.error_details());
}

int64_t CodePairGrpc::GetRightCode(const string& c1) const
{
    const auto it = _lr_map.find(c1);
    if (it != _lr_map.end())
    {
        return it->second;
    }
    // 冷启动下，获取远程id映射
    return const_cast<CodePairGrpc*>(this)->AcquireCode(c1);
}

void CodePairGrpc::Add(const string& c1, const int64_t& code)
{
    // not support
}

void CodePairGrpc::Add(const map<string, int64_t>& mp)
{
    for (auto& [k,v] : mp)
    {
        _lr_map[k] = v;
        const auto& it = _lr_map.find(k);
        _rl_map.insert({v, std::cref(it->first)});
    }
}

const string& CodePairGrpc::GetLeftCode(const int64_t& code) const
{
    auto it = _rl_map.find(code);
    if (it != _rl_map.end())
    {
        return it->second.get();
    }
    static string empty;
    return empty;
}

const CodePairInterface<>::LRMapT& CodePairGrpc::GetLRCodeMap() const
{
    return _lr_map;
}

const CodePairInterface<>::RLMapT& CodePairGrpc::GetRLCodeMap() const
{
    throw std::runtime_error("not support");
}

void CodePairGrpc::clear()
{
    // not support
    throw std::runtime_error("not support");
}

const set<int64_t>& CodePairGrpc::GetNewCodeSet() const
{
    throw std::runtime_error("not support");
}

void CodePairGrpc::ClearNewCodeSet()
{
    // not support
    throw std::runtime_error("not support");
}

void CodePairGrpc::setCommonStore(const CommonStoreSPtr& store)
{
    _common_store = store;
}

void CodePairGrpc::loadFromStore()
{
    auto t = _project_info.subtopic() == "attr_key" ? WD::store::CommonStore::Attrs : WD::store::CommonStore::Type;
    auto m = _common_store->load(t);
    Add(m);
}

void CodePairGrpc::saveToStore() const
{
    auto t = _project_info.subtopic() == "attr_key" ? WD::store::CommonStore::Attrs : WD::store::CommonStore::Type;
    _common_store->save(_lr_map, t);
}

void CodePairGrpc::syncFromRemote()
{
    loadFromStore();
    design::MaxOffset req;
    req.mutable_projectinfo()->CopyFrom(_project_info);
    bool needSave = false;
    do
    {
        auto maxId = _rl_map.empty() ? 0 : _rl_map.rbegin()->first;
        req.set_maxoffset(maxId);
        grpc::ClientContext context;
        design::ProjectCodes rsp;
        auto resp = _client->FetchAllDictionaryCode(&context, req, &rsp);
        if (resp.ok())
        {
            map<string, int64_t> mRet;
            for (const auto& it : rsp.key_code())
            {
                mRet[it.key()] = it.code();
            }
            if (mRet.empty())
            {
                LOG(INFO) << _project_info.classification() << " syncFromRemote finished. code size: " << _rl_map.size()
                    << "," << _lr_map.size();
                break;
            }
            needSave = true;
            LOG(INFO) << "syncFromRemote: " << mRet.size() << " codes; " << req.maxoffset() << "|" << req.projectinfo().
                classification() << "|" << req.projectinfo().subtopic();
            Add(mRet);
        }
        else
        {
            LOG(WARNING) << resp.error_code() << "|" << resp.error_message() << "|" << resp.error_details();
            break;
        }
    }
    while (true);
    if (needSave)
    {
        saveToStore();
    }
}
