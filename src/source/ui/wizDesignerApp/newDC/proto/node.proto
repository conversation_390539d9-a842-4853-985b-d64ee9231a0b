syntax = "proto3";

option java_package = "com.saas.dc3d.proto";

package design;
message NodeBaseValue{
  oneof OneValue {
    bool val_bool = 1;
    int32 val_int = 2;
    int64 val_int64 = 4;
    uint32 val_uint = 5;
    double val_double = 6;
    bytes val_string = 7;
    VectorInt vec_int = 21;
    VectorDouble vec_double = 22;
    VectorString vec_string = 23;
  }
}
message ValInt {
  int32 val_int = 1;
}
message DVec4 {
  double x = 1;
  double y = 2;
  double z = 3;
  double w = 4;
}
message VectorDouble{
  repeated double vec_double = 1 [packed = true];
}
message VectorInt{
  repeated int32 vec_int = 1 [packed = true];
}
message VectorInt64{
  repeated int64 vec_int64 = 1 [packed = true];
}
message SVecInt{
  repeated sint32 vec_sint = 1 [packed = true];
}
message VectorString{
  repeated bytes vec_string = 1;
}
message StringIntegerMap{
  map<string, int64> map = 1;
}
message NodeAttrs {
  map<int64, NodeBaseValue> attr_map = 1; // attr-key attr-value
}
message StrIntRecord{
  int64 id = 1;
  bytes str = 2;
}
message NodeAttrsRecord{
  int64 id = 1;
  bytes name = 2;
  int32 type = 3;
  NodeAttrs attrs = 4;
  int64 traceId = 5; // 追踪id，原值返回
  /**
 * 附加的动作；例如 做返回值用，无特殊含义
 * 1-notice 服务端不保存，作为广播，让所有客户端更新节点的状态（实时互动）
 * 2-checkpoint 服务端保存,生成一个版本； 即：持久化
 */
  int32 action = 6;
  bytes additionalJSON = 7; //备注信息
  AdditionalInfo additionalInfo = 8; // 附加信息
}
message NodeTreeRecord {
  int64 id = 1;
  VectorInt64 children = 2;
  int64 traceId = 3; // 追踪id，原值返回
  bytes additionalJSON = 4; //备注信息
  AdditionalInfo additionalInfo = 5; // 附加信息
}
message AdditionalInfo {
  string user = 1; // 用户账号，该节点创建者
  int64 time = 2; // 时间戳 单位：ms
  int32 checkOut = 3; // 0-签入 1-签出
  string checkOutUser = 4; // 用户账号，签出状态时，签入状态时为空，该字段由服务端维护
  int32 status = 5; // 0-正常 1-删除

}
message OffsetLength {
  int64 offset = 1;
  int32 length = 2;
  uint32 crc = 3;
}
message NodeOffsetRecord{
  int32 node_id = 1;
  OffsetLength off_len = 2;
}

message ProjectInfo {
  string projectCode = 1; // 项目(项目唯一标识)
  string classification = 2; // 分类： design/catalog
  string subTopic = 3; // 子主题/分类:  attr_key/uuid; 编码对象
  string user = 4; // 用户账号
  int32 progressPercentage = 5; // 进度百分比%      -1表示未初始化，100表示已完成初始化导入
   // 0 ： 不处理； -1：初始化导入；  1-5000：表示客户端导入进度；5001-10000：服务端写入minio进度；
}

message ProjectConfigInfo {
  string json = 1; // {"mq":{"type":"kafka","kafka":{"bootstrapServers":"192.168.66.112:9092,192.168.66.113:9092,192.168.66.114:9092","topic":"design-node-{project}"}},"dfs":{"type":"minio","minio":{"endpoint":"devminio01.wiz.top:9000","accessKey":"devuser","secretKey":"devuser@wiz.top2023","bucketName":"dc3dfile","nodeSnapshotBasePath":""//最新一小时的全量数据文件路径}}}
}

message MessageQueuesPackage{
  oneof Record {
    NodeTreeRecord trees = 1; // 树节点发生变化
    NodeAttrsRecord nodes = 2; // 节点数据发生变化
  }
}
message ProjectConfig{
  ProjectInfo projectInfo = 1;
  map<string, string> configs = 2;
}
message KeyCode{
  bytes key = 1;
  int64 code = 2;
}
message ProjectCodes{
  ProjectInfo projectInfo = 1;
  repeated KeyCode key_code = 3;
}
// 控制数量，包大小不超过1M； 大约 5000-8000 个nodes
// 每个node大约200字节
message ProjectNodes{
  ProjectInfo projectInfo = 1;
  repeated NodeAttrsRecord nodes = 2;
  /**
   * 附加的动作；例如 做返回值用，无特殊含义
   * 1-notice 服务端不保存，作为广播，让所有客户端更新节点的状态（实时互动）
   * 2-checkpoint 服务端保存,生成一个版本； 即：持久化
   */
  int32 action = 3;
  // bytes additionalJSON = 4; // 附加信息
  // int64 maxOffset = 5; // 作为返回值时，表示所有节点中最大的offset
}

message ProjectNodesResult{
  bytes code = 1; // 0X00 表示成功，其他表示失败
  string message = 2; // 返回结果信息
}

message ProjectNodesOffset{
  ProjectInfo projectInfo = 1;
  map<uint64, OffsetLength> offsets = 2;
  // map<uint64, MessageId> messageIds = 3;
}
message MaxOffset{
  ProjectInfo projectInfo = 1;
  int32 flag = 2; // 1-node有更新 2-tree层次有更新； request 忽略
  int64 maxOffset = 3;
}
enum TreeActionFlag {
  TAF_NONE = 0;
  TAF_INSERT = 1;
  TAF_DELETE = 2;
  TAF_REVERSE = 4;
  TAF_UPDATE_ALL = 8;
}
message NodeTreeAction{
  int64 parentId = 1;
  TreeActionFlag flag = 2; // 1-insert 2-delete 4-reverse 8-updateAll
  int64 leftSiblingId = 3;// 左节点, 0 - 表示插入最左边
  repeated int64 siblings = 4; // 子节点
}

message NodeTreeActions{
  ProjectInfo projectInfo = 1;
  int64 traceId = 2; // 追踪id，原值返回
  repeated NodeTreeAction actions = 3;
}

message ProjectNodesTree{
  ProjectInfo projectInfo = 1;
  repeated NodeTreeRecord trees = 2;
}

/**
 * 用于minio文件存储数据块；以及MQ消息队列的传输；
 */
message DataBlock{
  ProjectInfo projectInfo = 1;
  int32 flag = 2; // 1-nodeAttrs 2-nodeTree；
  /**
   * 附加的动作；例如 做返回值用，无特殊含义
   * 1-notice 服务端不保存，作为广播，让所有客户端更新节点的状态（实时互动）
   * 2-checkpoint 服务端保存,生成一个版本； 即：持久化
   */
  int32 action = 3;
  MessageQueuesPackage package = 5;
}


// 客户端登录
message UserInfo {
  ProjectInfo projectInfo = 1;
  LoginFlag flag = 2; // 0-获取RSA公钥 1-登录 2-登出
  string user = 3; // 用户名
  string pwd = 4; // type=0时，不需要密码； type=1时，密码需rsa加密
}

// 登录返回结果集
message LoginResponse {
  int32 code = 1;
  string message = 2;
  string result = 3; // token
}
enum LoginFlag {
  PUBLIC_KEY = 0;
  LOGIN = 1;
  LOGIN_OUT = 2;
}