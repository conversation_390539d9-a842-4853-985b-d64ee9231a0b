syntax = "proto3";
option java_package = "com.saas.dc3d.proto";
import "node.proto";
option optimize_for = LITE_RUNTIME;

package design;

service DesignService {
  // 授权登录
  rpc Login (UserInfo) returns (LoginResponse);

  // 签入签出
  rpc CheckInOut (ProjectNodesTree) returns (ProjectNodesTree);

  // 获取项目的一些配置信息，例如 mq 的地址等
  rpc GetConfig(ProjectInfo) returns (ProjectConfigInfo);
  /**
   * 通过此接口，将key编码成number类型
   * 常规用法
   * 1. 对属性 key 进行编码，属性key属于集中高频字段
   * 2. 对uuid进行编码，uuid展开字符串占用字节数比较多 在维护层级关系上可以节省空间；可以加快匹配速度
   *  uuid 属于海量数据，进行分库分区分表
   */
  rpc AcquireDictionaryCode(ProjectCodes) returns (ProjectCodes);

  // 返回所有主题的编码信息；控制包大小，协商一次性返还数量
  rpc FetchAllDictionaryCode(MaxOffset) returns (ProjectCodes);

  // 将nodes保存到服务端，同时返回 offset、len、crc；
  // offset 为逻辑增量，可以作为更新标记，新增节点默认都为签出状态
  rpc UpdateNodes(ProjectNodes) returns (ProjectNodesResult);

  // 更新节点层次关系；所有节点 先保存再更新关系，关系采用映射之后的number维护
  rpc UpdateNodeTree(NodeTreeActions) returns(ProjectNodesResult);

  // 客户端导入项目进度状态
  rpc ImportProjectProgress(ProjectInfo) returns(ProjectInfo);
}

