// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: node.proto
// Protobuf C++ Version: 5.29.3

#ifndef node_2eproto_2epb_2eh
#define node_2eproto_2epb_2eh

#include <limits>
#include <string>
#include <type_traits>
#include <utility>

#include "google/protobuf/runtime_version.h"
#if PROTOBUF_VERSION != 5029003
#error "Protobuf C++ gencode is built with an incompatible version of"
#error "Protobuf C++ headers/runtime. See"
#error "https://protobuf.dev/support/cross-version-runtime-guarantee/#cpp"
#endif
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/arena.h"
#include "google/protobuf/arenastring.h"
#include "google/protobuf/generated_message_tctable_decl.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/metadata_lite.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/message.h"
#include "google/protobuf/message_lite.h"
#include "google/protobuf/repeated_field.h"  // IWYU pragma: export
#include "google/protobuf/extension_set.h"  // IWYU pragma: export
#include "google/protobuf/map.h"  // IWYU pragma: export
#include "google/protobuf/map_entry.h"
#include "google/protobuf/map_field_inl.h"
#include "google/protobuf/generated_enum_reflection.h"
#include "google/protobuf/unknown_field_set.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"

#define PROTOBUF_INTERNAL_EXPORT_node_2eproto

namespace google {
namespace protobuf {
namespace internal {
template <typename T>
::absl::string_view GetAnyMessageName();
}  // namespace internal
}  // namespace protobuf
}  // namespace google

// Internal implementation detail -- do not use these members.
struct TableStruct_node_2eproto {
  static const ::uint32_t offsets[];
};
extern const ::google::protobuf::internal::DescriptorTable
    descriptor_table_node_2eproto;
namespace design {
class AdditionalInfo;
struct AdditionalInfoDefaultTypeInternal;
extern AdditionalInfoDefaultTypeInternal _AdditionalInfo_default_instance_;
class DVec4;
struct DVec4DefaultTypeInternal;
extern DVec4DefaultTypeInternal _DVec4_default_instance_;
class DataBlock;
struct DataBlockDefaultTypeInternal;
extern DataBlockDefaultTypeInternal _DataBlock_default_instance_;
class KeyCode;
struct KeyCodeDefaultTypeInternal;
extern KeyCodeDefaultTypeInternal _KeyCode_default_instance_;
class LoginResponse;
struct LoginResponseDefaultTypeInternal;
extern LoginResponseDefaultTypeInternal _LoginResponse_default_instance_;
class MaxOffset;
struct MaxOffsetDefaultTypeInternal;
extern MaxOffsetDefaultTypeInternal _MaxOffset_default_instance_;
class MessageQueuesPackage;
struct MessageQueuesPackageDefaultTypeInternal;
extern MessageQueuesPackageDefaultTypeInternal _MessageQueuesPackage_default_instance_;
class NodeAttrs;
struct NodeAttrsDefaultTypeInternal;
extern NodeAttrsDefaultTypeInternal _NodeAttrs_default_instance_;
class NodeAttrsRecord;
struct NodeAttrsRecordDefaultTypeInternal;
extern NodeAttrsRecordDefaultTypeInternal _NodeAttrsRecord_default_instance_;
class NodeAttrs_AttrMapEntry_DoNotUse;
struct NodeAttrs_AttrMapEntry_DoNotUseDefaultTypeInternal;
extern NodeAttrs_AttrMapEntry_DoNotUseDefaultTypeInternal _NodeAttrs_AttrMapEntry_DoNotUse_default_instance_;
class NodeBaseValue;
struct NodeBaseValueDefaultTypeInternal;
extern NodeBaseValueDefaultTypeInternal _NodeBaseValue_default_instance_;
class NodeOffsetRecord;
struct NodeOffsetRecordDefaultTypeInternal;
extern NodeOffsetRecordDefaultTypeInternal _NodeOffsetRecord_default_instance_;
class NodeTreeAction;
struct NodeTreeActionDefaultTypeInternal;
extern NodeTreeActionDefaultTypeInternal _NodeTreeAction_default_instance_;
class NodeTreeActions;
struct NodeTreeActionsDefaultTypeInternal;
extern NodeTreeActionsDefaultTypeInternal _NodeTreeActions_default_instance_;
class NodeTreeRecord;
struct NodeTreeRecordDefaultTypeInternal;
extern NodeTreeRecordDefaultTypeInternal _NodeTreeRecord_default_instance_;
class OffsetLength;
struct OffsetLengthDefaultTypeInternal;
extern OffsetLengthDefaultTypeInternal _OffsetLength_default_instance_;
class ProjectCodes;
struct ProjectCodesDefaultTypeInternal;
extern ProjectCodesDefaultTypeInternal _ProjectCodes_default_instance_;
class ProjectConfig;
struct ProjectConfigDefaultTypeInternal;
extern ProjectConfigDefaultTypeInternal _ProjectConfig_default_instance_;
class ProjectConfigInfo;
struct ProjectConfigInfoDefaultTypeInternal;
extern ProjectConfigInfoDefaultTypeInternal _ProjectConfigInfo_default_instance_;
class ProjectConfig_ConfigsEntry_DoNotUse;
struct ProjectConfig_ConfigsEntry_DoNotUseDefaultTypeInternal;
extern ProjectConfig_ConfigsEntry_DoNotUseDefaultTypeInternal _ProjectConfig_ConfigsEntry_DoNotUse_default_instance_;
class ProjectInfo;
struct ProjectInfoDefaultTypeInternal;
extern ProjectInfoDefaultTypeInternal _ProjectInfo_default_instance_;
class ProjectNodes;
struct ProjectNodesDefaultTypeInternal;
extern ProjectNodesDefaultTypeInternal _ProjectNodes_default_instance_;
class ProjectNodesOffset;
struct ProjectNodesOffsetDefaultTypeInternal;
extern ProjectNodesOffsetDefaultTypeInternal _ProjectNodesOffset_default_instance_;
class ProjectNodesOffset_OffsetsEntry_DoNotUse;
struct ProjectNodesOffset_OffsetsEntry_DoNotUseDefaultTypeInternal;
extern ProjectNodesOffset_OffsetsEntry_DoNotUseDefaultTypeInternal _ProjectNodesOffset_OffsetsEntry_DoNotUse_default_instance_;
class ProjectNodesResult;
struct ProjectNodesResultDefaultTypeInternal;
extern ProjectNodesResultDefaultTypeInternal _ProjectNodesResult_default_instance_;
class ProjectNodesTree;
struct ProjectNodesTreeDefaultTypeInternal;
extern ProjectNodesTreeDefaultTypeInternal _ProjectNodesTree_default_instance_;
class SVecInt;
struct SVecIntDefaultTypeInternal;
extern SVecIntDefaultTypeInternal _SVecInt_default_instance_;
class StrIntRecord;
struct StrIntRecordDefaultTypeInternal;
extern StrIntRecordDefaultTypeInternal _StrIntRecord_default_instance_;
class StringIntegerMap;
struct StringIntegerMapDefaultTypeInternal;
extern StringIntegerMapDefaultTypeInternal _StringIntegerMap_default_instance_;
class StringIntegerMap_MapEntry_DoNotUse;
struct StringIntegerMap_MapEntry_DoNotUseDefaultTypeInternal;
extern StringIntegerMap_MapEntry_DoNotUseDefaultTypeInternal _StringIntegerMap_MapEntry_DoNotUse_default_instance_;
class UserInfo;
struct UserInfoDefaultTypeInternal;
extern UserInfoDefaultTypeInternal _UserInfo_default_instance_;
class ValInt;
struct ValIntDefaultTypeInternal;
extern ValIntDefaultTypeInternal _ValInt_default_instance_;
class VectorDouble;
struct VectorDoubleDefaultTypeInternal;
extern VectorDoubleDefaultTypeInternal _VectorDouble_default_instance_;
class VectorInt;
struct VectorIntDefaultTypeInternal;
extern VectorIntDefaultTypeInternal _VectorInt_default_instance_;
class VectorInt64;
struct VectorInt64DefaultTypeInternal;
extern VectorInt64DefaultTypeInternal _VectorInt64_default_instance_;
class VectorString;
struct VectorStringDefaultTypeInternal;
extern VectorStringDefaultTypeInternal _VectorString_default_instance_;
}  // namespace design
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google

namespace design {
enum TreeActionFlag : int {
  TAF_NONE = 0,
  TAF_INSERT = 1,
  TAF_DELETE = 2,
  TAF_REVERSE = 4,
  TAF_UPDATE_ALL = 8,
  TreeActionFlag_INT_MIN_SENTINEL_DO_NOT_USE_ =
      std::numeric_limits<::int32_t>::min(),
  TreeActionFlag_INT_MAX_SENTINEL_DO_NOT_USE_ =
      std::numeric_limits<::int32_t>::max(),
};

bool TreeActionFlag_IsValid(int value);
extern const uint32_t TreeActionFlag_internal_data_[];
constexpr TreeActionFlag TreeActionFlag_MIN = static_cast<TreeActionFlag>(0);
constexpr TreeActionFlag TreeActionFlag_MAX = static_cast<TreeActionFlag>(8);
constexpr int TreeActionFlag_ARRAYSIZE = 8 + 1;
const ::google::protobuf::EnumDescriptor*
TreeActionFlag_descriptor();
template <typename T>
const std::string& TreeActionFlag_Name(T value) {
  static_assert(std::is_same<T, TreeActionFlag>::value ||
                    std::is_integral<T>::value,
                "Incorrect type passed to TreeActionFlag_Name().");
  return TreeActionFlag_Name(static_cast<TreeActionFlag>(value));
}
template <>
inline const std::string& TreeActionFlag_Name(TreeActionFlag value) {
  return ::google::protobuf::internal::NameOfDenseEnum<TreeActionFlag_descriptor,
                                                 0, 8>(
      static_cast<int>(value));
}
inline bool TreeActionFlag_Parse(absl::string_view name, TreeActionFlag* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TreeActionFlag>(
      TreeActionFlag_descriptor(), name, value);
}
enum LoginFlag : int {
  PUBLIC_KEY = 0,
  LOGIN = 1,
  LOGIN_OUT = 2,
  LoginFlag_INT_MIN_SENTINEL_DO_NOT_USE_ =
      std::numeric_limits<::int32_t>::min(),
  LoginFlag_INT_MAX_SENTINEL_DO_NOT_USE_ =
      std::numeric_limits<::int32_t>::max(),
};

bool LoginFlag_IsValid(int value);
extern const uint32_t LoginFlag_internal_data_[];
constexpr LoginFlag LoginFlag_MIN = static_cast<LoginFlag>(0);
constexpr LoginFlag LoginFlag_MAX = static_cast<LoginFlag>(2);
constexpr int LoginFlag_ARRAYSIZE = 2 + 1;
const ::google::protobuf::EnumDescriptor*
LoginFlag_descriptor();
template <typename T>
const std::string& LoginFlag_Name(T value) {
  static_assert(std::is_same<T, LoginFlag>::value ||
                    std::is_integral<T>::value,
                "Incorrect type passed to LoginFlag_Name().");
  return LoginFlag_Name(static_cast<LoginFlag>(value));
}
template <>
inline const std::string& LoginFlag_Name(LoginFlag value) {
  return ::google::protobuf::internal::NameOfDenseEnum<LoginFlag_descriptor,
                                                 0, 2>(
      static_cast<int>(value));
}
inline bool LoginFlag_Parse(absl::string_view name, LoginFlag* value) {
  return ::google::protobuf::internal::ParseNamedEnum<LoginFlag>(
      LoginFlag_descriptor(), name, value);
}

// ===================================================================


// -------------------------------------------------------------------

class VectorString final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.VectorString) */ {
 public:
  inline VectorString() : VectorString(nullptr) {}
  ~VectorString() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(VectorString* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(VectorString));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR VectorString(
      ::google::protobuf::internal::ConstantInitialized);

  inline VectorString(const VectorString& from) : VectorString(nullptr, from) {}
  inline VectorString(VectorString&& from) noexcept
      : VectorString(nullptr, std::move(from)) {}
  inline VectorString& operator=(const VectorString& from) {
    CopyFrom(from);
    return *this;
  }
  inline VectorString& operator=(VectorString&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VectorString& default_instance() {
    return *internal_default_instance();
  }
  static inline const VectorString* internal_default_instance() {
    return reinterpret_cast<const VectorString*>(
        &_VectorString_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 7;
  friend void swap(VectorString& a, VectorString& b) { a.Swap(&b); }
  inline void Swap(VectorString* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VectorString* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VectorString* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<VectorString>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const VectorString& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const VectorString& from) { VectorString::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(VectorString* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.VectorString"; }

 protected:
  explicit VectorString(::google::protobuf::Arena* arena);
  VectorString(::google::protobuf::Arena* arena, const VectorString& from);
  VectorString(::google::protobuf::Arena* arena, VectorString&& from) noexcept
      : VectorString(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kVecStringFieldNumber = 1,
  };
  // repeated bytes vec_string = 1;
  int vec_string_size() const;
  private:
  int _internal_vec_string_size() const;

  public:
  void clear_vec_string() ;
  const std::string& vec_string(int index) const;
  std::string* mutable_vec_string(int index);
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_vec_string(int index, Arg_&& value, Args_... args);
  std::string* add_vec_string();
  template <typename Arg_ = const std::string&, typename... Args_>
  void add_vec_string(Arg_&& value, Args_... args);
  const ::google::protobuf::RepeatedPtrField<std::string>& vec_string() const;
  ::google::protobuf::RepeatedPtrField<std::string>* mutable_vec_string();

  private:
  const ::google::protobuf::RepeatedPtrField<std::string>& _internal_vec_string() const;
  ::google::protobuf::RepeatedPtrField<std::string>* _internal_mutable_vec_string();

  public:
  // @@protoc_insertion_point(class_scope:design.VectorString)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const VectorString& from_msg);
    ::google::protobuf::RepeatedPtrField<std::string> vec_string_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class VectorInt64 final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.VectorInt64) */ {
 public:
  inline VectorInt64() : VectorInt64(nullptr) {}
  ~VectorInt64() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(VectorInt64* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(VectorInt64));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR VectorInt64(
      ::google::protobuf::internal::ConstantInitialized);

  inline VectorInt64(const VectorInt64& from) : VectorInt64(nullptr, from) {}
  inline VectorInt64(VectorInt64&& from) noexcept
      : VectorInt64(nullptr, std::move(from)) {}
  inline VectorInt64& operator=(const VectorInt64& from) {
    CopyFrom(from);
    return *this;
  }
  inline VectorInt64& operator=(VectorInt64&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VectorInt64& default_instance() {
    return *internal_default_instance();
  }
  static inline const VectorInt64* internal_default_instance() {
    return reinterpret_cast<const VectorInt64*>(
        &_VectorInt64_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 5;
  friend void swap(VectorInt64& a, VectorInt64& b) { a.Swap(&b); }
  inline void Swap(VectorInt64* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VectorInt64* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VectorInt64* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<VectorInt64>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const VectorInt64& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const VectorInt64& from) { VectorInt64::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(VectorInt64* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.VectorInt64"; }

 protected:
  explicit VectorInt64(::google::protobuf::Arena* arena);
  VectorInt64(::google::protobuf::Arena* arena, const VectorInt64& from);
  VectorInt64(::google::protobuf::Arena* arena, VectorInt64&& from) noexcept
      : VectorInt64(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kVecInt64FieldNumber = 1,
  };
  // repeated int64 vec_int64 = 1 [packed = true];
  int vec_int64_size() const;
  private:
  int _internal_vec_int64_size() const;

  public:
  void clear_vec_int64() ;
  ::int64_t vec_int64(int index) const;
  void set_vec_int64(int index, ::int64_t value);
  void add_vec_int64(::int64_t value);
  const ::google::protobuf::RepeatedField<::int64_t>& vec_int64() const;
  ::google::protobuf::RepeatedField<::int64_t>* mutable_vec_int64();

  private:
  const ::google::protobuf::RepeatedField<::int64_t>& _internal_vec_int64() const;
  ::google::protobuf::RepeatedField<::int64_t>* _internal_mutable_vec_int64();

  public:
  // @@protoc_insertion_point(class_scope:design.VectorInt64)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const VectorInt64& from_msg);
    ::google::protobuf::RepeatedField<::int64_t> vec_int64_;
    ::google::protobuf::internal::CachedSize _vec_int64_cached_byte_size_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class VectorInt final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.VectorInt) */ {
 public:
  inline VectorInt() : VectorInt(nullptr) {}
  ~VectorInt() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(VectorInt* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(VectorInt));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR VectorInt(
      ::google::protobuf::internal::ConstantInitialized);

  inline VectorInt(const VectorInt& from) : VectorInt(nullptr, from) {}
  inline VectorInt(VectorInt&& from) noexcept
      : VectorInt(nullptr, std::move(from)) {}
  inline VectorInt& operator=(const VectorInt& from) {
    CopyFrom(from);
    return *this;
  }
  inline VectorInt& operator=(VectorInt&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VectorInt& default_instance() {
    return *internal_default_instance();
  }
  static inline const VectorInt* internal_default_instance() {
    return reinterpret_cast<const VectorInt*>(
        &_VectorInt_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 4;
  friend void swap(VectorInt& a, VectorInt& b) { a.Swap(&b); }
  inline void Swap(VectorInt* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VectorInt* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VectorInt* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<VectorInt>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const VectorInt& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const VectorInt& from) { VectorInt::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(VectorInt* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.VectorInt"; }

 protected:
  explicit VectorInt(::google::protobuf::Arena* arena);
  VectorInt(::google::protobuf::Arena* arena, const VectorInt& from);
  VectorInt(::google::protobuf::Arena* arena, VectorInt&& from) noexcept
      : VectorInt(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kVecIntFieldNumber = 1,
  };
  // repeated int32 vec_int = 1 [packed = true];
  int vec_int_size() const;
  private:
  int _internal_vec_int_size() const;

  public:
  void clear_vec_int() ;
  ::int32_t vec_int(int index) const;
  void set_vec_int(int index, ::int32_t value);
  void add_vec_int(::int32_t value);
  const ::google::protobuf::RepeatedField<::int32_t>& vec_int() const;
  ::google::protobuf::RepeatedField<::int32_t>* mutable_vec_int();

  private:
  const ::google::protobuf::RepeatedField<::int32_t>& _internal_vec_int() const;
  ::google::protobuf::RepeatedField<::int32_t>* _internal_mutable_vec_int();

  public:
  // @@protoc_insertion_point(class_scope:design.VectorInt)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const VectorInt& from_msg);
    ::google::protobuf::RepeatedField<::int32_t> vec_int_;
    ::google::protobuf::internal::CachedSize _vec_int_cached_byte_size_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class VectorDouble final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.VectorDouble) */ {
 public:
  inline VectorDouble() : VectorDouble(nullptr) {}
  ~VectorDouble() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(VectorDouble* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(VectorDouble));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR VectorDouble(
      ::google::protobuf::internal::ConstantInitialized);

  inline VectorDouble(const VectorDouble& from) : VectorDouble(nullptr, from) {}
  inline VectorDouble(VectorDouble&& from) noexcept
      : VectorDouble(nullptr, std::move(from)) {}
  inline VectorDouble& operator=(const VectorDouble& from) {
    CopyFrom(from);
    return *this;
  }
  inline VectorDouble& operator=(VectorDouble&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VectorDouble& default_instance() {
    return *internal_default_instance();
  }
  static inline const VectorDouble* internal_default_instance() {
    return reinterpret_cast<const VectorDouble*>(
        &_VectorDouble_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 3;
  friend void swap(VectorDouble& a, VectorDouble& b) { a.Swap(&b); }
  inline void Swap(VectorDouble* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VectorDouble* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VectorDouble* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<VectorDouble>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const VectorDouble& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const VectorDouble& from) { VectorDouble::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(VectorDouble* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.VectorDouble"; }

 protected:
  explicit VectorDouble(::google::protobuf::Arena* arena);
  VectorDouble(::google::protobuf::Arena* arena, const VectorDouble& from);
  VectorDouble(::google::protobuf::Arena* arena, VectorDouble&& from) noexcept
      : VectorDouble(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kVecDoubleFieldNumber = 1,
  };
  // repeated double vec_double = 1 [packed = true];
  int vec_double_size() const;
  private:
  int _internal_vec_double_size() const;

  public:
  void clear_vec_double() ;
  double vec_double(int index) const;
  void set_vec_double(int index, double value);
  void add_vec_double(double value);
  const ::google::protobuf::RepeatedField<double>& vec_double() const;
  ::google::protobuf::RepeatedField<double>* mutable_vec_double();

  private:
  const ::google::protobuf::RepeatedField<double>& _internal_vec_double() const;
  ::google::protobuf::RepeatedField<double>* _internal_mutable_vec_double();

  public:
  // @@protoc_insertion_point(class_scope:design.VectorDouble)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const VectorDouble& from_msg);
    ::google::protobuf::RepeatedField<double> vec_double_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ValInt final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ValInt) */ {
 public:
  inline ValInt() : ValInt(nullptr) {}
  ~ValInt() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ValInt* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ValInt));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ValInt(
      ::google::protobuf::internal::ConstantInitialized);

  inline ValInt(const ValInt& from) : ValInt(nullptr, from) {}
  inline ValInt(ValInt&& from) noexcept
      : ValInt(nullptr, std::move(from)) {}
  inline ValInt& operator=(const ValInt& from) {
    CopyFrom(from);
    return *this;
  }
  inline ValInt& operator=(ValInt&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ValInt& default_instance() {
    return *internal_default_instance();
  }
  static inline const ValInt* internal_default_instance() {
    return reinterpret_cast<const ValInt*>(
        &_ValInt_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 1;
  friend void swap(ValInt& a, ValInt& b) { a.Swap(&b); }
  inline void Swap(ValInt* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ValInt* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ValInt* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ValInt>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ValInt& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ValInt& from) { ValInt::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ValInt* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ValInt"; }

 protected:
  explicit ValInt(::google::protobuf::Arena* arena);
  ValInt(::google::protobuf::Arena* arena, const ValInt& from);
  ValInt(::google::protobuf::Arena* arena, ValInt&& from) noexcept
      : ValInt(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kValIntFieldNumber = 1,
  };
  // int32 val_int = 1;
  void clear_val_int() ;
  ::int32_t val_int() const;
  void set_val_int(::int32_t value);

  private:
  ::int32_t _internal_val_int() const;
  void _internal_set_val_int(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.ValInt)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ValInt& from_msg);
    ::int32_t val_int_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class StringIntegerMap_MapEntry_DoNotUse final
    : public ::google::protobuf::internal::MapEntry<
          std::string, ::int64_t,
          ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
          ::google::protobuf::internal::WireFormatLite::TYPE_INT64> {
 public:
  using SuperType = ::google::protobuf::internal::MapEntry<
      std::string, ::int64_t,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64>;
  StringIntegerMap_MapEntry_DoNotUse();
  template <typename = void>
  explicit PROTOBUF_CONSTEXPR StringIntegerMap_MapEntry_DoNotUse(
      ::google::protobuf::internal::ConstantInitialized);
  explicit StringIntegerMap_MapEntry_DoNotUse(::google::protobuf::Arena* arena);
  static const StringIntegerMap_MapEntry_DoNotUse* internal_default_instance() {
    return reinterpret_cast<const StringIntegerMap_MapEntry_DoNotUse*>(
        &_StringIntegerMap_MapEntry_DoNotUse_default_instance_);
  }


 private:
  friend class ::google::protobuf::MessageLite;
  friend struct ::TableStruct_node_2eproto;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 0,
      44, 2>
      _table_;

  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;
};
// -------------------------------------------------------------------

class StrIntRecord final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.StrIntRecord) */ {
 public:
  inline StrIntRecord() : StrIntRecord(nullptr) {}
  ~StrIntRecord() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(StrIntRecord* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(StrIntRecord));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR StrIntRecord(
      ::google::protobuf::internal::ConstantInitialized);

  inline StrIntRecord(const StrIntRecord& from) : StrIntRecord(nullptr, from) {}
  inline StrIntRecord(StrIntRecord&& from) noexcept
      : StrIntRecord(nullptr, std::move(from)) {}
  inline StrIntRecord& operator=(const StrIntRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline StrIntRecord& operator=(StrIntRecord&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StrIntRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const StrIntRecord* internal_default_instance() {
    return reinterpret_cast<const StrIntRecord*>(
        &_StrIntRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 12;
  friend void swap(StrIntRecord& a, StrIntRecord& b) { a.Swap(&b); }
  inline void Swap(StrIntRecord* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StrIntRecord* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StrIntRecord* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<StrIntRecord>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const StrIntRecord& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const StrIntRecord& from) { StrIntRecord::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(StrIntRecord* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.StrIntRecord"; }

 protected:
  explicit StrIntRecord(::google::protobuf::Arena* arena);
  StrIntRecord(::google::protobuf::Arena* arena, const StrIntRecord& from);
  StrIntRecord(::google::protobuf::Arena* arena, StrIntRecord&& from) noexcept
      : StrIntRecord(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kStrFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // bytes str = 2;
  void clear_str() ;
  const std::string& str() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_str(Arg_&& arg, Args_... args);
  std::string* mutable_str();
  PROTOBUF_NODISCARD std::string* release_str();
  void set_allocated_str(std::string* value);

  private:
  const std::string& _internal_str() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_str(
      const std::string& value);
  std::string* _internal_mutable_str();

  public:
  // int64 id = 1;
  void clear_id() ;
  ::int64_t id() const;
  void set_id(::int64_t value);

  private:
  ::int64_t _internal_id() const;
  void _internal_set_id(::int64_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.StrIntRecord)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const StrIntRecord& from_msg);
    ::google::protobuf::internal::ArenaStringPtr str_;
    ::int64_t id_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class SVecInt final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.SVecInt) */ {
 public:
  inline SVecInt() : SVecInt(nullptr) {}
  ~SVecInt() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(SVecInt* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(SVecInt));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR SVecInt(
      ::google::protobuf::internal::ConstantInitialized);

  inline SVecInt(const SVecInt& from) : SVecInt(nullptr, from) {}
  inline SVecInt(SVecInt&& from) noexcept
      : SVecInt(nullptr, std::move(from)) {}
  inline SVecInt& operator=(const SVecInt& from) {
    CopyFrom(from);
    return *this;
  }
  inline SVecInt& operator=(SVecInt&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SVecInt& default_instance() {
    return *internal_default_instance();
  }
  static inline const SVecInt* internal_default_instance() {
    return reinterpret_cast<const SVecInt*>(
        &_SVecInt_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 6;
  friend void swap(SVecInt& a, SVecInt& b) { a.Swap(&b); }
  inline void Swap(SVecInt* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SVecInt* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SVecInt* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<SVecInt>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const SVecInt& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const SVecInt& from) { SVecInt::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(SVecInt* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.SVecInt"; }

 protected:
  explicit SVecInt(::google::protobuf::Arena* arena);
  SVecInt(::google::protobuf::Arena* arena, const SVecInt& from);
  SVecInt(::google::protobuf::Arena* arena, SVecInt&& from) noexcept
      : SVecInt(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kVecSintFieldNumber = 1,
  };
  // repeated sint32 vec_sint = 1 [packed = true];
  int vec_sint_size() const;
  private:
  int _internal_vec_sint_size() const;

  public:
  void clear_vec_sint() ;
  ::int32_t vec_sint(int index) const;
  void set_vec_sint(int index, ::int32_t value);
  void add_vec_sint(::int32_t value);
  const ::google::protobuf::RepeatedField<::int32_t>& vec_sint() const;
  ::google::protobuf::RepeatedField<::int32_t>* mutable_vec_sint();

  private:
  const ::google::protobuf::RepeatedField<::int32_t>& _internal_vec_sint() const;
  ::google::protobuf::RepeatedField<::int32_t>* _internal_mutable_vec_sint();

  public:
  // @@protoc_insertion_point(class_scope:design.SVecInt)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const SVecInt& from_msg);
    ::google::protobuf::RepeatedField<::int32_t> vec_sint_;
    ::google::protobuf::internal::CachedSize _vec_sint_cached_byte_size_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectNodesResult final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectNodesResult) */ {
 public:
  inline ProjectNodesResult() : ProjectNodesResult(nullptr) {}
  ~ProjectNodesResult() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectNodesResult* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectNodesResult));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectNodesResult(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectNodesResult(const ProjectNodesResult& from) : ProjectNodesResult(nullptr, from) {}
  inline ProjectNodesResult(ProjectNodesResult&& from) noexcept
      : ProjectNodesResult(nullptr, std::move(from)) {}
  inline ProjectNodesResult& operator=(const ProjectNodesResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectNodesResult& operator=(ProjectNodesResult&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectNodesResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectNodesResult* internal_default_instance() {
    return reinterpret_cast<const ProjectNodesResult*>(
        &_ProjectNodesResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 26;
  friend void swap(ProjectNodesResult& a, ProjectNodesResult& b) { a.Swap(&b); }
  inline void Swap(ProjectNodesResult* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectNodesResult* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectNodesResult* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectNodesResult>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectNodesResult& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectNodesResult& from) { ProjectNodesResult::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectNodesResult* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectNodesResult"; }

 protected:
  explicit ProjectNodesResult(::google::protobuf::Arena* arena);
  ProjectNodesResult(::google::protobuf::Arena* arena, const ProjectNodesResult& from);
  ProjectNodesResult(::google::protobuf::Arena* arena, ProjectNodesResult&& from) noexcept
      : ProjectNodesResult(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kCodeFieldNumber = 1,
    kMessageFieldNumber = 2,
  };
  // bytes code = 1;
  void clear_code() ;
  const std::string& code() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_code(Arg_&& arg, Args_... args);
  std::string* mutable_code();
  PROTOBUF_NODISCARD std::string* release_code();
  void set_allocated_code(std::string* value);

  private:
  const std::string& _internal_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_code(
      const std::string& value);
  std::string* _internal_mutable_code();

  public:
  // string message = 2;
  void clear_message() ;
  const std::string& message() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_message(Arg_&& arg, Args_... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* value);

  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(
      const std::string& value);
  std::string* _internal_mutable_message();

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectNodesResult)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 0,
      41, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectNodesResult& from_msg);
    ::google::protobuf::internal::ArenaStringPtr code_;
    ::google::protobuf::internal::ArenaStringPtr message_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectInfo final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectInfo) */ {
 public:
  inline ProjectInfo() : ProjectInfo(nullptr) {}
  ~ProjectInfo() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectInfo* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectInfo));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectInfo(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectInfo(const ProjectInfo& from) : ProjectInfo(nullptr, from) {}
  inline ProjectInfo(ProjectInfo&& from) noexcept
      : ProjectInfo(nullptr, std::move(from)) {}
  inline ProjectInfo& operator=(const ProjectInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectInfo& operator=(ProjectInfo&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectInfo* internal_default_instance() {
    return reinterpret_cast<const ProjectInfo*>(
        &_ProjectInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 18;
  friend void swap(ProjectInfo& a, ProjectInfo& b) { a.Swap(&b); }
  inline void Swap(ProjectInfo* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectInfo* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectInfo* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectInfo>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectInfo& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectInfo& from) { ProjectInfo::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectInfo* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectInfo"; }

 protected:
  explicit ProjectInfo(::google::protobuf::Arena* arena);
  ProjectInfo(::google::protobuf::Arena* arena, const ProjectInfo& from);
  ProjectInfo(::google::protobuf::Arena* arena, ProjectInfo&& from) noexcept
      : ProjectInfo(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kProjectCodeFieldNumber = 1,
    kClassificationFieldNumber = 2,
    kSubTopicFieldNumber = 3,
    kUserFieldNumber = 4,
    kProgressPercentageFieldNumber = 5,
  };
  // string projectCode = 1;
  void clear_projectcode() ;
  const std::string& projectcode() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_projectcode(Arg_&& arg, Args_... args);
  std::string* mutable_projectcode();
  PROTOBUF_NODISCARD std::string* release_projectcode();
  void set_allocated_projectcode(std::string* value);

  private:
  const std::string& _internal_projectcode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_projectcode(
      const std::string& value);
  std::string* _internal_mutable_projectcode();

  public:
  // string classification = 2;
  void clear_classification() ;
  const std::string& classification() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_classification(Arg_&& arg, Args_... args);
  std::string* mutable_classification();
  PROTOBUF_NODISCARD std::string* release_classification();
  void set_allocated_classification(std::string* value);

  private:
  const std::string& _internal_classification() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_classification(
      const std::string& value);
  std::string* _internal_mutable_classification();

  public:
  // string subTopic = 3;
  void clear_subtopic() ;
  const std::string& subtopic() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_subtopic(Arg_&& arg, Args_... args);
  std::string* mutable_subtopic();
  PROTOBUF_NODISCARD std::string* release_subtopic();
  void set_allocated_subtopic(std::string* value);

  private:
  const std::string& _internal_subtopic() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_subtopic(
      const std::string& value);
  std::string* _internal_mutable_subtopic();

  public:
  // string user = 4;
  void clear_user() ;
  const std::string& user() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_user(Arg_&& arg, Args_... args);
  std::string* mutable_user();
  PROTOBUF_NODISCARD std::string* release_user();
  void set_allocated_user(std::string* value);

  private:
  const std::string& _internal_user() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_user(
      const std::string& value);
  std::string* _internal_mutable_user();

  public:
  // int32 progressPercentage = 5;
  void clear_progresspercentage() ;
  ::int32_t progresspercentage() const;
  void set_progresspercentage(::int32_t value);

  private:
  ::int32_t _internal_progresspercentage() const;
  void _internal_set_progresspercentage(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectInfo)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 5, 0,
      64, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectInfo& from_msg);
    ::google::protobuf::internal::ArenaStringPtr projectcode_;
    ::google::protobuf::internal::ArenaStringPtr classification_;
    ::google::protobuf::internal::ArenaStringPtr subtopic_;
    ::google::protobuf::internal::ArenaStringPtr user_;
    ::int32_t progresspercentage_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectConfigInfo final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectConfigInfo) */ {
 public:
  inline ProjectConfigInfo() : ProjectConfigInfo(nullptr) {}
  ~ProjectConfigInfo() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectConfigInfo* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectConfigInfo));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectConfigInfo(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectConfigInfo(const ProjectConfigInfo& from) : ProjectConfigInfo(nullptr, from) {}
  inline ProjectConfigInfo(ProjectConfigInfo&& from) noexcept
      : ProjectConfigInfo(nullptr, std::move(from)) {}
  inline ProjectConfigInfo& operator=(const ProjectConfigInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectConfigInfo& operator=(ProjectConfigInfo&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectConfigInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectConfigInfo* internal_default_instance() {
    return reinterpret_cast<const ProjectConfigInfo*>(
        &_ProjectConfigInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 19;
  friend void swap(ProjectConfigInfo& a, ProjectConfigInfo& b) { a.Swap(&b); }
  inline void Swap(ProjectConfigInfo* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectConfigInfo* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectConfigInfo* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectConfigInfo>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectConfigInfo& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectConfigInfo& from) { ProjectConfigInfo::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectConfigInfo* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectConfigInfo"; }

 protected:
  explicit ProjectConfigInfo(::google::protobuf::Arena* arena);
  ProjectConfigInfo(::google::protobuf::Arena* arena, const ProjectConfigInfo& from);
  ProjectConfigInfo(::google::protobuf::Arena* arena, ProjectConfigInfo&& from) noexcept
      : ProjectConfigInfo(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kJsonFieldNumber = 1,
  };
  // string json = 1;
  void clear_json() ;
  const std::string& json() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_json(Arg_&& arg, Args_... args);
  std::string* mutable_json();
  PROTOBUF_NODISCARD std::string* release_json();
  void set_allocated_json(std::string* value);

  private:
  const std::string& _internal_json() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_json(
      const std::string& value);
  std::string* _internal_mutable_json();

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectConfigInfo)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 0,
      37, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectConfigInfo& from_msg);
    ::google::protobuf::internal::ArenaStringPtr json_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectConfig_ConfigsEntry_DoNotUse final
    : public ::google::protobuf::internal::MapEntry<
          std::string, std::string,
          ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
          ::google::protobuf::internal::WireFormatLite::TYPE_STRING> {
 public:
  using SuperType = ::google::protobuf::internal::MapEntry<
      std::string, std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING>;
  ProjectConfig_ConfigsEntry_DoNotUse();
  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectConfig_ConfigsEntry_DoNotUse(
      ::google::protobuf::internal::ConstantInitialized);
  explicit ProjectConfig_ConfigsEntry_DoNotUse(::google::protobuf::Arena* arena);
  static const ProjectConfig_ConfigsEntry_DoNotUse* internal_default_instance() {
    return reinterpret_cast<const ProjectConfig_ConfigsEntry_DoNotUse*>(
        &_ProjectConfig_ConfigsEntry_DoNotUse_default_instance_);
  }


 private:
  friend class ::google::protobuf::MessageLite;
  friend struct ::TableStruct_node_2eproto;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 0,
      50, 2>
      _table_;

  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;
};
// -------------------------------------------------------------------

class OffsetLength final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.OffsetLength) */ {
 public:
  inline OffsetLength() : OffsetLength(nullptr) {}
  ~OffsetLength() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(OffsetLength* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(OffsetLength));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR OffsetLength(
      ::google::protobuf::internal::ConstantInitialized);

  inline OffsetLength(const OffsetLength& from) : OffsetLength(nullptr, from) {}
  inline OffsetLength(OffsetLength&& from) noexcept
      : OffsetLength(nullptr, std::move(from)) {}
  inline OffsetLength& operator=(const OffsetLength& from) {
    CopyFrom(from);
    return *this;
  }
  inline OffsetLength& operator=(OffsetLength&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OffsetLength& default_instance() {
    return *internal_default_instance();
  }
  static inline const OffsetLength* internal_default_instance() {
    return reinterpret_cast<const OffsetLength*>(
        &_OffsetLength_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 16;
  friend void swap(OffsetLength& a, OffsetLength& b) { a.Swap(&b); }
  inline void Swap(OffsetLength* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OffsetLength* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OffsetLength* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<OffsetLength>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const OffsetLength& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const OffsetLength& from) { OffsetLength::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(OffsetLength* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.OffsetLength"; }

 protected:
  explicit OffsetLength(::google::protobuf::Arena* arena);
  OffsetLength(::google::protobuf::Arena* arena, const OffsetLength& from);
  OffsetLength(::google::protobuf::Arena* arena, OffsetLength&& from) noexcept
      : OffsetLength(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kOffsetFieldNumber = 1,
    kLengthFieldNumber = 2,
    kCrcFieldNumber = 3,
  };
  // int64 offset = 1;
  void clear_offset() ;
  ::int64_t offset() const;
  void set_offset(::int64_t value);

  private:
  ::int64_t _internal_offset() const;
  void _internal_set_offset(::int64_t value);

  public:
  // int32 length = 2;
  void clear_length() ;
  ::int32_t length() const;
  void set_length(::int32_t value);

  private:
  ::int32_t _internal_length() const;
  void _internal_set_length(::int32_t value);

  public:
  // uint32 crc = 3;
  void clear_crc() ;
  ::uint32_t crc() const;
  void set_crc(::uint32_t value);

  private:
  ::uint32_t _internal_crc() const;
  void _internal_set_crc(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.OffsetLength)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const OffsetLength& from_msg);
    ::int64_t offset_;
    ::int32_t length_;
    ::uint32_t crc_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class NodeTreeAction final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.NodeTreeAction) */ {
 public:
  inline NodeTreeAction() : NodeTreeAction(nullptr) {}
  ~NodeTreeAction() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(NodeTreeAction* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(NodeTreeAction));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeTreeAction(
      ::google::protobuf::internal::ConstantInitialized);

  inline NodeTreeAction(const NodeTreeAction& from) : NodeTreeAction(nullptr, from) {}
  inline NodeTreeAction(NodeTreeAction&& from) noexcept
      : NodeTreeAction(nullptr, std::move(from)) {}
  inline NodeTreeAction& operator=(const NodeTreeAction& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeTreeAction& operator=(NodeTreeAction&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeTreeAction& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeTreeAction* internal_default_instance() {
    return reinterpret_cast<const NodeTreeAction*>(
        &_NodeTreeAction_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 30;
  friend void swap(NodeTreeAction& a, NodeTreeAction& b) { a.Swap(&b); }
  inline void Swap(NodeTreeAction* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeTreeAction* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeTreeAction* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<NodeTreeAction>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const NodeTreeAction& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const NodeTreeAction& from) { NodeTreeAction::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(NodeTreeAction* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.NodeTreeAction"; }

 protected:
  explicit NodeTreeAction(::google::protobuf::Arena* arena);
  NodeTreeAction(::google::protobuf::Arena* arena, const NodeTreeAction& from);
  NodeTreeAction(::google::protobuf::Arena* arena, NodeTreeAction&& from) noexcept
      : NodeTreeAction(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kSiblingsFieldNumber = 4,
    kParentIdFieldNumber = 1,
    kLeftSiblingIdFieldNumber = 3,
    kFlagFieldNumber = 2,
  };
  // repeated int64 siblings = 4;
  int siblings_size() const;
  private:
  int _internal_siblings_size() const;

  public:
  void clear_siblings() ;
  ::int64_t siblings(int index) const;
  void set_siblings(int index, ::int64_t value);
  void add_siblings(::int64_t value);
  const ::google::protobuf::RepeatedField<::int64_t>& siblings() const;
  ::google::protobuf::RepeatedField<::int64_t>* mutable_siblings();

  private:
  const ::google::protobuf::RepeatedField<::int64_t>& _internal_siblings() const;
  ::google::protobuf::RepeatedField<::int64_t>* _internal_mutable_siblings();

  public:
  // int64 parentId = 1;
  void clear_parentid() ;
  ::int64_t parentid() const;
  void set_parentid(::int64_t value);

  private:
  ::int64_t _internal_parentid() const;
  void _internal_set_parentid(::int64_t value);

  public:
  // int64 leftSiblingId = 3;
  void clear_leftsiblingid() ;
  ::int64_t leftsiblingid() const;
  void set_leftsiblingid(::int64_t value);

  private:
  ::int64_t _internal_leftsiblingid() const;
  void _internal_set_leftsiblingid(::int64_t value);

  public:
  // .design.TreeActionFlag flag = 2;
  void clear_flag() ;
  ::design::TreeActionFlag flag() const;
  void set_flag(::design::TreeActionFlag value);

  private:
  ::design::TreeActionFlag _internal_flag() const;
  void _internal_set_flag(::design::TreeActionFlag value);

  public:
  // @@protoc_insertion_point(class_scope:design.NodeTreeAction)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const NodeTreeAction& from_msg);
    ::google::protobuf::RepeatedField<::int64_t> siblings_;
    ::google::protobuf::internal::CachedSize _siblings_cached_byte_size_;
    ::int64_t parentid_;
    ::int64_t leftsiblingid_;
    int flag_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class LoginResponse final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.LoginResponse) */ {
 public:
  inline LoginResponse() : LoginResponse(nullptr) {}
  ~LoginResponse() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(LoginResponse* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(LoginResponse));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR LoginResponse(
      ::google::protobuf::internal::ConstantInitialized);

  inline LoginResponse(const LoginResponse& from) : LoginResponse(nullptr, from) {}
  inline LoginResponse(LoginResponse&& from) noexcept
      : LoginResponse(nullptr, std::move(from)) {}
  inline LoginResponse& operator=(const LoginResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoginResponse& operator=(LoginResponse&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoginResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoginResponse* internal_default_instance() {
    return reinterpret_cast<const LoginResponse*>(
        &_LoginResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 35;
  friend void swap(LoginResponse& a, LoginResponse& b) { a.Swap(&b); }
  inline void Swap(LoginResponse* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoginResponse* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoginResponse* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<LoginResponse>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const LoginResponse& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const LoginResponse& from) { LoginResponse::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(LoginResponse* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.LoginResponse"; }

 protected:
  explicit LoginResponse(::google::protobuf::Arena* arena);
  LoginResponse(::google::protobuf::Arena* arena, const LoginResponse& from);
  LoginResponse(::google::protobuf::Arena* arena, LoginResponse&& from) noexcept
      : LoginResponse(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kMessageFieldNumber = 2,
    kResultFieldNumber = 3,
    kCodeFieldNumber = 1,
  };
  // string message = 2;
  void clear_message() ;
  const std::string& message() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_message(Arg_&& arg, Args_... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* value);

  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(
      const std::string& value);
  std::string* _internal_mutable_message();

  public:
  // string result = 3;
  void clear_result() ;
  const std::string& result() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_result(Arg_&& arg, Args_... args);
  std::string* mutable_result();
  PROTOBUF_NODISCARD std::string* release_result();
  void set_allocated_result(std::string* value);

  private:
  const std::string& _internal_result() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_result(
      const std::string& value);
  std::string* _internal_mutable_result();

  public:
  // int32 code = 1;
  void clear_code() ;
  ::int32_t code() const;
  void set_code(::int32_t value);

  private:
  ::int32_t _internal_code() const;
  void _internal_set_code(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.LoginResponse)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 0,
      42, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const LoginResponse& from_msg);
    ::google::protobuf::internal::ArenaStringPtr message_;
    ::google::protobuf::internal::ArenaStringPtr result_;
    ::int32_t code_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class KeyCode final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.KeyCode) */ {
 public:
  inline KeyCode() : KeyCode(nullptr) {}
  ~KeyCode() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(KeyCode* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(KeyCode));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR KeyCode(
      ::google::protobuf::internal::ConstantInitialized);

  inline KeyCode(const KeyCode& from) : KeyCode(nullptr, from) {}
  inline KeyCode(KeyCode&& from) noexcept
      : KeyCode(nullptr, std::move(from)) {}
  inline KeyCode& operator=(const KeyCode& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeyCode& operator=(KeyCode&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KeyCode& default_instance() {
    return *internal_default_instance();
  }
  static inline const KeyCode* internal_default_instance() {
    return reinterpret_cast<const KeyCode*>(
        &_KeyCode_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 23;
  friend void swap(KeyCode& a, KeyCode& b) { a.Swap(&b); }
  inline void Swap(KeyCode* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KeyCode* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KeyCode* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<KeyCode>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const KeyCode& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const KeyCode& from) { KeyCode::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(KeyCode* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.KeyCode"; }

 protected:
  explicit KeyCode(::google::protobuf::Arena* arena);
  KeyCode(::google::protobuf::Arena* arena, const KeyCode& from);
  KeyCode(::google::protobuf::Arena* arena, KeyCode&& from) noexcept
      : KeyCode(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kKeyFieldNumber = 1,
    kCodeFieldNumber = 2,
  };
  // bytes key = 1;
  void clear_key() ;
  const std::string& key() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_key(Arg_&& arg, Args_... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* value);

  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(
      const std::string& value);
  std::string* _internal_mutable_key();

  public:
  // int64 code = 2;
  void clear_code() ;
  ::int64_t code() const;
  void set_code(::int64_t value);

  private:
  ::int64_t _internal_code() const;
  void _internal_set_code(::int64_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.KeyCode)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const KeyCode& from_msg);
    ::google::protobuf::internal::ArenaStringPtr key_;
    ::int64_t code_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class DVec4 final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.DVec4) */ {
 public:
  inline DVec4() : DVec4(nullptr) {}
  ~DVec4() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(DVec4* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(DVec4));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR DVec4(
      ::google::protobuf::internal::ConstantInitialized);

  inline DVec4(const DVec4& from) : DVec4(nullptr, from) {}
  inline DVec4(DVec4&& from) noexcept
      : DVec4(nullptr, std::move(from)) {}
  inline DVec4& operator=(const DVec4& from) {
    CopyFrom(from);
    return *this;
  }
  inline DVec4& operator=(DVec4&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DVec4& default_instance() {
    return *internal_default_instance();
  }
  static inline const DVec4* internal_default_instance() {
    return reinterpret_cast<const DVec4*>(
        &_DVec4_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 2;
  friend void swap(DVec4& a, DVec4& b) { a.Swap(&b); }
  inline void Swap(DVec4* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DVec4* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DVec4* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<DVec4>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const DVec4& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const DVec4& from) { DVec4::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(DVec4* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.DVec4"; }

 protected:
  explicit DVec4(::google::protobuf::Arena* arena);
  DVec4(::google::protobuf::Arena* arena, const DVec4& from);
  DVec4(::google::protobuf::Arena* arena, DVec4&& from) noexcept
      : DVec4(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
    kWFieldNumber = 4,
  };
  // double x = 1;
  void clear_x() ;
  double x() const;
  void set_x(double value);

  private:
  double _internal_x() const;
  void _internal_set_x(double value);

  public:
  // double y = 2;
  void clear_y() ;
  double y() const;
  void set_y(double value);

  private:
  double _internal_y() const;
  void _internal_set_y(double value);

  public:
  // double z = 3;
  void clear_z() ;
  double z() const;
  void set_z(double value);

  private:
  double _internal_z() const;
  void _internal_set_z(double value);

  public:
  // double w = 4;
  void clear_w() ;
  double w() const;
  void set_w(double value);

  private:
  double _internal_w() const;
  void _internal_set_w(double value);

  public:
  // @@protoc_insertion_point(class_scope:design.DVec4)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const DVec4& from_msg);
    double x_;
    double y_;
    double z_;
    double w_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class AdditionalInfo final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.AdditionalInfo) */ {
 public:
  inline AdditionalInfo() : AdditionalInfo(nullptr) {}
  ~AdditionalInfo() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(AdditionalInfo* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(AdditionalInfo));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR AdditionalInfo(
      ::google::protobuf::internal::ConstantInitialized);

  inline AdditionalInfo(const AdditionalInfo& from) : AdditionalInfo(nullptr, from) {}
  inline AdditionalInfo(AdditionalInfo&& from) noexcept
      : AdditionalInfo(nullptr, std::move(from)) {}
  inline AdditionalInfo& operator=(const AdditionalInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdditionalInfo& operator=(AdditionalInfo&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdditionalInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdditionalInfo* internal_default_instance() {
    return reinterpret_cast<const AdditionalInfo*>(
        &_AdditionalInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 15;
  friend void swap(AdditionalInfo& a, AdditionalInfo& b) { a.Swap(&b); }
  inline void Swap(AdditionalInfo* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdditionalInfo* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdditionalInfo* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<AdditionalInfo>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const AdditionalInfo& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const AdditionalInfo& from) { AdditionalInfo::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(AdditionalInfo* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.AdditionalInfo"; }

 protected:
  explicit AdditionalInfo(::google::protobuf::Arena* arena);
  AdditionalInfo(::google::protobuf::Arena* arena, const AdditionalInfo& from);
  AdditionalInfo(::google::protobuf::Arena* arena, AdditionalInfo&& from) noexcept
      : AdditionalInfo(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kUserFieldNumber = 1,
    kCheckOutUserFieldNumber = 4,
    kTimeFieldNumber = 2,
    kCheckOutFieldNumber = 3,
    kStatusFieldNumber = 5,
  };
  // string user = 1;
  void clear_user() ;
  const std::string& user() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_user(Arg_&& arg, Args_... args);
  std::string* mutable_user();
  PROTOBUF_NODISCARD std::string* release_user();
  void set_allocated_user(std::string* value);

  private:
  const std::string& _internal_user() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_user(
      const std::string& value);
  std::string* _internal_mutable_user();

  public:
  // string checkOutUser = 4;
  void clear_checkoutuser() ;
  const std::string& checkoutuser() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_checkoutuser(Arg_&& arg, Args_... args);
  std::string* mutable_checkoutuser();
  PROTOBUF_NODISCARD std::string* release_checkoutuser();
  void set_allocated_checkoutuser(std::string* value);

  private:
  const std::string& _internal_checkoutuser() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_checkoutuser(
      const std::string& value);
  std::string* _internal_mutable_checkoutuser();

  public:
  // int64 time = 2;
  void clear_time() ;
  ::int64_t time() const;
  void set_time(::int64_t value);

  private:
  ::int64_t _internal_time() const;
  void _internal_set_time(::int64_t value);

  public:
  // int32 checkOut = 3;
  void clear_checkout() ;
  ::int32_t checkout() const;
  void set_checkout(::int32_t value);

  private:
  ::int32_t _internal_checkout() const;
  void _internal_set_checkout(::int32_t value);

  public:
  // int32 status = 5;
  void clear_status() ;
  ::int32_t status() const;
  void set_status(::int32_t value);

  private:
  ::int32_t _internal_status() const;
  void _internal_set_status(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.AdditionalInfo)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 5, 0,
      46, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const AdditionalInfo& from_msg);
    ::google::protobuf::internal::ArenaStringPtr user_;
    ::google::protobuf::internal::ArenaStringPtr checkoutuser_;
    ::int64_t time_;
    ::int32_t checkout_;
    ::int32_t status_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class UserInfo final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.UserInfo) */ {
 public:
  inline UserInfo() : UserInfo(nullptr) {}
  ~UserInfo() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(UserInfo* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(UserInfo));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR UserInfo(
      ::google::protobuf::internal::ConstantInitialized);

  inline UserInfo(const UserInfo& from) : UserInfo(nullptr, from) {}
  inline UserInfo(UserInfo&& from) noexcept
      : UserInfo(nullptr, std::move(from)) {}
  inline UserInfo& operator=(const UserInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline UserInfo& operator=(UserInfo&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UserInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const UserInfo* internal_default_instance() {
    return reinterpret_cast<const UserInfo*>(
        &_UserInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 34;
  friend void swap(UserInfo& a, UserInfo& b) { a.Swap(&b); }
  inline void Swap(UserInfo* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UserInfo* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UserInfo* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<UserInfo>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const UserInfo& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const UserInfo& from) { UserInfo::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(UserInfo* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.UserInfo"; }

 protected:
  explicit UserInfo(::google::protobuf::Arena* arena);
  UserInfo(::google::protobuf::Arena* arena, const UserInfo& from);
  UserInfo(::google::protobuf::Arena* arena, UserInfo&& from) noexcept
      : UserInfo(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kUserFieldNumber = 3,
    kPwdFieldNumber = 4,
    kProjectInfoFieldNumber = 1,
    kFlagFieldNumber = 2,
  };
  // string user = 3;
  void clear_user() ;
  const std::string& user() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_user(Arg_&& arg, Args_... args);
  std::string* mutable_user();
  PROTOBUF_NODISCARD std::string* release_user();
  void set_allocated_user(std::string* value);

  private:
  const std::string& _internal_user() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_user(
      const std::string& value);
  std::string* _internal_mutable_user();

  public:
  // string pwd = 4;
  void clear_pwd() ;
  const std::string& pwd() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_pwd(Arg_&& arg, Args_... args);
  std::string* mutable_pwd();
  PROTOBUF_NODISCARD std::string* release_pwd();
  void set_allocated_pwd(std::string* value);

  private:
  const std::string& _internal_pwd() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pwd(
      const std::string& value);
  std::string* _internal_mutable_pwd();

  public:
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // .design.LoginFlag flag = 2;
  void clear_flag() ;
  ::design::LoginFlag flag() const;
  void set_flag(::design::LoginFlag value);

  private:
  ::design::LoginFlag _internal_flag() const;
  void _internal_set_flag(::design::LoginFlag value);

  public:
  // @@protoc_insertion_point(class_scope:design.UserInfo)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 1,
      31, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const UserInfo& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::ArenaStringPtr user_;
    ::google::protobuf::internal::ArenaStringPtr pwd_;
    ::design::ProjectInfo* projectinfo_;
    int flag_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class StringIntegerMap final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.StringIntegerMap) */ {
 public:
  inline StringIntegerMap() : StringIntegerMap(nullptr) {}
  ~StringIntegerMap() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(StringIntegerMap* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(StringIntegerMap));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR StringIntegerMap(
      ::google::protobuf::internal::ConstantInitialized);

  inline StringIntegerMap(const StringIntegerMap& from) : StringIntegerMap(nullptr, from) {}
  inline StringIntegerMap(StringIntegerMap&& from) noexcept
      : StringIntegerMap(nullptr, std::move(from)) {}
  inline StringIntegerMap& operator=(const StringIntegerMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline StringIntegerMap& operator=(StringIntegerMap&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StringIntegerMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const StringIntegerMap* internal_default_instance() {
    return reinterpret_cast<const StringIntegerMap*>(
        &_StringIntegerMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 9;
  friend void swap(StringIntegerMap& a, StringIntegerMap& b) { a.Swap(&b); }
  inline void Swap(StringIntegerMap* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StringIntegerMap* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StringIntegerMap* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<StringIntegerMap>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const StringIntegerMap& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const StringIntegerMap& from) { StringIntegerMap::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(StringIntegerMap* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.StringIntegerMap"; }

 protected:
  explicit StringIntegerMap(::google::protobuf::Arena* arena);
  StringIntegerMap(::google::protobuf::Arena* arena, const StringIntegerMap& from);
  StringIntegerMap(::google::protobuf::Arena* arena, StringIntegerMap&& from) noexcept
      : StringIntegerMap(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kMapFieldNumber = 1,
  };
  // map<string, int64> map = 1;
  int map_size() const;
  private:
  int _internal_map_size() const;

  public:
  void clear_map() ;
  const ::google::protobuf::Map<std::string, ::int64_t>& map() const;
  ::google::protobuf::Map<std::string, ::int64_t>* mutable_map();

  private:
  const ::google::protobuf::Map<std::string, ::int64_t>& _internal_map() const;
  ::google::protobuf::Map<std::string, ::int64_t>* _internal_mutable_map();

  public:
  // @@protoc_insertion_point(class_scope:design.StringIntegerMap)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 1,
      35, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const StringIntegerMap& from_msg);
    ::google::protobuf::internal::MapField<StringIntegerMap_MapEntry_DoNotUse, std::string, ::int64_t,
                      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
                      ::google::protobuf::internal::WireFormatLite::TYPE_INT64>
        map_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectNodesOffset_OffsetsEntry_DoNotUse final
    : public ::google::protobuf::internal::MapEntry<
          ::uint64_t, ::google::protobuf::Message,
          ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
          ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE> {
 public:
  using SuperType = ::google::protobuf::internal::MapEntry<
      ::uint64_t, ::google::protobuf::Message,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE>;
  ProjectNodesOffset_OffsetsEntry_DoNotUse();
  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectNodesOffset_OffsetsEntry_DoNotUse(
      ::google::protobuf::internal::ConstantInitialized);
  explicit ProjectNodesOffset_OffsetsEntry_DoNotUse(::google::protobuf::Arena* arena);
  static const ProjectNodesOffset_OffsetsEntry_DoNotUse* internal_default_instance() {
    return reinterpret_cast<const ProjectNodesOffset_OffsetsEntry_DoNotUse*>(
        &_ProjectNodesOffset_OffsetsEntry_DoNotUse_default_instance_);
  }


 private:
  friend class ::google::protobuf::MessageLite;
  friend struct ::TableStruct_node_2eproto;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 1,
      0, 2>
      _table_;

  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;
};
// -------------------------------------------------------------------

class ProjectConfig final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectConfig) */ {
 public:
  inline ProjectConfig() : ProjectConfig(nullptr) {}
  ~ProjectConfig() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectConfig* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectConfig));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectConfig(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectConfig(const ProjectConfig& from) : ProjectConfig(nullptr, from) {}
  inline ProjectConfig(ProjectConfig&& from) noexcept
      : ProjectConfig(nullptr, std::move(from)) {}
  inline ProjectConfig& operator=(const ProjectConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectConfig& operator=(ProjectConfig&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectConfig* internal_default_instance() {
    return reinterpret_cast<const ProjectConfig*>(
        &_ProjectConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 22;
  friend void swap(ProjectConfig& a, ProjectConfig& b) { a.Swap(&b); }
  inline void Swap(ProjectConfig* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectConfig* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectConfig* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectConfig>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectConfig& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectConfig& from) { ProjectConfig::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectConfig* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectConfig"; }

 protected:
  explicit ProjectConfig(::google::protobuf::Arena* arena);
  ProjectConfig(::google::protobuf::Arena* arena, const ProjectConfig& from);
  ProjectConfig(::google::protobuf::Arena* arena, ProjectConfig&& from) noexcept
      : ProjectConfig(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kConfigsFieldNumber = 2,
    kProjectInfoFieldNumber = 1,
  };
  // map<string, string> configs = 2;
  int configs_size() const;
  private:
  int _internal_configs_size() const;

  public:
  void clear_configs() ;
  const ::google::protobuf::Map<std::string, std::string>& configs() const;
  ::google::protobuf::Map<std::string, std::string>* mutable_configs();

  private:
  const ::google::protobuf::Map<std::string, std::string>& _internal_configs() const;
  ::google::protobuf::Map<std::string, std::string>* _internal_mutable_configs();

  public:
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectConfig)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 2, 2,
      36, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectConfig& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::MapField<ProjectConfig_ConfigsEntry_DoNotUse, std::string, std::string,
                      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
                      ::google::protobuf::internal::WireFormatLite::TYPE_STRING>
        configs_;
    ::design::ProjectInfo* projectinfo_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectCodes final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectCodes) */ {
 public:
  inline ProjectCodes() : ProjectCodes(nullptr) {}
  ~ProjectCodes() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectCodes* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectCodes));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectCodes(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectCodes(const ProjectCodes& from) : ProjectCodes(nullptr, from) {}
  inline ProjectCodes(ProjectCodes&& from) noexcept
      : ProjectCodes(nullptr, std::move(from)) {}
  inline ProjectCodes& operator=(const ProjectCodes& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectCodes& operator=(ProjectCodes&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectCodes& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectCodes* internal_default_instance() {
    return reinterpret_cast<const ProjectCodes*>(
        &_ProjectCodes_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 24;
  friend void swap(ProjectCodes& a, ProjectCodes& b) { a.Swap(&b); }
  inline void Swap(ProjectCodes* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectCodes* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectCodes* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectCodes>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectCodes& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectCodes& from) { ProjectCodes::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectCodes* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectCodes"; }

 protected:
  explicit ProjectCodes(::google::protobuf::Arena* arena);
  ProjectCodes(::google::protobuf::Arena* arena, const ProjectCodes& from);
  ProjectCodes(::google::protobuf::Arena* arena, ProjectCodes&& from) noexcept
      : ProjectCodes(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kKeyCodeFieldNumber = 3,
    kProjectInfoFieldNumber = 1,
  };
  // repeated .design.KeyCode key_code = 3;
  int key_code_size() const;
  private:
  int _internal_key_code_size() const;

  public:
  void clear_key_code() ;
  ::design::KeyCode* mutable_key_code(int index);
  ::google::protobuf::RepeatedPtrField<::design::KeyCode>* mutable_key_code();

  private:
  const ::google::protobuf::RepeatedPtrField<::design::KeyCode>& _internal_key_code() const;
  ::google::protobuf::RepeatedPtrField<::design::KeyCode>* _internal_mutable_key_code();
  public:
  const ::design::KeyCode& key_code(int index) const;
  ::design::KeyCode* add_key_code();
  const ::google::protobuf::RepeatedPtrField<::design::KeyCode>& key_code() const;
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectCodes)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 2, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectCodes& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::design::KeyCode > key_code_;
    ::design::ProjectInfo* projectinfo_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class NodeTreeRecord final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.NodeTreeRecord) */ {
 public:
  inline NodeTreeRecord() : NodeTreeRecord(nullptr) {}
  ~NodeTreeRecord() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(NodeTreeRecord* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(NodeTreeRecord));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeTreeRecord(
      ::google::protobuf::internal::ConstantInitialized);

  inline NodeTreeRecord(const NodeTreeRecord& from) : NodeTreeRecord(nullptr, from) {}
  inline NodeTreeRecord(NodeTreeRecord&& from) noexcept
      : NodeTreeRecord(nullptr, std::move(from)) {}
  inline NodeTreeRecord& operator=(const NodeTreeRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeTreeRecord& operator=(NodeTreeRecord&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeTreeRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeTreeRecord* internal_default_instance() {
    return reinterpret_cast<const NodeTreeRecord*>(
        &_NodeTreeRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 14;
  friend void swap(NodeTreeRecord& a, NodeTreeRecord& b) { a.Swap(&b); }
  inline void Swap(NodeTreeRecord* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeTreeRecord* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeTreeRecord* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<NodeTreeRecord>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const NodeTreeRecord& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const NodeTreeRecord& from) { NodeTreeRecord::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(NodeTreeRecord* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.NodeTreeRecord"; }

 protected:
  explicit NodeTreeRecord(::google::protobuf::Arena* arena);
  NodeTreeRecord(::google::protobuf::Arena* arena, const NodeTreeRecord& from);
  NodeTreeRecord(::google::protobuf::Arena* arena, NodeTreeRecord&& from) noexcept
      : NodeTreeRecord(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kAdditionalJSONFieldNumber = 4,
    kChildrenFieldNumber = 2,
    kAdditionalInfoFieldNumber = 5,
    kIdFieldNumber = 1,
    kTraceIdFieldNumber = 3,
  };
  // bytes additionalJSON = 4;
  void clear_additionaljson() ;
  const std::string& additionaljson() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_additionaljson(Arg_&& arg, Args_... args);
  std::string* mutable_additionaljson();
  PROTOBUF_NODISCARD std::string* release_additionaljson();
  void set_allocated_additionaljson(std::string* value);

  private:
  const std::string& _internal_additionaljson() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_additionaljson(
      const std::string& value);
  std::string* _internal_mutable_additionaljson();

  public:
  // .design.VectorInt64 children = 2;
  bool has_children() const;
  void clear_children() ;
  const ::design::VectorInt64& children() const;
  PROTOBUF_NODISCARD ::design::VectorInt64* release_children();
  ::design::VectorInt64* mutable_children();
  void set_allocated_children(::design::VectorInt64* value);
  void unsafe_arena_set_allocated_children(::design::VectorInt64* value);
  ::design::VectorInt64* unsafe_arena_release_children();

  private:
  const ::design::VectorInt64& _internal_children() const;
  ::design::VectorInt64* _internal_mutable_children();

  public:
  // .design.AdditionalInfo additionalInfo = 5;
  bool has_additionalinfo() const;
  void clear_additionalinfo() ;
  const ::design::AdditionalInfo& additionalinfo() const;
  PROTOBUF_NODISCARD ::design::AdditionalInfo* release_additionalinfo();
  ::design::AdditionalInfo* mutable_additionalinfo();
  void set_allocated_additionalinfo(::design::AdditionalInfo* value);
  void unsafe_arena_set_allocated_additionalinfo(::design::AdditionalInfo* value);
  ::design::AdditionalInfo* unsafe_arena_release_additionalinfo();

  private:
  const ::design::AdditionalInfo& _internal_additionalinfo() const;
  ::design::AdditionalInfo* _internal_mutable_additionalinfo();

  public:
  // int64 id = 1;
  void clear_id() ;
  ::int64_t id() const;
  void set_id(::int64_t value);

  private:
  ::int64_t _internal_id() const;
  void _internal_set_id(::int64_t value);

  public:
  // int64 traceId = 3;
  void clear_traceid() ;
  ::int64_t traceid() const;
  void set_traceid(::int64_t value);

  private:
  ::int64_t _internal_traceid() const;
  void _internal_set_traceid(::int64_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.NodeTreeRecord)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 5, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const NodeTreeRecord& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::ArenaStringPtr additionaljson_;
    ::design::VectorInt64* children_;
    ::design::AdditionalInfo* additionalinfo_;
    ::int64_t id_;
    ::int64_t traceid_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class NodeTreeActions final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.NodeTreeActions) */ {
 public:
  inline NodeTreeActions() : NodeTreeActions(nullptr) {}
  ~NodeTreeActions() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(NodeTreeActions* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(NodeTreeActions));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeTreeActions(
      ::google::protobuf::internal::ConstantInitialized);

  inline NodeTreeActions(const NodeTreeActions& from) : NodeTreeActions(nullptr, from) {}
  inline NodeTreeActions(NodeTreeActions&& from) noexcept
      : NodeTreeActions(nullptr, std::move(from)) {}
  inline NodeTreeActions& operator=(const NodeTreeActions& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeTreeActions& operator=(NodeTreeActions&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeTreeActions& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeTreeActions* internal_default_instance() {
    return reinterpret_cast<const NodeTreeActions*>(
        &_NodeTreeActions_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 31;
  friend void swap(NodeTreeActions& a, NodeTreeActions& b) { a.Swap(&b); }
  inline void Swap(NodeTreeActions* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeTreeActions* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeTreeActions* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<NodeTreeActions>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const NodeTreeActions& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const NodeTreeActions& from) { NodeTreeActions::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(NodeTreeActions* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.NodeTreeActions"; }

 protected:
  explicit NodeTreeActions(::google::protobuf::Arena* arena);
  NodeTreeActions(::google::protobuf::Arena* arena, const NodeTreeActions& from);
  NodeTreeActions(::google::protobuf::Arena* arena, NodeTreeActions&& from) noexcept
      : NodeTreeActions(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kActionsFieldNumber = 3,
    kProjectInfoFieldNumber = 1,
    kTraceIdFieldNumber = 2,
  };
  // repeated .design.NodeTreeAction actions = 3;
  int actions_size() const;
  private:
  int _internal_actions_size() const;

  public:
  void clear_actions() ;
  ::design::NodeTreeAction* mutable_actions(int index);
  ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>* mutable_actions();

  private:
  const ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>& _internal_actions() const;
  ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>* _internal_mutable_actions();
  public:
  const ::design::NodeTreeAction& actions(int index) const;
  ::design::NodeTreeAction* add_actions();
  const ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>& actions() const;
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // int64 traceId = 2;
  void clear_traceid() ;
  ::int64_t traceid() const;
  void set_traceid(::int64_t value);

  private:
  ::int64_t _internal_traceid() const;
  void _internal_set_traceid(::int64_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.NodeTreeActions)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const NodeTreeActions& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::design::NodeTreeAction > actions_;
    ::design::ProjectInfo* projectinfo_;
    ::int64_t traceid_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class NodeOffsetRecord final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.NodeOffsetRecord) */ {
 public:
  inline NodeOffsetRecord() : NodeOffsetRecord(nullptr) {}
  ~NodeOffsetRecord() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(NodeOffsetRecord* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(NodeOffsetRecord));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeOffsetRecord(
      ::google::protobuf::internal::ConstantInitialized);

  inline NodeOffsetRecord(const NodeOffsetRecord& from) : NodeOffsetRecord(nullptr, from) {}
  inline NodeOffsetRecord(NodeOffsetRecord&& from) noexcept
      : NodeOffsetRecord(nullptr, std::move(from)) {}
  inline NodeOffsetRecord& operator=(const NodeOffsetRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeOffsetRecord& operator=(NodeOffsetRecord&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeOffsetRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeOffsetRecord* internal_default_instance() {
    return reinterpret_cast<const NodeOffsetRecord*>(
        &_NodeOffsetRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 17;
  friend void swap(NodeOffsetRecord& a, NodeOffsetRecord& b) { a.Swap(&b); }
  inline void Swap(NodeOffsetRecord* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeOffsetRecord* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeOffsetRecord* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<NodeOffsetRecord>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const NodeOffsetRecord& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const NodeOffsetRecord& from) { NodeOffsetRecord::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(NodeOffsetRecord* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.NodeOffsetRecord"; }

 protected:
  explicit NodeOffsetRecord(::google::protobuf::Arena* arena);
  NodeOffsetRecord(::google::protobuf::Arena* arena, const NodeOffsetRecord& from);
  NodeOffsetRecord(::google::protobuf::Arena* arena, NodeOffsetRecord&& from) noexcept
      : NodeOffsetRecord(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kOffLenFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // .design.OffsetLength off_len = 2;
  bool has_off_len() const;
  void clear_off_len() ;
  const ::design::OffsetLength& off_len() const;
  PROTOBUF_NODISCARD ::design::OffsetLength* release_off_len();
  ::design::OffsetLength* mutable_off_len();
  void set_allocated_off_len(::design::OffsetLength* value);
  void unsafe_arena_set_allocated_off_len(::design::OffsetLength* value);
  ::design::OffsetLength* unsafe_arena_release_off_len();

  private:
  const ::design::OffsetLength& _internal_off_len() const;
  ::design::OffsetLength* _internal_mutable_off_len();

  public:
  // int32 node_id = 1;
  void clear_node_id() ;
  ::int32_t node_id() const;
  void set_node_id(::int32_t value);

  private:
  ::int32_t _internal_node_id() const;
  void _internal_set_node_id(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.NodeOffsetRecord)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 1,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const NodeOffsetRecord& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::design::OffsetLength* off_len_;
    ::int32_t node_id_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class NodeBaseValue final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.NodeBaseValue) */ {
 public:
  inline NodeBaseValue() : NodeBaseValue(nullptr) {}
  ~NodeBaseValue() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(NodeBaseValue* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(NodeBaseValue));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeBaseValue(
      ::google::protobuf::internal::ConstantInitialized);

  inline NodeBaseValue(const NodeBaseValue& from) : NodeBaseValue(nullptr, from) {}
  inline NodeBaseValue(NodeBaseValue&& from) noexcept
      : NodeBaseValue(nullptr, std::move(from)) {}
  inline NodeBaseValue& operator=(const NodeBaseValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeBaseValue& operator=(NodeBaseValue&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeBaseValue& default_instance() {
    return *internal_default_instance();
  }
  enum OneValueCase {
    kValBool = 1,
    kValInt = 2,
    kValInt64 = 4,
    kValUint = 5,
    kValDouble = 6,
    kValString = 7,
    kVecInt = 21,
    kVecDouble = 22,
    kVecString = 23,
    ONEVALUE_NOT_SET = 0,
  };
  static inline const NodeBaseValue* internal_default_instance() {
    return reinterpret_cast<const NodeBaseValue*>(
        &_NodeBaseValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 0;
  friend void swap(NodeBaseValue& a, NodeBaseValue& b) { a.Swap(&b); }
  inline void Swap(NodeBaseValue* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeBaseValue* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeBaseValue* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<NodeBaseValue>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const NodeBaseValue& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const NodeBaseValue& from) { NodeBaseValue::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(NodeBaseValue* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.NodeBaseValue"; }

 protected:
  explicit NodeBaseValue(::google::protobuf::Arena* arena);
  NodeBaseValue(::google::protobuf::Arena* arena, const NodeBaseValue& from);
  NodeBaseValue(::google::protobuf::Arena* arena, NodeBaseValue&& from) noexcept
      : NodeBaseValue(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kValBoolFieldNumber = 1,
    kValIntFieldNumber = 2,
    kValInt64FieldNumber = 4,
    kValUintFieldNumber = 5,
    kValDoubleFieldNumber = 6,
    kValStringFieldNumber = 7,
    kVecIntFieldNumber = 21,
    kVecDoubleFieldNumber = 22,
    kVecStringFieldNumber = 23,
  };
  // bool val_bool = 1;
  bool has_val_bool() const;
  void clear_val_bool() ;
  bool val_bool() const;
  void set_val_bool(bool value);

  private:
  bool _internal_val_bool() const;
  void _internal_set_val_bool(bool value);

  public:
  // int32 val_int = 2;
  bool has_val_int() const;
  void clear_val_int() ;
  ::int32_t val_int() const;
  void set_val_int(::int32_t value);

  private:
  ::int32_t _internal_val_int() const;
  void _internal_set_val_int(::int32_t value);

  public:
  // int64 val_int64 = 4;
  bool has_val_int64() const;
  void clear_val_int64() ;
  ::int64_t val_int64() const;
  void set_val_int64(::int64_t value);

  private:
  ::int64_t _internal_val_int64() const;
  void _internal_set_val_int64(::int64_t value);

  public:
  // uint32 val_uint = 5;
  bool has_val_uint() const;
  void clear_val_uint() ;
  ::uint32_t val_uint() const;
  void set_val_uint(::uint32_t value);

  private:
  ::uint32_t _internal_val_uint() const;
  void _internal_set_val_uint(::uint32_t value);

  public:
  // double val_double = 6;
  bool has_val_double() const;
  void clear_val_double() ;
  double val_double() const;
  void set_val_double(double value);

  private:
  double _internal_val_double() const;
  void _internal_set_val_double(double value);

  public:
  // bytes val_string = 7;
  bool has_val_string() const;
  void clear_val_string() ;
  const std::string& val_string() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_val_string(Arg_&& arg, Args_... args);
  std::string* mutable_val_string();
  PROTOBUF_NODISCARD std::string* release_val_string();
  void set_allocated_val_string(std::string* value);

  private:
  const std::string& _internal_val_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_val_string(
      const std::string& value);
  std::string* _internal_mutable_val_string();

  public:
  // .design.VectorInt vec_int = 21;
  bool has_vec_int() const;
  private:
  bool _internal_has_vec_int() const;

  public:
  void clear_vec_int() ;
  const ::design::VectorInt& vec_int() const;
  PROTOBUF_NODISCARD ::design::VectorInt* release_vec_int();
  ::design::VectorInt* mutable_vec_int();
  void set_allocated_vec_int(::design::VectorInt* value);
  void unsafe_arena_set_allocated_vec_int(::design::VectorInt* value);
  ::design::VectorInt* unsafe_arena_release_vec_int();

  private:
  const ::design::VectorInt& _internal_vec_int() const;
  ::design::VectorInt* _internal_mutable_vec_int();

  public:
  // .design.VectorDouble vec_double = 22;
  bool has_vec_double() const;
  private:
  bool _internal_has_vec_double() const;

  public:
  void clear_vec_double() ;
  const ::design::VectorDouble& vec_double() const;
  PROTOBUF_NODISCARD ::design::VectorDouble* release_vec_double();
  ::design::VectorDouble* mutable_vec_double();
  void set_allocated_vec_double(::design::VectorDouble* value);
  void unsafe_arena_set_allocated_vec_double(::design::VectorDouble* value);
  ::design::VectorDouble* unsafe_arena_release_vec_double();

  private:
  const ::design::VectorDouble& _internal_vec_double() const;
  ::design::VectorDouble* _internal_mutable_vec_double();

  public:
  // .design.VectorString vec_string = 23;
  bool has_vec_string() const;
  private:
  bool _internal_has_vec_string() const;

  public:
  void clear_vec_string() ;
  const ::design::VectorString& vec_string() const;
  PROTOBUF_NODISCARD ::design::VectorString* release_vec_string();
  ::design::VectorString* mutable_vec_string();
  void set_allocated_vec_string(::design::VectorString* value);
  void unsafe_arena_set_allocated_vec_string(::design::VectorString* value);
  ::design::VectorString* unsafe_arena_release_vec_string();

  private:
  const ::design::VectorString& _internal_vec_string() const;
  ::design::VectorString* _internal_mutable_vec_string();

  public:
  void clear_OneValue();
  OneValueCase OneValue_case() const;
  // @@protoc_insertion_point(class_scope:design.NodeBaseValue)
 private:
  class _Internal;
  void set_has_val_bool();
  void set_has_val_int();
  void set_has_val_int64();
  void set_has_val_uint();
  void set_has_val_double();
  void set_has_val_string();
  void set_has_vec_int();
  void set_has_vec_double();
  void set_has_vec_string();
  inline bool has_OneValue() const;
  inline void clear_has_OneValue();
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 9, 3,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const NodeBaseValue& from_msg);
    union OneValueUnion {
      constexpr OneValueUnion() : _constinit_{} {}
      ::google::protobuf::internal::ConstantInitialized _constinit_;
      bool val_bool_;
      ::int32_t val_int_;
      ::int64_t val_int64_;
      ::uint32_t val_uint_;
      double val_double_;
      ::google::protobuf::internal::ArenaStringPtr val_string_;
      ::design::VectorInt* vec_int_;
      ::design::VectorDouble* vec_double_;
      ::design::VectorString* vec_string_;
    } OneValue_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::uint32_t _oneof_case_[1];
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class MaxOffset final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.MaxOffset) */ {
 public:
  inline MaxOffset() : MaxOffset(nullptr) {}
  ~MaxOffset() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(MaxOffset* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(MaxOffset));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR MaxOffset(
      ::google::protobuf::internal::ConstantInitialized);

  inline MaxOffset(const MaxOffset& from) : MaxOffset(nullptr, from) {}
  inline MaxOffset(MaxOffset&& from) noexcept
      : MaxOffset(nullptr, std::move(from)) {}
  inline MaxOffset& operator=(const MaxOffset& from) {
    CopyFrom(from);
    return *this;
  }
  inline MaxOffset& operator=(MaxOffset&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MaxOffset& default_instance() {
    return *internal_default_instance();
  }
  static inline const MaxOffset* internal_default_instance() {
    return reinterpret_cast<const MaxOffset*>(
        &_MaxOffset_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 29;
  friend void swap(MaxOffset& a, MaxOffset& b) { a.Swap(&b); }
  inline void Swap(MaxOffset* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MaxOffset* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MaxOffset* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<MaxOffset>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const MaxOffset& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const MaxOffset& from) { MaxOffset::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(MaxOffset* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.MaxOffset"; }

 protected:
  explicit MaxOffset(::google::protobuf::Arena* arena);
  MaxOffset(::google::protobuf::Arena* arena, const MaxOffset& from);
  MaxOffset(::google::protobuf::Arena* arena, MaxOffset&& from) noexcept
      : MaxOffset(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kProjectInfoFieldNumber = 1,
    kMaxOffsetFieldNumber = 3,
    kFlagFieldNumber = 2,
  };
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // int64 maxOffset = 3;
  void clear_maxoffset() ;
  ::int64_t maxoffset() const;
  void set_maxoffset(::int64_t value);

  private:
  ::int64_t _internal_maxoffset() const;
  void _internal_set_maxoffset(::int64_t value);

  public:
  // int32 flag = 2;
  void clear_flag() ;
  ::int32_t flag() const;
  void set_flag(::int32_t value);

  private:
  ::int32_t _internal_flag() const;
  void _internal_set_flag(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.MaxOffset)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 1,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const MaxOffset& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::design::ProjectInfo* projectinfo_;
    ::int64_t maxoffset_;
    ::int32_t flag_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectNodesTree final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectNodesTree) */ {
 public:
  inline ProjectNodesTree() : ProjectNodesTree(nullptr) {}
  ~ProjectNodesTree() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectNodesTree* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectNodesTree));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectNodesTree(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectNodesTree(const ProjectNodesTree& from) : ProjectNodesTree(nullptr, from) {}
  inline ProjectNodesTree(ProjectNodesTree&& from) noexcept
      : ProjectNodesTree(nullptr, std::move(from)) {}
  inline ProjectNodesTree& operator=(const ProjectNodesTree& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectNodesTree& operator=(ProjectNodesTree&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectNodesTree& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectNodesTree* internal_default_instance() {
    return reinterpret_cast<const ProjectNodesTree*>(
        &_ProjectNodesTree_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 32;
  friend void swap(ProjectNodesTree& a, ProjectNodesTree& b) { a.Swap(&b); }
  inline void Swap(ProjectNodesTree* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectNodesTree* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectNodesTree* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectNodesTree>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectNodesTree& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectNodesTree& from) { ProjectNodesTree::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectNodesTree* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectNodesTree"; }

 protected:
  explicit ProjectNodesTree(::google::protobuf::Arena* arena);
  ProjectNodesTree(::google::protobuf::Arena* arena, const ProjectNodesTree& from);
  ProjectNodesTree(::google::protobuf::Arena* arena, ProjectNodesTree&& from) noexcept
      : ProjectNodesTree(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kTreesFieldNumber = 2,
    kProjectInfoFieldNumber = 1,
  };
  // repeated .design.NodeTreeRecord trees = 2;
  int trees_size() const;
  private:
  int _internal_trees_size() const;

  public:
  void clear_trees() ;
  ::design::NodeTreeRecord* mutable_trees(int index);
  ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>* mutable_trees();

  private:
  const ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>& _internal_trees() const;
  ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>* _internal_mutable_trees();
  public:
  const ::design::NodeTreeRecord& trees(int index) const;
  ::design::NodeTreeRecord* add_trees();
  const ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>& trees() const;
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectNodesTree)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectNodesTree& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::design::NodeTreeRecord > trees_;
    ::design::ProjectInfo* projectinfo_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectNodesOffset final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectNodesOffset) */ {
 public:
  inline ProjectNodesOffset() : ProjectNodesOffset(nullptr) {}
  ~ProjectNodesOffset() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectNodesOffset* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectNodesOffset));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectNodesOffset(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectNodesOffset(const ProjectNodesOffset& from) : ProjectNodesOffset(nullptr, from) {}
  inline ProjectNodesOffset(ProjectNodesOffset&& from) noexcept
      : ProjectNodesOffset(nullptr, std::move(from)) {}
  inline ProjectNodesOffset& operator=(const ProjectNodesOffset& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectNodesOffset& operator=(ProjectNodesOffset&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectNodesOffset& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectNodesOffset* internal_default_instance() {
    return reinterpret_cast<const ProjectNodesOffset*>(
        &_ProjectNodesOffset_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 28;
  friend void swap(ProjectNodesOffset& a, ProjectNodesOffset& b) { a.Swap(&b); }
  inline void Swap(ProjectNodesOffset* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectNodesOffset* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectNodesOffset* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectNodesOffset>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectNodesOffset& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectNodesOffset& from) { ProjectNodesOffset::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectNodesOffset* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectNodesOffset"; }

 protected:
  explicit ProjectNodesOffset(::google::protobuf::Arena* arena);
  ProjectNodesOffset(::google::protobuf::Arena* arena, const ProjectNodesOffset& from);
  ProjectNodesOffset(::google::protobuf::Arena* arena, ProjectNodesOffset&& from) noexcept
      : ProjectNodesOffset(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kOffsetsFieldNumber = 2,
    kProjectInfoFieldNumber = 1,
  };
  // map<uint64, .design.OffsetLength> offsets = 2;
  int offsets_size() const;
  private:
  int _internal_offsets_size() const;

  public:
  void clear_offsets() ;
  const ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>& offsets() const;
  ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>* mutable_offsets();

  private:
  const ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>& _internal_offsets() const;
  ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>* _internal_mutable_offsets();

  public:
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectNodesOffset)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 2, 3,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectNodesOffset& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::MapField<ProjectNodesOffset_OffsetsEntry_DoNotUse, ::uint64_t, ::design::OffsetLength,
                      ::google::protobuf::internal::WireFormatLite::TYPE_UINT64,
                      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE>
        offsets_;
    ::design::ProjectInfo* projectinfo_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class NodeAttrs_AttrMapEntry_DoNotUse final
    : public ::google::protobuf::internal::MapEntry<
          ::int64_t, ::google::protobuf::Message,
          ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
          ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE> {
 public:
  using SuperType = ::google::protobuf::internal::MapEntry<
      ::int64_t, ::google::protobuf::Message,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE>;
  NodeAttrs_AttrMapEntry_DoNotUse();
  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeAttrs_AttrMapEntry_DoNotUse(
      ::google::protobuf::internal::ConstantInitialized);
  explicit NodeAttrs_AttrMapEntry_DoNotUse(::google::protobuf::Arena* arena);
  static const NodeAttrs_AttrMapEntry_DoNotUse* internal_default_instance() {
    return reinterpret_cast<const NodeAttrs_AttrMapEntry_DoNotUse*>(
        &_NodeAttrs_AttrMapEntry_DoNotUse_default_instance_);
  }


 private:
  friend class ::google::protobuf::MessageLite;
  friend struct ::TableStruct_node_2eproto;

  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 1,
      0, 2>
      _table_;

  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;
};
// -------------------------------------------------------------------

class NodeAttrs final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.NodeAttrs) */ {
 public:
  inline NodeAttrs() : NodeAttrs(nullptr) {}
  ~NodeAttrs() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(NodeAttrs* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(NodeAttrs));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeAttrs(
      ::google::protobuf::internal::ConstantInitialized);

  inline NodeAttrs(const NodeAttrs& from) : NodeAttrs(nullptr, from) {}
  inline NodeAttrs(NodeAttrs&& from) noexcept
      : NodeAttrs(nullptr, std::move(from)) {}
  inline NodeAttrs& operator=(const NodeAttrs& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeAttrs& operator=(NodeAttrs&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeAttrs& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeAttrs* internal_default_instance() {
    return reinterpret_cast<const NodeAttrs*>(
        &_NodeAttrs_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 11;
  friend void swap(NodeAttrs& a, NodeAttrs& b) { a.Swap(&b); }
  inline void Swap(NodeAttrs* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeAttrs* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeAttrs* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<NodeAttrs>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const NodeAttrs& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const NodeAttrs& from) { NodeAttrs::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(NodeAttrs* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.NodeAttrs"; }

 protected:
  explicit NodeAttrs(::google::protobuf::Arena* arena);
  NodeAttrs(::google::protobuf::Arena* arena, const NodeAttrs& from);
  NodeAttrs(::google::protobuf::Arena* arena, NodeAttrs&& from) noexcept
      : NodeAttrs(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kAttrMapFieldNumber = 1,
  };
  // map<int64, .design.NodeBaseValue> attr_map = 1;
  int attr_map_size() const;
  private:
  int _internal_attr_map_size() const;

  public:
  void clear_attr_map() ;
  const ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>& attr_map() const;
  ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>* mutable_attr_map();

  private:
  const ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>& _internal_attr_map() const;
  ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>* _internal_mutable_attr_map();

  public:
  // @@protoc_insertion_point(class_scope:design.NodeAttrs)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const NodeAttrs& from_msg);
    ::google::protobuf::internal::MapField<NodeAttrs_AttrMapEntry_DoNotUse, ::int64_t, ::design::NodeBaseValue,
                      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
                      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE>
        attr_map_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class NodeAttrsRecord final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.NodeAttrsRecord) */ {
 public:
  inline NodeAttrsRecord() : NodeAttrsRecord(nullptr) {}
  ~NodeAttrsRecord() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(NodeAttrsRecord* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(NodeAttrsRecord));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR NodeAttrsRecord(
      ::google::protobuf::internal::ConstantInitialized);

  inline NodeAttrsRecord(const NodeAttrsRecord& from) : NodeAttrsRecord(nullptr, from) {}
  inline NodeAttrsRecord(NodeAttrsRecord&& from) noexcept
      : NodeAttrsRecord(nullptr, std::move(from)) {}
  inline NodeAttrsRecord& operator=(const NodeAttrsRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeAttrsRecord& operator=(NodeAttrsRecord&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeAttrsRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeAttrsRecord* internal_default_instance() {
    return reinterpret_cast<const NodeAttrsRecord*>(
        &_NodeAttrsRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 13;
  friend void swap(NodeAttrsRecord& a, NodeAttrsRecord& b) { a.Swap(&b); }
  inline void Swap(NodeAttrsRecord* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeAttrsRecord* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeAttrsRecord* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<NodeAttrsRecord>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const NodeAttrsRecord& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const NodeAttrsRecord& from) { NodeAttrsRecord::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(NodeAttrsRecord* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.NodeAttrsRecord"; }

 protected:
  explicit NodeAttrsRecord(::google::protobuf::Arena* arena);
  NodeAttrsRecord(::google::protobuf::Arena* arena, const NodeAttrsRecord& from);
  NodeAttrsRecord(::google::protobuf::Arena* arena, NodeAttrsRecord&& from) noexcept
      : NodeAttrsRecord(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kNameFieldNumber = 2,
    kAdditionalJSONFieldNumber = 7,
    kAttrsFieldNumber = 4,
    kAdditionalInfoFieldNumber = 8,
    kIdFieldNumber = 1,
    kTypeFieldNumber = 3,
    kActionFieldNumber = 6,
    kTraceIdFieldNumber = 5,
  };
  // bytes name = 2;
  void clear_name() ;
  const std::string& name() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_name(Arg_&& arg, Args_... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* value);

  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(
      const std::string& value);
  std::string* _internal_mutable_name();

  public:
  // bytes additionalJSON = 7;
  void clear_additionaljson() ;
  const std::string& additionaljson() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_additionaljson(Arg_&& arg, Args_... args);
  std::string* mutable_additionaljson();
  PROTOBUF_NODISCARD std::string* release_additionaljson();
  void set_allocated_additionaljson(std::string* value);

  private:
  const std::string& _internal_additionaljson() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_additionaljson(
      const std::string& value);
  std::string* _internal_mutable_additionaljson();

  public:
  // .design.NodeAttrs attrs = 4;
  bool has_attrs() const;
  void clear_attrs() ;
  const ::design::NodeAttrs& attrs() const;
  PROTOBUF_NODISCARD ::design::NodeAttrs* release_attrs();
  ::design::NodeAttrs* mutable_attrs();
  void set_allocated_attrs(::design::NodeAttrs* value);
  void unsafe_arena_set_allocated_attrs(::design::NodeAttrs* value);
  ::design::NodeAttrs* unsafe_arena_release_attrs();

  private:
  const ::design::NodeAttrs& _internal_attrs() const;
  ::design::NodeAttrs* _internal_mutable_attrs();

  public:
  // .design.AdditionalInfo additionalInfo = 8;
  bool has_additionalinfo() const;
  void clear_additionalinfo() ;
  const ::design::AdditionalInfo& additionalinfo() const;
  PROTOBUF_NODISCARD ::design::AdditionalInfo* release_additionalinfo();
  ::design::AdditionalInfo* mutable_additionalinfo();
  void set_allocated_additionalinfo(::design::AdditionalInfo* value);
  void unsafe_arena_set_allocated_additionalinfo(::design::AdditionalInfo* value);
  ::design::AdditionalInfo* unsafe_arena_release_additionalinfo();

  private:
  const ::design::AdditionalInfo& _internal_additionalinfo() const;
  ::design::AdditionalInfo* _internal_mutable_additionalinfo();

  public:
  // int64 id = 1;
  void clear_id() ;
  ::int64_t id() const;
  void set_id(::int64_t value);

  private:
  ::int64_t _internal_id() const;
  void _internal_set_id(::int64_t value);

  public:
  // int32 type = 3;
  void clear_type() ;
  ::int32_t type() const;
  void set_type(::int32_t value);

  private:
  ::int32_t _internal_type() const;
  void _internal_set_type(::int32_t value);

  public:
  // int32 action = 6;
  void clear_action() ;
  ::int32_t action() const;
  void set_action(::int32_t value);

  private:
  ::int32_t _internal_action() const;
  void _internal_set_action(::int32_t value);

  public:
  // int64 traceId = 5;
  void clear_traceid() ;
  ::int64_t traceid() const;
  void set_traceid(::int64_t value);

  private:
  ::int64_t _internal_traceid() const;
  void _internal_set_traceid(::int64_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.NodeAttrsRecord)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 8, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const NodeAttrsRecord& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::ArenaStringPtr name_;
    ::google::protobuf::internal::ArenaStringPtr additionaljson_;
    ::design::NodeAttrs* attrs_;
    ::design::AdditionalInfo* additionalinfo_;
    ::int64_t id_;
    ::int32_t type_;
    ::int32_t action_;
    ::int64_t traceid_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class ProjectNodes final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.ProjectNodes) */ {
 public:
  inline ProjectNodes() : ProjectNodes(nullptr) {}
  ~ProjectNodes() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(ProjectNodes* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(ProjectNodes));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR ProjectNodes(
      ::google::protobuf::internal::ConstantInitialized);

  inline ProjectNodes(const ProjectNodes& from) : ProjectNodes(nullptr, from) {}
  inline ProjectNodes(ProjectNodes&& from) noexcept
      : ProjectNodes(nullptr, std::move(from)) {}
  inline ProjectNodes& operator=(const ProjectNodes& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProjectNodes& operator=(ProjectNodes&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProjectNodes& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProjectNodes* internal_default_instance() {
    return reinterpret_cast<const ProjectNodes*>(
        &_ProjectNodes_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 25;
  friend void swap(ProjectNodes& a, ProjectNodes& b) { a.Swap(&b); }
  inline void Swap(ProjectNodes* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProjectNodes* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProjectNodes* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<ProjectNodes>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const ProjectNodes& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const ProjectNodes& from) { ProjectNodes::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(ProjectNodes* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.ProjectNodes"; }

 protected:
  explicit ProjectNodes(::google::protobuf::Arena* arena);
  ProjectNodes(::google::protobuf::Arena* arena, const ProjectNodes& from);
  ProjectNodes(::google::protobuf::Arena* arena, ProjectNodes&& from) noexcept
      : ProjectNodes(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kNodesFieldNumber = 2,
    kProjectInfoFieldNumber = 1,
    kActionFieldNumber = 3,
  };
  // repeated .design.NodeAttrsRecord nodes = 2;
  int nodes_size() const;
  private:
  int _internal_nodes_size() const;

  public:
  void clear_nodes() ;
  ::design::NodeAttrsRecord* mutable_nodes(int index);
  ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>* mutable_nodes();

  private:
  const ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>& _internal_nodes() const;
  ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>* _internal_mutable_nodes();
  public:
  const ::design::NodeAttrsRecord& nodes(int index) const;
  ::design::NodeAttrsRecord* add_nodes();
  const ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>& nodes() const;
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // int32 action = 3;
  void clear_action() ;
  ::int32_t action() const;
  void set_action(::int32_t value);

  private:
  ::int32_t _internal_action() const;
  void _internal_set_action(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.ProjectNodes)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const ProjectNodes& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::design::NodeAttrsRecord > nodes_;
    ::design::ProjectInfo* projectinfo_;
    ::int32_t action_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class MessageQueuesPackage final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.MessageQueuesPackage) */ {
 public:
  inline MessageQueuesPackage() : MessageQueuesPackage(nullptr) {}
  ~MessageQueuesPackage() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(MessageQueuesPackage* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(MessageQueuesPackage));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR MessageQueuesPackage(
      ::google::protobuf::internal::ConstantInitialized);

  inline MessageQueuesPackage(const MessageQueuesPackage& from) : MessageQueuesPackage(nullptr, from) {}
  inline MessageQueuesPackage(MessageQueuesPackage&& from) noexcept
      : MessageQueuesPackage(nullptr, std::move(from)) {}
  inline MessageQueuesPackage& operator=(const MessageQueuesPackage& from) {
    CopyFrom(from);
    return *this;
  }
  inline MessageQueuesPackage& operator=(MessageQueuesPackage&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MessageQueuesPackage& default_instance() {
    return *internal_default_instance();
  }
  enum RecordCase {
    kTrees = 1,
    kNodes = 2,
    RECORD_NOT_SET = 0,
  };
  static inline const MessageQueuesPackage* internal_default_instance() {
    return reinterpret_cast<const MessageQueuesPackage*>(
        &_MessageQueuesPackage_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 20;
  friend void swap(MessageQueuesPackage& a, MessageQueuesPackage& b) { a.Swap(&b); }
  inline void Swap(MessageQueuesPackage* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MessageQueuesPackage* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MessageQueuesPackage* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<MessageQueuesPackage>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const MessageQueuesPackage& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const MessageQueuesPackage& from) { MessageQueuesPackage::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(MessageQueuesPackage* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.MessageQueuesPackage"; }

 protected:
  explicit MessageQueuesPackage(::google::protobuf::Arena* arena);
  MessageQueuesPackage(::google::protobuf::Arena* arena, const MessageQueuesPackage& from);
  MessageQueuesPackage(::google::protobuf::Arena* arena, MessageQueuesPackage&& from) noexcept
      : MessageQueuesPackage(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kTreesFieldNumber = 1,
    kNodesFieldNumber = 2,
  };
  // .design.NodeTreeRecord trees = 1;
  bool has_trees() const;
  private:
  bool _internal_has_trees() const;

  public:
  void clear_trees() ;
  const ::design::NodeTreeRecord& trees() const;
  PROTOBUF_NODISCARD ::design::NodeTreeRecord* release_trees();
  ::design::NodeTreeRecord* mutable_trees();
  void set_allocated_trees(::design::NodeTreeRecord* value);
  void unsafe_arena_set_allocated_trees(::design::NodeTreeRecord* value);
  ::design::NodeTreeRecord* unsafe_arena_release_trees();

  private:
  const ::design::NodeTreeRecord& _internal_trees() const;
  ::design::NodeTreeRecord* _internal_mutable_trees();

  public:
  // .design.NodeAttrsRecord nodes = 2;
  bool has_nodes() const;
  private:
  bool _internal_has_nodes() const;

  public:
  void clear_nodes() ;
  const ::design::NodeAttrsRecord& nodes() const;
  PROTOBUF_NODISCARD ::design::NodeAttrsRecord* release_nodes();
  ::design::NodeAttrsRecord* mutable_nodes();
  void set_allocated_nodes(::design::NodeAttrsRecord* value);
  void unsafe_arena_set_allocated_nodes(::design::NodeAttrsRecord* value);
  ::design::NodeAttrsRecord* unsafe_arena_release_nodes();

  private:
  const ::design::NodeAttrsRecord& _internal_nodes() const;
  ::design::NodeAttrsRecord* _internal_mutable_nodes();

  public:
  void clear_Record();
  RecordCase Record_case() const;
  // @@protoc_insertion_point(class_scope:design.MessageQueuesPackage)
 private:
  class _Internal;
  void set_has_trees();
  void set_has_nodes();
  inline bool has_Record() const;
  inline void clear_has_Record();
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 2, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const MessageQueuesPackage& from_msg);
    union RecordUnion {
      constexpr RecordUnion() : _constinit_{} {}
      ::google::protobuf::internal::ConstantInitialized _constinit_;
      ::design::NodeTreeRecord* trees_;
      ::design::NodeAttrsRecord* nodes_;
    } Record_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::uint32_t _oneof_case_[1];
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};
// -------------------------------------------------------------------

class DataBlock final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:design.DataBlock) */ {
 public:
  inline DataBlock() : DataBlock(nullptr) {}
  ~DataBlock() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(DataBlock* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(DataBlock));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR DataBlock(
      ::google::protobuf::internal::ConstantInitialized);

  inline DataBlock(const DataBlock& from) : DataBlock(nullptr, from) {}
  inline DataBlock(DataBlock&& from) noexcept
      : DataBlock(nullptr, std::move(from)) {}
  inline DataBlock& operator=(const DataBlock& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataBlock& operator=(DataBlock&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataBlock& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataBlock* internal_default_instance() {
    return reinterpret_cast<const DataBlock*>(
        &_DataBlock_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 33;
  friend void swap(DataBlock& a, DataBlock& b) { a.Swap(&b); }
  inline void Swap(DataBlock* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataBlock* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataBlock* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::Message::DefaultConstruct<DataBlock>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const DataBlock& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const DataBlock& from) { DataBlock::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(DataBlock* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "design.DataBlock"; }

 protected:
  explicit DataBlock(::google::protobuf::Arena* arena);
  DataBlock(::google::protobuf::Arena* arena, const DataBlock& from);
  DataBlock(::google::protobuf::Arena* arena, DataBlock&& from) noexcept
      : DataBlock(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataFull _class_data_;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kProjectInfoFieldNumber = 1,
    kPackageFieldNumber = 5,
    kFlagFieldNumber = 2,
    kActionFieldNumber = 3,
  };
  // .design.ProjectInfo projectInfo = 1;
  bool has_projectinfo() const;
  void clear_projectinfo() ;
  const ::design::ProjectInfo& projectinfo() const;
  PROTOBUF_NODISCARD ::design::ProjectInfo* release_projectinfo();
  ::design::ProjectInfo* mutable_projectinfo();
  void set_allocated_projectinfo(::design::ProjectInfo* value);
  void unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value);
  ::design::ProjectInfo* unsafe_arena_release_projectinfo();

  private:
  const ::design::ProjectInfo& _internal_projectinfo() const;
  ::design::ProjectInfo* _internal_mutable_projectinfo();

  public:
  // .design.MessageQueuesPackage package = 5;
  bool has_package() const;
  void clear_package() ;
  const ::design::MessageQueuesPackage& package() const;
  PROTOBUF_NODISCARD ::design::MessageQueuesPackage* release_package();
  ::design::MessageQueuesPackage* mutable_package();
  void set_allocated_package(::design::MessageQueuesPackage* value);
  void unsafe_arena_set_allocated_package(::design::MessageQueuesPackage* value);
  ::design::MessageQueuesPackage* unsafe_arena_release_package();

  private:
  const ::design::MessageQueuesPackage& _internal_package() const;
  ::design::MessageQueuesPackage* _internal_mutable_package();

  public:
  // int32 flag = 2;
  void clear_flag() ;
  ::int32_t flag() const;
  void set_flag(::int32_t value);

  private:
  ::int32_t _internal_flag() const;
  void _internal_set_flag(::int32_t value);

  public:
  // int32 action = 3;
  void clear_action() ;
  ::int32_t action() const;
  void set_action(::int32_t value);

  private:
  ::int32_t _internal_action() const;
  void _internal_set_action(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:design.DataBlock)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 4, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const DataBlock& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::design::ProjectInfo* projectinfo_;
    ::design::MessageQueuesPackage* package_;
    ::int32_t flag_;
    ::int32_t action_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_node_2eproto;
};

// ===================================================================




// ===================================================================


#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// NodeBaseValue

// bool val_bool = 1;
inline bool NodeBaseValue::has_val_bool() const {
  return OneValue_case() == kValBool;
}
inline void NodeBaseValue::set_has_val_bool() {
  _impl_._oneof_case_[0] = kValBool;
}
inline void NodeBaseValue::clear_val_bool() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kValBool) {
    _impl_.OneValue_.val_bool_ = false;
    clear_has_OneValue();
  }
}
inline bool NodeBaseValue::val_bool() const {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.val_bool)
  return _internal_val_bool();
}
inline void NodeBaseValue::set_val_bool(bool value) {
  if (OneValue_case() != kValBool) {
    clear_OneValue();
    set_has_val_bool();
  }
  _impl_.OneValue_.val_bool_ = value;
  // @@protoc_insertion_point(field_set:design.NodeBaseValue.val_bool)
}
inline bool NodeBaseValue::_internal_val_bool() const {
  if (OneValue_case() == kValBool) {
    return _impl_.OneValue_.val_bool_;
  }
  return false;
}

// int32 val_int = 2;
inline bool NodeBaseValue::has_val_int() const {
  return OneValue_case() == kValInt;
}
inline void NodeBaseValue::set_has_val_int() {
  _impl_._oneof_case_[0] = kValInt;
}
inline void NodeBaseValue::clear_val_int() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kValInt) {
    _impl_.OneValue_.val_int_ = 0;
    clear_has_OneValue();
  }
}
inline ::int32_t NodeBaseValue::val_int() const {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.val_int)
  return _internal_val_int();
}
inline void NodeBaseValue::set_val_int(::int32_t value) {
  if (OneValue_case() != kValInt) {
    clear_OneValue();
    set_has_val_int();
  }
  _impl_.OneValue_.val_int_ = value;
  // @@protoc_insertion_point(field_set:design.NodeBaseValue.val_int)
}
inline ::int32_t NodeBaseValue::_internal_val_int() const {
  if (OneValue_case() == kValInt) {
    return _impl_.OneValue_.val_int_;
  }
  return 0;
}

// int64 val_int64 = 4;
inline bool NodeBaseValue::has_val_int64() const {
  return OneValue_case() == kValInt64;
}
inline void NodeBaseValue::set_has_val_int64() {
  _impl_._oneof_case_[0] = kValInt64;
}
inline void NodeBaseValue::clear_val_int64() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kValInt64) {
    _impl_.OneValue_.val_int64_ = ::int64_t{0};
    clear_has_OneValue();
  }
}
inline ::int64_t NodeBaseValue::val_int64() const {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.val_int64)
  return _internal_val_int64();
}
inline void NodeBaseValue::set_val_int64(::int64_t value) {
  if (OneValue_case() != kValInt64) {
    clear_OneValue();
    set_has_val_int64();
  }
  _impl_.OneValue_.val_int64_ = value;
  // @@protoc_insertion_point(field_set:design.NodeBaseValue.val_int64)
}
inline ::int64_t NodeBaseValue::_internal_val_int64() const {
  if (OneValue_case() == kValInt64) {
    return _impl_.OneValue_.val_int64_;
  }
  return ::int64_t{0};
}

// uint32 val_uint = 5;
inline bool NodeBaseValue::has_val_uint() const {
  return OneValue_case() == kValUint;
}
inline void NodeBaseValue::set_has_val_uint() {
  _impl_._oneof_case_[0] = kValUint;
}
inline void NodeBaseValue::clear_val_uint() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kValUint) {
    _impl_.OneValue_.val_uint_ = 0u;
    clear_has_OneValue();
  }
}
inline ::uint32_t NodeBaseValue::val_uint() const {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.val_uint)
  return _internal_val_uint();
}
inline void NodeBaseValue::set_val_uint(::uint32_t value) {
  if (OneValue_case() != kValUint) {
    clear_OneValue();
    set_has_val_uint();
  }
  _impl_.OneValue_.val_uint_ = value;
  // @@protoc_insertion_point(field_set:design.NodeBaseValue.val_uint)
}
inline ::uint32_t NodeBaseValue::_internal_val_uint() const {
  if (OneValue_case() == kValUint) {
    return _impl_.OneValue_.val_uint_;
  }
  return 0u;
}

// double val_double = 6;
inline bool NodeBaseValue::has_val_double() const {
  return OneValue_case() == kValDouble;
}
inline void NodeBaseValue::set_has_val_double() {
  _impl_._oneof_case_[0] = kValDouble;
}
inline void NodeBaseValue::clear_val_double() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kValDouble) {
    _impl_.OneValue_.val_double_ = 0;
    clear_has_OneValue();
  }
}
inline double NodeBaseValue::val_double() const {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.val_double)
  return _internal_val_double();
}
inline void NodeBaseValue::set_val_double(double value) {
  if (OneValue_case() != kValDouble) {
    clear_OneValue();
    set_has_val_double();
  }
  _impl_.OneValue_.val_double_ = value;
  // @@protoc_insertion_point(field_set:design.NodeBaseValue.val_double)
}
inline double NodeBaseValue::_internal_val_double() const {
  if (OneValue_case() == kValDouble) {
    return _impl_.OneValue_.val_double_;
  }
  return 0;
}

// bytes val_string = 7;
inline bool NodeBaseValue::has_val_string() const {
  return OneValue_case() == kValString;
}
inline void NodeBaseValue::set_has_val_string() {
  _impl_._oneof_case_[0] = kValString;
}
inline void NodeBaseValue::clear_val_string() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kValString) {
    _impl_.OneValue_.val_string_.Destroy();
    clear_has_OneValue();
  }
}
inline const std::string& NodeBaseValue::val_string() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.val_string)
  return _internal_val_string();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void NodeBaseValue::set_val_string(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() != kValString) {
    clear_OneValue();

    set_has_val_string();
    _impl_.OneValue_.val_string_.InitDefault();
  }
  _impl_.OneValue_.val_string_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.NodeBaseValue.val_string)
}
inline std::string* NodeBaseValue::mutable_val_string() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_val_string();
  // @@protoc_insertion_point(field_mutable:design.NodeBaseValue.val_string)
  return _s;
}
inline const std::string& NodeBaseValue::_internal_val_string() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  if (OneValue_case() != kValString) {
    return ::google::protobuf::internal::GetEmptyStringAlreadyInited();
  }
  return _impl_.OneValue_.val_string_.Get();
}
inline void NodeBaseValue::_internal_set_val_string(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() != kValString) {
    clear_OneValue();

    set_has_val_string();
    _impl_.OneValue_.val_string_.InitDefault();
  }
  _impl_.OneValue_.val_string_.Set(value, GetArena());
}
inline std::string* NodeBaseValue::_internal_mutable_val_string() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() != kValString) {
    clear_OneValue();

    set_has_val_string();
    _impl_.OneValue_.val_string_.InitDefault();
  }
  return _impl_.OneValue_.val_string_.Mutable( GetArena());
}
inline std::string* NodeBaseValue::release_val_string() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeBaseValue.val_string)
  if (OneValue_case() != kValString) {
    return nullptr;
  }
  clear_has_OneValue();
  return _impl_.OneValue_.val_string_.Release();
}
inline void NodeBaseValue::set_allocated_val_string(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (has_OneValue()) {
    clear_OneValue();
  }
  if (value != nullptr) {
    set_has_val_string();
    _impl_.OneValue_.val_string_.InitAllocated(value, GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.NodeBaseValue.val_string)
}

// .design.VectorInt vec_int = 21;
inline bool NodeBaseValue::has_vec_int() const {
  return OneValue_case() == kVecInt;
}
inline bool NodeBaseValue::_internal_has_vec_int() const {
  return OneValue_case() == kVecInt;
}
inline void NodeBaseValue::set_has_vec_int() {
  _impl_._oneof_case_[0] = kVecInt;
}
inline void NodeBaseValue::clear_vec_int() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kVecInt) {
    if (GetArena() == nullptr) {
      delete _impl_.OneValue_.vec_int_;
    } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
      ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.OneValue_.vec_int_);
    }
    clear_has_OneValue();
  }
}
inline ::design::VectorInt* NodeBaseValue::release_vec_int() {
  // @@protoc_insertion_point(field_release:design.NodeBaseValue.vec_int)
  if (OneValue_case() == kVecInt) {
    clear_has_OneValue();
    auto* temp = _impl_.OneValue_.vec_int_;
    if (GetArena() != nullptr) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    _impl_.OneValue_.vec_int_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::design::VectorInt& NodeBaseValue::_internal_vec_int() const {
  return OneValue_case() == kVecInt ? *_impl_.OneValue_.vec_int_ : reinterpret_cast<::design::VectorInt&>(::design::_VectorInt_default_instance_);
}
inline const ::design::VectorInt& NodeBaseValue::vec_int() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.vec_int)
  return _internal_vec_int();
}
inline ::design::VectorInt* NodeBaseValue::unsafe_arena_release_vec_int() {
  // @@protoc_insertion_point(field_unsafe_arena_release:design.NodeBaseValue.vec_int)
  if (OneValue_case() == kVecInt) {
    clear_has_OneValue();
    auto* temp = _impl_.OneValue_.vec_int_;
    _impl_.OneValue_.vec_int_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void NodeBaseValue::unsafe_arena_set_allocated_vec_int(::design::VectorInt* value) {
  // We rely on the oneof clear method to free the earlier contents
  // of this oneof. We can directly use the pointer we're given to
  // set the new value.
  clear_OneValue();
  if (value) {
    set_has_vec_int();
    _impl_.OneValue_.vec_int_ = value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeBaseValue.vec_int)
}
inline ::design::VectorInt* NodeBaseValue::_internal_mutable_vec_int() {
  if (OneValue_case() != kVecInt) {
    clear_OneValue();
    set_has_vec_int();
    _impl_.OneValue_.vec_int_ =
        ::google::protobuf::Message::DefaultConstruct<::design::VectorInt>(GetArena());
  }
  return _impl_.OneValue_.vec_int_;
}
inline ::design::VectorInt* NodeBaseValue::mutable_vec_int() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::design::VectorInt* _msg = _internal_mutable_vec_int();
  // @@protoc_insertion_point(field_mutable:design.NodeBaseValue.vec_int)
  return _msg;
}

// .design.VectorDouble vec_double = 22;
inline bool NodeBaseValue::has_vec_double() const {
  return OneValue_case() == kVecDouble;
}
inline bool NodeBaseValue::_internal_has_vec_double() const {
  return OneValue_case() == kVecDouble;
}
inline void NodeBaseValue::set_has_vec_double() {
  _impl_._oneof_case_[0] = kVecDouble;
}
inline void NodeBaseValue::clear_vec_double() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kVecDouble) {
    if (GetArena() == nullptr) {
      delete _impl_.OneValue_.vec_double_;
    } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
      ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.OneValue_.vec_double_);
    }
    clear_has_OneValue();
  }
}
inline ::design::VectorDouble* NodeBaseValue::release_vec_double() {
  // @@protoc_insertion_point(field_release:design.NodeBaseValue.vec_double)
  if (OneValue_case() == kVecDouble) {
    clear_has_OneValue();
    auto* temp = _impl_.OneValue_.vec_double_;
    if (GetArena() != nullptr) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    _impl_.OneValue_.vec_double_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::design::VectorDouble& NodeBaseValue::_internal_vec_double() const {
  return OneValue_case() == kVecDouble ? *_impl_.OneValue_.vec_double_ : reinterpret_cast<::design::VectorDouble&>(::design::_VectorDouble_default_instance_);
}
inline const ::design::VectorDouble& NodeBaseValue::vec_double() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.vec_double)
  return _internal_vec_double();
}
inline ::design::VectorDouble* NodeBaseValue::unsafe_arena_release_vec_double() {
  // @@protoc_insertion_point(field_unsafe_arena_release:design.NodeBaseValue.vec_double)
  if (OneValue_case() == kVecDouble) {
    clear_has_OneValue();
    auto* temp = _impl_.OneValue_.vec_double_;
    _impl_.OneValue_.vec_double_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void NodeBaseValue::unsafe_arena_set_allocated_vec_double(::design::VectorDouble* value) {
  // We rely on the oneof clear method to free the earlier contents
  // of this oneof. We can directly use the pointer we're given to
  // set the new value.
  clear_OneValue();
  if (value) {
    set_has_vec_double();
    _impl_.OneValue_.vec_double_ = value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeBaseValue.vec_double)
}
inline ::design::VectorDouble* NodeBaseValue::_internal_mutable_vec_double() {
  if (OneValue_case() != kVecDouble) {
    clear_OneValue();
    set_has_vec_double();
    _impl_.OneValue_.vec_double_ =
        ::google::protobuf::Message::DefaultConstruct<::design::VectorDouble>(GetArena());
  }
  return _impl_.OneValue_.vec_double_;
}
inline ::design::VectorDouble* NodeBaseValue::mutable_vec_double() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::design::VectorDouble* _msg = _internal_mutable_vec_double();
  // @@protoc_insertion_point(field_mutable:design.NodeBaseValue.vec_double)
  return _msg;
}

// .design.VectorString vec_string = 23;
inline bool NodeBaseValue::has_vec_string() const {
  return OneValue_case() == kVecString;
}
inline bool NodeBaseValue::_internal_has_vec_string() const {
  return OneValue_case() == kVecString;
}
inline void NodeBaseValue::set_has_vec_string() {
  _impl_._oneof_case_[0] = kVecString;
}
inline void NodeBaseValue::clear_vec_string() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (OneValue_case() == kVecString) {
    if (GetArena() == nullptr) {
      delete _impl_.OneValue_.vec_string_;
    } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
      ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.OneValue_.vec_string_);
    }
    clear_has_OneValue();
  }
}
inline ::design::VectorString* NodeBaseValue::release_vec_string() {
  // @@protoc_insertion_point(field_release:design.NodeBaseValue.vec_string)
  if (OneValue_case() == kVecString) {
    clear_has_OneValue();
    auto* temp = _impl_.OneValue_.vec_string_;
    if (GetArena() != nullptr) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    _impl_.OneValue_.vec_string_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::design::VectorString& NodeBaseValue::_internal_vec_string() const {
  return OneValue_case() == kVecString ? *_impl_.OneValue_.vec_string_ : reinterpret_cast<::design::VectorString&>(::design::_VectorString_default_instance_);
}
inline const ::design::VectorString& NodeBaseValue::vec_string() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeBaseValue.vec_string)
  return _internal_vec_string();
}
inline ::design::VectorString* NodeBaseValue::unsafe_arena_release_vec_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:design.NodeBaseValue.vec_string)
  if (OneValue_case() == kVecString) {
    clear_has_OneValue();
    auto* temp = _impl_.OneValue_.vec_string_;
    _impl_.OneValue_.vec_string_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void NodeBaseValue::unsafe_arena_set_allocated_vec_string(::design::VectorString* value) {
  // We rely on the oneof clear method to free the earlier contents
  // of this oneof. We can directly use the pointer we're given to
  // set the new value.
  clear_OneValue();
  if (value) {
    set_has_vec_string();
    _impl_.OneValue_.vec_string_ = value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeBaseValue.vec_string)
}
inline ::design::VectorString* NodeBaseValue::_internal_mutable_vec_string() {
  if (OneValue_case() != kVecString) {
    clear_OneValue();
    set_has_vec_string();
    _impl_.OneValue_.vec_string_ =
        ::google::protobuf::Message::DefaultConstruct<::design::VectorString>(GetArena());
  }
  return _impl_.OneValue_.vec_string_;
}
inline ::design::VectorString* NodeBaseValue::mutable_vec_string() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::design::VectorString* _msg = _internal_mutable_vec_string();
  // @@protoc_insertion_point(field_mutable:design.NodeBaseValue.vec_string)
  return _msg;
}

inline bool NodeBaseValue::has_OneValue() const {
  return OneValue_case() != ONEVALUE_NOT_SET;
}
inline void NodeBaseValue::clear_has_OneValue() {
  _impl_._oneof_case_[0] = ONEVALUE_NOT_SET;
}
inline NodeBaseValue::OneValueCase NodeBaseValue::OneValue_case() const {
  return NodeBaseValue::OneValueCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// ValInt

// int32 val_int = 1;
inline void ValInt::clear_val_int() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.val_int_ = 0;
}
inline ::int32_t ValInt::val_int() const {
  // @@protoc_insertion_point(field_get:design.ValInt.val_int)
  return _internal_val_int();
}
inline void ValInt::set_val_int(::int32_t value) {
  _internal_set_val_int(value);
  // @@protoc_insertion_point(field_set:design.ValInt.val_int)
}
inline ::int32_t ValInt::_internal_val_int() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.val_int_;
}
inline void ValInt::_internal_set_val_int(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.val_int_ = value;
}

// -------------------------------------------------------------------

// DVec4

// double x = 1;
inline void DVec4::clear_x() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.x_ = 0;
}
inline double DVec4::x() const {
  // @@protoc_insertion_point(field_get:design.DVec4.x)
  return _internal_x();
}
inline void DVec4::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:design.DVec4.x)
}
inline double DVec4::_internal_x() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.x_;
}
inline void DVec4::_internal_set_x(double value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.x_ = value;
}

// double y = 2;
inline void DVec4::clear_y() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.y_ = 0;
}
inline double DVec4::y() const {
  // @@protoc_insertion_point(field_get:design.DVec4.y)
  return _internal_y();
}
inline void DVec4::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:design.DVec4.y)
}
inline double DVec4::_internal_y() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.y_;
}
inline void DVec4::_internal_set_y(double value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.y_ = value;
}

// double z = 3;
inline void DVec4::clear_z() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.z_ = 0;
}
inline double DVec4::z() const {
  // @@protoc_insertion_point(field_get:design.DVec4.z)
  return _internal_z();
}
inline void DVec4::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:design.DVec4.z)
}
inline double DVec4::_internal_z() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.z_;
}
inline void DVec4::_internal_set_z(double value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.z_ = value;
}

// double w = 4;
inline void DVec4::clear_w() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.w_ = 0;
}
inline double DVec4::w() const {
  // @@protoc_insertion_point(field_get:design.DVec4.w)
  return _internal_w();
}
inline void DVec4::set_w(double value) {
  _internal_set_w(value);
  // @@protoc_insertion_point(field_set:design.DVec4.w)
}
inline double DVec4::_internal_w() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.w_;
}
inline void DVec4::_internal_set_w(double value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.w_ = value;
}

// -------------------------------------------------------------------

// VectorDouble

// repeated double vec_double = 1 [packed = true];
inline int VectorDouble::_internal_vec_double_size() const {
  return _internal_vec_double().size();
}
inline int VectorDouble::vec_double_size() const {
  return _internal_vec_double_size();
}
inline void VectorDouble::clear_vec_double() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.vec_double_.Clear();
}
inline double VectorDouble::vec_double(int index) const {
  // @@protoc_insertion_point(field_get:design.VectorDouble.vec_double)
  return _internal_vec_double().Get(index);
}
inline void VectorDouble::set_vec_double(int index, double value) {
  _internal_mutable_vec_double()->Set(index, value);
  // @@protoc_insertion_point(field_set:design.VectorDouble.vec_double)
}
inline void VectorDouble::add_vec_double(double value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _internal_mutable_vec_double()->Add(value);
  // @@protoc_insertion_point(field_add:design.VectorDouble.vec_double)
}
inline const ::google::protobuf::RepeatedField<double>& VectorDouble::vec_double() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.VectorDouble.vec_double)
  return _internal_vec_double();
}
inline ::google::protobuf::RepeatedField<double>* VectorDouble::mutable_vec_double()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.VectorDouble.vec_double)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_vec_double();
}
inline const ::google::protobuf::RepeatedField<double>&
VectorDouble::_internal_vec_double() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.vec_double_;
}
inline ::google::protobuf::RepeatedField<double>* VectorDouble::_internal_mutable_vec_double() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.vec_double_;
}

// -------------------------------------------------------------------

// VectorInt

// repeated int32 vec_int = 1 [packed = true];
inline int VectorInt::_internal_vec_int_size() const {
  return _internal_vec_int().size();
}
inline int VectorInt::vec_int_size() const {
  return _internal_vec_int_size();
}
inline void VectorInt::clear_vec_int() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.vec_int_.Clear();
}
inline ::int32_t VectorInt::vec_int(int index) const {
  // @@protoc_insertion_point(field_get:design.VectorInt.vec_int)
  return _internal_vec_int().Get(index);
}
inline void VectorInt::set_vec_int(int index, ::int32_t value) {
  _internal_mutable_vec_int()->Set(index, value);
  // @@protoc_insertion_point(field_set:design.VectorInt.vec_int)
}
inline void VectorInt::add_vec_int(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _internal_mutable_vec_int()->Add(value);
  // @@protoc_insertion_point(field_add:design.VectorInt.vec_int)
}
inline const ::google::protobuf::RepeatedField<::int32_t>& VectorInt::vec_int() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.VectorInt.vec_int)
  return _internal_vec_int();
}
inline ::google::protobuf::RepeatedField<::int32_t>* VectorInt::mutable_vec_int()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.VectorInt.vec_int)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_vec_int();
}
inline const ::google::protobuf::RepeatedField<::int32_t>&
VectorInt::_internal_vec_int() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.vec_int_;
}
inline ::google::protobuf::RepeatedField<::int32_t>* VectorInt::_internal_mutable_vec_int() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.vec_int_;
}

// -------------------------------------------------------------------

// VectorInt64

// repeated int64 vec_int64 = 1 [packed = true];
inline int VectorInt64::_internal_vec_int64_size() const {
  return _internal_vec_int64().size();
}
inline int VectorInt64::vec_int64_size() const {
  return _internal_vec_int64_size();
}
inline void VectorInt64::clear_vec_int64() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.vec_int64_.Clear();
}
inline ::int64_t VectorInt64::vec_int64(int index) const {
  // @@protoc_insertion_point(field_get:design.VectorInt64.vec_int64)
  return _internal_vec_int64().Get(index);
}
inline void VectorInt64::set_vec_int64(int index, ::int64_t value) {
  _internal_mutable_vec_int64()->Set(index, value);
  // @@protoc_insertion_point(field_set:design.VectorInt64.vec_int64)
}
inline void VectorInt64::add_vec_int64(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _internal_mutable_vec_int64()->Add(value);
  // @@protoc_insertion_point(field_add:design.VectorInt64.vec_int64)
}
inline const ::google::protobuf::RepeatedField<::int64_t>& VectorInt64::vec_int64() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.VectorInt64.vec_int64)
  return _internal_vec_int64();
}
inline ::google::protobuf::RepeatedField<::int64_t>* VectorInt64::mutable_vec_int64()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.VectorInt64.vec_int64)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_vec_int64();
}
inline const ::google::protobuf::RepeatedField<::int64_t>&
VectorInt64::_internal_vec_int64() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.vec_int64_;
}
inline ::google::protobuf::RepeatedField<::int64_t>* VectorInt64::_internal_mutable_vec_int64() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.vec_int64_;
}

// -------------------------------------------------------------------

// SVecInt

// repeated sint32 vec_sint = 1 [packed = true];
inline int SVecInt::_internal_vec_sint_size() const {
  return _internal_vec_sint().size();
}
inline int SVecInt::vec_sint_size() const {
  return _internal_vec_sint_size();
}
inline void SVecInt::clear_vec_sint() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.vec_sint_.Clear();
}
inline ::int32_t SVecInt::vec_sint(int index) const {
  // @@protoc_insertion_point(field_get:design.SVecInt.vec_sint)
  return _internal_vec_sint().Get(index);
}
inline void SVecInt::set_vec_sint(int index, ::int32_t value) {
  _internal_mutable_vec_sint()->Set(index, value);
  // @@protoc_insertion_point(field_set:design.SVecInt.vec_sint)
}
inline void SVecInt::add_vec_sint(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _internal_mutable_vec_sint()->Add(value);
  // @@protoc_insertion_point(field_add:design.SVecInt.vec_sint)
}
inline const ::google::protobuf::RepeatedField<::int32_t>& SVecInt::vec_sint() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.SVecInt.vec_sint)
  return _internal_vec_sint();
}
inline ::google::protobuf::RepeatedField<::int32_t>* SVecInt::mutable_vec_sint()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.SVecInt.vec_sint)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_vec_sint();
}
inline const ::google::protobuf::RepeatedField<::int32_t>&
SVecInt::_internal_vec_sint() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.vec_sint_;
}
inline ::google::protobuf::RepeatedField<::int32_t>* SVecInt::_internal_mutable_vec_sint() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.vec_sint_;
}

// -------------------------------------------------------------------

// VectorString

// repeated bytes vec_string = 1;
inline int VectorString::_internal_vec_string_size() const {
  return _internal_vec_string().size();
}
inline int VectorString::vec_string_size() const {
  return _internal_vec_string_size();
}
inline void VectorString::clear_vec_string() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.vec_string_.Clear();
}
inline std::string* VectorString::add_vec_string() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  std::string* _s = _internal_mutable_vec_string()->Add();
  // @@protoc_insertion_point(field_add_mutable:design.VectorString.vec_string)
  return _s;
}
inline const std::string& VectorString::vec_string(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.VectorString.vec_string)
  return _internal_vec_string().Get(index);
}
inline std::string* VectorString::mutable_vec_string(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:design.VectorString.vec_string)
  return _internal_mutable_vec_string()->Mutable(index);
}
template <typename Arg_, typename... Args_>
inline void VectorString::set_vec_string(int index, Arg_&& value, Args_... args) {
  ::google::protobuf::internal::AssignToString(
      *_internal_mutable_vec_string()->Mutable(index),
      std::forward<Arg_>(value), args... , ::google::protobuf::internal::BytesTag{});
  // @@protoc_insertion_point(field_set:design.VectorString.vec_string)
}
template <typename Arg_, typename... Args_>
inline void VectorString::add_vec_string(Arg_&& value, Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::google::protobuf::internal::AddToRepeatedPtrField(*_internal_mutable_vec_string(),
                               std::forward<Arg_>(value),
                               args... , ::google::protobuf::internal::BytesTag{});
  // @@protoc_insertion_point(field_add:design.VectorString.vec_string)
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
VectorString::vec_string() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.VectorString.vec_string)
  return _internal_vec_string();
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
VectorString::mutable_vec_string() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.VectorString.vec_string)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_vec_string();
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
VectorString::_internal_vec_string() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.vec_string_;
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
VectorString::_internal_mutable_vec_string() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.vec_string_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// StringIntegerMap

// map<string, int64> map = 1;
inline int StringIntegerMap::_internal_map_size() const {
  return _internal_map().size();
}
inline int StringIntegerMap::map_size() const {
  return _internal_map_size();
}
inline void StringIntegerMap::clear_map() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.map_.Clear();
}
inline const ::google::protobuf::Map<std::string, ::int64_t>& StringIntegerMap::_internal_map() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.map_.GetMap();
}
inline const ::google::protobuf::Map<std::string, ::int64_t>& StringIntegerMap::map() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_map:design.StringIntegerMap.map)
  return _internal_map();
}
inline ::google::protobuf::Map<std::string, ::int64_t>* StringIntegerMap::_internal_mutable_map() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.map_.MutableMap();
}
inline ::google::protobuf::Map<std::string, ::int64_t>* StringIntegerMap::mutable_map() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_map:design.StringIntegerMap.map)
  return _internal_mutable_map();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// NodeAttrs

// map<int64, .design.NodeBaseValue> attr_map = 1;
inline int NodeAttrs::_internal_attr_map_size() const {
  return _internal_attr_map().size();
}
inline int NodeAttrs::attr_map_size() const {
  return _internal_attr_map_size();
}
inline void NodeAttrs::clear_attr_map() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.attr_map_.Clear();
}
inline const ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>& NodeAttrs::_internal_attr_map() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.attr_map_.GetMap();
}
inline const ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>& NodeAttrs::attr_map() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_map:design.NodeAttrs.attr_map)
  return _internal_attr_map();
}
inline ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>* NodeAttrs::_internal_mutable_attr_map() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.attr_map_.MutableMap();
}
inline ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>* NodeAttrs::mutable_attr_map() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_map:design.NodeAttrs.attr_map)
  return _internal_mutable_attr_map();
}

// -------------------------------------------------------------------

// StrIntRecord

// int64 id = 1;
inline void StrIntRecord::clear_id() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = ::int64_t{0};
}
inline ::int64_t StrIntRecord::id() const {
  // @@protoc_insertion_point(field_get:design.StrIntRecord.id)
  return _internal_id();
}
inline void StrIntRecord::set_id(::int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:design.StrIntRecord.id)
}
inline ::int64_t StrIntRecord::_internal_id() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.id_;
}
inline void StrIntRecord::_internal_set_id(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = value;
}

// bytes str = 2;
inline void StrIntRecord::clear_str() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.str_.ClearToEmpty();
}
inline const std::string& StrIntRecord::str() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.StrIntRecord.str)
  return _internal_str();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void StrIntRecord::set_str(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.str_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.StrIntRecord.str)
}
inline std::string* StrIntRecord::mutable_str() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_str();
  // @@protoc_insertion_point(field_mutable:design.StrIntRecord.str)
  return _s;
}
inline const std::string& StrIntRecord::_internal_str() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.str_.Get();
}
inline void StrIntRecord::_internal_set_str(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.str_.Set(value, GetArena());
}
inline std::string* StrIntRecord::_internal_mutable_str() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.str_.Mutable( GetArena());
}
inline std::string* StrIntRecord::release_str() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.StrIntRecord.str)
  return _impl_.str_.Release();
}
inline void StrIntRecord::set_allocated_str(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.str_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.str_.IsDefault()) {
    _impl_.str_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.StrIntRecord.str)
}

// -------------------------------------------------------------------

// NodeAttrsRecord

// int64 id = 1;
inline void NodeAttrsRecord::clear_id() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = ::int64_t{0};
}
inline ::int64_t NodeAttrsRecord::id() const {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.id)
  return _internal_id();
}
inline void NodeAttrsRecord::set_id(::int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:design.NodeAttrsRecord.id)
}
inline ::int64_t NodeAttrsRecord::_internal_id() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.id_;
}
inline void NodeAttrsRecord::_internal_set_id(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = value;
}

// bytes name = 2;
inline void NodeAttrsRecord::clear_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.ClearToEmpty();
}
inline const std::string& NodeAttrsRecord::name() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.name)
  return _internal_name();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void NodeAttrsRecord::set_name(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.NodeAttrsRecord.name)
}
inline std::string* NodeAttrsRecord::mutable_name() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:design.NodeAttrsRecord.name)
  return _s;
}
inline const std::string& NodeAttrsRecord::_internal_name() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.name_.Get();
}
inline void NodeAttrsRecord::_internal_set_name(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.Set(value, GetArena());
}
inline std::string* NodeAttrsRecord::_internal_mutable_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.name_.Mutable( GetArena());
}
inline std::string* NodeAttrsRecord::release_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeAttrsRecord.name)
  return _impl_.name_.Release();
}
inline void NodeAttrsRecord::set_allocated_name(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.NodeAttrsRecord.name)
}

// int32 type = 3;
inline void NodeAttrsRecord::clear_type() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.type_ = 0;
}
inline ::int32_t NodeAttrsRecord::type() const {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.type)
  return _internal_type();
}
inline void NodeAttrsRecord::set_type(::int32_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:design.NodeAttrsRecord.type)
}
inline ::int32_t NodeAttrsRecord::_internal_type() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.type_;
}
inline void NodeAttrsRecord::_internal_set_type(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.type_ = value;
}

// .design.NodeAttrs attrs = 4;
inline bool NodeAttrsRecord::has_attrs() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.attrs_ != nullptr);
  return value;
}
inline void NodeAttrsRecord::clear_attrs() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.attrs_ != nullptr) _impl_.attrs_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::NodeAttrs& NodeAttrsRecord::_internal_attrs() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::NodeAttrs* p = _impl_.attrs_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::NodeAttrs&>(::design::_NodeAttrs_default_instance_);
}
inline const ::design::NodeAttrs& NodeAttrsRecord::attrs() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.attrs)
  return _internal_attrs();
}
inline void NodeAttrsRecord::unsafe_arena_set_allocated_attrs(::design::NodeAttrs* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.attrs_);
  }
  _impl_.attrs_ = reinterpret_cast<::design::NodeAttrs*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeAttrsRecord.attrs)
}
inline ::design::NodeAttrs* NodeAttrsRecord::release_attrs() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::NodeAttrs* released = _impl_.attrs_;
  _impl_.attrs_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::NodeAttrs* NodeAttrsRecord::unsafe_arena_release_attrs() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeAttrsRecord.attrs)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::NodeAttrs* temp = _impl_.attrs_;
  _impl_.attrs_ = nullptr;
  return temp;
}
inline ::design::NodeAttrs* NodeAttrsRecord::_internal_mutable_attrs() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.attrs_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::NodeAttrs>(GetArena());
    _impl_.attrs_ = reinterpret_cast<::design::NodeAttrs*>(p);
  }
  return _impl_.attrs_;
}
inline ::design::NodeAttrs* NodeAttrsRecord::mutable_attrs() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::NodeAttrs* _msg = _internal_mutable_attrs();
  // @@protoc_insertion_point(field_mutable:design.NodeAttrsRecord.attrs)
  return _msg;
}
inline void NodeAttrsRecord::set_allocated_attrs(::design::NodeAttrs* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.attrs_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.attrs_ = reinterpret_cast<::design::NodeAttrs*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.NodeAttrsRecord.attrs)
}

// int64 traceId = 5;
inline void NodeAttrsRecord::clear_traceid() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.traceid_ = ::int64_t{0};
}
inline ::int64_t NodeAttrsRecord::traceid() const {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.traceId)
  return _internal_traceid();
}
inline void NodeAttrsRecord::set_traceid(::int64_t value) {
  _internal_set_traceid(value);
  // @@protoc_insertion_point(field_set:design.NodeAttrsRecord.traceId)
}
inline ::int64_t NodeAttrsRecord::_internal_traceid() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.traceid_;
}
inline void NodeAttrsRecord::_internal_set_traceid(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.traceid_ = value;
}

// int32 action = 6;
inline void NodeAttrsRecord::clear_action() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.action_ = 0;
}
inline ::int32_t NodeAttrsRecord::action() const {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.action)
  return _internal_action();
}
inline void NodeAttrsRecord::set_action(::int32_t value) {
  _internal_set_action(value);
  // @@protoc_insertion_point(field_set:design.NodeAttrsRecord.action)
}
inline ::int32_t NodeAttrsRecord::_internal_action() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.action_;
}
inline void NodeAttrsRecord::_internal_set_action(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.action_ = value;
}

// bytes additionalJSON = 7;
inline void NodeAttrsRecord::clear_additionaljson() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.ClearToEmpty();
}
inline const std::string& NodeAttrsRecord::additionaljson() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.additionalJSON)
  return _internal_additionaljson();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void NodeAttrsRecord::set_additionaljson(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.NodeAttrsRecord.additionalJSON)
}
inline std::string* NodeAttrsRecord::mutable_additionaljson() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_additionaljson();
  // @@protoc_insertion_point(field_mutable:design.NodeAttrsRecord.additionalJSON)
  return _s;
}
inline const std::string& NodeAttrsRecord::_internal_additionaljson() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.additionaljson_.Get();
}
inline void NodeAttrsRecord::_internal_set_additionaljson(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.Set(value, GetArena());
}
inline std::string* NodeAttrsRecord::_internal_mutable_additionaljson() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.additionaljson_.Mutable( GetArena());
}
inline std::string* NodeAttrsRecord::release_additionaljson() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeAttrsRecord.additionalJSON)
  return _impl_.additionaljson_.Release();
}
inline void NodeAttrsRecord::set_allocated_additionaljson(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.additionaljson_.IsDefault()) {
    _impl_.additionaljson_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.NodeAttrsRecord.additionalJSON)
}

// .design.AdditionalInfo additionalInfo = 8;
inline bool NodeAttrsRecord::has_additionalinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.additionalinfo_ != nullptr);
  return value;
}
inline void NodeAttrsRecord::clear_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.additionalinfo_ != nullptr) _impl_.additionalinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::design::AdditionalInfo& NodeAttrsRecord::_internal_additionalinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::AdditionalInfo* p = _impl_.additionalinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::AdditionalInfo&>(::design::_AdditionalInfo_default_instance_);
}
inline const ::design::AdditionalInfo& NodeAttrsRecord::additionalinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeAttrsRecord.additionalInfo)
  return _internal_additionalinfo();
}
inline void NodeAttrsRecord::unsafe_arena_set_allocated_additionalinfo(::design::AdditionalInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.additionalinfo_);
  }
  _impl_.additionalinfo_ = reinterpret_cast<::design::AdditionalInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeAttrsRecord.additionalInfo)
}
inline ::design::AdditionalInfo* NodeAttrsRecord::release_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::design::AdditionalInfo* released = _impl_.additionalinfo_;
  _impl_.additionalinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::AdditionalInfo* NodeAttrsRecord::unsafe_arena_release_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeAttrsRecord.additionalInfo)

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::design::AdditionalInfo* temp = _impl_.additionalinfo_;
  _impl_.additionalinfo_ = nullptr;
  return temp;
}
inline ::design::AdditionalInfo* NodeAttrsRecord::_internal_mutable_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.additionalinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::AdditionalInfo>(GetArena());
    _impl_.additionalinfo_ = reinterpret_cast<::design::AdditionalInfo*>(p);
  }
  return _impl_.additionalinfo_;
}
inline ::design::AdditionalInfo* NodeAttrsRecord::mutable_additionalinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000002u;
  ::design::AdditionalInfo* _msg = _internal_mutable_additionalinfo();
  // @@protoc_insertion_point(field_mutable:design.NodeAttrsRecord.additionalInfo)
  return _msg;
}
inline void NodeAttrsRecord::set_allocated_additionalinfo(::design::AdditionalInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.additionalinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }

  _impl_.additionalinfo_ = reinterpret_cast<::design::AdditionalInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.NodeAttrsRecord.additionalInfo)
}

// -------------------------------------------------------------------

// NodeTreeRecord

// int64 id = 1;
inline void NodeTreeRecord::clear_id() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = ::int64_t{0};
}
inline ::int64_t NodeTreeRecord::id() const {
  // @@protoc_insertion_point(field_get:design.NodeTreeRecord.id)
  return _internal_id();
}
inline void NodeTreeRecord::set_id(::int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:design.NodeTreeRecord.id)
}
inline ::int64_t NodeTreeRecord::_internal_id() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.id_;
}
inline void NodeTreeRecord::_internal_set_id(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = value;
}

// .design.VectorInt64 children = 2;
inline bool NodeTreeRecord::has_children() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.children_ != nullptr);
  return value;
}
inline void NodeTreeRecord::clear_children() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.children_ != nullptr) _impl_.children_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::VectorInt64& NodeTreeRecord::_internal_children() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::VectorInt64* p = _impl_.children_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::VectorInt64&>(::design::_VectorInt64_default_instance_);
}
inline const ::design::VectorInt64& NodeTreeRecord::children() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeTreeRecord.children)
  return _internal_children();
}
inline void NodeTreeRecord::unsafe_arena_set_allocated_children(::design::VectorInt64* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.children_);
  }
  _impl_.children_ = reinterpret_cast<::design::VectorInt64*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeTreeRecord.children)
}
inline ::design::VectorInt64* NodeTreeRecord::release_children() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::VectorInt64* released = _impl_.children_;
  _impl_.children_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::VectorInt64* NodeTreeRecord::unsafe_arena_release_children() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeTreeRecord.children)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::VectorInt64* temp = _impl_.children_;
  _impl_.children_ = nullptr;
  return temp;
}
inline ::design::VectorInt64* NodeTreeRecord::_internal_mutable_children() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.children_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::VectorInt64>(GetArena());
    _impl_.children_ = reinterpret_cast<::design::VectorInt64*>(p);
  }
  return _impl_.children_;
}
inline ::design::VectorInt64* NodeTreeRecord::mutable_children() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::VectorInt64* _msg = _internal_mutable_children();
  // @@protoc_insertion_point(field_mutable:design.NodeTreeRecord.children)
  return _msg;
}
inline void NodeTreeRecord::set_allocated_children(::design::VectorInt64* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.children_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.children_ = reinterpret_cast<::design::VectorInt64*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.NodeTreeRecord.children)
}

// int64 traceId = 3;
inline void NodeTreeRecord::clear_traceid() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.traceid_ = ::int64_t{0};
}
inline ::int64_t NodeTreeRecord::traceid() const {
  // @@protoc_insertion_point(field_get:design.NodeTreeRecord.traceId)
  return _internal_traceid();
}
inline void NodeTreeRecord::set_traceid(::int64_t value) {
  _internal_set_traceid(value);
  // @@protoc_insertion_point(field_set:design.NodeTreeRecord.traceId)
}
inline ::int64_t NodeTreeRecord::_internal_traceid() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.traceid_;
}
inline void NodeTreeRecord::_internal_set_traceid(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.traceid_ = value;
}

// bytes additionalJSON = 4;
inline void NodeTreeRecord::clear_additionaljson() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.ClearToEmpty();
}
inline const std::string& NodeTreeRecord::additionaljson() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeTreeRecord.additionalJSON)
  return _internal_additionaljson();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void NodeTreeRecord::set_additionaljson(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.NodeTreeRecord.additionalJSON)
}
inline std::string* NodeTreeRecord::mutable_additionaljson() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_additionaljson();
  // @@protoc_insertion_point(field_mutable:design.NodeTreeRecord.additionalJSON)
  return _s;
}
inline const std::string& NodeTreeRecord::_internal_additionaljson() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.additionaljson_.Get();
}
inline void NodeTreeRecord::_internal_set_additionaljson(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.Set(value, GetArena());
}
inline std::string* NodeTreeRecord::_internal_mutable_additionaljson() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.additionaljson_.Mutable( GetArena());
}
inline std::string* NodeTreeRecord::release_additionaljson() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeTreeRecord.additionalJSON)
  return _impl_.additionaljson_.Release();
}
inline void NodeTreeRecord::set_allocated_additionaljson(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.additionaljson_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.additionaljson_.IsDefault()) {
    _impl_.additionaljson_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.NodeTreeRecord.additionalJSON)
}

// .design.AdditionalInfo additionalInfo = 5;
inline bool NodeTreeRecord::has_additionalinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.additionalinfo_ != nullptr);
  return value;
}
inline void NodeTreeRecord::clear_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.additionalinfo_ != nullptr) _impl_.additionalinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::design::AdditionalInfo& NodeTreeRecord::_internal_additionalinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::AdditionalInfo* p = _impl_.additionalinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::AdditionalInfo&>(::design::_AdditionalInfo_default_instance_);
}
inline const ::design::AdditionalInfo& NodeTreeRecord::additionalinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeTreeRecord.additionalInfo)
  return _internal_additionalinfo();
}
inline void NodeTreeRecord::unsafe_arena_set_allocated_additionalinfo(::design::AdditionalInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.additionalinfo_);
  }
  _impl_.additionalinfo_ = reinterpret_cast<::design::AdditionalInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeTreeRecord.additionalInfo)
}
inline ::design::AdditionalInfo* NodeTreeRecord::release_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::design::AdditionalInfo* released = _impl_.additionalinfo_;
  _impl_.additionalinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::AdditionalInfo* NodeTreeRecord::unsafe_arena_release_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeTreeRecord.additionalInfo)

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::design::AdditionalInfo* temp = _impl_.additionalinfo_;
  _impl_.additionalinfo_ = nullptr;
  return temp;
}
inline ::design::AdditionalInfo* NodeTreeRecord::_internal_mutable_additionalinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.additionalinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::AdditionalInfo>(GetArena());
    _impl_.additionalinfo_ = reinterpret_cast<::design::AdditionalInfo*>(p);
  }
  return _impl_.additionalinfo_;
}
inline ::design::AdditionalInfo* NodeTreeRecord::mutable_additionalinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000002u;
  ::design::AdditionalInfo* _msg = _internal_mutable_additionalinfo();
  // @@protoc_insertion_point(field_mutable:design.NodeTreeRecord.additionalInfo)
  return _msg;
}
inline void NodeTreeRecord::set_allocated_additionalinfo(::design::AdditionalInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.additionalinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }

  _impl_.additionalinfo_ = reinterpret_cast<::design::AdditionalInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.NodeTreeRecord.additionalInfo)
}

// -------------------------------------------------------------------

// AdditionalInfo

// string user = 1;
inline void AdditionalInfo::clear_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.ClearToEmpty();
}
inline const std::string& AdditionalInfo::user() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.AdditionalInfo.user)
  return _internal_user();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void AdditionalInfo::set_user(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.AdditionalInfo.user)
}
inline std::string* AdditionalInfo::mutable_user() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_user();
  // @@protoc_insertion_point(field_mutable:design.AdditionalInfo.user)
  return _s;
}
inline const std::string& AdditionalInfo::_internal_user() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.user_.Get();
}
inline void AdditionalInfo::_internal_set_user(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.Set(value, GetArena());
}
inline std::string* AdditionalInfo::_internal_mutable_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.user_.Mutable( GetArena());
}
inline std::string* AdditionalInfo::release_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.AdditionalInfo.user)
  return _impl_.user_.Release();
}
inline void AdditionalInfo::set_allocated_user(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.user_.IsDefault()) {
    _impl_.user_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.AdditionalInfo.user)
}

// int64 time = 2;
inline void AdditionalInfo::clear_time() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.time_ = ::int64_t{0};
}
inline ::int64_t AdditionalInfo::time() const {
  // @@protoc_insertion_point(field_get:design.AdditionalInfo.time)
  return _internal_time();
}
inline void AdditionalInfo::set_time(::int64_t value) {
  _internal_set_time(value);
  // @@protoc_insertion_point(field_set:design.AdditionalInfo.time)
}
inline ::int64_t AdditionalInfo::_internal_time() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.time_;
}
inline void AdditionalInfo::_internal_set_time(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.time_ = value;
}

// int32 checkOut = 3;
inline void AdditionalInfo::clear_checkout() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.checkout_ = 0;
}
inline ::int32_t AdditionalInfo::checkout() const {
  // @@protoc_insertion_point(field_get:design.AdditionalInfo.checkOut)
  return _internal_checkout();
}
inline void AdditionalInfo::set_checkout(::int32_t value) {
  _internal_set_checkout(value);
  // @@protoc_insertion_point(field_set:design.AdditionalInfo.checkOut)
}
inline ::int32_t AdditionalInfo::_internal_checkout() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.checkout_;
}
inline void AdditionalInfo::_internal_set_checkout(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.checkout_ = value;
}

// string checkOutUser = 4;
inline void AdditionalInfo::clear_checkoutuser() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.checkoutuser_.ClearToEmpty();
}
inline const std::string& AdditionalInfo::checkoutuser() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.AdditionalInfo.checkOutUser)
  return _internal_checkoutuser();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void AdditionalInfo::set_checkoutuser(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.checkoutuser_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.AdditionalInfo.checkOutUser)
}
inline std::string* AdditionalInfo::mutable_checkoutuser() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_checkoutuser();
  // @@protoc_insertion_point(field_mutable:design.AdditionalInfo.checkOutUser)
  return _s;
}
inline const std::string& AdditionalInfo::_internal_checkoutuser() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.checkoutuser_.Get();
}
inline void AdditionalInfo::_internal_set_checkoutuser(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.checkoutuser_.Set(value, GetArena());
}
inline std::string* AdditionalInfo::_internal_mutable_checkoutuser() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.checkoutuser_.Mutable( GetArena());
}
inline std::string* AdditionalInfo::release_checkoutuser() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.AdditionalInfo.checkOutUser)
  return _impl_.checkoutuser_.Release();
}
inline void AdditionalInfo::set_allocated_checkoutuser(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.checkoutuser_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.checkoutuser_.IsDefault()) {
    _impl_.checkoutuser_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.AdditionalInfo.checkOutUser)
}

// int32 status = 5;
inline void AdditionalInfo::clear_status() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.status_ = 0;
}
inline ::int32_t AdditionalInfo::status() const {
  // @@protoc_insertion_point(field_get:design.AdditionalInfo.status)
  return _internal_status();
}
inline void AdditionalInfo::set_status(::int32_t value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:design.AdditionalInfo.status)
}
inline ::int32_t AdditionalInfo::_internal_status() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.status_;
}
inline void AdditionalInfo::_internal_set_status(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.status_ = value;
}

// -------------------------------------------------------------------

// OffsetLength

// int64 offset = 1;
inline void OffsetLength::clear_offset() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.offset_ = ::int64_t{0};
}
inline ::int64_t OffsetLength::offset() const {
  // @@protoc_insertion_point(field_get:design.OffsetLength.offset)
  return _internal_offset();
}
inline void OffsetLength::set_offset(::int64_t value) {
  _internal_set_offset(value);
  // @@protoc_insertion_point(field_set:design.OffsetLength.offset)
}
inline ::int64_t OffsetLength::_internal_offset() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.offset_;
}
inline void OffsetLength::_internal_set_offset(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.offset_ = value;
}

// int32 length = 2;
inline void OffsetLength::clear_length() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.length_ = 0;
}
inline ::int32_t OffsetLength::length() const {
  // @@protoc_insertion_point(field_get:design.OffsetLength.length)
  return _internal_length();
}
inline void OffsetLength::set_length(::int32_t value) {
  _internal_set_length(value);
  // @@protoc_insertion_point(field_set:design.OffsetLength.length)
}
inline ::int32_t OffsetLength::_internal_length() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.length_;
}
inline void OffsetLength::_internal_set_length(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.length_ = value;
}

// uint32 crc = 3;
inline void OffsetLength::clear_crc() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.crc_ = 0u;
}
inline ::uint32_t OffsetLength::crc() const {
  // @@protoc_insertion_point(field_get:design.OffsetLength.crc)
  return _internal_crc();
}
inline void OffsetLength::set_crc(::uint32_t value) {
  _internal_set_crc(value);
  // @@protoc_insertion_point(field_set:design.OffsetLength.crc)
}
inline ::uint32_t OffsetLength::_internal_crc() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.crc_;
}
inline void OffsetLength::_internal_set_crc(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.crc_ = value;
}

// -------------------------------------------------------------------

// NodeOffsetRecord

// int32 node_id = 1;
inline void NodeOffsetRecord::clear_node_id() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.node_id_ = 0;
}
inline ::int32_t NodeOffsetRecord::node_id() const {
  // @@protoc_insertion_point(field_get:design.NodeOffsetRecord.node_id)
  return _internal_node_id();
}
inline void NodeOffsetRecord::set_node_id(::int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:design.NodeOffsetRecord.node_id)
}
inline ::int32_t NodeOffsetRecord::_internal_node_id() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.node_id_;
}
inline void NodeOffsetRecord::_internal_set_node_id(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.node_id_ = value;
}

// .design.OffsetLength off_len = 2;
inline bool NodeOffsetRecord::has_off_len() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.off_len_ != nullptr);
  return value;
}
inline void NodeOffsetRecord::clear_off_len() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.off_len_ != nullptr) _impl_.off_len_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::OffsetLength& NodeOffsetRecord::_internal_off_len() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::OffsetLength* p = _impl_.off_len_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::OffsetLength&>(::design::_OffsetLength_default_instance_);
}
inline const ::design::OffsetLength& NodeOffsetRecord::off_len() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeOffsetRecord.off_len)
  return _internal_off_len();
}
inline void NodeOffsetRecord::unsafe_arena_set_allocated_off_len(::design::OffsetLength* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.off_len_);
  }
  _impl_.off_len_ = reinterpret_cast<::design::OffsetLength*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeOffsetRecord.off_len)
}
inline ::design::OffsetLength* NodeOffsetRecord::release_off_len() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::OffsetLength* released = _impl_.off_len_;
  _impl_.off_len_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::OffsetLength* NodeOffsetRecord::unsafe_arena_release_off_len() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeOffsetRecord.off_len)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::OffsetLength* temp = _impl_.off_len_;
  _impl_.off_len_ = nullptr;
  return temp;
}
inline ::design::OffsetLength* NodeOffsetRecord::_internal_mutable_off_len() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.off_len_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::OffsetLength>(GetArena());
    _impl_.off_len_ = reinterpret_cast<::design::OffsetLength*>(p);
  }
  return _impl_.off_len_;
}
inline ::design::OffsetLength* NodeOffsetRecord::mutable_off_len() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::OffsetLength* _msg = _internal_mutable_off_len();
  // @@protoc_insertion_point(field_mutable:design.NodeOffsetRecord.off_len)
  return _msg;
}
inline void NodeOffsetRecord::set_allocated_off_len(::design::OffsetLength* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.off_len_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.off_len_ = reinterpret_cast<::design::OffsetLength*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.NodeOffsetRecord.off_len)
}

// -------------------------------------------------------------------

// ProjectInfo

// string projectCode = 1;
inline void ProjectInfo::clear_projectcode() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.projectcode_.ClearToEmpty();
}
inline const std::string& ProjectInfo::projectcode() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectInfo.projectCode)
  return _internal_projectcode();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProjectInfo::set_projectcode(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.projectcode_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.ProjectInfo.projectCode)
}
inline std::string* ProjectInfo::mutable_projectcode() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_projectcode();
  // @@protoc_insertion_point(field_mutable:design.ProjectInfo.projectCode)
  return _s;
}
inline const std::string& ProjectInfo::_internal_projectcode() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.projectcode_.Get();
}
inline void ProjectInfo::_internal_set_projectcode(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.projectcode_.Set(value, GetArena());
}
inline std::string* ProjectInfo::_internal_mutable_projectcode() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.projectcode_.Mutable( GetArena());
}
inline std::string* ProjectInfo::release_projectcode() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectInfo.projectCode)
  return _impl_.projectcode_.Release();
}
inline void ProjectInfo::set_allocated_projectcode(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.projectcode_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.projectcode_.IsDefault()) {
    _impl_.projectcode_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.ProjectInfo.projectCode)
}

// string classification = 2;
inline void ProjectInfo::clear_classification() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.classification_.ClearToEmpty();
}
inline const std::string& ProjectInfo::classification() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectInfo.classification)
  return _internal_classification();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProjectInfo::set_classification(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.classification_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.ProjectInfo.classification)
}
inline std::string* ProjectInfo::mutable_classification() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_classification();
  // @@protoc_insertion_point(field_mutable:design.ProjectInfo.classification)
  return _s;
}
inline const std::string& ProjectInfo::_internal_classification() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.classification_.Get();
}
inline void ProjectInfo::_internal_set_classification(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.classification_.Set(value, GetArena());
}
inline std::string* ProjectInfo::_internal_mutable_classification() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.classification_.Mutable( GetArena());
}
inline std::string* ProjectInfo::release_classification() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectInfo.classification)
  return _impl_.classification_.Release();
}
inline void ProjectInfo::set_allocated_classification(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.classification_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.classification_.IsDefault()) {
    _impl_.classification_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.ProjectInfo.classification)
}

// string subTopic = 3;
inline void ProjectInfo::clear_subtopic() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.subtopic_.ClearToEmpty();
}
inline const std::string& ProjectInfo::subtopic() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectInfo.subTopic)
  return _internal_subtopic();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProjectInfo::set_subtopic(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.subtopic_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.ProjectInfo.subTopic)
}
inline std::string* ProjectInfo::mutable_subtopic() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_subtopic();
  // @@protoc_insertion_point(field_mutable:design.ProjectInfo.subTopic)
  return _s;
}
inline const std::string& ProjectInfo::_internal_subtopic() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.subtopic_.Get();
}
inline void ProjectInfo::_internal_set_subtopic(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.subtopic_.Set(value, GetArena());
}
inline std::string* ProjectInfo::_internal_mutable_subtopic() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.subtopic_.Mutable( GetArena());
}
inline std::string* ProjectInfo::release_subtopic() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectInfo.subTopic)
  return _impl_.subtopic_.Release();
}
inline void ProjectInfo::set_allocated_subtopic(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.subtopic_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.subtopic_.IsDefault()) {
    _impl_.subtopic_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.ProjectInfo.subTopic)
}

// string user = 4;
inline void ProjectInfo::clear_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.ClearToEmpty();
}
inline const std::string& ProjectInfo::user() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectInfo.user)
  return _internal_user();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProjectInfo::set_user(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.ProjectInfo.user)
}
inline std::string* ProjectInfo::mutable_user() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_user();
  // @@protoc_insertion_point(field_mutable:design.ProjectInfo.user)
  return _s;
}
inline const std::string& ProjectInfo::_internal_user() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.user_.Get();
}
inline void ProjectInfo::_internal_set_user(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.Set(value, GetArena());
}
inline std::string* ProjectInfo::_internal_mutable_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.user_.Mutable( GetArena());
}
inline std::string* ProjectInfo::release_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectInfo.user)
  return _impl_.user_.Release();
}
inline void ProjectInfo::set_allocated_user(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.user_.IsDefault()) {
    _impl_.user_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.ProjectInfo.user)
}

// int32 progressPercentage = 5;
inline void ProjectInfo::clear_progresspercentage() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.progresspercentage_ = 0;
}
inline ::int32_t ProjectInfo::progresspercentage() const {
  // @@protoc_insertion_point(field_get:design.ProjectInfo.progressPercentage)
  return _internal_progresspercentage();
}
inline void ProjectInfo::set_progresspercentage(::int32_t value) {
  _internal_set_progresspercentage(value);
  // @@protoc_insertion_point(field_set:design.ProjectInfo.progressPercentage)
}
inline ::int32_t ProjectInfo::_internal_progresspercentage() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.progresspercentage_;
}
inline void ProjectInfo::_internal_set_progresspercentage(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.progresspercentage_ = value;
}

// -------------------------------------------------------------------

// ProjectConfigInfo

// string json = 1;
inline void ProjectConfigInfo::clear_json() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.json_.ClearToEmpty();
}
inline const std::string& ProjectConfigInfo::json() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectConfigInfo.json)
  return _internal_json();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProjectConfigInfo::set_json(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.json_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.ProjectConfigInfo.json)
}
inline std::string* ProjectConfigInfo::mutable_json() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_json();
  // @@protoc_insertion_point(field_mutable:design.ProjectConfigInfo.json)
  return _s;
}
inline const std::string& ProjectConfigInfo::_internal_json() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.json_.Get();
}
inline void ProjectConfigInfo::_internal_set_json(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.json_.Set(value, GetArena());
}
inline std::string* ProjectConfigInfo::_internal_mutable_json() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.json_.Mutable( GetArena());
}
inline std::string* ProjectConfigInfo::release_json() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectConfigInfo.json)
  return _impl_.json_.Release();
}
inline void ProjectConfigInfo::set_allocated_json(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.json_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.json_.IsDefault()) {
    _impl_.json_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.ProjectConfigInfo.json)
}

// -------------------------------------------------------------------

// MessageQueuesPackage

// .design.NodeTreeRecord trees = 1;
inline bool MessageQueuesPackage::has_trees() const {
  return Record_case() == kTrees;
}
inline bool MessageQueuesPackage::_internal_has_trees() const {
  return Record_case() == kTrees;
}
inline void MessageQueuesPackage::set_has_trees() {
  _impl_._oneof_case_[0] = kTrees;
}
inline void MessageQueuesPackage::clear_trees() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (Record_case() == kTrees) {
    if (GetArena() == nullptr) {
      delete _impl_.Record_.trees_;
    } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
      ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.Record_.trees_);
    }
    clear_has_Record();
  }
}
inline ::design::NodeTreeRecord* MessageQueuesPackage::release_trees() {
  // @@protoc_insertion_point(field_release:design.MessageQueuesPackage.trees)
  if (Record_case() == kTrees) {
    clear_has_Record();
    auto* temp = _impl_.Record_.trees_;
    if (GetArena() != nullptr) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    _impl_.Record_.trees_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::design::NodeTreeRecord& MessageQueuesPackage::_internal_trees() const {
  return Record_case() == kTrees ? *_impl_.Record_.trees_ : reinterpret_cast<::design::NodeTreeRecord&>(::design::_NodeTreeRecord_default_instance_);
}
inline const ::design::NodeTreeRecord& MessageQueuesPackage::trees() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.MessageQueuesPackage.trees)
  return _internal_trees();
}
inline ::design::NodeTreeRecord* MessageQueuesPackage::unsafe_arena_release_trees() {
  // @@protoc_insertion_point(field_unsafe_arena_release:design.MessageQueuesPackage.trees)
  if (Record_case() == kTrees) {
    clear_has_Record();
    auto* temp = _impl_.Record_.trees_;
    _impl_.Record_.trees_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void MessageQueuesPackage::unsafe_arena_set_allocated_trees(::design::NodeTreeRecord* value) {
  // We rely on the oneof clear method to free the earlier contents
  // of this oneof. We can directly use the pointer we're given to
  // set the new value.
  clear_Record();
  if (value) {
    set_has_trees();
    _impl_.Record_.trees_ = value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.MessageQueuesPackage.trees)
}
inline ::design::NodeTreeRecord* MessageQueuesPackage::_internal_mutable_trees() {
  if (Record_case() != kTrees) {
    clear_Record();
    set_has_trees();
    _impl_.Record_.trees_ =
        ::google::protobuf::Message::DefaultConstruct<::design::NodeTreeRecord>(GetArena());
  }
  return _impl_.Record_.trees_;
}
inline ::design::NodeTreeRecord* MessageQueuesPackage::mutable_trees() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::design::NodeTreeRecord* _msg = _internal_mutable_trees();
  // @@protoc_insertion_point(field_mutable:design.MessageQueuesPackage.trees)
  return _msg;
}

// .design.NodeAttrsRecord nodes = 2;
inline bool MessageQueuesPackage::has_nodes() const {
  return Record_case() == kNodes;
}
inline bool MessageQueuesPackage::_internal_has_nodes() const {
  return Record_case() == kNodes;
}
inline void MessageQueuesPackage::set_has_nodes() {
  _impl_._oneof_case_[0] = kNodes;
}
inline void MessageQueuesPackage::clear_nodes() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (Record_case() == kNodes) {
    if (GetArena() == nullptr) {
      delete _impl_.Record_.nodes_;
    } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
      ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.Record_.nodes_);
    }
    clear_has_Record();
  }
}
inline ::design::NodeAttrsRecord* MessageQueuesPackage::release_nodes() {
  // @@protoc_insertion_point(field_release:design.MessageQueuesPackage.nodes)
  if (Record_case() == kNodes) {
    clear_has_Record();
    auto* temp = _impl_.Record_.nodes_;
    if (GetArena() != nullptr) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    _impl_.Record_.nodes_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::design::NodeAttrsRecord& MessageQueuesPackage::_internal_nodes() const {
  return Record_case() == kNodes ? *_impl_.Record_.nodes_ : reinterpret_cast<::design::NodeAttrsRecord&>(::design::_NodeAttrsRecord_default_instance_);
}
inline const ::design::NodeAttrsRecord& MessageQueuesPackage::nodes() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.MessageQueuesPackage.nodes)
  return _internal_nodes();
}
inline ::design::NodeAttrsRecord* MessageQueuesPackage::unsafe_arena_release_nodes() {
  // @@protoc_insertion_point(field_unsafe_arena_release:design.MessageQueuesPackage.nodes)
  if (Record_case() == kNodes) {
    clear_has_Record();
    auto* temp = _impl_.Record_.nodes_;
    _impl_.Record_.nodes_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void MessageQueuesPackage::unsafe_arena_set_allocated_nodes(::design::NodeAttrsRecord* value) {
  // We rely on the oneof clear method to free the earlier contents
  // of this oneof. We can directly use the pointer we're given to
  // set the new value.
  clear_Record();
  if (value) {
    set_has_nodes();
    _impl_.Record_.nodes_ = value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.MessageQueuesPackage.nodes)
}
inline ::design::NodeAttrsRecord* MessageQueuesPackage::_internal_mutable_nodes() {
  if (Record_case() != kNodes) {
    clear_Record();
    set_has_nodes();
    _impl_.Record_.nodes_ =
        ::google::protobuf::Message::DefaultConstruct<::design::NodeAttrsRecord>(GetArena());
  }
  return _impl_.Record_.nodes_;
}
inline ::design::NodeAttrsRecord* MessageQueuesPackage::mutable_nodes() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::design::NodeAttrsRecord* _msg = _internal_mutable_nodes();
  // @@protoc_insertion_point(field_mutable:design.MessageQueuesPackage.nodes)
  return _msg;
}

inline bool MessageQueuesPackage::has_Record() const {
  return Record_case() != RECORD_NOT_SET;
}
inline void MessageQueuesPackage::clear_has_Record() {
  _impl_._oneof_case_[0] = RECORD_NOT_SET;
}
inline MessageQueuesPackage::RecordCase MessageQueuesPackage::Record_case() const {
  return MessageQueuesPackage::RecordCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProjectConfig

// .design.ProjectInfo projectInfo = 1;
inline bool ProjectConfig::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void ProjectConfig::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& ProjectConfig::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& ProjectConfig::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectConfig.projectInfo)
  return _internal_projectinfo();
}
inline void ProjectConfig::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.ProjectConfig.projectInfo)
}
inline ::design::ProjectInfo* ProjectConfig::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* ProjectConfig::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectConfig.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* ProjectConfig::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* ProjectConfig::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.ProjectConfig.projectInfo)
  return _msg;
}
inline void ProjectConfig::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.ProjectConfig.projectInfo)
}

// map<string, string> configs = 2;
inline int ProjectConfig::_internal_configs_size() const {
  return _internal_configs().size();
}
inline int ProjectConfig::configs_size() const {
  return _internal_configs_size();
}
inline void ProjectConfig::clear_configs() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.configs_.Clear();
}
inline const ::google::protobuf::Map<std::string, std::string>& ProjectConfig::_internal_configs() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.configs_.GetMap();
}
inline const ::google::protobuf::Map<std::string, std::string>& ProjectConfig::configs() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_map:design.ProjectConfig.configs)
  return _internal_configs();
}
inline ::google::protobuf::Map<std::string, std::string>* ProjectConfig::_internal_mutable_configs() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.configs_.MutableMap();
}
inline ::google::protobuf::Map<std::string, std::string>* ProjectConfig::mutable_configs() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_map:design.ProjectConfig.configs)
  return _internal_mutable_configs();
}

// -------------------------------------------------------------------

// KeyCode

// bytes key = 1;
inline void KeyCode::clear_key() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.key_.ClearToEmpty();
}
inline const std::string& KeyCode::key() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.KeyCode.key)
  return _internal_key();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void KeyCode::set_key(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.key_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.KeyCode.key)
}
inline std::string* KeyCode::mutable_key() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:design.KeyCode.key)
  return _s;
}
inline const std::string& KeyCode::_internal_key() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.key_.Get();
}
inline void KeyCode::_internal_set_key(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.key_.Set(value, GetArena());
}
inline std::string* KeyCode::_internal_mutable_key() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.key_.Mutable( GetArena());
}
inline std::string* KeyCode::release_key() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.KeyCode.key)
  return _impl_.key_.Release();
}
inline void KeyCode::set_allocated_key(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.key_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.KeyCode.key)
}

// int64 code = 2;
inline void KeyCode::clear_code() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_ = ::int64_t{0};
}
inline ::int64_t KeyCode::code() const {
  // @@protoc_insertion_point(field_get:design.KeyCode.code)
  return _internal_code();
}
inline void KeyCode::set_code(::int64_t value) {
  _internal_set_code(value);
  // @@protoc_insertion_point(field_set:design.KeyCode.code)
}
inline ::int64_t KeyCode::_internal_code() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.code_;
}
inline void KeyCode::_internal_set_code(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_ = value;
}

// -------------------------------------------------------------------

// ProjectCodes

// .design.ProjectInfo projectInfo = 1;
inline bool ProjectCodes::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void ProjectCodes::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& ProjectCodes::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& ProjectCodes::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectCodes.projectInfo)
  return _internal_projectinfo();
}
inline void ProjectCodes::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.ProjectCodes.projectInfo)
}
inline ::design::ProjectInfo* ProjectCodes::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* ProjectCodes::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectCodes.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* ProjectCodes::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* ProjectCodes::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.ProjectCodes.projectInfo)
  return _msg;
}
inline void ProjectCodes::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.ProjectCodes.projectInfo)
}

// repeated .design.KeyCode key_code = 3;
inline int ProjectCodes::_internal_key_code_size() const {
  return _internal_key_code().size();
}
inline int ProjectCodes::key_code_size() const {
  return _internal_key_code_size();
}
inline void ProjectCodes::clear_key_code() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.key_code_.Clear();
}
inline ::design::KeyCode* ProjectCodes::mutable_key_code(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:design.ProjectCodes.key_code)
  return _internal_mutable_key_code()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::design::KeyCode>* ProjectCodes::mutable_key_code()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.ProjectCodes.key_code)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_key_code();
}
inline const ::design::KeyCode& ProjectCodes::key_code(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectCodes.key_code)
  return _internal_key_code().Get(index);
}
inline ::design::KeyCode* ProjectCodes::add_key_code() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::design::KeyCode* _add = _internal_mutable_key_code()->Add();
  // @@protoc_insertion_point(field_add:design.ProjectCodes.key_code)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::design::KeyCode>& ProjectCodes::key_code() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.ProjectCodes.key_code)
  return _internal_key_code();
}
inline const ::google::protobuf::RepeatedPtrField<::design::KeyCode>&
ProjectCodes::_internal_key_code() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.key_code_;
}
inline ::google::protobuf::RepeatedPtrField<::design::KeyCode>*
ProjectCodes::_internal_mutable_key_code() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.key_code_;
}

// -------------------------------------------------------------------

// ProjectNodes

// .design.ProjectInfo projectInfo = 1;
inline bool ProjectNodes::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void ProjectNodes::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& ProjectNodes::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& ProjectNodes::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectNodes.projectInfo)
  return _internal_projectinfo();
}
inline void ProjectNodes::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.ProjectNodes.projectInfo)
}
inline ::design::ProjectInfo* ProjectNodes::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* ProjectNodes::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectNodes.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* ProjectNodes::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* ProjectNodes::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.ProjectNodes.projectInfo)
  return _msg;
}
inline void ProjectNodes::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.ProjectNodes.projectInfo)
}

// repeated .design.NodeAttrsRecord nodes = 2;
inline int ProjectNodes::_internal_nodes_size() const {
  return _internal_nodes().size();
}
inline int ProjectNodes::nodes_size() const {
  return _internal_nodes_size();
}
inline void ProjectNodes::clear_nodes() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.nodes_.Clear();
}
inline ::design::NodeAttrsRecord* ProjectNodes::mutable_nodes(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:design.ProjectNodes.nodes)
  return _internal_mutable_nodes()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>* ProjectNodes::mutable_nodes()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.ProjectNodes.nodes)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_nodes();
}
inline const ::design::NodeAttrsRecord& ProjectNodes::nodes(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectNodes.nodes)
  return _internal_nodes().Get(index);
}
inline ::design::NodeAttrsRecord* ProjectNodes::add_nodes() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::design::NodeAttrsRecord* _add = _internal_mutable_nodes()->Add();
  // @@protoc_insertion_point(field_add:design.ProjectNodes.nodes)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>& ProjectNodes::nodes() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.ProjectNodes.nodes)
  return _internal_nodes();
}
inline const ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>&
ProjectNodes::_internal_nodes() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.nodes_;
}
inline ::google::protobuf::RepeatedPtrField<::design::NodeAttrsRecord>*
ProjectNodes::_internal_mutable_nodes() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.nodes_;
}

// int32 action = 3;
inline void ProjectNodes::clear_action() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.action_ = 0;
}
inline ::int32_t ProjectNodes::action() const {
  // @@protoc_insertion_point(field_get:design.ProjectNodes.action)
  return _internal_action();
}
inline void ProjectNodes::set_action(::int32_t value) {
  _internal_set_action(value);
  // @@protoc_insertion_point(field_set:design.ProjectNodes.action)
}
inline ::int32_t ProjectNodes::_internal_action() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.action_;
}
inline void ProjectNodes::_internal_set_action(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.action_ = value;
}

// -------------------------------------------------------------------

// ProjectNodesResult

// bytes code = 1;
inline void ProjectNodesResult::clear_code() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_.ClearToEmpty();
}
inline const std::string& ProjectNodesResult::code() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectNodesResult.code)
  return _internal_code();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProjectNodesResult::set_code(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.ProjectNodesResult.code)
}
inline std::string* ProjectNodesResult::mutable_code() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_code();
  // @@protoc_insertion_point(field_mutable:design.ProjectNodesResult.code)
  return _s;
}
inline const std::string& ProjectNodesResult::_internal_code() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.code_.Get();
}
inline void ProjectNodesResult::_internal_set_code(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_.Set(value, GetArena());
}
inline std::string* ProjectNodesResult::_internal_mutable_code() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.code_.Mutable( GetArena());
}
inline std::string* ProjectNodesResult::release_code() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectNodesResult.code)
  return _impl_.code_.Release();
}
inline void ProjectNodesResult::set_allocated_code(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.code_.IsDefault()) {
    _impl_.code_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.ProjectNodesResult.code)
}

// string message = 2;
inline void ProjectNodesResult::clear_message() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.ClearToEmpty();
}
inline const std::string& ProjectNodesResult::message() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectNodesResult.message)
  return _internal_message();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void ProjectNodesResult::set_message(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.ProjectNodesResult.message)
}
inline std::string* ProjectNodesResult::mutable_message() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:design.ProjectNodesResult.message)
  return _s;
}
inline const std::string& ProjectNodesResult::_internal_message() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.message_.Get();
}
inline void ProjectNodesResult::_internal_set_message(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.Set(value, GetArena());
}
inline std::string* ProjectNodesResult::_internal_mutable_message() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.message_.Mutable( GetArena());
}
inline std::string* ProjectNodesResult::release_message() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectNodesResult.message)
  return _impl_.message_.Release();
}
inline void ProjectNodesResult::set_allocated_message(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.message_.IsDefault()) {
    _impl_.message_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.ProjectNodesResult.message)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProjectNodesOffset

// .design.ProjectInfo projectInfo = 1;
inline bool ProjectNodesOffset::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void ProjectNodesOffset::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& ProjectNodesOffset::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& ProjectNodesOffset::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectNodesOffset.projectInfo)
  return _internal_projectinfo();
}
inline void ProjectNodesOffset::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.ProjectNodesOffset.projectInfo)
}
inline ::design::ProjectInfo* ProjectNodesOffset::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* ProjectNodesOffset::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectNodesOffset.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* ProjectNodesOffset::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* ProjectNodesOffset::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.ProjectNodesOffset.projectInfo)
  return _msg;
}
inline void ProjectNodesOffset::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.ProjectNodesOffset.projectInfo)
}

// map<uint64, .design.OffsetLength> offsets = 2;
inline int ProjectNodesOffset::_internal_offsets_size() const {
  return _internal_offsets().size();
}
inline int ProjectNodesOffset::offsets_size() const {
  return _internal_offsets_size();
}
inline void ProjectNodesOffset::clear_offsets() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.offsets_.Clear();
}
inline const ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>& ProjectNodesOffset::_internal_offsets() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.offsets_.GetMap();
}
inline const ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>& ProjectNodesOffset::offsets() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_map:design.ProjectNodesOffset.offsets)
  return _internal_offsets();
}
inline ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>* ProjectNodesOffset::_internal_mutable_offsets() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.offsets_.MutableMap();
}
inline ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>* ProjectNodesOffset::mutable_offsets() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_map:design.ProjectNodesOffset.offsets)
  return _internal_mutable_offsets();
}

// -------------------------------------------------------------------

// MaxOffset

// .design.ProjectInfo projectInfo = 1;
inline bool MaxOffset::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void MaxOffset::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& MaxOffset::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& MaxOffset::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.MaxOffset.projectInfo)
  return _internal_projectinfo();
}
inline void MaxOffset::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.MaxOffset.projectInfo)
}
inline ::design::ProjectInfo* MaxOffset::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* MaxOffset::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.MaxOffset.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* MaxOffset::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* MaxOffset::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.MaxOffset.projectInfo)
  return _msg;
}
inline void MaxOffset::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.MaxOffset.projectInfo)
}

// int32 flag = 2;
inline void MaxOffset::clear_flag() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = 0;
}
inline ::int32_t MaxOffset::flag() const {
  // @@protoc_insertion_point(field_get:design.MaxOffset.flag)
  return _internal_flag();
}
inline void MaxOffset::set_flag(::int32_t value) {
  _internal_set_flag(value);
  // @@protoc_insertion_point(field_set:design.MaxOffset.flag)
}
inline ::int32_t MaxOffset::_internal_flag() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.flag_;
}
inline void MaxOffset::_internal_set_flag(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = value;
}

// int64 maxOffset = 3;
inline void MaxOffset::clear_maxoffset() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.maxoffset_ = ::int64_t{0};
}
inline ::int64_t MaxOffset::maxoffset() const {
  // @@protoc_insertion_point(field_get:design.MaxOffset.maxOffset)
  return _internal_maxoffset();
}
inline void MaxOffset::set_maxoffset(::int64_t value) {
  _internal_set_maxoffset(value);
  // @@protoc_insertion_point(field_set:design.MaxOffset.maxOffset)
}
inline ::int64_t MaxOffset::_internal_maxoffset() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.maxoffset_;
}
inline void MaxOffset::_internal_set_maxoffset(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.maxoffset_ = value;
}

// -------------------------------------------------------------------

// NodeTreeAction

// int64 parentId = 1;
inline void NodeTreeAction::clear_parentid() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.parentid_ = ::int64_t{0};
}
inline ::int64_t NodeTreeAction::parentid() const {
  // @@protoc_insertion_point(field_get:design.NodeTreeAction.parentId)
  return _internal_parentid();
}
inline void NodeTreeAction::set_parentid(::int64_t value) {
  _internal_set_parentid(value);
  // @@protoc_insertion_point(field_set:design.NodeTreeAction.parentId)
}
inline ::int64_t NodeTreeAction::_internal_parentid() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.parentid_;
}
inline void NodeTreeAction::_internal_set_parentid(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.parentid_ = value;
}

// .design.TreeActionFlag flag = 2;
inline void NodeTreeAction::clear_flag() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = 0;
}
inline ::design::TreeActionFlag NodeTreeAction::flag() const {
  // @@protoc_insertion_point(field_get:design.NodeTreeAction.flag)
  return _internal_flag();
}
inline void NodeTreeAction::set_flag(::design::TreeActionFlag value) {
  _internal_set_flag(value);
  // @@protoc_insertion_point(field_set:design.NodeTreeAction.flag)
}
inline ::design::TreeActionFlag NodeTreeAction::_internal_flag() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return static_cast<::design::TreeActionFlag>(_impl_.flag_);
}
inline void NodeTreeAction::_internal_set_flag(::design::TreeActionFlag value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = value;
}

// int64 leftSiblingId = 3;
inline void NodeTreeAction::clear_leftsiblingid() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.leftsiblingid_ = ::int64_t{0};
}
inline ::int64_t NodeTreeAction::leftsiblingid() const {
  // @@protoc_insertion_point(field_get:design.NodeTreeAction.leftSiblingId)
  return _internal_leftsiblingid();
}
inline void NodeTreeAction::set_leftsiblingid(::int64_t value) {
  _internal_set_leftsiblingid(value);
  // @@protoc_insertion_point(field_set:design.NodeTreeAction.leftSiblingId)
}
inline ::int64_t NodeTreeAction::_internal_leftsiblingid() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.leftsiblingid_;
}
inline void NodeTreeAction::_internal_set_leftsiblingid(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.leftsiblingid_ = value;
}

// repeated int64 siblings = 4;
inline int NodeTreeAction::_internal_siblings_size() const {
  return _internal_siblings().size();
}
inline int NodeTreeAction::siblings_size() const {
  return _internal_siblings_size();
}
inline void NodeTreeAction::clear_siblings() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.siblings_.Clear();
}
inline ::int64_t NodeTreeAction::siblings(int index) const {
  // @@protoc_insertion_point(field_get:design.NodeTreeAction.siblings)
  return _internal_siblings().Get(index);
}
inline void NodeTreeAction::set_siblings(int index, ::int64_t value) {
  _internal_mutable_siblings()->Set(index, value);
  // @@protoc_insertion_point(field_set:design.NodeTreeAction.siblings)
}
inline void NodeTreeAction::add_siblings(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _internal_mutable_siblings()->Add(value);
  // @@protoc_insertion_point(field_add:design.NodeTreeAction.siblings)
}
inline const ::google::protobuf::RepeatedField<::int64_t>& NodeTreeAction::siblings() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.NodeTreeAction.siblings)
  return _internal_siblings();
}
inline ::google::protobuf::RepeatedField<::int64_t>* NodeTreeAction::mutable_siblings()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.NodeTreeAction.siblings)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_siblings();
}
inline const ::google::protobuf::RepeatedField<::int64_t>&
NodeTreeAction::_internal_siblings() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.siblings_;
}
inline ::google::protobuf::RepeatedField<::int64_t>* NodeTreeAction::_internal_mutable_siblings() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.siblings_;
}

// -------------------------------------------------------------------

// NodeTreeActions

// .design.ProjectInfo projectInfo = 1;
inline bool NodeTreeActions::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void NodeTreeActions::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& NodeTreeActions::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& NodeTreeActions::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeTreeActions.projectInfo)
  return _internal_projectinfo();
}
inline void NodeTreeActions::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.NodeTreeActions.projectInfo)
}
inline ::design::ProjectInfo* NodeTreeActions::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* NodeTreeActions::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.NodeTreeActions.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* NodeTreeActions::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* NodeTreeActions::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.NodeTreeActions.projectInfo)
  return _msg;
}
inline void NodeTreeActions::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.NodeTreeActions.projectInfo)
}

// int64 traceId = 2;
inline void NodeTreeActions::clear_traceid() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.traceid_ = ::int64_t{0};
}
inline ::int64_t NodeTreeActions::traceid() const {
  // @@protoc_insertion_point(field_get:design.NodeTreeActions.traceId)
  return _internal_traceid();
}
inline void NodeTreeActions::set_traceid(::int64_t value) {
  _internal_set_traceid(value);
  // @@protoc_insertion_point(field_set:design.NodeTreeActions.traceId)
}
inline ::int64_t NodeTreeActions::_internal_traceid() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.traceid_;
}
inline void NodeTreeActions::_internal_set_traceid(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.traceid_ = value;
}

// repeated .design.NodeTreeAction actions = 3;
inline int NodeTreeActions::_internal_actions_size() const {
  return _internal_actions().size();
}
inline int NodeTreeActions::actions_size() const {
  return _internal_actions_size();
}
inline void NodeTreeActions::clear_actions() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.actions_.Clear();
}
inline ::design::NodeTreeAction* NodeTreeActions::mutable_actions(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:design.NodeTreeActions.actions)
  return _internal_mutable_actions()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>* NodeTreeActions::mutable_actions()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.NodeTreeActions.actions)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_actions();
}
inline const ::design::NodeTreeAction& NodeTreeActions::actions(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.NodeTreeActions.actions)
  return _internal_actions().Get(index);
}
inline ::design::NodeTreeAction* NodeTreeActions::add_actions() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::design::NodeTreeAction* _add = _internal_mutable_actions()->Add();
  // @@protoc_insertion_point(field_add:design.NodeTreeActions.actions)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>& NodeTreeActions::actions() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.NodeTreeActions.actions)
  return _internal_actions();
}
inline const ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>&
NodeTreeActions::_internal_actions() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.actions_;
}
inline ::google::protobuf::RepeatedPtrField<::design::NodeTreeAction>*
NodeTreeActions::_internal_mutable_actions() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.actions_;
}

// -------------------------------------------------------------------

// ProjectNodesTree

// .design.ProjectInfo projectInfo = 1;
inline bool ProjectNodesTree::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void ProjectNodesTree::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& ProjectNodesTree::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& ProjectNodesTree::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectNodesTree.projectInfo)
  return _internal_projectinfo();
}
inline void ProjectNodesTree::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.ProjectNodesTree.projectInfo)
}
inline ::design::ProjectInfo* ProjectNodesTree::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* ProjectNodesTree::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.ProjectNodesTree.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* ProjectNodesTree::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* ProjectNodesTree::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.ProjectNodesTree.projectInfo)
  return _msg;
}
inline void ProjectNodesTree::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.ProjectNodesTree.projectInfo)
}

// repeated .design.NodeTreeRecord trees = 2;
inline int ProjectNodesTree::_internal_trees_size() const {
  return _internal_trees().size();
}
inline int ProjectNodesTree::trees_size() const {
  return _internal_trees_size();
}
inline void ProjectNodesTree::clear_trees() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.trees_.Clear();
}
inline ::design::NodeTreeRecord* ProjectNodesTree::mutable_trees(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:design.ProjectNodesTree.trees)
  return _internal_mutable_trees()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>* ProjectNodesTree::mutable_trees()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:design.ProjectNodesTree.trees)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_trees();
}
inline const ::design::NodeTreeRecord& ProjectNodesTree::trees(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.ProjectNodesTree.trees)
  return _internal_trees().Get(index);
}
inline ::design::NodeTreeRecord* ProjectNodesTree::add_trees() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::design::NodeTreeRecord* _add = _internal_mutable_trees()->Add();
  // @@protoc_insertion_point(field_add:design.ProjectNodesTree.trees)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>& ProjectNodesTree::trees() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:design.ProjectNodesTree.trees)
  return _internal_trees();
}
inline const ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>&
ProjectNodesTree::_internal_trees() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.trees_;
}
inline ::google::protobuf::RepeatedPtrField<::design::NodeTreeRecord>*
ProjectNodesTree::_internal_mutable_trees() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.trees_;
}

// -------------------------------------------------------------------

// DataBlock

// .design.ProjectInfo projectInfo = 1;
inline bool DataBlock::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void DataBlock::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& DataBlock::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& DataBlock::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.DataBlock.projectInfo)
  return _internal_projectinfo();
}
inline void DataBlock::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.DataBlock.projectInfo)
}
inline ::design::ProjectInfo* DataBlock::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* DataBlock::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.DataBlock.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* DataBlock::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* DataBlock::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.DataBlock.projectInfo)
  return _msg;
}
inline void DataBlock::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.DataBlock.projectInfo)
}

// int32 flag = 2;
inline void DataBlock::clear_flag() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = 0;
}
inline ::int32_t DataBlock::flag() const {
  // @@protoc_insertion_point(field_get:design.DataBlock.flag)
  return _internal_flag();
}
inline void DataBlock::set_flag(::int32_t value) {
  _internal_set_flag(value);
  // @@protoc_insertion_point(field_set:design.DataBlock.flag)
}
inline ::int32_t DataBlock::_internal_flag() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.flag_;
}
inline void DataBlock::_internal_set_flag(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = value;
}

// int32 action = 3;
inline void DataBlock::clear_action() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.action_ = 0;
}
inline ::int32_t DataBlock::action() const {
  // @@protoc_insertion_point(field_get:design.DataBlock.action)
  return _internal_action();
}
inline void DataBlock::set_action(::int32_t value) {
  _internal_set_action(value);
  // @@protoc_insertion_point(field_set:design.DataBlock.action)
}
inline ::int32_t DataBlock::_internal_action() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.action_;
}
inline void DataBlock::_internal_set_action(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.action_ = value;
}

// .design.MessageQueuesPackage package = 5;
inline bool DataBlock::has_package() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.package_ != nullptr);
  return value;
}
inline void DataBlock::clear_package() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.package_ != nullptr) _impl_.package_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::design::MessageQueuesPackage& DataBlock::_internal_package() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::MessageQueuesPackage* p = _impl_.package_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::MessageQueuesPackage&>(::design::_MessageQueuesPackage_default_instance_);
}
inline const ::design::MessageQueuesPackage& DataBlock::package() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.DataBlock.package)
  return _internal_package();
}
inline void DataBlock::unsafe_arena_set_allocated_package(::design::MessageQueuesPackage* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.package_);
  }
  _impl_.package_ = reinterpret_cast<::design::MessageQueuesPackage*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.DataBlock.package)
}
inline ::design::MessageQueuesPackage* DataBlock::release_package() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::design::MessageQueuesPackage* released = _impl_.package_;
  _impl_.package_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::MessageQueuesPackage* DataBlock::unsafe_arena_release_package() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.DataBlock.package)

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::design::MessageQueuesPackage* temp = _impl_.package_;
  _impl_.package_ = nullptr;
  return temp;
}
inline ::design::MessageQueuesPackage* DataBlock::_internal_mutable_package() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.package_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::MessageQueuesPackage>(GetArena());
    _impl_.package_ = reinterpret_cast<::design::MessageQueuesPackage*>(p);
  }
  return _impl_.package_;
}
inline ::design::MessageQueuesPackage* DataBlock::mutable_package() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000002u;
  ::design::MessageQueuesPackage* _msg = _internal_mutable_package();
  // @@protoc_insertion_point(field_mutable:design.DataBlock.package)
  return _msg;
}
inline void DataBlock::set_allocated_package(::design::MessageQueuesPackage* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.package_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }

  _impl_.package_ = reinterpret_cast<::design::MessageQueuesPackage*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.DataBlock.package)
}

// -------------------------------------------------------------------

// UserInfo

// .design.ProjectInfo projectInfo = 1;
inline bool UserInfo::has_projectinfo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.projectinfo_ != nullptr);
  return value;
}
inline void UserInfo::clear_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ != nullptr) _impl_.projectinfo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::design::ProjectInfo& UserInfo::_internal_projectinfo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::design::ProjectInfo* p = _impl_.projectinfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::design::ProjectInfo&>(::design::_ProjectInfo_default_instance_);
}
inline const ::design::ProjectInfo& UserInfo::projectinfo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.UserInfo.projectInfo)
  return _internal_projectinfo();
}
inline void UserInfo::unsafe_arena_set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.projectinfo_);
  }
  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:design.UserInfo.projectInfo)
}
inline ::design::ProjectInfo* UserInfo::release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* released = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  if (::google::protobuf::internal::DebugHardenForceCopyInRelease()) {
    auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    if (GetArena() == nullptr) {
      delete old;
    }
  } else {
    if (GetArena() != nullptr) {
      released = ::google::protobuf::internal::DuplicateIfNonNull(released);
    }
  }
  return released;
}
inline ::design::ProjectInfo* UserInfo::unsafe_arena_release_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.UserInfo.projectInfo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::design::ProjectInfo* temp = _impl_.projectinfo_;
  _impl_.projectinfo_ = nullptr;
  return temp;
}
inline ::design::ProjectInfo* UserInfo::_internal_mutable_projectinfo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.projectinfo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::design::ProjectInfo>(GetArena());
    _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(p);
  }
  return _impl_.projectinfo_;
}
inline ::design::ProjectInfo* UserInfo::mutable_projectinfo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::design::ProjectInfo* _msg = _internal_mutable_projectinfo();
  // @@protoc_insertion_point(field_mutable:design.UserInfo.projectInfo)
  return _msg;
}
inline void UserInfo::set_allocated_projectinfo(::design::ProjectInfo* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.projectinfo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.projectinfo_ = reinterpret_cast<::design::ProjectInfo*>(value);
  // @@protoc_insertion_point(field_set_allocated:design.UserInfo.projectInfo)
}

// .design.LoginFlag flag = 2;
inline void UserInfo::clear_flag() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = 0;
}
inline ::design::LoginFlag UserInfo::flag() const {
  // @@protoc_insertion_point(field_get:design.UserInfo.flag)
  return _internal_flag();
}
inline void UserInfo::set_flag(::design::LoginFlag value) {
  _internal_set_flag(value);
  // @@protoc_insertion_point(field_set:design.UserInfo.flag)
}
inline ::design::LoginFlag UserInfo::_internal_flag() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return static_cast<::design::LoginFlag>(_impl_.flag_);
}
inline void UserInfo::_internal_set_flag(::design::LoginFlag value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.flag_ = value;
}

// string user = 3;
inline void UserInfo::clear_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.ClearToEmpty();
}
inline const std::string& UserInfo::user() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.UserInfo.user)
  return _internal_user();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void UserInfo::set_user(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.UserInfo.user)
}
inline std::string* UserInfo::mutable_user() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_user();
  // @@protoc_insertion_point(field_mutable:design.UserInfo.user)
  return _s;
}
inline const std::string& UserInfo::_internal_user() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.user_.Get();
}
inline void UserInfo::_internal_set_user(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.Set(value, GetArena());
}
inline std::string* UserInfo::_internal_mutable_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.user_.Mutable( GetArena());
}
inline std::string* UserInfo::release_user() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.UserInfo.user)
  return _impl_.user_.Release();
}
inline void UserInfo::set_allocated_user(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.user_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.user_.IsDefault()) {
    _impl_.user_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.UserInfo.user)
}

// string pwd = 4;
inline void UserInfo::clear_pwd() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.pwd_.ClearToEmpty();
}
inline const std::string& UserInfo::pwd() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.UserInfo.pwd)
  return _internal_pwd();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void UserInfo::set_pwd(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.pwd_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.UserInfo.pwd)
}
inline std::string* UserInfo::mutable_pwd() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_pwd();
  // @@protoc_insertion_point(field_mutable:design.UserInfo.pwd)
  return _s;
}
inline const std::string& UserInfo::_internal_pwd() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.pwd_.Get();
}
inline void UserInfo::_internal_set_pwd(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.pwd_.Set(value, GetArena());
}
inline std::string* UserInfo::_internal_mutable_pwd() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.pwd_.Mutable( GetArena());
}
inline std::string* UserInfo::release_pwd() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.UserInfo.pwd)
  return _impl_.pwd_.Release();
}
inline void UserInfo::set_allocated_pwd(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.pwd_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.pwd_.IsDefault()) {
    _impl_.pwd_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.UserInfo.pwd)
}

// -------------------------------------------------------------------

// LoginResponse

// int32 code = 1;
inline void LoginResponse::clear_code() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_ = 0;
}
inline ::int32_t LoginResponse::code() const {
  // @@protoc_insertion_point(field_get:design.LoginResponse.code)
  return _internal_code();
}
inline void LoginResponse::set_code(::int32_t value) {
  _internal_set_code(value);
  // @@protoc_insertion_point(field_set:design.LoginResponse.code)
}
inline ::int32_t LoginResponse::_internal_code() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.code_;
}
inline void LoginResponse::_internal_set_code(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.code_ = value;
}

// string message = 2;
inline void LoginResponse::clear_message() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.ClearToEmpty();
}
inline const std::string& LoginResponse::message() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.LoginResponse.message)
  return _internal_message();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void LoginResponse::set_message(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.LoginResponse.message)
}
inline std::string* LoginResponse::mutable_message() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:design.LoginResponse.message)
  return _s;
}
inline const std::string& LoginResponse::_internal_message() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.message_.Get();
}
inline void LoginResponse::_internal_set_message(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.Set(value, GetArena());
}
inline std::string* LoginResponse::_internal_mutable_message() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.message_.Mutable( GetArena());
}
inline std::string* LoginResponse::release_message() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.LoginResponse.message)
  return _impl_.message_.Release();
}
inline void LoginResponse::set_allocated_message(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.message_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.message_.IsDefault()) {
    _impl_.message_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.LoginResponse.message)
}

// string result = 3;
inline void LoginResponse::clear_result() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.result_.ClearToEmpty();
}
inline const std::string& LoginResponse::result() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:design.LoginResponse.result)
  return _internal_result();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void LoginResponse::set_result(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.result_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:design.LoginResponse.result)
}
inline std::string* LoginResponse::mutable_result() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_result();
  // @@protoc_insertion_point(field_mutable:design.LoginResponse.result)
  return _s;
}
inline const std::string& LoginResponse::_internal_result() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.result_.Get();
}
inline void LoginResponse::_internal_set_result(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.result_.Set(value, GetArena());
}
inline std::string* LoginResponse::_internal_mutable_result() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.result_.Mutable( GetArena());
}
inline std::string* LoginResponse::release_result() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:design.LoginResponse.result)
  return _impl_.result_.Release();
}
inline void LoginResponse::set_allocated_result(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.result_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.result_.IsDefault()) {
    _impl_.result_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:design.LoginResponse.result)
}

#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)
}  // namespace design


namespace google {
namespace protobuf {

template <>
struct is_proto_enum<::design::TreeActionFlag> : std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor<::design::TreeActionFlag>() {
  return ::design::TreeActionFlag_descriptor();
}
template <>
struct is_proto_enum<::design::LoginFlag> : std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor<::design::LoginFlag>() {
  return ::design::LoginFlag_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#include "google/protobuf/port_undef.inc"

#endif  // node_2eproto_2epb_2eh
