// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: service.proto

#ifndef GRPC_MOCK_service_2eproto__INCLUDED
#define GRPC_MOCK_service_2eproto__INCLUDED

#include "service.pb.h"
#include "service.grpc.pb.h"

#include <grpcpp/support/async_stream.h>
#include <grpcpp/support/sync_stream.h>
#include <gmock/gmock.h>
namespace design {

class MockDesignServiceStub : public DesignService::StubInterface {
 public:
  MOCK_METHOD3(Login, ::grpc::Status(::grpc::ClientContext* context, const ::design::UserInfo& request, ::design::LoginResponse* response));
  MOCK_METHOD3(AsyncLoginRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>*(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncLoginRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>*(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(CheckInOut, ::grpc::Status(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::design::ProjectNodesTree* response));
  MOCK_METHOD3(AsyncCheckInOutRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>*(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncCheckInOutRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>*(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetConfig, ::grpc::Status(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectConfigInfo* response));
  MOCK_METHOD3(AsyncGetConfigRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>*(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetConfigRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>*(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(AcquireDictionaryCode, ::grpc::Status(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::design::ProjectCodes* response));
  MOCK_METHOD3(AsyncAcquireDictionaryCodeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>*(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncAcquireDictionaryCodeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>*(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(FetchAllDictionaryCode, ::grpc::Status(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::design::ProjectCodes* response));
  MOCK_METHOD3(AsyncFetchAllDictionaryCodeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>*(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncFetchAllDictionaryCodeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>*(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(UpdateNodes, ::grpc::Status(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::design::ProjectNodesResult* response));
  MOCK_METHOD3(AsyncUpdateNodesRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>*(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUpdateNodesRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>*(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(UpdateNodeTree, ::grpc::Status(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::design::ProjectNodesResult* response));
  MOCK_METHOD3(AsyncUpdateNodeTreeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>*(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUpdateNodeTreeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>*(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(ImportProjectProgress, ::grpc::Status(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectInfo* response));
  MOCK_METHOD3(AsyncImportProjectProgressRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>*(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncImportProjectProgressRaw, ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>*(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq));
};

}  // namespace design


#endif  // GRPC_MOCK_service_2eproto__INCLUDED
