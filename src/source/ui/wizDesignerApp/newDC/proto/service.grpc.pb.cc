// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: service.proto

#include "service.pb.h"
#include "service.grpc.pb.h"

#include <functional>
#include <grpcpp/support/async_stream.h>
#include <grpcpp/support/async_unary_call.h>
#include <grpcpp/impl/channel_interface.h>
#include <grpcpp/impl/client_unary_call.h>
#include <grpcpp/support/client_callback.h>
#include <grpcpp/support/message_allocator.h>
#include <grpcpp/support/method_handler.h>
#include <grpcpp/impl/rpc_service_method.h>
#include <grpcpp/support/server_callback.h>
#include <grpcpp/impl/server_callback_handlers.h>
#include <grpcpp/server_context.h>
#include <grpcpp/impl/service_type.h>
#include <grpcpp/support/sync_stream.h>
namespace design {

static const char* DesignService_method_names[] = {
  "/design.DesignService/Login",
  "/design.DesignService/CheckInOut",
  "/design.DesignService/GetConfig",
  "/design.DesignService/AcquireDictionaryCode",
  "/design.DesignService/FetchAllDictionaryCode",
  "/design.DesignService/UpdateNodes",
  "/design.DesignService/UpdateNodeTree",
  "/design.DesignService/ImportProjectProgress",
};

std::unique_ptr< DesignService::Stub> DesignService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< DesignService::Stub> stub(new DesignService::Stub(channel, options));
  return stub;
}

DesignService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Login_(DesignService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CheckInOut_(DesignService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetConfig_(DesignService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_AcquireDictionaryCode_(DesignService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_FetchAllDictionaryCode_(DesignService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateNodes_(DesignService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateNodeTree_(DesignService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ImportProjectProgress_(DesignService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status DesignService::Stub::Login(::grpc::ClientContext* context, const ::design::UserInfo& request, ::design::LoginResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::UserInfo, ::design::LoginResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Login_, context, request, response);
}

void DesignService::Stub::async::Login(::grpc::ClientContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::UserInfo, ::design::LoginResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Login_, context, request, response, std::move(f));
}

void DesignService::Stub::async::Login(::grpc::ClientContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Login_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::LoginResponse>* DesignService::Stub::PrepareAsyncLoginRaw(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::LoginResponse, ::design::UserInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Login_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::LoginResponse>* DesignService::Stub::AsyncLoginRaw(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLoginRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DesignService::Stub::CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::design::ProjectNodesTree* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::ProjectNodesTree, ::design::ProjectNodesTree, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CheckInOut_, context, request, response);
}

void DesignService::Stub::async::CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::ProjectNodesTree, ::design::ProjectNodesTree, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CheckInOut_, context, request, response, std::move(f));
}

void DesignService::Stub::async::CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CheckInOut_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>* DesignService::Stub::PrepareAsyncCheckInOutRaw(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::ProjectNodesTree, ::design::ProjectNodesTree, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CheckInOut_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>* DesignService::Stub::AsyncCheckInOutRaw(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCheckInOutRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DesignService::Stub::GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectConfigInfo* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::ProjectInfo, ::design::ProjectConfigInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetConfig_, context, request, response);
}

void DesignService::Stub::async::GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::ProjectInfo, ::design::ProjectConfigInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfig_, context, request, response, std::move(f));
}

void DesignService::Stub::async::GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>* DesignService::Stub::PrepareAsyncGetConfigRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::ProjectConfigInfo, ::design::ProjectInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>* DesignService::Stub::AsyncGetConfigRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DesignService::Stub::AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::design::ProjectCodes* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::ProjectCodes, ::design::ProjectCodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_AcquireDictionaryCode_, context, request, response);
}

void DesignService::Stub::async::AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::ProjectCodes, ::design::ProjectCodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AcquireDictionaryCode_, context, request, response, std::move(f));
}

void DesignService::Stub::async::AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AcquireDictionaryCode_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* DesignService::Stub::PrepareAsyncAcquireDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::ProjectCodes, ::design::ProjectCodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_AcquireDictionaryCode_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* DesignService::Stub::AsyncAcquireDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncAcquireDictionaryCodeRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DesignService::Stub::FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::design::ProjectCodes* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::MaxOffset, ::design::ProjectCodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FetchAllDictionaryCode_, context, request, response);
}

void DesignService::Stub::async::FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::MaxOffset, ::design::ProjectCodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FetchAllDictionaryCode_, context, request, response, std::move(f));
}

void DesignService::Stub::async::FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FetchAllDictionaryCode_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* DesignService::Stub::PrepareAsyncFetchAllDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::ProjectCodes, ::design::MaxOffset, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FetchAllDictionaryCode_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* DesignService::Stub::AsyncFetchAllDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFetchAllDictionaryCodeRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DesignService::Stub::UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::design::ProjectNodesResult* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::ProjectNodes, ::design::ProjectNodesResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpdateNodes_, context, request, response);
}

void DesignService::Stub::async::UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::ProjectNodes, ::design::ProjectNodesResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateNodes_, context, request, response, std::move(f));
}

void DesignService::Stub::async::UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateNodes_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* DesignService::Stub::PrepareAsyncUpdateNodesRaw(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::ProjectNodesResult, ::design::ProjectNodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpdateNodes_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* DesignService::Stub::AsyncUpdateNodesRaw(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateNodesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DesignService::Stub::UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::design::ProjectNodesResult* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::NodeTreeActions, ::design::ProjectNodesResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpdateNodeTree_, context, request, response);
}

void DesignService::Stub::async::UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::NodeTreeActions, ::design::ProjectNodesResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateNodeTree_, context, request, response, std::move(f));
}

void DesignService::Stub::async::UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateNodeTree_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* DesignService::Stub::PrepareAsyncUpdateNodeTreeRaw(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::ProjectNodesResult, ::design::NodeTreeActions, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpdateNodeTree_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* DesignService::Stub::AsyncUpdateNodeTreeRaw(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateNodeTreeRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DesignService::Stub::ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectInfo* response) {
  return ::grpc::internal::BlockingUnaryCall< ::design::ProjectInfo, ::design::ProjectInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ImportProjectProgress_, context, request, response);
}

void DesignService::Stub::async::ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::design::ProjectInfo, ::design::ProjectInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ImportProjectProgress_, context, request, response, std::move(f));
}

void DesignService::Stub::async::ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ImportProjectProgress_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>* DesignService::Stub::PrepareAsyncImportProjectProgressRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::design::ProjectInfo, ::design::ProjectInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ImportProjectProgress_, context, request);
}

::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>* DesignService::Stub::AsyncImportProjectProgressRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncImportProjectProgressRaw(context, request, cq);
  result->StartCall();
  return result;
}

DesignService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::UserInfo, ::design::LoginResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::UserInfo* req,
             ::design::LoginResponse* resp) {
               return service->Login(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::ProjectNodesTree, ::design::ProjectNodesTree, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::ProjectNodesTree* req,
             ::design::ProjectNodesTree* resp) {
               return service->CheckInOut(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::ProjectInfo, ::design::ProjectConfigInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::ProjectInfo* req,
             ::design::ProjectConfigInfo* resp) {
               return service->GetConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::ProjectCodes, ::design::ProjectCodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::ProjectCodes* req,
             ::design::ProjectCodes* resp) {
               return service->AcquireDictionaryCode(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::MaxOffset, ::design::ProjectCodes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::MaxOffset* req,
             ::design::ProjectCodes* resp) {
               return service->FetchAllDictionaryCode(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::ProjectNodes, ::design::ProjectNodesResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::ProjectNodes* req,
             ::design::ProjectNodesResult* resp) {
               return service->UpdateNodes(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::NodeTreeActions, ::design::ProjectNodesResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::NodeTreeActions* req,
             ::design::ProjectNodesResult* resp) {
               return service->UpdateNodeTree(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DesignService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DesignService::Service, ::design::ProjectInfo, ::design::ProjectInfo, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DesignService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::design::ProjectInfo* req,
             ::design::ProjectInfo* resp) {
               return service->ImportProjectProgress(ctx, req, resp);
             }, this)));
}

DesignService::Service::~Service() {
}

::grpc::Status DesignService::Service::Login(::grpc::ServerContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DesignService::Service::CheckInOut(::grpc::ServerContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DesignService::Service::GetConfig(::grpc::ServerContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DesignService::Service::AcquireDictionaryCode(::grpc::ServerContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DesignService::Service::FetchAllDictionaryCode(::grpc::ServerContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DesignService::Service::UpdateNodes(::grpc::ServerContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DesignService::Service::UpdateNodeTree(::grpc::ServerContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DesignService::Service::ImportProjectProgress(::grpc::ServerContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace design

