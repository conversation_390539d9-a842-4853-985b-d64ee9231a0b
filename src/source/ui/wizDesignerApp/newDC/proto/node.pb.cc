// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: node.proto
// Protobuf C++ Version: 5.29.3

#include "node.pb.h"

#include <algorithm>
#include <type_traits>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/generated_message_tctable_impl.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/descriptor.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/reflection_ops.h"
#include "google/protobuf/wire_format.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace design {

inline constexpr VectorString::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : vec_string_{},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR VectorString::VectorString(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct VectorStringDefaultTypeInternal {
  PROTOBUF_CONSTEXPR VectorStringDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~VectorStringDefaultTypeInternal() {}
  union {
    VectorString _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VectorStringDefaultTypeInternal _VectorString_default_instance_;

inline constexpr VectorInt64::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : vec_int64_{},
        _vec_int64_cached_byte_size_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR VectorInt64::VectorInt64(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct VectorInt64DefaultTypeInternal {
  PROTOBUF_CONSTEXPR VectorInt64DefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~VectorInt64DefaultTypeInternal() {}
  union {
    VectorInt64 _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VectorInt64DefaultTypeInternal _VectorInt64_default_instance_;

inline constexpr VectorInt::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : vec_int_{},
        _vec_int_cached_byte_size_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR VectorInt::VectorInt(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct VectorIntDefaultTypeInternal {
  PROTOBUF_CONSTEXPR VectorIntDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~VectorIntDefaultTypeInternal() {}
  union {
    VectorInt _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VectorIntDefaultTypeInternal _VectorInt_default_instance_;

inline constexpr VectorDouble::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : vec_double_{},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR VectorDouble::VectorDouble(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct VectorDoubleDefaultTypeInternal {
  PROTOBUF_CONSTEXPR VectorDoubleDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~VectorDoubleDefaultTypeInternal() {}
  union {
    VectorDouble _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VectorDoubleDefaultTypeInternal _VectorDouble_default_instance_;

inline constexpr ValInt::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : val_int_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ValInt::ValInt(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ValIntDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ValIntDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ValIntDefaultTypeInternal() {}
  union {
    ValInt _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ValIntDefaultTypeInternal _ValInt_default_instance_;
              template <typename>
PROTOBUF_CONSTEXPR StringIntegerMap_MapEntry_DoNotUse::StringIntegerMap_MapEntry_DoNotUse(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : StringIntegerMap_MapEntry_DoNotUse::MapEntry(_class_data_.base()){}
#else   // PROTOBUF_CUSTOM_VTABLE
    : StringIntegerMap_MapEntry_DoNotUse::MapEntry() {
}
#endif  // PROTOBUF_CUSTOM_VTABLE
struct StringIntegerMap_MapEntry_DoNotUseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR StringIntegerMap_MapEntry_DoNotUseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~StringIntegerMap_MapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    StringIntegerMap_MapEntry_DoNotUse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 StringIntegerMap_MapEntry_DoNotUseDefaultTypeInternal _StringIntegerMap_MapEntry_DoNotUse_default_instance_;

inline constexpr StrIntRecord::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : str_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        id_{::int64_t{0}},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR StrIntRecord::StrIntRecord(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct StrIntRecordDefaultTypeInternal {
  PROTOBUF_CONSTEXPR StrIntRecordDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~StrIntRecordDefaultTypeInternal() {}
  union {
    StrIntRecord _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 StrIntRecordDefaultTypeInternal _StrIntRecord_default_instance_;

inline constexpr SVecInt::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : vec_sint_{},
        _vec_sint_cached_byte_size_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR SVecInt::SVecInt(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct SVecIntDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SVecIntDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~SVecIntDefaultTypeInternal() {}
  union {
    SVecInt _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SVecIntDefaultTypeInternal _SVecInt_default_instance_;

inline constexpr ProjectNodesResult::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : code_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        message_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectNodesResult::ProjectNodesResult(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectNodesResultDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectNodesResultDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectNodesResultDefaultTypeInternal() {}
  union {
    ProjectNodesResult _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectNodesResultDefaultTypeInternal _ProjectNodesResult_default_instance_;

inline constexpr ProjectInfo::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : projectcode_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        classification_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        subtopic_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        user_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        progresspercentage_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectInfo::ProjectInfo(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectInfoDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectInfoDefaultTypeInternal() {}
  union {
    ProjectInfo _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectInfoDefaultTypeInternal _ProjectInfo_default_instance_;

inline constexpr ProjectConfigInfo::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : json_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectConfigInfo::ProjectConfigInfo(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectConfigInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectConfigInfoDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectConfigInfoDefaultTypeInternal() {}
  union {
    ProjectConfigInfo _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectConfigInfoDefaultTypeInternal _ProjectConfigInfo_default_instance_;
              template <typename>
PROTOBUF_CONSTEXPR ProjectConfig_ConfigsEntry_DoNotUse::ProjectConfig_ConfigsEntry_DoNotUse(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ProjectConfig_ConfigsEntry_DoNotUse::MapEntry(_class_data_.base()){}
#else   // PROTOBUF_CUSTOM_VTABLE
    : ProjectConfig_ConfigsEntry_DoNotUse::MapEntry() {
}
#endif  // PROTOBUF_CUSTOM_VTABLE
struct ProjectConfig_ConfigsEntry_DoNotUseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectConfig_ConfigsEntry_DoNotUseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectConfig_ConfigsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ProjectConfig_ConfigsEntry_DoNotUse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectConfig_ConfigsEntry_DoNotUseDefaultTypeInternal _ProjectConfig_ConfigsEntry_DoNotUse_default_instance_;

inline constexpr OffsetLength::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : offset_{::int64_t{0}},
        length_{0},
        crc_{0u},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR OffsetLength::OffsetLength(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct OffsetLengthDefaultTypeInternal {
  PROTOBUF_CONSTEXPR OffsetLengthDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~OffsetLengthDefaultTypeInternal() {}
  union {
    OffsetLength _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 OffsetLengthDefaultTypeInternal _OffsetLength_default_instance_;

inline constexpr NodeTreeAction::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : siblings_{},
        _siblings_cached_byte_size_{0},
        parentid_{::int64_t{0}},
        leftsiblingid_{::int64_t{0}},
        flag_{static_cast< ::design::TreeActionFlag >(0)},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR NodeTreeAction::NodeTreeAction(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct NodeTreeActionDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeTreeActionDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeTreeActionDefaultTypeInternal() {}
  union {
    NodeTreeAction _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeTreeActionDefaultTypeInternal _NodeTreeAction_default_instance_;

inline constexpr LoginResponse::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : message_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        result_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        code_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR LoginResponse::LoginResponse(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct LoginResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LoginResponseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~LoginResponseDefaultTypeInternal() {}
  union {
    LoginResponse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LoginResponseDefaultTypeInternal _LoginResponse_default_instance_;

inline constexpr KeyCode::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : key_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        code_{::int64_t{0}},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR KeyCode::KeyCode(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct KeyCodeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR KeyCodeDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~KeyCodeDefaultTypeInternal() {}
  union {
    KeyCode _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 KeyCodeDefaultTypeInternal _KeyCode_default_instance_;

inline constexpr DVec4::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : x_{0},
        y_{0},
        z_{0},
        w_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR DVec4::DVec4(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct DVec4DefaultTypeInternal {
  PROTOBUF_CONSTEXPR DVec4DefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~DVec4DefaultTypeInternal() {}
  union {
    DVec4 _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DVec4DefaultTypeInternal _DVec4_default_instance_;

inline constexpr AdditionalInfo::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : user_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        checkoutuser_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        time_{::int64_t{0}},
        checkout_{0},
        status_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR AdditionalInfo::AdditionalInfo(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct AdditionalInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AdditionalInfoDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~AdditionalInfoDefaultTypeInternal() {}
  union {
    AdditionalInfo _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AdditionalInfoDefaultTypeInternal _AdditionalInfo_default_instance_;

inline constexpr UserInfo::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        user_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        pwd_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        projectinfo_{nullptr},
        flag_{static_cast< ::design::LoginFlag >(0)} {}

template <typename>
PROTOBUF_CONSTEXPR UserInfo::UserInfo(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct UserInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR UserInfoDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~UserInfoDefaultTypeInternal() {}
  union {
    UserInfo _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 UserInfoDefaultTypeInternal _UserInfo_default_instance_;

inline constexpr StringIntegerMap::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : map_{},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR StringIntegerMap::StringIntegerMap(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct StringIntegerMapDefaultTypeInternal {
  PROTOBUF_CONSTEXPR StringIntegerMapDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~StringIntegerMapDefaultTypeInternal() {}
  union {
    StringIntegerMap _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 StringIntegerMapDefaultTypeInternal _StringIntegerMap_default_instance_;
              template <typename>
PROTOBUF_CONSTEXPR ProjectNodesOffset_OffsetsEntry_DoNotUse::ProjectNodesOffset_OffsetsEntry_DoNotUse(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ProjectNodesOffset_OffsetsEntry_DoNotUse::MapEntry(_class_data_.base()){}
#else   // PROTOBUF_CUSTOM_VTABLE
    : ProjectNodesOffset_OffsetsEntry_DoNotUse::MapEntry() {
}
#endif  // PROTOBUF_CUSTOM_VTABLE
struct ProjectNodesOffset_OffsetsEntry_DoNotUseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectNodesOffset_OffsetsEntry_DoNotUseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectNodesOffset_OffsetsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ProjectNodesOffset_OffsetsEntry_DoNotUse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectNodesOffset_OffsetsEntry_DoNotUseDefaultTypeInternal _ProjectNodesOffset_OffsetsEntry_DoNotUse_default_instance_;

inline constexpr ProjectConfig::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        configs_{},
        projectinfo_{nullptr} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectConfig::ProjectConfig(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectConfigDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectConfigDefaultTypeInternal() {}
  union {
    ProjectConfig _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectConfigDefaultTypeInternal _ProjectConfig_default_instance_;

inline constexpr ProjectCodes::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        key_code_{},
        projectinfo_{nullptr} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectCodes::ProjectCodes(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectCodesDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectCodesDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectCodesDefaultTypeInternal() {}
  union {
    ProjectCodes _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectCodesDefaultTypeInternal _ProjectCodes_default_instance_;

inline constexpr NodeTreeRecord::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        additionaljson_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        children_{nullptr},
        additionalinfo_{nullptr},
        id_{::int64_t{0}},
        traceid_{::int64_t{0}} {}

template <typename>
PROTOBUF_CONSTEXPR NodeTreeRecord::NodeTreeRecord(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct NodeTreeRecordDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeTreeRecordDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeTreeRecordDefaultTypeInternal() {}
  union {
    NodeTreeRecord _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeTreeRecordDefaultTypeInternal _NodeTreeRecord_default_instance_;

inline constexpr NodeTreeActions::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        actions_{},
        projectinfo_{nullptr},
        traceid_{::int64_t{0}} {}

template <typename>
PROTOBUF_CONSTEXPR NodeTreeActions::NodeTreeActions(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct NodeTreeActionsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeTreeActionsDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeTreeActionsDefaultTypeInternal() {}
  union {
    NodeTreeActions _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeTreeActionsDefaultTypeInternal _NodeTreeActions_default_instance_;

inline constexpr NodeOffsetRecord::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        off_len_{nullptr},
        node_id_{0} {}

template <typename>
PROTOBUF_CONSTEXPR NodeOffsetRecord::NodeOffsetRecord(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct NodeOffsetRecordDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeOffsetRecordDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeOffsetRecordDefaultTypeInternal() {}
  union {
    NodeOffsetRecord _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeOffsetRecordDefaultTypeInternal _NodeOffsetRecord_default_instance_;

inline constexpr NodeBaseValue::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : OneValue_{},
        _cached_size_{0},
        _oneof_case_{} {}

template <typename>
PROTOBUF_CONSTEXPR NodeBaseValue::NodeBaseValue(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct NodeBaseValueDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeBaseValueDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeBaseValueDefaultTypeInternal() {}
  union {
    NodeBaseValue _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeBaseValueDefaultTypeInternal _NodeBaseValue_default_instance_;

inline constexpr MaxOffset::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        projectinfo_{nullptr},
        maxoffset_{::int64_t{0}},
        flag_{0} {}

template <typename>
PROTOBUF_CONSTEXPR MaxOffset::MaxOffset(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct MaxOffsetDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MaxOffsetDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~MaxOffsetDefaultTypeInternal() {}
  union {
    MaxOffset _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MaxOffsetDefaultTypeInternal _MaxOffset_default_instance_;

inline constexpr ProjectNodesTree::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        trees_{},
        projectinfo_{nullptr} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectNodesTree::ProjectNodesTree(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectNodesTreeDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectNodesTreeDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectNodesTreeDefaultTypeInternal() {}
  union {
    ProjectNodesTree _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectNodesTreeDefaultTypeInternal _ProjectNodesTree_default_instance_;

inline constexpr ProjectNodesOffset::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        offsets_{},
        projectinfo_{nullptr} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectNodesOffset::ProjectNodesOffset(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectNodesOffsetDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectNodesOffsetDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectNodesOffsetDefaultTypeInternal() {}
  union {
    ProjectNodesOffset _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectNodesOffsetDefaultTypeInternal _ProjectNodesOffset_default_instance_;
              template <typename>
PROTOBUF_CONSTEXPR NodeAttrs_AttrMapEntry_DoNotUse::NodeAttrs_AttrMapEntry_DoNotUse(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : NodeAttrs_AttrMapEntry_DoNotUse::MapEntry(_class_data_.base()){}
#else   // PROTOBUF_CUSTOM_VTABLE
    : NodeAttrs_AttrMapEntry_DoNotUse::MapEntry() {
}
#endif  // PROTOBUF_CUSTOM_VTABLE
struct NodeAttrs_AttrMapEntry_DoNotUseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeAttrs_AttrMapEntry_DoNotUseDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeAttrs_AttrMapEntry_DoNotUseDefaultTypeInternal() {}
  union {
    NodeAttrs_AttrMapEntry_DoNotUse _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeAttrs_AttrMapEntry_DoNotUseDefaultTypeInternal _NodeAttrs_AttrMapEntry_DoNotUse_default_instance_;

inline constexpr NodeAttrs::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : attr_map_{},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR NodeAttrs::NodeAttrs(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct NodeAttrsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeAttrsDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeAttrsDefaultTypeInternal() {}
  union {
    NodeAttrs _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeAttrsDefaultTypeInternal _NodeAttrs_default_instance_;

inline constexpr NodeAttrsRecord::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        name_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        additionaljson_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        attrs_{nullptr},
        additionalinfo_{nullptr},
        id_{::int64_t{0}},
        type_{0},
        action_{0},
        traceid_{::int64_t{0}} {}

template <typename>
PROTOBUF_CONSTEXPR NodeAttrsRecord::NodeAttrsRecord(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct NodeAttrsRecordDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NodeAttrsRecordDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~NodeAttrsRecordDefaultTypeInternal() {}
  union {
    NodeAttrsRecord _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NodeAttrsRecordDefaultTypeInternal _NodeAttrsRecord_default_instance_;

inline constexpr ProjectNodes::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        nodes_{},
        projectinfo_{nullptr},
        action_{0} {}

template <typename>
PROTOBUF_CONSTEXPR ProjectNodes::ProjectNodes(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct ProjectNodesDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProjectNodesDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProjectNodesDefaultTypeInternal() {}
  union {
    ProjectNodes _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProjectNodesDefaultTypeInternal _ProjectNodes_default_instance_;

inline constexpr MessageQueuesPackage::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : Record_{},
        _cached_size_{0},
        _oneof_case_{} {}

template <typename>
PROTOBUF_CONSTEXPR MessageQueuesPackage::MessageQueuesPackage(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct MessageQueuesPackageDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MessageQueuesPackageDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~MessageQueuesPackageDefaultTypeInternal() {}
  union {
    MessageQueuesPackage _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MessageQueuesPackageDefaultTypeInternal _MessageQueuesPackage_default_instance_;

inline constexpr DataBlock::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        projectinfo_{nullptr},
        package_{nullptr},
        flag_{0},
        action_{0} {}

template <typename>
PROTOBUF_CONSTEXPR DataBlock::DataBlock(::_pbi::ConstantInitialized)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(_class_data_.base()),
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(),
#endif  // PROTOBUF_CUSTOM_VTABLE
      _impl_(::_pbi::ConstantInitialized()) {
}
struct DataBlockDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DataBlockDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~DataBlockDefaultTypeInternal() {}
  union {
    DataBlock _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DataBlockDefaultTypeInternal _DataBlock_default_instance_;
}  // namespace design
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_node_2eproto[2];
static constexpr const ::_pb::ServiceDescriptor**
    file_level_service_descriptors_node_2eproto = nullptr;
const ::uint32_t
    TableStruct_node_2eproto::offsets[] ABSL_ATTRIBUTE_SECTION_VARIABLE(
        protodesc_cold) = {
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::NodeBaseValue, _internal_metadata_),
        ~0u,  // no _extensions_
        PROTOBUF_FIELD_OFFSET(::design::NodeBaseValue, _impl_._oneof_case_[0]),
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        PROTOBUF_FIELD_OFFSET(::design::NodeBaseValue, _impl_.OneValue_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::ValInt, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ValInt, _impl_.val_int_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::DVec4, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::DVec4, _impl_.x_),
        PROTOBUF_FIELD_OFFSET(::design::DVec4, _impl_.y_),
        PROTOBUF_FIELD_OFFSET(::design::DVec4, _impl_.z_),
        PROTOBUF_FIELD_OFFSET(::design::DVec4, _impl_.w_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::VectorDouble, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::VectorDouble, _impl_.vec_double_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::VectorInt, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::VectorInt, _impl_.vec_int_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::VectorInt64, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::VectorInt64, _impl_.vec_int64_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::SVecInt, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::SVecInt, _impl_.vec_sint_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::VectorString, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::VectorString, _impl_.vec_string_),
        PROTOBUF_FIELD_OFFSET(::design::StringIntegerMap_MapEntry_DoNotUse, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::StringIntegerMap_MapEntry_DoNotUse, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::StringIntegerMap_MapEntry_DoNotUse, _impl_.key_),
        PROTOBUF_FIELD_OFFSET(::design::StringIntegerMap_MapEntry_DoNotUse, _impl_.value_),
        0,
        1,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::StringIntegerMap, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::StringIntegerMap, _impl_.map_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrs_AttrMapEntry_DoNotUse, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrs_AttrMapEntry_DoNotUse, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrs_AttrMapEntry_DoNotUse, _impl_.key_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrs_AttrMapEntry_DoNotUse, _impl_.value_),
        0,
        1,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrs, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrs, _impl_.attr_map_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::StrIntRecord, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::StrIntRecord, _impl_.id_),
        PROTOBUF_FIELD_OFFSET(::design::StrIntRecord, _impl_.str_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.id_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.name_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.type_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.attrs_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.traceid_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.action_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.additionaljson_),
        PROTOBUF_FIELD_OFFSET(::design::NodeAttrsRecord, _impl_.additionalinfo_),
        ~0u,
        ~0u,
        ~0u,
        0,
        ~0u,
        ~0u,
        ~0u,
        1,
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeRecord, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeRecord, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeRecord, _impl_.id_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeRecord, _impl_.children_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeRecord, _impl_.traceid_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeRecord, _impl_.additionaljson_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeRecord, _impl_.additionalinfo_),
        ~0u,
        0,
        ~0u,
        ~0u,
        1,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::AdditionalInfo, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::AdditionalInfo, _impl_.user_),
        PROTOBUF_FIELD_OFFSET(::design::AdditionalInfo, _impl_.time_),
        PROTOBUF_FIELD_OFFSET(::design::AdditionalInfo, _impl_.checkout_),
        PROTOBUF_FIELD_OFFSET(::design::AdditionalInfo, _impl_.checkoutuser_),
        PROTOBUF_FIELD_OFFSET(::design::AdditionalInfo, _impl_.status_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::OffsetLength, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::OffsetLength, _impl_.offset_),
        PROTOBUF_FIELD_OFFSET(::design::OffsetLength, _impl_.length_),
        PROTOBUF_FIELD_OFFSET(::design::OffsetLength, _impl_.crc_),
        PROTOBUF_FIELD_OFFSET(::design::NodeOffsetRecord, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::NodeOffsetRecord, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::NodeOffsetRecord, _impl_.node_id_),
        PROTOBUF_FIELD_OFFSET(::design::NodeOffsetRecord, _impl_.off_len_),
        ~0u,
        0,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::ProjectInfo, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectInfo, _impl_.projectcode_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectInfo, _impl_.classification_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectInfo, _impl_.subtopic_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectInfo, _impl_.user_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectInfo, _impl_.progresspercentage_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfigInfo, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfigInfo, _impl_.json_),
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::MessageQueuesPackage, _internal_metadata_),
        ~0u,  // no _extensions_
        PROTOBUF_FIELD_OFFSET(::design::MessageQueuesPackage, _impl_._oneof_case_[0]),
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        ::_pbi::kInvalidFieldOffsetTag,
        ::_pbi::kInvalidFieldOffsetTag,
        PROTOBUF_FIELD_OFFSET(::design::MessageQueuesPackage, _impl_.Record_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig_ConfigsEntry_DoNotUse, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig_ConfigsEntry_DoNotUse, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig_ConfigsEntry_DoNotUse, _impl_.key_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig_ConfigsEntry_DoNotUse, _impl_.value_),
        0,
        1,
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectConfig, _impl_.configs_),
        0,
        ~0u,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::KeyCode, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::KeyCode, _impl_.key_),
        PROTOBUF_FIELD_OFFSET(::design::KeyCode, _impl_.code_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectCodes, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectCodes, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectCodes, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectCodes, _impl_.key_code_),
        0,
        ~0u,
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodes, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodes, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodes, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodes, _impl_.nodes_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodes, _impl_.action_),
        0,
        ~0u,
        ~0u,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesResult, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesResult, _impl_.code_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesResult, _impl_.message_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset_OffsetsEntry_DoNotUse, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_.key_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_.value_),
        0,
        1,
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesOffset, _impl_.offsets_),
        0,
        ~0u,
        PROTOBUF_FIELD_OFFSET(::design::MaxOffset, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::MaxOffset, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::MaxOffset, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::MaxOffset, _impl_.flag_),
        PROTOBUF_FIELD_OFFSET(::design::MaxOffset, _impl_.maxoffset_),
        0,
        ~0u,
        ~0u,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeAction, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeAction, _impl_.parentid_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeAction, _impl_.flag_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeAction, _impl_.leftsiblingid_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeAction, _impl_.siblings_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeActions, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeActions, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeActions, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeActions, _impl_.traceid_),
        PROTOBUF_FIELD_OFFSET(::design::NodeTreeActions, _impl_.actions_),
        0,
        ~0u,
        ~0u,
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesTree, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesTree, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesTree, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::ProjectNodesTree, _impl_.trees_),
        0,
        ~0u,
        PROTOBUF_FIELD_OFFSET(::design::DataBlock, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::DataBlock, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::DataBlock, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::DataBlock, _impl_.flag_),
        PROTOBUF_FIELD_OFFSET(::design::DataBlock, _impl_.action_),
        PROTOBUF_FIELD_OFFSET(::design::DataBlock, _impl_.package_),
        0,
        ~0u,
        ~0u,
        1,
        PROTOBUF_FIELD_OFFSET(::design::UserInfo, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::design::UserInfo, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::UserInfo, _impl_.projectinfo_),
        PROTOBUF_FIELD_OFFSET(::design::UserInfo, _impl_.flag_),
        PROTOBUF_FIELD_OFFSET(::design::UserInfo, _impl_.user_),
        PROTOBUF_FIELD_OFFSET(::design::UserInfo, _impl_.pwd_),
        0,
        ~0u,
        ~0u,
        ~0u,
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::design::LoginResponse, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::design::LoginResponse, _impl_.code_),
        PROTOBUF_FIELD_OFFSET(::design::LoginResponse, _impl_.message_),
        PROTOBUF_FIELD_OFFSET(::design::LoginResponse, _impl_.result_),
};

static const ::_pbi::MigrationSchema
    schemas[] ABSL_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
        {0, -1, -1, sizeof(::design::NodeBaseValue)},
        {18, -1, -1, sizeof(::design::ValInt)},
        {27, -1, -1, sizeof(::design::DVec4)},
        {39, -1, -1, sizeof(::design::VectorDouble)},
        {48, -1, -1, sizeof(::design::VectorInt)},
        {57, -1, -1, sizeof(::design::VectorInt64)},
        {66, -1, -1, sizeof(::design::SVecInt)},
        {75, -1, -1, sizeof(::design::VectorString)},
        {84, 94, -1, sizeof(::design::StringIntegerMap_MapEntry_DoNotUse)},
        {96, -1, -1, sizeof(::design::StringIntegerMap)},
        {105, 115, -1, sizeof(::design::NodeAttrs_AttrMapEntry_DoNotUse)},
        {117, -1, -1, sizeof(::design::NodeAttrs)},
        {126, -1, -1, sizeof(::design::StrIntRecord)},
        {136, 152, -1, sizeof(::design::NodeAttrsRecord)},
        {160, 173, -1, sizeof(::design::NodeTreeRecord)},
        {178, -1, -1, sizeof(::design::AdditionalInfo)},
        {191, -1, -1, sizeof(::design::OffsetLength)},
        {202, 212, -1, sizeof(::design::NodeOffsetRecord)},
        {214, -1, -1, sizeof(::design::ProjectInfo)},
        {227, -1, -1, sizeof(::design::ProjectConfigInfo)},
        {236, -1, -1, sizeof(::design::MessageQueuesPackage)},
        {247, 257, -1, sizeof(::design::ProjectConfig_ConfigsEntry_DoNotUse)},
        {259, 269, -1, sizeof(::design::ProjectConfig)},
        {271, -1, -1, sizeof(::design::KeyCode)},
        {281, 291, -1, sizeof(::design::ProjectCodes)},
        {293, 304, -1, sizeof(::design::ProjectNodes)},
        {307, -1, -1, sizeof(::design::ProjectNodesResult)},
        {317, 327, -1, sizeof(::design::ProjectNodesOffset_OffsetsEntry_DoNotUse)},
        {329, 339, -1, sizeof(::design::ProjectNodesOffset)},
        {341, 352, -1, sizeof(::design::MaxOffset)},
        {355, -1, -1, sizeof(::design::NodeTreeAction)},
        {367, 378, -1, sizeof(::design::NodeTreeActions)},
        {381, 391, -1, sizeof(::design::ProjectNodesTree)},
        {393, 405, -1, sizeof(::design::DataBlock)},
        {409, 421, -1, sizeof(::design::UserInfo)},
        {425, -1, -1, sizeof(::design::LoginResponse)},
};
static const ::_pb::Message* const file_default_instances[] = {
    &::design::_NodeBaseValue_default_instance_._instance,
    &::design::_ValInt_default_instance_._instance,
    &::design::_DVec4_default_instance_._instance,
    &::design::_VectorDouble_default_instance_._instance,
    &::design::_VectorInt_default_instance_._instance,
    &::design::_VectorInt64_default_instance_._instance,
    &::design::_SVecInt_default_instance_._instance,
    &::design::_VectorString_default_instance_._instance,
    &::design::_StringIntegerMap_MapEntry_DoNotUse_default_instance_._instance,
    &::design::_StringIntegerMap_default_instance_._instance,
    &::design::_NodeAttrs_AttrMapEntry_DoNotUse_default_instance_._instance,
    &::design::_NodeAttrs_default_instance_._instance,
    &::design::_StrIntRecord_default_instance_._instance,
    &::design::_NodeAttrsRecord_default_instance_._instance,
    &::design::_NodeTreeRecord_default_instance_._instance,
    &::design::_AdditionalInfo_default_instance_._instance,
    &::design::_OffsetLength_default_instance_._instance,
    &::design::_NodeOffsetRecord_default_instance_._instance,
    &::design::_ProjectInfo_default_instance_._instance,
    &::design::_ProjectConfigInfo_default_instance_._instance,
    &::design::_MessageQueuesPackage_default_instance_._instance,
    &::design::_ProjectConfig_ConfigsEntry_DoNotUse_default_instance_._instance,
    &::design::_ProjectConfig_default_instance_._instance,
    &::design::_KeyCode_default_instance_._instance,
    &::design::_ProjectCodes_default_instance_._instance,
    &::design::_ProjectNodes_default_instance_._instance,
    &::design::_ProjectNodesResult_default_instance_._instance,
    &::design::_ProjectNodesOffset_OffsetsEntry_DoNotUse_default_instance_._instance,
    &::design::_ProjectNodesOffset_default_instance_._instance,
    &::design::_MaxOffset_default_instance_._instance,
    &::design::_NodeTreeAction_default_instance_._instance,
    &::design::_NodeTreeActions_default_instance_._instance,
    &::design::_ProjectNodesTree_default_instance_._instance,
    &::design::_DataBlock_default_instance_._instance,
    &::design::_UserInfo_default_instance_._instance,
    &::design::_LoginResponse_default_instance_._instance,
};
const char descriptor_table_protodef_node_2eproto[] ABSL_ATTRIBUTE_SECTION_VARIABLE(
    protodesc_cold) = {
    "\n\nnode.proto\022\006design\"\225\002\n\rNodeBaseValue\022\022"
    "\n\010val_bool\030\001 \001(\010H\000\022\021\n\007val_int\030\002 \001(\005H\000\022\023\n"
    "\tval_int64\030\004 \001(\003H\000\022\022\n\010val_uint\030\005 \001(\rH\000\022\024"
    "\n\nval_double\030\006 \001(\001H\000\022\024\n\nval_string\030\007 \001(\014"
    "H\000\022$\n\007vec_int\030\025 \001(\0132\021.design.VectorIntH\000"
    "\022*\n\nvec_double\030\026 \001(\0132\024.design.VectorDoub"
    "leH\000\022*\n\nvec_string\030\027 \001(\0132\024.design.Vector"
    "StringH\000B\n\n\010OneValue\"\031\n\006ValInt\022\017\n\007val_in"
    "t\030\001 \001(\005\"3\n\005DVec4\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002 \001(\001\022\t"
    "\n\001z\030\003 \001(\001\022\t\n\001w\030\004 \001(\001\"&\n\014VectorDouble\022\026\n\n"
    "vec_double\030\001 \003(\001B\002\020\001\" \n\tVectorInt\022\023\n\007vec"
    "_int\030\001 \003(\005B\002\020\001\"$\n\013VectorInt64\022\025\n\tvec_int"
    "64\030\001 \003(\003B\002\020\001\"\037\n\007SVecInt\022\024\n\010vec_sint\030\001 \003("
    "\021B\002\020\001\"\"\n\014VectorString\022\022\n\nvec_string\030\001 \003("
    "\014\"n\n\020StringIntegerMap\022.\n\003map\030\001 \003(\0132!.des"
    "ign.StringIntegerMap.MapEntry\032*\n\010MapEntr"
    "y\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\003:\0028\001\"\204\001\n\tNo"
    "deAttrs\0220\n\010attr_map\030\001 \003(\0132\036.design.NodeA"
    "ttrs.AttrMapEntry\032E\n\014AttrMapEntry\022\013\n\003key"
    "\030\001 \001(\003\022$\n\005value\030\002 \001(\0132\025.design.NodeBaseV"
    "alue:\0028\001\"\'\n\014StrIntRecord\022\n\n\002id\030\001 \001(\003\022\013\n\003"
    "str\030\002 \001(\014\"\304\001\n\017NodeAttrsRecord\022\n\n\002id\030\001 \001("
    "\003\022\014\n\004name\030\002 \001(\014\022\014\n\004type\030\003 \001(\005\022 \n\005attrs\030\004"
    " \001(\0132\021.design.NodeAttrs\022\017\n\007traceId\030\005 \001(\003"
    "\022\016\n\006action\030\006 \001(\005\022\026\n\016additionalJSON\030\007 \001(\014"
    "\022.\n\016additionalInfo\030\010 \001(\0132\026.design.Additi"
    "onalInfo\"\234\001\n\016NodeTreeRecord\022\n\n\002id\030\001 \001(\003\022"
    "%\n\010children\030\002 \001(\0132\023.design.VectorInt64\022\017"
    "\n\007traceId\030\003 \001(\003\022\026\n\016additionalJSON\030\004 \001(\014\022"
    ".\n\016additionalInfo\030\005 \001(\0132\026.design.Additio"
    "nalInfo\"d\n\016AdditionalInfo\022\014\n\004user\030\001 \001(\t\022"
    "\014\n\004time\030\002 \001(\003\022\020\n\010checkOut\030\003 \001(\005\022\024\n\014check"
    "OutUser\030\004 \001(\t\022\016\n\006status\030\005 \001(\005\";\n\014OffsetL"
    "ength\022\016\n\006offset\030\001 \001(\003\022\016\n\006length\030\002 \001(\005\022\013\n"
    "\003crc\030\003 \001(\r\"J\n\020NodeOffsetRecord\022\017\n\007node_i"
    "d\030\001 \001(\005\022%\n\007off_len\030\002 \001(\0132\024.design.Offset"
    "Length\"v\n\013ProjectInfo\022\023\n\013projectCode\030\001 \001"
    "(\t\022\026\n\016classification\030\002 \001(\t\022\020\n\010subTopic\030\003"
    " \001(\t\022\014\n\004user\030\004 \001(\t\022\032\n\022progressPercentage"
    "\030\005 \001(\005\"!\n\021ProjectConfigInfo\022\014\n\004json\030\001 \001("
    "\t\"s\n\024MessageQueuesPackage\022\'\n\005trees\030\001 \001(\013"
    "2\026.design.NodeTreeRecordH\000\022(\n\005nodes\030\002 \001("
    "\0132\027.design.NodeAttrsRecordH\000B\010\n\006Record\"\236"
    "\001\n\rProjectConfig\022(\n\013projectInfo\030\001 \001(\0132\023."
    "design.ProjectInfo\0223\n\007configs\030\002 \003(\0132\".de"
    "sign.ProjectConfig.ConfigsEntry\032.\n\014Confi"
    "gsEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\""
    "$\n\007KeyCode\022\013\n\003key\030\001 \001(\014\022\014\n\004code\030\002 \001(\003\"[\n"
    "\014ProjectCodes\022(\n\013projectInfo\030\001 \001(\0132\023.des"
    "ign.ProjectInfo\022!\n\010key_code\030\003 \003(\0132\017.desi"
    "gn.KeyCode\"p\n\014ProjectNodes\022(\n\013projectInf"
    "o\030\001 \001(\0132\023.design.ProjectInfo\022&\n\005nodes\030\002 "
    "\003(\0132\027.design.NodeAttrsRecord\022\016\n\006action\030\003"
    " \001(\005\"3\n\022ProjectNodesResult\022\014\n\004code\030\001 \001(\014"
    "\022\017\n\007message\030\002 \001(\t\"\276\001\n\022ProjectNodesOffset"
    "\022(\n\013projectInfo\030\001 \001(\0132\023.design.ProjectIn"
    "fo\0228\n\007offsets\030\002 \003(\0132\'.design.ProjectNode"
    "sOffset.OffsetsEntry\032D\n\014OffsetsEntry\022\013\n\003"
    "key\030\001 \001(\004\022#\n\005value\030\002 \001(\0132\024.design.Offset"
    "Length:\0028\001\"V\n\tMaxOffset\022(\n\013projectInfo\030\001"
    " \001(\0132\023.design.ProjectInfo\022\014\n\004flag\030\002 \001(\005\022"
    "\021\n\tmaxOffset\030\003 \001(\003\"q\n\016NodeTreeAction\022\020\n\010"
    "parentId\030\001 \001(\003\022$\n\004flag\030\002 \001(\0162\026.design.Tr"
    "eeActionFlag\022\025\n\rleftSiblingId\030\003 \001(\003\022\020\n\010s"
    "iblings\030\004 \003(\003\"u\n\017NodeTreeActions\022(\n\013proj"
    "ectInfo\030\001 \001(\0132\023.design.ProjectInfo\022\017\n\007tr"
    "aceId\030\002 \001(\003\022\'\n\007actions\030\003 \003(\0132\026.design.No"
    "deTreeAction\"c\n\020ProjectNodesTree\022(\n\013proj"
    "ectInfo\030\001 \001(\0132\023.design.ProjectInfo\022%\n\005tr"
    "ees\030\002 \003(\0132\026.design.NodeTreeRecord\"\202\001\n\tDa"
    "taBlock\022(\n\013projectInfo\030\001 \001(\0132\023.design.Pr"
    "ojectInfo\022\014\n\004flag\030\002 \001(\005\022\016\n\006action\030\003 \001(\005\022"
    "-\n\007package\030\005 \001(\0132\034.design.MessageQueuesP"
    "ackage\"p\n\010UserInfo\022(\n\013projectInfo\030\001 \001(\0132"
    "\023.design.ProjectInfo\022\037\n\004flag\030\002 \001(\0162\021.des"
    "ign.LoginFlag\022\014\n\004user\030\003 \001(\t\022\013\n\003pwd\030\004 \001(\t"
    "\">\n\rLoginResponse\022\014\n\004code\030\001 \001(\005\022\017\n\007messa"
    "ge\030\002 \001(\t\022\016\n\006result\030\003 \001(\t*c\n\016TreeActionFl"
    "ag\022\014\n\010TAF_NONE\020\000\022\016\n\nTAF_INSERT\020\001\022\016\n\nTAF_"
    "DELETE\020\002\022\017\n\013TAF_REVERSE\020\004\022\022\n\016TAF_UPDATE_"
    "ALL\020\010*5\n\tLoginFlag\022\016\n\nPUBLIC_KEY\020\000\022\t\n\005LO"
    "GIN\020\001\022\r\n\tLOGIN_OUT\020\002B\025\n\023com.saas.dc3d.pr"
    "otob\006proto3"
};
static ::absl::once_flag descriptor_table_node_2eproto_once;
PROTOBUF_CONSTINIT const ::_pbi::DescriptorTable descriptor_table_node_2eproto = {
    false,
    false,
    3291,
    descriptor_table_protodef_node_2eproto,
    "node.proto",
    &descriptor_table_node_2eproto_once,
    nullptr,
    0,
    36,
    schemas,
    file_default_instances,
    TableStruct_node_2eproto::offsets,
    file_level_enum_descriptors_node_2eproto,
    file_level_service_descriptors_node_2eproto,
};
namespace design {
const ::google::protobuf::EnumDescriptor* TreeActionFlag_descriptor() {
  ::google::protobuf::internal::AssignDescriptors(&descriptor_table_node_2eproto);
  return file_level_enum_descriptors_node_2eproto[0];
}
PROTOBUF_CONSTINIT const uint32_t TreeActionFlag_internal_data_[] = {
    196608u, 32u, 34u, };
bool TreeActionFlag_IsValid(int value) {
  return 0 <= value && value <= 8 && ((279u >> value) & 1) != 0;
}
const ::google::protobuf::EnumDescriptor* LoginFlag_descriptor() {
  ::google::protobuf::internal::AssignDescriptors(&descriptor_table_node_2eproto);
  return file_level_enum_descriptors_node_2eproto[1];
}
PROTOBUF_CONSTINIT const uint32_t LoginFlag_internal_data_[] = {
    196608u, 0u, };
bool LoginFlag_IsValid(int value) {
  return 0 <= value && value <= 2;
}
// ===================================================================

class NodeBaseValue::_Internal {
 public:
  static constexpr ::int32_t kOneofCaseOffset =
      PROTOBUF_FIELD_OFFSET(::design::NodeBaseValue, _impl_._oneof_case_);
};

void NodeBaseValue::set_allocated_vec_int(::design::VectorInt* vec_int) {
  ::google::protobuf::Arena* message_arena = GetArena();
  clear_OneValue();
  if (vec_int) {
    ::google::protobuf::Arena* submessage_arena = vec_int->GetArena();
    if (message_arena != submessage_arena) {
      vec_int = ::google::protobuf::internal::GetOwnedMessage(message_arena, vec_int, submessage_arena);
    }
    set_has_vec_int();
    _impl_.OneValue_.vec_int_ = vec_int;
  }
  // @@protoc_insertion_point(field_set_allocated:design.NodeBaseValue.vec_int)
}
void NodeBaseValue::set_allocated_vec_double(::design::VectorDouble* vec_double) {
  ::google::protobuf::Arena* message_arena = GetArena();
  clear_OneValue();
  if (vec_double) {
    ::google::protobuf::Arena* submessage_arena = vec_double->GetArena();
    if (message_arena != submessage_arena) {
      vec_double = ::google::protobuf::internal::GetOwnedMessage(message_arena, vec_double, submessage_arena);
    }
    set_has_vec_double();
    _impl_.OneValue_.vec_double_ = vec_double;
  }
  // @@protoc_insertion_point(field_set_allocated:design.NodeBaseValue.vec_double)
}
void NodeBaseValue::set_allocated_vec_string(::design::VectorString* vec_string) {
  ::google::protobuf::Arena* message_arena = GetArena();
  clear_OneValue();
  if (vec_string) {
    ::google::protobuf::Arena* submessage_arena = vec_string->GetArena();
    if (message_arena != submessage_arena) {
      vec_string = ::google::protobuf::internal::GetOwnedMessage(message_arena, vec_string, submessage_arena);
    }
    set_has_vec_string();
    _impl_.OneValue_.vec_string_ = vec_string;
  }
  // @@protoc_insertion_point(field_set_allocated:design.NodeBaseValue.vec_string)
}
NodeBaseValue::NodeBaseValue(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.NodeBaseValue)
}
inline PROTOBUF_NDEBUG_INLINE NodeBaseValue::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::NodeBaseValue& from_msg)
      : OneValue_{},
        _cached_size_{0},
        _oneof_case_{from._oneof_case_[0]} {}

NodeBaseValue::NodeBaseValue(
    ::google::protobuf::Arena* arena,
    const NodeBaseValue& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  NodeBaseValue* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  switch (OneValue_case()) {
    case ONEVALUE_NOT_SET:
      break;
      case kValBool:
        _impl_.OneValue_.val_bool_ = from._impl_.OneValue_.val_bool_;
        break;
      case kValInt:
        _impl_.OneValue_.val_int_ = from._impl_.OneValue_.val_int_;
        break;
      case kValInt64:
        _impl_.OneValue_.val_int64_ = from._impl_.OneValue_.val_int64_;
        break;
      case kValUint:
        _impl_.OneValue_.val_uint_ = from._impl_.OneValue_.val_uint_;
        break;
      case kValDouble:
        _impl_.OneValue_.val_double_ = from._impl_.OneValue_.val_double_;
        break;
      case kValString:
        new (&_impl_.OneValue_.val_string_) decltype(_impl_.OneValue_.val_string_){arena, from._impl_.OneValue_.val_string_};
        break;
      case kVecInt:
        _impl_.OneValue_.vec_int_ = ::google::protobuf::Message::CopyConstruct<::design::VectorInt>(arena, *from._impl_.OneValue_.vec_int_);
        break;
      case kVecDouble:
        _impl_.OneValue_.vec_double_ = ::google::protobuf::Message::CopyConstruct<::design::VectorDouble>(arena, *from._impl_.OneValue_.vec_double_);
        break;
      case kVecString:
        _impl_.OneValue_.vec_string_ = ::google::protobuf::Message::CopyConstruct<::design::VectorString>(arena, *from._impl_.OneValue_.vec_string_);
        break;
  }

  // @@protoc_insertion_point(copy_constructor:design.NodeBaseValue)
}
inline PROTOBUF_NDEBUG_INLINE NodeBaseValue::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : OneValue_{},
        _cached_size_{0},
        _oneof_case_{} {}

inline void NodeBaseValue::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
NodeBaseValue::~NodeBaseValue() {
  // @@protoc_insertion_point(destructor:design.NodeBaseValue)
  SharedDtor(*this);
}
inline void NodeBaseValue::SharedDtor(MessageLite& self) {
  NodeBaseValue& this_ = static_cast<NodeBaseValue&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  if (this_.has_OneValue()) {
    this_.clear_OneValue();
  }
  this_._impl_.~Impl_();
}

void NodeBaseValue::clear_OneValue() {
// @@protoc_insertion_point(one_of_clear_start:design.NodeBaseValue)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  switch (OneValue_case()) {
    case kValBool: {
      // No need to clear
      break;
    }
    case kValInt: {
      // No need to clear
      break;
    }
    case kValInt64: {
      // No need to clear
      break;
    }
    case kValUint: {
      // No need to clear
      break;
    }
    case kValDouble: {
      // No need to clear
      break;
    }
    case kValString: {
      _impl_.OneValue_.val_string_.Destroy();
      break;
    }
    case kVecInt: {
      if (GetArena() == nullptr) {
        delete _impl_.OneValue_.vec_int_;
      } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
        ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.OneValue_.vec_int_);
      }
      break;
    }
    case kVecDouble: {
      if (GetArena() == nullptr) {
        delete _impl_.OneValue_.vec_double_;
      } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
        ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.OneValue_.vec_double_);
      }
      break;
    }
    case kVecString: {
      if (GetArena() == nullptr) {
        delete _impl_.OneValue_.vec_string_;
      } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
        ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.OneValue_.vec_string_);
      }
      break;
    }
    case ONEVALUE_NOT_SET: {
      break;
    }
  }
  _impl_._oneof_case_[0] = ONEVALUE_NOT_SET;
}


inline void* NodeBaseValue::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) NodeBaseValue(arena);
}
constexpr auto NodeBaseValue::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(NodeBaseValue),
                                            alignof(NodeBaseValue));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull NodeBaseValue::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_NodeBaseValue_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &NodeBaseValue::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<NodeBaseValue>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &NodeBaseValue::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<NodeBaseValue>(), &NodeBaseValue::ByteSizeLong,
            &NodeBaseValue::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_._cached_size_),
        false,
    },
    &NodeBaseValue::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* NodeBaseValue::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 9, 3, 0, 2> NodeBaseValue::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    23, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4287627140,  // skipmap
    offsetof(decltype(_table_), field_entries),
    9,  // num_field_entries
    3,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeBaseValue>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // bool val_bool = 1;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.val_bool_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kBool)},
    // int32 val_int = 2;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.val_int_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kInt32)},
    // int64 val_int64 = 4;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.val_int64_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kInt64)},
    // uint32 val_uint = 5;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.val_uint_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kUInt32)},
    // double val_double = 6;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.val_double_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kDouble)},
    // bytes val_string = 7;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.val_string_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kBytes | ::_fl::kRepAString)},
    // .design.VectorInt vec_int = 21;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.vec_int_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kMessage | ::_fl::kTvTable)},
    // .design.VectorDouble vec_double = 22;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.vec_double_), _Internal::kOneofCaseOffset + 0, 1,
    (0 | ::_fl::kFcOneof | ::_fl::kMessage | ::_fl::kTvTable)},
    // .design.VectorString vec_string = 23;
    {PROTOBUF_FIELD_OFFSET(NodeBaseValue, _impl_.OneValue_.vec_string_), _Internal::kOneofCaseOffset + 0, 2,
    (0 | ::_fl::kFcOneof | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::VectorInt>()},
    {::_pbi::TcParser::GetTable<::design::VectorDouble>()},
    {::_pbi::TcParser::GetTable<::design::VectorString>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void NodeBaseValue::Clear() {
// @@protoc_insertion_point(message_clear_start:design.NodeBaseValue)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_OneValue();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* NodeBaseValue::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const NodeBaseValue& this_ = static_cast<const NodeBaseValue&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* NodeBaseValue::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const NodeBaseValue& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.NodeBaseValue)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          switch (this_.OneValue_case()) {
            case kValBool: {
              target = stream->EnsureSpace(target);
              target = ::_pbi::WireFormatLite::WriteBoolToArray(
                  1, this_._internal_val_bool(), target);
              break;
            }
            case kValInt: {
              target = ::google::protobuf::internal::WireFormatLite::
                  WriteInt32ToArrayWithField<2>(
                      stream, this_._internal_val_int(), target);
              break;
            }
            case kValInt64: {
              target = ::google::protobuf::internal::WireFormatLite::
                  WriteInt64ToArrayWithField<4>(
                      stream, this_._internal_val_int64(), target);
              break;
            }
            case kValUint: {
              target = stream->EnsureSpace(target);
              target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                  5, this_._internal_val_uint(), target);
              break;
            }
            case kValDouble: {
              target = stream->EnsureSpace(target);
              target = ::_pbi::WireFormatLite::WriteDoubleToArray(
                  6, this_._internal_val_double(), target);
              break;
            }
            case kValString: {
              const std::string& _s = this_._internal_val_string();
              target = stream->WriteBytesMaybeAliased(7, _s, target);
              break;
            }
            case kVecInt: {
              target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                  21, *this_._impl_.OneValue_.vec_int_, this_._impl_.OneValue_.vec_int_->GetCachedSize(), target,
                  stream);
              break;
            }
            case kVecDouble: {
              target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                  22, *this_._impl_.OneValue_.vec_double_, this_._impl_.OneValue_.vec_double_->GetCachedSize(), target,
                  stream);
              break;
            }
            case kVecString: {
              target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                  23, *this_._impl_.OneValue_.vec_string_, this_._impl_.OneValue_.vec_string_->GetCachedSize(), target,
                  stream);
              break;
            }
            default:
              break;
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.NodeBaseValue)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t NodeBaseValue::ByteSizeLong(const MessageLite& base) {
          const NodeBaseValue& this_ = static_cast<const NodeBaseValue&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t NodeBaseValue::ByteSizeLong() const {
          const NodeBaseValue& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.NodeBaseValue)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          switch (this_.OneValue_case()) {
            // bool val_bool = 1;
            case kValBool: {
              total_size += 2;
              break;
            }
            // int32 val_int = 2;
            case kValInt: {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_val_int());
              break;
            }
            // int64 val_int64 = 4;
            case kValInt64: {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_val_int64());
              break;
            }
            // uint32 val_uint = 5;
            case kValUint: {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_val_uint());
              break;
            }
            // double val_double = 6;
            case kValDouble: {
              total_size += 9;
              break;
            }
            // bytes val_string = 7;
            case kValString: {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_val_string());
              break;
            }
            // .design.VectorInt vec_int = 21;
            case kVecInt: {
              total_size += 2 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.OneValue_.vec_int_);
              break;
            }
            // .design.VectorDouble vec_double = 22;
            case kVecDouble: {
              total_size += 2 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.OneValue_.vec_double_);
              break;
            }
            // .design.VectorString vec_string = 23;
            case kVecString: {
              total_size += 2 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.OneValue_.vec_string_);
              break;
            }
            case ONEVALUE_NOT_SET: {
              break;
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void NodeBaseValue::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<NodeBaseValue*>(&to_msg);
  auto& from = static_cast<const NodeBaseValue&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.NodeBaseValue)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (const uint32_t oneof_from_case = from._impl_._oneof_case_[0]) {
    const uint32_t oneof_to_case = _this->_impl_._oneof_case_[0];
    const bool oneof_needs_init = oneof_to_case != oneof_from_case;
    if (oneof_needs_init) {
      if (oneof_to_case != 0) {
        _this->clear_OneValue();
      }
      _this->_impl_._oneof_case_[0] = oneof_from_case;
    }

    switch (oneof_from_case) {
      case kValBool: {
        _this->_impl_.OneValue_.val_bool_ = from._impl_.OneValue_.val_bool_;
        break;
      }
      case kValInt: {
        _this->_impl_.OneValue_.val_int_ = from._impl_.OneValue_.val_int_;
        break;
      }
      case kValInt64: {
        _this->_impl_.OneValue_.val_int64_ = from._impl_.OneValue_.val_int64_;
        break;
      }
      case kValUint: {
        _this->_impl_.OneValue_.val_uint_ = from._impl_.OneValue_.val_uint_;
        break;
      }
      case kValDouble: {
        _this->_impl_.OneValue_.val_double_ = from._impl_.OneValue_.val_double_;
        break;
      }
      case kValString: {
        if (oneof_needs_init) {
          _this->_impl_.OneValue_.val_string_.InitDefault();
        }
        _this->_impl_.OneValue_.val_string_.Set(from._internal_val_string(), arena);
        break;
      }
      case kVecInt: {
        if (oneof_needs_init) {
          _this->_impl_.OneValue_.vec_int_ =
              ::google::protobuf::Message::CopyConstruct<::design::VectorInt>(arena, *from._impl_.OneValue_.vec_int_);
        } else {
          _this->_impl_.OneValue_.vec_int_->MergeFrom(from._internal_vec_int());
        }
        break;
      }
      case kVecDouble: {
        if (oneof_needs_init) {
          _this->_impl_.OneValue_.vec_double_ =
              ::google::protobuf::Message::CopyConstruct<::design::VectorDouble>(arena, *from._impl_.OneValue_.vec_double_);
        } else {
          _this->_impl_.OneValue_.vec_double_->MergeFrom(from._internal_vec_double());
        }
        break;
      }
      case kVecString: {
        if (oneof_needs_init) {
          _this->_impl_.OneValue_.vec_string_ =
              ::google::protobuf::Message::CopyConstruct<::design::VectorString>(arena, *from._impl_.OneValue_.vec_string_);
        } else {
          _this->_impl_.OneValue_.vec_string_->MergeFrom(from._internal_vec_string());
        }
        break;
      }
      case ONEVALUE_NOT_SET:
        break;
    }
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void NodeBaseValue::CopyFrom(const NodeBaseValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.NodeBaseValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void NodeBaseValue::InternalSwap(NodeBaseValue* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.OneValue_, other->_impl_.OneValue_);
  swap(_impl_._oneof_case_[0], other->_impl_._oneof_case_[0]);
}

::google::protobuf::Metadata NodeBaseValue::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class ValInt::_Internal {
 public:
};

ValInt::ValInt(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ValInt)
}
ValInt::ValInt(
    ::google::protobuf::Arena* arena, const ValInt& from)
    : ValInt(arena) {
  MergeFrom(from);
}
inline PROTOBUF_NDEBUG_INLINE ValInt::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void ValInt::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.val_int_ = {};
}
ValInt::~ValInt() {
  // @@protoc_insertion_point(destructor:design.ValInt)
  SharedDtor(*this);
}
inline void ValInt::SharedDtor(MessageLite& self) {
  ValInt& this_ = static_cast<ValInt&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* ValInt::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ValInt(arena);
}
constexpr auto ValInt::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(ValInt),
                                            alignof(ValInt));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ValInt::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ValInt_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ValInt::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ValInt>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ValInt::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ValInt>(), &ValInt::ByteSizeLong,
            &ValInt::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ValInt, _impl_._cached_size_),
        false,
    },
    &ValInt::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ValInt::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 0, 2> ValInt::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ValInt>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // int32 val_int = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ValInt, _impl_.val_int_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(ValInt, _impl_.val_int_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int32 val_int = 1;
    {PROTOBUF_FIELD_OFFSET(ValInt, _impl_.val_int_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void ValInt::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ValInt)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.val_int_ = 0;
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ValInt::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ValInt& this_ = static_cast<const ValInt&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ValInt::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ValInt& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ValInt)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int32 val_int = 1;
          if (this_._internal_val_int() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<1>(
                    stream, this_._internal_val_int(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ValInt)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ValInt::ByteSizeLong(const MessageLite& base) {
          const ValInt& this_ = static_cast<const ValInt&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ValInt::ByteSizeLong() const {
          const ValInt& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ValInt)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

           {
            // int32 val_int = 1;
            if (this_._internal_val_int() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_val_int());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ValInt::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ValInt*>(&to_msg);
  auto& from = static_cast<const ValInt&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ValInt)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_val_int() != 0) {
    _this->_impl_.val_int_ = from._impl_.val_int_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ValInt::CopyFrom(const ValInt& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ValInt)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ValInt::InternalSwap(ValInt* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
        swap(_impl_.val_int_, other->_impl_.val_int_);
}

::google::protobuf::Metadata ValInt::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class DVec4::_Internal {
 public:
};

DVec4::DVec4(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.DVec4)
}
DVec4::DVec4(
    ::google::protobuf::Arena* arena, const DVec4& from)
    : DVec4(arena) {
  MergeFrom(from);
}
inline PROTOBUF_NDEBUG_INLINE DVec4::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void DVec4::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, x_),
           0,
           offsetof(Impl_, w_) -
               offsetof(Impl_, x_) +
               sizeof(Impl_::w_));
}
DVec4::~DVec4() {
  // @@protoc_insertion_point(destructor:design.DVec4)
  SharedDtor(*this);
}
inline void DVec4::SharedDtor(MessageLite& self) {
  DVec4& this_ = static_cast<DVec4&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* DVec4::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) DVec4(arena);
}
constexpr auto DVec4::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(DVec4),
                                            alignof(DVec4));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull DVec4::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_DVec4_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &DVec4::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<DVec4>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &DVec4::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<DVec4>(), &DVec4::ByteSizeLong,
            &DVec4::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(DVec4, _impl_._cached_size_),
        false,
    },
    &DVec4::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* DVec4::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 0, 0, 2> DVec4::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::DVec4>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // double w = 4;
    {::_pbi::TcParser::FastF64S1,
     {33, 63, 0, PROTOBUF_FIELD_OFFSET(DVec4, _impl_.w_)}},
    // double x = 1;
    {::_pbi::TcParser::FastF64S1,
     {9, 63, 0, PROTOBUF_FIELD_OFFSET(DVec4, _impl_.x_)}},
    // double y = 2;
    {::_pbi::TcParser::FastF64S1,
     {17, 63, 0, PROTOBUF_FIELD_OFFSET(DVec4, _impl_.y_)}},
    // double z = 3;
    {::_pbi::TcParser::FastF64S1,
     {25, 63, 0, PROTOBUF_FIELD_OFFSET(DVec4, _impl_.z_)}},
  }}, {{
    65535, 65535
  }}, {{
    // double x = 1;
    {PROTOBUF_FIELD_OFFSET(DVec4, _impl_.x_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // double y = 2;
    {PROTOBUF_FIELD_OFFSET(DVec4, _impl_.y_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // double z = 3;
    {PROTOBUF_FIELD_OFFSET(DVec4, _impl_.z_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
    // double w = 4;
    {PROTOBUF_FIELD_OFFSET(DVec4, _impl_.w_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kDouble)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void DVec4::Clear() {
// @@protoc_insertion_point(message_clear_start:design.DVec4)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.w_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.w_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* DVec4::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const DVec4& this_ = static_cast<const DVec4&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* DVec4::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const DVec4& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.DVec4)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // double x = 1;
          if (::absl::bit_cast<::uint64_t>(this_._internal_x()) != 0) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteDoubleToArray(
                1, this_._internal_x(), target);
          }

          // double y = 2;
          if (::absl::bit_cast<::uint64_t>(this_._internal_y()) != 0) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteDoubleToArray(
                2, this_._internal_y(), target);
          }

          // double z = 3;
          if (::absl::bit_cast<::uint64_t>(this_._internal_z()) != 0) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteDoubleToArray(
                3, this_._internal_z(), target);
          }

          // double w = 4;
          if (::absl::bit_cast<::uint64_t>(this_._internal_w()) != 0) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteDoubleToArray(
                4, this_._internal_w(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.DVec4)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t DVec4::ByteSizeLong(const MessageLite& base) {
          const DVec4& this_ = static_cast<const DVec4&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t DVec4::ByteSizeLong() const {
          const DVec4& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.DVec4)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // double x = 1;
            if (::absl::bit_cast<::uint64_t>(this_._internal_x()) != 0) {
              total_size += 9;
            }
            // double y = 2;
            if (::absl::bit_cast<::uint64_t>(this_._internal_y()) != 0) {
              total_size += 9;
            }
            // double z = 3;
            if (::absl::bit_cast<::uint64_t>(this_._internal_z()) != 0) {
              total_size += 9;
            }
            // double w = 4;
            if (::absl::bit_cast<::uint64_t>(this_._internal_w()) != 0) {
              total_size += 9;
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void DVec4::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<DVec4*>(&to_msg);
  auto& from = static_cast<const DVec4&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.DVec4)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (::absl::bit_cast<::uint64_t>(from._internal_x()) != 0) {
    _this->_impl_.x_ = from._impl_.x_;
  }
  if (::absl::bit_cast<::uint64_t>(from._internal_y()) != 0) {
    _this->_impl_.y_ = from._impl_.y_;
  }
  if (::absl::bit_cast<::uint64_t>(from._internal_z()) != 0) {
    _this->_impl_.z_ = from._impl_.z_;
  }
  if (::absl::bit_cast<::uint64_t>(from._internal_w()) != 0) {
    _this->_impl_.w_ = from._impl_.w_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void DVec4::CopyFrom(const DVec4& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.DVec4)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void DVec4::InternalSwap(DVec4* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DVec4, _impl_.w_)
      + sizeof(DVec4::_impl_.w_)
      - PROTOBUF_FIELD_OFFSET(DVec4, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::google::protobuf::Metadata DVec4::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class VectorDouble::_Internal {
 public:
};

VectorDouble::VectorDouble(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.VectorDouble)
}
inline PROTOBUF_NDEBUG_INLINE VectorDouble::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::VectorDouble& from_msg)
      : vec_double_{visibility, arena, from.vec_double_},
        _cached_size_{0} {}

VectorDouble::VectorDouble(
    ::google::protobuf::Arena* arena,
    const VectorDouble& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  VectorDouble* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.VectorDouble)
}
inline PROTOBUF_NDEBUG_INLINE VectorDouble::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : vec_double_{visibility, arena},
        _cached_size_{0} {}

inline void VectorDouble::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
VectorDouble::~VectorDouble() {
  // @@protoc_insertion_point(destructor:design.VectorDouble)
  SharedDtor(*this);
}
inline void VectorDouble::SharedDtor(MessageLite& self) {
  VectorDouble& this_ = static_cast<VectorDouble&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* VectorDouble::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) VectorDouble(arena);
}
constexpr auto VectorDouble::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(VectorDouble, _impl_.vec_double_) +
          decltype(VectorDouble::_impl_.vec_double_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(VectorDouble), alignof(VectorDouble), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&VectorDouble::PlacementNew_,
                                 sizeof(VectorDouble),
                                 alignof(VectorDouble));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull VectorDouble::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_VectorDouble_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &VectorDouble::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<VectorDouble>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &VectorDouble::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<VectorDouble>(), &VectorDouble::ByteSizeLong,
            &VectorDouble::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(VectorDouble, _impl_._cached_size_),
        false,
    },
    &VectorDouble::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* VectorDouble::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 0, 2> VectorDouble::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::VectorDouble>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated double vec_double = 1 [packed = true];
    {::_pbi::TcParser::FastF64P1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(VectorDouble, _impl_.vec_double_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated double vec_double = 1 [packed = true];
    {PROTOBUF_FIELD_OFFSET(VectorDouble, _impl_.vec_double_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kPackedDouble)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void VectorDouble::Clear() {
// @@protoc_insertion_point(message_clear_start:design.VectorDouble)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.vec_double_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* VectorDouble::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const VectorDouble& this_ = static_cast<const VectorDouble&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* VectorDouble::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const VectorDouble& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.VectorDouble)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // repeated double vec_double = 1 [packed = true];
          if (this_._internal_vec_double_size() > 0) {
            target = stream->WriteFixedPacked(1, this_._internal_vec_double(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.VectorDouble)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t VectorDouble::ByteSizeLong(const MessageLite& base) {
          const VectorDouble& this_ = static_cast<const VectorDouble&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t VectorDouble::ByteSizeLong() const {
          const VectorDouble& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.VectorDouble)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated double vec_double = 1 [packed = true];
            {
              std::size_t data_size = std::size_t{8} *
                  ::_pbi::FromIntSize(this_._internal_vec_double_size());
              std::size_t tag_size = data_size == 0
                  ? 0
                  : 1 + ::_pbi::WireFormatLite::Int32Size(
                                      static_cast<int32_t>(data_size));
              total_size += tag_size + data_size;
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void VectorDouble::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<VectorDouble*>(&to_msg);
  auto& from = static_cast<const VectorDouble&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.VectorDouble)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_vec_double()->MergeFrom(from._internal_vec_double());
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void VectorDouble::CopyFrom(const VectorDouble& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.VectorDouble)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void VectorDouble::InternalSwap(VectorDouble* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.vec_double_.InternalSwap(&other->_impl_.vec_double_);
}

::google::protobuf::Metadata VectorDouble::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class VectorInt::_Internal {
 public:
};

VectorInt::VectorInt(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.VectorInt)
}
inline PROTOBUF_NDEBUG_INLINE VectorInt::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::VectorInt& from_msg)
      : vec_int_{visibility, arena, from.vec_int_},
        _vec_int_cached_byte_size_{0},
        _cached_size_{0} {}

VectorInt::VectorInt(
    ::google::protobuf::Arena* arena,
    const VectorInt& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  VectorInt* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.VectorInt)
}
inline PROTOBUF_NDEBUG_INLINE VectorInt::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : vec_int_{visibility, arena},
        _vec_int_cached_byte_size_{0},
        _cached_size_{0} {}

inline void VectorInt::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
VectorInt::~VectorInt() {
  // @@protoc_insertion_point(destructor:design.VectorInt)
  SharedDtor(*this);
}
inline void VectorInt::SharedDtor(MessageLite& self) {
  VectorInt& this_ = static_cast<VectorInt&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* VectorInt::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) VectorInt(arena);
}
constexpr auto VectorInt::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(VectorInt, _impl_.vec_int_) +
          decltype(VectorInt::_impl_.vec_int_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(VectorInt), alignof(VectorInt), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&VectorInt::PlacementNew_,
                                 sizeof(VectorInt),
                                 alignof(VectorInt));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull VectorInt::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_VectorInt_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &VectorInt::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<VectorInt>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &VectorInt::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<VectorInt>(), &VectorInt::ByteSizeLong,
            &VectorInt::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(VectorInt, _impl_._cached_size_),
        false,
    },
    &VectorInt::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* VectorInt::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 0, 2> VectorInt::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::VectorInt>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated int32 vec_int = 1 [packed = true];
    {::_pbi::TcParser::FastV32P1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(VectorInt, _impl_.vec_int_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated int32 vec_int = 1 [packed = true];
    {PROTOBUF_FIELD_OFFSET(VectorInt, _impl_.vec_int_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kPackedInt32)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void VectorInt::Clear() {
// @@protoc_insertion_point(message_clear_start:design.VectorInt)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.vec_int_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* VectorInt::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const VectorInt& this_ = static_cast<const VectorInt&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* VectorInt::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const VectorInt& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.VectorInt)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // repeated int32 vec_int = 1 [packed = true];
          {
            int byte_size = this_._impl_._vec_int_cached_byte_size_.Get();
            if (byte_size > 0) {
              target = stream->WriteInt32Packed(
                  1, this_._internal_vec_int(), byte_size, target);
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.VectorInt)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t VectorInt::ByteSizeLong(const MessageLite& base) {
          const VectorInt& this_ = static_cast<const VectorInt&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t VectorInt::ByteSizeLong() const {
          const VectorInt& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.VectorInt)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated int32 vec_int = 1 [packed = true];
            {
              total_size +=
                  ::_pbi::WireFormatLite::Int32SizeWithPackedTagSize(
                      this_._internal_vec_int(), 1,
                      this_._impl_._vec_int_cached_byte_size_);
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void VectorInt::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<VectorInt*>(&to_msg);
  auto& from = static_cast<const VectorInt&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.VectorInt)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_vec_int()->MergeFrom(from._internal_vec_int());
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void VectorInt::CopyFrom(const VectorInt& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.VectorInt)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void VectorInt::InternalSwap(VectorInt* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.vec_int_.InternalSwap(&other->_impl_.vec_int_);
}

::google::protobuf::Metadata VectorInt::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class VectorInt64::_Internal {
 public:
};

VectorInt64::VectorInt64(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.VectorInt64)
}
inline PROTOBUF_NDEBUG_INLINE VectorInt64::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::VectorInt64& from_msg)
      : vec_int64_{visibility, arena, from.vec_int64_},
        _vec_int64_cached_byte_size_{0},
        _cached_size_{0} {}

VectorInt64::VectorInt64(
    ::google::protobuf::Arena* arena,
    const VectorInt64& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  VectorInt64* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.VectorInt64)
}
inline PROTOBUF_NDEBUG_INLINE VectorInt64::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : vec_int64_{visibility, arena},
        _vec_int64_cached_byte_size_{0},
        _cached_size_{0} {}

inline void VectorInt64::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
VectorInt64::~VectorInt64() {
  // @@protoc_insertion_point(destructor:design.VectorInt64)
  SharedDtor(*this);
}
inline void VectorInt64::SharedDtor(MessageLite& self) {
  VectorInt64& this_ = static_cast<VectorInt64&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* VectorInt64::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) VectorInt64(arena);
}
constexpr auto VectorInt64::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(VectorInt64, _impl_.vec_int64_) +
          decltype(VectorInt64::_impl_.vec_int64_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(VectorInt64), alignof(VectorInt64), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&VectorInt64::PlacementNew_,
                                 sizeof(VectorInt64),
                                 alignof(VectorInt64));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull VectorInt64::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_VectorInt64_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &VectorInt64::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<VectorInt64>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &VectorInt64::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<VectorInt64>(), &VectorInt64::ByteSizeLong,
            &VectorInt64::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(VectorInt64, _impl_._cached_size_),
        false,
    },
    &VectorInt64::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* VectorInt64::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 0, 2> VectorInt64::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::VectorInt64>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated int64 vec_int64 = 1 [packed = true];
    {::_pbi::TcParser::FastV64P1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(VectorInt64, _impl_.vec_int64_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated int64 vec_int64 = 1 [packed = true];
    {PROTOBUF_FIELD_OFFSET(VectorInt64, _impl_.vec_int64_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kPackedInt64)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void VectorInt64::Clear() {
// @@protoc_insertion_point(message_clear_start:design.VectorInt64)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.vec_int64_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* VectorInt64::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const VectorInt64& this_ = static_cast<const VectorInt64&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* VectorInt64::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const VectorInt64& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.VectorInt64)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // repeated int64 vec_int64 = 1 [packed = true];
          {
            int byte_size = this_._impl_._vec_int64_cached_byte_size_.Get();
            if (byte_size > 0) {
              target = stream->WriteInt64Packed(
                  1, this_._internal_vec_int64(), byte_size, target);
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.VectorInt64)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t VectorInt64::ByteSizeLong(const MessageLite& base) {
          const VectorInt64& this_ = static_cast<const VectorInt64&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t VectorInt64::ByteSizeLong() const {
          const VectorInt64& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.VectorInt64)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated int64 vec_int64 = 1 [packed = true];
            {
              total_size +=
                  ::_pbi::WireFormatLite::Int64SizeWithPackedTagSize(
                      this_._internal_vec_int64(), 1,
                      this_._impl_._vec_int64_cached_byte_size_);
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void VectorInt64::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<VectorInt64*>(&to_msg);
  auto& from = static_cast<const VectorInt64&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.VectorInt64)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_vec_int64()->MergeFrom(from._internal_vec_int64());
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void VectorInt64::CopyFrom(const VectorInt64& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.VectorInt64)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void VectorInt64::InternalSwap(VectorInt64* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.vec_int64_.InternalSwap(&other->_impl_.vec_int64_);
}

::google::protobuf::Metadata VectorInt64::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class SVecInt::_Internal {
 public:
};

SVecInt::SVecInt(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.SVecInt)
}
inline PROTOBUF_NDEBUG_INLINE SVecInt::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::SVecInt& from_msg)
      : vec_sint_{visibility, arena, from.vec_sint_},
        _vec_sint_cached_byte_size_{0},
        _cached_size_{0} {}

SVecInt::SVecInt(
    ::google::protobuf::Arena* arena,
    const SVecInt& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SVecInt* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.SVecInt)
}
inline PROTOBUF_NDEBUG_INLINE SVecInt::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : vec_sint_{visibility, arena},
        _vec_sint_cached_byte_size_{0},
        _cached_size_{0} {}

inline void SVecInt::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
SVecInt::~SVecInt() {
  // @@protoc_insertion_point(destructor:design.SVecInt)
  SharedDtor(*this);
}
inline void SVecInt::SharedDtor(MessageLite& self) {
  SVecInt& this_ = static_cast<SVecInt&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* SVecInt::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) SVecInt(arena);
}
constexpr auto SVecInt::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(SVecInt, _impl_.vec_sint_) +
          decltype(SVecInt::_impl_.vec_sint_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(SVecInt), alignof(SVecInt), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&SVecInt::PlacementNew_,
                                 sizeof(SVecInt),
                                 alignof(SVecInt));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull SVecInt::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_SVecInt_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &SVecInt::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<SVecInt>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &SVecInt::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<SVecInt>(), &SVecInt::ByteSizeLong,
            &SVecInt::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(SVecInt, _impl_._cached_size_),
        false,
    },
    &SVecInt::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* SVecInt::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 0, 2> SVecInt::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::SVecInt>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated sint32 vec_sint = 1 [packed = true];
    {::_pbi::TcParser::FastZ32P1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(SVecInt, _impl_.vec_sint_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated sint32 vec_sint = 1 [packed = true];
    {PROTOBUF_FIELD_OFFSET(SVecInt, _impl_.vec_sint_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kPackedSInt32)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void SVecInt::Clear() {
// @@protoc_insertion_point(message_clear_start:design.SVecInt)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.vec_sint_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* SVecInt::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const SVecInt& this_ = static_cast<const SVecInt&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* SVecInt::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const SVecInt& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.SVecInt)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // repeated sint32 vec_sint = 1 [packed = true];
          {
            int byte_size = this_._impl_._vec_sint_cached_byte_size_.Get();
            if (byte_size > 0) {
              target = stream->WriteSInt32Packed(
                  1, this_._internal_vec_sint(), byte_size, target);
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.SVecInt)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t SVecInt::ByteSizeLong(const MessageLite& base) {
          const SVecInt& this_ = static_cast<const SVecInt&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t SVecInt::ByteSizeLong() const {
          const SVecInt& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.SVecInt)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated sint32 vec_sint = 1 [packed = true];
            {
              total_size +=
                  ::_pbi::WireFormatLite::SInt32SizeWithPackedTagSize(
                      this_._internal_vec_sint(), 1,
                      this_._impl_._vec_sint_cached_byte_size_);
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void SVecInt::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<SVecInt*>(&to_msg);
  auto& from = static_cast<const SVecInt&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.SVecInt)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_vec_sint()->MergeFrom(from._internal_vec_sint());
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void SVecInt::CopyFrom(const SVecInt& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.SVecInt)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void SVecInt::InternalSwap(SVecInt* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.vec_sint_.InternalSwap(&other->_impl_.vec_sint_);
}

::google::protobuf::Metadata SVecInt::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class VectorString::_Internal {
 public:
};

VectorString::VectorString(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.VectorString)
}
inline PROTOBUF_NDEBUG_INLINE VectorString::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::VectorString& from_msg)
      : vec_string_{visibility, arena, from.vec_string_},
        _cached_size_{0} {}

VectorString::VectorString(
    ::google::protobuf::Arena* arena,
    const VectorString& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  VectorString* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.VectorString)
}
inline PROTOBUF_NDEBUG_INLINE VectorString::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : vec_string_{visibility, arena},
        _cached_size_{0} {}

inline void VectorString::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
VectorString::~VectorString() {
  // @@protoc_insertion_point(destructor:design.VectorString)
  SharedDtor(*this);
}
inline void VectorString::SharedDtor(MessageLite& self) {
  VectorString& this_ = static_cast<VectorString&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* VectorString::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) VectorString(arena);
}
constexpr auto VectorString::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(VectorString, _impl_.vec_string_) +
          decltype(VectorString::_impl_.vec_string_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(VectorString), alignof(VectorString), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&VectorString::PlacementNew_,
                                 sizeof(VectorString),
                                 alignof(VectorString));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull VectorString::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_VectorString_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &VectorString::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<VectorString>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &VectorString::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<VectorString>(), &VectorString::ByteSizeLong,
            &VectorString::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(VectorString, _impl_._cached_size_),
        false,
    },
    &VectorString::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* VectorString::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 0, 2> VectorString::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::VectorString>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated bytes vec_string = 1;
    {::_pbi::TcParser::FastBR1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(VectorString, _impl_.vec_string_)}},
  }}, {{
    65535, 65535
  }}, {{
    // repeated bytes vec_string = 1;
    {PROTOBUF_FIELD_OFFSET(VectorString, _impl_.vec_string_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kBytes | ::_fl::kRepSString)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void VectorString::Clear() {
// @@protoc_insertion_point(message_clear_start:design.VectorString)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.vec_string_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* VectorString::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const VectorString& this_ = static_cast<const VectorString&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* VectorString::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const VectorString& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.VectorString)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // repeated bytes vec_string = 1;
          for (int i = 0, n = this_._internal_vec_string_size(); i < n; ++i) {
            const auto& s = this_._internal_vec_string().Get(i);
            target = stream->WriteBytes(1, s, target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.VectorString)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t VectorString::ByteSizeLong(const MessageLite& base) {
          const VectorString& this_ = static_cast<const VectorString&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t VectorString::ByteSizeLong() const {
          const VectorString& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.VectorString)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated bytes vec_string = 1;
            {
              total_size +=
                  1 * ::google::protobuf::internal::FromIntSize(this_._internal_vec_string().size());
              for (int i = 0, n = this_._internal_vec_string().size(); i < n; ++i) {
                total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
                    this_._internal_vec_string().Get(i));
              }
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void VectorString::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<VectorString*>(&to_msg);
  auto& from = static_cast<const VectorString&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.VectorString)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_vec_string()->MergeFrom(from._internal_vec_string());
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void VectorString::CopyFrom(const VectorString& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.VectorString)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void VectorString::InternalSwap(VectorString* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.vec_string_.InternalSwap(&other->_impl_.vec_string_);
}

::google::protobuf::Metadata VectorString::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

#if defined(PROTOBUF_CUSTOM_VTABLE)
              StringIntegerMap_MapEntry_DoNotUse::StringIntegerMap_MapEntry_DoNotUse() : SuperType(_class_data_.base()) {}
              StringIntegerMap_MapEntry_DoNotUse::StringIntegerMap_MapEntry_DoNotUse(::google::protobuf::Arena* arena)
                  : SuperType(arena, _class_data_.base()) {}
#else   // PROTOBUF_CUSTOM_VTABLE
              StringIntegerMap_MapEntry_DoNotUse::StringIntegerMap_MapEntry_DoNotUse() : SuperType() {}
              StringIntegerMap_MapEntry_DoNotUse::StringIntegerMap_MapEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
#endif  // PROTOBUF_CUSTOM_VTABLE
              inline void* StringIntegerMap_MapEntry_DoNotUse::PlacementNew_(const void*, void* mem,
                                                      ::google::protobuf::Arena* arena) {
                return ::new (mem) StringIntegerMap_MapEntry_DoNotUse(arena);
              }
              constexpr auto StringIntegerMap_MapEntry_DoNotUse::InternalNewImpl_() {
                return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(StringIntegerMap_MapEntry_DoNotUse),
                                                          alignof(StringIntegerMap_MapEntry_DoNotUse));
              }
              PROTOBUF_CONSTINIT
              PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
              const ::google::protobuf::internal::ClassDataFull StringIntegerMap_MapEntry_DoNotUse::_class_data_ = {
                  ::google::protobuf::internal::ClassData{
                      &_StringIntegerMap_MapEntry_DoNotUse_default_instance_._instance,
                      &_table_.header,
                      nullptr,  // OnDemandRegisterArenaDtor
                      nullptr,  // IsInitialized
                      &StringIntegerMap_MapEntry_DoNotUse::MergeImpl,
                      ::google::protobuf::Message::GetNewImpl<StringIntegerMap_MapEntry_DoNotUse>(),
              #if defined(PROTOBUF_CUSTOM_VTABLE)
                      &StringIntegerMap_MapEntry_DoNotUse::SharedDtor,
                      static_cast<void (::google::protobuf::MessageLite::*)()>(
                          &StringIntegerMap_MapEntry_DoNotUse::ClearImpl),
                          ::google::protobuf::Message::ByteSizeLongImpl, ::google::protobuf::Message::_InternalSerializeImpl
                          ,
              #endif  // PROTOBUF_CUSTOM_VTABLE
                      PROTOBUF_FIELD_OFFSET(StringIntegerMap_MapEntry_DoNotUse, _impl_._cached_size_),
                      false,
                  },
                  &StringIntegerMap_MapEntry_DoNotUse::kDescriptorMethods,
                  &descriptor_table_node_2eproto,
                  nullptr,  // tracker
              };
              const ::google::protobuf::internal::ClassData* StringIntegerMap_MapEntry_DoNotUse::GetClassData() const {
                ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
                ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
                return _class_data_.base();
              }
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 44, 2> StringIntegerMap_MapEntry_DoNotUse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(StringIntegerMap_MapEntry_DoNotUse, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::DiscardEverythingFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::StringIntegerMap_MapEntry_DoNotUse>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // int64 value = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(StringIntegerMap_MapEntry_DoNotUse, _impl_.value_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(StringIntegerMap_MapEntry_DoNotUse, _impl_.value_)}},
    // string key = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(StringIntegerMap_MapEntry_DoNotUse, _impl_.key_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string key = 1;
    {PROTOBUF_FIELD_OFFSET(StringIntegerMap_MapEntry_DoNotUse, _impl_.key_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int64 value = 2;
    {PROTOBUF_FIELD_OFFSET(StringIntegerMap_MapEntry_DoNotUse, _impl_.value_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
  }},
  // no aux_entries
  {{
    "\40\3\0\0\0\0\0\0"
    "design.StringIntegerMap.MapEntry"
    "key"
  }},
};

// ===================================================================

class StringIntegerMap::_Internal {
 public:
};

StringIntegerMap::StringIntegerMap(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.StringIntegerMap)
}
inline PROTOBUF_NDEBUG_INLINE StringIntegerMap::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::StringIntegerMap& from_msg)
      : map_{visibility, arena, from.map_},
        _cached_size_{0} {}

StringIntegerMap::StringIntegerMap(
    ::google::protobuf::Arena* arena,
    const StringIntegerMap& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  StringIntegerMap* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.StringIntegerMap)
}
inline PROTOBUF_NDEBUG_INLINE StringIntegerMap::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : map_{visibility, arena},
        _cached_size_{0} {}

inline void StringIntegerMap::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
StringIntegerMap::~StringIntegerMap() {
  // @@protoc_insertion_point(destructor:design.StringIntegerMap)
  SharedDtor(*this);
}
inline void StringIntegerMap::SharedDtor(MessageLite& self) {
  StringIntegerMap& this_ = static_cast<StringIntegerMap&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* StringIntegerMap::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) StringIntegerMap(arena);
}
constexpr auto StringIntegerMap::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(StringIntegerMap, _impl_.map_) +
          decltype(StringIntegerMap::_impl_.map_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(StringIntegerMap, _impl_.map_) +
          decltype(StringIntegerMap::_impl_.map_)::
              InternalGetArenaOffsetAlt(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(StringIntegerMap), alignof(StringIntegerMap), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&StringIntegerMap::PlacementNew_,
                                 sizeof(StringIntegerMap),
                                 alignof(StringIntegerMap));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull StringIntegerMap::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_StringIntegerMap_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &StringIntegerMap::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<StringIntegerMap>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &StringIntegerMap::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<StringIntegerMap>(), &StringIntegerMap::ByteSizeLong,
            &StringIntegerMap::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(StringIntegerMap, _impl_._cached_size_),
        false,
    },
    &StringIntegerMap::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* StringIntegerMap::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 1, 35, 2> StringIntegerMap::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::StringIntegerMap>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // map<string, int64> map = 1;
    {PROTOBUF_FIELD_OFFSET(StringIntegerMap, _impl_.map_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMap)},
  }}, {{
    {::_pbi::TcParser::GetMapAuxInfo<
        decltype(StringIntegerMap()._impl_.map_)>(
        1, 0, 0, 9,
        3)},
  }}, {{
    "\27\3\0\0\0\0\0\0"
    "design.StringIntegerMap"
    "map"
  }},
};

PROTOBUF_NOINLINE void StringIntegerMap::Clear() {
// @@protoc_insertion_point(message_clear_start:design.StringIntegerMap)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.map_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* StringIntegerMap::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const StringIntegerMap& this_ = static_cast<const StringIntegerMap&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* StringIntegerMap::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const StringIntegerMap& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.StringIntegerMap)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // map<string, int64> map = 1;
          if (!this_._internal_map().empty()) {
            using MapType = ::google::protobuf::Map<std::string, ::int64_t>;
            using WireHelper = _pbi::MapEntryFuncs<std::string, ::int64_t,
                                           _pbi::WireFormatLite::TYPE_STRING,
                                           _pbi::WireFormatLite::TYPE_INT64>;
            const auto& field = this_._internal_map();

            if (stream->IsSerializationDeterministic() && field.size() > 1) {
              for (const auto& entry : ::google::protobuf::internal::MapSorterPtr<MapType>(field)) {
                target = WireHelper::InternalSerialize(
                    1, entry.first, entry.second, target, stream);
                ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                    entry.first.data(), static_cast<int>(entry.first.length()),
 ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.StringIntegerMap.map");
              }
            } else {
              for (const auto& entry : field) {
                target = WireHelper::InternalSerialize(
                    1, entry.first, entry.second, target, stream);
                ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                    entry.first.data(), static_cast<int>(entry.first.length()),
 ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.StringIntegerMap.map");
              }
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.StringIntegerMap)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t StringIntegerMap::ByteSizeLong(const MessageLite& base) {
          const StringIntegerMap& this_ = static_cast<const StringIntegerMap&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t StringIntegerMap::ByteSizeLong() const {
          const StringIntegerMap& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.StringIntegerMap)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // map<string, int64> map = 1;
            {
              total_size +=
                  1 * ::google::protobuf::internal::FromIntSize(this_._internal_map_size());
              for (const auto& entry : this_._internal_map()) {
                total_size += _pbi::MapEntryFuncs<std::string, ::int64_t,
                                               _pbi::WireFormatLite::TYPE_STRING,
                                               _pbi::WireFormatLite::TYPE_INT64>::ByteSizeLong(entry.first, entry.second);
              }
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void StringIntegerMap::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<StringIntegerMap*>(&to_msg);
  auto& from = static_cast<const StringIntegerMap&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.StringIntegerMap)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.map_.MergeFrom(from._impl_.map_);
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void StringIntegerMap::CopyFrom(const StringIntegerMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.StringIntegerMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void StringIntegerMap::InternalSwap(StringIntegerMap* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.map_.InternalSwap(&other->_impl_.map_);
}

::google::protobuf::Metadata StringIntegerMap::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

#if defined(PROTOBUF_CUSTOM_VTABLE)
              NodeAttrs_AttrMapEntry_DoNotUse::NodeAttrs_AttrMapEntry_DoNotUse() : SuperType(_class_data_.base()) {}
              NodeAttrs_AttrMapEntry_DoNotUse::NodeAttrs_AttrMapEntry_DoNotUse(::google::protobuf::Arena* arena)
                  : SuperType(arena, _class_data_.base()) {}
#else   // PROTOBUF_CUSTOM_VTABLE
              NodeAttrs_AttrMapEntry_DoNotUse::NodeAttrs_AttrMapEntry_DoNotUse() : SuperType() {}
              NodeAttrs_AttrMapEntry_DoNotUse::NodeAttrs_AttrMapEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
#endif  // PROTOBUF_CUSTOM_VTABLE
              inline void* NodeAttrs_AttrMapEntry_DoNotUse::PlacementNew_(const void*, void* mem,
                                                      ::google::protobuf::Arena* arena) {
                return ::new (mem) NodeAttrs_AttrMapEntry_DoNotUse(arena);
              }
              constexpr auto NodeAttrs_AttrMapEntry_DoNotUse::InternalNewImpl_() {
                return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(NodeAttrs_AttrMapEntry_DoNotUse),
                                                          alignof(NodeAttrs_AttrMapEntry_DoNotUse));
              }
              PROTOBUF_CONSTINIT
              PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
              const ::google::protobuf::internal::ClassDataFull NodeAttrs_AttrMapEntry_DoNotUse::_class_data_ = {
                  ::google::protobuf::internal::ClassData{
                      &_NodeAttrs_AttrMapEntry_DoNotUse_default_instance_._instance,
                      &_table_.header,
                      nullptr,  // OnDemandRegisterArenaDtor
                      nullptr,  // IsInitialized
                      &NodeAttrs_AttrMapEntry_DoNotUse::MergeImpl,
                      ::google::protobuf::Message::GetNewImpl<NodeAttrs_AttrMapEntry_DoNotUse>(),
              #if defined(PROTOBUF_CUSTOM_VTABLE)
                      &NodeAttrs_AttrMapEntry_DoNotUse::SharedDtor,
                      static_cast<void (::google::protobuf::MessageLite::*)()>(
                          &NodeAttrs_AttrMapEntry_DoNotUse::ClearImpl),
                          ::google::protobuf::Message::ByteSizeLongImpl, ::google::protobuf::Message::_InternalSerializeImpl
                          ,
              #endif  // PROTOBUF_CUSTOM_VTABLE
                      PROTOBUF_FIELD_OFFSET(NodeAttrs_AttrMapEntry_DoNotUse, _impl_._cached_size_),
                      false,
                  },
                  &NodeAttrs_AttrMapEntry_DoNotUse::kDescriptorMethods,
                  &descriptor_table_node_2eproto,
                  nullptr,  // tracker
              };
              const ::google::protobuf::internal::ClassData* NodeAttrs_AttrMapEntry_DoNotUse::GetClassData() const {
                ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
                ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
                return _class_data_.base();
              }
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 1, 0, 2> NodeAttrs_AttrMapEntry_DoNotUse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(NodeAttrs_AttrMapEntry_DoNotUse, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::DiscardEverythingFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeAttrs_AttrMapEntry_DoNotUse>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // .design.NodeBaseValue value = 2;
    {::_pbi::TcParser::FastMtS1,
     {18, 0, 0, PROTOBUF_FIELD_OFFSET(NodeAttrs_AttrMapEntry_DoNotUse, _impl_.value_)}},
    // int64 key = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeAttrs_AttrMapEntry_DoNotUse, _impl_.key_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(NodeAttrs_AttrMapEntry_DoNotUse, _impl_.key_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 key = 1;
    {PROTOBUF_FIELD_OFFSET(NodeAttrs_AttrMapEntry_DoNotUse, _impl_.key_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // .design.NodeBaseValue value = 2;
    {PROTOBUF_FIELD_OFFSET(NodeAttrs_AttrMapEntry_DoNotUse, _impl_.value_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::NodeBaseValue>()},
  }}, {{
  }},
};

// ===================================================================

class NodeAttrs::_Internal {
 public:
};

NodeAttrs::NodeAttrs(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.NodeAttrs)
}
inline PROTOBUF_NDEBUG_INLINE NodeAttrs::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::NodeAttrs& from_msg)
      : attr_map_{visibility, arena, from.attr_map_},
        _cached_size_{0} {}

NodeAttrs::NodeAttrs(
    ::google::protobuf::Arena* arena,
    const NodeAttrs& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  NodeAttrs* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.NodeAttrs)
}
inline PROTOBUF_NDEBUG_INLINE NodeAttrs::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : attr_map_{visibility, arena},
        _cached_size_{0} {}

inline void NodeAttrs::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
NodeAttrs::~NodeAttrs() {
  // @@protoc_insertion_point(destructor:design.NodeAttrs)
  SharedDtor(*this);
}
inline void NodeAttrs::SharedDtor(MessageLite& self) {
  NodeAttrs& this_ = static_cast<NodeAttrs&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* NodeAttrs::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) NodeAttrs(arena);
}
constexpr auto NodeAttrs::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(NodeAttrs, _impl_.attr_map_) +
          decltype(NodeAttrs::_impl_.attr_map_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(NodeAttrs, _impl_.attr_map_) +
          decltype(NodeAttrs::_impl_.attr_map_)::
              InternalGetArenaOffsetAlt(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(NodeAttrs), alignof(NodeAttrs), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&NodeAttrs::PlacementNew_,
                                 sizeof(NodeAttrs),
                                 alignof(NodeAttrs));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull NodeAttrs::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_NodeAttrs_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &NodeAttrs::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<NodeAttrs>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &NodeAttrs::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<NodeAttrs>(), &NodeAttrs::ByteSizeLong,
            &NodeAttrs::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(NodeAttrs, _impl_._cached_size_),
        false,
    },
    &NodeAttrs::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* NodeAttrs::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 2, 0, 2> NodeAttrs::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeAttrs>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // map<int64, .design.NodeBaseValue> attr_map = 1;
    {PROTOBUF_FIELD_OFFSET(NodeAttrs, _impl_.attr_map_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kMap)},
  }}, {{
    {::_pbi::TcParser::GetMapAuxInfo<
        decltype(NodeAttrs()._impl_.attr_map_)>(
        0, 0, 0, 3,
        11)},
    {::_pbi::TcParser::GetTable<::design::NodeBaseValue>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void NodeAttrs::Clear() {
// @@protoc_insertion_point(message_clear_start:design.NodeAttrs)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.attr_map_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* NodeAttrs::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const NodeAttrs& this_ = static_cast<const NodeAttrs&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* NodeAttrs::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const NodeAttrs& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.NodeAttrs)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // map<int64, .design.NodeBaseValue> attr_map = 1;
          if (!this_._internal_attr_map().empty()) {
            using MapType = ::google::protobuf::Map<::int64_t, ::design::NodeBaseValue>;
            using WireHelper = _pbi::MapEntryFuncs<::int64_t, ::design::NodeBaseValue,
                                           _pbi::WireFormatLite::TYPE_INT64,
                                           _pbi::WireFormatLite::TYPE_MESSAGE>;
            const auto& field = this_._internal_attr_map();

            if (stream->IsSerializationDeterministic() && field.size() > 1) {
              for (const auto& entry : ::google::protobuf::internal::MapSorterFlat<MapType>(field)) {
                target = WireHelper::InternalSerialize(
                    1, entry.first, entry.second, target, stream);
              }
            } else {
              for (const auto& entry : field) {
                target = WireHelper::InternalSerialize(
                    1, entry.first, entry.second, target, stream);
              }
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.NodeAttrs)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t NodeAttrs::ByteSizeLong(const MessageLite& base) {
          const NodeAttrs& this_ = static_cast<const NodeAttrs&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t NodeAttrs::ByteSizeLong() const {
          const NodeAttrs& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.NodeAttrs)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // map<int64, .design.NodeBaseValue> attr_map = 1;
            {
              total_size +=
                  1 * ::google::protobuf::internal::FromIntSize(this_._internal_attr_map_size());
              for (const auto& entry : this_._internal_attr_map()) {
                total_size += _pbi::MapEntryFuncs<::int64_t, ::design::NodeBaseValue,
                                               _pbi::WireFormatLite::TYPE_INT64,
                                               _pbi::WireFormatLite::TYPE_MESSAGE>::ByteSizeLong(entry.first, entry.second);
              }
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void NodeAttrs::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<NodeAttrs*>(&to_msg);
  auto& from = static_cast<const NodeAttrs&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.NodeAttrs)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.attr_map_.MergeFrom(from._impl_.attr_map_);
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void NodeAttrs::CopyFrom(const NodeAttrs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.NodeAttrs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void NodeAttrs::InternalSwap(NodeAttrs* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.attr_map_.InternalSwap(&other->_impl_.attr_map_);
}

::google::protobuf::Metadata NodeAttrs::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class StrIntRecord::_Internal {
 public:
};

StrIntRecord::StrIntRecord(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.StrIntRecord)
}
inline PROTOBUF_NDEBUG_INLINE StrIntRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::StrIntRecord& from_msg)
      : str_(arena, from.str_),
        _cached_size_{0} {}

StrIntRecord::StrIntRecord(
    ::google::protobuf::Arena* arena,
    const StrIntRecord& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  StrIntRecord* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_.id_ = from._impl_.id_;

  // @@protoc_insertion_point(copy_constructor:design.StrIntRecord)
}
inline PROTOBUF_NDEBUG_INLINE StrIntRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : str_(arena),
        _cached_size_{0} {}

inline void StrIntRecord::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.id_ = {};
}
StrIntRecord::~StrIntRecord() {
  // @@protoc_insertion_point(destructor:design.StrIntRecord)
  SharedDtor(*this);
}
inline void StrIntRecord::SharedDtor(MessageLite& self) {
  StrIntRecord& this_ = static_cast<StrIntRecord&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.str_.Destroy();
  this_._impl_.~Impl_();
}

inline void* StrIntRecord::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) StrIntRecord(arena);
}
constexpr auto StrIntRecord::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(StrIntRecord),
                                            alignof(StrIntRecord));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull StrIntRecord::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_StrIntRecord_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &StrIntRecord::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<StrIntRecord>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &StrIntRecord::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<StrIntRecord>(), &StrIntRecord::ByteSizeLong,
            &StrIntRecord::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(StrIntRecord, _impl_._cached_size_),
        false,
    },
    &StrIntRecord::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* StrIntRecord::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 0, 2> StrIntRecord::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::StrIntRecord>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // bytes str = 2;
    {::_pbi::TcParser::FastBS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(StrIntRecord, _impl_.str_)}},
    // int64 id = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(StrIntRecord, _impl_.id_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(StrIntRecord, _impl_.id_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 id = 1;
    {PROTOBUF_FIELD_OFFSET(StrIntRecord, _impl_.id_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // bytes str = 2;
    {PROTOBUF_FIELD_OFFSET(StrIntRecord, _impl_.str_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBytes | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void StrIntRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:design.StrIntRecord)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.str_.ClearToEmpty();
  _impl_.id_ = ::int64_t{0};
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* StrIntRecord::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const StrIntRecord& this_ = static_cast<const StrIntRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* StrIntRecord::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const StrIntRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.StrIntRecord)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int64 id = 1;
          if (this_._internal_id() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<1>(
                    stream, this_._internal_id(), target);
          }

          // bytes str = 2;
          if (!this_._internal_str().empty()) {
            const std::string& _s = this_._internal_str();
            target = stream->WriteBytesMaybeAliased(2, _s, target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.StrIntRecord)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t StrIntRecord::ByteSizeLong(const MessageLite& base) {
          const StrIntRecord& this_ = static_cast<const StrIntRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t StrIntRecord::ByteSizeLong() const {
          const StrIntRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.StrIntRecord)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // bytes str = 2;
            if (!this_._internal_str().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_str());
            }
            // int64 id = 1;
            if (this_._internal_id() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_id());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void StrIntRecord::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<StrIntRecord*>(&to_msg);
  auto& from = static_cast<const StrIntRecord&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.StrIntRecord)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_str().empty()) {
    _this->_internal_set_str(from._internal_str());
  }
  if (from._internal_id() != 0) {
    _this->_impl_.id_ = from._impl_.id_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void StrIntRecord::CopyFrom(const StrIntRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.StrIntRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void StrIntRecord::InternalSwap(StrIntRecord* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.str_, &other->_impl_.str_, arena);
        swap(_impl_.id_, other->_impl_.id_);
}

::google::protobuf::Metadata StrIntRecord::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class NodeAttrsRecord::_Internal {
 public:
  using HasBits =
      decltype(std::declval<NodeAttrsRecord>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_._has_bits_);
};

NodeAttrsRecord::NodeAttrsRecord(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.NodeAttrsRecord)
}
inline PROTOBUF_NDEBUG_INLINE NodeAttrsRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::NodeAttrsRecord& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        name_(arena, from.name_),
        additionaljson_(arena, from.additionaljson_) {}

NodeAttrsRecord::NodeAttrsRecord(
    ::google::protobuf::Arena* arena,
    const NodeAttrsRecord& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  NodeAttrsRecord* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.attrs_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::NodeAttrs>(
                              arena, *from._impl_.attrs_)
                        : nullptr;
  _impl_.additionalinfo_ = (cached_has_bits & 0x00000002u) ? ::google::protobuf::Message::CopyConstruct<::design::AdditionalInfo>(
                              arena, *from._impl_.additionalinfo_)
                        : nullptr;
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, id_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, id_),
           offsetof(Impl_, traceid_) -
               offsetof(Impl_, id_) +
               sizeof(Impl_::traceid_));

  // @@protoc_insertion_point(copy_constructor:design.NodeAttrsRecord)
}
inline PROTOBUF_NDEBUG_INLINE NodeAttrsRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        name_(arena),
        additionaljson_(arena) {}

inline void NodeAttrsRecord::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, attrs_),
           0,
           offsetof(Impl_, traceid_) -
               offsetof(Impl_, attrs_) +
               sizeof(Impl_::traceid_));
}
NodeAttrsRecord::~NodeAttrsRecord() {
  // @@protoc_insertion_point(destructor:design.NodeAttrsRecord)
  SharedDtor(*this);
}
inline void NodeAttrsRecord::SharedDtor(MessageLite& self) {
  NodeAttrsRecord& this_ = static_cast<NodeAttrsRecord&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.name_.Destroy();
  this_._impl_.additionaljson_.Destroy();
  delete this_._impl_.attrs_;
  delete this_._impl_.additionalinfo_;
  this_._impl_.~Impl_();
}

inline void* NodeAttrsRecord::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) NodeAttrsRecord(arena);
}
constexpr auto NodeAttrsRecord::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(NodeAttrsRecord),
                                            alignof(NodeAttrsRecord));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull NodeAttrsRecord::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_NodeAttrsRecord_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &NodeAttrsRecord::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<NodeAttrsRecord>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &NodeAttrsRecord::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<NodeAttrsRecord>(), &NodeAttrsRecord::ByteSizeLong,
            &NodeAttrsRecord::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_._cached_size_),
        false,
    },
    &NodeAttrsRecord::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* NodeAttrsRecord::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 8, 2, 0, 2> NodeAttrsRecord::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_._has_bits_),
    0, // no _extensions_
    8, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967040,  // skipmap
    offsetof(decltype(_table_), field_entries),
    8,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeAttrsRecord>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // .design.AdditionalInfo additionalInfo = 8;
    {::_pbi::TcParser::FastMtS1,
     {66, 1, 1, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.additionalinfo_)}},
    // int64 id = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeAttrsRecord, _impl_.id_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.id_)}},
    // bytes name = 2;
    {::_pbi::TcParser::FastBS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.name_)}},
    // int32 type = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(NodeAttrsRecord, _impl_.type_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.type_)}},
    // .design.NodeAttrs attrs = 4;
    {::_pbi::TcParser::FastMtS1,
     {34, 0, 0, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.attrs_)}},
    // int64 traceId = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeAttrsRecord, _impl_.traceid_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.traceid_)}},
    // int32 action = 6;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(NodeAttrsRecord, _impl_.action_), 63>(),
     {48, 63, 0, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.action_)}},
    // bytes additionalJSON = 7;
    {::_pbi::TcParser::FastBS1,
     {58, 63, 0, PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.additionaljson_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 id = 1;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.id_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // bytes name = 2;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.name_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBytes | ::_fl::kRepAString)},
    // int32 type = 3;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.type_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // .design.NodeAttrs attrs = 4;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.attrs_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // int64 traceId = 5;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.traceid_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // int32 action = 6;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.action_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // bytes additionalJSON = 7;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.additionaljson_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBytes | ::_fl::kRepAString)},
    // .design.AdditionalInfo additionalInfo = 8;
    {PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.additionalinfo_), _Internal::kHasBitsOffset + 1, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::NodeAttrs>()},
    {::_pbi::TcParser::GetTable<::design::AdditionalInfo>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void NodeAttrsRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:design.NodeAttrsRecord)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.name_.ClearToEmpty();
  _impl_.additionaljson_.ClearToEmpty();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(_impl_.attrs_ != nullptr);
      _impl_.attrs_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(_impl_.additionalinfo_ != nullptr);
      _impl_.additionalinfo_->Clear();
    }
  }
  ::memset(&_impl_.id_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.traceid_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.traceid_));
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* NodeAttrsRecord::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const NodeAttrsRecord& this_ = static_cast<const NodeAttrsRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* NodeAttrsRecord::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const NodeAttrsRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.NodeAttrsRecord)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int64 id = 1;
          if (this_._internal_id() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<1>(
                    stream, this_._internal_id(), target);
          }

          // bytes name = 2;
          if (!this_._internal_name().empty()) {
            const std::string& _s = this_._internal_name();
            target = stream->WriteBytesMaybeAliased(2, _s, target);
          }

          // int32 type = 3;
          if (this_._internal_type() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<3>(
                    stream, this_._internal_type(), target);
          }

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.NodeAttrs attrs = 4;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                4, *this_._impl_.attrs_, this_._impl_.attrs_->GetCachedSize(), target,
                stream);
          }

          // int64 traceId = 5;
          if (this_._internal_traceid() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<5>(
                    stream, this_._internal_traceid(), target);
          }

          // int32 action = 6;
          if (this_._internal_action() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<6>(
                    stream, this_._internal_action(), target);
          }

          // bytes additionalJSON = 7;
          if (!this_._internal_additionaljson().empty()) {
            const std::string& _s = this_._internal_additionaljson();
            target = stream->WriteBytesMaybeAliased(7, _s, target);
          }

          // .design.AdditionalInfo additionalInfo = 8;
          if (cached_has_bits & 0x00000002u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                8, *this_._impl_.additionalinfo_, this_._impl_.additionalinfo_->GetCachedSize(), target,
                stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.NodeAttrsRecord)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t NodeAttrsRecord::ByteSizeLong(const MessageLite& base) {
          const NodeAttrsRecord& this_ = static_cast<const NodeAttrsRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t NodeAttrsRecord::ByteSizeLong() const {
          const NodeAttrsRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.NodeAttrsRecord)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // bytes name = 2;
            if (!this_._internal_name().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_name());
            }
            // bytes additionalJSON = 7;
            if (!this_._internal_additionaljson().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_additionaljson());
            }
          }
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x00000003u) {
            // .design.NodeAttrs attrs = 4;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.attrs_);
            }
            // .design.AdditionalInfo additionalInfo = 8;
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.additionalinfo_);
            }
          }
           {
            // int64 id = 1;
            if (this_._internal_id() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_id());
            }
            // int32 type = 3;
            if (this_._internal_type() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_type());
            }
            // int32 action = 6;
            if (this_._internal_action() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_action());
            }
            // int64 traceId = 5;
            if (this_._internal_traceid() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_traceid());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void NodeAttrsRecord::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<NodeAttrsRecord*>(&to_msg);
  auto& from = static_cast<const NodeAttrsRecord&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.NodeAttrsRecord)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_additionaljson().empty()) {
    _this->_internal_set_additionaljson(from._internal_additionaljson());
  }
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(from._impl_.attrs_ != nullptr);
      if (_this->_impl_.attrs_ == nullptr) {
        _this->_impl_.attrs_ =
            ::google::protobuf::Message::CopyConstruct<::design::NodeAttrs>(arena, *from._impl_.attrs_);
      } else {
        _this->_impl_.attrs_->MergeFrom(*from._impl_.attrs_);
      }
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(from._impl_.additionalinfo_ != nullptr);
      if (_this->_impl_.additionalinfo_ == nullptr) {
        _this->_impl_.additionalinfo_ =
            ::google::protobuf::Message::CopyConstruct<::design::AdditionalInfo>(arena, *from._impl_.additionalinfo_);
      } else {
        _this->_impl_.additionalinfo_->MergeFrom(*from._impl_.additionalinfo_);
      }
    }
  }
  if (from._internal_id() != 0) {
    _this->_impl_.id_ = from._impl_.id_;
  }
  if (from._internal_type() != 0) {
    _this->_impl_.type_ = from._impl_.type_;
  }
  if (from._internal_action() != 0) {
    _this->_impl_.action_ = from._impl_.action_;
  }
  if (from._internal_traceid() != 0) {
    _this->_impl_.traceid_ = from._impl_.traceid_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void NodeAttrsRecord::CopyFrom(const NodeAttrsRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.NodeAttrsRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void NodeAttrsRecord::InternalSwap(NodeAttrsRecord* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.name_, &other->_impl_.name_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.additionaljson_, &other->_impl_.additionaljson_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.traceid_)
      + sizeof(NodeAttrsRecord::_impl_.traceid_)
      - PROTOBUF_FIELD_OFFSET(NodeAttrsRecord, _impl_.attrs_)>(
          reinterpret_cast<char*>(&_impl_.attrs_),
          reinterpret_cast<char*>(&other->_impl_.attrs_));
}

::google::protobuf::Metadata NodeAttrsRecord::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class NodeTreeRecord::_Internal {
 public:
  using HasBits =
      decltype(std::declval<NodeTreeRecord>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_._has_bits_);
};

NodeTreeRecord::NodeTreeRecord(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.NodeTreeRecord)
}
inline PROTOBUF_NDEBUG_INLINE NodeTreeRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::NodeTreeRecord& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        additionaljson_(arena, from.additionaljson_) {}

NodeTreeRecord::NodeTreeRecord(
    ::google::protobuf::Arena* arena,
    const NodeTreeRecord& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  NodeTreeRecord* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.children_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::VectorInt64>(
                              arena, *from._impl_.children_)
                        : nullptr;
  _impl_.additionalinfo_ = (cached_has_bits & 0x00000002u) ? ::google::protobuf::Message::CopyConstruct<::design::AdditionalInfo>(
                              arena, *from._impl_.additionalinfo_)
                        : nullptr;
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, id_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, id_),
           offsetof(Impl_, traceid_) -
               offsetof(Impl_, id_) +
               sizeof(Impl_::traceid_));

  // @@protoc_insertion_point(copy_constructor:design.NodeTreeRecord)
}
inline PROTOBUF_NDEBUG_INLINE NodeTreeRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        additionaljson_(arena) {}

inline void NodeTreeRecord::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, children_),
           0,
           offsetof(Impl_, traceid_) -
               offsetof(Impl_, children_) +
               sizeof(Impl_::traceid_));
}
NodeTreeRecord::~NodeTreeRecord() {
  // @@protoc_insertion_point(destructor:design.NodeTreeRecord)
  SharedDtor(*this);
}
inline void NodeTreeRecord::SharedDtor(MessageLite& self) {
  NodeTreeRecord& this_ = static_cast<NodeTreeRecord&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.additionaljson_.Destroy();
  delete this_._impl_.children_;
  delete this_._impl_.additionalinfo_;
  this_._impl_.~Impl_();
}

inline void* NodeTreeRecord::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) NodeTreeRecord(arena);
}
constexpr auto NodeTreeRecord::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(NodeTreeRecord),
                                            alignof(NodeTreeRecord));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull NodeTreeRecord::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_NodeTreeRecord_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &NodeTreeRecord::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<NodeTreeRecord>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &NodeTreeRecord::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<NodeTreeRecord>(), &NodeTreeRecord::ByteSizeLong,
            &NodeTreeRecord::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_._cached_size_),
        false,
    },
    &NodeTreeRecord::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* NodeTreeRecord::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 5, 2, 0, 2> NodeTreeRecord::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_._has_bits_),
    0, // no _extensions_
    5, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967264,  // skipmap
    offsetof(decltype(_table_), field_entries),
    5,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeTreeRecord>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // int64 id = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeTreeRecord, _impl_.id_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.id_)}},
    // .design.VectorInt64 children = 2;
    {::_pbi::TcParser::FastMtS1,
     {18, 0, 0, PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.children_)}},
    // int64 traceId = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeTreeRecord, _impl_.traceid_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.traceid_)}},
    // bytes additionalJSON = 4;
    {::_pbi::TcParser::FastBS1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.additionaljson_)}},
    // .design.AdditionalInfo additionalInfo = 5;
    {::_pbi::TcParser::FastMtS1,
     {42, 1, 1, PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.additionalinfo_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 id = 1;
    {PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.id_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // .design.VectorInt64 children = 2;
    {PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.children_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // int64 traceId = 3;
    {PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.traceid_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // bytes additionalJSON = 4;
    {PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.additionaljson_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBytes | ::_fl::kRepAString)},
    // .design.AdditionalInfo additionalInfo = 5;
    {PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.additionalinfo_), _Internal::kHasBitsOffset + 1, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::VectorInt64>()},
    {::_pbi::TcParser::GetTable<::design::AdditionalInfo>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void NodeTreeRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:design.NodeTreeRecord)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.additionaljson_.ClearToEmpty();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(_impl_.children_ != nullptr);
      _impl_.children_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(_impl_.additionalinfo_ != nullptr);
      _impl_.additionalinfo_->Clear();
    }
  }
  ::memset(&_impl_.id_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.traceid_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.traceid_));
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* NodeTreeRecord::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const NodeTreeRecord& this_ = static_cast<const NodeTreeRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* NodeTreeRecord::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const NodeTreeRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.NodeTreeRecord)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int64 id = 1;
          if (this_._internal_id() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<1>(
                    stream, this_._internal_id(), target);
          }

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.VectorInt64 children = 2;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                2, *this_._impl_.children_, this_._impl_.children_->GetCachedSize(), target,
                stream);
          }

          // int64 traceId = 3;
          if (this_._internal_traceid() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<3>(
                    stream, this_._internal_traceid(), target);
          }

          // bytes additionalJSON = 4;
          if (!this_._internal_additionaljson().empty()) {
            const std::string& _s = this_._internal_additionaljson();
            target = stream->WriteBytesMaybeAliased(4, _s, target);
          }

          // .design.AdditionalInfo additionalInfo = 5;
          if (cached_has_bits & 0x00000002u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                5, *this_._impl_.additionalinfo_, this_._impl_.additionalinfo_->GetCachedSize(), target,
                stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.NodeTreeRecord)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t NodeTreeRecord::ByteSizeLong(const MessageLite& base) {
          const NodeTreeRecord& this_ = static_cast<const NodeTreeRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t NodeTreeRecord::ByteSizeLong() const {
          const NodeTreeRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.NodeTreeRecord)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // bytes additionalJSON = 4;
            if (!this_._internal_additionaljson().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_additionaljson());
            }
          }
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x00000003u) {
            // .design.VectorInt64 children = 2;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.children_);
            }
            // .design.AdditionalInfo additionalInfo = 5;
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.additionalinfo_);
            }
          }
           {
            // int64 id = 1;
            if (this_._internal_id() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_id());
            }
            // int64 traceId = 3;
            if (this_._internal_traceid() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_traceid());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void NodeTreeRecord::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<NodeTreeRecord*>(&to_msg);
  auto& from = static_cast<const NodeTreeRecord&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.NodeTreeRecord)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_additionaljson().empty()) {
    _this->_internal_set_additionaljson(from._internal_additionaljson());
  }
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(from._impl_.children_ != nullptr);
      if (_this->_impl_.children_ == nullptr) {
        _this->_impl_.children_ =
            ::google::protobuf::Message::CopyConstruct<::design::VectorInt64>(arena, *from._impl_.children_);
      } else {
        _this->_impl_.children_->MergeFrom(*from._impl_.children_);
      }
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(from._impl_.additionalinfo_ != nullptr);
      if (_this->_impl_.additionalinfo_ == nullptr) {
        _this->_impl_.additionalinfo_ =
            ::google::protobuf::Message::CopyConstruct<::design::AdditionalInfo>(arena, *from._impl_.additionalinfo_);
      } else {
        _this->_impl_.additionalinfo_->MergeFrom(*from._impl_.additionalinfo_);
      }
    }
  }
  if (from._internal_id() != 0) {
    _this->_impl_.id_ = from._impl_.id_;
  }
  if (from._internal_traceid() != 0) {
    _this->_impl_.traceid_ = from._impl_.traceid_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void NodeTreeRecord::CopyFrom(const NodeTreeRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.NodeTreeRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void NodeTreeRecord::InternalSwap(NodeTreeRecord* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.additionaljson_, &other->_impl_.additionaljson_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.traceid_)
      + sizeof(NodeTreeRecord::_impl_.traceid_)
      - PROTOBUF_FIELD_OFFSET(NodeTreeRecord, _impl_.children_)>(
          reinterpret_cast<char*>(&_impl_.children_),
          reinterpret_cast<char*>(&other->_impl_.children_));
}

::google::protobuf::Metadata NodeTreeRecord::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class AdditionalInfo::_Internal {
 public:
};

AdditionalInfo::AdditionalInfo(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.AdditionalInfo)
}
inline PROTOBUF_NDEBUG_INLINE AdditionalInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::AdditionalInfo& from_msg)
      : user_(arena, from.user_),
        checkoutuser_(arena, from.checkoutuser_),
        _cached_size_{0} {}

AdditionalInfo::AdditionalInfo(
    ::google::protobuf::Arena* arena,
    const AdditionalInfo& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  AdditionalInfo* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, time_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, time_),
           offsetof(Impl_, status_) -
               offsetof(Impl_, time_) +
               sizeof(Impl_::status_));

  // @@protoc_insertion_point(copy_constructor:design.AdditionalInfo)
}
inline PROTOBUF_NDEBUG_INLINE AdditionalInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : user_(arena),
        checkoutuser_(arena),
        _cached_size_{0} {}

inline void AdditionalInfo::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, time_),
           0,
           offsetof(Impl_, status_) -
               offsetof(Impl_, time_) +
               sizeof(Impl_::status_));
}
AdditionalInfo::~AdditionalInfo() {
  // @@protoc_insertion_point(destructor:design.AdditionalInfo)
  SharedDtor(*this);
}
inline void AdditionalInfo::SharedDtor(MessageLite& self) {
  AdditionalInfo& this_ = static_cast<AdditionalInfo&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.user_.Destroy();
  this_._impl_.checkoutuser_.Destroy();
  this_._impl_.~Impl_();
}

inline void* AdditionalInfo::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) AdditionalInfo(arena);
}
constexpr auto AdditionalInfo::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(AdditionalInfo),
                                            alignof(AdditionalInfo));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull AdditionalInfo::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_AdditionalInfo_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &AdditionalInfo::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<AdditionalInfo>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &AdditionalInfo::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<AdditionalInfo>(), &AdditionalInfo::ByteSizeLong,
            &AdditionalInfo::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_._cached_size_),
        false,
    },
    &AdditionalInfo::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* AdditionalInfo::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 5, 0, 46, 2> AdditionalInfo::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    5, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967264,  // skipmap
    offsetof(decltype(_table_), field_entries),
    5,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::AdditionalInfo>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // string user = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.user_)}},
    // int64 time = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(AdditionalInfo, _impl_.time_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.time_)}},
    // int32 checkOut = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(AdditionalInfo, _impl_.checkout_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.checkout_)}},
    // string checkOutUser = 4;
    {::_pbi::TcParser::FastUS1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.checkoutuser_)}},
    // int32 status = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(AdditionalInfo, _impl_.status_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.status_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // string user = 1;
    {PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.user_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int64 time = 2;
    {PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.time_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // int32 checkOut = 3;
    {PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.checkout_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // string checkOutUser = 4;
    {PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.checkoutuser_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int32 status = 5;
    {PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.status_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
    "\25\4\0\0\14\0\0\0"
    "design.AdditionalInfo"
    "user"
    "checkOutUser"
  }},
};

PROTOBUF_NOINLINE void AdditionalInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:design.AdditionalInfo)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.user_.ClearToEmpty();
  _impl_.checkoutuser_.ClearToEmpty();
  ::memset(&_impl_.time_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.status_) -
      reinterpret_cast<char*>(&_impl_.time_)) + sizeof(_impl_.status_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* AdditionalInfo::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const AdditionalInfo& this_ = static_cast<const AdditionalInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* AdditionalInfo::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const AdditionalInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.AdditionalInfo)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // string user = 1;
          if (!this_._internal_user().empty()) {
            const std::string& _s = this_._internal_user();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.AdditionalInfo.user");
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          // int64 time = 2;
          if (this_._internal_time() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<2>(
                    stream, this_._internal_time(), target);
          }

          // int32 checkOut = 3;
          if (this_._internal_checkout() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<3>(
                    stream, this_._internal_checkout(), target);
          }

          // string checkOutUser = 4;
          if (!this_._internal_checkoutuser().empty()) {
            const std::string& _s = this_._internal_checkoutuser();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.AdditionalInfo.checkOutUser");
            target = stream->WriteStringMaybeAliased(4, _s, target);
          }

          // int32 status = 5;
          if (this_._internal_status() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<5>(
                    stream, this_._internal_status(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.AdditionalInfo)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t AdditionalInfo::ByteSizeLong(const MessageLite& base) {
          const AdditionalInfo& this_ = static_cast<const AdditionalInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t AdditionalInfo::ByteSizeLong() const {
          const AdditionalInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.AdditionalInfo)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // string user = 1;
            if (!this_._internal_user().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_user());
            }
            // string checkOutUser = 4;
            if (!this_._internal_checkoutuser().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_checkoutuser());
            }
            // int64 time = 2;
            if (this_._internal_time() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_time());
            }
            // int32 checkOut = 3;
            if (this_._internal_checkout() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_checkout());
            }
            // int32 status = 5;
            if (this_._internal_status() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_status());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void AdditionalInfo::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<AdditionalInfo*>(&to_msg);
  auto& from = static_cast<const AdditionalInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.AdditionalInfo)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_user().empty()) {
    _this->_internal_set_user(from._internal_user());
  }
  if (!from._internal_checkoutuser().empty()) {
    _this->_internal_set_checkoutuser(from._internal_checkoutuser());
  }
  if (from._internal_time() != 0) {
    _this->_impl_.time_ = from._impl_.time_;
  }
  if (from._internal_checkout() != 0) {
    _this->_impl_.checkout_ = from._impl_.checkout_;
  }
  if (from._internal_status() != 0) {
    _this->_impl_.status_ = from._impl_.status_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void AdditionalInfo::CopyFrom(const AdditionalInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.AdditionalInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void AdditionalInfo::InternalSwap(AdditionalInfo* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.user_, &other->_impl_.user_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.checkoutuser_, &other->_impl_.checkoutuser_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.status_)
      + sizeof(AdditionalInfo::_impl_.status_)
      - PROTOBUF_FIELD_OFFSET(AdditionalInfo, _impl_.time_)>(
          reinterpret_cast<char*>(&_impl_.time_),
          reinterpret_cast<char*>(&other->_impl_.time_));
}

::google::protobuf::Metadata AdditionalInfo::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class OffsetLength::_Internal {
 public:
};

OffsetLength::OffsetLength(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.OffsetLength)
}
OffsetLength::OffsetLength(
    ::google::protobuf::Arena* arena, const OffsetLength& from)
    : OffsetLength(arena) {
  MergeFrom(from);
}
inline PROTOBUF_NDEBUG_INLINE OffsetLength::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void OffsetLength::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, offset_),
           0,
           offsetof(Impl_, crc_) -
               offsetof(Impl_, offset_) +
               sizeof(Impl_::crc_));
}
OffsetLength::~OffsetLength() {
  // @@protoc_insertion_point(destructor:design.OffsetLength)
  SharedDtor(*this);
}
inline void OffsetLength::SharedDtor(MessageLite& self) {
  OffsetLength& this_ = static_cast<OffsetLength&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* OffsetLength::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) OffsetLength(arena);
}
constexpr auto OffsetLength::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(OffsetLength),
                                            alignof(OffsetLength));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull OffsetLength::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_OffsetLength_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &OffsetLength::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<OffsetLength>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &OffsetLength::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<OffsetLength>(), &OffsetLength::ByteSizeLong,
            &OffsetLength::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_._cached_size_),
        false,
    },
    &OffsetLength::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* OffsetLength::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 0, 0, 2> OffsetLength::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::OffsetLength>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // int64 offset = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(OffsetLength, _impl_.offset_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.offset_)}},
    // int32 length = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(OffsetLength, _impl_.length_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.length_)}},
    // uint32 crc = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(OffsetLength, _impl_.crc_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.crc_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 offset = 1;
    {PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.offset_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // int32 length = 2;
    {PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.length_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // uint32 crc = 3;
    {PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.crc_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt32)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void OffsetLength::Clear() {
// @@protoc_insertion_point(message_clear_start:design.OffsetLength)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.offset_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.crc_) -
      reinterpret_cast<char*>(&_impl_.offset_)) + sizeof(_impl_.crc_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* OffsetLength::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const OffsetLength& this_ = static_cast<const OffsetLength&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* OffsetLength::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const OffsetLength& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.OffsetLength)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int64 offset = 1;
          if (this_._internal_offset() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<1>(
                    stream, this_._internal_offset(), target);
          }

          // int32 length = 2;
          if (this_._internal_length() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<2>(
                    stream, this_._internal_length(), target);
          }

          // uint32 crc = 3;
          if (this_._internal_crc() != 0) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteUInt32ToArray(
                3, this_._internal_crc(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.OffsetLength)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t OffsetLength::ByteSizeLong(const MessageLite& base) {
          const OffsetLength& this_ = static_cast<const OffsetLength&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t OffsetLength::ByteSizeLong() const {
          const OffsetLength& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.OffsetLength)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // int64 offset = 1;
            if (this_._internal_offset() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_offset());
            }
            // int32 length = 2;
            if (this_._internal_length() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_length());
            }
            // uint32 crc = 3;
            if (this_._internal_crc() != 0) {
              total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(
                  this_._internal_crc());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void OffsetLength::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<OffsetLength*>(&to_msg);
  auto& from = static_cast<const OffsetLength&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.OffsetLength)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_offset() != 0) {
    _this->_impl_.offset_ = from._impl_.offset_;
  }
  if (from._internal_length() != 0) {
    _this->_impl_.length_ = from._impl_.length_;
  }
  if (from._internal_crc() != 0) {
    _this->_impl_.crc_ = from._impl_.crc_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void OffsetLength::CopyFrom(const OffsetLength& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.OffsetLength)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void OffsetLength::InternalSwap(OffsetLength* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.crc_)
      + sizeof(OffsetLength::_impl_.crc_)
      - PROTOBUF_FIELD_OFFSET(OffsetLength, _impl_.offset_)>(
          reinterpret_cast<char*>(&_impl_.offset_),
          reinterpret_cast<char*>(&other->_impl_.offset_));
}

::google::protobuf::Metadata OffsetLength::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class NodeOffsetRecord::_Internal {
 public:
  using HasBits =
      decltype(std::declval<NodeOffsetRecord>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_._has_bits_);
};

NodeOffsetRecord::NodeOffsetRecord(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.NodeOffsetRecord)
}
inline PROTOBUF_NDEBUG_INLINE NodeOffsetRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::NodeOffsetRecord& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0} {}

NodeOffsetRecord::NodeOffsetRecord(
    ::google::protobuf::Arena* arena,
    const NodeOffsetRecord& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  NodeOffsetRecord* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.off_len_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::OffsetLength>(
                              arena, *from._impl_.off_len_)
                        : nullptr;
  _impl_.node_id_ = from._impl_.node_id_;

  // @@protoc_insertion_point(copy_constructor:design.NodeOffsetRecord)
}
inline PROTOBUF_NDEBUG_INLINE NodeOffsetRecord::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void NodeOffsetRecord::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, off_len_),
           0,
           offsetof(Impl_, node_id_) -
               offsetof(Impl_, off_len_) +
               sizeof(Impl_::node_id_));
}
NodeOffsetRecord::~NodeOffsetRecord() {
  // @@protoc_insertion_point(destructor:design.NodeOffsetRecord)
  SharedDtor(*this);
}
inline void NodeOffsetRecord::SharedDtor(MessageLite& self) {
  NodeOffsetRecord& this_ = static_cast<NodeOffsetRecord&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.off_len_;
  this_._impl_.~Impl_();
}

inline void* NodeOffsetRecord::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) NodeOffsetRecord(arena);
}
constexpr auto NodeOffsetRecord::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(NodeOffsetRecord),
                                            alignof(NodeOffsetRecord));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull NodeOffsetRecord::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_NodeOffsetRecord_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &NodeOffsetRecord::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<NodeOffsetRecord>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &NodeOffsetRecord::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<NodeOffsetRecord>(), &NodeOffsetRecord::ByteSizeLong,
            &NodeOffsetRecord::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_._cached_size_),
        false,
    },
    &NodeOffsetRecord::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* NodeOffsetRecord::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 1, 0, 2> NodeOffsetRecord::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeOffsetRecord>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // .design.OffsetLength off_len = 2;
    {::_pbi::TcParser::FastMtS1,
     {18, 0, 0, PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_.off_len_)}},
    // int32 node_id = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(NodeOffsetRecord, _impl_.node_id_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_.node_id_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int32 node_id = 1;
    {PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_.node_id_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // .design.OffsetLength off_len = 2;
    {PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_.off_len_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::OffsetLength>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void NodeOffsetRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:design.NodeOffsetRecord)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.off_len_ != nullptr);
    _impl_.off_len_->Clear();
  }
  _impl_.node_id_ = 0;
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* NodeOffsetRecord::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const NodeOffsetRecord& this_ = static_cast<const NodeOffsetRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* NodeOffsetRecord::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const NodeOffsetRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.NodeOffsetRecord)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int32 node_id = 1;
          if (this_._internal_node_id() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<1>(
                    stream, this_._internal_node_id(), target);
          }

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.OffsetLength off_len = 2;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                2, *this_._impl_.off_len_, this_._impl_.off_len_->GetCachedSize(), target,
                stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.NodeOffsetRecord)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t NodeOffsetRecord::ByteSizeLong(const MessageLite& base) {
          const NodeOffsetRecord& this_ = static_cast<const NodeOffsetRecord&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t NodeOffsetRecord::ByteSizeLong() const {
          const NodeOffsetRecord& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.NodeOffsetRecord)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // .design.OffsetLength off_len = 2;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.off_len_);
            }
          }
           {
            // int32 node_id = 1;
            if (this_._internal_node_id() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_node_id());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void NodeOffsetRecord::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<NodeOffsetRecord*>(&to_msg);
  auto& from = static_cast<const NodeOffsetRecord&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.NodeOffsetRecord)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.off_len_ != nullptr);
    if (_this->_impl_.off_len_ == nullptr) {
      _this->_impl_.off_len_ =
          ::google::protobuf::Message::CopyConstruct<::design::OffsetLength>(arena, *from._impl_.off_len_);
    } else {
      _this->_impl_.off_len_->MergeFrom(*from._impl_.off_len_);
    }
  }
  if (from._internal_node_id() != 0) {
    _this->_impl_.node_id_ = from._impl_.node_id_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void NodeOffsetRecord::CopyFrom(const NodeOffsetRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.NodeOffsetRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void NodeOffsetRecord::InternalSwap(NodeOffsetRecord* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_.node_id_)
      + sizeof(NodeOffsetRecord::_impl_.node_id_)
      - PROTOBUF_FIELD_OFFSET(NodeOffsetRecord, _impl_.off_len_)>(
          reinterpret_cast<char*>(&_impl_.off_len_),
          reinterpret_cast<char*>(&other->_impl_.off_len_));
}

::google::protobuf::Metadata NodeOffsetRecord::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class ProjectInfo::_Internal {
 public:
};

ProjectInfo::ProjectInfo(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectInfo)
}
inline PROTOBUF_NDEBUG_INLINE ProjectInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectInfo& from_msg)
      : projectcode_(arena, from.projectcode_),
        classification_(arena, from.classification_),
        subtopic_(arena, from.subtopic_),
        user_(arena, from.user_),
        _cached_size_{0} {}

ProjectInfo::ProjectInfo(
    ::google::protobuf::Arena* arena,
    const ProjectInfo& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectInfo* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_.progresspercentage_ = from._impl_.progresspercentage_;

  // @@protoc_insertion_point(copy_constructor:design.ProjectInfo)
}
inline PROTOBUF_NDEBUG_INLINE ProjectInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : projectcode_(arena),
        classification_(arena),
        subtopic_(arena),
        user_(arena),
        _cached_size_{0} {}

inline void ProjectInfo::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.progresspercentage_ = {};
}
ProjectInfo::~ProjectInfo() {
  // @@protoc_insertion_point(destructor:design.ProjectInfo)
  SharedDtor(*this);
}
inline void ProjectInfo::SharedDtor(MessageLite& self) {
  ProjectInfo& this_ = static_cast<ProjectInfo&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.projectcode_.Destroy();
  this_._impl_.classification_.Destroy();
  this_._impl_.subtopic_.Destroy();
  this_._impl_.user_.Destroy();
  this_._impl_.~Impl_();
}

inline void* ProjectInfo::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectInfo(arena);
}
constexpr auto ProjectInfo::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(ProjectInfo),
                                            alignof(ProjectInfo));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectInfo::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectInfo_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectInfo::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectInfo>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectInfo::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectInfo>(), &ProjectInfo::ByteSizeLong,
            &ProjectInfo::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_._cached_size_),
        false,
    },
    &ProjectInfo::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectInfo::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 5, 0, 64, 2> ProjectInfo::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    5, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967264,  // skipmap
    offsetof(decltype(_table_), field_entries),
    5,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectInfo>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // string projectCode = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.projectcode_)}},
    // string classification = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.classification_)}},
    // string subTopic = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.subtopic_)}},
    // string user = 4;
    {::_pbi::TcParser::FastUS1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.user_)}},
    // int32 progressPercentage = 5;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ProjectInfo, _impl_.progresspercentage_), 63>(),
     {40, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.progresspercentage_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // string projectCode = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.projectcode_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string classification = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.classification_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string subTopic = 3;
    {PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.subtopic_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string user = 4;
    {PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.user_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // int32 progressPercentage = 5;
    {PROTOBUF_FIELD_OFFSET(ProjectInfo, _impl_.progresspercentage_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
    "\22\13\16\10\4\0\0\0"
    "design.ProjectInfo"
    "projectCode"
    "classification"
    "subTopic"
    "user"
  }},
};

PROTOBUF_NOINLINE void ProjectInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectInfo)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.projectcode_.ClearToEmpty();
  _impl_.classification_.ClearToEmpty();
  _impl_.subtopic_.ClearToEmpty();
  _impl_.user_.ClearToEmpty();
  _impl_.progresspercentage_ = 0;
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectInfo::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectInfo& this_ = static_cast<const ProjectInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectInfo::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectInfo)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // string projectCode = 1;
          if (!this_._internal_projectcode().empty()) {
            const std::string& _s = this_._internal_projectcode();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectInfo.projectCode");
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          // string classification = 2;
          if (!this_._internal_classification().empty()) {
            const std::string& _s = this_._internal_classification();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectInfo.classification");
            target = stream->WriteStringMaybeAliased(2, _s, target);
          }

          // string subTopic = 3;
          if (!this_._internal_subtopic().empty()) {
            const std::string& _s = this_._internal_subtopic();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectInfo.subTopic");
            target = stream->WriteStringMaybeAliased(3, _s, target);
          }

          // string user = 4;
          if (!this_._internal_user().empty()) {
            const std::string& _s = this_._internal_user();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectInfo.user");
            target = stream->WriteStringMaybeAliased(4, _s, target);
          }

          // int32 progressPercentage = 5;
          if (this_._internal_progresspercentage() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<5>(
                    stream, this_._internal_progresspercentage(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectInfo)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectInfo::ByteSizeLong(const MessageLite& base) {
          const ProjectInfo& this_ = static_cast<const ProjectInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectInfo::ByteSizeLong() const {
          const ProjectInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectInfo)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // string projectCode = 1;
            if (!this_._internal_projectcode().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_projectcode());
            }
            // string classification = 2;
            if (!this_._internal_classification().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_classification());
            }
            // string subTopic = 3;
            if (!this_._internal_subtopic().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_subtopic());
            }
            // string user = 4;
            if (!this_._internal_user().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_user());
            }
            // int32 progressPercentage = 5;
            if (this_._internal_progresspercentage() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_progresspercentage());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectInfo::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectInfo*>(&to_msg);
  auto& from = static_cast<const ProjectInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectInfo)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_projectcode().empty()) {
    _this->_internal_set_projectcode(from._internal_projectcode());
  }
  if (!from._internal_classification().empty()) {
    _this->_internal_set_classification(from._internal_classification());
  }
  if (!from._internal_subtopic().empty()) {
    _this->_internal_set_subtopic(from._internal_subtopic());
  }
  if (!from._internal_user().empty()) {
    _this->_internal_set_user(from._internal_user());
  }
  if (from._internal_progresspercentage() != 0) {
    _this->_impl_.progresspercentage_ = from._impl_.progresspercentage_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectInfo::CopyFrom(const ProjectInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectInfo::InternalSwap(ProjectInfo* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.projectcode_, &other->_impl_.projectcode_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.classification_, &other->_impl_.classification_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.subtopic_, &other->_impl_.subtopic_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.user_, &other->_impl_.user_, arena);
        swap(_impl_.progresspercentage_, other->_impl_.progresspercentage_);
}

::google::protobuf::Metadata ProjectInfo::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class ProjectConfigInfo::_Internal {
 public:
};

ProjectConfigInfo::ProjectConfigInfo(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectConfigInfo)
}
inline PROTOBUF_NDEBUG_INLINE ProjectConfigInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectConfigInfo& from_msg)
      : json_(arena, from.json_),
        _cached_size_{0} {}

ProjectConfigInfo::ProjectConfigInfo(
    ::google::protobuf::Arena* arena,
    const ProjectConfigInfo& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectConfigInfo* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.ProjectConfigInfo)
}
inline PROTOBUF_NDEBUG_INLINE ProjectConfigInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : json_(arena),
        _cached_size_{0} {}

inline void ProjectConfigInfo::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
ProjectConfigInfo::~ProjectConfigInfo() {
  // @@protoc_insertion_point(destructor:design.ProjectConfigInfo)
  SharedDtor(*this);
}
inline void ProjectConfigInfo::SharedDtor(MessageLite& self) {
  ProjectConfigInfo& this_ = static_cast<ProjectConfigInfo&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.json_.Destroy();
  this_._impl_.~Impl_();
}

inline void* ProjectConfigInfo::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectConfigInfo(arena);
}
constexpr auto ProjectConfigInfo::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(ProjectConfigInfo),
                                            alignof(ProjectConfigInfo));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectConfigInfo::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectConfigInfo_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectConfigInfo::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectConfigInfo>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectConfigInfo::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectConfigInfo>(), &ProjectConfigInfo::ByteSizeLong,
            &ProjectConfigInfo::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectConfigInfo, _impl_._cached_size_),
        false,
    },
    &ProjectConfigInfo::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectConfigInfo::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 1, 0, 37, 2> ProjectConfigInfo::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    1, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967294,  // skipmap
    offsetof(decltype(_table_), field_entries),
    1,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectConfigInfo>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // string json = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectConfigInfo, _impl_.json_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string json = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectConfigInfo, _impl_.json_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\30\4\0\0\0\0\0\0"
    "design.ProjectConfigInfo"
    "json"
  }},
};

PROTOBUF_NOINLINE void ProjectConfigInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectConfigInfo)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.json_.ClearToEmpty();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectConfigInfo::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectConfigInfo& this_ = static_cast<const ProjectConfigInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectConfigInfo::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectConfigInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectConfigInfo)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // string json = 1;
          if (!this_._internal_json().empty()) {
            const std::string& _s = this_._internal_json();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectConfigInfo.json");
            target = stream->WriteStringMaybeAliased(1, _s, target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectConfigInfo)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectConfigInfo::ByteSizeLong(const MessageLite& base) {
          const ProjectConfigInfo& this_ = static_cast<const ProjectConfigInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectConfigInfo::ByteSizeLong() const {
          const ProjectConfigInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectConfigInfo)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

           {
            // string json = 1;
            if (!this_._internal_json().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_json());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectConfigInfo::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectConfigInfo*>(&to_msg);
  auto& from = static_cast<const ProjectConfigInfo&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectConfigInfo)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_json().empty()) {
    _this->_internal_set_json(from._internal_json());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectConfigInfo::CopyFrom(const ProjectConfigInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectConfigInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectConfigInfo::InternalSwap(ProjectConfigInfo* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.json_, &other->_impl_.json_, arena);
}

::google::protobuf::Metadata ProjectConfigInfo::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class MessageQueuesPackage::_Internal {
 public:
  static constexpr ::int32_t kOneofCaseOffset =
      PROTOBUF_FIELD_OFFSET(::design::MessageQueuesPackage, _impl_._oneof_case_);
};

void MessageQueuesPackage::set_allocated_trees(::design::NodeTreeRecord* trees) {
  ::google::protobuf::Arena* message_arena = GetArena();
  clear_Record();
  if (trees) {
    ::google::protobuf::Arena* submessage_arena = trees->GetArena();
    if (message_arena != submessage_arena) {
      trees = ::google::protobuf::internal::GetOwnedMessage(message_arena, trees, submessage_arena);
    }
    set_has_trees();
    _impl_.Record_.trees_ = trees;
  }
  // @@protoc_insertion_point(field_set_allocated:design.MessageQueuesPackage.trees)
}
void MessageQueuesPackage::set_allocated_nodes(::design::NodeAttrsRecord* nodes) {
  ::google::protobuf::Arena* message_arena = GetArena();
  clear_Record();
  if (nodes) {
    ::google::protobuf::Arena* submessage_arena = nodes->GetArena();
    if (message_arena != submessage_arena) {
      nodes = ::google::protobuf::internal::GetOwnedMessage(message_arena, nodes, submessage_arena);
    }
    set_has_nodes();
    _impl_.Record_.nodes_ = nodes;
  }
  // @@protoc_insertion_point(field_set_allocated:design.MessageQueuesPackage.nodes)
}
MessageQueuesPackage::MessageQueuesPackage(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.MessageQueuesPackage)
}
inline PROTOBUF_NDEBUG_INLINE MessageQueuesPackage::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::MessageQueuesPackage& from_msg)
      : Record_{},
        _cached_size_{0},
        _oneof_case_{from._oneof_case_[0]} {}

MessageQueuesPackage::MessageQueuesPackage(
    ::google::protobuf::Arena* arena,
    const MessageQueuesPackage& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  MessageQueuesPackage* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  switch (Record_case()) {
    case RECORD_NOT_SET:
      break;
      case kTrees:
        _impl_.Record_.trees_ = ::google::protobuf::Message::CopyConstruct<::design::NodeTreeRecord>(arena, *from._impl_.Record_.trees_);
        break;
      case kNodes:
        _impl_.Record_.nodes_ = ::google::protobuf::Message::CopyConstruct<::design::NodeAttrsRecord>(arena, *from._impl_.Record_.nodes_);
        break;
  }

  // @@protoc_insertion_point(copy_constructor:design.MessageQueuesPackage)
}
inline PROTOBUF_NDEBUG_INLINE MessageQueuesPackage::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : Record_{},
        _cached_size_{0},
        _oneof_case_{} {}

inline void MessageQueuesPackage::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
MessageQueuesPackage::~MessageQueuesPackage() {
  // @@protoc_insertion_point(destructor:design.MessageQueuesPackage)
  SharedDtor(*this);
}
inline void MessageQueuesPackage::SharedDtor(MessageLite& self) {
  MessageQueuesPackage& this_ = static_cast<MessageQueuesPackage&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  if (this_.has_Record()) {
    this_.clear_Record();
  }
  this_._impl_.~Impl_();
}

void MessageQueuesPackage::clear_Record() {
// @@protoc_insertion_point(one_of_clear_start:design.MessageQueuesPackage)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  switch (Record_case()) {
    case kTrees: {
      if (GetArena() == nullptr) {
        delete _impl_.Record_.trees_;
      } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
        ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.Record_.trees_);
      }
      break;
    }
    case kNodes: {
      if (GetArena() == nullptr) {
        delete _impl_.Record_.nodes_;
      } else if (::google::protobuf::internal::DebugHardenClearOneofMessageOnArena()) {
        ::google::protobuf::internal::MaybePoisonAfterClear(_impl_.Record_.nodes_);
      }
      break;
    }
    case RECORD_NOT_SET: {
      break;
    }
  }
  _impl_._oneof_case_[0] = RECORD_NOT_SET;
}


inline void* MessageQueuesPackage::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) MessageQueuesPackage(arena);
}
constexpr auto MessageQueuesPackage::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(MessageQueuesPackage),
                                            alignof(MessageQueuesPackage));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull MessageQueuesPackage::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_MessageQueuesPackage_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &MessageQueuesPackage::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<MessageQueuesPackage>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &MessageQueuesPackage::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<MessageQueuesPackage>(), &MessageQueuesPackage::ByteSizeLong,
            &MessageQueuesPackage::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(MessageQueuesPackage, _impl_._cached_size_),
        false,
    },
    &MessageQueuesPackage::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* MessageQueuesPackage::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 2, 2, 0, 2> MessageQueuesPackage::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    2, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::MessageQueuesPackage>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.NodeTreeRecord trees = 1;
    {PROTOBUF_FIELD_OFFSET(MessageQueuesPackage, _impl_.Record_.trees_), _Internal::kOneofCaseOffset + 0, 0,
    (0 | ::_fl::kFcOneof | ::_fl::kMessage | ::_fl::kTvTable)},
    // .design.NodeAttrsRecord nodes = 2;
    {PROTOBUF_FIELD_OFFSET(MessageQueuesPackage, _impl_.Record_.nodes_), _Internal::kOneofCaseOffset + 0, 1,
    (0 | ::_fl::kFcOneof | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::NodeTreeRecord>()},
    {::_pbi::TcParser::GetTable<::design::NodeAttrsRecord>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void MessageQueuesPackage::Clear() {
// @@protoc_insertion_point(message_clear_start:design.MessageQueuesPackage)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_Record();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* MessageQueuesPackage::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const MessageQueuesPackage& this_ = static_cast<const MessageQueuesPackage&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* MessageQueuesPackage::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const MessageQueuesPackage& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.MessageQueuesPackage)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          switch (this_.Record_case()) {
            case kTrees: {
              target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                  1, *this_._impl_.Record_.trees_, this_._impl_.Record_.trees_->GetCachedSize(), target,
                  stream);
              break;
            }
            case kNodes: {
              target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                  2, *this_._impl_.Record_.nodes_, this_._impl_.Record_.nodes_->GetCachedSize(), target,
                  stream);
              break;
            }
            default:
              break;
          }
          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.MessageQueuesPackage)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t MessageQueuesPackage::ByteSizeLong(const MessageLite& base) {
          const MessageQueuesPackage& this_ = static_cast<const MessageQueuesPackage&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t MessageQueuesPackage::ByteSizeLong() const {
          const MessageQueuesPackage& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.MessageQueuesPackage)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          switch (this_.Record_case()) {
            // .design.NodeTreeRecord trees = 1;
            case kTrees: {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.Record_.trees_);
              break;
            }
            // .design.NodeAttrsRecord nodes = 2;
            case kNodes: {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.Record_.nodes_);
              break;
            }
            case RECORD_NOT_SET: {
              break;
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void MessageQueuesPackage::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<MessageQueuesPackage*>(&to_msg);
  auto& from = static_cast<const MessageQueuesPackage&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.MessageQueuesPackage)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (const uint32_t oneof_from_case = from._impl_._oneof_case_[0]) {
    const uint32_t oneof_to_case = _this->_impl_._oneof_case_[0];
    const bool oneof_needs_init = oneof_to_case != oneof_from_case;
    if (oneof_needs_init) {
      if (oneof_to_case != 0) {
        _this->clear_Record();
      }
      _this->_impl_._oneof_case_[0] = oneof_from_case;
    }

    switch (oneof_from_case) {
      case kTrees: {
        if (oneof_needs_init) {
          _this->_impl_.Record_.trees_ =
              ::google::protobuf::Message::CopyConstruct<::design::NodeTreeRecord>(arena, *from._impl_.Record_.trees_);
        } else {
          _this->_impl_.Record_.trees_->MergeFrom(from._internal_trees());
        }
        break;
      }
      case kNodes: {
        if (oneof_needs_init) {
          _this->_impl_.Record_.nodes_ =
              ::google::protobuf::Message::CopyConstruct<::design::NodeAttrsRecord>(arena, *from._impl_.Record_.nodes_);
        } else {
          _this->_impl_.Record_.nodes_->MergeFrom(from._internal_nodes());
        }
        break;
      }
      case RECORD_NOT_SET:
        break;
    }
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void MessageQueuesPackage::CopyFrom(const MessageQueuesPackage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.MessageQueuesPackage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void MessageQueuesPackage::InternalSwap(MessageQueuesPackage* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.Record_, other->_impl_.Record_);
  swap(_impl_._oneof_case_[0], other->_impl_._oneof_case_[0]);
}

::google::protobuf::Metadata MessageQueuesPackage::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

#if defined(PROTOBUF_CUSTOM_VTABLE)
              ProjectConfig_ConfigsEntry_DoNotUse::ProjectConfig_ConfigsEntry_DoNotUse() : SuperType(_class_data_.base()) {}
              ProjectConfig_ConfigsEntry_DoNotUse::ProjectConfig_ConfigsEntry_DoNotUse(::google::protobuf::Arena* arena)
                  : SuperType(arena, _class_data_.base()) {}
#else   // PROTOBUF_CUSTOM_VTABLE
              ProjectConfig_ConfigsEntry_DoNotUse::ProjectConfig_ConfigsEntry_DoNotUse() : SuperType() {}
              ProjectConfig_ConfigsEntry_DoNotUse::ProjectConfig_ConfigsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
#endif  // PROTOBUF_CUSTOM_VTABLE
              inline void* ProjectConfig_ConfigsEntry_DoNotUse::PlacementNew_(const void*, void* mem,
                                                      ::google::protobuf::Arena* arena) {
                return ::new (mem) ProjectConfig_ConfigsEntry_DoNotUse(arena);
              }
              constexpr auto ProjectConfig_ConfigsEntry_DoNotUse::InternalNewImpl_() {
                return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(ProjectConfig_ConfigsEntry_DoNotUse),
                                                          alignof(ProjectConfig_ConfigsEntry_DoNotUse));
              }
              PROTOBUF_CONSTINIT
              PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
              const ::google::protobuf::internal::ClassDataFull ProjectConfig_ConfigsEntry_DoNotUse::_class_data_ = {
                  ::google::protobuf::internal::ClassData{
                      &_ProjectConfig_ConfigsEntry_DoNotUse_default_instance_._instance,
                      &_table_.header,
                      nullptr,  // OnDemandRegisterArenaDtor
                      nullptr,  // IsInitialized
                      &ProjectConfig_ConfigsEntry_DoNotUse::MergeImpl,
                      ::google::protobuf::Message::GetNewImpl<ProjectConfig_ConfigsEntry_DoNotUse>(),
              #if defined(PROTOBUF_CUSTOM_VTABLE)
                      &ProjectConfig_ConfigsEntry_DoNotUse::SharedDtor,
                      static_cast<void (::google::protobuf::MessageLite::*)()>(
                          &ProjectConfig_ConfigsEntry_DoNotUse::ClearImpl),
                          ::google::protobuf::Message::ByteSizeLongImpl, ::google::protobuf::Message::_InternalSerializeImpl
                          ,
              #endif  // PROTOBUF_CUSTOM_VTABLE
                      PROTOBUF_FIELD_OFFSET(ProjectConfig_ConfigsEntry_DoNotUse, _impl_._cached_size_),
                      false,
                  },
                  &ProjectConfig_ConfigsEntry_DoNotUse::kDescriptorMethods,
                  &descriptor_table_node_2eproto,
                  nullptr,  // tracker
              };
              const ::google::protobuf::internal::ClassData* ProjectConfig_ConfigsEntry_DoNotUse::GetClassData() const {
                ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
                ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
                return _class_data_.base();
              }
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 50, 2> ProjectConfig_ConfigsEntry_DoNotUse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(ProjectConfig_ConfigsEntry_DoNotUse, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::DiscardEverythingFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectConfig_ConfigsEntry_DoNotUse>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // string value = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectConfig_ConfigsEntry_DoNotUse, _impl_.value_)}},
    // string key = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectConfig_ConfigsEntry_DoNotUse, _impl_.key_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string key = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectConfig_ConfigsEntry_DoNotUse, _impl_.key_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string value = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectConfig_ConfigsEntry_DoNotUse, _impl_.value_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\41\3\5\0\0\0\0\0"
    "design.ProjectConfig.ConfigsEntry"
    "key"
    "value"
  }},
};

// ===================================================================

class ProjectConfig::_Internal {
 public:
  using HasBits =
      decltype(std::declval<ProjectConfig>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_._has_bits_);
};

ProjectConfig::ProjectConfig(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectConfig)
}
inline PROTOBUF_NDEBUG_INLINE ProjectConfig::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectConfig& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        configs_{visibility, arena, from.configs_} {}

ProjectConfig::ProjectConfig(
    ::google::protobuf::Arena* arena,
    const ProjectConfig& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectConfig* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;

  // @@protoc_insertion_point(copy_constructor:design.ProjectConfig)
}
inline PROTOBUF_NDEBUG_INLINE ProjectConfig::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        configs_{visibility, arena} {}

inline void ProjectConfig::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.projectinfo_ = {};
}
ProjectConfig::~ProjectConfig() {
  // @@protoc_insertion_point(destructor:design.ProjectConfig)
  SharedDtor(*this);
}
inline void ProjectConfig::SharedDtor(MessageLite& self) {
  ProjectConfig& this_ = static_cast<ProjectConfig&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* ProjectConfig::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectConfig(arena);
}
constexpr auto ProjectConfig::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_.configs_) +
          decltype(ProjectConfig::_impl_.configs_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_.configs_) +
          decltype(ProjectConfig::_impl_.configs_)::
              InternalGetArenaOffsetAlt(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(ProjectConfig), alignof(ProjectConfig), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&ProjectConfig::PlacementNew_,
                                 sizeof(ProjectConfig),
                                 alignof(ProjectConfig));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectConfig::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectConfig_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectConfig::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectConfig>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectConfig::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectConfig>(), &ProjectConfig::ByteSizeLong,
            &ProjectConfig::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_._cached_size_),
        false,
    },
    &ProjectConfig::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectConfig::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 2, 2, 36, 2> ProjectConfig::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_._has_bits_),
    0, // no _extensions_
    2, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectConfig>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_.projectinfo_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // map<string, string> configs = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectConfig, _impl_.configs_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMap)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
    {::_pbi::TcParser::GetMapAuxInfo<
        decltype(ProjectConfig()._impl_.configs_)>(
        1, 0, 0, 9,
        9)},
  }}, {{
    "\24\0\7\0\0\0\0\0"
    "design.ProjectConfig"
    "configs"
  }},
};

PROTOBUF_NOINLINE void ProjectConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectConfig)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.configs_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectConfig::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectConfig& this_ = static_cast<const ProjectConfig&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectConfig::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectConfig& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectConfig)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // map<string, string> configs = 2;
          if (!this_._internal_configs().empty()) {
            using MapType = ::google::protobuf::Map<std::string, std::string>;
            using WireHelper = _pbi::MapEntryFuncs<std::string, std::string,
                                           _pbi::WireFormatLite::TYPE_STRING,
                                           _pbi::WireFormatLite::TYPE_STRING>;
            const auto& field = this_._internal_configs();

            if (stream->IsSerializationDeterministic() && field.size() > 1) {
              for (const auto& entry : ::google::protobuf::internal::MapSorterPtr<MapType>(field)) {
                target = WireHelper::InternalSerialize(
                    2, entry.first, entry.second, target, stream);
                ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                    entry.first.data(), static_cast<int>(entry.first.length()),
 ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectConfig.configs");
                ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                    entry.second.data(), static_cast<int>(entry.second.length()),
 ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectConfig.configs");
              }
            } else {
              for (const auto& entry : field) {
                target = WireHelper::InternalSerialize(
                    2, entry.first, entry.second, target, stream);
                ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                    entry.first.data(), static_cast<int>(entry.first.length()),
 ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectConfig.configs");
                ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                    entry.second.data(), static_cast<int>(entry.second.length()),
 ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectConfig.configs");
              }
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectConfig)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectConfig::ByteSizeLong(const MessageLite& base) {
          const ProjectConfig& this_ = static_cast<const ProjectConfig&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectConfig::ByteSizeLong() const {
          const ProjectConfig& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectConfig)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // map<string, string> configs = 2;
            {
              total_size +=
                  1 * ::google::protobuf::internal::FromIntSize(this_._internal_configs_size());
              for (const auto& entry : this_._internal_configs()) {
                total_size += _pbi::MapEntryFuncs<std::string, std::string,
                                               _pbi::WireFormatLite::TYPE_STRING,
                                               _pbi::WireFormatLite::TYPE_STRING>::ByteSizeLong(entry.first, entry.second);
              }
            }
          }
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectConfig::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectConfig*>(&to_msg);
  auto& from = static_cast<const ProjectConfig&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectConfig)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.configs_.MergeFrom(from._impl_.configs_);
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectConfig::CopyFrom(const ProjectConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectConfig::InternalSwap(ProjectConfig* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.configs_.InternalSwap(&other->_impl_.configs_);
  swap(_impl_.projectinfo_, other->_impl_.projectinfo_);
}

::google::protobuf::Metadata ProjectConfig::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class KeyCode::_Internal {
 public:
};

KeyCode::KeyCode(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.KeyCode)
}
inline PROTOBUF_NDEBUG_INLINE KeyCode::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::KeyCode& from_msg)
      : key_(arena, from.key_),
        _cached_size_{0} {}

KeyCode::KeyCode(
    ::google::protobuf::Arena* arena,
    const KeyCode& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  KeyCode* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_.code_ = from._impl_.code_;

  // @@protoc_insertion_point(copy_constructor:design.KeyCode)
}
inline PROTOBUF_NDEBUG_INLINE KeyCode::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : key_(arena),
        _cached_size_{0} {}

inline void KeyCode::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.code_ = {};
}
KeyCode::~KeyCode() {
  // @@protoc_insertion_point(destructor:design.KeyCode)
  SharedDtor(*this);
}
inline void KeyCode::SharedDtor(MessageLite& self) {
  KeyCode& this_ = static_cast<KeyCode&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.key_.Destroy();
  this_._impl_.~Impl_();
}

inline void* KeyCode::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) KeyCode(arena);
}
constexpr auto KeyCode::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(KeyCode),
                                            alignof(KeyCode));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull KeyCode::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_KeyCode_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &KeyCode::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<KeyCode>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &KeyCode::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<KeyCode>(), &KeyCode::ByteSizeLong,
            &KeyCode::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(KeyCode, _impl_._cached_size_),
        false,
    },
    &KeyCode::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* KeyCode::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 0, 2> KeyCode::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::KeyCode>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // int64 code = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(KeyCode, _impl_.code_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(KeyCode, _impl_.code_)}},
    // bytes key = 1;
    {::_pbi::TcParser::FastBS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(KeyCode, _impl_.key_)}},
  }}, {{
    65535, 65535
  }}, {{
    // bytes key = 1;
    {PROTOBUF_FIELD_OFFSET(KeyCode, _impl_.key_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBytes | ::_fl::kRepAString)},
    // int64 code = 2;
    {PROTOBUF_FIELD_OFFSET(KeyCode, _impl_.code_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void KeyCode::Clear() {
// @@protoc_insertion_point(message_clear_start:design.KeyCode)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.key_.ClearToEmpty();
  _impl_.code_ = ::int64_t{0};
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* KeyCode::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const KeyCode& this_ = static_cast<const KeyCode&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* KeyCode::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const KeyCode& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.KeyCode)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // bytes key = 1;
          if (!this_._internal_key().empty()) {
            const std::string& _s = this_._internal_key();
            target = stream->WriteBytesMaybeAliased(1, _s, target);
          }

          // int64 code = 2;
          if (this_._internal_code() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<2>(
                    stream, this_._internal_code(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.KeyCode)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t KeyCode::ByteSizeLong(const MessageLite& base) {
          const KeyCode& this_ = static_cast<const KeyCode&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t KeyCode::ByteSizeLong() const {
          const KeyCode& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.KeyCode)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // bytes key = 1;
            if (!this_._internal_key().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_key());
            }
            // int64 code = 2;
            if (this_._internal_code() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_code());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void KeyCode::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<KeyCode*>(&to_msg);
  auto& from = static_cast<const KeyCode&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.KeyCode)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _this->_internal_set_key(from._internal_key());
  }
  if (from._internal_code() != 0) {
    _this->_impl_.code_ = from._impl_.code_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void KeyCode::CopyFrom(const KeyCode& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.KeyCode)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void KeyCode::InternalSwap(KeyCode* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.key_, &other->_impl_.key_, arena);
        swap(_impl_.code_, other->_impl_.code_);
}

::google::protobuf::Metadata KeyCode::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class ProjectCodes::_Internal {
 public:
  using HasBits =
      decltype(std::declval<ProjectCodes>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_._has_bits_);
};

ProjectCodes::ProjectCodes(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectCodes)
}
inline PROTOBUF_NDEBUG_INLINE ProjectCodes::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectCodes& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        key_code_{visibility, arena, from.key_code_} {}

ProjectCodes::ProjectCodes(
    ::google::protobuf::Arena* arena,
    const ProjectCodes& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectCodes* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;

  // @@protoc_insertion_point(copy_constructor:design.ProjectCodes)
}
inline PROTOBUF_NDEBUG_INLINE ProjectCodes::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        key_code_{visibility, arena} {}

inline void ProjectCodes::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.projectinfo_ = {};
}
ProjectCodes::~ProjectCodes() {
  // @@protoc_insertion_point(destructor:design.ProjectCodes)
  SharedDtor(*this);
}
inline void ProjectCodes::SharedDtor(MessageLite& self) {
  ProjectCodes& this_ = static_cast<ProjectCodes&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* ProjectCodes::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectCodes(arena);
}
constexpr auto ProjectCodes::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_.key_code_) +
          decltype(ProjectCodes::_impl_.key_code_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(ProjectCodes), alignof(ProjectCodes), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&ProjectCodes::PlacementNew_,
                                 sizeof(ProjectCodes),
                                 alignof(ProjectCodes));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectCodes::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectCodes_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectCodes::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectCodes>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectCodes::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectCodes>(), &ProjectCodes::ByteSizeLong,
            &ProjectCodes::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_._cached_size_),
        false,
    },
    &ProjectCodes::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectCodes::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 2, 2, 0, 2> ProjectCodes::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_._has_bits_),
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967290,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectCodes>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_.projectinfo_)}},
    {::_pbi::TcParser::MiniParse, {}},
    // repeated .design.KeyCode key_code = 3;
    {::_pbi::TcParser::FastMtR1,
     {26, 63, 1, PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_.key_code_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .design.KeyCode key_code = 3;
    {PROTOBUF_FIELD_OFFSET(ProjectCodes, _impl_.key_code_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
    {::_pbi::TcParser::GetTable<::design::KeyCode>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void ProjectCodes::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectCodes)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.key_code_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectCodes::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectCodes& this_ = static_cast<const ProjectCodes&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectCodes::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectCodes& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectCodes)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // repeated .design.KeyCode key_code = 3;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_key_code_size());
               i < n; i++) {
            const auto& repfield = this_._internal_key_code().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    3, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectCodes)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectCodes::ByteSizeLong(const MessageLite& base) {
          const ProjectCodes& this_ = static_cast<const ProjectCodes&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectCodes::ByteSizeLong() const {
          const ProjectCodes& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectCodes)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .design.KeyCode key_code = 3;
            {
              total_size += 1UL * this_._internal_key_code_size();
              for (const auto& msg : this_._internal_key_code()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectCodes::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectCodes*>(&to_msg);
  auto& from = static_cast<const ProjectCodes&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectCodes)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_key_code()->MergeFrom(
      from._internal_key_code());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectCodes::CopyFrom(const ProjectCodes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectCodes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectCodes::InternalSwap(ProjectCodes* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.key_code_.InternalSwap(&other->_impl_.key_code_);
  swap(_impl_.projectinfo_, other->_impl_.projectinfo_);
}

::google::protobuf::Metadata ProjectCodes::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class ProjectNodes::_Internal {
 public:
  using HasBits =
      decltype(std::declval<ProjectNodes>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_._has_bits_);
};

ProjectNodes::ProjectNodes(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectNodes)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodes::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectNodes& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        nodes_{visibility, arena, from.nodes_} {}

ProjectNodes::ProjectNodes(
    ::google::protobuf::Arena* arena,
    const ProjectNodes& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectNodes* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;
  _impl_.action_ = from._impl_.action_;

  // @@protoc_insertion_point(copy_constructor:design.ProjectNodes)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodes::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        nodes_{visibility, arena} {}

inline void ProjectNodes::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, projectinfo_),
           0,
           offsetof(Impl_, action_) -
               offsetof(Impl_, projectinfo_) +
               sizeof(Impl_::action_));
}
ProjectNodes::~ProjectNodes() {
  // @@protoc_insertion_point(destructor:design.ProjectNodes)
  SharedDtor(*this);
}
inline void ProjectNodes::SharedDtor(MessageLite& self) {
  ProjectNodes& this_ = static_cast<ProjectNodes&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* ProjectNodes::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectNodes(arena);
}
constexpr auto ProjectNodes::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.nodes_) +
          decltype(ProjectNodes::_impl_.nodes_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(ProjectNodes), alignof(ProjectNodes), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&ProjectNodes::PlacementNew_,
                                 sizeof(ProjectNodes),
                                 alignof(ProjectNodes));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectNodes::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectNodes_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectNodes::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectNodes>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectNodes::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectNodes>(), &ProjectNodes::ByteSizeLong,
            &ProjectNodes::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_._cached_size_),
        false,
    },
    &ProjectNodes::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectNodes::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 2, 0, 2> ProjectNodes::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_._has_bits_),
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectNodes>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.projectinfo_)}},
    // repeated .design.NodeAttrsRecord nodes = 2;
    {::_pbi::TcParser::FastMtR1,
     {18, 63, 1, PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.nodes_)}},
    // int32 action = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(ProjectNodes, _impl_.action_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.action_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .design.NodeAttrsRecord nodes = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.nodes_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
    // int32 action = 3;
    {PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.action_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
    {::_pbi::TcParser::GetTable<::design::NodeAttrsRecord>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void ProjectNodes::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectNodes)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.nodes_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  _impl_.action_ = 0;
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectNodes::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectNodes& this_ = static_cast<const ProjectNodes&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectNodes::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectNodes& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectNodes)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // repeated .design.NodeAttrsRecord nodes = 2;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_nodes_size());
               i < n; i++) {
            const auto& repfield = this_._internal_nodes().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    2, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          // int32 action = 3;
          if (this_._internal_action() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<3>(
                    stream, this_._internal_action(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectNodes)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectNodes::ByteSizeLong(const MessageLite& base) {
          const ProjectNodes& this_ = static_cast<const ProjectNodes&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectNodes::ByteSizeLong() const {
          const ProjectNodes& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectNodes)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .design.NodeAttrsRecord nodes = 2;
            {
              total_size += 1UL * this_._internal_nodes_size();
              for (const auto& msg : this_._internal_nodes()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
           {
            // int32 action = 3;
            if (this_._internal_action() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_action());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectNodes::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectNodes*>(&to_msg);
  auto& from = static_cast<const ProjectNodes&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectNodes)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_nodes()->MergeFrom(
      from._internal_nodes());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  if (from._internal_action() != 0) {
    _this->_impl_.action_ = from._impl_.action_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectNodes::CopyFrom(const ProjectNodes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectNodes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectNodes::InternalSwap(ProjectNodes* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.nodes_.InternalSwap(&other->_impl_.nodes_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.action_)
      + sizeof(ProjectNodes::_impl_.action_)
      - PROTOBUF_FIELD_OFFSET(ProjectNodes, _impl_.projectinfo_)>(
          reinterpret_cast<char*>(&_impl_.projectinfo_),
          reinterpret_cast<char*>(&other->_impl_.projectinfo_));
}

::google::protobuf::Metadata ProjectNodes::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class ProjectNodesResult::_Internal {
 public:
};

ProjectNodesResult::ProjectNodesResult(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectNodesResult)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodesResult::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectNodesResult& from_msg)
      : code_(arena, from.code_),
        message_(arena, from.message_),
        _cached_size_{0} {}

ProjectNodesResult::ProjectNodesResult(
    ::google::protobuf::Arena* arena,
    const ProjectNodesResult& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectNodesResult* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);

  // @@protoc_insertion_point(copy_constructor:design.ProjectNodesResult)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodesResult::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : code_(arena),
        message_(arena),
        _cached_size_{0} {}

inline void ProjectNodesResult::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
}
ProjectNodesResult::~ProjectNodesResult() {
  // @@protoc_insertion_point(destructor:design.ProjectNodesResult)
  SharedDtor(*this);
}
inline void ProjectNodesResult::SharedDtor(MessageLite& self) {
  ProjectNodesResult& this_ = static_cast<ProjectNodesResult&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.code_.Destroy();
  this_._impl_.message_.Destroy();
  this_._impl_.~Impl_();
}

inline void* ProjectNodesResult::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectNodesResult(arena);
}
constexpr auto ProjectNodesResult::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(ProjectNodesResult),
                                            alignof(ProjectNodesResult));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectNodesResult::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectNodesResult_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectNodesResult::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectNodesResult>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectNodesResult::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectNodesResult>(), &ProjectNodesResult::ByteSizeLong,
            &ProjectNodesResult::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectNodesResult, _impl_._cached_size_),
        false,
    },
    &ProjectNodesResult::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectNodesResult::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 41, 2> ProjectNodesResult::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectNodesResult>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // string message = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectNodesResult, _impl_.message_)}},
    // bytes code = 1;
    {::_pbi::TcParser::FastBS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectNodesResult, _impl_.code_)}},
  }}, {{
    65535, 65535
  }}, {{
    // bytes code = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesResult, _impl_.code_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBytes | ::_fl::kRepAString)},
    // string message = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesResult, _impl_.message_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\31\0\7\0\0\0\0\0"
    "design.ProjectNodesResult"
    "message"
  }},
};

PROTOBUF_NOINLINE void ProjectNodesResult::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectNodesResult)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.code_.ClearToEmpty();
  _impl_.message_.ClearToEmpty();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectNodesResult::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectNodesResult& this_ = static_cast<const ProjectNodesResult&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectNodesResult::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectNodesResult& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectNodesResult)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // bytes code = 1;
          if (!this_._internal_code().empty()) {
            const std::string& _s = this_._internal_code();
            target = stream->WriteBytesMaybeAliased(1, _s, target);
          }

          // string message = 2;
          if (!this_._internal_message().empty()) {
            const std::string& _s = this_._internal_message();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.ProjectNodesResult.message");
            target = stream->WriteStringMaybeAliased(2, _s, target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectNodesResult)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectNodesResult::ByteSizeLong(const MessageLite& base) {
          const ProjectNodesResult& this_ = static_cast<const ProjectNodesResult&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectNodesResult::ByteSizeLong() const {
          const ProjectNodesResult& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectNodesResult)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // bytes code = 1;
            if (!this_._internal_code().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::BytesSize(
                                              this_._internal_code());
            }
            // string message = 2;
            if (!this_._internal_message().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_message());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectNodesResult::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectNodesResult*>(&to_msg);
  auto& from = static_cast<const ProjectNodesResult&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectNodesResult)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_code().empty()) {
    _this->_internal_set_code(from._internal_code());
  }
  if (!from._internal_message().empty()) {
    _this->_internal_set_message(from._internal_message());
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectNodesResult::CopyFrom(const ProjectNodesResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectNodesResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectNodesResult::InternalSwap(ProjectNodesResult* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.code_, &other->_impl_.code_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.message_, &other->_impl_.message_, arena);
}

::google::protobuf::Metadata ProjectNodesResult::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

#if defined(PROTOBUF_CUSTOM_VTABLE)
              ProjectNodesOffset_OffsetsEntry_DoNotUse::ProjectNodesOffset_OffsetsEntry_DoNotUse() : SuperType(_class_data_.base()) {}
              ProjectNodesOffset_OffsetsEntry_DoNotUse::ProjectNodesOffset_OffsetsEntry_DoNotUse(::google::protobuf::Arena* arena)
                  : SuperType(arena, _class_data_.base()) {}
#else   // PROTOBUF_CUSTOM_VTABLE
              ProjectNodesOffset_OffsetsEntry_DoNotUse::ProjectNodesOffset_OffsetsEntry_DoNotUse() : SuperType() {}
              ProjectNodesOffset_OffsetsEntry_DoNotUse::ProjectNodesOffset_OffsetsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
#endif  // PROTOBUF_CUSTOM_VTABLE
              inline void* ProjectNodesOffset_OffsetsEntry_DoNotUse::PlacementNew_(const void*, void* mem,
                                                      ::google::protobuf::Arena* arena) {
                return ::new (mem) ProjectNodesOffset_OffsetsEntry_DoNotUse(arena);
              }
              constexpr auto ProjectNodesOffset_OffsetsEntry_DoNotUse::InternalNewImpl_() {
                return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(ProjectNodesOffset_OffsetsEntry_DoNotUse),
                                                          alignof(ProjectNodesOffset_OffsetsEntry_DoNotUse));
              }
              PROTOBUF_CONSTINIT
              PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
              const ::google::protobuf::internal::ClassDataFull ProjectNodesOffset_OffsetsEntry_DoNotUse::_class_data_ = {
                  ::google::protobuf::internal::ClassData{
                      &_ProjectNodesOffset_OffsetsEntry_DoNotUse_default_instance_._instance,
                      &_table_.header,
                      nullptr,  // OnDemandRegisterArenaDtor
                      nullptr,  // IsInitialized
                      &ProjectNodesOffset_OffsetsEntry_DoNotUse::MergeImpl,
                      ::google::protobuf::Message::GetNewImpl<ProjectNodesOffset_OffsetsEntry_DoNotUse>(),
              #if defined(PROTOBUF_CUSTOM_VTABLE)
                      &ProjectNodesOffset_OffsetsEntry_DoNotUse::SharedDtor,
                      static_cast<void (::google::protobuf::MessageLite::*)()>(
                          &ProjectNodesOffset_OffsetsEntry_DoNotUse::ClearImpl),
                          ::google::protobuf::Message::ByteSizeLongImpl, ::google::protobuf::Message::_InternalSerializeImpl
                          ,
              #endif  // PROTOBUF_CUSTOM_VTABLE
                      PROTOBUF_FIELD_OFFSET(ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_._cached_size_),
                      false,
                  },
                  &ProjectNodesOffset_OffsetsEntry_DoNotUse::kDescriptorMethods,
                  &descriptor_table_node_2eproto,
                  nullptr,  // tracker
              };
              const ::google::protobuf::internal::ClassData* ProjectNodesOffset_OffsetsEntry_DoNotUse::GetClassData() const {
                ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
                ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
                return _class_data_.base();
              }
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 1, 0, 2> ProjectNodesOffset_OffsetsEntry_DoNotUse::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::DiscardEverythingFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectNodesOffset_OffsetsEntry_DoNotUse>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // .design.OffsetLength value = 2;
    {::_pbi::TcParser::FastMtS1,
     {18, 0, 0, PROTOBUF_FIELD_OFFSET(ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_.value_)}},
    // uint64 key = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_.key_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_.key_)}},
  }}, {{
    65535, 65535
  }}, {{
    // uint64 key = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_.key_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUInt64)},
    // .design.OffsetLength value = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesOffset_OffsetsEntry_DoNotUse, _impl_.value_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::OffsetLength>()},
  }}, {{
  }},
};

// ===================================================================

class ProjectNodesOffset::_Internal {
 public:
  using HasBits =
      decltype(std::declval<ProjectNodesOffset>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_._has_bits_);
};

ProjectNodesOffset::ProjectNodesOffset(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectNodesOffset)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodesOffset::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectNodesOffset& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        offsets_{visibility, arena, from.offsets_} {}

ProjectNodesOffset::ProjectNodesOffset(
    ::google::protobuf::Arena* arena,
    const ProjectNodesOffset& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectNodesOffset* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;

  // @@protoc_insertion_point(copy_constructor:design.ProjectNodesOffset)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodesOffset::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        offsets_{visibility, arena} {}

inline void ProjectNodesOffset::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.projectinfo_ = {};
}
ProjectNodesOffset::~ProjectNodesOffset() {
  // @@protoc_insertion_point(destructor:design.ProjectNodesOffset)
  SharedDtor(*this);
}
inline void ProjectNodesOffset::SharedDtor(MessageLite& self) {
  ProjectNodesOffset& this_ = static_cast<ProjectNodesOffset&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* ProjectNodesOffset::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectNodesOffset(arena);
}
constexpr auto ProjectNodesOffset::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_.offsets_) +
          decltype(ProjectNodesOffset::_impl_.offsets_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
      PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_.offsets_) +
          decltype(ProjectNodesOffset::_impl_.offsets_)::
              InternalGetArenaOffsetAlt(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::CopyInit(
        sizeof(ProjectNodesOffset), alignof(ProjectNodesOffset), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&ProjectNodesOffset::PlacementNew_,
                                 sizeof(ProjectNodesOffset),
                                 alignof(ProjectNodesOffset));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectNodesOffset::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectNodesOffset_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectNodesOffset::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectNodesOffset>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectNodesOffset::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectNodesOffset>(), &ProjectNodesOffset::ByteSizeLong,
            &ProjectNodesOffset::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_._cached_size_),
        false,
    },
    &ProjectNodesOffset::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectNodesOffset::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<0, 2, 3, 0, 2> ProjectNodesOffset::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_._has_bits_),
    0, // no _extensions_
    2, 0,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    3,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectNodesOffset>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_.projectinfo_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // map<uint64, .design.OffsetLength> offsets = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesOffset, _impl_.offsets_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMap)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
    {::_pbi::TcParser::GetMapAuxInfo<
        decltype(ProjectNodesOffset()._impl_.offsets_)>(
        0, 0, 0, 4,
        11)},
    {::_pbi::TcParser::GetTable<::design::OffsetLength>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void ProjectNodesOffset::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectNodesOffset)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.offsets_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectNodesOffset::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectNodesOffset& this_ = static_cast<const ProjectNodesOffset&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectNodesOffset::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectNodesOffset& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectNodesOffset)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // map<uint64, .design.OffsetLength> offsets = 2;
          if (!this_._internal_offsets().empty()) {
            using MapType = ::google::protobuf::Map<::uint64_t, ::design::OffsetLength>;
            using WireHelper = _pbi::MapEntryFuncs<::uint64_t, ::design::OffsetLength,
                                           _pbi::WireFormatLite::TYPE_UINT64,
                                           _pbi::WireFormatLite::TYPE_MESSAGE>;
            const auto& field = this_._internal_offsets();

            if (stream->IsSerializationDeterministic() && field.size() > 1) {
              for (const auto& entry : ::google::protobuf::internal::MapSorterFlat<MapType>(field)) {
                target = WireHelper::InternalSerialize(
                    2, entry.first, entry.second, target, stream);
              }
            } else {
              for (const auto& entry : field) {
                target = WireHelper::InternalSerialize(
                    2, entry.first, entry.second, target, stream);
              }
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectNodesOffset)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectNodesOffset::ByteSizeLong(const MessageLite& base) {
          const ProjectNodesOffset& this_ = static_cast<const ProjectNodesOffset&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectNodesOffset::ByteSizeLong() const {
          const ProjectNodesOffset& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectNodesOffset)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // map<uint64, .design.OffsetLength> offsets = 2;
            {
              total_size +=
                  1 * ::google::protobuf::internal::FromIntSize(this_._internal_offsets_size());
              for (const auto& entry : this_._internal_offsets()) {
                total_size += _pbi::MapEntryFuncs<::uint64_t, ::design::OffsetLength,
                                               _pbi::WireFormatLite::TYPE_UINT64,
                                               _pbi::WireFormatLite::TYPE_MESSAGE>::ByteSizeLong(entry.first, entry.second);
              }
            }
          }
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectNodesOffset::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectNodesOffset*>(&to_msg);
  auto& from = static_cast<const ProjectNodesOffset&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectNodesOffset)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.offsets_.MergeFrom(from._impl_.offsets_);
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectNodesOffset::CopyFrom(const ProjectNodesOffset& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectNodesOffset)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectNodesOffset::InternalSwap(ProjectNodesOffset* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.offsets_.InternalSwap(&other->_impl_.offsets_);
  swap(_impl_.projectinfo_, other->_impl_.projectinfo_);
}

::google::protobuf::Metadata ProjectNodesOffset::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class MaxOffset::_Internal {
 public:
  using HasBits =
      decltype(std::declval<MaxOffset>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_._has_bits_);
};

MaxOffset::MaxOffset(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.MaxOffset)
}
inline PROTOBUF_NDEBUG_INLINE MaxOffset::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::MaxOffset& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0} {}

MaxOffset::MaxOffset(
    ::google::protobuf::Arena* arena,
    const MaxOffset& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  MaxOffset* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, maxoffset_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, maxoffset_),
           offsetof(Impl_, flag_) -
               offsetof(Impl_, maxoffset_) +
               sizeof(Impl_::flag_));

  // @@protoc_insertion_point(copy_constructor:design.MaxOffset)
}
inline PROTOBUF_NDEBUG_INLINE MaxOffset::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void MaxOffset::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, projectinfo_),
           0,
           offsetof(Impl_, flag_) -
               offsetof(Impl_, projectinfo_) +
               sizeof(Impl_::flag_));
}
MaxOffset::~MaxOffset() {
  // @@protoc_insertion_point(destructor:design.MaxOffset)
  SharedDtor(*this);
}
inline void MaxOffset::SharedDtor(MessageLite& self) {
  MaxOffset& this_ = static_cast<MaxOffset&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* MaxOffset::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) MaxOffset(arena);
}
constexpr auto MaxOffset::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(MaxOffset),
                                            alignof(MaxOffset));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull MaxOffset::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_MaxOffset_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &MaxOffset::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<MaxOffset>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &MaxOffset::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<MaxOffset>(), &MaxOffset::ByteSizeLong,
            &MaxOffset::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_._cached_size_),
        false,
    },
    &MaxOffset::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* MaxOffset::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 1, 0, 2> MaxOffset::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_._has_bits_),
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::MaxOffset>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.projectinfo_)}},
    // int32 flag = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(MaxOffset, _impl_.flag_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.flag_)}},
    // int64 maxOffset = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(MaxOffset, _impl_.maxoffset_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.maxoffset_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // int32 flag = 2;
    {PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.flag_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // int64 maxOffset = 3;
    {PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.maxoffset_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void MaxOffset::Clear() {
// @@protoc_insertion_point(message_clear_start:design.MaxOffset)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  ::memset(&_impl_.maxoffset_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.flag_) -
      reinterpret_cast<char*>(&_impl_.maxoffset_)) + sizeof(_impl_.flag_));
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* MaxOffset::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const MaxOffset& this_ = static_cast<const MaxOffset&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* MaxOffset::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const MaxOffset& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.MaxOffset)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // int32 flag = 2;
          if (this_._internal_flag() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<2>(
                    stream, this_._internal_flag(), target);
          }

          // int64 maxOffset = 3;
          if (this_._internal_maxoffset() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<3>(
                    stream, this_._internal_maxoffset(), target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.MaxOffset)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t MaxOffset::ByteSizeLong(const MessageLite& base) {
          const MaxOffset& this_ = static_cast<const MaxOffset&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t MaxOffset::ByteSizeLong() const {
          const MaxOffset& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.MaxOffset)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
           {
            // int64 maxOffset = 3;
            if (this_._internal_maxoffset() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_maxoffset());
            }
            // int32 flag = 2;
            if (this_._internal_flag() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_flag());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void MaxOffset::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<MaxOffset*>(&to_msg);
  auto& from = static_cast<const MaxOffset&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.MaxOffset)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  if (from._internal_maxoffset() != 0) {
    _this->_impl_.maxoffset_ = from._impl_.maxoffset_;
  }
  if (from._internal_flag() != 0) {
    _this->_impl_.flag_ = from._impl_.flag_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void MaxOffset::CopyFrom(const MaxOffset& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.MaxOffset)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void MaxOffset::InternalSwap(MaxOffset* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.flag_)
      + sizeof(MaxOffset::_impl_.flag_)
      - PROTOBUF_FIELD_OFFSET(MaxOffset, _impl_.projectinfo_)>(
          reinterpret_cast<char*>(&_impl_.projectinfo_),
          reinterpret_cast<char*>(&other->_impl_.projectinfo_));
}

::google::protobuf::Metadata MaxOffset::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class NodeTreeAction::_Internal {
 public:
};

NodeTreeAction::NodeTreeAction(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.NodeTreeAction)
}
inline PROTOBUF_NDEBUG_INLINE NodeTreeAction::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::NodeTreeAction& from_msg)
      : siblings_{visibility, arena, from.siblings_},
        _siblings_cached_byte_size_{0},
        _cached_size_{0} {}

NodeTreeAction::NodeTreeAction(
    ::google::protobuf::Arena* arena,
    const NodeTreeAction& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  NodeTreeAction* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, parentid_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, parentid_),
           offsetof(Impl_, flag_) -
               offsetof(Impl_, parentid_) +
               sizeof(Impl_::flag_));

  // @@protoc_insertion_point(copy_constructor:design.NodeTreeAction)
}
inline PROTOBUF_NDEBUG_INLINE NodeTreeAction::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : siblings_{visibility, arena},
        _siblings_cached_byte_size_{0},
        _cached_size_{0} {}

inline void NodeTreeAction::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, parentid_),
           0,
           offsetof(Impl_, flag_) -
               offsetof(Impl_, parentid_) +
               sizeof(Impl_::flag_));
}
NodeTreeAction::~NodeTreeAction() {
  // @@protoc_insertion_point(destructor:design.NodeTreeAction)
  SharedDtor(*this);
}
inline void NodeTreeAction::SharedDtor(MessageLite& self) {
  NodeTreeAction& this_ = static_cast<NodeTreeAction&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.~Impl_();
}

inline void* NodeTreeAction::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) NodeTreeAction(arena);
}
constexpr auto NodeTreeAction::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.siblings_) +
          decltype(NodeTreeAction::_impl_.siblings_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(NodeTreeAction), alignof(NodeTreeAction), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&NodeTreeAction::PlacementNew_,
                                 sizeof(NodeTreeAction),
                                 alignof(NodeTreeAction));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull NodeTreeAction::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_NodeTreeAction_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &NodeTreeAction::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<NodeTreeAction>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &NodeTreeAction::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<NodeTreeAction>(), &NodeTreeAction::ByteSizeLong,
            &NodeTreeAction::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_._cached_size_),
        false,
    },
    &NodeTreeAction::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* NodeTreeAction::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 0, 0, 2> NodeTreeAction::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeTreeAction>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated int64 siblings = 4;
    {::_pbi::TcParser::FastV64P1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.siblings_)}},
    // int64 parentId = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeTreeAction, _impl_.parentid_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.parentid_)}},
    // .design.TreeActionFlag flag = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(NodeTreeAction, _impl_.flag_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.flag_)}},
    // int64 leftSiblingId = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeTreeAction, _impl_.leftsiblingid_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.leftsiblingid_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int64 parentId = 1;
    {PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.parentid_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // .design.TreeActionFlag flag = 2;
    {PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.flag_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kOpenEnum)},
    // int64 leftSiblingId = 3;
    {PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.leftsiblingid_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // repeated int64 siblings = 4;
    {PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.siblings_), 0, 0,
    (0 | ::_fl::kFcRepeated | ::_fl::kPackedInt64)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void NodeTreeAction::Clear() {
// @@protoc_insertion_point(message_clear_start:design.NodeTreeAction)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.siblings_.Clear();
  ::memset(&_impl_.parentid_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.flag_) -
      reinterpret_cast<char*>(&_impl_.parentid_)) + sizeof(_impl_.flag_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* NodeTreeAction::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const NodeTreeAction& this_ = static_cast<const NodeTreeAction&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* NodeTreeAction::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const NodeTreeAction& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.NodeTreeAction)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int64 parentId = 1;
          if (this_._internal_parentid() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<1>(
                    stream, this_._internal_parentid(), target);
          }

          // .design.TreeActionFlag flag = 2;
          if (this_._internal_flag() != 0) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteEnumToArray(
                2, this_._internal_flag(), target);
          }

          // int64 leftSiblingId = 3;
          if (this_._internal_leftsiblingid() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<3>(
                    stream, this_._internal_leftsiblingid(), target);
          }

          // repeated int64 siblings = 4;
          {
            int byte_size = this_._impl_._siblings_cached_byte_size_.Get();
            if (byte_size > 0) {
              target = stream->WriteInt64Packed(
                  4, this_._internal_siblings(), byte_size, target);
            }
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.NodeTreeAction)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t NodeTreeAction::ByteSizeLong(const MessageLite& base) {
          const NodeTreeAction& this_ = static_cast<const NodeTreeAction&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t NodeTreeAction::ByteSizeLong() const {
          const NodeTreeAction& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.NodeTreeAction)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated int64 siblings = 4;
            {
              total_size +=
                  ::_pbi::WireFormatLite::Int64SizeWithPackedTagSize(
                      this_._internal_siblings(), 1,
                      this_._impl_._siblings_cached_byte_size_);
            }
          }
           {
            // int64 parentId = 1;
            if (this_._internal_parentid() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_parentid());
            }
            // int64 leftSiblingId = 3;
            if (this_._internal_leftsiblingid() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_leftsiblingid());
            }
            // .design.TreeActionFlag flag = 2;
            if (this_._internal_flag() != 0) {
              total_size += 1 +
                            ::_pbi::WireFormatLite::EnumSize(this_._internal_flag());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void NodeTreeAction::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<NodeTreeAction*>(&to_msg);
  auto& from = static_cast<const NodeTreeAction&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.NodeTreeAction)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_siblings()->MergeFrom(from._internal_siblings());
  if (from._internal_parentid() != 0) {
    _this->_impl_.parentid_ = from._impl_.parentid_;
  }
  if (from._internal_leftsiblingid() != 0) {
    _this->_impl_.leftsiblingid_ = from._impl_.leftsiblingid_;
  }
  if (from._internal_flag() != 0) {
    _this->_impl_.flag_ = from._impl_.flag_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void NodeTreeAction::CopyFrom(const NodeTreeAction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.NodeTreeAction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void NodeTreeAction::InternalSwap(NodeTreeAction* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.siblings_.InternalSwap(&other->_impl_.siblings_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.flag_)
      + sizeof(NodeTreeAction::_impl_.flag_)
      - PROTOBUF_FIELD_OFFSET(NodeTreeAction, _impl_.parentid_)>(
          reinterpret_cast<char*>(&_impl_.parentid_),
          reinterpret_cast<char*>(&other->_impl_.parentid_));
}

::google::protobuf::Metadata NodeTreeAction::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class NodeTreeActions::_Internal {
 public:
  using HasBits =
      decltype(std::declval<NodeTreeActions>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_._has_bits_);
};

NodeTreeActions::NodeTreeActions(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.NodeTreeActions)
}
inline PROTOBUF_NDEBUG_INLINE NodeTreeActions::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::NodeTreeActions& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        actions_{visibility, arena, from.actions_} {}

NodeTreeActions::NodeTreeActions(
    ::google::protobuf::Arena* arena,
    const NodeTreeActions& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  NodeTreeActions* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;
  _impl_.traceid_ = from._impl_.traceid_;

  // @@protoc_insertion_point(copy_constructor:design.NodeTreeActions)
}
inline PROTOBUF_NDEBUG_INLINE NodeTreeActions::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        actions_{visibility, arena} {}

inline void NodeTreeActions::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, projectinfo_),
           0,
           offsetof(Impl_, traceid_) -
               offsetof(Impl_, projectinfo_) +
               sizeof(Impl_::traceid_));
}
NodeTreeActions::~NodeTreeActions() {
  // @@protoc_insertion_point(destructor:design.NodeTreeActions)
  SharedDtor(*this);
}
inline void NodeTreeActions::SharedDtor(MessageLite& self) {
  NodeTreeActions& this_ = static_cast<NodeTreeActions&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* NodeTreeActions::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) NodeTreeActions(arena);
}
constexpr auto NodeTreeActions::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.actions_) +
          decltype(NodeTreeActions::_impl_.actions_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(NodeTreeActions), alignof(NodeTreeActions), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&NodeTreeActions::PlacementNew_,
                                 sizeof(NodeTreeActions),
                                 alignof(NodeTreeActions));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull NodeTreeActions::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_NodeTreeActions_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &NodeTreeActions::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<NodeTreeActions>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &NodeTreeActions::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<NodeTreeActions>(), &NodeTreeActions::ByteSizeLong,
            &NodeTreeActions::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_._cached_size_),
        false,
    },
    &NodeTreeActions::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* NodeTreeActions::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 2, 0, 2> NodeTreeActions::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_._has_bits_),
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::NodeTreeActions>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.projectinfo_)}},
    // int64 traceId = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint64_t, offsetof(NodeTreeActions, _impl_.traceid_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.traceid_)}},
    // repeated .design.NodeTreeAction actions = 3;
    {::_pbi::TcParser::FastMtR1,
     {26, 63, 1, PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.actions_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // int64 traceId = 2;
    {PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.traceid_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt64)},
    // repeated .design.NodeTreeAction actions = 3;
    {PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.actions_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
    {::_pbi::TcParser::GetTable<::design::NodeTreeAction>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void NodeTreeActions::Clear() {
// @@protoc_insertion_point(message_clear_start:design.NodeTreeActions)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.actions_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  _impl_.traceid_ = ::int64_t{0};
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* NodeTreeActions::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const NodeTreeActions& this_ = static_cast<const NodeTreeActions&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* NodeTreeActions::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const NodeTreeActions& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.NodeTreeActions)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // int64 traceId = 2;
          if (this_._internal_traceid() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt64ToArrayWithField<2>(
                    stream, this_._internal_traceid(), target);
          }

          // repeated .design.NodeTreeAction actions = 3;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_actions_size());
               i < n; i++) {
            const auto& repfield = this_._internal_actions().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    3, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.NodeTreeActions)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t NodeTreeActions::ByteSizeLong(const MessageLite& base) {
          const NodeTreeActions& this_ = static_cast<const NodeTreeActions&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t NodeTreeActions::ByteSizeLong() const {
          const NodeTreeActions& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.NodeTreeActions)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .design.NodeTreeAction actions = 3;
            {
              total_size += 1UL * this_._internal_actions_size();
              for (const auto& msg : this_._internal_actions()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
           {
            // int64 traceId = 2;
            if (this_._internal_traceid() != 0) {
              total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(
                  this_._internal_traceid());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void NodeTreeActions::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<NodeTreeActions*>(&to_msg);
  auto& from = static_cast<const NodeTreeActions&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.NodeTreeActions)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_actions()->MergeFrom(
      from._internal_actions());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  if (from._internal_traceid() != 0) {
    _this->_impl_.traceid_ = from._impl_.traceid_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void NodeTreeActions::CopyFrom(const NodeTreeActions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.NodeTreeActions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void NodeTreeActions::InternalSwap(NodeTreeActions* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.actions_.InternalSwap(&other->_impl_.actions_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.traceid_)
      + sizeof(NodeTreeActions::_impl_.traceid_)
      - PROTOBUF_FIELD_OFFSET(NodeTreeActions, _impl_.projectinfo_)>(
          reinterpret_cast<char*>(&_impl_.projectinfo_),
          reinterpret_cast<char*>(&other->_impl_.projectinfo_));
}

::google::protobuf::Metadata NodeTreeActions::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class ProjectNodesTree::_Internal {
 public:
  using HasBits =
      decltype(std::declval<ProjectNodesTree>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_._has_bits_);
};

ProjectNodesTree::ProjectNodesTree(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.ProjectNodesTree)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodesTree::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::ProjectNodesTree& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        trees_{visibility, arena, from.trees_} {}

ProjectNodesTree::ProjectNodesTree(
    ::google::protobuf::Arena* arena,
    const ProjectNodesTree& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  ProjectNodesTree* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;

  // @@protoc_insertion_point(copy_constructor:design.ProjectNodesTree)
}
inline PROTOBUF_NDEBUG_INLINE ProjectNodesTree::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        trees_{visibility, arena} {}

inline void ProjectNodesTree::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.projectinfo_ = {};
}
ProjectNodesTree::~ProjectNodesTree() {
  // @@protoc_insertion_point(destructor:design.ProjectNodesTree)
  SharedDtor(*this);
}
inline void ProjectNodesTree::SharedDtor(MessageLite& self) {
  ProjectNodesTree& this_ = static_cast<ProjectNodesTree&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* ProjectNodesTree::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) ProjectNodesTree(arena);
}
constexpr auto ProjectNodesTree::InternalNewImpl_() {
  constexpr auto arena_bits = ::google::protobuf::internal::EncodePlacementArenaOffsets({
      PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_.trees_) +
          decltype(ProjectNodesTree::_impl_.trees_)::
              InternalGetArenaOffset(
                  ::google::protobuf::Message::internal_visibility()),
  });
  if (arena_bits.has_value()) {
    return ::google::protobuf::internal::MessageCreator::ZeroInit(
        sizeof(ProjectNodesTree), alignof(ProjectNodesTree), *arena_bits);
  } else {
    return ::google::protobuf::internal::MessageCreator(&ProjectNodesTree::PlacementNew_,
                                 sizeof(ProjectNodesTree),
                                 alignof(ProjectNodesTree));
  }
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull ProjectNodesTree::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_ProjectNodesTree_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &ProjectNodesTree::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<ProjectNodesTree>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &ProjectNodesTree::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<ProjectNodesTree>(), &ProjectNodesTree::ByteSizeLong,
            &ProjectNodesTree::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_._cached_size_),
        false,
    },
    &ProjectNodesTree::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* ProjectNodesTree::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 2, 0, 2> ProjectNodesTree::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::ProjectNodesTree>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // repeated .design.NodeTreeRecord trees = 2;
    {::_pbi::TcParser::FastMtR1,
     {18, 63, 1, PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_.trees_)}},
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_.projectinfo_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // repeated .design.NodeTreeRecord trees = 2;
    {PROTOBUF_FIELD_OFFSET(ProjectNodesTree, _impl_.trees_), -1, 1,
    (0 | ::_fl::kFcRepeated | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
    {::_pbi::TcParser::GetTable<::design::NodeTreeRecord>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void ProjectNodesTree::Clear() {
// @@protoc_insertion_point(message_clear_start:design.ProjectNodesTree)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.trees_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* ProjectNodesTree::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const ProjectNodesTree& this_ = static_cast<const ProjectNodesTree&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* ProjectNodesTree::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const ProjectNodesTree& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.ProjectNodesTree)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // repeated .design.NodeTreeRecord trees = 2;
          for (unsigned i = 0, n = static_cast<unsigned>(
                                   this_._internal_trees_size());
               i < n; i++) {
            const auto& repfield = this_._internal_trees().Get(i);
            target =
                ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                    2, repfield, repfield.GetCachedSize(),
                    target, stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.ProjectNodesTree)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t ProjectNodesTree::ByteSizeLong(const MessageLite& base) {
          const ProjectNodesTree& this_ = static_cast<const ProjectNodesTree&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t ProjectNodesTree::ByteSizeLong() const {
          const ProjectNodesTree& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.ProjectNodesTree)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // repeated .design.NodeTreeRecord trees = 2;
            {
              total_size += 1UL * this_._internal_trees_size();
              for (const auto& msg : this_._internal_trees()) {
                total_size += ::google::protobuf::internal::WireFormatLite::MessageSize(msg);
              }
            }
          }
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void ProjectNodesTree::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<ProjectNodesTree*>(&to_msg);
  auto& from = static_cast<const ProjectNodesTree&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.ProjectNodesTree)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_internal_mutable_trees()->MergeFrom(
      from._internal_trees());
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void ProjectNodesTree::CopyFrom(const ProjectNodesTree& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.ProjectNodesTree)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void ProjectNodesTree::InternalSwap(ProjectNodesTree* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.trees_.InternalSwap(&other->_impl_.trees_);
  swap(_impl_.projectinfo_, other->_impl_.projectinfo_);
}

::google::protobuf::Metadata ProjectNodesTree::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class DataBlock::_Internal {
 public:
  using HasBits =
      decltype(std::declval<DataBlock>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(DataBlock, _impl_._has_bits_);
};

DataBlock::DataBlock(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.DataBlock)
}
inline PROTOBUF_NDEBUG_INLINE DataBlock::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::DataBlock& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0} {}

DataBlock::DataBlock(
    ::google::protobuf::Arena* arena,
    const DataBlock& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  DataBlock* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;
  _impl_.package_ = (cached_has_bits & 0x00000002u) ? ::google::protobuf::Message::CopyConstruct<::design::MessageQueuesPackage>(
                              arena, *from._impl_.package_)
                        : nullptr;
  ::memcpy(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, flag_),
           reinterpret_cast<const char *>(&from._impl_) +
               offsetof(Impl_, flag_),
           offsetof(Impl_, action_) -
               offsetof(Impl_, flag_) +
               sizeof(Impl_::action_));

  // @@protoc_insertion_point(copy_constructor:design.DataBlock)
}
inline PROTOBUF_NDEBUG_INLINE DataBlock::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void DataBlock::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, projectinfo_),
           0,
           offsetof(Impl_, action_) -
               offsetof(Impl_, projectinfo_) +
               sizeof(Impl_::action_));
}
DataBlock::~DataBlock() {
  // @@protoc_insertion_point(destructor:design.DataBlock)
  SharedDtor(*this);
}
inline void DataBlock::SharedDtor(MessageLite& self) {
  DataBlock& this_ = static_cast<DataBlock&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  delete this_._impl_.projectinfo_;
  delete this_._impl_.package_;
  this_._impl_.~Impl_();
}

inline void* DataBlock::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) DataBlock(arena);
}
constexpr auto DataBlock::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::ZeroInit(sizeof(DataBlock),
                                            alignof(DataBlock));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull DataBlock::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_DataBlock_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &DataBlock::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<DataBlock>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &DataBlock::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<DataBlock>(), &DataBlock::ByteSizeLong,
            &DataBlock::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(DataBlock, _impl_._cached_size_),
        false,
    },
    &DataBlock::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* DataBlock::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<3, 4, 2, 0, 2> DataBlock::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(DataBlock, _impl_._has_bits_),
    0, // no _extensions_
    5, 56,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967272,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::DataBlock>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.projectinfo_)}},
    // int32 flag = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(DataBlock, _impl_.flag_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.flag_)}},
    // int32 action = 3;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(DataBlock, _impl_.action_), 63>(),
     {24, 63, 0, PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.action_)}},
    {::_pbi::TcParser::MiniParse, {}},
    // .design.MessageQueuesPackage package = 5;
    {::_pbi::TcParser::FastMtS1,
     {42, 1, 1, PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.package_)}},
    {::_pbi::TcParser::MiniParse, {}},
    {::_pbi::TcParser::MiniParse, {}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // int32 flag = 2;
    {PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.flag_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // int32 action = 3;
    {PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.action_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // .design.MessageQueuesPackage package = 5;
    {PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.package_), _Internal::kHasBitsOffset + 1, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
    {::_pbi::TcParser::GetTable<::design::MessageQueuesPackage>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void DataBlock::Clear() {
// @@protoc_insertion_point(message_clear_start:design.DataBlock)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
      _impl_.projectinfo_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(_impl_.package_ != nullptr);
      _impl_.package_->Clear();
    }
  }
  ::memset(&_impl_.flag_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.action_) -
      reinterpret_cast<char*>(&_impl_.flag_)) + sizeof(_impl_.action_));
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* DataBlock::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const DataBlock& this_ = static_cast<const DataBlock&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* DataBlock::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const DataBlock& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.DataBlock)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // int32 flag = 2;
          if (this_._internal_flag() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<2>(
                    stream, this_._internal_flag(), target);
          }

          // int32 action = 3;
          if (this_._internal_action() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<3>(
                    stream, this_._internal_action(), target);
          }

          // .design.MessageQueuesPackage package = 5;
          if (cached_has_bits & 0x00000002u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                5, *this_._impl_.package_, this_._impl_.package_->GetCachedSize(), target,
                stream);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.DataBlock)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t DataBlock::ByteSizeLong(const MessageLite& base) {
          const DataBlock& this_ = static_cast<const DataBlock&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t DataBlock::ByteSizeLong() const {
          const DataBlock& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.DataBlock)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
          cached_has_bits = this_._impl_._has_bits_[0];
          if (cached_has_bits & 0x00000003u) {
            // .design.ProjectInfo projectInfo = 1;
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
            // .design.MessageQueuesPackage package = 5;
            if (cached_has_bits & 0x00000002u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.package_);
            }
          }
           {
            // int32 flag = 2;
            if (this_._internal_flag() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_flag());
            }
            // int32 action = 3;
            if (this_._internal_action() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_action());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void DataBlock::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<DataBlock*>(&to_msg);
  auto& from = static_cast<const DataBlock&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.DataBlock)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
      if (_this->_impl_.projectinfo_ == nullptr) {
        _this->_impl_.projectinfo_ =
            ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
      } else {
        _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
      }
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(from._impl_.package_ != nullptr);
      if (_this->_impl_.package_ == nullptr) {
        _this->_impl_.package_ =
            ::google::protobuf::Message::CopyConstruct<::design::MessageQueuesPackage>(arena, *from._impl_.package_);
      } else {
        _this->_impl_.package_->MergeFrom(*from._impl_.package_);
      }
    }
  }
  if (from._internal_flag() != 0) {
    _this->_impl_.flag_ = from._impl_.flag_;
  }
  if (from._internal_action() != 0) {
    _this->_impl_.action_ = from._impl_.action_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void DataBlock::CopyFrom(const DataBlock& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.DataBlock)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void DataBlock::InternalSwap(DataBlock* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.action_)
      + sizeof(DataBlock::_impl_.action_)
      - PROTOBUF_FIELD_OFFSET(DataBlock, _impl_.projectinfo_)>(
          reinterpret_cast<char*>(&_impl_.projectinfo_),
          reinterpret_cast<char*>(&other->_impl_.projectinfo_));
}

::google::protobuf::Metadata DataBlock::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class UserInfo::_Internal {
 public:
  using HasBits =
      decltype(std::declval<UserInfo>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(UserInfo, _impl_._has_bits_);
};

UserInfo::UserInfo(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.UserInfo)
}
inline PROTOBUF_NDEBUG_INLINE UserInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::UserInfo& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        user_(arena, from.user_),
        pwd_(arena, from.pwd_) {}

UserInfo::UserInfo(
    ::google::protobuf::Arena* arena,
    const UserInfo& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  UserInfo* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.projectinfo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(
                              arena, *from._impl_.projectinfo_)
                        : nullptr;
  _impl_.flag_ = from._impl_.flag_;

  // @@protoc_insertion_point(copy_constructor:design.UserInfo)
}
inline PROTOBUF_NDEBUG_INLINE UserInfo::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        user_(arena),
        pwd_(arena) {}

inline void UserInfo::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, projectinfo_),
           0,
           offsetof(Impl_, flag_) -
               offsetof(Impl_, projectinfo_) +
               sizeof(Impl_::flag_));
}
UserInfo::~UserInfo() {
  // @@protoc_insertion_point(destructor:design.UserInfo)
  SharedDtor(*this);
}
inline void UserInfo::SharedDtor(MessageLite& self) {
  UserInfo& this_ = static_cast<UserInfo&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.user_.Destroy();
  this_._impl_.pwd_.Destroy();
  delete this_._impl_.projectinfo_;
  this_._impl_.~Impl_();
}

inline void* UserInfo::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) UserInfo(arena);
}
constexpr auto UserInfo::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(UserInfo),
                                            alignof(UserInfo));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull UserInfo::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_UserInfo_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &UserInfo::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<UserInfo>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &UserInfo::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<UserInfo>(), &UserInfo::ByteSizeLong,
            &UserInfo::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(UserInfo, _impl_._cached_size_),
        false,
    },
    &UserInfo::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* UserInfo::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 4, 1, 31, 2> UserInfo::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(UserInfo, _impl_._has_bits_),
    0, // no _extensions_
    4, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967280,  // skipmap
    offsetof(decltype(_table_), field_entries),
    4,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::UserInfo>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // string pwd = 4;
    {::_pbi::TcParser::FastUS1,
     {34, 63, 0, PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.pwd_)}},
    // .design.ProjectInfo projectInfo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.projectinfo_)}},
    // .design.LoginFlag flag = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(UserInfo, _impl_.flag_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.flag_)}},
    // string user = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.user_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .design.ProjectInfo projectInfo = 1;
    {PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.projectinfo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // .design.LoginFlag flag = 2;
    {PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.flag_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kOpenEnum)},
    // string user = 3;
    {PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.user_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string pwd = 4;
    {PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.pwd_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }}, {{
    {::_pbi::TcParser::GetTable<::design::ProjectInfo>()},
  }}, {{
    "\17\0\0\4\3\0\0\0"
    "design.UserInfo"
    "user"
    "pwd"
  }},
};

PROTOBUF_NOINLINE void UserInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:design.UserInfo)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.user_.ClearToEmpty();
  _impl_.pwd_.ClearToEmpty();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.projectinfo_ != nullptr);
    _impl_.projectinfo_->Clear();
  }
  _impl_.flag_ = 0;
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* UserInfo::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const UserInfo& this_ = static_cast<const UserInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* UserInfo::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const UserInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.UserInfo)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          cached_has_bits = this_._impl_._has_bits_[0];
          // .design.ProjectInfo projectInfo = 1;
          if (cached_has_bits & 0x00000001u) {
            target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
                1, *this_._impl_.projectinfo_, this_._impl_.projectinfo_->GetCachedSize(), target,
                stream);
          }

          // .design.LoginFlag flag = 2;
          if (this_._internal_flag() != 0) {
            target = stream->EnsureSpace(target);
            target = ::_pbi::WireFormatLite::WriteEnumToArray(
                2, this_._internal_flag(), target);
          }

          // string user = 3;
          if (!this_._internal_user().empty()) {
            const std::string& _s = this_._internal_user();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.UserInfo.user");
            target = stream->WriteStringMaybeAliased(3, _s, target);
          }

          // string pwd = 4;
          if (!this_._internal_pwd().empty()) {
            const std::string& _s = this_._internal_pwd();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.UserInfo.pwd");
            target = stream->WriteStringMaybeAliased(4, _s, target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.UserInfo)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t UserInfo::ByteSizeLong(const MessageLite& base) {
          const UserInfo& this_ = static_cast<const UserInfo&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t UserInfo::ByteSizeLong() const {
          const UserInfo& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.UserInfo)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // string user = 3;
            if (!this_._internal_user().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_user());
            }
            // string pwd = 4;
            if (!this_._internal_pwd().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_pwd());
            }
          }
           {
            // .design.ProjectInfo projectInfo = 1;
            cached_has_bits = this_._impl_._has_bits_[0];
            if (cached_has_bits & 0x00000001u) {
              total_size += 1 +
                            ::google::protobuf::internal::WireFormatLite::MessageSize(*this_._impl_.projectinfo_);
            }
          }
           {
            // .design.LoginFlag flag = 2;
            if (this_._internal_flag() != 0) {
              total_size += 1 +
                            ::_pbi::WireFormatLite::EnumSize(this_._internal_flag());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void UserInfo::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<UserInfo*>(&to_msg);
  auto& from = static_cast<const UserInfo&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:design.UserInfo)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_user().empty()) {
    _this->_internal_set_user(from._internal_user());
  }
  if (!from._internal_pwd().empty()) {
    _this->_internal_set_pwd(from._internal_pwd());
  }
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.projectinfo_ != nullptr);
    if (_this->_impl_.projectinfo_ == nullptr) {
      _this->_impl_.projectinfo_ =
          ::google::protobuf::Message::CopyConstruct<::design::ProjectInfo>(arena, *from._impl_.projectinfo_);
    } else {
      _this->_impl_.projectinfo_->MergeFrom(*from._impl_.projectinfo_);
    }
  }
  if (from._internal_flag() != 0) {
    _this->_impl_.flag_ = from._impl_.flag_;
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void UserInfo::CopyFrom(const UserInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.UserInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void UserInfo::InternalSwap(UserInfo* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.user_, &other->_impl_.user_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.pwd_, &other->_impl_.pwd_, arena);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.flag_)
      + sizeof(UserInfo::_impl_.flag_)
      - PROTOBUF_FIELD_OFFSET(UserInfo, _impl_.projectinfo_)>(
          reinterpret_cast<char*>(&_impl_.projectinfo_),
          reinterpret_cast<char*>(&other->_impl_.projectinfo_));
}

::google::protobuf::Metadata UserInfo::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class LoginResponse::_Internal {
 public:
};

LoginResponse::LoginResponse(::google::protobuf::Arena* arena)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:design.LoginResponse)
}
inline PROTOBUF_NDEBUG_INLINE LoginResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::design::LoginResponse& from_msg)
      : message_(arena, from.message_),
        result_(arena, from.result_),
        _cached_size_{0} {}

LoginResponse::LoginResponse(
    ::google::protobuf::Arena* arena,
    const LoginResponse& from)
#if defined(PROTOBUF_CUSTOM_VTABLE)
    : ::google::protobuf::Message(arena, _class_data_.base()) {
#else   // PROTOBUF_CUSTOM_VTABLE
    : ::google::protobuf::Message(arena) {
#endif  // PROTOBUF_CUSTOM_VTABLE
  LoginResponse* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  _impl_.code_ = from._impl_.code_;

  // @@protoc_insertion_point(copy_constructor:design.LoginResponse)
}
inline PROTOBUF_NDEBUG_INLINE LoginResponse::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : message_(arena),
        result_(arena),
        _cached_size_{0} {}

inline void LoginResponse::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.code_ = {};
}
LoginResponse::~LoginResponse() {
  // @@protoc_insertion_point(destructor:design.LoginResponse)
  SharedDtor(*this);
}
inline void LoginResponse::SharedDtor(MessageLite& self) {
  LoginResponse& this_ = static_cast<LoginResponse&>(self);
  this_._internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  ABSL_DCHECK(this_.GetArena() == nullptr);
  this_._impl_.message_.Destroy();
  this_._impl_.result_.Destroy();
  this_._impl_.~Impl_();
}

inline void* LoginResponse::PlacementNew_(const void*, void* mem,
                                        ::google::protobuf::Arena* arena) {
  return ::new (mem) LoginResponse(arena);
}
constexpr auto LoginResponse::InternalNewImpl_() {
  return ::google::protobuf::internal::MessageCreator::CopyInit(sizeof(LoginResponse),
                                            alignof(LoginResponse));
}
PROTOBUF_CONSTINIT
PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::google::protobuf::internal::ClassDataFull LoginResponse::_class_data_ = {
    ::google::protobuf::internal::ClassData{
        &_LoginResponse_default_instance_._instance,
        &_table_.header,
        nullptr,  // OnDemandRegisterArenaDtor
        nullptr,  // IsInitialized
        &LoginResponse::MergeImpl,
        ::google::protobuf::Message::GetNewImpl<LoginResponse>(),
#if defined(PROTOBUF_CUSTOM_VTABLE)
        &LoginResponse::SharedDtor,
        ::google::protobuf::Message::GetClearImpl<LoginResponse>(), &LoginResponse::ByteSizeLong,
            &LoginResponse::_InternalSerialize,
#endif  // PROTOBUF_CUSTOM_VTABLE
        PROTOBUF_FIELD_OFFSET(LoginResponse, _impl_._cached_size_),
        false,
    },
    &LoginResponse::kDescriptorMethods,
    &descriptor_table_node_2eproto,
    nullptr,  // tracker
};
const ::google::protobuf::internal::ClassData* LoginResponse::GetClassData() const {
  ::google::protobuf::internal::PrefetchToLocalCache(&_class_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_class_data_.tc_table);
  return _class_data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 0, 42, 2> LoginResponse::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    _class_data_.base(),
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::design::LoginResponse>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // int32 code = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(LoginResponse, _impl_.code_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(LoginResponse, _impl_.code_)}},
    // string message = 2;
    {::_pbi::TcParser::FastUS1,
     {18, 63, 0, PROTOBUF_FIELD_OFFSET(LoginResponse, _impl_.message_)}},
    // string result = 3;
    {::_pbi::TcParser::FastUS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(LoginResponse, _impl_.result_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int32 code = 1;
    {PROTOBUF_FIELD_OFFSET(LoginResponse, _impl_.code_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // string message = 2;
    {PROTOBUF_FIELD_OFFSET(LoginResponse, _impl_.message_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // string result = 3;
    {PROTOBUF_FIELD_OFFSET(LoginResponse, _impl_.result_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
  }},
  // no aux_entries
  {{
    "\24\0\7\6\0\0\0\0"
    "design.LoginResponse"
    "message"
    "result"
  }},
};

PROTOBUF_NOINLINE void LoginResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:design.LoginResponse)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.message_.ClearToEmpty();
  _impl_.result_.ClearToEmpty();
  _impl_.code_ = 0;
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::uint8_t* LoginResponse::_InternalSerialize(
            const MessageLite& base, ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) {
          const LoginResponse& this_ = static_cast<const LoginResponse&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::uint8_t* LoginResponse::_InternalSerialize(
            ::uint8_t* target,
            ::google::protobuf::io::EpsCopyOutputStream* stream) const {
          const LoginResponse& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(serialize_to_array_start:design.LoginResponse)
          ::uint32_t cached_has_bits = 0;
          (void)cached_has_bits;

          // int32 code = 1;
          if (this_._internal_code() != 0) {
            target = ::google::protobuf::internal::WireFormatLite::
                WriteInt32ToArrayWithField<1>(
                    stream, this_._internal_code(), target);
          }

          // string message = 2;
          if (!this_._internal_message().empty()) {
            const std::string& _s = this_._internal_message();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.LoginResponse.message");
            target = stream->WriteStringMaybeAliased(2, _s, target);
          }

          // string result = 3;
          if (!this_._internal_result().empty()) {
            const std::string& _s = this_._internal_result();
            ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
                _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "design.LoginResponse.result");
            target = stream->WriteStringMaybeAliased(3, _s, target);
          }

          if (PROTOBUF_PREDICT_FALSE(this_._internal_metadata_.have_unknown_fields())) {
            target =
                ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
                    this_._internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
          }
          // @@protoc_insertion_point(serialize_to_array_end:design.LoginResponse)
          return target;
        }

#if defined(PROTOBUF_CUSTOM_VTABLE)
        ::size_t LoginResponse::ByteSizeLong(const MessageLite& base) {
          const LoginResponse& this_ = static_cast<const LoginResponse&>(base);
#else   // PROTOBUF_CUSTOM_VTABLE
        ::size_t LoginResponse::ByteSizeLong() const {
          const LoginResponse& this_ = *this;
#endif  // PROTOBUF_CUSTOM_VTABLE
          // @@protoc_insertion_point(message_byte_size_start:design.LoginResponse)
          ::size_t total_size = 0;

          ::uint32_t cached_has_bits = 0;
          // Prevent compiler warnings about cached_has_bits being unused
          (void)cached_has_bits;

          ::_pbi::Prefetch5LinesFrom7Lines(&this_);
           {
            // string message = 2;
            if (!this_._internal_message().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_message());
            }
            // string result = 3;
            if (!this_._internal_result().empty()) {
              total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                              this_._internal_result());
            }
            // int32 code = 1;
            if (this_._internal_code() != 0) {
              total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
                  this_._internal_code());
            }
          }
          return this_.MaybeComputeUnknownFieldsSize(total_size,
                                                     &this_._impl_._cached_size_);
        }

void LoginResponse::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<LoginResponse*>(&to_msg);
  auto& from = static_cast<const LoginResponse&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:design.LoginResponse)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _this->_internal_set_message(from._internal_message());
  }
  if (!from._internal_result().empty()) {
    _this->_internal_set_result(from._internal_result());
  }
  if (from._internal_code() != 0) {
    _this->_impl_.code_ = from._impl_.code_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void LoginResponse::CopyFrom(const LoginResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:design.LoginResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void LoginResponse::InternalSwap(LoginResponse* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.message_, &other->_impl_.message_, arena);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.result_, &other->_impl_.result_, arena);
        swap(_impl_.code_, other->_impl_.code_);
}

::google::protobuf::Metadata LoginResponse::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// @@protoc_insertion_point(namespace_scope)
}  // namespace design
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::std::false_type
    _static_init2_ PROTOBUF_UNUSED =
        (::_pbi::AddDescriptors(&descriptor_table_node_2eproto),
         ::std::false_type{});
#include "google/protobuf/port_undef.inc"
