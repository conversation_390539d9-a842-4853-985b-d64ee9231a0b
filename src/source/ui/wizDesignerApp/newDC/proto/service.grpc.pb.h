// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: service.proto
#ifndef GRPC_service_2eproto__INCLUDED
#define GRPC_service_2eproto__INCLUDED

#include "service.pb.h"

#include <functional>
#include <grpcpp/generic/async_generic_service.h>
#include <grpcpp/support/async_stream.h>
#include <grpcpp/support/async_unary_call.h>
#include <grpcpp/support/client_callback.h>
#include <grpcpp/client_context.h>
#include <grpcpp/completion_queue.h>
#include <grpcpp/support/message_allocator.h>
#include <grpcpp/support/method_handler.h>
#include <grpcpp/impl/proto_utils.h>
#include <grpcpp/impl/rpc_method.h>
#include <grpcpp/support/server_callback.h>
#include <grpcpp/impl/server_callback_handlers.h>
#include <grpcpp/server_context.h>
#include <grpcpp/impl/service_type.h>
#include <grpcpp/support/status.h>
#include <grpcpp/support/stub_options.h>
#include <grpcpp/support/sync_stream.h>
#include <grpcpp/ports_def.inc>

namespace design {

class DesignService final {
 public:
  static constexpr char const* service_full_name() {
    return "design.DesignService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // 授权登录
    virtual ::grpc::Status Login(::grpc::ClientContext* context, const ::design::UserInfo& request, ::design::LoginResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>> AsyncLogin(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>>(AsyncLoginRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>> PrepareAsyncLogin(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>>(PrepareAsyncLoginRaw(context, request, cq));
    }
    // 签入签出
    virtual ::grpc::Status CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::design::ProjectNodesTree* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>> AsyncCheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>>(AsyncCheckInOutRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>> PrepareAsyncCheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>>(PrepareAsyncCheckInOutRaw(context, request, cq));
    }
    // 获取项目的一些配置信息，例如 mq 的地址等
    virtual ::grpc::Status GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectConfigInfo* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>> AsyncGetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>>(AsyncGetConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>> PrepareAsyncGetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>>(PrepareAsyncGetConfigRaw(context, request, cq));
    }
    // *
    // 通过此接口，将key编码成number类型
    // 常规用法
    // 1. 对属性 key 进行编码，属性key属于集中高频字段
    // 2. 对uuid进行编码，uuid展开字符串占用字节数比较多 在维护层级关系上可以节省空间；可以加快匹配速度
    //  uuid 属于海量数据，进行分库分区分表
    virtual ::grpc::Status AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::design::ProjectCodes* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>> AsyncAcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>>(AsyncAcquireDictionaryCodeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>> PrepareAsyncAcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>>(PrepareAsyncAcquireDictionaryCodeRaw(context, request, cq));
    }
    // 返回所有主题的编码信息；控制包大小，协商一次性返还数量
    virtual ::grpc::Status FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::design::ProjectCodes* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>> AsyncFetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>>(AsyncFetchAllDictionaryCodeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>> PrepareAsyncFetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>>(PrepareAsyncFetchAllDictionaryCodeRaw(context, request, cq));
    }
    // 将nodes保存到服务端，同时返回 offset、len、crc；
    // offset 为逻辑增量，可以作为更新标记，新增节点默认都为签出状态
    virtual ::grpc::Status UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::design::ProjectNodesResult* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>> AsyncUpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>>(AsyncUpdateNodesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>> PrepareAsyncUpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>>(PrepareAsyncUpdateNodesRaw(context, request, cq));
    }
    // 更新节点层次关系；所有节点 先保存再更新关系，关系采用映射之后的number维护
    virtual ::grpc::Status UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::design::ProjectNodesResult* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>> AsyncUpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>>(AsyncUpdateNodeTreeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>> PrepareAsyncUpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>>(PrepareAsyncUpdateNodeTreeRaw(context, request, cq));
    }
    // 客户端导入项目进度状态
    virtual ::grpc::Status ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectInfo* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>> AsyncImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>>(AsyncImportProjectProgressRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>> PrepareAsyncImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>>(PrepareAsyncImportProjectProgressRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      // 授权登录
      virtual void Login(::grpc::ClientContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Login(::grpc::ClientContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // 签入签出
      virtual void CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // 获取项目的一些配置信息，例如 mq 的地址等
      virtual void GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // *
      // 通过此接口，将key编码成number类型
      // 常规用法
      // 1. 对属性 key 进行编码，属性key属于集中高频字段
      // 2. 对uuid进行编码，uuid展开字符串占用字节数比较多 在维护层级关系上可以节省空间；可以加快匹配速度
      //  uuid 属于海量数据，进行分库分区分表
      virtual void AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response, std::function<void(::grpc::Status)>) = 0;
      virtual void AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // 返回所有主题的编码信息；控制包大小，协商一次性返还数量
      virtual void FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response, std::function<void(::grpc::Status)>) = 0;
      virtual void FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // 将nodes保存到服务端，同时返回 offset、len、crc；
      // offset 为逻辑增量，可以作为更新标记，新增节点默认都为签出状态
      virtual void UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // 更新节点层次关系；所有节点 先保存再更新关系，关系采用映射之后的number维护
      virtual void UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // 客户端导入项目进度状态
      virtual void ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>* AsyncLoginRaw(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::LoginResponse>* PrepareAsyncLoginRaw(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>* AsyncCheckInOutRaw(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesTree>* PrepareAsyncCheckInOutRaw(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>* AsyncGetConfigRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectConfigInfo>* PrepareAsyncGetConfigRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>* AsyncAcquireDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>* PrepareAsyncAcquireDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>* AsyncFetchAllDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectCodes>* PrepareAsyncFetchAllDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>* AsyncUpdateNodesRaw(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>* PrepareAsyncUpdateNodesRaw(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>* AsyncUpdateNodeTreeRaw(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectNodesResult>* PrepareAsyncUpdateNodeTreeRaw(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>* AsyncImportProjectProgressRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::design::ProjectInfo>* PrepareAsyncImportProjectProgressRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Login(::grpc::ClientContext* context, const ::design::UserInfo& request, ::design::LoginResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::LoginResponse>> AsyncLogin(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::LoginResponse>>(AsyncLoginRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::LoginResponse>> PrepareAsyncLogin(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::LoginResponse>>(PrepareAsyncLoginRaw(context, request, cq));
    }
    ::grpc::Status CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::design::ProjectNodesTree* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>> AsyncCheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>>(AsyncCheckInOutRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>> PrepareAsyncCheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>>(PrepareAsyncCheckInOutRaw(context, request, cq));
    }
    ::grpc::Status GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectConfigInfo* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>> AsyncGetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>>(AsyncGetConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>> PrepareAsyncGetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>>(PrepareAsyncGetConfigRaw(context, request, cq));
    }
    ::grpc::Status AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::design::ProjectCodes* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>> AsyncAcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>>(AsyncAcquireDictionaryCodeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>> PrepareAsyncAcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>>(PrepareAsyncAcquireDictionaryCodeRaw(context, request, cq));
    }
    ::grpc::Status FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::design::ProjectCodes* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>> AsyncFetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>>(AsyncFetchAllDictionaryCodeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>> PrepareAsyncFetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>>(PrepareAsyncFetchAllDictionaryCodeRaw(context, request, cq));
    }
    ::grpc::Status UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::design::ProjectNodesResult* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>> AsyncUpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>>(AsyncUpdateNodesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>> PrepareAsyncUpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>>(PrepareAsyncUpdateNodesRaw(context, request, cq));
    }
    ::grpc::Status UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::design::ProjectNodesResult* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>> AsyncUpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>>(AsyncUpdateNodeTreeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>> PrepareAsyncUpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>>(PrepareAsyncUpdateNodeTreeRaw(context, request, cq));
    }
    ::grpc::Status ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::design::ProjectInfo* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>> AsyncImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>>(AsyncImportProjectProgressRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>> PrepareAsyncImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>>(PrepareAsyncImportProjectProgressRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Login(::grpc::ClientContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response, std::function<void(::grpc::Status)>) override;
      void Login(::grpc::ClientContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response, std::function<void(::grpc::Status)>) override;
      void CheckInOut(::grpc::ClientContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response, std::function<void(::grpc::Status)>) override;
      void GetConfig(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response, ::grpc::ClientUnaryReactor* reactor) override;
      void AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response, std::function<void(::grpc::Status)>) override;
      void AcquireDictionaryCode(::grpc::ClientContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response, ::grpc::ClientUnaryReactor* reactor) override;
      void FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response, std::function<void(::grpc::Status)>) override;
      void FetchAllDictionaryCode(::grpc::ClientContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response, std::function<void(::grpc::Status)>) override;
      void UpdateNodes(::grpc::ClientContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response, std::function<void(::grpc::Status)>) override;
      void UpdateNodeTree(::grpc::ClientContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response, std::function<void(::grpc::Status)>) override;
      void ImportProjectProgress(::grpc::ClientContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::design::LoginResponse>* AsyncLoginRaw(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::LoginResponse>* PrepareAsyncLoginRaw(::grpc::ClientContext* context, const ::design::UserInfo& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>* AsyncCheckInOutRaw(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesTree>* PrepareAsyncCheckInOutRaw(::grpc::ClientContext* context, const ::design::ProjectNodesTree& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>* AsyncGetConfigRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectConfigInfo>* PrepareAsyncGetConfigRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* AsyncAcquireDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* PrepareAsyncAcquireDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::ProjectCodes& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* AsyncFetchAllDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectCodes>* PrepareAsyncFetchAllDictionaryCodeRaw(::grpc::ClientContext* context, const ::design::MaxOffset& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* AsyncUpdateNodesRaw(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* PrepareAsyncUpdateNodesRaw(::grpc::ClientContext* context, const ::design::ProjectNodes& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* AsyncUpdateNodeTreeRaw(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectNodesResult>* PrepareAsyncUpdateNodeTreeRaw(::grpc::ClientContext* context, const ::design::NodeTreeActions& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>* AsyncImportProjectProgressRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::design::ProjectInfo>* PrepareAsyncImportProjectProgressRaw(::grpc::ClientContext* context, const ::design::ProjectInfo& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Login_;
    const ::grpc::internal::RpcMethod rpcmethod_CheckInOut_;
    const ::grpc::internal::RpcMethod rpcmethod_GetConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_AcquireDictionaryCode_;
    const ::grpc::internal::RpcMethod rpcmethod_FetchAllDictionaryCode_;
    const ::grpc::internal::RpcMethod rpcmethod_UpdateNodes_;
    const ::grpc::internal::RpcMethod rpcmethod_UpdateNodeTree_;
    const ::grpc::internal::RpcMethod rpcmethod_ImportProjectProgress_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // 授权登录
    virtual ::grpc::Status Login(::grpc::ServerContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response);
    // 签入签出
    virtual ::grpc::Status CheckInOut(::grpc::ServerContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response);
    // 获取项目的一些配置信息，例如 mq 的地址等
    virtual ::grpc::Status GetConfig(::grpc::ServerContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response);
    // *
    // 通过此接口，将key编码成number类型
    // 常规用法
    // 1. 对属性 key 进行编码，属性key属于集中高频字段
    // 2. 对uuid进行编码，uuid展开字符串占用字节数比较多 在维护层级关系上可以节省空间；可以加快匹配速度
    //  uuid 属于海量数据，进行分库分区分表
    virtual ::grpc::Status AcquireDictionaryCode(::grpc::ServerContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response);
    // 返回所有主题的编码信息；控制包大小，协商一次性返还数量
    virtual ::grpc::Status FetchAllDictionaryCode(::grpc::ServerContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response);
    // 将nodes保存到服务端，同时返回 offset、len、crc；
    // offset 为逻辑增量，可以作为更新标记，新增节点默认都为签出状态
    virtual ::grpc::Status UpdateNodes(::grpc::ServerContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response);
    // 更新节点层次关系；所有节点 先保存再更新关系，关系采用映射之后的number维护
    virtual ::grpc::Status UpdateNodeTree(::grpc::ServerContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response);
    // 客户端导入项目进度状态
    virtual ::grpc::Status ImportProjectProgress(::grpc::ServerContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Login : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Login() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Login() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Login(::grpc::ServerContext* /*context*/, const ::design::UserInfo* /*request*/, ::design::LoginResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLogin(::grpc::ServerContext* context, ::design::UserInfo* request, ::grpc::ServerAsyncResponseWriter< ::design::LoginResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CheckInOut : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CheckInOut() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_CheckInOut() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CheckInOut(::grpc::ServerContext* /*context*/, const ::design::ProjectNodesTree* /*request*/, ::design::ProjectNodesTree* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCheckInOut(::grpc::ServerContext* context, ::design::ProjectNodesTree* request, ::grpc::ServerAsyncResponseWriter< ::design::ProjectNodesTree>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetConfig() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectConfigInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfig(::grpc::ServerContext* context, ::design::ProjectInfo* request, ::grpc::ServerAsyncResponseWriter< ::design::ProjectConfigInfo>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_AcquireDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_AcquireDictionaryCode() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_AcquireDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcquireDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::ProjectCodes* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAcquireDictionaryCode(::grpc::ServerContext* context, ::design::ProjectCodes* request, ::grpc::ServerAsyncResponseWriter< ::design::ProjectCodes>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_FetchAllDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_FetchAllDictionaryCode() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_FetchAllDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchAllDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::MaxOffset* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFetchAllDictionaryCode(::grpc::ServerContext* context, ::design::MaxOffset* request, ::grpc::ServerAsyncResponseWriter< ::design::ProjectCodes>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpdateNodes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpdateNodes() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_UpdateNodes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodes(::grpc::ServerContext* /*context*/, const ::design::ProjectNodes* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateNodes(::grpc::ServerContext* context, ::design::ProjectNodes* request, ::grpc::ServerAsyncResponseWriter< ::design::ProjectNodesResult>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpdateNodeTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpdateNodeTree() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_UpdateNodeTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodeTree(::grpc::ServerContext* /*context*/, const ::design::NodeTreeActions* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateNodeTree(::grpc::ServerContext* context, ::design::NodeTreeActions* request, ::grpc::ServerAsyncResponseWriter< ::design::ProjectNodesResult>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ImportProjectProgress : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ImportProjectProgress() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_ImportProjectProgress() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ImportProjectProgress(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestImportProjectProgress(::grpc::ServerContext* context, ::design::ProjectInfo* request, ::grpc::ServerAsyncResponseWriter< ::design::ProjectInfo>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Login<WithAsyncMethod_CheckInOut<WithAsyncMethod_GetConfig<WithAsyncMethod_AcquireDictionaryCode<WithAsyncMethod_FetchAllDictionaryCode<WithAsyncMethod_UpdateNodes<WithAsyncMethod_UpdateNodeTree<WithAsyncMethod_ImportProjectProgress<Service > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Login : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Login() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::design::UserInfo, ::design::LoginResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::UserInfo* request, ::design::LoginResponse* response) { return this->Login(context, request, response); }));}
    void SetMessageAllocatorFor_Login(
        ::grpc::MessageAllocator< ::design::UserInfo, ::design::LoginResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::UserInfo, ::design::LoginResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Login() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Login(::grpc::ServerContext* /*context*/, const ::design::UserInfo* /*request*/, ::design::LoginResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Login(
      ::grpc::CallbackServerContext* /*context*/, const ::design::UserInfo* /*request*/, ::design::LoginResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CheckInOut : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CheckInOut() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::design::ProjectNodesTree, ::design::ProjectNodesTree>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::ProjectNodesTree* request, ::design::ProjectNodesTree* response) { return this->CheckInOut(context, request, response); }));}
    void SetMessageAllocatorFor_CheckInOut(
        ::grpc::MessageAllocator< ::design::ProjectNodesTree, ::design::ProjectNodesTree>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::ProjectNodesTree, ::design::ProjectNodesTree>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CheckInOut() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CheckInOut(::grpc::ServerContext* /*context*/, const ::design::ProjectNodesTree* /*request*/, ::design::ProjectNodesTree* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CheckInOut(
      ::grpc::CallbackServerContext* /*context*/, const ::design::ProjectNodesTree* /*request*/, ::design::ProjectNodesTree* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetConfig() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::design::ProjectInfo, ::design::ProjectConfigInfo>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::ProjectInfo* request, ::design::ProjectConfigInfo* response) { return this->GetConfig(context, request, response); }));}
    void SetMessageAllocatorFor_GetConfig(
        ::grpc::MessageAllocator< ::design::ProjectInfo, ::design::ProjectConfigInfo>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::ProjectInfo, ::design::ProjectConfigInfo>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectConfigInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectConfigInfo* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_AcquireDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_AcquireDictionaryCode() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::design::ProjectCodes, ::design::ProjectCodes>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::ProjectCodes* request, ::design::ProjectCodes* response) { return this->AcquireDictionaryCode(context, request, response); }));}
    void SetMessageAllocatorFor_AcquireDictionaryCode(
        ::grpc::MessageAllocator< ::design::ProjectCodes, ::design::ProjectCodes>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::ProjectCodes, ::design::ProjectCodes>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_AcquireDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcquireDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::ProjectCodes* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AcquireDictionaryCode(
      ::grpc::CallbackServerContext* /*context*/, const ::design::ProjectCodes* /*request*/, ::design::ProjectCodes* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_FetchAllDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_FetchAllDictionaryCode() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::design::MaxOffset, ::design::ProjectCodes>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::MaxOffset* request, ::design::ProjectCodes* response) { return this->FetchAllDictionaryCode(context, request, response); }));}
    void SetMessageAllocatorFor_FetchAllDictionaryCode(
        ::grpc::MessageAllocator< ::design::MaxOffset, ::design::ProjectCodes>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::MaxOffset, ::design::ProjectCodes>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_FetchAllDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchAllDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::MaxOffset* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FetchAllDictionaryCode(
      ::grpc::CallbackServerContext* /*context*/, const ::design::MaxOffset* /*request*/, ::design::ProjectCodes* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UpdateNodes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UpdateNodes() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::design::ProjectNodes, ::design::ProjectNodesResult>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::ProjectNodes* request, ::design::ProjectNodesResult* response) { return this->UpdateNodes(context, request, response); }));}
    void SetMessageAllocatorFor_UpdateNodes(
        ::grpc::MessageAllocator< ::design::ProjectNodes, ::design::ProjectNodesResult>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::ProjectNodes, ::design::ProjectNodesResult>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UpdateNodes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodes(::grpc::ServerContext* /*context*/, const ::design::ProjectNodes* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateNodes(
      ::grpc::CallbackServerContext* /*context*/, const ::design::ProjectNodes* /*request*/, ::design::ProjectNodesResult* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UpdateNodeTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UpdateNodeTree() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::design::NodeTreeActions, ::design::ProjectNodesResult>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::NodeTreeActions* request, ::design::ProjectNodesResult* response) { return this->UpdateNodeTree(context, request, response); }));}
    void SetMessageAllocatorFor_UpdateNodeTree(
        ::grpc::MessageAllocator< ::design::NodeTreeActions, ::design::ProjectNodesResult>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::NodeTreeActions, ::design::ProjectNodesResult>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UpdateNodeTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodeTree(::grpc::ServerContext* /*context*/, const ::design::NodeTreeActions* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateNodeTree(
      ::grpc::CallbackServerContext* /*context*/, const ::design::NodeTreeActions* /*request*/, ::design::ProjectNodesResult* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ImportProjectProgress : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ImportProjectProgress() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::design::ProjectInfo, ::design::ProjectInfo>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::design::ProjectInfo* request, ::design::ProjectInfo* response) { return this->ImportProjectProgress(context, request, response); }));}
    void SetMessageAllocatorFor_ImportProjectProgress(
        ::grpc::MessageAllocator< ::design::ProjectInfo, ::design::ProjectInfo>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::design::ProjectInfo, ::design::ProjectInfo>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ImportProjectProgress() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ImportProjectProgress(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ImportProjectProgress(
      ::grpc::CallbackServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectInfo* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Login<WithCallbackMethod_CheckInOut<WithCallbackMethod_GetConfig<WithCallbackMethod_AcquireDictionaryCode<WithCallbackMethod_FetchAllDictionaryCode<WithCallbackMethod_UpdateNodes<WithCallbackMethod_UpdateNodeTree<WithCallbackMethod_ImportProjectProgress<Service > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Login : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Login() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Login() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Login(::grpc::ServerContext* /*context*/, const ::design::UserInfo* /*request*/, ::design::LoginResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CheckInOut : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CheckInOut() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_CheckInOut() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CheckInOut(::grpc::ServerContext* /*context*/, const ::design::ProjectNodesTree* /*request*/, ::design::ProjectNodesTree* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetConfig() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectConfigInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_AcquireDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_AcquireDictionaryCode() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_AcquireDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcquireDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::ProjectCodes* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_FetchAllDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_FetchAllDictionaryCode() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_FetchAllDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchAllDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::MaxOffset* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpdateNodes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpdateNodes() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_UpdateNodes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodes(::grpc::ServerContext* /*context*/, const ::design::ProjectNodes* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpdateNodeTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpdateNodeTree() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_UpdateNodeTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodeTree(::grpc::ServerContext* /*context*/, const ::design::NodeTreeActions* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ImportProjectProgress : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ImportProjectProgress() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_ImportProjectProgress() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ImportProjectProgress(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Login : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Login() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Login() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Login(::grpc::ServerContext* /*context*/, const ::design::UserInfo* /*request*/, ::design::LoginResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLogin(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CheckInOut : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CheckInOut() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_CheckInOut() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CheckInOut(::grpc::ServerContext* /*context*/, const ::design::ProjectNodesTree* /*request*/, ::design::ProjectNodesTree* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCheckInOut(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetConfig() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectConfigInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_AcquireDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_AcquireDictionaryCode() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_AcquireDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcquireDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::ProjectCodes* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAcquireDictionaryCode(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_FetchAllDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_FetchAllDictionaryCode() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_FetchAllDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchAllDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::MaxOffset* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFetchAllDictionaryCode(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpdateNodes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpdateNodes() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_UpdateNodes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodes(::grpc::ServerContext* /*context*/, const ::design::ProjectNodes* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateNodes(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpdateNodeTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpdateNodeTree() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_UpdateNodeTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodeTree(::grpc::ServerContext* /*context*/, const ::design::NodeTreeActions* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateNodeTree(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ImportProjectProgress : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ImportProjectProgress() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_ImportProjectProgress() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ImportProjectProgress(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestImportProjectProgress(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Login : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Login() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Login(context, request, response); }));
    }
    ~WithRawCallbackMethod_Login() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Login(::grpc::ServerContext* /*context*/, const ::design::UserInfo* /*request*/, ::design::LoginResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Login(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CheckInOut : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CheckInOut() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CheckInOut(context, request, response); }));
    }
    ~WithRawCallbackMethod_CheckInOut() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CheckInOut(::grpc::ServerContext* /*context*/, const ::design::ProjectNodesTree* /*request*/, ::design::ProjectNodesTree* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CheckInOut(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetConfig() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectConfigInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_AcquireDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_AcquireDictionaryCode() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->AcquireDictionaryCode(context, request, response); }));
    }
    ~WithRawCallbackMethod_AcquireDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcquireDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::ProjectCodes* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AcquireDictionaryCode(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_FetchAllDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_FetchAllDictionaryCode() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->FetchAllDictionaryCode(context, request, response); }));
    }
    ~WithRawCallbackMethod_FetchAllDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchAllDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::MaxOffset* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FetchAllDictionaryCode(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UpdateNodes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UpdateNodes() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpdateNodes(context, request, response); }));
    }
    ~WithRawCallbackMethod_UpdateNodes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodes(::grpc::ServerContext* /*context*/, const ::design::ProjectNodes* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateNodes(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UpdateNodeTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UpdateNodeTree() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpdateNodeTree(context, request, response); }));
    }
    ~WithRawCallbackMethod_UpdateNodeTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateNodeTree(::grpc::ServerContext* /*context*/, const ::design::NodeTreeActions* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateNodeTree(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ImportProjectProgress : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ImportProjectProgress() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ImportProjectProgress(context, request, response); }));
    }
    ~WithRawCallbackMethod_ImportProjectProgress() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ImportProjectProgress(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ImportProjectProgress(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Login : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Login() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::UserInfo, ::design::LoginResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::UserInfo, ::design::LoginResponse>* streamer) {
                       return this->StreamedLogin(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Login() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Login(::grpc::ServerContext* /*context*/, const ::design::UserInfo* /*request*/, ::design::LoginResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedLogin(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::UserInfo,::design::LoginResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CheckInOut : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CheckInOut() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::ProjectNodesTree, ::design::ProjectNodesTree>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::ProjectNodesTree, ::design::ProjectNodesTree>* streamer) {
                       return this->StreamedCheckInOut(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CheckInOut() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CheckInOut(::grpc::ServerContext* /*context*/, const ::design::ProjectNodesTree* /*request*/, ::design::ProjectNodesTree* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCheckInOut(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::ProjectNodesTree,::design::ProjectNodesTree>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetConfig() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::ProjectInfo, ::design::ProjectConfigInfo>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::ProjectInfo, ::design::ProjectConfigInfo>* streamer) {
                       return this->StreamedGetConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectConfigInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::ProjectInfo,::design::ProjectConfigInfo>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_AcquireDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_AcquireDictionaryCode() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::ProjectCodes, ::design::ProjectCodes>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::ProjectCodes, ::design::ProjectCodes>* streamer) {
                       return this->StreamedAcquireDictionaryCode(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_AcquireDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status AcquireDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::ProjectCodes* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedAcquireDictionaryCode(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::ProjectCodes,::design::ProjectCodes>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_FetchAllDictionaryCode : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_FetchAllDictionaryCode() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::MaxOffset, ::design::ProjectCodes>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::MaxOffset, ::design::ProjectCodes>* streamer) {
                       return this->StreamedFetchAllDictionaryCode(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_FetchAllDictionaryCode() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status FetchAllDictionaryCode(::grpc::ServerContext* /*context*/, const ::design::MaxOffset* /*request*/, ::design::ProjectCodes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedFetchAllDictionaryCode(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::MaxOffset,::design::ProjectCodes>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpdateNodes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpdateNodes() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::ProjectNodes, ::design::ProjectNodesResult>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::ProjectNodes, ::design::ProjectNodesResult>* streamer) {
                       return this->StreamedUpdateNodes(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UpdateNodes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpdateNodes(::grpc::ServerContext* /*context*/, const ::design::ProjectNodes* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpdateNodes(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::ProjectNodes,::design::ProjectNodesResult>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpdateNodeTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpdateNodeTree() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::NodeTreeActions, ::design::ProjectNodesResult>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::NodeTreeActions, ::design::ProjectNodesResult>* streamer) {
                       return this->StreamedUpdateNodeTree(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UpdateNodeTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpdateNodeTree(::grpc::ServerContext* /*context*/, const ::design::NodeTreeActions* /*request*/, ::design::ProjectNodesResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpdateNodeTree(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::NodeTreeActions,::design::ProjectNodesResult>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ImportProjectProgress : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ImportProjectProgress() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::design::ProjectInfo, ::design::ProjectInfo>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::design::ProjectInfo, ::design::ProjectInfo>* streamer) {
                       return this->StreamedImportProjectProgress(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ImportProjectProgress() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ImportProjectProgress(::grpc::ServerContext* /*context*/, const ::design::ProjectInfo* /*request*/, ::design::ProjectInfo* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedImportProjectProgress(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::design::ProjectInfo,::design::ProjectInfo>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Login<WithStreamedUnaryMethod_CheckInOut<WithStreamedUnaryMethod_GetConfig<WithStreamedUnaryMethod_AcquireDictionaryCode<WithStreamedUnaryMethod_FetchAllDictionaryCode<WithStreamedUnaryMethod_UpdateNodes<WithStreamedUnaryMethod_UpdateNodeTree<WithStreamedUnaryMethod_ImportProjectProgress<Service > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Login<WithStreamedUnaryMethod_CheckInOut<WithStreamedUnaryMethod_GetConfig<WithStreamedUnaryMethod_AcquireDictionaryCode<WithStreamedUnaryMethod_FetchAllDictionaryCode<WithStreamedUnaryMethod_UpdateNodes<WithStreamedUnaryMethod_UpdateNodeTree<WithStreamedUnaryMethod_ImportProjectProgress<Service > > > > > > > > StreamedService;
};

}  // namespace design


#include <grpcpp/ports_undef.inc>
#endif  // GRPC_service_2eproto__INCLUDED
