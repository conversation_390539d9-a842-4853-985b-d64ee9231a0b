#pragma once

#include <QThread>
#include <QEventLoop>
#include "core/WDCore.h"
#include "Collaboration.h"
#include "preWidget/WizSplashScreen.h"
#include "core/businessModule/WDBMProjectMgr.h"
#include "core/log/WDLoggerPort.h"

WD_NAMESPACE_BEGIN

class ProjectDataLoadThread : public QThread
{
	Q_OBJECT
public:
	ProjectDataLoadThread(QEventLoop& loop
		, WDCore& core
		, ICollaboration& collaboration
		, WizSplashScreen& splashScreen)
		: _loop(loop)
		, _core(core)
		, _collaboration(collaboration)
		, _splashScreen(splashScreen)
		, _bExit(false)
	{
		_splashScreen.setProjectDataLoadThread(this);
		connect(this, &ProjectDataLoadThread::sigProgressText, this, &ProjectDataLoadThread::slotProgressText);
		connect(this, &ProjectDataLoadThread::sigMsg, this, &ProjectDataLoadThread::slotMsg);
	}
public:
	void setExit() 
	{
		_bExit = true;
	}
signals:
	void sigProgressText(const QString& text, double value);
	void sigMsg(const QString& text);
protected:
	virtual void run() override 
	{
		/****** 根据选择的项目数据加载本地数据文件  *******/
		// 加载项目,注意,模块根节点在此处创建,完成项目加载后可以通过类型管理创建节点
		WDBMProject project;
		project.name = _collaboration.projectInfo().name;
		project.code = _collaboration.projectInfo().code;
		project.id = _collaboration.projectInfo().id;
		project.projectDir = std::string(_core.dataDirPath()) + "db/projects/";
		_core.projectMgr().load(project
			// 设置进度的回调函数
			, [&](const char* text, double value)
			{
				emit sigProgressText(QString::fromUtf8(text), value);
				// 返回中断加载且退出的标志
				return _bExit;
			}
			// 输出日志的回调函数
			, [&](const char* text, int level)
			{
				WD::WDLogger::getInstance()->log(WD::WDLogger::LogLevel(level), "", -1, text);
			}
			// 显示消息的回调函数
			, [&](const char* text)
			{
				emit sigMsg(QString::fromUtf8(text));
			});
		_loop.quit();
	}
private slots:
	void slotProgressText(const QString& text, double value)
	{
		_splashScreen.setProgressText(value, text);
	}
	void slotMsg(const QString& text) 
	{
		_splashScreen.showMsg(text);
	}
private:
	QEventLoop& _loop;
	WDCore& _core;
	ICollaboration& _collaboration;
	WizSplashScreen& _splashScreen;
	bool _bExit;
};

WD_NAMESPACE_END