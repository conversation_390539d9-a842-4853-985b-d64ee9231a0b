#include    "QTableWidget2Excel.h"
#include    <qfiledialog.h>
#ifdef WIN32
    #include    <qaxobject.h>
#endif
#include    "core/WDTranslate.h"
#ifdef WIN32
#include <combaseapi.h>
#else
#include <uuid/uuid.h>
#endif
#include <QTemporaryFile>
#include    "core/WDCore.h"
#include    "core/message/WDMessage.h"

QAxObject*  QTableWidget2Excel::CreateNewExCelObj(ApplicationFlag* pFlag)
{
#ifdef WIN32
    auto excel = new QAxObject();
    // 判断是WPS还是Word操作表格
    if (excel->setControl("ket.Application"))
    {
        if (pFlag != nullptr)
            *pFlag = ApplicationFlag::Wps;
    }
    else if (excel->setControl("Excel.Application"))
    {
        if (pFlag != nullptr)
            *pFlag = ApplicationFlag::Word;
    }
    else
    {
        delete excel;
        excel = nullptr;
    }
    return excel;
#else
    return nullptr;
#endif
}
void        QTableWidget2Excel::DestoryExCelObj(QAxObject* pObj)
{
    if (pObj != nullptr)
    {
#ifdef WIN32
        pObj->dynamicCall("Quit()");
        delete pObj;
        pObj = nullptr;
#endif
    }
}
QVariantList QTableWidget2Excel::FuncDataConvert(const ExcelData& data, int startRow)
{
    QVariantList rList;
    rList.reserve(static_cast<int>(data.size()));
    for (size_t i = startRow; i < data.size(); ++i)
    {
        const auto& rowData = data[i];
        QVariantList tList;
        tList.reserve(static_cast<int>(rowData.size()));
        for (size_t j = 0; j < rowData.size(); ++j)
        {
            QVariant value = QVariant(QString::fromUtf8(rowData[j].c_str()));
            tList.push_back(value);
        }
        rList.push_back(tList);
    }
    return rList;
}
QVariantList QTableWidget2Excel::FuncDataConvert(const Sheet& sheet,    int startRow)
{
    return FuncDataConvert(sheet.data, startRow);
}

QAxObject*  QTableWidget2Excel::GetWorkSheetByIndex(QAxObject& workBook, int index, bool bAutoAddSheet)
{
#ifdef WIN32
    if (index < 1)
        return nullptr;
    // 获取工作表集合
    auto pWorkSheets =  workBook.querySubObject("Sheets");
    if (pWorkSheets == nullptr)
        return nullptr;

    int workSheetsCnt = pWorkSheets->property("Count").toInt();

    if (workSheetsCnt >= index)
        return pWorkSheets->querySubObject("Item(int)", index);

    if (!bAutoAddSheet)
        return nullptr;

    for(size_t i = workSheetsCnt + 1; i <= index; ++i)
    {
        QAxObject* pObject = pWorkSheets->querySubObject("Add");
        assert(pObject != nullptr);
        if (i  == index)
        {
            pWorkSheets->deleteLater();
            return pObject;
        }
        pObject->deleteLater();
    }
    pWorkSheets->deleteLater();
    assert(false);
    return nullptr;
#else
    return nullptr;
#endif
}

void    QTableWidget2Excel::SaveWorkBook(const QString& filePath, QAxObject& workBook)
{
#ifdef WIN32
    workBook.dynamicCall("SaveAs(const QString&)", QDir::toNativeSeparators(filePath));
#endif
}

QAxObject*  QTableWidget2Excel::MergeCells(QAxObject& obj, const QString& start, const QString& end)
{
#ifdef WIN32
    // 获取excel的范围对象
    QAxObject* range = obj.querySubObject("Range(const QVariant&)", QVariant(start + ": " + end));
    if (range == nullptr)
        return nullptr;
    // 合并范围内的单元格
    range->dynamicCall("Merge()");
    return range;
#else
    return nullptr;
#endif
}

QAxObject*  QTableWidget2Excel::GetCell(QAxObject& obj, const QString& index)
{
#ifdef WIN32
    // 获取excel的范围对象
    return  obj.querySubObject("Range(const QString&)", index);
#else
    return nullptr;
#endif
}
QAxObject* QTableWidget2Excel::GetCell(QAxObject& obj, int row, int col)
{
#ifdef WIN32
    // 获取excel的范围对象
    return obj.querySubObject("Cells(int,int)", row, col);
#else
    return nullptr;
#endif
}
bool QTableWidget2Excel::SetExcelRowHeight(QAxObject& obj, int index, double height)
{
#ifdef WIN32
    auto pRowObj = obj.querySubObject("Rows(int)", index);
    if (pRowObj == nullptr)
        return false;
    pRowObj->setProperty("RowHeight", height);
    pRowObj->deleteLater();
    return true;
#else
    return false;
#endif
}

bool QTableWidget2Excel::SetExcelColWidth(QAxObject& obj, int index, double width)
{
#ifdef WIN32
    auto pColObj = obj.querySubObject("Columns(int)", index);
    if (pColObj == nullptr)
        return false;
    pColObj->dynamicCall("ColumnWidth", width);
    pColObj->deleteLater();
    return true;
#else
    return false;
#endif
}

void QTableWidget2Excel::SetCellData(QAxObject& cell, const QVariant& data, const std::optional<ExcelFont>& font)
{
#ifdef WIN32
    // 设置导出Cell类型为文本类型
    cell.dynamicCall("SetValue(const QVariant&)", data);
    cell.setProperty("NumberFormat", "General"); // 明确设置单元格格式为常规
    if (font)
    {
        const auto& fontVal = font.value();
        auto pFont = cell.querySubObject("Font");
        if (pFont != nullptr)
        {
            pFont->dynamicCall("Bold", fontVal.bBold);
            pFont->setProperty("Size", fontVal.size);
            pFont->setProperty("Name", fontVal.family);
            pFont->deleteLater();
        }
        cell.setProperty("WrapText", fontVal.bWrapText);
        cell.dynamicCall("SetHorizontalAlignment(int)", QTableWidget2Excel::ExcelFont::GetAlignValue(fontVal.hAlign));
        cell.dynamicCall("SetVerticalAlignment(int)",   QTableWidget2Excel::ExcelFont::GetAlignValue(fontVal.vAlign));
    }
#endif
}

bool QTableWidget2Excel::SetCellData(QAxObject& sheet, const CellData& cellData)
{
#ifdef WIN32
    auto pCell = cellData.index.getCell(sheet);
    if (pCell == nullptr)
        return false;
    // 设置导出Cell类型为文本类型
    SetCellData(*pCell, cellData.value, cellData.font);
    pCell->deleteLater();
    return true;
#else
    return false;
#endif
}

bool QTableWidget2Excel::ExcelDrawImage(QAxObject& sheet
    , const WD::WDImage& image
    , const std::array<double, 2>& index
    , const std::optional<std::array<double, 2>>& size)
{
    QImage qImg;
    auto* dataPtr = image.dataPtr();
    const auto dataSize = image.dataSize();
    if (dataPtr == nullptr || dataSize == 0)
        return false;
    switch (image.format())
    {
    case WD::WDImage::Format::Format_Invalid:
        qImg = QImage(dataPtr, image.width(), image.height(), QImage::Format::Format_Invalid);
        break;
    case WD::WDImage::Format::Format_A:
    case WD::WDImage::Format::Format_R:
    case WD::WDImage::Format::Format_RG:
        break;
    case WD::WDImage::Format::Format_RGB:
        qImg = QImage(dataPtr, image.width(), image.height(), QImage::Format::Format_RGB888);
        break;
    case WD::WDImage::Format::Format_RGBA:
        qImg = QImage(dataPtr, image.width(), image.height(), QImage::Format::Format_RGBA8888);
        break;
    case WD::WDImage::Format::Format_RGB565:
    case WD::WDImage::Format::Format_RGBA4444:
    case WD::WDImage::Format::Format_Depth16:
    case WD::WDImage::Format::Format_Depth24:
    case WD::WDImage::Format::Format_Depth32:
    case WD::WDImage::Format::Format_DXT1:
    case WD::WDImage::Format::Format_DXT3:
    case WD::WDImage::Format::Format_DXT5:
    case WD::WDImage::Format::Format_Stream:
    default:
        {
            assert(false && "暂不支持的图片格式!");
            return false;
        }
        break;
    };
    QPixmap pixmap = QPixmap::fromImage(qImg);
    QTemporaryFile tempFile;
    tempFile.setAutoRemove(true);
    if (!tempFile.open())
        return false;
    pixmap.save(tempFile.fileName(), "PNG");
    tempFile.close();
    auto tempPath = QDir::toNativeSeparators(tempFile.fileName());
    double width = image.width();
    double height = image.height();
    if (size)
    {
        width = size.value()[0];
        height = size.value()[1];
    }
    return ExcelDrawImage(sheet, tempPath, index, {width, height});
}
bool QTableWidget2Excel::ExcelDrawImage(QAxObject& sheet
    , const QString& filePath
    , const std::array<double, 2>& index
    , const std::array<double, 2>& size)
{
#ifdef WIN32
    QAxObjectSPtr pShape(sheet.querySubObject("Shapes"));
    if (pShape == nullptr)
        return false;

    QAxObjectSPtr pPicture(pShape->querySubObject("AddPicture(const QString&, bool, bool, double, double, double, double)"
        // 文件路径
        , filePath
        // 嵌入图片(非链接)
        , false
        // 随文档保存
        , true
        // 坐标
        , index[0]
        , index[1]
        // 图片大小
        , size[0]
        , size[1]));
    if (pPicture == nullptr)
        return false;
    pPicture->setProperty("Left",   index[0]);
    pPicture->setProperty("Top",    index[1]);
    pPicture->setProperty("Width",  size[0]);
    pPicture->setProperty("Height", size[1]);
    return true;
#else
    return false;
#endif
}

bool QTableWidget2Excel::DrawTextByIndex(QAxObject& sheet
    , const QString& text
    , const std::array<double, 2>& index
    , const std::array<double, 2>& size
    , const ExcelFont& font)
{
#ifdef WIN32
    QAxObjectSPtr pShape(sheet.querySubObject("Shapes"));
    if (pShape == nullptr)
        return false;
    QAxObjectSPtr pTextBox(pShape->querySubObject("AddTextbox(OleFormatType, float, float, float, float)"
        // 文本框类: msoTextOrientationHorizontal(水平方向)
        , 1
        // 坐标
        , index[0]
        , index[1]
        // 图片大小
        , size[0]
        , size[1]));
    if (pTextBox == nullptr)
        return false;
    // 禁用自动缩放
    pTextBox->setProperty("AutoSize", 0);
    pTextBox->setProperty("Left",   index[0]);
    pTextBox->setProperty("Top",    index[1]);
    pTextBox->setProperty("Width",  size[0]);
    pTextBox->setProperty("Height", size[1]);

    QAxObjectSPtr pFrame(pTextBox->querySubObject("TextFrame"));
    if (pFrame == nullptr)
        return false;

    QAxObjectSPtr pCharacters(pFrame->querySubObject("Characters()"));
    if (pCharacters == nullptr)
        return false;

    pCharacters->setProperty("Text", text);
    QAxObjectSPtr pFont(pCharacters->querySubObject("Font"));
    if (pFont != nullptr)
    {
        pFont->setProperty("Name", font.family);
        pFont->setProperty("Size", font.size);
        pFont->setProperty("Bold", font.bBold);
    }

    QAxObjectSPtr pFill(pTextBox->querySubObject("Fill"));
    if (pFill != nullptr)
    {
        pFill->dynamicCall("SetVisible(bool)", false);
    }

    QAxObjectSPtr pLine(pTextBox->querySubObject("Line"));
    if (pLine != nullptr)
    {
        pLine->dynamicCall("SetVisible(bool)", false);
    }
    pFrame->setProperty("MarginTop",    0.0);
    pFrame->setProperty("MarginRight",  0.0);
    pFrame->setProperty("MarginBottom", 0.0);
    pFrame->setProperty("MarginLeft",   0.0);

   // pFrame->setProperty("Left",     pTextBox->property("Left").toDouble());
   // pFrame->setProperty("Top",      pTextBox->property("Top").toDouble());
   // pFrame->setProperty("Width",    pTextBox->property("Width").toDouble());
   // pFrame->setProperty("Height",   pTextBox->property("Height").toDouble());

    pFrame->dynamicCall("SetHorizontalAlignment(int)", QTableWidget2Excel::ExcelFont::GetAlignValue(font.hAlign));
    pFrame->dynamicCall("SetVerticalAlignment(int)",   QTableWidget2Excel::ExcelFont::GetAlignValue(font.vAlign));
    return true;
#else
    return false;
#endif
}

void QTableWidget2Excel::SetRangeLindWidth(QAxObject& obj, int innerWidth, int outerWidth)
{
#ifdef WIN32
    QAxObjectSPtr borDers(obj.querySubObject("Borders"));
    if (borDers == nullptr)
        return ;
    // 需要被设置的线 7:左边框线 8:上边框线 9:下边框线 10:右边框线 11:内部垂直线 12:内部水平线
    constexpr const int outerL  = 7;
    constexpr const int outerT  = 8;
    constexpr const int outerD  = 9;
    constexpr const int outerR  = 10;
    constexpr const int innerH  = 11;
    constexpr const int innerV  = 12;

    std::array<int, 2> innerLines = {innerH, innerV};
    for (auto& each : innerLines)
    {
        QAxObjectSPtr border(borDers->querySubObject("Item(Int)", each));
        if (border != nullptr)
            border->setProperty("Weight", innerWidth);
    }
    std::array<int, 4> outerLines = {outerL, outerT, outerD, outerR};
    for (auto& each : outerLines)
    {
        QAxObjectSPtr border(borDers->querySubObject("Item(Int)", each));
        if (border != nullptr)
            border->setProperty("Weight", outerWidth);
    }
#endif
}

QAxObject* QTableWidget2Excel::CellIndex::getCell(QAxObject& sheet, bool bAutoMerge) const
{
#ifdef WIN32
    if (bRange)
    {
        if (bAutoMerge)
            return QTableWidget2Excel::MergeCells(sheet, start, end);
        else
            return  sheet.querySubObject("Range(const QVariant&)", QVariant(start + ": " + end));
    }
    else
    {
        if (auto pCell = sheet.querySubObject("Cells(int,int)", start); pCell != nullptr)
            return pCell;
        return  sheet.querySubObject("Range(const QVariant&)", start);
    }
#else
    return nullptr;
#endif
}

bool QTableWidget2Excel::SetPrintRange(QAxObject& sheet, const std::array<int, 2>& hwRangeCnt, const std::array<int, 2>& hwPageCnt)
{
#if WIN32
    auto pHBreaks = QAxObjectSPtr(sheet.querySubObject("HPageBreaks"));
    if (pHBreaks != nullptr)
        pHBreaks->dynamicCall("Delete()");

    auto pVBreaks = QAxObjectSPtr(sheet.querySubObject("VPageBreaks"));
    if (pVBreaks != nullptr)
        pVBreaks->dynamicCall("Delete()");

    QAxObjectSPtr pPageSetup(sheet.querySubObject("PageSetup"));
    if (pPageSetup == nullptr)
        return false;

    for (int hIdx = 0; hIdx < hwPageCnt[0]; ++hIdx)
    {
        auto pBreakRow = QAxObjectSPtr(sheet.querySubObject("Rows(int)", (hIdx + 1) * hwRangeCnt[0]));
        pVBreaks->dynamicCall("Add(QVariant)", pBreakRow->asVariant());
    }

    for (int vIdx = 0; vIdx < hwPageCnt[1]; ++vIdx)
    {
        auto pBreakCol = QAxObjectSPtr(sheet.querySubObject("Rows(int)", (vIdx + 1) * hwRangeCnt[1]));
        pHBreaks->dynamicCall("Add(QVariant)", pBreakCol->asVariant());
    }

    auto pPagrSetup = QAxObjectSPtr(sheet.querySubObject("PageSetup"));
    if (pPagrSetup != nullptr)
    {
        pPagrSetup->setProperty("Zoom", 100);
        pPagrSetup->setProperty("FitToPagesTall", false);
        pPagrSetup->setProperty("FitToPagesWide", false);
    }
    return true;
#else
    return false;
#endif
}



void QTableWidget2Excel::SetSheetData(QAxObject& sheet, const ExcelCellDatas& datas)
{
    for (auto& each : datas)
    {
        SetCellData(sheet, each);
    }
}

QTableWidget2Excel::QTableWidget2Excel()
{
    _excel = CreateNewExCelObj(&_flag);
    // 判断是WPS还是Word操作表格
    if (_excel == nullptr)
        WD_WARN("未安装对应软件!");
}

QTableWidget2Excel::~QTableWidget2Excel()
{
    DestoryExCelObj(_excel);
}

QAxObject*  QTableWidget2Excel::createNewWorkBook()
{
#ifdef WIN32
    if (_excel == nullptr)
        return nullptr;
    // 不显示窗体
    _excel->dynamicCall("SetVisible (bool visible)", "false");
    // 不显示警告消息
    _excel->setProperty("DisplayerAlerts", false);
    // 获取工作簿集合
    QAxObject* pWorkBooks = _excel->querySubObject("WorkBooks");
    if (pWorkBooks == nullptr)
        return nullptr;
    pWorkBooks->dynamicCall("Add");
    // 获取当前工作簿
    QAxObject* pWorkBook = _excel->querySubObject("ActiveWorkBook");
    if (pWorkBook == nullptr)
        return nullptr;
    return pWorkBook;
#else
    return nullptr;
#endif
}

bool    QTableWidget2Excel::getExcelData(const std::string& filePath, ExcelData& dataVec)
{
    QString filePathQStr = QString::fromUtf8(filePath.c_str());
    if (filePathQStr.isEmpty())
        return false;
    return this->getExcelData(filePathQStr, dataVec);
}

bool    QTableWidget2Excel::getExcelData(const QString& filePath, ExcelData& dataVec)
{
#ifdef WIN32
    //打开excel进程， 获取工作簿， 工作表， 单元格
    if (_excel == nullptr)
        return false;
    _excel->setProperty("DisPlayAlerts", false);
    QAxObject*          workBooks       =           _excel->querySubObject("WorkBooks");
    if(workBooks == nullptr)
        return false;
    workBooks->dynamicCall("Open(const QString&)", filePath);
    QAxObject*          workBook        =           _excel->querySubObject("ActiveWorkBook");
    if(workBook == nullptr)
        return false;

    QAxObject*          sheets          =           workBook->querySubObject("Sheets");
    if(sheets == nullptr)
        return false;
    QAxObject*          sheet           =           sheets->querySubObject("Item(int)", 1);
    if (sheet == nullptr)
        return false;
    // 获取已经使用的单元格区域， 并得到行列数
    QAxObject*          range           =           sheet->querySubObject("UsedRange");
    if (range == nullptr)
        return false;
    QAxObject*          rows            =           range->querySubObject("Rows");
    if (rows == nullptr)
        return false;
    int                 rowCount        =           rows->dynamicCall("Count").toUInt();
    QAxObject*          columns         =           range->querySubObject("Columns");
    if (columns == nullptr)
        return false;
    int                 colCount        =           columns->dynamicCall("Count").toUInt();

    // 提取单元格内容，存放到二维数组中
    for (int row = 1; row <= rowCount; row++)
    {
        std::vector<std::string> rowData;
        for (int col = 1; col <= colCount; col++)
        {
            QAxObject*  cell            =           range->querySubObject("Cells(int,int)", row, col);
            if (cell == nullptr)
                continue ;
            QVariant    varValue        =           cell->dynamicCall("Value");
            QString     temp            =           varValue.toString();
            std::string strValue        =           temp.toUtf8().data();
            delete cell;
            rowData.push_back(strValue);
        }
        if (!rowData.empty())
        {
            dataVec.push_back(rowData);
        }
        
    }

    // 关闭工作流
    workBook->dynamicCall("Close()");
    if (dataVec.empty())
        return false;
    return true;
#else
    return false;
#endif
}

bool    QTableWidget2Excel::createTableItemByDataVec(ExcelData& srcVec, QTableWidget* destTable)
{
    if (destTable == nullptr)
        return false;
    if (srcVec.empty())
        return false;
    // 第一个Vector装的是表头数据
    auto&           headerVec           =           srcVec.front();
    // 获取excel文件列数
    int             srcCol              =           (int)headerVec.size();
    // 获取当前Table表列数
    int             curCol              =           destTable->columnCount();
    // 添加表头？？？
    if (curCol < srcCol)
        curCol = srcCol;
        
    // 设置列数
    destTable->setColumnCount(curCol);
    // 获取源数据行数
    int             srcRow              =           (int)srcVec.size()-1;
    // 获取当前行数
    int             curRow              =           destTable->rowCount();
    // 添加后的新行数
    int             newRow              =           srcRow + curRow;
    // 设置新行数
    destTable->setRowCount(newRow);
    // 创建新QTableWidgetItem到列表窗口上
    for (int row = curRow; row < newRow; row++)
    {
        // 取数据源数组的下标索引值
        int srcIndex = row - curRow;
        if (srcIndex > srcRow)
            continue ; 
        // 获取行数据
        auto& rowVec = srcVec[srcIndex + 1];
        for (int col = 0; col < curCol; col++)
        {
            if (srcCol < col)
            {
                QTableWidgetItem* emptyItem = new QTableWidgetItem();
                destTable->setItem(row, col, emptyItem);
                continue ;
            }
            // 获取cell格数据
            std::string& cellStr = rowVec[col];
            QTableWidgetItem* newItem = new QTableWidgetItem(QString::fromUtf8(cellStr.c_str()));
            destTable->setItem(row, col, newItem);
        }
    }

    return true;
}

bool    QTableWidget2Excel::exportTableWidgetToExcel(QString& filePath, QTableWidget* srcWidget)
{
#ifdef WIN32
    if (_excel == nullptr)
        return false;
    // 连接excel控件
    HRESULT r = OleInitialize(0);
    if(r != S_OK && r != S_FALSE)
    {
        qWarning("Qt 初始化Ole失败(error %x)", (unsigned int)r);
    }
    //CoInitializeEx(NULL, COINIT_MULTITHREADED);
    // 获取工作簿集合
    // 获取当前工作簿
    QAxObject*  workBook    =   this->createNewWorkBook();
    if (workBook == nullptr)
        return false;
    // 获取工作表1
    QAxObject*  workSheet   =   GetWorkSheetByIndex(*workBook);
    if (workSheet == nullptr)
        return false;

    int rowCount    =   srcWidget->rowCount();
    int colCount    =   srcWidget->columnCount();
    // 写入表头数据
    for (int i = 0; i < colCount; i++)
    {
        auto    topItem =   srcWidget->horizontalHeaderItem(i);
        QString text    =   topItem->text();
        this->setDataToExcel(1, i+1, text, workSheet);
    }
    // 写入item数据
    for (int row = 0; row < rowCount; row++)
    {
        for (int col = 0; col < colCount; col++)
        {
            auto    pItem   =   srcWidget->item(row, col);
            if (pItem == nullptr)
                continue;
            auto    text    =   pItem->text();
            bool    bOk     =   false;
            // 如果是double类型的数据则以double类型导出
            auto    value   =   text.toDouble(&bOk);
            if (bOk)
                this->setDataToExcel(row+2, col+1, value, workSheet);
            else
                this->setDataToExcel(row+2, col+1, text, workSheet); 
        }
    }

    SaveWorkBook(filePath, *workBook);
    // 关闭工作簿
    workBook->dynamicCall("Close");
    return true;
#else
    return false;
#endif 
}

bool    QTableWidget2Excel::exportTableWidgetToExcel(const std::string& filePath, QTableWidget* srcWidget)
{
    QString filePathQStr = QString::fromUtf8(filePath.c_str());
    if (filePathQStr.isEmpty())
        return false;
    return this->exportTableWidgetToExcel(filePathQStr, srcWidget);
}

bool    QTableWidget2Excel::exportSheetDataVecToExcel(const QString& filePath, const Sheets& sheets)
{
    if (sheets.empty())
        return false;

#ifdef WIN32
    if (_excel == nullptr)
        return false;

    // 连接excel控件
    HRESULT r = OleInitialize(0);
    if(r != S_OK && r != S_FALSE)
    {
        qWarning("Qt 初始化Ole失败(error %x)", (unsigned int)r);
    }
    //CoInitializeEx(NULL, COINIT_MULTITHREADED);

    QAxObject* pWorkBook = this->createNewWorkBook();
    if (pWorkBook == nullptr)
    {
        OleUninitialize();
        return false;
    }
    // 需要的工作表集合个数
    std::vector<QAxObject*> sheetObjects;
    sheetObjects.reserve(sheets.size());
    for(int i = 0; i < sheets.size(); ++i)
    {
        QAxObject* pObject = GetWorkSheetByIndex(*pWorkBook, i + 1);
        if (pObject == nullptr)
        {
            assert(pObject != nullptr);
            continue;
        }
        sheetObjects.push_back(pObject);
    }
    if (sheetObjects.size() != sheets.size())
    {
        OleUninitialize();
        return false;
    }
    // 这里由于工作表是向前插入，因此这里需要将工作表对象的数组翻转，以保证顺序正确
    std::reverse(sheetObjects.begin(), sheetObjects.end());

    for (size_t i = 0; i < sheets.size(); ++i)
    {
        const auto& sheetData   = sheets[i];
        assert(sheetData.data.size() > 2);
        QAxObject* pSheetObject = sheetObjects[i];
        if(pSheetObject == nullptr)
        {
            assert(pSheetObject != nullptr);
            continue;
        }
        //设置第sheet名称
        pSheetObject->setProperty("Name", sheetData.name.c_str());
        
        // 写入表头
        QString titleData = QString::fromUtf8(sheetData.data[0][0].c_str());
        setDataToExcel(1, 1, titleData, pSheetObject);
        // 写入数据
        auto dataRange1     = pSheetObject->querySubObject("Range(QString)", "A2");
        if(dataRange1 == nullptr)
        {
            assert(dataRange1 != nullptr);
            continue;
        }
        int row             = static_cast<int>(sheetData.data.size() - 1);
        int col             = static_cast<int>(sheetData.data[1].size());
        auto dataRange2     = dataRange1->querySubObject("Resize(int, int)", row, col);
        if (dataRange2 == nullptr)
        {
            assert(dataRange2 != nullptr);
            continue;
        }
        auto datas  = FuncDataConvert(sheetData, 1);
        bool ret1    = dataRange2->setProperty("Value2", datas);
        WDUnused(ret1);
        assert(ret1);

        //设置自动列宽
        auto cells = dataRange2->querySubObject("Columns");
        if (cells == nullptr)
        {
            assert(cells != nullptr);
            continue;
        }        
        cells->dynamicCall("AutoFit");
    }
    SaveWorkBook(filePath, *pWorkBook);
    // 关闭工作簿
    pWorkBook->dynamicCall("Close");

    OleUninitialize();
    return true;
#else
    return false;
#endif 
}

bool    QTableWidget2Excel::exportExcelByTwoArray(const QString& filePath, const ExcelData& data)
{
#ifdef WIN32
    if (_excel == nullptr)
        return false;

    // 连接excel控件
    HRESULT r = OleInitialize(0);
    if (r != S_OK && r != S_FALSE)
    {
        qWarning("Qt 初始化Ole失败(error %x)", (unsigned int)r);
    }
    //CoInitializeEx(NULL, COINIT_MULTITHREADED);
    QAxObject* pWorkBook = this->createNewWorkBook();
    if (pWorkBook == nullptr)
    {
        OleUninitialize();
        return false;
    }
    // 获取工作簿的工作表1
    QAxObject* workSheet = GetWorkSheetByIndex(*pWorkBook, 1);
    if (workSheet == nullptr)
    {
        OleUninitialize();
        return false;
    }
    //通过Range项sheet中写入数据
            // 写入数据
    auto dataRange1 = workSheet->querySubObject("Range(QString)", "A1");
    if (dataRange1 == nullptr)
    {
        assert(dataRange1 != nullptr);
        return false;
    }
    int row = static_cast<int>(data.size());
    int col = static_cast<int>(data[0].size());
    auto dataRange2 = dataRange1->querySubObject("Resize(int, int)", row, col);
    if (dataRange2 == nullptr)
    {
        assert(dataRange2 != nullptr);
        return false;
    }
    auto datas = FuncDataConvert(data);
    bool ret1 = dataRange2->setProperty("Value2", datas);
    WDUnused(ret1);
    assert(ret1);

    //设置自动列宽
    auto cells = dataRange2->querySubObject("Columns");
    if (cells == nullptr)
    {
        assert(cells != nullptr);
        return false;
    }
    cells->dynamicCall("AutoFit");

    SaveWorkBook(filePath, *pWorkBook);
    // 关闭工作簿
    pWorkBook->dynamicCall("Close");

    OleUninitialize();
    return true;
#else
    return false;
#endif 
}

bool    QTableWidget2Excel::setDataToExcel(int row, int col, QString& data, QAxObject* sheet)
{
#ifdef WIN32
    if (sheet == nullptr)
        return false;
    // 获取excel单元格对象
    QAxObject* cell = GetCell(*sheet, row, col);
    if (cell == nullptr)
        return false;
    // 设置导出Cell类型为文本类型
    SetCellData(*cell, data);
    delete cell;
    return true;
#else
    return false;
#endif
}
bool    QTableWidget2Excel::setDataToExcel(int row, int col, double& data, QAxObject* sheet)
{
#ifdef WIN32
    if (sheet == nullptr)
        return false;
    // 获取excel单元格对象
    QAxObject* cell = GetCell(*sheet, row, col);
    if (cell == nullptr)
        return false;
    // 设置导出Cell类型为文本类型
    SetCellData(*cell, data);
    delete cell;
    return true;
#else
    return false;
#endif
}
