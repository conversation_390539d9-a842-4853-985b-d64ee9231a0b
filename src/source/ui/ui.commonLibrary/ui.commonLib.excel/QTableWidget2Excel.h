#include <qtablewidget.h>
#include "core/common/WDImage.h"

class QAxObject;
class QTableWidget2Excel
{
public:
    enum ApplicationFlag
    {
        // 使用Word操作表格
        Word = 0,
        // 使用Wps操作表格
        Wps
    };
    using   ExcelData   =   std::vector<std::vector<std::string>>;
    struct Sheet
    {
        std::string name;
        ExcelData   data;
    };
    using Sheets = std::vector<Sheet>;

    using QAxObjectSPtr = std::shared_ptr<QAxObject>;
public:
    /**
     * @brief 新建一个表格对象
     * @return 
    */
    static QAxObject*   CreateNewExCelObj(ApplicationFlag* pFlag = nullptr);

    static void         DestoryExCelObj(QAxObject* pObj);
    /**
     * @brief 把表格数据转换为QVariant
     * @param data 
     * @param startRow 
     * @return 
    */
    static QVariantList FuncDataConvert(const ExcelData&    data,   int startRow = 0);
    static QVariantList FuncDataConvert(const Sheet&        sheet,  int startRow = 0);
    /**
    * @brief 新建工作簿中指定下标的工作表
    * @param index 下标从1开始
    * @param bAutoAddSheet 当指定的下标大于已有的工作表数量时是否创建新的工作表直至获取到指定下表的工作表
    */
    static QAxObject*   GetWorkSheetByIndex(QAxObject& workBook, int index = 1, bool bAutoAddSheet = true);
    /**
     * @brief 保存表格
     * @param filePath 
     * @param workBook 
    */
    static void     SaveWorkBook(const QString& filePath, QAxObject& workBook);
    /**
    * @brief 合并单元格
    * @param obj
    * @param start 
    * @param end 
    * @return 
    */
    static QAxObject*   MergeCells(QAxObject& obj, const QString& start, const QString& end);
    /**
     * @brief 获取单元格
    */
    QAxObject* GetCell(QAxObject& obj, const QString& index);
    QAxObject* GetCell(QAxObject& obj, int row, int col);
    /**
     * @brief 设置表格指定行的行高
     * @param obj 
     * @param index 
     * @param height 
     * @return 
    */
    static bool SetExcelRowHeight(QAxObject& obj, int index, double height);
    /**
     * @brief 设置表格指定列的列宽
     * @param obj 
     * @param index 
     * @param width 
     * @return 
    */
    static bool SetExcelColWidth(QAxObject& obj, int index, double width);
public:
    struct CellIndex
    {
    private:
        bool bRange = false;
        QString start;
        QString end;
    public:
        CellIndex()
        {
        }
        CellIndex(QString index) : start(index)
        {
            bRange = false;
        }
        CellIndex(QString startIdx, QString endIdx) : start(startIdx), end(endIdx)
        {
            bRange = true;
        }
    public:
        inline void setIndex(QString index)
        {
            start = index;
            bRange = false;
        }
        inline void setIndex(QString startIdx, QString endIdx)
        {
            this->start = startIdx;
            this->end = endIdx;
            bRange = true;
        }
        /**
         * @brief bAutoMerge 如果当前下标为范围下标 是否自动合并单元格
        */
        QAxObject* getCell(QAxObject& sheet, bool bAutoMerge = true) const;
    };

    struct ExcelFont
    {
        // 字体大小(磅)
        double size;
        // 字体族
        QString family;
        // 是否是粗体
        bool bBold = false;
        // 是否自动换行
        bool bWrapText = false;
        // 对齐方式
        enum class HAlign
        {
            HA_Left,
            HA_Center,
            HA_Right
        };
        enum class VAlign
        {
            VA_Top,
            VA_Center,
            VA_Bottom
        };
        HAlign hAlign = HAlign::HA_Center;
        VAlign vAlign = VAlign::VA_Center;
    public:
        static inline const int GetAlignValue(HAlign align)
        {
            switch (align)
            {
            case QTableWidget2Excel::ExcelFont::HAlign::HA_Left:
                return -4131;
            case QTableWidget2Excel::ExcelFont::HAlign::HA_Center:
                return -4108;
            case QTableWidget2Excel::ExcelFont::HAlign::HA_Right:
                return -4152;
            default:
                break;
            }
            return -4131;
        }
        static inline const int GetAlignValue(VAlign align)
        {
            switch (align)
            {
            case QTableWidget2Excel::ExcelFont::VAlign::VA_Top:
                return -4160;
            case QTableWidget2Excel::ExcelFont::VAlign::VA_Center:
                return -4108;
            case QTableWidget2Excel::ExcelFont::VAlign::VA_Bottom:
                return -4107;
            default:
                break;
            }
            return -4160;
        }
    public:
        ExcelFont(const QString& family = QString::fromUtf8("黑体"), double size = 10.0) : family(family), size(size)
        {
        }
    };
    struct CellData
    {
        CellIndex index;
        QVariant value;
        ExcelFont font;

        double width = 0.0;
        double height = 0.0;
    };
    using  ExcelCellDatas = std::vector<CellData>;

    // 转换 -> 磅转为字符
    inline static double CvtPointToChar(double val)
    {
        return val / 6.23;
    };
    // 转换 -> 字符转为磅
    inline static double CvtCharToPoint(double val)
    {
        return val * 6.23;
    };
    // 转换 -> mm转为磅
    inline static double CvtMMToPoint(double val)
    {
        return val * 2.835;
    };

    /**
     * @brief 设置单元格内容
    */
    static void SetCellData(QAxObject& cell, const QVariant& data, const std::optional<ExcelFont>& font = std::nullopt);
    static bool SetCellData(QAxObject& sheet, const CellData& cellData);
    /**
     * @brief 在工作表的指定位置绘制图片
    */
    static bool ExcelDrawImage(QAxObject& sheet
        , const WD::WDImage& image
        , const std::array<double, 2>& index
        , const std::optional<std::array<double, 2>>& size = std::nullopt);
    static bool ExcelDrawImage(QAxObject& sheet
        , const QString& filePath
        , const std::array<double, 2>& index
        , const std::array<double, 2>& size);

    /**
     * @brief 在工作表指定坐标(物理坐标,不是单元格下标)绘制文字
     * @param sheet 
     * @param filePath 
     * @param index 
     * @param size 
     * @return 
    */
    static bool DrawTextByIndex(QAxObject& sheet
        , const QString& text
        , const std::array<double, 2>& index
        , const std::array<double, 2>& size
        , const ExcelFont& font = ExcelFont());
    /**
     * @brief 设置给定范围的边框线宽
    */
    static void SetRangeLindWidth(QAxObject& obj, int innerWidth, int outerWidth);

    /**
    * @brief 
    */
    static bool SetPrintRange(QAxObject& sheet, const std::array<int, 2>& hwRangeCnt, const std::array<int, 2>& hwPageCnt = {1, 1});

    static void SetSheetData(QAxObject& sheet, const ExcelCellDatas& datas);
public:
    QTableWidget2Excel();
    ~QTableWidget2Excel();
public:
    /**
    * @brief 新建工作簿
    */
    QAxObject*   createNewWorkBook();

    /**
    * @brief 导入excel   生成二维数组
    * @param dataVec    写入数据的二维数组
    */
    bool    getExcelData(const std::string& filePath, ExcelData& dataVec);

    bool    getExcelData(const QString& filePath, ExcelData& dataVec);

    /**
    * @brief 根据二维数组新建item到QTableWidget对象上
    * @param srcVec     数据源二维数组
    * @param destTable  目标TableWidget
    */
    bool    createTableItemByDataVec(ExcelData& srcVec, QTableWidget* destTable);

    /**
    * @brief 导出QTableWidget数据到excel文件
    * @param srcWidget 数据源QTableWidget
    */
    bool    exportTableWidgetToExcel(QString& filePath, QTableWidget* srcWidget);

    bool    exportTableWidgetToExcel(const std::string& filePath, QTableWidget* srcWidget);
    /**
     * @brief 将sheets数据导出到excel
     * @param filePath excel的全路径
     * @param sheetNameVec sheet的名称Vec
     * @param sheetDataVec sheet数据的Vec
    */
    bool exportSheetDataVecToExcel(const QString& filePath, const Sheets& sheets);
    /**
     * @brief 导出1张默认名称的sheet xlsx
     * @param filePath 路径+文件名
     * @param data sheet中的二维数据
     * @return 导出是否成功
    */
    bool exportExcelByTwoArray(const QString& filePath, const ExcelData& data);
private:
    /**
    * @brief 将QTableWidgetItem信息写入Excel表格
    * @param 函数参数名称 函数参数简要说明
    * @return 函数返回值简要说明
    */
    bool    setDataToExcel(int row, int col, QString& data, QAxObject* sheet);
    /**
    * @brief 将QTableWidgetItem信息写入Excel表格
    * @param 函数参数名称 函数参数简要说明
    * @return 函数返回值简要说明
    */
    bool    setDataToExcel(int row, int col, double& data, QAxObject* sheet);
private:
    QAxObject*          _excel;

    ApplicationFlag     _flag;
};

