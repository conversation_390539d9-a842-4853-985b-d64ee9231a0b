#include "UiCommon.h"
#include "core/WDTranslate.h"

#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMPermissionMgr.h"
#include "core/businessModule/WDBMClaimMgr.h"

#include "core/log/WDLoggerPort.h"
#include "core/WDCore.h"
#include "core/message/WDMessage.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/businessModule/WDBDBase.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"

#include <QStack>


WD_NAMESPACE_BEGIN;

// 删除节点去重，并且查找列表中是否同时存在节点以及其祖先节点，如果存在，只保留祖先节点
WDNode::Nodes DeleteNodesRemoveDuplicates(const WDNode::Nodes& nodes)
{
    std::set<WDNode::SharedPtr> fNodes;
    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        fNodes.insert(pNode);
    }

    WDNode::Nodes rNodes;
    rNodes.reserve(fNodes.size());
    for (auto pNode : fNodes)
    {
        if (pNode == nullptr)
            continue;

        WDNode::SharedPtr pTNode = pNode->parent();
        while (pTNode != nullptr)
        {
            auto fItr = fNodes.find(pTNode);
            if (fItr != fNodes.end())
                break;
            pTNode = pTNode->parent();
        }
        // 说明没有找到祖先节点，执行销毁
        rNodes.push_back(pNode);
    }

    return rNodes;
}


void DeleteGivenNodes(const WD::WDNode::Nodes& nodes, WD::WDCore& app, const std::string& cxtName, bool bTipDelete)
{
    if (nodes.empty())
    {
        // 提示选择要删除的节点
        WD_WARN_T(cxtName, "deleteNode");
        return;
    }

    if (bTipDelete && WD_QUESTION_T(cxtName, "isDel") != 0)
        return;

    // 对要删除的节点去重
    auto rNodes = DeleteNodesRemoveDuplicates(nodes);

    auto bm = app.currentBM();
    if (bm == nullptr)
        return;
    // 校验权限
    for (const auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        //  权限校验
        if (const WDNode::SharedPtr pParent = pNode->parent(); pParent != nullptr && !bm->permissionMgr().check(*pParent))
        {
            WD_WARN_T(cxtName, "You cannot operate the current node!");
            return;
        }
    }

    // 申领节点删除
    if (!bm->claimMgr().checkDelete(rNodes))
        return;

    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;

        // 处理根节点的情况
        // 删除根节点下面的所有子节点
        if (pNode->parent() == nullptr)
        {
            // 删除根节点时，根节点不允许删除，弹窗询问是否清空该根节点的项目
            if (WD_QUESTION_T(cxtName, "Is Clear This Project") != 0)
                return;
            DeleteGivenNodes(pNode->children(), app, cxtName, false);
            return;
        }
    }

    // 从场景移除
    auto cmdSceneRemove = WD::WDBMBase::MakeSceneRemoveCommand(nodes);
    // 删除节点
    auto cmdDestroy     = WD::WDBMBase::MakeDestroyCommand(nodes);
    // 合并为一个命令并添加到栈中
    if (cmdSceneRemove != nullptr && cmdDestroy != nullptr)
    {
        // 节点列表触发更新,删除操作重做和撤销后将需要触发更新的部分调用更新

        // 在删除操作执行前,先获取所有的需要触发更新的节点的父节点
        WD::WDNode::Nodes parents;
        parents.reserve(nodes.size());
        for (auto& pNode : nodes)
        {
            if (pNode == nullptr)
                continue;
            auto pDesc = pNode->getTypeDesc();
            if (pDesc == nullptr)
                continue;
            if (pDesc->flags().hasFlag(WD::WDBMTypeDesc::Flag::F_TriggerUpdate) && pNode->parent() != nullptr)
                parents.emplace_back(pNode->parent());
        }
        // 节点去重
        auto updateNodes = DeleteNodesRemoveDuplicates(parents);
        auto nodesTriggerUpdate = [updateNodes] (const WD::WDUndoCommand&)
        {
            for (auto& pNode : updateNodes)
            {
                if (pNode != nullptr)
                    pNode->triggerUpdate(true);
            }
        };
        cmdDestroy->setNoticeAfterRedo(nodesTriggerUpdate);
        cmdDestroy->setNoticeAfterUndo(nodesTriggerUpdate);

        app.undoStack().beginMarco("DeleteGivenNodes");
        app.undoStack().push(cmdSceneRemove);
        app.undoStack().push(cmdDestroy);
        app.undoStack().endMarco();
    }
}

WD::WDNode::SharedPtr GetNextNode(WD::WDNode::SharedPtr pParent, WD::WDNode::SharedPtr pNode)
{
    if (pNode ==nullptr || pParent == nullptr)
        return nullptr;

    // 节点树当前节点与新建节点的父节点相同
    if(pParent == pNode)
    {
        // 父节点无子节点，则新建节点的后1个节点为空
        if(pParent->childCount() == 0)
            return nullptr;
        // 父节点有子节点，则新建节点的后1个节点为此时父节点下的第1个节点
        return pParent->childAt(0);
    }

    auto pTmp = pNode;
    WD::WDNode::SharedPtr pNext = nullptr;
    // pNext：节点树当前节点祖宗节点中父节点为pParent的节点
    while (pTmp)
    {
        auto pTmpParent = pTmp->parent();
        if (pTmpParent == pParent)
        {
            pNext = pTmp;
            break;
        }
        else
        {
            pTmp = pTmpParent;
        }
    }
    // 获取pNext 节点的后1个节点
    if (pNext != nullptr)
        pNext = pNext->nextBrother();

    return pNext;
}

DVec3               GetNozzPos(WDNode& node, WDWRTType wrtType)
{
    if (!node.isType("NOZZ"))
        return DVec3();

    // 获取连接口点, 管嘴连接点number固定为1
    auto pPt = node.keyPoint(1);
    if (pPt == nullptr)
        return DVec3();

    // 根据坐标系类型返回坐标
    DVec3 pos = pPt->position;
    switch (wrtType)
    {
    case WD::WRT_World:
    {
        pos = pPt->transformedPosition(node.globalTransform());
    }
    break;
    case WD::WRT_Parent:
    {
        pos = pPt->transformedPosition(node.localTransform());
    }
    break;
    case WD::WRT_Self:
        break;
    default:
        break;
    }

    return pos;
}
DVec3               GetComsHPos(WDNode& node, WDWRTType wrtType)
{
    // 获取入口点
    auto pPt = node.keyPoint(node.getAttribute("Arrive").toInt());
    if (pPt == nullptr)
        return DVec3();

    // 根据坐标系类型返回坐标
    DVec3 pos = pPt->position;
    switch (wrtType)
    {
    case WD::WRT_World:
        {
            pos = pPt->transformedPosition(node.globalTransform());
        }
        break;
    case WD::WRT_Parent:
        {
            pos = pPt->transformedPosition(node.localTransform());
        }
        break;
    case WD::WRT_Self:
        break;
    default:
        break;
    }

    return pos;
}
DVec3               GetBranHPos(WDNode& node, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
        return node.getAttribute("Hposition WRT World").toDVec3();
        break;
    case WD::WRT_Parent:
        return node.getAttribute("Hposition WRT Owner").toDVec3();
        break;
    default:
        assert(false);
        break;
    }
    return DVec3::Zero();
}
void                SetBranHPos(WDNode& node, const DVec3& pos, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
    {
        node.setAttribute("Hposition WRT World", pos);
        node.triggerUpdate(true);
    }
    break;
    case WD::WRT_Parent:
    {
        node.setAttribute("Hposition WRT Owner", pos);
        node.triggerUpdate(true);
    }
    break;
    default:
        assert(false);
        break;
    }
}
DVec3               GetComsTPos(WDNode& node, WDWRTType wrtType)
{
    // 获取出口点
    auto pPt = node.keyPoint(node.getAttribute("Leave").toInt());
    if (pPt == nullptr)
        return DVec3();

    // 根据坐标系类型返回坐标
    DVec3 pos = pPt->position;
    switch (wrtType)
    {
    case WD::WRT_World:
        {
            pos = pPt->transformedPosition(node.globalTransform());
        }
        break;
    case WD::WRT_Parent:
        {
            pos = pPt->transformedPosition(node.localTransform());
        }
        break;
    case WD::WRT_Self:
        break;
    default:
        break;
    }

    return pos;
}
DVec3               GetBranTPos(WDNode& node, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
        return node.getAttribute("Tposition WRT World").toDVec3();
        break;
    case WD::WRT_Parent:
        return node.getAttribute("Tposition WRT Owner").toDVec3();
        break;
    default:
        assert(false);
        break;
    }
    return DVec3::Zero();
}
void                SetBranTPos(WDNode& node, const DVec3& pos, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
    {
        node.setAttribute("Tposition WRT World", pos);
        node.triggerUpdate(true);
    }
    break;
    case WD::WRT_Parent:
    {
        node.setAttribute("Tposition WRT Owner", pos);
        node.triggerUpdate(true);
    }
    break;
    default:
        assert(false);
        break;
    }
}

DVec3               GetHPos(WDNode& node, WDWRTType wrtType)
{
    DVec3 result;

    if (node.isType("BRAN"))
        result = GetBranHPos(node, wrtType);
    else if (IsPipeCom(node))
        result = GetComsHPos(node, wrtType);
    else if (node.isType("NOZZ"))
        result = GetNozzPos(node, wrtType);

    return result;
}
void                SetHPos(WDNode& node, const DVec3& pos, WDWRTType wrtType)
{
    if (node.isType("BRAN"))
        SetBranHPos(node, pos, wrtType);
    else if (IsPipeCom(node))
        return ;
    else if (node.isType("NOZZ"))
        return ;
}
DVec3               GetTPos(WDNode& node, WDWRTType wrtType)
{
    DVec3 result;

    if (node.isType("BRAN"))
        result = GetBranTPos(node, wrtType);
    else if (IsPipeCom(node))
        result = GetComsTPos(node, wrtType);
    else if (node.isType("NOZZ"))
        result = GetNozzPos(node, wrtType);

    return result;
}
void                SetTPos(WDNode& node, const DVec3& pos, WDWRTType wrtType)
{
    if (node.isType("BRAN"))
        SetBranTPos(node, pos, wrtType);
    else if (IsPipeCom(node))
        return ;    
    else if (node.isType("NOZZ"))
        return ;
}

DVec3               GetNozzDir(WDNode& node, WDWRTType wrtType)
{
    if (!node.isType("NOZZ"))
        return DVec3();

    // 获取连接口点, 管嘴连接点number固定为1
    auto pPt = node.keyPoint(1);
    if (pPt == nullptr)
        return DVec3();

    // 根据坐标系类型返回坐标
    DVec3 dir = pPt->direction;
    switch (wrtType)
    {
    case WD::WRT_World:
    {
        dir = pPt->transformedDirection(node.globalTransform());
    }
    break;
    case WD::WRT_Parent:
    {
        dir = pPt->transformedDirection(node.localTransform());
    }
    break;
    case WD::WRT_Self:
        break;
    default:
        break;
    }

    return DVec3::Normalize(dir);
}
DVec3               GetComsHDir(WDNode& node, WDWRTType wrtType)
{
    // 获取入口点
    auto pPt = node.keyPoint(node.getAttribute("Arrive").toInt());
    if (pPt == nullptr)
        return DVec3();

    // 根据坐标系类型返回坐标
    DVec3 dir = pPt->direction;
    switch (wrtType)
    {
    case WD::WRT_World:
        {
            dir = pPt->transformedDirection(node.globalTransform());
        }
        break;
    case WD::WRT_Parent:
        {
            dir = pPt->transformedDirection(node.localTransform());
        }
        break;
    case WD::WRT_Self:
        break;
    default:
        break;
    }

    return DVec3::Normalize(dir);
}
DVec3               GetBranHDir(WDNode& node, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
        return node.getAttribute("Hdirection WRT World").toDVec3();
        break;
    case WD::WRT_Parent:
        return node.getAttribute("Hdirection WRT Owner").toDVec3();
        break;
    default:
        assert(false);
        break;
    }
    return DVec3::AxisZ();
}
void                SetBranHDir(WDNode& node, const DVec3& dir, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
    {
        node.setAttribute("Hdirection WRT World", dir);
        node.triggerUpdate(true);
    }
    break;
    case WD::WRT_Parent:
    {
        node.setAttribute("Hdirection WRT Owner", dir);
        node.triggerUpdate(true);
    }
    break;
    default:
        assert(false);
        break;
    }
}
DVec3               GetComsTDir(WDNode& node, WDWRTType wrtType)
{
    // 获取出口点
    auto pPt = node.keyPoint(node.getAttribute("Leave").toInt());
    if (pPt == nullptr)
        return DVec3();

    // 根据坐标系类型返回坐标
    DVec3 dir = pPt->direction;
    switch (wrtType)
    {
    case WD::WRT_World:
        {
            dir = pPt->transformedDirection(node.globalTransform());
        }
        break;
    case WD::WRT_Parent:
        {
            dir = pPt->transformedDirection(node.localTransform());
        }
        break;
    case WD::WRT_Self:
        break;
    default:
        break;
    }

    return DVec3::Normalize(dir);
}
DVec3               GetBranTDir(WDNode& node, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
        return node.getAttribute("Tdirection WRT World").toDVec3();
        break;
    case WD::WRT_Parent:
        return node.getAttribute("Tdirection WRT Owner").toDVec3();
        break;
    default:
        assert(false);
        break;
    }
    return DVec3::AxisZ();
}
void                SetBranTDir(WDNode& node, const DVec3& dir, WDWRTType wrtType)
{
    switch (wrtType)
    {
    case WD::WRT_World:
    {
        node.setAttribute("Tdirection WRT World", dir);
        node.triggerUpdate(true);
    }
    break;
    case WD::WRT_Parent:
    {
        node.setAttribute("Tdirection WRT Owner", dir);
        node.triggerUpdate(true);
    }
    break;
    default:
        assert(false);
        break;
    }
}

DVec3               GetHDir(WDNode& node, WDWRTType wrtType)
{
    DVec3 result;
    if (node.isType("BRAN"))
        result = GetBranHDir(node, wrtType);
    else if (IsPipeCom(node))
        result = GetComsHDir(node, wrtType);
    else if (node.isType("NOZZ"))
        result = GetNozzDir(node, wrtType);

    return result;
}
void                SetHDir(WDNode& node, const DVec3& dir, WDWRTType wrtType)
{
    if (node.isType("BRAN"))
        SetBranHDir(node, dir, wrtType);
    else if (IsPipeCom(node))
        return ;
    else if (node.isType("NOZZ"))
        return ;
}
DVec3               GetTDir(WDNode& node, WDWRTType wrtType)
{
    DVec3 result;

    if (node.isType("BRAN"))
        result = GetBranTDir(node, wrtType);
    else if (IsPipeCom(node))
        result = GetComsTDir(node, wrtType);
    else if (node.isType("NOZZ"))
        result = GetNozzDir(node, wrtType);

    return result;
}
void                SetTDir(WDNode& node, const DVec3& dir, WDWRTType wrtType)
{
    if (node.isType("BRAN"))
        SetBranTDir(node, dir, wrtType);
    else if (IsPipeCom(node))
        return ;
    else if (node.isType("NOZZ"))
        return;
}

std::string         GetNozzBore(WDNode& node)
{
    if (!node.isType("NOZZ"))
        return std::string();

    // 获取连接口点, 管嘴连接点number固定为1
    auto pPt = node.keyPoint(1);
    if (pPt == nullptr)
        return std::string();

    return pPt->bore();
}
std::string         GetComsHBore(WDNode& node)
{
    // 获取入口点
    auto pPt = node.keyPoint(node.getAttribute("Arrive").toInt());
    if (pPt == nullptr)
        return std::string();

    return pPt->bore();
}
std::string         GetComsTBore(WDNode& node)
{
    // 获取出口点
    auto pPt = node.keyPoint(node.getAttribute("Leave").toInt());
    if (pPt == nullptr)
        return std::string();

    return pPt->bore();
}

std::string         GetHBore(WDNode& node)
{
    std::string result;

    if (node.isType("BRAN"))
        result = GetBranHBore(node);
    else if (IsPipeCom(node))
        result = GetComsHBore(node);
    else if (node.isType("NOZZ"))
        result = GetNozzBore(node);

    return result;
}
void                SetHBore(WDNode& node, const std::string& bore)
{
    if (node.isType("BRAN"))
        node.setAttribute("Hbore", bore);
    else if (IsPipeCom(node))
        return ;
    else if (node.isType("NOZZ"))
        return ;
}
std::string         GetTBore(WDNode& node)
{
    std::string result;

    if (node.isType("BRAN"))
        result = GetBranTBore(node);
    else if (IsPipeCom(node))
        result = GetComsTBore(node);
    else if (node.isType("NOZZ"))
        result = GetNozzBore(node);

    return result;
}
void                SetTBore(WDNode& node, const std::string& bore)
{
    if (node.isType("BRAN"))
        SetBranTBore(node, bore);
    else if (IsPipeCom(node))
        return ;
    else if (node.isType("NOZZ"))
        return;
}

std::string         GetNozzConnType(WDNode& node)
{
    if (!node.isType("NOZZ"))
        return std::string();

    // 获取连接点,管嘴的连接点number固定为1
    auto pPt = node.keyPoint(1);
    if (pPt == nullptr)
        return std::string();

    return pPt->connType();
}
std::string         GetComsHConnType(WDNode& node)
{
    // 获取入口点
    auto pPt = node.keyPoint(node.getAttribute("Arrive").toInt());
    if (pPt == nullptr)
        return std::string();

    return pPt->connType();
}
std::string         GetComsTConnType(WDNode& node)
{
    // 获取出口点
    auto pPt = node.keyPoint(node.getAttribute("Leave").toInt());
    if (pPt == nullptr)
        return std::string();

    return pPt->connType();
}

std::string         GetHConnType(WDNode& node)
{
    std::string result;

    if (node.isType("BRAN"))
        result = node.getAttribute("Hconnect").toString();
    else if (IsPipeCom(node))
        result = GetComsHConnType(node);
    else if (node.isType("NOZZ"))
        result = GetNozzConnType(node);

    return result;
}
void                SetHConnType(WDNode& node, const std::string& connType)
{
    if (node.isType("BRAN"))
        node.setAttribute("Hconnect", WDBMAttrValue(connType));
    else if (IsPipeCom(node))
        return ;
    else if (node.isType("NOZZ"))
        return ;
}
std::string         GetTConnType(WDNode& node)
{
    std::string result;

    if (node.isType("BRAN"))
        result = node.getAttribute("Tconnect").toString();
    else if (IsPipeCom(node))
        result = GetComsTConnType(node);
    else if (node.isType("NOZZ"))
        result = GetNozzConnType(node);

    return result;
}
void                SetTConnType(WDNode& node, const std::string& connType)
{
    if (node.isType("BRAN"))
        node.setAttribute("Tconnect", WDBMAttrValue(connType));
    else if (IsPipeCom(node))
        return ;
    else if (node.isType("NOZZ"))
        return ;
}

WDNode::SharedPtr   GetStube(WDNode& node)
{
    WDNode::SharedPtr result;
    
    if (node.isType("BRAN"))
        result = node.getAttribute("Hstube").toNodeRef().refNode();
    else if (IsPipeCom(node))
        result = node.getAttribute("Lstube").toNodeRef().refNode();

    return result;
}
void                SetStube(WDNode& node, WDNode::SharedPtr pStube)
{
    if (node.isType("BRAN"))
    {
        auto lStubeRef = node.getAttribute("Hstube").toNodeRef();
        lStubeRef.setRefNode(pStube);
        node.setAttribute("Hstube", WDBMAttrValue(lStubeRef));
    }
    else if (IsPipeCom(node))
    {
        auto lStubeRef = node.getAttribute("Lstube").toNodeRef();
        lStubeRef.setRefNode(pStube);
        node.setAttribute("Lstube", WDBMAttrValue(lStubeRef));
    }
}

bool IsInverse(WDNode& com)
{
    return com.getAttribute("Arrive").toInt() != 1;
}

WDNode::SharedPtr GetHRefNode(WDNode& node)
{
    if (node.isType("BRAN"))
        return node.getAttribute("Href").toNodeRef().refNode();
    else
        return node.getAttribute("Cref").toNodeRef().refNode();
}
void SetHRefNode(WDNode& node, WDNode::SharedPtr pTar)
{
    if (node.isType("BRAN"))
    {
        auto pRef = node.getAttribute("Href").toNodeRef();
        pRef.setRefNode(pTar);
        node.setAttribute("Href", WDBMNodeRef(pRef));
    }
    else
    {
        auto pRef = node.getAttribute("Cref").toNodeRef();
        pRef.setRefNode(pTar);
        node.setAttribute("Cref", WDBMNodeRef(pRef));
    }
}
WDNode::SharedPtr GetTRefNode(WDNode& node)
{
    if (node.isType("BRAN"))
        return node.getAttribute("Tref").toNodeRef().refNode();
    else
        return node.getAttribute("Cref").toNodeRef().refNode();
}
void SetTRefNode(WDNode& node, WDNode::SharedPtr pTar)
{
    if (node.isType("BRAN"))
    {
        auto pRef = node.getAttribute("Tref").toNodeRef();
        pRef.setRefNode(pTar);
        node.setAttribute("Tref", WDBMNodeRef(pRef));
    }
    else
    {
        auto pRef = node.getAttribute("Cref").toNodeRef();
        pRef.setRefNode(pTar);
        node.setAttribute("Cref", WDBMNodeRef(pRef));
    }
}
bool DisconnectCRef(WDNode& node)
{
    auto pRefNode = node.getAttribute("Cref").toNodeRef().refNode();
    if (pRefNode == nullptr)
        return false;

    if (pRefNode->isType("BRAN"))
    {
        // 分支需判断分支头尾的连接来判断是否断开
        auto    pHRefNode   =   pRefNode->getAttribute("Href").toNodeRef().refNode();
        auto    pTRefNode   =   pRefNode->getAttribute("Tref").toNodeRef().refNode();
        if (pHRefNode.get() == &node)
        {
            auto hRef = pRefNode->getAttribute("Href").toNodeRef();
            hRef.setRefNode(nullptr);
            pRefNode->setAttribute("Href", hRef);
        }
        if (pTRefNode.get() == &node)
        {
            auto tRef = pRefNode->getAttribute("Tref").toNodeRef();
            tRef.setRefNode(nullptr);
            pRefNode->setAttribute("Tref", tRef);
        }
    }
    else
    {
        // 其余节点类型直接断开cref
        auto tRef = pRefNode->getAttribute("Cref").toNodeRef();
        tRef.setRefNode(nullptr);
        pRefNode->setAttribute("Cref", tRef);
    }

    return true;
}
bool DisconnectHRef(WDNode& node)
{
    // 断开连接端
    auto pRefNode = GetHRefNode(node);
    if (pRefNode != nullptr)
    {
        if (pRefNode->isType("BRAN"))
        {
            // 分支需判断分支头尾的连接来判断是否断开
            auto    pHRefNode   =   pRefNode->getAttribute("Href").toNodeRef().refNode();
            auto    pTRefNode   =   pRefNode->getAttribute("Tref").toNodeRef().refNode();
            if (pHRefNode.get() == &node)
            {
                auto hRef = pRefNode->getAttribute("Href").toNodeRef();
                hRef.setRefNode(nullptr);
                pRefNode->setAttribute("Href", hRef);
            }
            if (pTRefNode.get() == &node)
            {
                auto tRef = pRefNode->getAttribute("Tref").toNodeRef();
                tRef.setRefNode(nullptr);
                pRefNode->setAttribute("Tref", tRef);
            }
        }
        else
        {
            // 其余节点类型直接断开cref
            auto tRef = pRefNode->getAttribute("Cref").toNodeRef();
            tRef.setRefNode(nullptr);
            pRefNode->setAttribute("Cref", tRef);
        }
    }

    // 断开自己这端
    SetHRefNode(node, nullptr);

    return true;
}
bool DisconnectTRef(WDNode& node)
{
    // 断开连接端
    auto pRefNode = GetTRefNode(node);
    if (pRefNode != nullptr)
    {
        if (pRefNode->isType("BRAN"))
        {
            // 分支需判断分支头尾的连接来判断是否断开
            auto    pHRefNode   =   pRefNode->getAttribute("Href").toNodeRef().refNode();
            auto    pTRefNode   =   pRefNode->getAttribute("Tref").toNodeRef().refNode();
            if (pHRefNode.get() == &node)
            {
                auto hRef = pRefNode->getAttribute("Href").toNodeRef();
                hRef.setRefNode(nullptr);
                pRefNode->setAttribute("Href", hRef);
            }
            if (pTRefNode.get() == &node)
            {
                auto tRef = pRefNode->getAttribute("Tref").toNodeRef();
                tRef.setRefNode(nullptr);
                pRefNode->setAttribute("Tref", tRef);
            }
        }
        else
        {
            // 其余节点类型直接断开cref
            auto tRef = pRefNode->getAttribute("Cref").toNodeRef();
            tRef.setRefNode(nullptr);
            pRefNode->setAttribute("Cref", tRef);
        }
    }

    // 断开自己这端
    SetTRefNode(node, nullptr);

    return true;
}

/**
* @brief 连接first节点和second节点（first、second节点存在连接对象，则保持原有连接，返回失败）
* 若为 分支1->分支2:将first的尾坐标设置为second的头坐标
* 若为 分支->管件：first尾坐标为second的入口坐标
* 若为 管件->分支：second的头位置设置为first的出口点
* @param first 前一个管件或者分支
* @param second 后一个管件或者分支
* @return true：first节点成功连接到second节点;false：失败
*/
static bool ReConnectCRefKeep(WDNode::SharedPtr first, WDNode::SharedPtr second, bool precursor)
{
    // 校验second是否被连接
    if (second != nullptr)
    {
        if (GetHRefNode(*second) != nullptr)
            return false;
    }
    // 校验second是否被连接
    if (first != nullptr)
    {
        if (GetTRefNode(*first) != nullptr)
            return false;
    }

    if (first != nullptr)
    {
        // 设置连接对象
        SetTRefNode(*first, second);
        // 设置其他属性
        if (second != nullptr)
        {
            if (precursor)
            {
                // 尾坐标
                SetTPos(*first, GetHPos(*second, WDWRTType::WRT_World), WDWRTType::WRT_World);
                // 尾方向
                SetTDir(*first, GetHDir(*second, WDWRTType::WRT_World), WDWRTType::WRT_World);
                // 尾管径
                SetTBore(*first, GetHBore(*second));
                // 连接类型
                SetTConnType(*first, GetHConnType(*second));
            }
        }
    }
    if (second != nullptr)
    {
        // 设置连接对象
        SetHRefNode(*second, first);
        // 设置其他属性
        if (first != nullptr)
        {
            // 头坐标
            SetHPos(*second, GetTPos(*first, WDWRTType::WRT_World), WDWRTType::WRT_World);
            // 头方向
            SetHDir(*second, GetTDir(*first, WDWRTType::WRT_World), WDWRTType::WRT_World);
            // 头管径
            SetHBore(*second, GetTBore(*first));
            // 头连接类型
            SetHConnType(*second, GetTConnType(*first));
        }
    }

    return true;
}
/**
* @brief 强制连接first节点和second节点（first、second节点存在连接对象，则断开原连接对象）
* 若为 分支1->分支2:将first的尾坐标设置为second的头坐标
* 若为 分支->管件：first尾坐标为second的入口坐标
* 若为 管件->分支：second的头位置设置为first的出口点
* @param first 前一个管件或者分支
* @param second 后一个管件或者分支
* @return true：first节点成功强制连接到second节点;false：失败
*/
static bool ReConnectCRefForce(WDNode::SharedPtr first, WDNode::SharedPtr second, bool precursor)
{
    if (first != nullptr)
    {
        // 断开尾连接
        DisconnectTRef(*first);
        // 设置连接对象
        SetTRefNode(*first, second);
        // 设置其他属性
        if (second != nullptr)
        {
            if (precursor)
            {
                // 尾坐标
                SetTPos(*first, GetHPos(*second, WDWRTType::WRT_World), WDWRTType::WRT_World);
                // 尾方向
                SetTDir(*first, GetHDir(*second, WDWRTType::WRT_World), WDWRTType::WRT_World);
                // 尾管径
                SetTBore(*first, GetHBore(*second));
                // 连接类型
                SetTConnType(*first, GetHConnType(*second));
            }
        }
    }
    if (second != nullptr)
    {
        // 断开头连接
        DisconnectHRef(*second);
        // 设置连接对象
        SetHRefNode(*second, first);
        // 设置其他属性
        if (first != nullptr)
        {
            // 头坐标
            SetHPos(*second, GetTPos(*first, WDWRTType::WRT_World), WDWRTType::WRT_World);
            // 头方向
            SetHDir(*second, GetTDir(*first, WDWRTType::WRT_World), WDWRTType::WRT_World);
            // 头管径
            SetHBore(*second, GetTBore(*first));
            // 头连接类型
            SetHConnType(*second, GetTConnType(*first));
        }
    }

    return true;
}
bool ReConnectCRef(WDNode::SharedPtr first, WDNode::SharedPtr second, bool force, bool precursor)
{
    if (force)
        return ReConnectCRefForce(first, second, precursor);
    else
        return ReConnectCRefKeep(first, second, precursor);
}

bool SetElboLeaveDirection(WDNode& pCom, const Vec3& direction, double errorValue)
{
    auto    gArriveDir  =   GetHDir(pCom, WRT_World);
    Vec3    gLeaveDir   =   GetTDir(pCom, WRT_World);

    // 出口点朝向与目标点目标朝向同向，则不发生旋转
    if (DVec3::InTheSameDirection(gLeaveDir, direction, errorValue))
        return false;

    // 目标朝向与入口点朝向共线，则不允许旋转
    if (Vec3::InTheSameDirection(gArriveDir, direction, errorValue) || Vec3::InTheSameDirection(-gArriveDir, direction, errorValue))
    {
        LOG_ERROR << "不允许此旋转!";
        return false;
    }
    // 出口点朝向与入口点朝向相反，则不允许旋转
    if (Vec3::InTheSameDirection(gArriveDir, -gLeaveDir, errorValue))
    {
        LOG_ERROR << "不允许此旋转!";
        return false;
    }

    // 入口点朝向 与 出口点朝向 形成平面s的 法线
    Vec3    sNor    =   Vec3::Cross(gArriveDir, gLeaveDir);
    // 入口点朝向 与 目标朝向 形成平面t的 法线
    Vec3    tNor    =   Vec3::Cross(gArriveDir, direction);
    // 出口点朝向 旋转至 目标朝向 的 旋转角度
    real    angle   =   Vec3::Angle(sNor, tNor);
    // 旋转管件 至 出口点朝向与平面t共面(旋转方向与入口点朝向是否在同一侧)
    Vec3    rotDir  =   Vec3::Cross(sNor, tNor);
    if (Vec3::Dot(rotDir, gArriveDir) < 0)
    {
        angle = -angle;
    }
    pCom.rotate(gArriveDir, angle);
    pCom.update();

    // 旋转过后的 出口点朝向
    Vec3    aGLDir  =   GetTDir(pCom, WRT_World);
    // 旋转过后 出口点朝向 与 目标朝向 不一致，则判断是否为 可变角度管件
    if (!Vec3::InTheSameDirection(aGLDir, direction))
    {
        // 可变角度管件 设置角度
        if (IsVariablePipeComponent(pCom))
        {
            real beforeAngle = pCom.getAttribute("Angle").toDouble();
            // 目标夹角
            real inclAangle = Vec3::Angle(gArriveDir, direction);

            // 变化之前的入口朝向
            auto beforeArriveDir = gArriveDir;
            // 设置变化过后的管件角度
            pCom.setAttribute("Angle", inclAangle);
            pCom.updateModel();
            // 更新出入口朝向
            gLeaveDir   =   GetTDir(pCom, WRT_World);
            gArriveDir  =   GetHDir(pCom, WRT_World);

            // 判断设置角度后入口朝向与设置之前是否同向
            if (DVec3::InTheSameDirection(beforeArriveDir, gArriveDir, errorValue))
            {
                // 判断出口朝向与目标朝向是否一致，不一致进行一次互补的角度设置
                if (!DVec3::InTheSameDirection(gLeaveDir, direction, errorValue))
                {
                    beforeAngle = pCom.getAttribute("Angle").toDouble();
                    inclAangle = 180 - inclAangle;
                    // 设置变化过后的管件角度
                    pCom.setAttribute("Angle", inclAangle);
                    pCom.updateModel();
                }
                else
                {
                    //assert(false);
                }
            }
            else
            {
                // 进行反向旋转向出口朝向对齐(向量形成角度一定不会=180°，所以直接用两向量计算四元数)
                auto    tQuatPCom   =   Quat::FromVectors(gLeaveDir, direction);
                auto    tMatPCom    =   Mat4::FromQuat(tQuatPCom);
                auto    srcMatPCom  =   pCom.globalRSTransform();
                pCom.setAttribute("Orientation WRT World", DMat4::ToQuat(tMatPCom * srcMatPCom));
                pCom.triggerUpdate();
                gArriveDir = GetHDir(pCom, WRT_World);
                // 判断入口朝向与设置角度之前是否一致，不一致进行一次互补的角度设置
                if (!DVec3::InTheSameDirection(gArriveDir, beforeArriveDir, errorValue))
                {
                    beforeAngle = pCom.getAttribute("Angle").toDouble();
                    inclAangle = 180 - inclAangle;
                    // 设置变化过后的管件角度
                    pCom.setAttribute("Angle", inclAangle);
                    pCom.updateModel();
                }
                else
                {
                    //assert(false);
                }
            }
        }
    }
    return true;
}
bool SetElboArriveDirection(WDNode& pCom, const Vec3& direction, double errorValue)
{
    // 获取入口点出口点朝向
    Vec3    gArriveDir  =   GetHDir(pCom, WRT_World);
    Vec3    gLeaveDir   =   GetTDir(pCom, WRT_World);
    Vec3    gBeforPos   =   GetTPos(pCom, WRT_World);

    // 入口点朝向与目标点目标朝向同向，则不发生旋转
    if (DVec3::InTheSameDirection(gArriveDir, direction, errorValue))
        return false;

    // 出口点朝向与目标朝向共线，则不允许旋转
    if (Vec3::InTheSameDirection(direction, gLeaveDir, errorValue) || Vec3::InTheSameDirection(direction, -gLeaveDir, errorValue))
    {
        LOG_ERROR << "不允许此旋转!";
        return false;
    }
    // 入口点朝向与出口点朝向相反，则不允许旋转
    if (Vec3::InTheSameDirection(gLeaveDir, -gArriveDir, errorValue))
    {
        LOG_ERROR << "不允许此旋转!";
        return false;
    }

    // 出口点朝向 与 入口点朝向 形成平面s的 法线
    Vec3    sNor    =   Vec3::Cross(gLeaveDir, gArriveDir);
    // 目标朝向 与 出口点朝向 形成平面t的 法线
    Vec3    tNor    =   Vec3::Cross(gLeaveDir, direction);
    // 入口点朝向 旋转至 目标朝向 的 旋转角度
    real    angle   =   Vec3::Angle(sNor, tNor);
    // 旋转管件 至 入口点朝向与平面t共面(旋转方向与出口点朝向是否在同一侧)
    Vec3    rotDir  =   Vec3::Cross(sNor, tNor);
    if (Vec3::Dot(rotDir, gLeaveDir) < 0)
    {
        angle = -angle;
    }
    pCom.rotate(gLeaveDir, angle);
    pCom.update();

    // 旋转过后的 入口点朝向
    Vec3    aGADir  =   GetHDir(pCom, WRT_World);
    // 旋转过后 目标朝向 与 入口点朝向 不一致，则判断是否为 可变角度管件
    if (!Vec3::InTheSameDirection(direction, aGADir))
    {
        // 可变角度管件 设置角度
        if (IsVariablePipeComponent(pCom))
        {
            real beforeAngle = pCom.getAttribute("Angle").toDouble();
            // 目标夹角
            real inclAangle = Vec3::Angle(direction, gLeaveDir);

            // 变化之前的出口朝向
            auto beforeLeaveDir = gLeaveDir;
            // 设置变化过后的管件角度
            pCom.setAttribute("Angle", inclAangle);
            pCom.updateModel();
            // 更新出入口朝向
            gLeaveDir   =   GetTDir(pCom, WRT_World);
            gArriveDir  =   GetHDir(pCom, WRT_World);

            // 判断设置角度后出口朝向与设置之前是否同向
            if (DVec3::InTheSameDirection(beforeLeaveDir, gLeaveDir, errorValue))
            {
                // 判断入口朝向与目标朝向是否一致，不一致进行一次互补的角度设置
                if (!DVec3::InTheSameDirection(gArriveDir, direction, errorValue))
                {
                    beforeAngle = pCom.getAttribute("Angle").toDouble();
                    inclAangle = 180 - inclAangle;
                    // 设置变化过后的管件角度
                    pCom.setAttribute("Angle", inclAangle);
                    pCom.updateModel();
                }
                else
                {
                    //assert(false);
                }
            }
            else
            {
                // 进行反向旋转向入口朝向对齐
                auto    tQuatPCom   =   Quat::FromVectors(gArriveDir, direction);
                auto    tMatPCom    =   Mat4::FromQuat(tQuatPCom);
                auto srcMatPCom = pCom.globalRSTransform();
                pCom.setAttribute("Orientation WRT World", DMat4::ToQuat(tMatPCom * srcMatPCom));
                pCom.triggerUpdate();
                gLeaveDir = GetTDir(pCom, WRT_World);
                // 判断出口朝向与设置角度之前是否一致，不一致进行一次互补的角度设置
                if (!DVec3::InTheSameDirection(gLeaveDir, beforeLeaveDir, errorValue))
                {
                    beforeAngle = pCom.getAttribute("Angle").toDouble();
                    inclAangle = 180 - inclAangle;
                    // 设置变化过后的管件角度
                    pCom.setAttribute("Angle", inclAangle);
                    pCom.updateModel();
                }
                else
                {
                    //assert(false);
                }
            }
        }
    }
    return true;
}
void SetStraightLeaveDirection(WDNode& pCom, const Vec3& direction, double errorValue)
{
    WDUnused(errorValue);

    // 获取出口点朝向
    Vec3    gLeaveDir   =   GetTDir(pCom, WRT_World);
    auto    tQuat       =   Quat::FromVectors(gLeaveDir, direction);
    auto    tMat        =   Mat4::FromQuat(tQuat);
    auto    srcMat      =   pCom.globalRSTransform();
    pCom.setAttribute("Orientation WRT World", DMat4::ToQuat(tMat * srcMat));
    pCom.update();
}
void SetStraightArriveDirection(WDNode& pCom, const Vec3& direction, double errorValue)
{
    WDUnused(errorValue);

    // 获取入口点朝向
    Vec3    gArriveDir  =   GetHDir(pCom, WRT_World);
    auto    tQuat       =   Quat::FromVectors(gArriveDir, direction);
    auto    tMat        =   Mat4::FromQuat(tQuat);
    auto    srcMat      =   pCom.globalRSTransform();
    pCom.setAttribute("Orientation WRT World", DMat4::ToQuat(tMat * srcMat));
    pCom.update();
}
void BranchHeadConnectTo(WDNode& bran, WDNode& tar)
{
    if (!bran.isType("BRAN"))
        return ;

    // 目标节点类型
    if (tar.isType("NOZZ"))
    {
        // 坐标朝向
        auto    pConnectPt  = tar.keyPoint(1);
        if (pConnectPt == nullptr)
            return ;
        auto    gPos        =   pConnectPt->transformedPosition(tar.globalTransform());
        auto    gDir        =   pConnectPt->transformedDirection(tar.globalRSTransform());

        bran.setAttribute("Hposition WRT World", gPos);
        bran.setAttribute("Hdirection WRT World", gDir);

        // 管径
        bran.setAttribute("Hbore", pConnectPt->bore());
        // 管子连接方式
        bran.setAttribute("Hconnect", pConnectPt->connType());
        // 目标节点连接
        tar.setAttribute("Cref", WDBMNodeRef(WDNode::ToShared(&bran)));
        // 原连接断开
        DisconnectHRef(bran);
        // 建立新连接
        bran.setAttribute("Href", WD::WDBMNodeRef(WDNode::ToShared(&tar)));

        // 数据更新
        bran.updateModel();
        tar.updateModel();
    }
    else if (tar.isType("TEE"))
    {
        // 坐标朝向
        auto    pBranchPt   =   tar.keyPoint(WDBMDPipeUtils::Fork(tar));
        if (pBranchPt == nullptr)
            return ;
        auto    gPos        =   pBranchPt->transformedPosition(tar.globalTransform());
        auto    gDir        =   pBranchPt->transformedDirection(tar.globalRSTransform());

        bran.setAttribute("Hposition WRT World", gPos);
        bran.setAttribute("Hdirection WRT World", gDir);

        // 头管径
        bran.setAttribute("Hbore", pBranchPt->bore());
        // 管子连接方式
        bran.setAttribute("Hconnect", pBranchPt->connType());
        // 目标节点连接
        tar.setAttribute("Cref", WDBMNodeRef(WDNode::ToShared(&bran)));
        // 原连接断开
        DisconnectHRef(bran);
        // 建立新连接
        bran.setAttribute("Href", WD::WDBMNodeRef(WDNode::ToShared(&tar)));

        // 数据更新
        bran.updateModel();
        tar.updateModel();
    }

}
void BranchHeadConnectTo(WDNode& bran, WDNode& tar, bool ht)
{
    if (!bran.isType("BRAN"))
        return ;

    // 目标节点类型
    if (tar.isType("BRAN"))
    {
        if (ht) // 连接目标分支头
        {
            // 坐标朝向
            const auto gPos = tar.getAttribute("Hposition WRT World").toDVec3();
            const auto gDir = tar.getAttribute("Hdirection WRT World").toDVec3();

            bran.setAttribute("Hposition WRT World", gPos);
            bran.setAttribute("Hdirection WRT World", -gDir);

            // 头管径
            bran.setAttribute("Hbore", tar.getAttribute("Hbore"));
            // 管子连接方式
            bran.setAttribute("Hconnect", tar.getAttribute("Hconnect"));
            // 目标节点连接
            tar.setAttribute("Href", WD::WDBMNodeRef(WDNode::ToShared(&bran)));
            // 原连接断开
            DisconnectHRef(bran);
            // 建立新连接
            bran.setAttribute("Href", WD::WDBMNodeRef(WDNode::ToShared(&tar)));

            bran.updateModel();
            tar.updateModel();
        }
        else // 连接分支尾
        {
            // 坐标朝向
            const auto gPos = tar.getAttribute("Tposition WRT World").toDVec3();
            const auto gDir = tar.getAttribute("Tdirection WRT World").toDVec3();

            bran.setAttribute("Hposition WRT World", gPos);
            bran.setAttribute("Hdirection WRT World", -gDir);

            // 头管径
            bran.setAttribute("Hbore", tar.getAttribute("Tbore"));
            // 管子连接方式
            bran.setAttribute("Hconnect", tar.getAttribute("Tconnect"));
            // 目标节点连接
            tar.setAttribute("Tref", WD::WDBMNodeRef(WDNode::ToShared(&bran)));
            // 原连接断开
            DisconnectHRef(bran);
            // 建立新连接
            bran.setAttribute("Href", WD::WDBMNodeRef(WDNode::ToShared(&tar)));

            // 数据更新
            bran.updateModel();
            tar.updateModel();
        }
    }
    else if (tar.isAnyOfType("ELBO", "TEE"))  // 若需连接到另一分支的三通，请使用不带头尾方向的连接接口
    {
        // 目标管件是否属于分支
        bool sameBranch = false;
        if (&bran == tar.parent().get())
        {
            sameBranch = true;
        }

        if (ht) // 连接目标管件入口点
        {
            // 坐标朝向
            auto    pArrive =   tar.keyPoint(tar.getAttribute("Arrive").toInt());
            if (pArrive == nullptr)
                return ;
            auto    gPos    =   pArrive->transformedPosition(tar.globalTransform());
            auto    gDir    =   pArrive->transformedDirection(tar.globalRSTransform());

            bran.setAttribute("Hposition WRT World", gPos);
            if (sameBranch)
                bran.setAttribute("Hdirection WRT World", -gDir);
            else
                bran.setAttribute("Hdirection WRT World", gDir);

            // 头管径
            bran.setAttribute("Hbore", pArrive->bore());
            // 管子连接方式
            bran.setAttribute("Hconnect", pArrive->connType());
            // 原连接断开
            DisconnectHRef(bran);

            // 数据更新
            bran.updateModel();
        }
        else // 连接目标管件出口点
        {
            // 坐标朝向
            auto    pLeave  =   tar.keyPoint(tar.getAttribute("Leave").toInt());
            if (pLeave == nullptr)
                return ;
            auto    gPos    =   pLeave->transformedPosition(tar.globalTransform());
            auto    gDir    =   pLeave->transformedDirection(tar.globalRSTransform());
            bran.setAttribute("Hposition WRT World", gPos);
            if (sameBranch)
                bran.setAttribute("Hdirection WRT World", -gDir);
            else
                bran.setAttribute("Hdirection WRT World", gDir);

            // 头管径
            bran.setAttribute("Hbore", pLeave->bore());
            // 管子连接方式
            bran.setAttribute("Hconnect", pLeave->connType());
            // 原连接断开
            DisconnectHRef(bran);
            // 头连接置空
            bran.setAttribute("Href", WD::WDBMNodeRef(nullptr));

            bran.updateModel();
        }
    }
}
void BranchTailConnectTo(WDNode& bran, WDNode& tar)
{
    if (!bran.isType("BRAN"))
        return ;

    if (tar.isType("NOZZ"))
    {
        // 坐标朝向
        auto    pConnectPt  =   tar.keyPoint(1);
        if (pConnectPt == nullptr)
            return ;
        auto    gPos        =   pConnectPt->transformedPosition(tar.globalTransform());
        auto    gDir        =   pConnectPt->transformedDirection(tar.globalRSTransform());

        bran.setAttribute("Tposition WRT World", gPos);
        bran.setAttribute("Tdirection WRT World", gDir);

        // 尾管径
        bran.setAttribute("Tbore", pConnectPt->bore());
        // 管子连接方式
        bran.setAttribute("Tconnect", pConnectPt->connType());
        // 目标节点连接
        tar.setAttribute("Cref", WDBMNodeRef(WDNode::ToShared(&bran)));
        // 原连接断开
        DisconnectTRef(bran);
        // 建立新连接
        bran.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&tar)));

        bran.updateModel();
        tar.updateModel();
    }
    else if (tar.isType("TEE"))
    {
        // 坐标朝向
        auto    pBranchPt   =   tar.keyPoint(WDBMDPipeUtils::Fork(tar));
        if (pBranchPt == nullptr)
            return ;
        auto    gPos        =   pBranchPt->transformedPosition(tar.globalTransform());
        auto    gDir        =   pBranchPt->transformedDirection(tar.globalRSTransform());

        bran.setAttribute("Tposition WRT World", gPos);
        bran.setAttribute("Tdirection WRT World", gDir);

        // 尾管径
        bran.setAttribute("Tbore", pBranchPt->bore());
        // 管子连接方式
        bran.setAttribute("Tconnect", pBranchPt->connType());
        // 目标节点连接
        tar.setAttribute("Cref", WDBMNodeRef(WDNode::ToShared(&bran)));
        // 原连接断开
        DisconnectTRef(bran);
        // 建立新连接
        bran.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&tar)));

        bran.updateModel();
        tar.updateModel();
    }
}
void BranchTailConnectTo(WDNode& bran, WDNode& tar, bool ht)
{
    if (!bran.isType("BRAN"))
        return ;

    // 目标节点类型
    if (tar.isType("BRAN"))
    {
        if (ht) // 连接目标分支头
        {
            // 坐标朝向
            const auto gPos = tar.getAttribute("Hposition WRT World").toDVec3();
            const auto gDir = tar.getAttribute("Hdirection WRT World").toDVec3(); 

            bran.setAttribute("Tposition WRT World", gPos);
            bran.setAttribute("Tdirection WRT World", -gDir);
            
            // 尾管径
            bran.setAttribute("Tbore", tar.getAttribute("Hbore"));
            // 管子连接方式
            bran.setAttribute("Tconnect", tar.getAttribute("Hconnect"));
            // 目标节点连接
            tar.setAttribute("Href", WDBMNodeRef(WDNode::ToShared(&bran)));
            // 原连接断开
            DisconnectTRef(bran);
            // 建立新连接
            bran.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&tar)));

            bran.updateModel();
            tar.updateModel();
        }
        else // 连接分支尾
        {
            // 坐标朝向
            const auto gPos = tar.getAttribute("Tposition WRT World").toDVec3();
            const auto gDir = tar.getAttribute("Tdirection WRT World").toDVec3();

            bran.setAttribute("Tposition WRT World", gPos);
            bran.setAttribute("Tdirection WRT World", -gDir);

            // 尾管径
            bran.setAttribute("Tbore", tar.getAttribute("Tbore"));
            // 管子连接方式
            bran.setAttribute("Tconnect", tar.getAttribute("Tconnect"));
            // 目标节点连接
            tar.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&bran)));
            // 原连接断开
            DisconnectTRef(bran);
            // 建立新连接
            bran.setAttribute("Tref", WDBMNodeRef(WDNode::ToShared(&tar)));

            bran.updateModel();
            tar.updateModel();
        }
    }
    else if (tar.isAnyOfType("ELBO", "TEE"))  // 若需连接到另一分支的三通，请使用不带头尾方向的连接接口
    {
        // 目标管件是否属于分支
        bool sameBranch = false;
        if (&bran == tar.parent().get())
        {
            sameBranch = true;
        }

        if (ht) // 连接目标管件入口点
        {
            // 坐标朝向
            auto    pArrive =   tar.keyPoint(tar.getAttribute("Arrive").toInt());
            if (pArrive == nullptr)
                return ;
            auto    gPos    =   pArrive->transformedPosition(tar.globalTransform());
            auto    gDir    =   pArrive->transformedDirection(tar.globalRSTransform());
            bran.setAttribute("Tposition WRT World", gPos);
            if (sameBranch)
                bran.setAttribute("Tdirection WRT World", -gDir);
            else
                bran.setAttribute("Tdirection WRT World", gDir);
            
            // 尾管径
            bran.setAttribute("Tbore", pArrive->bore());
            // 管子连接方式
            bran.setAttribute("Tconnect", pArrive->connType());
            // 原连接断开
            DisconnectTRef(bran);

            bran.updateModel();
        }
        else // 连接目标管件出口点
        {
            // 坐标朝向
            auto    pLeave  =   tar.keyPoint(tar.getAttribute("Leave").toInt());
            if (pLeave == nullptr)
                return ;
            auto    gPos    =   pLeave->transformedPosition(tar.globalTransform());
            auto    gDir    =   pLeave->transformedDirection(tar.globalRSTransform());
            bran.setAttribute("Tposition WRT World", gPos);
            if (sameBranch)
                bran.setAttribute("Tdirection WRT World", -gDir);
            else
                bran.setAttribute("Tdirection WRT World", gDir);
            
            // 尾管径
            bran.setAttribute("Tbore", pLeave->bore());
            // 管子连接方式
            bran.setAttribute("Tconnect", pLeave->connType());
            // 原连接断开
            DisconnectTRef(bran);
            // 尾连接置空
            bran.setAttribute("Tref", WDBMNodeRef(nullptr));

            bran.updateModel();
        }
    }
}

void ComponentAlignDir(WD::WDCore& core, WDNode& component, const DVec3& gAlignDir, PipeFlowDir flowDir)
{
    WDUnused(core);
    // 当前管件所属的分支节点
    auto pBran = component.parent();
    if (pBran == nullptr)
        return ;

    // 获取管件用来对齐的朝向
    DVec3 gDir;
    if (flowDir == PFD_Forward)
    {
        gDir = GetComsHDir(component, WRT_World);
    }
    else if (flowDir == PFD_Backward)
    {
        gDir = GetComsTDir(component, WRT_World);
    }

    //计算位置以及朝向
    WD::DVec3   nv0         =   gDir.normalized();
    WD::DVec3   nv1         =   gAlignDir.normalized();
    WD::DQuat   tQuat;
    //这里旋转计算加入角度误差，误差值未 0.001°
    static constexpr const double AngleErrorValue = 0.001;
    double      tAng        =   WD::DVec3::Angle(nv0, nv1);
    if (tAng <= AngleErrorValue)
    {
        //俩向量方向相同，不做旋转
        tQuat = WD::DQuat::Identity();
    }
    else if (tAng >= 180 - AngleErrorValue)
    {
        //俩向量反向
        nv1 = -nv0;
        tQuat = WD::DQuat::FromVectors(nv0, nv1);
    }
    else
    {
        tQuat = WD::DQuat::FromVectors(nv0, nv1);
    }

    component.setAttribute("Orientation WRT World", tQuat * component.getAttribute("Orientation WRT World").toQuat());

    component.update();

    //更新分支连接
    if (component.parent() != nullptr)
        component.parent()->triggerUpdate(true);
}
void ComponentAlignPos(WD::WDCore& core, WDNode& component, const DVec3& gAlignPos, PipeFlowDir flowDir)
{
    WDUnused(core);
    // 当前管件所属的分支节点
    auto pBran = component.parent();
    if (pBran == nullptr)
        return ;

    // 获取管件用来对齐的位置
    DVec3 gPos;
    if (flowDir == PFD_Forward)
    {
        gPos = GetComsHPos(component, WRT_World);
    }
    else if (flowDir == PFD_Backward)
    {
        gPos = GetComsTPos(component, WRT_World);
    }

    auto gOffset = gAlignPos - gPos;
    component.setAttribute("Position WRT World", component.getAttribute("Position WRT World").toDVec3() + gOffset);

    //更新分支连接
    if (component.parent() != nullptr)
        component.parent()->triggerUpdate(true);
}
void ComponentAlign(WD::WDCore& core, WDNode& component, const DVec3& gAlignDir, const DVec3& gAlignPos, PipeFlowDir flowDir)
{
    // 先对齐朝向
    ComponentAlignDir(core, component, gAlignDir, flowDir);
    // 然后对齐位置
    ComponentAlignPos(core, component, gAlignPos, flowDir);
}

WDNode::Nodes       GetCocos(WDBMCatalog& mgr)
{
    WDNode::Nodes   cocos;
    auto            pRoot   =   mgr.root();
    if (pRoot == nullptr)
        return cocos;
    // 获取根节点下所有CCTA节点
    WDNode::Nodes pCctas;
    for (const auto& pChild : pRoot->children())
    {
        if (pChild == nullptr)
            continue;
        if (!pChild->isType("CCTA"))
            continue;
        pCctas.push_back(pChild);
    }
    for (const auto& pCcta : pCctas)
    {
        if (pCcta == nullptr)
            continue;
        // 遍历获取所有coco节点
        for (const auto& pChild : pCcta->children())
        {
            if (pChild == nullptr)
                continue;
            if (!pChild->isType("COCO"))
                continue;
            cocos.push_back(pChild);
        }
    }
    return cocos;
}

WDNode::Nodes       GetSPECByAttrPurpose(WDBMCatalog& mgr, const std::string& purpose)
{
    WDNode::Nodes   rNodes;
    // 元件库根节点
    auto pRoot   =   mgr.root();
    if (pRoot == nullptr)
        return rNodes;
    // 遍历根节点的所有子节点，处理SPWL类型的节点
    for (auto pSPWLNode : pRoot->children())
    {
        if (pSPWLNode == nullptr || !pSPWLNode->isType("SPWL"))
            continue;
        // 遍历SPWL的所有子节点，处理SPEC类型的节点
        // 如果SPEC节点的purpose属性值与指定的purpose值相同，则SPEC节点就是需要返回的结果节点之一
        for (auto pSPECNode : pSPWLNode->children())
        {
            if (pSPECNode == nullptr)
                continue;
            //判断是否是等级节点
            auto purposeStr = pSPECNode->getAttribute("Purpose").toWord();
            if (purposeStr == purpose)
                rNodes.push_back(pSPECNode);
        }
    }
    return rNodes;
}
std::vector<WDNode::SharedPtr> GetSPECByTEXTAttrSText(WDBMCatalog& mgr, const std::string& sText)
{
    WDNode::Nodes   rNodes;
    // 元件库根节点
    auto pRoot = mgr.root();
    if (pRoot == nullptr)
        return rNodes;
    // 遍历根节点的所有子节点，处理SPWL类型的节点
    for (auto pSPWLNode: pRoot->children())
    {
        if (pSPWLNode == nullptr || !pSPWLNode->isType("SPWL"))
            continue;
        // 遍历SPWL的所有子节点，处理SPEC类型的节点
        for (auto pSPECNode: pSPWLNode->children())
        {
            if (pSPECNode == nullptr || !pSPECNode->isType("SPEC"))
                continue;
            // 找到SPEC节点下的第一个TEXT子节点，判断该节点的Stext值是否与指定值(sText)匹配
            // 如果匹配，则SPEC节点就是需要返回的结果节点之一
            for (auto pTextNode: pSPECNode->children())
            {
                if (pTextNode == nullptr || !pTextNode->isType("TEXT"))
                    continue;
                auto tSTextStr = pTextNode->getAttribute("Stext").toString();
                if (tSTextStr == sText)
                    rNodes.push_back(pSPECNode);
            }
        }
    }
    return rNodes;
}


/**
* @brief 连接结果
*/
struct ConnectResult
{
    // 可连接
    bool connectable    =   false;
    // 警告
    bool warning        =   false;
};

constexpr const char* COCOConnectAny    = "ANY";
constexpr const char* COCOConnectNull   = "NULL";

static ConnectResult Connectable(const WDNode::Nodes& cocos
    , const std::string& connType1
    , const std::string& connType2)
{
    if (cocos.empty())
    {
        //不创建且报错
        return { false, true };
    }

    // 当前两点连接类型
    std::string connectType1 = connType1;
    std::string connectType2 = connType2;

    if (connectType1.empty())
        connectType1 = COCOConnectNull;
    if (connectType2.empty())
        connectType2 = COCOConnectNull;

    bool bNull = false;
    if (connectType1 == COCOConnectNull
        || connectType2 == COCOConnectNull)
    {
        bNull = true;
    }
    std::vector<std::array<std::string, 2> > cocoStrings;
    cocoStrings.reserve(cocos.size());
    //先整理数据
    bool bExistAnyAny = false;
    for (const WDNode::SharedPtr& pCoco : cocos)
    {
        if (pCoco == nullptr)
            continue;
        std::string cType   =   pCoco->getAttribute("Ctype").toString();
        auto cocoTypes      =   WD::StringSplit(cType, " ");
        //assert(cocoTypes.size() == 2);
        if (cocoTypes.size() != 2)
        {
            //assert(false && "coco data exception!");
            continue;
        }

        //如果存在ANY-ANY项，直接加一个标志，不用临时存储到数组中
        if (cocoTypes[0] == COCOConnectAny
            && cocoTypes[1] == COCOConnectAny)
        {
            bExistAnyAny = true;
            continue;
        }

        cocoStrings.push_back({cocoTypes[0], cocoTypes[1]});
    }
    //COCO表为空
    if (cocoStrings.empty() && !bExistAnyAny)
    {
        //不创建且报错
        return { false, true };
    }

    //存在ANY-ANY项且传入的两个连接方式也是ANY-ANY
    if (bExistAnyAny
        && connectType1 == COCOConnectAny
        && connectType2 == COCOConnectAny)
    {
        return { true, false };
    }


    //直接查找两种类型是否存在,如果存在NULL,则报警告
    for (size_t i = 0; i < cocoStrings.size(); ++i)
    {
        const auto& cocoTypes = cocoStrings[i];
        if ((connectType1 == cocoTypes[0] && connectType2 == cocoTypes[1])
            || (connectType2 == cocoTypes[0] && connectType1 == cocoTypes[1]))
        {
            if (bNull)
            {
                //创建且报错
                return { true, true };
            }
            else
            {
                //正常创建
                return { true, false };
            }
        }
    }
    //如果没直接找到， 查找是否存在 "xxx-ANY" 或 "ANY-xxx"
    for (size_t i = 0; i < cocoStrings.size(); ++i)
    {
        const auto& cocoTypes = cocoStrings[i];

        if ((connectType1       == cocoTypes[0] && cocoTypes[1] == COCOConnectAny)
            || (connectType2    == cocoTypes[0] && cocoTypes[1] == COCOConnectAny)
            || (connectType1    == cocoTypes[1] && cocoTypes[0] == COCOConnectAny)
            || (connectType2    == cocoTypes[1] && cocoTypes[0] == COCOConnectAny))
        {
            //正常创建
            return { true, false };
        }
    }
    // 检测是否存在ANY-ANY
    if (bExistAnyAny)
    {
        //创建且报错
        return { true, true };
    }
    //不创建且报错
    return { false, true };

}
static ConnectResult Connectable(WDBMCatalog& mgr
    , const std::string& connType1
    , const std::string& connType2)
{
    auto cocos = GetCocos(mgr);
    return Connectable(cocos, connType1, connType2);
}
CheckCOCOResult  CheckCOCOTable(WDBMCatalog& mgr
    , const std::string& connectType1
    , const std::string& connectType2)
{
	// 连接校验
	auto ret = Connectable(mgr
        , connectType1
		, connectType2);
	//不能连接
	if (!ret.connectable)
		return CCCR_Faild;
	//可以连接但报警告,并且两个管件相距100毫米
	if (ret.warning)
		return CCCR_Waring;
	//可以连接
	return CCCR_Success;
}
CheckCOCOResult  CheckCOCOTable(const WDNode::Nodes& cocos
    , const std::string& connectType1
    , const std::string& connectType2)
{
	// 连接校验
	auto ret = Connectable(cocos
        , connectType1
		, connectType2);
	//不能连接
	if (!ret.connectable)
		return CCCR_Faild;
	//可以连接但报警告,并且两个管件相距100毫米
	if (ret.warning)
		return CCCR_Waring;
	//可以连接
	return CCCR_Success;
}

/*********** SPEC SELE SPCO 节点的 "Question", "Answer", "Maxanswer", "Tanswer" 相关接口 ***********/

std::string GetQuestionStr(const WD::WDNode& node, bool* bOk)
{
    return node.getAttribute("Question").toString(bOk);
}
std::string GetAnswerStr(const WD::WDNode& node
    , bool deduplicate
    , std::optional<std::string> parentQuestionStr
    , bool* bOk)
{
    std::string quesStr = "";
    if (parentQuestionStr)
    {
        quesStr = parentQuestionStr.value();
    }
    else if (node.parent() != nullptr)
    {
        bool bTOk = false;
        auto ret = GetQuestionStr(*(node.parent()), &bTOk);
        if (bTOk)
            quesStr = ret;
    }

    WD::SetValueToBooleanPtr(bOk, true);

    if (quesStr == WD::Spec_Question_PBOR
        || quesStr == WD::Spec_Question_DIAM
        || quesStr == WD::Spec_Question_ANGL
        || quesStr == WD::Spec_Question_RADI
        || quesStr == WD::Spec_Question_HEIG
        || quesStr == WD::Spec_Question_BDIA)
    {
        bool bA = false;
        bool bMaxA = false;
        std::string strA = node.getAttribute("Answer").toString(&bA);
        std::string strMaxA = node.getAttribute("Maxanswer").toString(&bMaxA);
        if (bA && bMaxA)
        {
            if (strMaxA.empty())
                return strA;

            if (deduplicate && (strA == strMaxA))
                return strA;
            else
                return strA + "," + strMaxA;
        }
        else if (bA)
        {
            return strA;
        }
        else if (bMaxA)
        {
            return strMaxA;
        }
        else
        {
            WD::SetValueToBooleanPtr(bOk, false);
            return "";
        }
    }

    return node.getAttribute("Tanswer").toString(bOk);
}
bool SetAnswerStr(WD::WDNode& node, const std::string& answerStr, std::optional<std::string> parentQuestionStr, bool bUseAnswerAsMaxanswer)
{
    std::string quesStr = "";
    if (parentQuestionStr)
    {
        quesStr = parentQuestionStr.value();
    }
    else if (node.parent() != nullptr)
    {
        bool bTOk = false;
        auto ret = GetQuestionStr(*(node.parent()), &bTOk);
        if (bTOk)
            quesStr = ret;
    }

    if (quesStr == WD::Spec_Question_PBOR
        || quesStr == WD::Spec_Question_DIAM
        || quesStr == WD::Spec_Question_ANGL
        || quesStr == WD::Spec_Question_RADI
        || quesStr == WD::Spec_Question_HEIG
        || quesStr == WD::Spec_Question_BDIA)
    {
        auto strs = WD::StringSplit(answerStr, ",");
        switch (strs.size())
        {
        case 0:
        {
            bool bA = node.setAttribute("Answer", std::string(""));
            bool bMaxA = node.setAttribute("Maxanswer", std::string(""));
            return bA && bMaxA;
        }
        break;
        case 1:
        {
            bool bA = node.setAttribute("Answer", strs[0]);
            bool bMaxA = false;
            if (bUseAnswerAsMaxanswer)
                bMaxA = node.setAttribute("Maxanswer", strs[0]);
            else
                bMaxA = node.setAttribute("Maxanswer", std::string(""));
            return bA && bMaxA;
        }
        break;
        case 2:
        {
            bool bA = node.setAttribute("Answer", strs[0]);
            bool bMaxA = node.setAttribute("Maxanswer", strs[1]);
            return bA && bMaxA;
        }
        break;
        default:
        {
            std::array<std::string, 2> names = { "Answer" , "Maxanswer" };
            size_t nameIdx = 0;
            for (const auto& str : strs)
            {
                if (nameIdx >= names.size())
                    break;
                if (str.empty())
                    continue;
                node.setAttribute(names[nameIdx], str);
                nameIdx++;
            }
            return nameIdx == 2;
        }
        break;
        }
    }
    else
    {
        return node.setAttribute("Tanswer", answerStr);
    }
}

WDNode::SharedPtr GetClassificationByTAnswer(WDNode::SharedPtr pParent, const std::string& tAnswerValue)
{
    if (pParent == nullptr)
        return nullptr;
    for (auto pChild : pParent->children())
    {
        if (pChild == nullptr)
            continue;
        if (!CheckTAnswer(*pChild, tAnswerValue))
            continue;
        return pChild;
    }
    return nullptr;
}

/*****************************************管道专业接口********************************************/

WDNode::Nodes GetSPCONodes(WD::WDNode::SharedPtr pSELENode)
{
    if (pSELENode == nullptr)
        return {};
    // 递归次公称直径的SELE节点, 每次只拿第一个子节点，直到找到SPCO节点
    using SPCONodes = std::vector<WD::WDNode::SharedPtr>;
    SPCONodes spcoNodes;
    WD::WDNode::RecursionHelpterFirstLeaf(*pSELENode
        , [&spcoNodes](WD::WDNode& node)
        {
            if (node.isType("SPCO"))
                spcoNodes.push_back(WD::WDNode::ToShared(&node));
        });

    return spcoNodes;
}

WDNode::Nodes QueryPipeComponentSPCOs(WD::WDNode::SharedPtr pSPECNode
    , const std::string& pipeComType
    , const WD::StringVector& bores)
{
    if (pSPECNode == nullptr)
        return WDNode::Nodes();

    if (bores.empty())
        return WDNode::Nodes();

    // 在SPEC类型节点的子节点中查找 对应管件类型的 SELE节点
    auto pTypeSELENode = GetClassificationByTAnswer(pSPECNode, pipeComType);
    if (pTypeSELENode == nullptr)
        return WDNode::Nodes();

    // 将公称直径转换为 int 处理
    std::vector<int> iBores;
    iBores.reserve(bores.size());
    bool bOk = false;
    for (const auto& bore : bores) 
    {
        int iBore = FromString<int>(bore, &bOk);
        if (!bOk)
            continue;
        iBores.push_back(iBore);
    }
    if (iBores.size() != bores.size())
    {
        assert(false);
        return WDNode::Nodes();
    }

    // 根据指定的公称直径列表,依次查询
    auto pRetSELENode = pTypeSELENode;
    for (auto iBore: iBores)
    {
        pRetSELENode = GetClassificationByAnswer(pRetSELENode, iBore);
        if (pRetSELENode == nullptr)
            break;
    }
    if (pRetSELENode == nullptr)
        return WDNode::Nodes();

    // 再获取结果节点下的所有SPCO节点
    return GetSPCONodes(pRetSELENode);
}

WD::WDNode::SharedPtr CreatePipeComponentNode(WD::WDCore& app
    , WD::WDNode::SharedPtr pParentBranchNode
    , const std::string& pipeComType
    , const WD::StringVector& bores
    , WD::WDNode::SharedPtr pNextComponentNode
    , const std::string& presetName)
{
    // 校验分支数据有效性
    if (pParentBranchNode == nullptr || !pParentBranchNode->isType("BRAN"))
        return nullptr;

    // 管线等级
    auto pPSpecNode = pParentBranchNode->getAttribute("Pspec").toNodeRef().refNode();
    // 保温等级
    auto pISpecNode = pParentBranchNode->getAttribute("Ispec").toNodeRef().refNode();
    // 伴热等级
    auto pTSpecNode = pParentBranchNode->getAttribute("Tspec").toNodeRef().refNode();

    // 查询管件元件等级节点
    auto pSPCONode = QueryPipeComponentSPCO(pPSpecNode, pipeComType, bores);
    if (pSPCONode == nullptr)
    {
        //!TODO: 提示等级查询失败
        assert(false);
        return nullptr;
    }
    // 创建管件节点
    auto pComNode   = app.getBMDesign().create(pParentBranchNode
        , pipeComType
        , pNextComponentNode
        , presetName);
    if (pComNode == nullptr)
    {
        assert(false);
        return nullptr;
    }
    // 设置保温等级
    pComNode->setAttribute("Ispec", WDBMNodeRef(pISpecNode));
    // 设置伴热等级
    pComNode->setAttribute("Tspec", WDBMNodeRef(pTSpecNode));
    // 设置管件的元件等级
    pComNode->setAttribute("Spref", WDBMNodeRef(pSPCONode));

    // 触发更新
    pComNode->triggerUpdate();

    return pComNode;
}

WD::WDNode::Nodes PipeComponentKits(WD::WDNode::SharedPtr pNode) 
{
    WD::WDNode::Nodes result;
    if (pNode == nullptr)
        return result;
    auto pBran = pNode->parent();
    if (pBran == nullptr || !pBran->isType("BRAN"))
        return result;

    static constexpr const char* strVALV     = "VALV";
    static constexpr const char* strINST     = "INST";
    static constexpr const char* strPCOM     = "PCOM";
    static constexpr const char* strFILT     = "FILT";
    static constexpr const char* strFLAN     = "FLAN";
    static constexpr const char* strGASK     = "GASK";
    static constexpr const char* strFBLI     = "FBLI";

    using PrevsNexts = std::pair<std::set<std::string_view>, std::set<std::string_view>>;
    static std::map<std::string_view, PrevsNexts> tMap = {
        // VALV
        {strVALV, PrevsNexts({strGASK}, {strGASK})}
        // INST
        , {strINST, PrevsNexts({strGASK}, {strGASK})}
        // PCOM
        , {strPCOM, PrevsNexts({strGASK}, {strGASK})}
        // FILT
        , {strFILT, PrevsNexts({strGASK}, {strGASK})}
        // FLAN
        , {strFLAN, PrevsNexts({strGASK}, {strGASK})}
        // GASK
        , {strGASK, PrevsNexts({strVALV, strINST, strPCOM, strFILT, strFLAN}
            , {strVALV, strINST, strPCOM, strFILT, strFLAN, strFBLI})}
        // FBLI
        , {strFBLI, PrevsNexts({strGASK}, {strGASK})}
    };

    // 先从套件列表中查找, 如果未查找到, 则不做套件选择
    auto type = pNode->type();
    auto fItr = tMap.find(type);
    if (fItr == tMap.end())
        return result;

    // 确定当前管件在子列表中的索引
    int idx = -1;
    const auto& children = pBran->children();
    for (size_t i = 0; i < children.size(); ++i)
    {
        if (children[i] == nullptr)
            continue;
        if (children[i] == pNode)
        {
            idx = static_cast<int>(i);
            break;
        }
    }
    if (idx == -1)
        return result;

    result.push_back(pNode);
    // 根据索引向前查找
    {
        auto pPrevSets = &(fItr->second.first);
        for (int i = idx - 1; i >= 0; --i)
        {
            auto prevType = children[i]->type();
            if (pPrevSets->find(prevType) == pPrevSets->end())
                break;
            result.push_back(children[i]);
            auto tFItr = tMap.find(prevType);
            if (tFItr == tMap.end())
                break;
            pPrevSets = &(tFItr->second.first);
        }
    }
    // 根据索引向后查找
    {
        auto pNextSets = &(fItr->second.second);
        for (int i = idx + 1; i < children.size(); ++i)
        {
            auto nextType = children[i]->type();
            if (pNextSets->find(nextType) == pNextSets->end())
                break;
            result.push_back(children[i]);
            auto tFItr = tMap.find(nextType);
            if (tFItr == tMap.end())
                break;
            pNextSets = &(tFItr->second.first);
        }
    }

    return result;
}

/*****************************************管道专业界面辅助函数********************************************/

std::string UpdateBoreCombobox(WD::WDNode::SharedPtr pSPECNode
    , QComboBox& cbb
    , const std::string& preBore)
{
    cbb.clear();

    if (pSPECNode == nullptr)
        return "";

    // 获取当前SPEC标准等级下tube类型等级节点
    int currentIndex = -1;
    for (const auto& pNodeTube : pSPECNode->children())
    {
        if (pNodeTube == nullptr)
            continue;
        if(!pNodeTube->isType("SELE"))
            continue;
        if (!WD::WDBMDPipeUtils::CheckTAnswer(*pNodeTube, "TUBE"))
            continue;
        for (auto pBoreNode : pNodeTube->children()) 
        {
            if (pBoreNode == nullptr)
                continue;
            auto ansStr = pBoreNode->getAttribute("Answer").toString();
            if (ansStr.empty())
                continue;

            QString text = QString::fromUtf8(ansStr.c_str());

            int idx = cbb.count();

            cbb.addItem(text);

            if (preBore == ansStr)
                currentIndex = idx;
        }
    }

    if (currentIndex != -1)
    {
        cbb.setCurrentIndex(currentIndex);
        return "";
    }
    else
    {
        cbb.setCurrentIndex(0);
        return "";
    }
}


std::vector<std::vector<WDNode::WeakPtr> > CollectSELETable(WD::WDNode::SharedPtr pSELENode)
{
    std::vector<std::vector<WDNode::WeakPtr> > rData;
    if (pSELENode == nullptr)
        return rData;

    // 从pRootSELE开始，收集所有叶子节点
    WDNode::Nodes leafNodes;
    WDNode::RecursionHelpterFirstLeaf(*pSELENode
        , [&leafNodes](WDNode& node)
        {
            if (node.childCount() == 0)
                leafNodes.push_back(WDNode::ToShared(&node));
        });
    // 分配行个数
    rData.reserve(leafNodes.size());
    // 列的最大个数, 这里预估一个值，后续会根据收集的列数据重新调整，目的是减少内存分配次数
    size_t colMaxCnt = 10;
    // 一个叶子节点代表一行, 收集行数据
    for (auto pLeafNode : leafNodes)
    {
        if (pLeafNode == nullptr)
            continue;

        // 从叶子节点开始，依次收集父节点，直到 pRootSELE
        std::vector<WDNode::WeakPtr> nodes;
        nodes.reserve(colMaxCnt);
        auto pTNode = pLeafNode;
        while (pTNode != nullptr) 
        {
            nodes.push_back(pTNode);
            // 收集到SPEC节点之后就终止
            if (pTNode == pSELENode)
                break;
            pTNode = pTNode->parent();
        }
        // 逆序
        std::reverse(nodes.begin(), nodes.end());
        // 统计列最大值
        colMaxCnt = Max(colMaxCnt, nodes.size());
        // 
        rData.push_back(std::move(nodes));
    }

    return rData;
}

std::optional<size_t> UpdatePipeComponentInfoTableWidget(WDCore& core
    , WD::WDNode::SharedPtr pTypeSELENode
    , QTableWidget& tableWidget
    , const std::string& filterBore
    , bool bShowDescription
    , std::function<std::string(const std::string&)> trFunc
    , std::function<void(QTableWidgetItem& item, WDNode::SharedPtr)> itemUDataSetFunc)
{
    if (pTypeSELENode == nullptr)
        return std::nullopt;

    // 将公称直径转换为int，用于比较
    bool bBoreOk = false;
    int iBore = WD::FromString<int>(filterBore, &bBoreOk);
    if (!bBoreOk)
        return std::nullopt;

    // 根据pTypeSELENode的Tanswer属性获取类型
    std::string type = "";
    if (!pTypeSELENode->getAttribute("Tanswer").toString(type))
        return std::nullopt;

    // 清除表格
    while (tableWidget.rowCount() != 0)
    {
        int rowIndex = tableWidget.rowCount() - 1;
        for (int i = 0; i < tableWidget.columnCount(); ++i)
        {
            QTableWidgetItem* pItem = tableWidget.takeItem(rowIndex, i);
            if (pItem != nullptr)
                delete pItem;
        }
        tableWidget.removeRow(rowIndex);
    }
    tableWidget.clear();

    // 这里有个特殊点是 这些管件中，多个公称直径的挂在树上的格式基本是固定的，如果从类型节点开始，则这些管件层级依次为:
    //   异径管->公称直径->公称直径->xxx->xxx->...
    //   弯头->公称直径->公称直径->xxx->xxxx->...
    // 另外一个特殊点就是，在一个类型(比如说大小头)下,查询到的所有等级的层级(深度)是一致的，所以可以用第一个节点去生成表头
    // 在填值时，只需要把对应公称直径的节点列排除掉就可以
    auto tableData = WD::CollectSELETable(pTypeSELENode);
    if (tableData.empty() || tableData.front().empty())
        return 0;

    // 这里表格个数固定-2是因为获取到的tableData的第一列为类型列，不需要显示
    // 后续列中，有一列为公称直径与参与筛选的公称直径值一致，也不需要显示
    // 因此需要固定减去两列
    int columnCount = static_cast<int>(tableData.front().size()) - 2;
    assert(columnCount > 0);
    if (columnCount <= 0)
        return 0;
    tableWidget.setColumnCount(bShowDescription ? columnCount + 1 : columnCount);

    // 根据第一行数据生成表头, 跳过第一列(问题为类型的列)
    QStringList headers;
    bool bSkipBoreLabel = false;
    for (size_t dataCol = 1; dataCol < tableData[0].size(); ++dataCol)
    {
        auto pNode = tableData[0][dataCol].lock();
        if (pNode == nullptr || pNode->parent() == nullptr)
        {
            assert(false);
            continue;
        }
        // 根据父节点的question 来生成表头
        bool bOk = false;
        std::string quesStr = WD::GetQuestionStr(*(pNode->parent()), &bOk);
        if (!bOk)
            continue;
        // 跳过一个公称直径
        if (!bSkipBoreLabel && (quesStr == Spec_Question_PBOR))
        {
            bSkipBoreLabel = true;
            continue;
        }
        headers.push_back(QString::fromUtf8(trFunc(quesStr).c_str()));
    }

    // 加入管件描述
    if (bShowDescription)
        headers.push_back(QString::fromUtf8(trFunc("Component description").c_str()));

    // 设置表头
    tableWidget.setHorizontalHeaderLabels(headers);

    // 设置表格内容
    for (size_t dataRow = 0; dataRow < tableData.size(); ++dataRow)
    {
        // 先根据公称直径筛选当前行数据是否显示到表格中
        WD::WDNode::SharedPtr pBoreNode = nullptr;
        for (auto col : tableData[dataRow])
        {
            auto pNode = col.lock();
            if (pNode == nullptr || pNode->parent() == nullptr)
                continue;
            // 先获取父节点的Question 是否是BORE
            bool bOk = false;
            std::string quesStr = WD::GetQuestionStr(*(pNode->parent()), &bOk);
            if (!bOk)
                continue;
            if (quesStr != WD::Spec_Question_PBOR)
                continue;
            if (!WD::CheckAnswer(*pNode, iBore))
            {
                // 如果是大小头管件, 则需要两个管径都校验, 所以这里继续
                if (type == "REDU")
                    continue;
                // 其他管件只需要校验最上层管径，所以第一次校验不同过则直接break
                else
                    break;
            }
            // 校验通过，需要显示到表格中
            pBoreNode = pNode;
            break;
        }
        if (pBoreNode == nullptr)
            continue;

        // 最后一个节点应该是SPCO节点
        WD::WDNode::SharedPtr pSPCONode = tableData[dataRow].back().lock();
        if (!pSPCONode->isType("SPCO"))
        {
            assert(false);
            continue;
        }

        // 加入数据到表格中
        int tableRow = tableWidget.rowCount();
        int rowCount = tableRow + 1;
        tableWidget.setRowCount(rowCount);
        int tableCol = 0;
        // 数据的第0列是类型，所以不需要添加， 从第一列开始，并且需要将pBoreNode过滤掉不显示
        for (size_t dataCol = 1; dataCol < tableData[dataRow].size(); ++dataCol)
        {
            auto pNode = tableData[dataRow][dataCol].lock();
            if (pNode == nullptr || pNode == pBoreNode)
                continue;

            std::string str = WD::GetAnswerStr(*pNode, true, std::nullopt);
            QTableWidgetItem* pItem = new QTableWidgetItem(QString::fromUtf8(str.c_str()));
            // 设置内容居中
            pItem->setTextAlignment(Qt::AlignCenter);
            // 由用户设置item的用户数据
            itemUDataSetFunc(*pItem, pNode);
            tableWidget.setItem(tableRow, tableCol, pItem);
            tableCol++;
        }
        // 如果需要显示管件描述，最后一列还需要加入管件描述
        if (bShowDescription)
        {
            auto desc = WD::WDBMDPipeUtils::GetPipeComponentDescriptionBySPCO(core, pSPCONode);
            QString itemText = QString::fromUtf8(desc.c_str());
            QTableWidgetItem* pItem = new QTableWidgetItem(QString::fromUtf8(desc.c_str()));
            tableWidget.setItem(tableRow, tableCol, pItem);
        }
    }

    return tableWidget.rowCount();
}


/*****************************************螺栓信息统计接口********************************************/

std::string DoubleValueToString(const double& val, int cnt)
{
    if (val <= NumLimits<double>::Epsilon)
        return "0";

    char flag[1024] = { 0 };
    sprintf_s(flag, sizeof(flag), "%s%df", "%.", cnt);

    char valC[1024] = { 0 };
    sprintf_s(valC, sizeof(valC), flag, val);
    std::string valStr = std::string(valC);
    while (!valStr.empty())
    {
        if (valStr.back() == '0')
        {
            valStr.pop_back();
        }
        else
        {
            if (valStr.back() == '.')
                valStr.pop_back();
            break;
        }
    }
    return valStr;
}
bool IsValvType(const WD::WDNode& node)
{
    return node.isAnyOfType("VALV", "INST", "PCOM", "TRAP", "FILT");
}
bool IsValvHaveFlan(const WDNode& node)
{
    if (IsValvType(node))
    {
        // 判断 VALV INST PCOM 的出入口点的连接类型是否是法兰连接,即连接类型是不是F或L开头的
        auto bFlan = [](const std::string& connectType)->bool
        {
            return !connectType.empty()
                && ((connectType.front() == 'F')
                    || (connectType.front() == 'f')
                    || (connectType.front() == 'L')
                    || (connectType.front() == 'l'));
        };

        std::string arrivePointConnectType;
        std::string leavePointConnectType;
        auto pArrive = node.keyPoint(node.getAttribute("Arrive").toInt());
        if (pArrive != nullptr)
            arrivePointConnectType = pArrive->connType();

        auto pLeave = node.keyPoint(node.getAttribute("Leave").toInt()); 
        if (pLeave != nullptr)
            leavePointConnectType = pLeave->connType();

        bool bArriveIsFlan = bFlan(arrivePointConnectType);
        bool bLeaveIsFlan = bFlan(leavePointConnectType);

        return bArriveIsFlan || bLeaveIsFlan;
    }
    return false;
}
/**
* @brief 校验节点列表是否是构成需要螺栓的情况
*/
bool CheckBoltNodes(const WD::WDNode::Nodes& nodes)
{
    if (nodes.size() < 2)
        return false;
    auto& pStartNode = nodes.front();
    auto& pEndNode = nodes.back();
    if (pStartNode == nullptr || pEndNode == nullptr)
        return false;
    // 起始节点和结束节点必须是法兰或者带法兰的阀门
    if (!pStartNode->isAnyOfType("FLAN", "NOZZ", "FBLI")
        && !IsValvHaveFlan(*pStartNode))
        return false;
    if (!pEndNode->isAnyOfType("FLAN", "NOZZ", "FBLI")
        && !IsValvHaveFlan(*pEndNode))
        return false;
    auto& pFrontGask = nodes[1];
    auto& pBackGask = nodes[nodes.size() - 2];
    return pFrontGask->isType("GASK") && pBackGask->isType("GASK");
}

double GetNodeBdiameter(WDCore& core, const WDNode& node, const Site& site, bool* bSuccess)
{
    if (bSuccess != nullptr)
        *bSuccess = false;

    auto pSpcoNode = node.getAttribute("Spref").toNodeRef().refNode();
    if (pSpcoNode == nullptr)
        return 0.0;
    auto pScomNode = pSpcoNode->getAttribute("Catref").toNodeRef().refNode();
    if (pScomNode == nullptr)
        return 0.0;
    auto blRefArray = pScomNode->getAttribute("Blrfarray").toNodeRefVector();
    if (blRefArray.empty())
        return 0.0;

    // 收集当前管件的全局变量
    if (blRefArray.empty())
        return 0.0;
    double rDouble = 0.0;
    WDNode::SharedPtr blRefNode = nullptr;
    if (blRefArray.size() == 2)
    {
        switch (site)
        {
        case In:
            {
                blRefNode = blRefArray[0].refNode();
            }
            break;
        case Out:
            {
                if (blRefArray.size() > 1)
                {
                    blRefNode = blRefArray[1].refNode();
                    if (blRefNode == nullptr)
                        blRefNode = blRefArray[0].refNode();
                }
                else
                {
                    blRefNode = blRefArray[0].refNode();
                }
            }
            break;
        default:
            break;
        }
    }
    else
    {
        if (blRefArray.size() > 2)
        {
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "节点(%s[%s])的螺栓集引用数量为:%d,未处理的情况!"
                , node.name().c_str()
                , node.uuid().toString().c_str()
                , static_cast<int>(blRefArray.size()));

            LOG_INFO << info;
        }
        blRefNode = blRefArray[0].refNode();
    }
    if (blRefNode == nullptr)
        return 0.0;

    // 构建属性
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet(node);
    // 优先使用bltp节点的bDiameter
    bool bOk = false;
    for (auto& pChild : blRefNode->children())
    {
        if (pChild == nullptr)
            continue;
        if (rDouble = aGet.getAttribute(*pChild, "Bdiameter").convertToDouble(&bOk); bOk)
        {
            if (bSuccess != nullptr)
                *bSuccess = true;
            return rDouble;
        }
    }
    if (rDouble = aGet.getAttribute(*blRefNode, "Bdiameter").convertToDouble(&bOk); bOk)
    {
        if (bSuccess != nullptr)
            *bSuccess = true;
        return rDouble;
    }
    return 0.0;
}
/**
* @brief 获取节点的Btype
*/
std::string GetNodeBType(const WDNode& node, const Site& site)
{
    auto pSpcoNode = node.getAttribute("Spref").toNodeRef().refNode();
    if (pSpcoNode == nullptr)
        return "";
    auto pScomNode = pSpcoNode->getAttribute("Catref").toNodeRef().refNode();
    if (pScomNode == nullptr)
        return "";
    auto blRefArray = pScomNode->getAttribute("Blrfarray").toNodeRefVector();
    if (blRefArray.empty())
        return "";
    WDNode::SharedPtr blRefNode = nullptr;

    if (blRefArray.size() == 2)
    {
        switch (site)
        {
        case In:
            {
                blRefNode = blRefArray[0].refNode();
            }
            break;
        case Out:
            {
                if (blRefArray.size() > 1)
                {
                    blRefNode = blRefArray[1].refNode();
                    if (blRefNode == nullptr)
                        blRefNode = blRefArray[0].refNode();
                }
                else
                {
                    blRefNode = blRefArray[0].refNode();
                }
            }
            break;
        default:
            break;
        }
    }
    else
    {
        if (blRefArray.size() > 2)
        {
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "节点(%s[%s])的螺栓集引用数量为:%d,未处理的情况!"
                , node.name().c_str()
                , node.uuid().toString().c_str()
                , static_cast<int>(blRefArray.size()));

            LOG_INFO << info;
        }
        blRefNode = blRefArray[0].refNode();
    }
    if (blRefNode == nullptr)
        return "";
    auto blRefBtype = blRefNode->getAttribute("Btype").toString();
    // 优先使用bltp节点的Btype
    for (auto& pChild : blRefNode->children())
    {
        if (pChild == nullptr)
            continue;
        auto bltpBtype = pChild->getAttribute("Btype").toString();
        if (!bltpBtype.empty())
            return bltpBtype.c_str();
    }
    if (!blRefBtype.empty())
        return blRefBtype.c_str();

    return "";
}

enum CheckResult
{
    CR_Normal,
    CR_Success,
    CR_Failed
};
CheckResult CheckGaskConnNode(const WDNode& node)
{
    if (node.isAnyOfType("TEE", "ELBO"))
    {
        char info[1024] = { 0 };
        sprintf_s(info, sizeof(info), "垫片(%s[%s])的连接错误!"
            , node.name().c_str()
            , node.uuid().toString().c_str());

        LOG_INFO << info;
        return CR_Failed;
    }
    if (node.isAnyOfType("FLAN", "FBLI") || IsValvHaveFlan(node))
        return CR_Success;
    return CR_Normal;
};

WD::WDNode::Nodes GetBoltsNodesByGask(WD::WDNode& node, std::set<WDNode::SharedPtr>* pGaskNodesSet)
{
    if (!node.isType("GASK"))
        return WD::WDNode::Nodes();
    auto pBranNode = node.parent();
    if (pBranNode == nullptr)
    {
        assert(false);
        return WD::WDNode::Nodes();
    }
    // 获取垫片在分支下的下标
    std::optional<size_t> gaskIndex;
    for (size_t idx = 0; idx < pBranNode->childCount(); ++idx)
    {
        auto pChild = pBranNode->childAt(idx);
        if (pChild == nullptr)
            continue;
        if (pChild.get() == &node)
        {
            gaskIndex = idx;
            break;
        }
    }
    if (!gaskIndex)
    {
        assert(false);
        return WD::WDNode::Nodes();
    }
    // 先向前查找
    WDNode::Nodes outNodes;
    outNodes.emplace_back(WDNode::ToShared(&node));
    size_t idx = gaskIndex.value();
    bool bSuccess = false;
    while (idx > 0)
    {
        --idx;
        auto pPreNode = pBranNode->childAt(idx);
        if (pPreNode == nullptr)
            continue;
        auto ret = CheckGaskConnNode(*pPreNode);
        if (ret == CR_Failed)
            return WD::WDNode::Nodes();
        if (pPreNode->isType("GASK") && pGaskNodesSet != nullptr)
            pGaskNodesSet->emplace(pPreNode);
        outNodes.insert(outNodes.begin(), pPreNode);
        if (ret == CR_Success)
        {
            bSuccess = true;
            break;
        }
    }
    if (!bSuccess)
    {
        if (idx != 0)
            return WD::WDNode::Nodes();
        // 获取分支的头连接
        auto pHrefNode = pBranNode->getAttribute("Href").toNodeRef().refNode();
        if (pHrefNode == nullptr)
            return WD::WDNode::Nodes();
        if (pHrefNode->isAnyOfType("TEE", "ELBO"))
        {
            char text[1024] = { 0 };
            sprintf_s(text, sizeof(text), "垫片(%s[%s])的前向连接错误!"
                , node.name().c_str()
                , node.uuid().toString().c_str());
            LOG_INFO << text;
            return WD::WDNode::Nodes();
        }
        else if (pHrefNode->isType("NOZZ"))
        {
            outNodes.insert(outNodes.begin(), pHrefNode);
            bSuccess = true;
        }
        else if (pHrefNode->isType("BRAN"))
        {
            // 处理头连接是分支的情况
            auto refNodeHrefNode = pHrefNode->getAttribute("Href").toNodeRef().refNode();
            auto execNode = [&](const int& index) -> bool
            {
                auto pHrefChild = pHrefNode->childAt(index);
                if (pHrefChild == nullptr)
                    return false;
                auto ret = CheckGaskConnNode(*pHrefChild);
                if (ret == CR_Failed)
                    return true;
                if (pHrefChild->isType("GASK") && pGaskNodesSet != nullptr)
                    pGaskNodesSet->emplace(pHrefChild);
                outNodes.insert(outNodes.begin(), pHrefChild);
                if (ret == CR_Success)
                {
                    bSuccess = true;
                    return true;
                }
                return false;
            };
            if (refNodeHrefNode == pBranNode)
            {
                for (size_t hrefIdx = 0; hrefIdx < pHrefNode->childCount(); ++hrefIdx)
                    if (execNode(static_cast<int>(hrefIdx)))
                        break;
            }
            else
            {
                for (int hrefIdx = static_cast<int>(pHrefNode->childCount()) - 1; hrefIdx >= 0; --hrefIdx)
                    if (execNode(hrefIdx))
                        break;
            }
        }
    }
    if (!bSuccess)
        return WD::WDNode::Nodes();
    bSuccess = false;
    idx = gaskIndex.value();
    while (idx < pBranNode->childCount() - 1)
    {
        ++idx;
        auto pNexNode = pBranNode->childAt(idx);
        if (pNexNode == nullptr)
            continue;
        auto ret = CheckGaskConnNode(*pNexNode);
        if (ret == CR_Failed)
            return WD::WDNode::Nodes();
        if (pNexNode->isType("GASK") && pGaskNodesSet != nullptr)
            pGaskNodesSet->emplace(pNexNode);
        outNodes.emplace_back(pNexNode);
        if (ret == CR_Success)
        {
            bSuccess = true;
            break;
        }
    }
    if (!bSuccess)
    {
        if (idx != pBranNode->childCount() - 1)
            return WD::WDNode::Nodes();
        // 获取分支的尾连接
        auto pTrefNode = pBranNode->getAttribute("Tref").toNodeRef().refNode();
        if (pTrefNode == nullptr)
            return WD::WDNode::Nodes();
        if (pTrefNode->isAnyOfType("TEE", "ELBO"))
        {
            char text[1024] = { 0 };
            sprintf_s(text, sizeof(text), "垫片(%s[%s])的后向连接错误!"
                , node.name().c_str()
                , node.uuid().toString().c_str());
            LOG_INFO << text;
            return WD::WDNode::Nodes();
        }
        else if (pTrefNode->isType("NOZZ"))
        {
            outNodes.emplace_back(pTrefNode);
            bSuccess = true;
        }
        else if (pTrefNode->isType("BRAN"))
        {
            // 处理尾连接是分支的情况
            auto refNodeHrefNode = pTrefNode->getAttribute("Href").toNodeRef().refNode();
            auto execNode = [&](const int& index) -> bool
            {
                auto pHrefChild = pTrefNode->childAt(index);
                if (pHrefChild == nullptr)
                    return false;
                auto ret = CheckGaskConnNode(*pHrefChild);
                if (ret == CR_Failed)
                {
                    bSuccess = false;
                    return true;
                }
                if (pHrefChild->isType("GASK") && pGaskNodesSet != nullptr)
                    pGaskNodesSet->emplace(pHrefChild);
                outNodes.emplace_back(pHrefChild);
                if (ret == CR_Success)
                {
                    bSuccess = true;
                    return true;
                }
                return false;
            };
            if (refNodeHrefNode == pBranNode)
            {
                for (size_t hrefIdx = 0; hrefIdx < pTrefNode->childCount(); ++hrefIdx)
                    if (execNode(static_cast<int>(hrefIdx)))
                        break;
            }
            else
            {
                for (int hrefIdx = static_cast<int>(pTrefNode->childCount()) - 1; hrefIdx >= 0; --hrefIdx)
                    if (execNode(hrefIdx))
                        break;
            }
        }
    }
    if (!bSuccess)
        return WD::WDNode::Nodes();

    return outNodes;
}
BoltKitInfo GetBoltNode(WDCore& core, const WDNode& start, const WDNode& end)
{
    BoltKitInfo boltNode;
    // 开始阀门/法兰节点的元件
    auto pStartSpcoNode = start.getAttribute("Spref").toNodeRef().refNode();
    // 结束阀门/法兰节点的元件
    auto pEndSpcoNode = end.getAttribute("Spref").toNodeRef().refNode();

    if (pStartSpcoNode == nullptr && pEndSpcoNode == nullptr)
        return boltNode;

    WDNode::SharedPtr pBSpecNode = nullptr;
    {
        WDNode::SharedPtr pSSPecNode = nullptr;
        if (pStartSpcoNode != nullptr)
        {
            pSSPecNode = pStartSpcoNode->parent();
            while (pSSPecNode != nullptr)
            {
                if (pSSPecNode->isType("SPEC"))
                    break;
                pSSPecNode = pSSPecNode->parent();
            }
        }
        WDNode::SharedPtr pESPecNode = nullptr;
        if (pEndSpcoNode != nullptr)
        {
            pESPecNode = pEndSpcoNode->parent();
            while (pESPecNode != nullptr)
            {
                if (pESPecNode->isType("SPEC"))
                    break;
                pESPecNode = pESPecNode->parent();
            }
        }
        if (pSSPecNode == nullptr && pESPecNode == nullptr)
        {
            assert(false);
            return boltNode;
        }
        if (pSSPecNode != nullptr && pESPecNode != nullptr && pSSPecNode != pESPecNode)
        {
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "螺栓两侧法兰/阀门(%s[%s] 和 %s[%s])等级SPEC节点不同!"
                , start.name().c_str()
                , start.uuid().toString().c_str()
                , end.name().c_str()
                , end.uuid().toString().c_str());
            LOG_INFO << info;
        }
        // 仅当前面的节点不是法兰节点且后面的节点是法兰节点时优先使用后面节点的Bspec
        if (!start.isType("FLAN") && end.isType("FLAN"))
        {
            if (pESPecNode != nullptr)
                pBSpecNode = pESPecNode->getAttribute("Bspec").toNodeRef().refNode();
            if (pBSpecNode == nullptr && pSSPecNode != nullptr)
                pBSpecNode = pSSPecNode->getAttribute("Bspec").toNodeRef().refNode();
        }
        else
        {
            if (pSSPecNode != nullptr)
                pBSpecNode = pSSPecNode->getAttribute("Bspec").toNodeRef().refNode();
            if (pBSpecNode == nullptr && pESPecNode != nullptr)
                pBSpecNode = pESPecNode->getAttribute("Bspec").toNodeRef().refNode();
        }
    }
    if (pBSpecNode == nullptr)
        return boltNode;

    double boltDia = 0.0;
    {
        auto startDia = GetNodeBdiameter(core, start, Out);
        auto endDia = GetNodeBdiameter(core, end, In);
        if (startDia == 0.0 && endDia == 0.0)
        {
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "螺栓两侧法兰/阀门(%s[%s] 和 %s[%s])的螺栓直径值无效!"
                , start.name().c_str()
                , start.uuid().toString().c_str()
                , end.name().c_str()
                , end.uuid().toString().c_str());
            LOG_INFO << info;
            return boltNode;
        }
        if (startDia != endDia)
        {
            // 如果两个法兰面节点的螺栓直径不一样,优先使用FLAN节点的螺栓直径,如果都不是FLAN节点,则使用两者的更大值
            if (start.isType("FLAN"))
                boltDia = startDia;
            else if (end.isType("FLAN"))
                boltDia = endDia;
            else
                boltDia = std::max(startDia, endDia);

            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "螺栓两侧法兰/阀门(%s[%s] 和 %s[%s])的螺栓直径值不同!"
                , start.name().c_str()
                , start.uuid().toString().c_str()
                , end.name().c_str()
                , end.uuid().toString().c_str());
            LOG_INFO << info;
        }
        else
        {
            boltDia = startDia;
        }
    }

    WDNode::SharedPtr pSeleNode = nullptr;
    for (auto& pSeleChild : pBSpecNode->children())
    {
        if (pSeleChild == nullptr || !pSeleChild->isType("SELE"))
            continue;
        auto tAnswer = pSeleChild->getAttribute("Tanswer").toString();
        if (_stricmp(tAnswer.c_str(), "BOLT") != 0)
            continue;
        for (auto& pBoreChild : pSeleChild->children())
        {
            if (pBoreChild == nullptr || !pBoreChild->isType("SELE"))
                continue;
            auto answer = pBoreChild->getAttribute("Answer").toString();
            if (answer.empty())
                continue;
            bool bOk = false;
            auto tmpValue = FromString<double>(answer, &bOk);
            if (!bOk)
                continue;

            if (tmpValue != boltDia)
                continue;

            pSeleNode = pBoreChild;
            break;
        }
        if (pSeleNode != nullptr)
            break;
    }
    if (pSeleNode == nullptr)
        return boltNode;
    // 螺母的tAnswer和bSel的值
    std::string bNutType = "NUT";
    std::string bNutSele = "W";
    // 垫片的tAnswer和bSel的值
    std::string bWashType = "WASH";
    std::string bWashSele = "W";
    // 螺栓的tAnswer和bSel的值
    std::string bStudType = "STUD";
    std::string bStudSele = "W";

    std::string preBType = GetNodeBType(start, Out);
    std::string bType = GetNodeBType(end, In);
    if ((preBType == "CAP" && bType == "CAP")
        || (preBType == "CAP" && (bType == "BOLT" || bType.empty()))
        || ((preBType == "BOLT" || preBType.empty()) && bType == "CAP"))
    {
        bNutType = "NUT";
        bNutSele = "A";

        bWashType = "";
        bWashSele = "";

        bStudType = "CAP";
        bStudSele = "A";
    }

    for (auto& pChild : pSeleNode->children())
    {
        if (pChild == nullptr)
            continue;
        // 这里暂时写死特殊处理华东院的情况 华东院的数据到这一层时直接已经是SPCO节点了,这里根据文本答案判断是螺母,垫片还是螺栓的SPCO

        if (pChild->isType("SPCO"))
        {
            auto tAnswer = pChild->getAttribute("Tanswer").toString();
            if (tAnswer.empty())
                continue;
            if (_stricmp(tAnswer.c_str(), bNutType.c_str()) == 0)
                boltNode.pNutNode = pChild;
            else if (_stricmp(tAnswer.c_str(), bWashType.c_str()) == 0)
                boltNode.pWashNode = pChild;
            else if (_stricmp(tAnswer.c_str(), bStudType.c_str()) == 0)
                boltNode.pStudNode = pChild;
        }

        if (!pChild->isType("SELE"))
            continue;

        auto tAnswer = pChild->getAttribute("Tanswer").toString();
        if (tAnswer.empty())
            continue;
        if (_stricmp(tAnswer.c_str(), bNutType.c_str()) == 0)
        {
            for (auto& pSpco : pChild->children())
            {
                if (!pSpco->isType("SPCO"))
                    continue;
                tAnswer = pSpco->getAttribute("Tanswer").toString();
                if (tAnswer.empty())
                    continue;
                if (_stricmp(tAnswer.c_str(), bNutSele.c_str()) == 0)
                    boltNode.pNutNode = pSpco;
            }
        }
        else if (_stricmp(tAnswer.c_str(), bWashType.c_str()) == 0)
        {
            for (auto& pSpco : pChild->children())
            {
                if (!pSpco->isType("SPCO"))
                    continue;
                tAnswer = pSpco->getAttribute("Tanswer").toString();
                if (tAnswer.empty())
                    continue;
                if (_stricmp(tAnswer.c_str(), bWashSele.c_str()) == 0)
                    boltNode.pWashNode = pSpco;
            }
        }
        else if (_stricmp(tAnswer.c_str(), bStudType.c_str()) == 0)
        {
            for (auto& pSpco : pChild->children())
            {
                if (!pSpco->isType("SPCO"))
                    continue;
                tAnswer = pSpco->getAttribute("Tanswer").toString();
                if (tAnswer.empty())
                    continue;
                if (_stricmp(tAnswer.c_str(), bStudSele.c_str()) == 0)
                    boltNode.pStudNode = pSpco;
            }
        }
    }
    return boltNode;
}
double GetNodeLength(WDCore& core, const WD::WDNode& node, int ptNumber)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet(node);
    if (node.isType("NOZZ"))
    {
        // 构建属性
        auto pCatrefNode = node.getAttribute("Catref").toNodeRef().refNode();
        if (pCatrefNode == nullptr)
            return 0.0;
        if (pCatrefNode->isType("SPCO"))
            pCatrefNode = pCatrefNode->getAttribute("Catref").toNodeRef().refNode();

        bool bOk = false;
        auto value = aGet.getAttribute(*pCatrefNode, "PARAM", 4).convertToDouble(&bOk);
        if (bOk)
            return value;

        char info[1024] = { 0 };
        sprintf_s(info, sizeof(info), "管嘴(%s[%s])的法兰厚度获取失败!"
            , node.name().c_str()
            , node.uuid().toString().c_str());
        LOG_INFO << info;
        return 0.0;
    }

    auto pSprefNode = node.getAttribute("Spref").toNodeRef().refNode();
    if (pSprefNode == nullptr)
        return 0.0;

    auto pCatrefNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
    if (pCatrefNode == nullptr)
        return 0.0;

    auto pDtrefNode = pCatrefNode->getAttribute("Dtref").toNodeRef().refNode();
    if (pDtrefNode == nullptr)
        return 0.0;

    bool bUseFlTh = true;
    auto pDetRefNode = pSprefNode->getAttribute("Detref").toNodeRef().refNode();
    if (pDetRefNode != nullptr && pDetRefNode->getAttribute("Skey").toString() == "RAFL")
        bUseFlTh = false;

    std::string key = "FLTH";
    // 这里暂时特殊处理INST
    if (bUseFlTh || node.isType("INST"))
    {
        for (auto& pChild : pDtrefNode->children())
        {
            if (pChild == nullptr)
                continue;
            if (pChild->getAttribute("Dkey").toString() != key)
                continue;

            bool bOk = false;
            auto value = aGet.getAttribute(*pChild, "Pproperty").convertToDouble(&bOk);
            if (bOk)
                return value;

            value = aGet.getAttribute(*pChild, "Dproperty").convertToDouble(&bOk);
            if (bOk)
                return value;

            assert(false);
            return 0.0;
        }
    }
    // 阀门作为起点或终点时没有找到FLTH的特殊处理
    // 1.通过FLTH取,如果可以拿到,就是阀门法兰厚度.(大多都有这个变量) (出入口阀门法兰厚度不一致时没有FLTH)
    // 2.没有FLTH时
    //  1)参数个数 > 9,取PARAM9(p1点) 和 PARAM11(p2点)
    //  2)参数个数 <= 9且 >7 时,取 PARAM7
    // 如果参数个数 > 9 但是 < 11 或者 < 7 将法兰厚度置为0然后日志报错
    if (IsValvType(node))
    {
        const WD::WDKeyPoint* pPoint = node.keyPoint(ptNumber);
        if (pPoint == nullptr)
        {
            assert(false);
            return 0.0;
        }

        // 构建属性
        auto vec = pCatrefNode->getAttribute("Param").toStringVector();
        if (vec.size() > 9)
        {
            if (vec.size() < 11)
            {
                char info[1024] = { 0 };
                sprintf_s(info, sizeof(info), "阀门(%s[%s])的参数个数大于9小于11,法兰厚度置0处理"
                    , node.name().c_str()
                    , node.uuid().toString().c_str());
                LOG_INFO << info;
                return 0.0;
            }
            if (pPoint->numb() == 1)
            {
                bool bOk = false;
                auto value = aGet.getAttribute(*pCatrefNode, "PARAM", 9).convertToDouble(&bOk);
                if (bOk)
                    return value;
            }
            else if (pPoint->numb() == 2)
            {
                bool bOk = false;
                auto value = aGet.getAttribute(*pCatrefNode, "PARAM", 11).convertToDouble(&bOk);
                if (bOk)
                    return value;
            }
        }
        // 取不到 PARAM9 和 PARAM11 时默认取 PARAM7
        else if (vec.size() > 7)
        {
            bool bOk = false;
            auto value = aGet.getAttribute(*pCatrefNode, "PARAM", 7).convertToDouble(&bOk);
            if (bOk)
                return value;
        }
        else
        {
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "阀门(%s[%s])的参数个数小于7,法兰厚度置0处理"
                , node.name().c_str()
                , node.uuid().toString().c_str());
            LOG_INFO << info;
            return 0.0;
        }
        char info[1024] = { 0 };
        sprintf_s(info, sizeof(info), "阀门(%s[%s])的参数获取失败,请检查数据是否正确!"
            , node.name().c_str()
            , node.uuid().toString().c_str());
        LOG_INFO << info;
    }
    return 0;
}
double GetBoltLength(WDCore& core, const WDNode::Nodes& nodes, const WDNode& boltNode)
{
    if (nodes.size() <= 2)
        return 0.0;

    auto& pStart = nodes.front();
    auto& pEnd = nodes.back();
    if (pStart == nullptr || pEnd == nullptr)
        return 0.0;
    double length = GetNodeLength(core, *pStart, pStart->getAttribute("Leave").toInt()) 
        + GetNodeLength(core, *pEnd, pEnd->getAttribute("Arrive").toInt());
    if (nodes.size() == 3)
    {
        auto& pGask = nodes[1];
        assert(pGask != nullptr && pGask->isType("GASK"));
        if (pGask != nullptr && pGask->isType("GASK"))
        {
            auto pArrive = pGask->keyPoint(pGask->getAttribute("Arrive").toInt());
            auto pLeave = pGask->keyPoint(pGask->getAttribute("Leave").toInt());
            if (pArrive != nullptr && pLeave != nullptr)
                length += DVec3::Distance(pArrive->position, pLeave->position);
        }
    }
    else
    {
        auto& pFrontGask = nodes[1];
        auto& pBackGask = nodes[nodes.size() - 2];
        assert(pFrontGask != nullptr && pFrontGask->isType("GASK") && pBackGask != nullptr && pBackGask->isType("GASK"));
        if (pFrontGask != nullptr && pFrontGask->isType("GASK") && pBackGask != nullptr && pBackGask->isType("GASK"))
        {
            auto pArrive = pFrontGask->keyPoint(pFrontGask->getAttribute("Arrive").toInt());
            auto pLeave = pBackGask->keyPoint(pBackGask->getAttribute("Leave").toInt());
            if (pArrive != nullptr && pLeave != nullptr)
            {
                auto arrive = pArrive->transformed(pFrontGask->globalTransform());
                auto leave = pLeave->transformed(pBackGask->globalTransform());
                length += DVec3::Distance(arrive.position, leave.position);
            }
        }
    }
    auto pBltr = boltNode.getAttribute("Bltref").toNodeRef().refNode();
    if (pBltr != nullptr)
    {
        auto bBitems = pBltr->getAttribute("Bitlength").toDoubleVector();
        double bItemval = 0.0;
        for (auto& each : bBitems)
            bItemval += each;
        length += bItemval;
        length += pBltr->getAttribute("Xtralength").toDouble();

        auto pNstd = pBltr->getAttribute("Nstdblength").toNodeRef().refNode();
        if (pNstd != nullptr)
        {
            auto bLengths = pNstd->getAttribute("Blength").toDoubleVector();
            double realLength = 0.0;
            for (auto& each : bLengths)
            {
                if (each > realLength)
                    realLength = each;
            }
            // 数组中为实际值列表,这里的逻辑是取列表中大于计算值的最小值,如果列表中没有比计算值更大的值,则使用计算值
            for (auto& each : bLengths)
            {
                if (each >= length && each < realLength)
                    realLength = each;
            }
            if (length < realLength)
                length = realLength;
        }
    }
    return length;
}

WD_NAMESPACE_END