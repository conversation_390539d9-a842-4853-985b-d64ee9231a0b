#include "UiOffsetCaptureHelpter.h"
#include <QDoubleSpinBox>
#include <QCheckBox>
#include "core/viewer/WDViewer.h"
#include "core/businessModule/WDBMBase.h"
#include "core/undoRedo/WDUndoStack.h"

class UiOffsetCaptureHelpterPrivate: public WD::WDCapturePositioningMonitor
{
public:
    UiOffsetCaptureHelpter& _d;
    std::vector<WD::DVec3>  _gPts;
    bool _pushed = false;
public:
    UiOffsetCaptureHelpterPrivate(UiOffsetCaptureHelpter& d):_d(d)
    {

    }
public:
    virtual void onActived(const WD::WDCapturePositioning& sender) override
    {
        WDUnused(sender);
        _gPts.clear();
        if (_d._pCheckBoxCapture != nullptr)
        {
            _d._pCheckBoxCapture->setChecked(true);
        }
    }
    virtual void onStart(const std::optional<WD::WDCapturePositioningResult> hover
        , const WD::WDCapturePositioning& sender) override
    {
        WDUnused(sender);

        if (hover)
        {
            _gPts.push_back(hover.value().point);
            _pushed = true;
        }
    }
    virtual void onHover(const std::optional<WD::WDCapturePositioningResult> hover
        , const WD::WDCapturePositioning& sender) override
    {
        WDUnused(sender);
        if (hover)
        {
            if (_pushed)
            {
                _gPts.back() = hover.value().point;
            }
            else
            {
                _gPts.push_back(hover.value().point);
                _pushed = true;
            }
        }
        else
        {
            if (_pushed && !_gPts.empty())
            {
                _gPts.pop_back();
                _pushed = false;
            }
        }
    }
    virtual void onEnd(const std::optional<WD::WDCapturePositioningResult> result
        , bool& existFlag
        , const WD::WDCapturePositioning& sender) override
    {
        WDUnused(sender);

        // 确定此次捕捉的最终结果
        if (result)
        {
            if (_pushed)
            {
                _pushed         = false;
                _gPts.back()    = result.value().point;
            }
            else
            {
                _gPts.push_back(result.value().point);
            }

            // 偏移量已经确定
            if (_gPts.size() == 2)
            {
                _gPts.back() = result.value().point;
                // 通过捕捉的两个点设置偏移量
                _d.setOffsetByPositions(_gPts[0], _gPts[1]);
                // 已确定偏移量，清空捕捉结果
                _gPts.clear();

                // 设置退出
                if (_d._captureTimes == UiOffsetCaptureHelpter::CT_Once)
                    existFlag = true;
            }
        }
        else
        {
            if (_pushed)
            {
                _gPts.pop_back();
                _pushed = false;
            }
        }
    }
    virtual bool onNodeFilter(WD::WDNode& node, const WD::WDCapturePositioning& sender)override
    {
        WDUnused(sender);
        if (_d._nodeFilter)
            return _d._nodeFilter(node, _d);

        return true;
    }
    virtual void onDeactived(const WD::WDCapturePositioning& sender)override
    {
        WDUnused(sender);
        _gPts.clear();
        if (_d._pCheckBoxCapture != nullptr)
        {
            _d._pCheckBoxCapture->setChecked(false);
        }
    }
};

UiOffsetCaptureHelpter::UiOffsetCaptureHelpter(WD::WDCore& core)
    :_core(core)
{
    _p = new UiOffsetCaptureHelpterPrivate(*this);
    
    _param.pMonitor                 = nullptr;
    _param.bDisplayCaptureTypeUi    = true;
    _param.bEnabledCaptureTypeUi    = true;
    _param.bDisplayCaptureOptionsUi = true;
    _param.bEnabledCaptureOptionsUi = true;
    _param.bShowKeyPointId          = true;
    _param.bShowPLineKey            = true;
    _param.bShowResultCoord         = false;

    _captureTimes   = CaptureTimes::CT_Once;
    _transform      = WD::DMat4::Identity();

    _pDSpinBoxX = nullptr;
    _pDSpinBoxY = nullptr;
    _pDSpinBoxZ = nullptr;

    _pCheckBoxCapture = nullptr;

    _pCheckBoxX = nullptr;
    _pCheckBoxY = nullptr;
    _pCheckBoxZ = nullptr;

    _offset     = WD::DVec3::Zero();
    this->updateOffsetDisplay();
}
UiOffsetCaptureHelpter::~UiOffsetCaptureHelpter()
{
    this->setDoubleSpinBoxXYZ(nullptr, nullptr, nullptr);
    this->setCheckBoxCapture(nullptr);
    this->setCheckBoxXYZ(nullptr, nullptr, nullptr);

    if (_p != nullptr)
    {
        delete _p;
        _p = nullptr;
    }
}

void UiOffsetCaptureHelpter::setTransform(const WD::DMat4& transform)
{
    if (_transform == transform)
        return;
    WD::DVec3 gOff = this->offset();
    _transform = transform;
    this->setOffset(gOff);
}
void UiOffsetCaptureHelpter::setOffset(const WD::DVec3& offset, bool bBasedTransform)
{
    WD::DVec3 off = offset;

    if (!bBasedTransform)
    {
        // 这里变换偏移只会用到 旋转和缩放信息，不会用到位置信息
        off = WD::DMat4::ToMat3(_transform).inverse() * offset;
    }

    if (_offset == off)
        return;

    WD::DVec3 oldOff = _offset;

    _offset = off;
    // 更新偏移界面显示
    this->updateOffsetDisplay();
    // 通知偏移量改变
    emit sigOffsetChanged(_offset, oldOff, _transform);
}
void UiOffsetCaptureHelpter::setOffsetByPositions(const WD::DVec3& ptA
    , const WD::DVec3& ptB
    , bool bABasedTransform
    , bool bBBasedTransform)
{
    WD::DVec3 tPtA = ptA;
    if (!bABasedTransform)
        tPtA = _transform.inverse() * tPtA;

    WD::DVec3 tPtB = ptB;
    if (!bBBasedTransform)
        tPtB = _transform.inverse() * tPtB;

    auto offset = tPtB - tPtA;
    // 设置偏移量之间判断值是否被锁定,锁定的值默认为0
    if (_pCheckBoxX != nullptr && _pCheckBoxX->isChecked())
        offset.x = _offset.x;
    if (_pCheckBoxY != nullptr && _pCheckBoxY->isChecked())
        offset.y = _offset.y;
    if (_pCheckBoxZ != nullptr && _pCheckBoxZ->isChecked())
        offset.z = _offset.z;

    this->setOffset(offset, true);
}
WD::DVec3 UiOffsetCaptureHelpter::offset(bool bBasedTransform) const
{
    if (bBasedTransform)
    {
        return _offset;
    }
    else
    {
        // 这里变换偏移只会用到 旋转和缩放信息，不会用到位置信息
        return WD::DMat4::ToMat3(_transform) * _offset;
    }
}

void UiOffsetCaptureHelpter::exit(bool bResetUi)
{
    if (_pCheckBoxCapture != nullptr)
    {
        if (_pCheckBoxCapture->isChecked())
        {
            // 直接调用槽函数来关闭捕捉工具
            this->slotCheckBoxCaptureClicked(false);
            // 设置checked状态
            _pCheckBoxCapture->setChecked(false);
        }
    }
    if (bResetUi)
    {
        if (_pCheckBoxX != nullptr)
            _pCheckBoxX->setChecked(false);
        if (_pCheckBoxY != nullptr)
            _pCheckBoxY->setChecked(false);
        if (_pCheckBoxZ != nullptr)
            _pCheckBoxZ->setChecked(false);
        _offset = WD::DVec3::Zero();
        updateOffsetDisplay();
    }
}

void UiOffsetCaptureHelpter::applyOffsetToNode(WD::WDNode::SharedPtr pNode
    , WD::WDUndoStack* pUndoStack
    , NoticeUndoCommandExecution notice) const
{
    if (pNode == nullptr)
        return;

    WD::DVec3 off = this->offset();
    if (off.lengthSq() <= WD::NumLimits<float>::Epsilon)
        return;

    WD::DVec3 oldGPos = pNode->globalTranslation();

    if (pUndoStack == nullptr)
    {
        pNode->move(off);

        pNode->triggerUpdate();
        _core.needRepaint();
    }
    else
    {
        WD::WDNode::WeakPtr wNode = pNode;
        auto pCmd = WD::WDBMBase::MakeMoveCommand({pNode}, off);
        pCmd->setNoticeAfterRedo([this, notice, wNode](const WD::WDUndoCommand& sender)
            {
                WDUnused(sender);
                if (notice)
                    notice(wNode.lock(), *this);
            });
        pCmd->setNoticeAfterUndo([this, notice, wNode](const WD::WDUndoCommand& sender)
            {
                WDUnused(sender);
                if (notice)
                    notice(wNode.lock(), *this);
            });
        pUndoStack->push(pCmd);
    }
}

void UiOffsetCaptureHelpter::setDoubleSpinBoxXYZ(QDoubleSpinBox* pDSpinBoxX
    , QDoubleSpinBox* pDSpinBoxY
    , QDoubleSpinBox* pDSpinBoxZ)
{
    if (_pDSpinBoxX != pDSpinBoxX)
    {
        if (_pDSpinBoxX != nullptr)
            disconnect(_pDSpinBoxX, SIGNAL(editingFinished()), this, SLOT(slotDSpinBoxValueChanged()));
        _pDSpinBoxX = pDSpinBoxX;
        if (_pDSpinBoxX != nullptr)
            connect(_pDSpinBoxX, SIGNAL(editingFinished()), this, SLOT(slotDSpinBoxValueChanged()));
    }

    if (_pDSpinBoxY != pDSpinBoxY)
    {
        if (_pDSpinBoxY != nullptr)
            disconnect(_pDSpinBoxY, SIGNAL(editingFinished()), this, SLOT(slotDSpinBoxValueChanged()));
        _pDSpinBoxY = pDSpinBoxY;
        if (_pDSpinBoxY != nullptr)
            connect(_pDSpinBoxY, SIGNAL(editingFinished()), this, SLOT(slotDSpinBoxValueChanged()));
    }

    if (_pDSpinBoxZ != pDSpinBoxZ)
    {
        if (_pDSpinBoxZ != nullptr)
            disconnect(_pDSpinBoxZ, SIGNAL(editingFinished()), this, SLOT(slotDSpinBoxValueChanged()));
        _pDSpinBoxZ = pDSpinBoxZ;
        if (_pDSpinBoxZ != nullptr)
            connect(_pDSpinBoxZ, SIGNAL(editingFinished()), this, SLOT(slotDSpinBoxValueChanged()));
    }
}
void UiOffsetCaptureHelpter::setCheckBoxCapture(QCheckBox* pCheckBoxCapture)
{
    if (_pCheckBoxCapture == pCheckBoxCapture)
        return;

    if (_pCheckBoxCapture != nullptr)
        disconnect(_pCheckBoxCapture, SIGNAL(clicked(bool)), this, SLOT(slotCheckBoxCaptureClicked(bool)));

    _pCheckBoxCapture = pCheckBoxCapture;

    if (_pCheckBoxCapture != nullptr)
        connect(_pCheckBoxCapture, SIGNAL(clicked(bool)), this, SLOT(slotCheckBoxCaptureClicked(bool)));
}
void UiOffsetCaptureHelpter::setCheckBoxXYZ(QCheckBox* pCheckBoxX
    , QCheckBox* pCheckBoxY
    , QCheckBox* pCheckBoxZ)
{
    if (_pCheckBoxX != pCheckBoxX)
    {
        if (_pCheckBoxX != nullptr)
            disconnect(_pCheckBoxX, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxXYZStateChanged(int)));
        _pCheckBoxX = pCheckBoxX;
        if (_pCheckBoxX != nullptr)
            connect(_pCheckBoxX, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxXYZStateChanged(int)));
    }

    if (_pCheckBoxY != pCheckBoxY)
    {
        if (_pCheckBoxY != nullptr)
            disconnect(_pCheckBoxY, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxXYZStateChanged(int)));
        _pCheckBoxY = pCheckBoxY;
        if (_pCheckBoxY != nullptr)
            connect(_pCheckBoxY, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxXYZStateChanged(int)));
    }

    if (_pCheckBoxZ != pCheckBoxY)
    {
        if (_pCheckBoxZ != nullptr)
            disconnect(_pCheckBoxZ, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxXYZStateChanged(int)));
        _pCheckBoxZ = pCheckBoxZ;
        if (_pCheckBoxZ != nullptr)
            connect(_pCheckBoxZ, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxXYZStateChanged(int)));
    }
}

const std::vector<WD::DVec3>& UiOffsetCaptureHelpter::capturePoints() const
{
    return _p->_gPts;
}

void UiOffsetCaptureHelpter::slotDSpinBoxValueChanged()
{
    const QDoubleSpinBox* pSender = qobject_cast<const QDoubleSpinBox*>(QObject::sender());
    if (pSender == nullptr)
        return;

    WD::DVec3 tPos = this->offset(true);
    if (pSender == _pDSpinBoxX && _pDSpinBoxX != nullptr)
    {
        tPos.x = _pDSpinBoxX->value();
        this->setOffset(tPos, true);
    }
    else if (pSender == _pDSpinBoxY && _pDSpinBoxY != nullptr)
    {
        tPos.y = _pDSpinBoxY->value();
        this->setOffset(tPos, true);
    }
    else if (pSender == _pDSpinBoxZ && _pDSpinBoxZ != nullptr)
    {
        tPos.z = _pDSpinBoxZ->value();
        this->setOffset(tPos, true);
    }
    else
    {
        assert(false);
    }
}
void UiOffsetCaptureHelpter::slotCheckBoxXYZStateChanged(int state)
{
    WDUnused(state);
    const QCheckBox* pSender = qobject_cast<const QCheckBox*>(QObject::sender());
    if (pSender == nullptr)
        return;

    if (pSender == _pCheckBoxX && _pDSpinBoxX != nullptr)
    {
        _pDSpinBoxX->setReadOnly(_pCheckBoxX->isChecked());
    }
    else if (pSender == _pCheckBoxY && _pDSpinBoxY != nullptr)
    {
        _pDSpinBoxY->setReadOnly(_pCheckBoxY->isChecked());
    }
    else if (pSender == _pCheckBoxZ && _pDSpinBoxZ != nullptr)
    {
        _pDSpinBoxZ->setReadOnly(_pCheckBoxZ->isChecked());
    }
    else
    {
        assert(false);
    }
}
void UiOffsetCaptureHelpter::slotCheckBoxCaptureClicked(bool bChecked)
{
    if (bChecked)
    {
        WD::WDCapturePositioningParam param = _param;
        param.pMonitor = this->_p;
        _core.viewer().capturePositioning().activate(param);
    }
    else
    {
        _core.viewer().capturePositioning().deativate();
    }
}

void UiOffsetCaptureHelpter::updateOffsetDisplay()
{
    if (_pDSpinBoxX != nullptr)
    {
        assert(_offset.x > _pDSpinBoxX->minimum()
            && _offset.x < _pDSpinBoxX->maximum()
            && "位置的X分量超出了界面设置的显示范围，请考虑调整界面的显示范围!");
        _pDSpinBoxX->blockSignals(true);
        _pDSpinBoxX->setValue(_offset.x);
        _pDSpinBoxX->blockSignals(false);
    }
    if (_pDSpinBoxY != nullptr)
    {
        assert(_offset.y > _pDSpinBoxY->minimum()
            && _offset.y < _pDSpinBoxY->maximum()
            && "位置的Y分量超出了界面设置的显示范围，请考虑调整界面的显示范围!");
        _pDSpinBoxY->blockSignals(true);
        _pDSpinBoxY->setValue(_offset.y);
        _pDSpinBoxY->blockSignals(false);
    }
    if (_pDSpinBoxZ != nullptr)
    {
        assert(_offset.z > _pDSpinBoxZ->minimum()
            && _offset.z < _pDSpinBoxZ->maximum()
            && "位置的Z分量超出了界面设置的显示范围，请考虑调整界面的显示范围!");
        _pDSpinBoxZ->blockSignals(true);
        _pDSpinBoxZ->setValue(_offset.z);
        _pDSpinBoxZ->blockSignals(false);
    }
}