#include "PositionExplicitlyDialog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/viewer/WDViewer.h"
#include "core/material/WDDrawHelpter.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMClaimMgr.h"

WD_NAMESPACE_BEGIN

PositionExplicitlyRenderObject::PositionExplicitlyRenderObject(PositionExplicitlyDialog& d) 
    : WDRenderObject(RL_Scene), _d(d)
{
    _textRender.setRenderType(WDText2DRender::RT_BillBoard);
    _lineMat.setColor(Color::white);
    _lineMat.addStates({
          WDRenderStateLineWidth::MakeShared(2.0f) // 线宽
        , WDRenderStateLineStipple::Get(WDRenderStateLineStipple::DashLine) //虚线样式
        , WDRenderStateDepthTest::MakeShared(false) // 深度测试
        });
}
void PositionExplicitlyRenderObject::updateAabb(WDContext& context, const WDScene&)
{
    WDUnused(context);
}
void PositionExplicitlyRenderObject::update(WDContext& context, const WDScene&)
{
    WDUnused(context);
}
void PositionExplicitlyRenderObject::render(WDContext& context, const WDScene&)
{
    _textRender.reset();

    auto nodes = _d._listHelpter.getSelectedNodes();
    if (nodes.empty())
        return;

    DVec3 ptOri = DVec3::Zero();
    if (!nodes.front().expired())
        ptOri = nodes.front().lock()->globalTranslation();
    FVec3 fPtOri    = FVec3(ptOri);
    // 原点标签
    _textRender.add(L"O", fPtOri, Color::yellow);

    DVec3 ptNew     = _d._positionCaptureHelpter.position();
    if (WD::DVec3::DistanceSq(ptOri, ptNew) > NumLimits<float>::Epsilon)
    {
        FVec3 fPtNew = FVec3(ptNew);
        // 新位置标签
        _textRender.add(L"P", fPtNew, Color::yellow);
        // 绘制连线
        WDMesh lineMesh;
        lineMesh.setPositions({ fPtOri , fPtNew });
        WDInstance inst;
        inst._color = Color(255,255,0,200);
        inst._local = FMat4::Identity();
        WDPrimitiveSet pri = WDPrimitiveSet::FromData(0, 2, WDPrimitiveSet::PT_Lines);

        WDDrawHelpter::Guard dg(context, _lineMat);
        dg.drawInstance({ inst }, lineMesh, pri);
    }

    _textRender.render(context, false);
}

WD_NAMESPACE_END


static auto TrF(const char* str, const char* cxtStr = "PositionExplicitlyDialog")
{
    return WD::WDTs(cxtStr, str);
};


PositionExplicitlyDialog::PositionExplicitlyDialog(WD::WDCore& core, QWidget *parent)
	: QDialog(parent)
    , _core(core)
    , _selectWRTHelpter(core, "Design")
    , _positionCaptureHelpter(core)
    , _listHelpter(core)
    , _renderObject(*this)
{
	ui.setupUi(this);

    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    _selectWRTHelpter.setLineEdit(ui.lineEditWRT);
    _selectWRTHelpter.setCheckBox(ui.checkBoxWRT);

    _listHelpter.setLineEdit(ui.lineEditCE);
    _listHelpter.setMenuButton(ui.pushButtonMenu, [](const std::string& key)-> std::string
        {
            return WD::WDTs("PositionExplicitlyDialog", key);
        });
    _listHelpter.setRefreshButton(ui.pushButtonRefresh);

    _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CT_Repeat);
    _positionCaptureHelpter.setDoubleSpinBoxXYZ(
        ui.doubleSpinBoxPositionX
        , ui.doubleSpinBoxPositionY
        , ui.doubleSpinBoxPositionZ
    );
    _positionCaptureHelpter.setCheckBoxCapture(ui.checkBoxCapture);
    _positionCaptureHelpter.setCheckBoxXYZ(
        ui.checkBoxPositionX
        , ui.checkBoxPositionY
        , ui.checkBoxPositionZ
    );

    _datumTexts.push_back(std::make_pair(0, "Origin"));
    for (const auto& dt: _datumTexts)
    {
        ui.comboBoxDatum->addItem(QString::fromUtf8(dt.second.c_str()), dt.first);
    }

    this->retranslateUi();

    connect(&_listHelpter, &UiListSelectHelpter::sigTargetChanged, [this]()
        {
            // 设置位置显示
            auto nodes = _listHelpter.getSelectedNodes();
            if (nodes.empty())
                return ;
            if (!nodes.front().expired())
                _positionCaptureHelpter.setPosition(nodes.front().lock()->globalTranslation());
            // 更新浏览轴
            this->updateBrowseAxis(nodes, _selectWRTHelpter.node());
            _core.needRepaint();
        });

    connect(&_selectWRTHelpter
        , SIGNAL(sigCurrentNodeChanged(WD::WDNode *, WD::WDNode *))
        , this
        , SLOT(slotWRTCurrentNodeChanged(WD::WDNode*, WD::WDNode*)));


    connect(&_positionCaptureHelpter
        , &UiPositionCaptureHelpter::sigPositionChanged
        , [this](const WD::DVec3& , const WD::DVec3& , const WD::DMat4& )
        {
            _core.needRepaint();
        });

    connect(ui.pushButtonOk
        , SIGNAL(clicked())
        , this
        , SLOT(slotPushButtonOkClicked()));

    connect(ui.pushButtonCancel
        , SIGNAL(clicked())
        , this
        , SLOT(close()));
}
PositionExplicitlyDialog::~PositionExplicitlyDialog()
{
    this->setRenderObjectActivation(false);

    this->updateBrowseAxis({}, nullptr);
}

void PositionExplicitlyDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    // 设置节点
    if (_selectWRTHelpter.node() == nullptr)
    {
        _selectWRTHelpter.setNodeUseTheRootNode();
    }
    else
    {
        _selectWRTHelpter.setNode(_selectWRTHelpter.node());//目的是触发信号
    }
    // 更新浏览轴
    this->updateBrowseAxis(_listHelpter.getSelectedNodes(), _selectWRTHelpter.node());
    // 激活绘制对象
    this->setRenderObjectActivation(true);
    _listHelpter.setCurrent(false);
    _core.needRepaint();
}
void PositionExplicitlyDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
    // 更新浏览轴(隐藏)
    this->updateBrowseAxis({}, nullptr);
    // 取消激活绘制对象
    this->setRenderObjectActivation(false);
    // 退出捕捉状态
    _positionCaptureHelpter.exit(true);
    // 退出节点选择状态
    //_selectWRTHelpter.exit();
    _core.needRepaint();
}

void PositionExplicitlyDialog::slotPushButtonOkClicked()
{
    auto nodes = _listHelpter.getSelectedNodes();
    if (nodes.empty())
    {
        WD_ERROR(TrF("Invalid CE"));
        return;
    }
    // 申领对象
    WD::WDBMClaimMgr::AttrDatas datas;
    datas.reserve(nodes.size());
    for (auto pWNode : nodes)
    {
        auto pNode = pWNode.lock();
        if (pNode == nullptr)
            continue;
        datas.emplace_back(WD::WDBMClaimMgr::AttrData(pNode, {"Position"}));
    }
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(datas, bCancelMd))
        return;
    if (bCancelMd)
        return;
    for (auto node : nodes)
    {
        if (node.expired())
            continue;
        // 应用位置信息到节点
        _positionCaptureHelpter.applyPositionToNode(node.lock(), &(_core.undoStack())
            , [this](WD::WDNode::SharedPtr pNode, const UiPositionCaptureHelpter& sender)
            {
                WDUnused(sender);
                this->onUndoCommandExecution(pNode); 
            });
    }

    // 更新浏览轴
    this->updateBrowseAxis(nodes, _selectWRTHelpter.node());
    _core.needRepaint();
}

void PositionExplicitlyDialog::slotWRTCurrentNodeChanged(WD::WDNode* pCurrNode, WD::WDNode* pPrevNode)
{
    WDUnused(pPrevNode);
    if (this->isHidden())
        return;
    // 设置位置显示transform
    if (pCurrNode != nullptr)
        _positionCaptureHelpter.setTransform(pCurrNode->globalTransform());
    // 更新浏览轴
    this->updateBrowseAxis(_listHelpter.getSelectedNodes(), _selectWRTHelpter.node());
    _core.needRepaint();
}

void PositionExplicitlyDialog::updateBrowseAxis(const UiListSelectHelpter::Nodes& nodes, WD::WDNode::SharedPtr pWRTNode)
{
    static constexpr const char* name = "UiComDesignEquiPosition.PositionExplicitlyDialog";
    if (nodes.empty())
    {
        _core.viewer().browseAxisMgr().put(name);
        return;
    }

    // 考虑将第一个节点作为辅助线显示的参考
    auto pCENode = nodes.front();
    if (pCENode.expired())
    {
        _core.viewer().browseAxisMgr().put(name);
        return;
    }

    auto& axis = _core.viewer().browseAxisMgr().get(name);
    // 更新浏览轴变换
    WD::DMat4 matTRS = WD::DMat4::Identity();
    if (pWRTNode != nullptr)
        matTRS = pWRTNode->globalRSTransform();

    matTRS[3].setXyz(pCENode.lock()->globalTranslation());

    axis.setAxisColor(WD::WDBrowseAxisTransform::ADF_X, WD::Color(255, 0, 0, 150));
    axis.setAxisColor(WD::WDBrowseAxisTransform::ADF_Y, WD::Color(0, 255, 0, 150));
    axis.setAxisColor(WD::WDBrowseAxisTransform::ADF_Z, WD::Color(0, 0, 255, 150));
    axis.setTransform(matTRS);
}
void PositionExplicitlyDialog::setRenderObjectActivation(bool bActived)
{\
    if (bActived)
        _core.scene().addRenderObject(&_renderObject);
    else
        _core.scene().removeRenderObject(&_renderObject);

}

void PositionExplicitlyDialog::onUndoCommandExecution(WD::WDNode::SharedPtr pNode)
{
    WDUnused(pNode);
    // 触发重绘
    _core.needRepaint();
    // 更新界面内容
    if (!this->isVisible())
        return;
    auto nodes = _listHelpter.getSelectedNodes();
    // 更新浏览轴
    this->updateBrowseAxis(nodes, _selectWRTHelpter.node());
    // 设置位置显示
    if (!nodes.empty() && !nodes.front().expired())
        _positionCaptureHelpter.setPosition(nodes.front().lock()->globalTranslation());
    // 设置位置显示transform
    if (_selectWRTHelpter.node() != nullptr)
        _positionCaptureHelpter.setTransform(_selectWRTHelpter.node()->globalTransform());
}

void PositionExplicitlyDialog::retranslateUi()
{
    Trs("PositionExplicitlyDialog"
        , static_cast<QDialog*>(this)
        , ui.labelCurrentNode
        , ui.labelDatum
        , ui.comboBoxDatum
        , ui.groupBoxPosition
        , ui.checkBoxCapture
        , ui.labelWRT
        , ui.pushButtonOk
        , ui.pushButtonCancel);
}
