#include "PipeBulkEditDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/message/WDMessage.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/typeMgr/WDBMAttrValue.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/businessModule/WDBDBase.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include <QButtonGroup>
#include <qcolordialog.h>
#include "CommonSelectDialog.h"
#include <qstyleditemdelegate.h>
#include <QPainter>
#include "core/viewer/WDViewer.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/businessModule/catalog/WDBMCatalog.h"

// 用于匹配保温的等级标准
static constexpr const char* Insu_SPEC_Stext = "INSU";
static constexpr const char* Insu_SPEC_Stext1 = "INSUL";
// 用于匹配伴热的等级标准
static constexpr const char* Trac_SPEC_Stext = "TRACE";
// 用于匹配管径的问题文本
static constexpr const char* Question_Bore = "PBOR";
// 管子类型等级节点答案
static constexpr const char* Sele_Tube  =   "TUBE";


/**
 * @brief 处理附点类型数据的字符串->去除多余的0
*/
void HandleDoubleString(std::string& str)
{
    if (str.empty())
        return;
    if (str.find('.') == std::string::npos)
        return;
    while(!str.empty())
    {
        if (str.back() == '0')
        {
            str.pop_back();
            continue;
        }

        if (str.back() == '.')
        {
            str.pop_back();
            return;
        }
    }
};

WD_NAMESPACE_USE

Q_DECLARE_METATYPE(PipeBulkEditDialog::NodeInfo);

void  MarkRenderObject::addRenderNode(const WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;
    auto flags = pNode->flags();
    pNode->setCustomHighlightColor(highlightColor);
    flags.addFlag(WD::WDNode::F_CustomHightlight);
    pNode->setFlags(flags);
    RenderInfo info;
    info.pNode = pNode;
    info.pRender = new WDText2DRender();
    _renderInfos.emplace_back(info);
    _bNeedUpdate = true;
}

void MarkRenderObject::clear()
{
    for (auto& each : _renderInfos)
    {
        auto pNode = each.pNode.lock();
        if (pNode == nullptr)
            continue;
        auto flags = pNode->flags();
        flags.removeFlag(WD::WDNode::F_CustomHightlight);
        pNode->setFlags(flags);

        auto pRender = each.pRender;
        if (pRender != nullptr)
        {
            delete pRender;
            pRender = nullptr;
        }
    }
    _renderInfos.clear();
    _bNeedUpdate = true;
}
void MarkRenderObject::updateAabb(WDContext& , const WDScene& )
{

}
void MarkRenderObject::update(WDContext&, const WDScene&)
{
    if (!_bNeedUpdate)
        return;
    for (auto& each : _renderInfos)
    {
        auto pNode = each.pNode.lock();
        auto pRender = each.pRender;
        if (pNode == nullptr || pNode->name().empty())
            continue;
        auto string = stringToWString(pNode->name());
        pRender->add(string                     // 文本
            , FVec3(pNode->globalTranslation()) // 文本位置
            , Color::white                      // 文本颜色
            , 10                                // 文本尺寸(世界坐标)
            , WDText2DRender::HA_Center         // 水平对齐方式
            , WDText2DRender::VA_Center         // 垂直对齐方式
            , FVec2(1.0f, 2.0f)                 // 水平(spacing.x())以及垂直(spacing.y())间距
        );
    }
    _bNeedUpdate = false;
}
void MarkRenderObject::render(WDContext& context, const WDScene&)
{
    for (auto& each : _renderInfos)
    {
        auto pNode = each.pNode.lock();
        auto pRender = each.pRender;
        if (pNode == nullptr || !pNode->flags().hasFlag(WDNode::F_InTheScene) || pRender == nullptr)
            continue;
        pRender->render(context);
    }
}

// 管件节点编辑command基类
class ComsNodeEditCommand : public WDUndoCommand
{
public:
    struct CommondInfo
    {
        WDNode::WeakPtr pNode;
        struct ValueInfo
        {
            std::string attributeName;
            WDBMAttrValue oldValue;
            WDBMAttrValue newValue;
        };
        using ValueInfos = std::vector<ValueInfo>;
        ValueInfos vals;
    };
    using CommondInfos = std::vector<CommondInfo>;
    WD::WDNode::WeakPtr pParent;
public:
    ComsNodeEditCommand(CommondInfos infos = CommondInfos(), const std::string& name = "ComsNodeEditCommand")
        :WDUndoCommand(name), _infos(infos)
    {
    }
    virtual ~ComsNodeEditCommand() 
    {}
public:
    void addCommand(const CommondInfo& commandInfo)
    {
        _infos.push_back(commandInfo);
    }
    void addCommand(WDNode::SharedPtr pNode, const std::string& attributeName, const WDBMAttrValue& newValue)
    {
        if (pNode == nullptr)
            return;
        CommondInfo info;
        info.pNode = pNode;
        CommondInfo::ValueInfo valInfo;
        valInfo.attributeName = attributeName;
        valInfo.newValue = newValue;
        valInfo.oldValue = pNode->getAttribute(attributeName);
        info.vals.push_back(valInfo);
        addCommand(info);
    }
    bool empty()
    {
        for (auto& info : _infos)
        {
            if (info.pNode.lock() != nullptr && !info.vals.empty())
                return false;
        }
        return true;
    }
    void clear()
    {
        _infos.clear();
    }
public:
    inline bool checkUpdate(WD::WDCore& core)
    {
        // 申领对象
        WD::WDBMClaimMgr::AttrDatas checkDatas;
        checkDatas.reserve(_infos.size());
        for (auto& commandInfo : _infos)
        {
            auto pNode = commandInfo.pNode.lock();
            if (pNode == nullptr)
                continue;
            // 添加到校审列表中
            WD::WDBMClaimMgr::AttrNames names;
            for (auto& eachAttr : commandInfo.vals)
                names.emplace(eachAttr.attributeName);

            checkDatas.emplace_back(WD::WDBMClaimMgr::AttrData(pNode, names));
        }
        bool bCancelMdy = false;
        if (core.getBMDesign().claimMgr().checkUpdate(checkDatas, bCancelMdy) && !bCancelMdy)
            return true;

        return false;
    }
public:
    virtual void redoP() override final
    {
        auto pParentSPtr = pParent.lock();
        if (pParentSPtr != nullptr)
        {
            for (auto& each : _infos)
            {
                auto pNode = each.pNode.lock();
                if (pNode == nullptr)
                    continue;
                for (auto& val : each.vals)
                {
                    pNode->setAttribute(val.attributeName, val.newValue);
                }
            }
            pParentSPtr->triggerUpdate(true);
        }
        else
        {
            for (auto& each : _infos)
            {
                auto pNode = each.pNode.lock();
                if (pNode == nullptr)
                    continue;
                for (auto& val : each.vals)
                {
                    pNode->setAttribute(val.attributeName, val.newValue);
                }
                pNode->triggerUpdate();
            }
        }
        WDUndoCommand::redoP();
        WD::Core().needRepaint();
    }
    virtual void undoP() override final
    {
        WDUndoCommand::undoP();
        auto pParentSPtr = pParent.lock();
        if (pParentSPtr != nullptr)
        {
            for (auto& each : _infos)
            {
                auto pNode = each.pNode.lock();
                if (pNode == nullptr)
                    continue;
                for (auto& val : each.vals)
                {
                    pNode->setAttribute(val.attributeName, val.oldValue);
                }
            }
            pParentSPtr->triggerUpdate(true);
        }
        else
        {
            for (auto& each : _infos)
            {
                auto pNode = each.pNode.lock();
                if (pNode == nullptr)
                    continue;
                for (auto& val : each.vals)
                {
                    pNode->setAttribute(val.attributeName, val.oldValue);
                }
                pNode->triggerUpdate();
            }
        }
        WD::Core().needRepaint();
    }
private:
    CommondInfos _infos;
};

class HighlightDelegate : public QStyledItemDelegate
{
public:
    HighlightDelegate(QObject* parent = nullptr) : QStyledItemDelegate(parent)
    {
    }
public:
    void registColor(int id, const QColor& color, bool bUse = true)
    {
        auto itr = _colorMap.find(id);
        if (itr == _colorMap.end())
        {
            _colorMap.emplace(id, std::make_pair(color, bUse));
            return;
        }
        itr->second = std::make_pair(color, bUse);
    }
    void changeColorUsedStatu(int id, bool bUse)
    {
        auto itr = _colorMap.find(id);
        if (itr == _colorMap.end())
            return;
        itr->second.second = bUse;
    }
    void changeColor(int id, QColor color)
    {
        auto itr = _colorMap.find(id);
        if (itr == _colorMap.end())
            return;
        itr->second.first = color;
    }
    void clearColorMap()
    {
        _colorMap.clear();
    }
    void registRowId(int row, int id)
    {
        auto itr = _idMap.find(row);
        if (itr == _idMap.end())
        {
            _idMap.emplace(row, id);
            return;
        }
        itr->second = id;
    }
    std::pair<QColor, bool> findRowColor(const int row) const
    {
        auto itr = _idMap.find(row);
        if (itr != _idMap.end())
        {
            auto colorItr = _colorMap.find(itr->second);
            if (colorItr != _colorMap.end())
                return colorItr->second;
        }
        return std::make_pair(QColor(), false);
    }
    void clearIdMap()
    {
        _idMap.clear();
    }
protected:
    void paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const override
    {
        auto color = findRowColor(index.row());
        if (color.second)
        {
            if (painter != nullptr)
                painter->fillRect(option.rect, color.first);
        }
        QStyledItemDelegate::paint(painter, option, index);
    }
private:
    std::map<int, int> _idMap;
    //  <id,   <color, 是否应用颜色>>
    std::map<int, std::pair<QColor, bool>> _colorMap;
};

static QColor GetPushButtonColor(QPushButton& pushButton)
{
    auto& palette = pushButton.palette();
    return palette.color(QPalette::Button);
}


WDNode::SharedPtr GetClassificationByAnswers(WDNode& parent
    , const std::vector<std::string>& answerStrs)
{
    if (answerStrs.empty())
        return nullptr;

    WDNode::SharedPtr pTParent = WDNode::ToShared(&parent);
    for (const auto& ansStr : answerStrs)
    {
        WDNode::SharedPtr pRNode = nullptr;
        WDNode::ChildrenTraversalHelpterR(*pTParent
            , [&pRNode, &ansStr](WDNode& node)
            {
                std::string tmpAnsStr = GetAnswerStr(node, false, std::nullopt);
                if (tmpAnsStr != ansStr)
                    return false;
                pRNode = WDNode::ToShared(&node);
                return true;
            });

        if (pRNode == nullptr)
            return nullptr;

        pTParent = pRNode;
    }
    return pTParent;
}

std::vector<std::string>  GetAllAnswersBetweenParentAndChild(const WDNode& parent, const WDNode& child)
{
    std::vector<std::string> answerStrs;

    auto pNode = &child;
    while (pNode != nullptr)
    {
        if (pNode == &parent)
            break;
        std::string ansStr = GetAnswerStr(*pNode, false, std::nullopt);
        answerStrs.push_back(ansStr);
        pNode = pNode->parent().get();
    }
    if (pNode == nullptr)
    {
        assert(false && "传入的两个节点之前并无父子关系");
        return std::vector<std::string>();
    }
    std::reverse(answerStrs.begin(), answerStrs.end());

    return answerStrs;
}

WDNode::SharedPtr GetNewSpcoByOldSpcoAndNewBore(const WDNode& spco
    , const std::string boreStr)
{
    bool bOk = false;
    int iBore = FromString<int>(boreStr, &bOk);
    if (!bOk)
    {
        assert(false);
        return nullptr;
    }

    std::vector<const WDNode*> nodes;
    auto pParent = spco.parent();
    while (pParent != nullptr)
    {
        if (pParent->isType("SPEC"))
            break;
        nodes.push_back(pParent.get());
        pParent = pParent->parent();
    }
    // 理论上这个数组的内容应该是 SELE,...SELE,SPCO
    std::reverse(nodes.begin(), nodes.end());

    // 根据新的管径找到新管径的SELE节点，并且保存从这个SELE节点之后的所有Answer字符串
    std::vector<std::string> ansStrs;
    ansStrs.reserve(nodes.size());
    WDNode* pQuesBoreParent = nullptr;
    for (size_t i = 0; i < nodes.size(); ++i)
    {
        auto pNode = nodes.at(i);
        if (pNode == nullptr)
        {
            assert(false);
            return nullptr;
        }
        if (pQuesBoreParent == nullptr)
        {
            auto quesStr = GetQuestionStr(*(pNode->parent()));
            if (Question_Bore == quesStr)
            {
                pQuesBoreParent = pNode->parent().get();
                ansStrs = GetAllAnswersBetweenParentAndChild(*pNode, *(nodes.back()));
                break;
            }
        }
    }
    if (pQuesBoreParent == nullptr)
        return nullptr;

    WDNode::SharedPtr pRSELENode = nullptr;
    for (auto pBoreSELENode : pQuesBoreParent->children())
    {
        if (pBoreSELENode == nullptr)
            continue;
        // 在最上层的问题是管径的SELE中找到需要的SELE节点
        if (!CheckAnswer(*pBoreSELENode, iBore))
            continue;

        pRSELENode = pBoreSELENode;
        // 遍历答案列表在SELE节点下查找需要的SPCO节点
        for (auto& ansStr : ansStrs)
        {
            // 等级中可能会存在多层问题是管径的SELE节点,这里判断当前SELE的父节点问题是不是管径,是的话用新的管径答案替换旧的答案
            bool bCheckBore = false;
            if (pRSELENode->parent() != nullptr)
            {
                auto parentQuesStr = GetQuestionStr(*pRSELENode->parent(), &bOk);
                if (bOk && (Question_Bore == parentQuesStr))
                {
                    bCheckBore = true;
                    ansStr = boreStr;
                }
            }

            WDNode::SharedPtr pRNode = nullptr;
            WDNode::ChildrenTraversalHelpterR(*pRSELENode, [&pRNode, &ansStr](bool bCheckBore, WDNode& node)
            {
                // 校验管径
                if (bCheckBore)
                {
                    auto answer = GetAnswerStr(node, false, std::nullopt);
                    // 如果是范围值,管径答案可能是范围值,这里转成浮点数进行比较
                    if (size_t i = answer.find(','); i != std::string::npos)
                    {
                        double min = FromString<double>(std::string(answer.begin(), answer.begin() + i));
                        double max = FromString<double>(std::string(answer.begin() + i + 1, answer.end()));
                        double bore = FromString<double>(ansStr);
                        if (min <= bore && bore <= max)
                        {
                            pRNode = WDNode::ToShared(&node);
                            return true;
                        }
                        return false;
                    }
                }

                std::string tmpAnsStr = GetAnswerStr(node, false, std::nullopt);
                if (!tmpAnsStr.empty() && !ansStr.empty() && CheckAnswer(node, ansStr))
                    return false;
                pRNode = WDNode::ToShared(&node);
                return true;
            }, bCheckBore);

            pRSELENode = pRNode;

            if (pRSELENode == nullptr)
                break;
        }

        break;
    }

    if (pRSELENode == nullptr)
        return nullptr;

    WDNode::SharedPtr pFirstValidNode = nullptr;
    for (auto pSPCONode : pRSELENode->children())
    {
        if (pSPCONode == nullptr)
            continue;

        if (pFirstValidNode == nullptr)
            pFirstValidNode = pSPCONode;

        auto leftAnsStr = GetAnswerStr(*pSPCONode);
        auto rightAnsStr = GetAnswerStr(spco);
        if (leftAnsStr == rightAnsStr)
            return pSPCONode;
    }

    return pFirstValidNode;
}


PipeBulkEditDialog::PipeBulkEditDialog(WD::WDCore& core, QWidget *parent)
    : _core(core)
    , QDialog(parent)
{
    ui.setupUi(this);

    qRegisterMetaType<NodeInfo>();

    initDialog();
    retranslateUi();
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    QObject::connect(ui.pushButtonCurrentNode,  &QPushButton::clicked,  this,   &PipeBulkEditDialog::slotPushButtonCurrentNodeClicked);
    QObject::connect(ui.pushButtonApply,        &QPushButton::clicked,  this,   &PipeBulkEditDialog::slotPushButtonApplyClicked);
    QObject::connect(ui.pushButtonUndo,         &QPushButton::clicked,  this,   &PipeBulkEditDialog::slotPushButtonUndoClicked);
    QObject::connect(ui.pushButtonDismiss,      &QPushButton::clicked,  this,   &PipeBulkEditDialog::slotPushButtonDismissClicked);

    QObject::connect(ui.checkBoxInsulationSpec, &QCheckBox::toggled, this, &PipeBulkEditDialog::slotUpdateTableViewVisibleStatu);
    QObject::connect(ui.checkBoxTracingSpec,    &QCheckBox::toggled, this, &PipeBulkEditDialog::slotUpdateTableViewVisibleStatu);

    QObject::connect(ui.tableView, &QTableView::clicked, this, &PipeBulkEditDialog::slotTableViewClicked);
    QObject::connect(ui.tableView, &QTableView::customContextMenuRequested
        , this, &PipeBulkEditDialog::slotTableViewCustomContextMenuRequested);

    QObject::connect(ui.pushButtonReset,     &QPushButton::clicked, this,   &PipeBulkEditDialog::slotPushButtonResetClicked);

    QObject::connect(ui.tableView->selectionModel(), &QItemSelectionModel::selectionChanged, this, &PipeBulkEditDialog::slotTableViewSelectionsChanged);

    QObject::connect(&_modifySpecAction,            &QAction::triggered,    this,   &PipeBulkEditDialog::slotModifySpecActionTriggered);
    QObject::connect(&_modifyBoreAction,            &QAction::triggered,    this,   &PipeBulkEditDialog::slotModifyBoreActionTriggered);
    QObject::connect(&_modifyInsulationSpecAction,  &QAction::triggered,    this,   &PipeBulkEditDialog::slotModifyInsulationSpecActionTriggered);
    QObject::connect(&_modifyTracingSpecAction,     &QAction::triggered,    this,   &PipeBulkEditDialog::slotModifyTracingSpecActionTriggered);
    QObject::connect(&_selectAllAction,             &QAction::triggered,    this,   &PipeBulkEditDialog::slotSelectAllActionTriggered);

    auto buttonGroupCom = new QButtonGroup(this);
    buttonGroupCom->addButton(ui.checkBoxComponentOff);
    buttonGroupCom->addButton(ui.checkBoxComponentOn);
    auto buttonGroupTube = new QButtonGroup(this);
    buttonGroupTube->addButton(ui.checkBoxTubeOff);
    buttonGroupTube->addButton(ui.checkBoxTubeOn);
    auto buttonGroupNoSpec = new QButtonGroup(this);
    buttonGroupNoSpec->addButton(ui.checkBoxNoSpecOff);
    buttonGroupNoSpec->addButton(ui.checkBoxNoSpecOn);
    auto buttonGroupSuccess = new QButtonGroup(this);
    buttonGroupSuccess->addButton(ui.checkBoxSuccessSelectNewSpecOff);
    buttonGroupSuccess->addButton(ui.checkBoxSuccessSelectNewSpecOn);
    auto buttonGroupFailed = new QButtonGroup(this);
    buttonGroupFailed->addButton(ui.checkBoxFailedToSelectNewSpecOff);
    buttonGroupFailed->addButton(ui.checkBoxFailedToSelectNewSpecOn);
    QObject::connect(buttonGroupCom, QOverload<int, bool>::of(&QButtonGroup::buttonToggled)
        , this, &PipeBulkEditDialog::slotButtonGroupComToggled);
    QObject::connect(buttonGroupTube, QOverload<int, bool>::of(&QButtonGroup::buttonToggled)
        , this, &PipeBulkEditDialog::slotButtonGroupTubeToggled);
    QObject::connect(buttonGroupNoSpec, QOverload<int, bool>::of(&QButtonGroup::buttonToggled)
        , this, &PipeBulkEditDialog::slotButtonGroupNoSpecToggled);
    QObject::connect(buttonGroupSuccess, QOverload<int, bool>::of(&QButtonGroup::buttonToggled)
        , this, &PipeBulkEditDialog::slotButtonGroupSuccessToggled);
    QObject::connect(buttonGroupFailed, QOverload<int, bool>::of(&QButtonGroup::buttonToggled)
        , this, &PipeBulkEditDialog::slotButtonGroupFailedToggled);
    ui.checkBoxComponentOn->setChecked(true);
    ui.checkBoxTubeOn->setChecked(true);
    ui.checkBoxNoSpecOn->setChecked(true);
    ui.checkBoxSuccessSelectNewSpecOn->setChecked(true);
    ui.checkBoxFailedToSelectNewSpecOn->setChecked(true);

    QObject::connect(ui.pushButtonComponent, &QPushButton::clicked, [&]()
    {
        selectPusbuttonColor(*ui.pushButtonComponent, CI_Coms);
    });
    QObject::connect(ui.pushButtonTube, &QPushButton::clicked, [&]()
    {
        selectPusbuttonColor(*ui.pushButtonTube, CI_Tube);
    });
    QObject::connect(ui.pushButtonNoSpec, &QPushButton::clicked, [&]()
    {
        selectPusbuttonColor(*ui.pushButtonNoSpec, CI_NoSpec);
    });
    QObject::connect(ui.pushButtonSuccessSelectNewSpec, &QPushButton::clicked, [&]()
    {
        selectPusbuttonColor(*ui.pushButtonSuccessSelectNewSpec, CI_Success);
    });
    QObject::connect(ui.pushButtonFailedToSelectNewSpec, &QPushButton::clicked, [&]()
    {
        selectPusbuttonColor(*ui.pushButtonFailedToSelectNewSpec, CI_Failed);
    });
}
PipeBulkEditDialog::~PipeBulkEditDialog()
{
    auto pCurrentNode = _pNode.lock();
    if (pCurrentNode != nullptr)
    {
        if (pCurrentNode->isType("BRAN"))
        {
            pCurrentNode->observers().remove(this);
        }
        else if (pCurrentNode->isType("PIPE"))
        {
            for (auto& pChild : pCurrentNode->children())
            {
                if (pChild->isType("BRAN"))
                    pChild->observers().remove(this);
            }
        }
    }
}

void PipeBulkEditDialog::changeCurrentNode(WDNode::SharedPtr pCurrentNode)
{
    auto pPrevNode = _pNode.lock();
    if (pPrevNode != nullptr)
    {
        if (pPrevNode->isType("BRAN"))
        {
            pPrevNode->observers().remove(this);
        }
        else if (pPrevNode->isType("PIPE"))
        {
            for (auto& pChild : pPrevNode->children())
            {
                if (pChild->isType("BRAN"))
                    pChild->observers().remove(this);
            }
        }
    }
    _pNode = pCurrentNode;
    if (pCurrentNode != nullptr)
    {
        if (pCurrentNode->isType("BRAN"))
        {
            pCurrentNode->observers().push_back(this);
        }
        else if (pCurrentNode->isType("PIPE"))
        {
            for (auto& pChild : pCurrentNode->children())
            {
                if (pChild->isType("BRAN"))
                    pChild->observers().push_back(this);
            }
        }
    }
}

void PipeBulkEditDialog::slotPushButtonCurrentNodeClicked()
{
    updateDialog();
}
void PipeBulkEditDialog::slotPushButtonApplyClicked()
{
    auto pCurrNode = _pNode.lock();
    if (pCurrNode == nullptr)
        return;

    if (WD_QUESTION_T("PipeBulkEditDialog", "Sure to apply your change?") != 0)
        return;
    auto pBranSpecNode = _pBranSpecNode.lock();
    bool bForceUpdate = false;
    auto command = new ComsNodeEditCommand();
    command->pParent = pCurrNode;
    if (pBranSpecNode != nullptr)
    {
        bForceUpdate = true;
        if (pCurrNode->isType("BRAN"))
        {
            command->addCommand(pCurrNode, "Pspec", WDBMAttrValue(WDBMNodeRef(pBranSpecNode)));
        }
        else if (pCurrNode->isType("PIPE"))
        {
            for (auto& pChild : pCurrNode->children())
            {
                if (pChild->isType("BRAN"))
                    command->addCommand(pChild, "Pspec", WDBMAttrValue(WDBMNodeRef(pBranSpecNode)));
            }
        }
        else
        {
            assert(false);
        }
        _pBranSpecNode.reset();
    }
    auto pPipeSpecNode = _pPipeSpecNode.lock();
    if (pPipeSpecNode != nullptr)
    {
        bForceUpdate = true;
        if (pCurrNode->isType("BRAN"))
        {
            assert(pCurrNode->parent() != nullptr);
            if (pCurrNode->parent() != nullptr)
                command->addCommand(pCurrNode->parent(), "Pspec", WDBMAttrValue(WDBMNodeRef(pPipeSpecNode)));
        }
        else if (pCurrNode->isType("PIPE"))
        {
            command->addCommand(pCurrNode, "Pspec", WDBMAttrValue(WDBMNodeRef(pPipeSpecNode)));
        }
        else
        {
            assert(false);
        }
        _pPipeSpecNode.reset();
    }
    for (int row = 0; row < _model.rowCount(); ++row)
    {
        auto nodeInfo = getNodeInfoByIndexInTableView(row);
        auto pNode = nodeInfo.pNode.lock();
        auto prevNode = nodeInfo.pPrevNode.lock();
        if (pNode != nullptr)
        {
            auto pNewInsuNode = getInsuByIndexInTableView(row);
            auto pOldInsuNode = pNode->getAttribute("Ispec").toNodeRef().refNode();
            if (pNewInsuNode != nullptr)
            {
                if (pOldInsuNode == nullptr || pOldInsuNode->uuid() != pNewInsuNode->uuid())
                {
                    bForceUpdate = true;
                    command->addCommand(pNode, "Ispec", WDBMAttrValue(WDBMNodeRef(pNewInsuNode)));
                }
            }
            else if (pOldInsuNode != nullptr)
            {
                command->addCommand(pNode, "Ispec", WDBMAttrValue(WDBMNodeRef(nullptr)));
            }
            auto pNewTracNode = getTracByIndexInTableView(row);
            auto pOldTracNode = pNode->getAttribute("Tspec").toNodeRef().refNode();
            if (pNewTracNode != nullptr)
            {
                if (pOldTracNode == nullptr || pOldTracNode->uuid() != pNewTracNode->uuid())
                {
                    bForceUpdate = true;
                    command->addCommand(pNode, "Tspec", WDBMAttrValue(WDBMNodeRef(pNewTracNode)));
                }
            }
            else if (pOldTracNode != nullptr)
            {
                command->addCommand(pNode, "Tspec", WDBMAttrValue(WDBMNodeRef(nullptr)));
            }
            auto pNewSpecNode = getNewSpcoByIndexInTableView(row);
            auto pOldSpecNode = pNode->getAttribute("Spref").toNodeRef().refNode();
            if (pNewSpecNode != nullptr)
            {
                if (pOldSpecNode == nullptr || pOldSpecNode->uuid() != pNewSpecNode->uuid())
                {
                    bForceUpdate = true;
                    command->addCommand(pNode, "Spref", WDBMAttrValue(WDBMNodeRef(pNewSpecNode)));
                }
            }
        }
        else if (prevNode != nullptr)
        {
            auto pNewSpecNode = getNewSpcoByIndexInTableView(row);
            if (pNewSpecNode != nullptr)
            {
                if (prevNode->isType("BRAN"))
                {
                    auto pOldSpecNode = prevNode->getAttribute("Hstube").toNodeRef().refNode();
                    if (pOldSpecNode == nullptr || pOldSpecNode != pNewSpecNode)
                    {
                        bForceUpdate = true;
                        command->addCommand(prevNode, "Hstube", WDBMAttrValue(WDBMNodeRef(pNewSpecNode)));
                        command->addCommand(prevNode, "Hbore", WDBMAttrValue(getNewBoreByIndexInTableView(row)));
                    }
                    if (nodeInfo.isLastTubi)
                        command->addCommand(prevNode, "Tbore", WDBMAttrValue(getNewBoreByIndexInTableView(row)));
                }
                else
                {
                    auto pOldSpecNode = prevNode->getAttribute("Lstube").toNodeRef().refNode();
                    if (pOldSpecNode == nullptr || pOldSpecNode != pNewSpecNode)
                    {
                        bForceUpdate = true;
                        command->addCommand(prevNode, "Lstube", WDBMAttrValue(WDBMNodeRef(pNewSpecNode)));
                    }
                    if (nodeInfo.isLastTubi)
                        command->addCommand(prevNode->parent(), "Tbore", WDBMAttrValue(getNewBoreByIndexInTableView(row)));
                }
            }
        }
    }

    if (!command->checkUpdate(_core))
    {
        delete command;
        command = nullptr;
        return;
    }

    if (!command->empty())
    {
        _core.undoStack().push(command);
    }
    pCurrNode->triggerUpdate(bForceUpdate);
    updateTableView();
}
void PipeBulkEditDialog::slotPushButtonUndoClicked()
{
    if (_core.undoStack().canUndo())
    {
        _core.undoStack().undo();
    }
}
void PipeBulkEditDialog::slotPushButtonDismissClicked()
{
    this->reject();
}
void PipeBulkEditDialog::slotTableViewClicked()
{
}
void PipeBulkEditDialog::slotTableViewCustomContextMenuRequested(const QPoint &pos)
{
    auto index = ui.tableView->indexAt(pos);
    if (index.isValid())
    {
        _menu.exec(QCursor::pos());
    }
}
void PipeBulkEditDialog::slotUpdateTableViewVisibleStatu()
{
    if (_model.columnCount() == 0)
        return;
    if (ui.checkBoxInsulationSpec->isChecked())
        ui.tableView->showColumn(6);
    else
        ui.tableView->hideColumn(6);
    if (ui.checkBoxTracingSpec->isChecked())
        ui.tableView->showColumn(7);
    else
        ui.tableView->hideColumn(7);
    ui.tableView->resizeColumnsToContents();
}

void PipeBulkEditDialog::slotTableViewSelectionsChanged()
{
    _render.clear();
    auto selects = ui.tableView->selectionModel()->selectedIndexes();
    for (auto idx = 0; idx < selects.size(); ++idx)
    {
        auto row = selects[idx].row();
        auto nodeInfo = getNodeInfoByIndexInTableView(row);
        auto pNode = nodeInfo.pNode.lock();
        if (pNode == nullptr)
            continue;
        _render.addRenderNode(pNode);
    }
    _core.needRepaint();
}

void PipeBulkEditDialog::slotModifySpecActionTriggered()
{
    auto selectIndexes = ui.tableView->selectionModel()->selectedIndexes();
    if (selectIndexes.empty())
    {
        WD_WARN_T("PipeBulkEditDialog", "Please Select Item!");
        return;
    }
    auto pCurrNode = _pNode.lock();
    if (pCurrNode == nullptr)
    {
        WD_WARN_T("PipeBulkEditDialog", "CurrentNode is Null!");
        return;
    }
    auto pSpecVal = pCurrNode->getAttribute("Pspec");
    auto pSpecNodeRef = pSpecVal.data<WDBMNodeRef>();

    SpecSelectDialog selectDialog(_core, this);
    {
        WDNode::SharedPtr pOldSpecNode;
        if (pSpecNodeRef != nullptr)
        {
            pOldSpecNode = pSpecNodeRef->refNode();
            if (pOldSpecNode == nullptr)
            {
                WD_WARN_T("PipeBulkEditDialog", "CurrentNode Spec is Null!");
                return;
            }
        }
        selectDialog.setPSpec(pOldSpecNode);
    }
    if (selectDialog.exec() != QDialog::Accepted)
        return;
    auto pSpecNode = selectDialog.pSpec();
    if (pSpecNode == nullptr)
    {
        assert(false);
        return;
    }
    switch (selectDialog.type)
    {
    case SpecSelectDialog::Branch:
        {
            _pBranSpecNode = pSpecNode;
        }
        break;
    case SpecSelectDialog::Pipe:
        {
            _pBranSpecNode = pSpecNode;
            _pPipeSpecNode = pSpecNode;
        }
        break;
    default:
        break;
    }

    for (int index = 0; index < selectIndexes.size(); ++index)
    {
        int row = selectIndexes[index].row();
        auto nodeInfo = getNodeInfoByIndexInTableView(row);
        auto pNode = nodeInfo.pNode.lock();
        auto pPrevNode = nodeInfo.pPrevNode.lock();
        // 管件
        if (pNode != nullptr)
        {
            auto pOldSprefNode = pNode->getAttribute("Spref").toNodeRef().refNode();
            if (pOldSprefNode == nullptr)
            {
                assert(false);
                continue;
            }
            WDNode::SharedPtr pOldSPECNode = pOldSprefNode->parent();
            while (pOldSPECNode != nullptr)
            {
                if (pOldSPECNode->isType("SPEC"))
                    break;
                pOldSPECNode = pOldSPECNode->parent();
            }
            if (pOldSPECNode == nullptr)
            {
                assert(false);
                continue;
            }
            auto answers = GetAllAnswersBetweenParentAndChild(*pOldSPECNode, *pOldSprefNode);
            auto pNewSprefNode = GetClassificationByAnswers(*pSpecNode, answers);
            setNewSpcoByIndexToTableView(row, pNewSprefNode);
        }
        // 直管段
        else if (pPrevNode != nullptr)
        {
            std::string boreStr = _model.data(_model.index(row, 2)).toString().toLocal8Bit().data();
            auto pNewSprefNode = WDBMDPipeUtils::GetStubeBySPEC(pSpecNode, boreStr);
            setNewSpcoByIndexToTableView(row, pNewSprefNode);
        }
        else 
        {
            assert(false);
        }
    }
}
void PipeBulkEditDialog::slotModifyBoreActionTriggered()
{
    auto selectIndexes = ui.tableView->selectionModel()->selectedIndexes();
    if (selectIndexes.empty())
    {
        WD_WARN_T("PipeBulkEditDialog", "Please Select Item!");
        return;
    }
    auto pCurrNode = _pNode.lock();
    if (pCurrNode == nullptr)
    {
        WD_WARN_T("PipeBulkEditDialog", "CurrentNode is Null!");
        return;
    }
    auto pSpecVal = pCurrNode->getAttribute("Pspec");
    auto pSpecNodeRef = pSpecVal.data<WDBMNodeRef>();
    WDNode::SharedPtr pOldSpecNode;
    if (pSpecNodeRef != nullptr)
    {
        pOldSpecNode = pSpecNodeRef->refNode();
        if (pOldSpecNode == nullptr)
        {
            WD_WARN_T("PipeBulkEditDialog", "CurrentNode Spec is Null!");
            return;
        }
    }

    CommonSelectDialog dialog;
    dialog.setComboBoxListBySPECBore(pOldSpecNode);
    std::string pOldBoreStr = _model.data(_model.index(selectIndexes[0].row(), 2)).toString().toLocal8Bit().data();
    dialog.setComboBoxItemByData(QVariant(WD::FromString<int>(pOldBoreStr)));
    dialog.setTitle(WDTs("PipeBulkEditDialog", "SelectBores").c_str());
    dialog.setLabelText(WDTs("PipeBulkEditDialog", "Bores").c_str());
    if (dialog.exec() != QDialog::Accepted)
        return;
    auto& resultData = dialog.resultData;
    if (!resultData.isValid())
    {
        assert(false);
        return;
    }
    std::string boreStr = resultData.toString().toLocal8Bit().data();
    for (int index = 0; index < selectIndexes.size(); ++index)
    {
        int row = selectIndexes[index].row();
        auto pOldSprefNode = getOldSpcoByIndexInTableView(row);
        if (pOldSprefNode == nullptr)
        {
            assert(false);
            continue;
        }
        auto pSpecNode = pOldSprefNode;
        while (pSpecNode != nullptr)
        {
            if (pSpecNode->isType("SPEC"))
                break;
            pSpecNode = pSpecNode->parent();
        }
        if (pSpecNode == nullptr)
            continue;

        auto nodeInfo = getNodeInfoByIndexInTableView(row);
        auto pNode = nodeInfo.pNode.lock();
        auto pPrevNode = nodeInfo.pPrevNode.lock();
        if (pNode != nullptr)
        {
            auto pNewSprefNode = GetNewSpcoByOldSpcoAndNewBore(*pOldSprefNode, boreStr);
            setNewSpcoByIndexToTableView(row, pNewSprefNode);
        }
        else if (pPrevNode != nullptr)
        {
            auto pNewSprefNode = WDBMDPipeUtils::GetStubeBySPEC(pSpecNode, boreStr);
            setNewSpcoByIndexToTableView(row, pNewSprefNode);
        }
        else 
        {
            assert(false);
        }
    }
}
void PipeBulkEditDialog::slotModifyInsulationSpecActionTriggered()
{
    auto selectIndexes = ui.tableView->selectionModel()->selectedIndexes();
    if (selectIndexes.empty())
    {
        WD_WARN_T("PipeBulkEditDialog", "Please Select Item!");
        return;
    }
    if (insuSpecs.empty())
    {
        WD_WARN_T("PipeBulkEditDialog", "Insulation is Empty!");
        return;
    }

    CommonSelectDialog::Datas datas;
    datas.emplace_back(CommonSelectDialog::Data());
    datas.back().text = QString::fromUtf8(WD::WDTs("PipeBulkEditDialog", "None").c_str());
    for (auto& each : insuSpecs)
    {
        if (each == nullptr)
            continue;
        CommonSelectDialog::Data eachData;
        eachData.text = QString::fromUtf8(each->name().c_str());
        eachData.data.setValue<UiWeakObject>(UiWeakObject(each));
        datas.push_back(eachData);
    }
    CommonSelectDialog dialog;
    dialog.setComboBoxListByDatas(datas);

    dialog.setTitle(WDTs("PipeBulkEditDialog", "SelectInsulations").c_str());
    dialog.setLabelText(WDTs("PipeBulkEditDialog", "Insulations").c_str());
    if (dialog.exec() != QDialog::Accepted)
        return;
    auto& resultData = dialog.resultData;
    auto pInsulationNode = resultData.value<UiWeakObject>().subObject<WDNode>();

    for (int index = 0; index < selectIndexes.size(); ++index)
    {
        auto row = selectIndexes[index].row();
        setInsuByIndexToTableView(row, pInsulationNode);
    }
}
void PipeBulkEditDialog::slotModifyTracingSpecActionTriggered()
{
    auto selectIndexes = ui.tableView->selectionModel()->selectedIndexes();
    if (selectIndexes.empty())
    {
        WD_WARN_T("PipeBulkEditDialog", "Please Select Item!");
        return;
    }
    if (tracSpecs.empty())
    {
        WD_WARN_T("PipeBulkEditDialog", "Tracing is Empty!");
        return;
    }

    CommonSelectDialog::Datas datas;
    datas.emplace_back(CommonSelectDialog::Data());
    datas.back().text = QString::fromUtf8(WD::WDTs("PipeBulkEditDialog", "None").c_str());
    for (auto& each : tracSpecs)
    {
        if (each == nullptr)
            continue;
        CommonSelectDialog::Data eachData;
        eachData.text = QString::fromUtf8(each->name().c_str());
        eachData.data.setValue<UiWeakObject>(UiWeakObject(each));
        datas.push_back(eachData);
    }
    CommonSelectDialog dialog;
    dialog.setComboBoxListByDatas(datas);

    dialog.setTitle(WDTs("PipeBulkEditDialog", "SelectTracings").c_str());
    dialog.setLabelText(WDTs("PipeBulkEditDialog", "Tracings").c_str());
    if (dialog.exec() != QDialog::Accepted)
        return;
    auto& resultData = dialog.resultData;
    auto pTracingNode = resultData.value<UiWeakObject>().subObject<WDNode>();

    for (int index = 0; index < selectIndexes.size(); ++index)
    {
        auto row = selectIndexes[index].row();
        setTracByIndexToTableView(row, pTracingNode);
    }
}
void PipeBulkEditDialog::slotSelectAllActionTriggered()
{
    ui.tableView->selectAll();
}

void PipeBulkEditDialog::slotButtonGroupComToggled()
{
    _highlightDelegate->changeColorUsedStatu(CI_Coms, ui.checkBoxComponentOn->isChecked());
}
void PipeBulkEditDialog::slotButtonGroupTubeToggled()
{
    _highlightDelegate->changeColorUsedStatu(CI_Tube, ui.checkBoxTubeOn->isChecked());
}
void PipeBulkEditDialog::slotButtonGroupNoSpecToggled()
{
    _highlightDelegate->changeColorUsedStatu(CI_NoSpec, ui.checkBoxNoSpecOn->isChecked());
}
void PipeBulkEditDialog::slotButtonGroupSuccessToggled()
{
    _highlightDelegate->changeColorUsedStatu(CI_Success, ui.checkBoxSuccessSelectNewSpecOn->isChecked());
}
void PipeBulkEditDialog::slotButtonGroupFailedToggled()
{
    _highlightDelegate->changeColorUsedStatu(CI_Failed, ui.checkBoxFailedToSelectNewSpecOn->isChecked());
}

void PipeBulkEditDialog::slotPushButtonResetClicked()
{
    setPushButtonColor(*ui.pushButtonComponent,             _comDefaultColor,       CI_Coms);
    setPushButtonColor(*ui.pushButtonTube,                  _tubeDefaultColor,      CI_Tube);
    setPushButtonColor(*ui.pushButtonNoSpec,                _noSpecDefaultColor,    CI_NoSpec);
    setPushButtonColor(*ui.pushButtonSuccessSelectNewSpec,  _successDefaultColor,   CI_Success);
    setPushButtonColor(*ui.pushButtonFailedToSelectNewSpec, _failedDefaultColor,    CI_Failed);
}

void PipeBulkEditDialog::showEvent(QShowEvent *)
{
    updateDialog();
    _core.scene().addRenderObject(&_render);
}
void PipeBulkEditDialog::hideEvent(QHideEvent *)
{
    ui.tableView->selectionModel()->clearSelection();;
    _core.scene().removeRenderObject(&_render);
}

void PipeBulkEditDialog::onNodeUpdateAfter(WDNode::SharedPtr pNode)
{
    WDUnused(pNode);
    updateTableView();
    slotTableViewSelectionsChanged();
}

bool PipeBulkEditDialog::selectPusbuttonColor(QPushButton& button, ColorId id)
{
    auto currColor = GetPushButtonColor(button);
    auto color = QColorDialog::getColor(currColor, this, "ColorSelect");
    if (currColor == color)
        return false;
    setPushButtonColor(button, color, id);
    return true;
}
void PipeBulkEditDialog::setPushButtonColor(QPushButton& pushButton, const QColor& color, ColorId id)
{
    QString colorName = color.name(QColor::HexArgb);
    QString style = QString("QPushButton{background-color:%1;}").arg(colorName);
    _highlightDelegate->changeColor(id, color);
    pushButton.setStyleSheet(style);
}

void PipeBulkEditDialog::updateTableView()
{
    clearDialog();
    auto pNode = _pNode.lock();
    if (pNode == nullptr)
        return;
    // 只支持处理分支(BRAN)和管道(PIPE)节点
    if (!pNode->isAnyOfType("BRAN", "PIPE"))
        return;
    // 获取当前节点的信息
    {
        auto& mgr = _core.getBMDesign();
        char text[1024] = { 0 };
        sprintf_s(text, sizeof(text), "%s:%s", mgr.trT(pNode->type().data()).c_str(), pNode->name().c_str());
        ui.labelNodeInfo->setText(text);
    }

    // 获取当前节点的管道等级
    {
        auto& mgr = _core.getBMCatalog();
        WDNode::SharedPtr pSpecNode = pNode->getAttribute("Pspec").toNodeRef().refNode();
        char text[1024] = { 0 };
        if (pSpecNode != nullptr)
            sprintf_s(text, sizeof(text), "%s:%s", mgr.trT("SPEC").c_str(), pSpecNode->name().c_str());
        else
            sprintf_s(text, sizeof(text), "%s:", mgr.trT("SPEC").c_str());
        ui.labelSpecName->setText(text);
    }

    if (pNode->isType("BRAN"))
    {
        addBranchToTableView(*pNode);
    }
    else if (pNode->isType("PIPE"))
    {
        for (auto& pChild : pNode->children())
        {
            if (pChild == nullptr)
                continue;
            if (!pChild->isType("BRAN"))
                continue;
            addBranchToTableView(*pChild);
        }
    }

    // 获取当前总行数
    ui.labelNodeCnt->setText(QString::fromUtf8(WDTs("PipeBulkEditDialog", "Total node count:").c_str()) + QString::number(_model.rowCount()));

    slotUpdateTableViewVisibleStatu();
}
std::string GetNodeName(WDNode& node)
{
    if (!node.isNamed())
    {
        // 直接返回流水命名
        return node.name();
    }
    else
    {
        // 名称前 + 类型
        char nodeName[1024] = { 0 };
        sprintf_s(nodeName, sizeof(nodeName), "%s %s", node.type().data(), node.name().c_str());
        return nodeName;
    }
}

void PipeBulkEditDialog::addBranchToTableView(WDNode& branch)
{
    if (!branch.isType("BRAN"))
        return;
    _model.setColumnCount(13);
    WDCxtTsBg("PipeBulkEditDialog");
    _model.setHorizontalHeaderLabels({QString::fromUtf8(WDCxtTs("nodeName").c_str())
        , QString::fromUtf8(WDCxtTs("pipeComsDescription").c_str())
        , QString::fromUtf8(WDCxtTs("BoreOfP1").c_str())
        , QString::fromUtf8(WDCxtTs("BoreOfP2").c_str())
        , QString::fromUtf8(WDCxtTs("BoreOfP3").c_str())
        , QString::fromUtf8(WDCxtTs("pipeComsSpref").c_str())
        , QString::fromUtf8(_core.getBMDesign().trA("Ispec").c_str())
        , QString::fromUtf8(_core.getBMDesign().trA("Tspec").c_str())
        , QString::fromUtf8(WDCxtTs("NewPipeComsSpref").c_str())
        , QString::fromUtf8(WDCxtTs("NewPipeComsDescription").c_str())
        , QString::fromUtf8(WDCxtTs("NewBoreOfP1").c_str())
        , QString::fromUtf8(WDCxtTs("NewBoreOfP2").c_str())
        , QString::fromUtf8(WDCxtTs("NewBoreOfP3").c_str())});
    WDCxtTsEd();

    auto branchSpecNode = branch.getAttribute("Pspec").toNodeRef().refNode();
    auto& branchName = branch.srcName();
    auto pipeComs = WD::WDBMDPipeUtils::PipeComponents(WD::WDNode::ToShared(&branch));
    // 第一行为分支头管
    {
        auto row = _model.rowCount();
        _model.setRowCount(row + 1);
        // 1.节点名称
        {
            char tubiName[1024] = { 0 };
            sprintf_s(tubiName, sizeof(tubiName), "head tube of %s", branchName.c_str());
            _highlightDelegate->registRowId(row, CI_Tube);

            QVariant userData;
            NodeInfo info;
            info.pPrevNode = WDNode::ToShared(&branch);
            QStandardItem* item = new QStandardItem(tubiName);
            userData.setValue(info);
            item->setData(userData, Qt::UserRole);
            _model.setItem(row, 0, item);
        }
        auto hBore = branch.getAttribute("Hbore").toString();
        assert(!hBore.empty());
        auto pSprefNode = branch.getAttribute("Hstube").toNodeRef().refNode();
        // 2.管件描述信息
        {
            auto descVal = WD::WDBMDPipeUtils::GetPipeComponentDescriptionBySPCO(_core, pSprefNode);
            if (!descVal.empty())
            {
                _model.setData(_model.index(row, 1), QString(descVal.c_str()), Qt::EditRole);
            }
        }
        {
            // 3.p1点的管径
            _model.setData(_model.index(row, 2), QString::fromLocal8Bit(hBore.c_str()), Qt::EditRole);
            // 4.p2点的管径
            _model.setData(_model.index(row, 3), QString::fromLocal8Bit(hBore.c_str()), Qt::EditRole);
            // 4.p3点的管径
            _model.setData(_model.index(row, 4), "-", Qt::EditRole);
        }
        // 6.管件等级引用
        {
            if (pSprefNode != nullptr)
            {
                QStandardItem* item = new QStandardItem(pSprefNode->name().c_str());
                QVariant userData;
                userData.setValue(UiWeakObject(pSprefNode));
                item->setData(userData, Qt::UserRole);
                _model.setItem(row, 5, item);
            }
            else
            {
                _highlightDelegate->registRowId(row, CI_Success);
            }
        }
    }
    for (size_t idx = 0; idx < pipeComs.size(); ++idx)
    {
        const auto& pPipeComNode = pipeComs[idx];
        if (pPipeComNode == nullptr)
            continue;
        auto row = _model.rowCount();
        _model.setRowCount(row + 1);

        std::string comNodeName = GetNodeName(*pPipeComNode);
        // 1.节点名称
        {
            _highlightDelegate->registRowId(row, CI_Coms);
            QVariant userData;
            NodeInfo info;
            info.pNode = pPipeComNode;
            QStandardItem* item = new QStandardItem(comNodeName.c_str());
            userData.setValue(info);
            item->setData(userData, Qt::UserRole);
            _model.setItem(row, 0, item);
        }
        // 2.管件描述信息
        {
            auto descVal = pPipeComNode->getAttribute("PipeComponentDescription");
            auto pDescStr = descVal.data<std::string>();
            if (pDescStr != nullptr)
            {
                _model.setData(_model.index(row, 1), QString(pDescStr->c_str()), Qt::EditRole);
            }
        }
        // 记录管件的出口管径
        std::string outBoreStr;
        // 3,4,5. 管径
        {
            // 3.p1点的管径 入口点
            auto pKeyPoint1 = pPipeComNode->keyPoint(pPipeComNode->getAttribute("Arrive").toInt());
            assert(pKeyPoint1 != nullptr);
            if (pKeyPoint1 != nullptr)
            {
                std::string text = pKeyPoint1->bore();
                HandleDoubleString(text);
                _model.setData(_model.index(row, 2), QString(text.c_str()), Qt::EditRole);
            }
            // 4.p2点的管径 出口点
            auto pKeyPoint2 = pPipeComNode->keyPoint(pPipeComNode->getAttribute("Leave").toInt());
            assert(pKeyPoint2 != nullptr);
            if (pKeyPoint2 != nullptr)
            {
                outBoreStr = pKeyPoint2->bore();
                HandleDoubleString(outBoreStr);
                _model.setData(_model.index(row, 3), QString(outBoreStr.c_str()), Qt::EditRole);
            }
            // 5.p3点的管径 分支口点
            auto pKeyPoint3 = pPipeComNode->keyPoint(WD::WDBMDPipeUtils::Fork(*pPipeComNode));
            if (pKeyPoint3 != nullptr)
            {
                std::string text = pKeyPoint3->bore();
                HandleDoubleString(text);
                _model.setData(_model.index(row, 4), QString(text.c_str()), Qt::EditRole);
            }
            else
            {
                _model.setData(_model.index(row, 4), "-", Qt::EditRole);
            }
        }
        // 6.管件等级引用
        {
            WDNode::SharedPtr pRefNode = pPipeComNode->getAttribute("Spref").toNodeRef().refNode();
            if (pRefNode != nullptr)
            {
                QStandardItem* item = new QStandardItem(pRefNode->name().c_str());
                QVariant userData;
                userData.setValue(UiWeakObject(pRefNode));
                item->setData(userData, Qt::UserRole);
                _model.setItem(row, 5, item);
            }
            else
            {
                _highlightDelegate->registRowId(row, CI_NoSpec);
            }
        }
        // 7.保温等级
        setInsuByIndexToTableView(row, pPipeComNode->getAttribute("Ispec").toNodeRef().refNode());
        // 8.伴热等级
        setTracByIndexToTableView(row, pPipeComNode->getAttribute("Tspec").toNodeRef().refNode());


        row = _model.rowCount();
        _model.setRowCount(row + 1);
        // 1.节点名称
        {
            char tubiName[1024] = { 0 };
            sprintf_s(tubiName, sizeof(tubiName), "leave tube of %s", comNodeName.c_str());
            _highlightDelegate->registRowId(row, CI_Tube);
            QVariant userData;
            NodeInfo info;
            info.pPrevNode = pPipeComNode;
            // 如果是分支的最后一个管径的出口管子
            if (idx == pipeComs.size() - 1)
                info.isLastTubi = true;
            QStandardItem* item = new QStandardItem(tubiName);
            userData.setValue(info);
            item->setData(userData, Qt::UserRole);
            _model.setItem(row, 0, item);
        }
        // 管子等级为管件的出口管子等级,出口管子等级为空时用管件出口管径和分支的SPEC等级查找管子等级
        auto pTubiSprefNode = pPipeComNode->getAttribute("Lstube").toNodeRef().refNode();
        if (pTubiSprefNode == nullptr)
        {
            assert(!outBoreStr.empty());
            pTubiSprefNode = branchSpecNode->getAttribute("Hstube").toNodeRef().refNode();
        }
        // 2.管件描述信息
        {
            auto descVal = WD::WDBMDPipeUtils::GetPipeComponentDescriptionBySPCO(_core, pTubiSprefNode);
            if (!descVal.empty())
            {
                _model.setData(_model.index(row, 1), QString(descVal.c_str()), Qt::EditRole);
            }
        }
        {
            // 3.p1点的管径
            _model.setData(_model.index(row, 2), QString::fromLocal8Bit(outBoreStr.c_str()), Qt::EditRole);
            // 4.p2点的管径
            _model.setData(_model.index(row, 3), QString::fromLocal8Bit(outBoreStr.c_str()), Qt::EditRole);
            // 3.p3点的管径
            _model.setData(_model.index(row, 4), "-", Qt::EditRole);
        }
        // 6.管件等级引用
        {
            if (pTubiSprefNode != nullptr)
            {
                QStandardItem* item = new QStandardItem(pTubiSprefNode->name().c_str());
                QVariant userData;
                userData.setValue(UiWeakObject(pTubiSprefNode));
                item->setData(userData, Qt::UserRole);
                _model.setItem(row, 5, item);
            }
            else
            {
                _highlightDelegate->registRowId(row, CI_NoSpec);
            }
        }
    }
}

PipeBulkEditDialog::NodeInfo PipeBulkEditDialog::getNodeInfoByIndexInTableView(int index)
{
    auto item = _model.item(index, 0);
    if (item != nullptr)
        return item->data(Qt::UserRole).value<NodeInfo>();

    return PipeBulkEditDialog::NodeInfo();
}

WD::WDNode::SharedPtr PipeBulkEditDialog::getOldSpcoByIndexInTableView(int index)
{
    auto itemSpref = _model.item(index, 5);
    if (itemSpref != nullptr)
        return itemSpref->data(Qt::UserRole).value<UiWeakObject>().subObject<WDNode>();
    return nullptr;
}
WD::WDNode::SharedPtr PipeBulkEditDialog::getNewSpcoByIndexInTableView(int index)
{
    auto itemNewSpref = _model.item(index, 8);
    if (itemNewSpref != nullptr)
        return  itemNewSpref->data(Qt::UserRole).value<UiWeakObject>().subObject<WDNode>();
    return nullptr;
}
void PipeBulkEditDialog::setNewSpcoByIndexToTableView(int index, WD::WDNode::SharedPtr pNode)
{
    auto pOldSpecNode = getNewSpcoByIndexInTableView(index);
    if (pOldSpecNode == nullptr)
    {
        pOldSpecNode = getOldSpcoByIndexInTableView(index);
        if (pOldSpecNode == nullptr)
        {
            assert(false);
            return;
        }
    }
    auto itemNewSpref = _model.item(index, 8);
    if (itemNewSpref == nullptr)
    {
        itemNewSpref = new QStandardItem();
        _model.setItem(index, 8, itemNewSpref);
    }
    QVariant userData;
    std::string newPipeDesc;
    std::string arriveStr;
    std::string leaveStr;
    std::string forkStr;
    if (pNode == nullptr || pNode->uuid() == pOldSpecNode->uuid())
    {
        userData.setValue(UiWeakObject(nullptr));
        itemNewSpref->setText("No selection avaliable");
        _highlightDelegate->registRowId(index, CI_Failed);
    }
    else
    {
        forkStr = "-";
        userData.setValue(UiWeakObject(pNode));
        itemNewSpref->setText(QString::fromLocal8Bit(pNode->name().c_str()));
        _highlightDelegate->registRowId(index, CI_Success);
        int arriveNumber = 1;
        int leaveNumber = 2;
        int forkNumber = -1;
        auto pCurrentNode = getNodeInfoByIndexInTableView(index).pNode.lock();
        if (pCurrentNode != nullptr)
        {
            arriveNumber = pCurrentNode->getAttribute("Arrive").toInt();
            leaveNumber = pCurrentNode->getAttribute("Leave").toInt();
            forkNumber = WD::WDBMDPipeUtils::Fork(*pCurrentNode);
        }
        // 元件节点
        auto pCatrefNode = pNode->getAttribute("Catref").toNodeRef().refNode();
        // rText
        auto pDetRefNode = pNode->getAttribute("Detref").toNodeRef().refNode();
        // xText
        auto pMatTxtNode = pNode->getAttribute("Matxt").toNodeRef().refNode();
        // 构建属性
        auto aGet = _core.getBMCatalog().modelBuilder().cAttributeGet(pCurrentNode, pNode);
        // 获取新的管道描述
        std::string rText;
        std::string xText;
        if (pDetRefNode != nullptr)
            rText = aGet.getAttribute(*pDetRefNode, "Rtext").convertToString();
        if (pMatTxtNode != nullptr)
            xText = aGet.getAttribute(*pMatTxtNode, "Xtext").convertToString();
        if (!rText.empty() && !xText.empty())
            newPipeDesc = rText + " " + xText;
        else
            newPipeDesc = rText + xText;
        if (pCatrefNode != nullptr)
        {
            // 获取点集节点
            WD::WDNode::SharedPtr pPtNode = pCatrefNode->getAttribute("Ptref").toNodeRef().refNode();
            assert(pPtNode != nullptr);
            if (pPtNode != nullptr)
            {
                // 设置了新的管径
                std::map<int, WD::WDNode::SharedPtr> ptNodes;
                for (auto& pChild : pPtNode->children())
                {
                    if (pChild == nullptr)
                        continue;
                    auto number = pChild->getAttribute("Number").toInt();
                    // 编号相同取第一个
                    if (ptNodes.find(number) != ptNodes.end())
                        continue;
                    ptNodes.emplace(number, pChild);
                }
                auto numberItr = ptNodes.find(arriveNumber);
                if (numberItr != ptNodes.end())
                {
                    const auto& pTmpNode = numberItr->second;
                    if (pTmpNode != nullptr)
                        arriveStr = aGet.getAttribute(*pTmpNode, "Pbore").convertToString();
                }
                numberItr = ptNodes.find(leaveNumber);
                if (numberItr != ptNodes.end())
                {
                    const auto& pTmpNode = numberItr->second;
                    if (pTmpNode != nullptr)
                        leaveStr = aGet.getAttribute(*pTmpNode, "Pbore").convertToString();
                }
                if (forkNumber != -1)
                {
                    numberItr = ptNodes.find(forkNumber);
                    if (numberItr != ptNodes.end())
                    {
                        const auto& pTmpNode = numberItr->second;
                        if (pTmpNode != nullptr)
                            forkStr = aGet.getAttribute(*pTmpNode, "Pbore").convertToString();
                    }
                }
            }
        }
    }
    _model.setData(_model.index(index, 9),  newPipeDesc.c_str(),    Qt::EditRole);
    _model.setData(_model.index(index, 10), arriveStr.c_str(),      Qt::EditRole);
    _model.setData(_model.index(index, 11), leaveStr.c_str(),       Qt::EditRole);
    _model.setData(_model.index(index, 12), forkStr.c_str(),        Qt::EditRole);
    itemNewSpref->setData(userData, Qt::UserRole);
    slotUpdateTableViewVisibleStatu();
}

WD::WDNode::SharedPtr PipeBulkEditDialog::getInsuByIndexInTableView(int index)
{
    auto item = _model.item(index, 6);
    if (item == nullptr)
        return nullptr;

    return item->data(Qt::UserRole).value<UiWeakObject>().subObject<WDNode>();
}
void PipeBulkEditDialog::setInsuByIndexToTableView(int index, WD::WDNode::SharedPtr pNode)
{
    auto pOldNode = getTracByIndexInTableView(index);
    if (pNode != nullptr && (pOldNode != nullptr && pNode->uuid() == pOldNode->uuid()))
        return;
  
    auto item = _model.item(index, 6);
    if (item == nullptr)
    {
        item = new QStandardItem();
        _model.setItem(index, 6, item);
    }
    QVariant userData;
    userData.setValue(UiWeakObject(pNode));
    if (pNode != nullptr)
        item->setText(QString::fromLocal8Bit(pNode->name().c_str()));
    else
        item->setText("");
    item->setData(userData, Qt::UserRole);
    slotUpdateTableViewVisibleStatu();
}

WD::WDNode::SharedPtr PipeBulkEditDialog::getTracByIndexInTableView(int index)
{
    auto item = _model.item(index, 7);
    if (item == nullptr)
        return nullptr;

    return item->data(Qt::UserRole).value<UiWeakObject>().subObject<WDNode>();
}
void PipeBulkEditDialog::setTracByIndexToTableView(int index, WD::WDNode::SharedPtr pNode)
{
    auto pOldNode = getTracByIndexInTableView(index);
    if (pNode != nullptr && (pOldNode != nullptr && pNode->uuid() == pOldNode->uuid()))
        return;

    auto item = _model.item(index, 7);
    if (item == nullptr)
    {
        item = new QStandardItem();
        _model.setItem(index, 7, item);
    }
    QVariant userData;
    userData.setValue(UiWeakObject(pNode));
    if (pNode != nullptr)
        item->setText(QString::fromLocal8Bit(pNode->name().c_str()));
    else
        item->setText("");
    item->setData(userData, Qt::UserRole);
    slotUpdateTableViewVisibleStatu();
}

std::string PipeBulkEditDialog::getNewBoreByIndexInTableView(int index, bool bLeaveBoreFirst)
{
    if (bLeaveBoreFirst)
    {
        // 优先获取出口管径
        std::string boreStr = _model.data(_model.index(index, 11)).toString().toLocal8Bit().data();
        if (!boreStr.empty())
            return boreStr;
        return _model.data(_model.index(index, 10)).toString().toLocal8Bit().data();
    }
    else
    {
        // 优先获取入口管径,没有入口管径的话肯定也没有出口管径
        return _model.data(_model.index(index, 10)).toString().toLocal8Bit().data();
    }
}

void PipeBulkEditDialog::clearDialog()
{
    ui.labelNodeCnt->clear();
    ui.labelNodeInfo->clear();
    ui.labelSpecName->clear();
    _model.clear();
    _model.setRowCount(0);
}
void PipeBulkEditDialog::updateDialog()
{
    auto pCurrentNode = _core.nodeTree().currentNode();
    changeCurrentNode(pCurrentNode);
    updateTableView();
}

void PipeBulkEditDialog::updateTableViewColor()
{
    slotButtonGroupComToggled();
    slotButtonGroupTubeToggled();
    slotButtonGroupNoSpecToggled();
    slotButtonGroupSuccessToggled();
    slotButtonGroupFailedToggled();
}
PipeBulkEditDialog::ColorId PipeBulkEditDialog::getRowStatu(int row)
{
    // 先判断有没有等级
    auto pSpcoNode = getOldSpcoByIndexInTableView(row);
    if (pSpcoNode == nullptr)
        return CI_NoSpec;
    // 判断是否有设置新等级
    auto pNewSpco = getNewSpcoByIndexInTableView(row);
    if (pNewSpco != nullptr)
        return CI_Success;
    // 判断是否是等级设置失败
    if (_model.item(row, 8) != nullptr)
        return CI_Failed;
    // 判断节点是否是直管段
    auto nodeInfo = getNodeInfoByIndexInTableView(row);
    auto pNode = nodeInfo.pNode.lock();
    if (pNode != nullptr && pNode->isType("TUBI"))
        return CI_Tube;
    // 默认返回 CI_Coms
    return CI_Coms;
}
void PipeBulkEditDialog::initDialog()
{
    _highlightDelegate = new HighlightDelegate();
    _highlightDelegate->registColor(CI_Coms ,   _comDefaultColor);
    _highlightDelegate->registColor(CI_Tube,    _tubeDefaultColor);
    _highlightDelegate->registColor(CI_NoSpec,  _noSpecDefaultColor);
    _highlightDelegate->registColor(CI_Success, _successDefaultColor);
    _highlightDelegate->registColor(CI_Failed,  _failedDefaultColor);
    ui.tableView->setItemDelegate(_highlightDelegate);

    slotPushButtonResetClicked();
    ui.tableView->setContextMenuPolicy(Qt::CustomContextMenu);
    ui.tableView->verticalHeader()->hide();
    ui.tableView->setModel(&_model);
    ui.tableView->setEditTriggers(QAbstractItemView::NoEditTriggers);

    _modifySpecAction.setText(QString::fromLocal8Bit("ModifySpecification"));
    _modifyBoreAction.setText(QString::fromLocal8Bit("ModifyBore"));
    _modifyInsulationSpecAction.setText(QString::fromLocal8Bit("ModifyInsulationSpec"));
    _modifyTracingSpecAction.setText(QString::fromLocal8Bit("ModifyTracingSpec"));
    _selectAllAction.setText(QString::fromLocal8Bit("SelectAll"));
    _menu.addAction(&_modifySpecAction);
    _menu.addAction(&_modifyBoreAction);
    _menu.addAction(&_modifyInsulationSpecAction);
    _menu.addAction(&_modifyTracingSpecAction);
    _menu.addAction(&_selectAllAction);
    _selectAllAction.setShortcut(Qt::CTRL + Qt::Key_A);
    ui.tabWidget->setCurrentIndex(0);

    insuSpecs = WD::GetSPECByTEXTAttrSText(_core.getBMCatalog(), Insu_SPEC_Stext);
    WD::WDNode::Nodes insuSpecs1 = WD::GetSPECByTEXTAttrSText(_core.getBMCatalog(), Insu_SPEC_Stext1);
    insuSpecs.insert(insuSpecs.end(), insuSpecs1.begin(), insuSpecs1.end());

    tracSpecs = WD::GetSPECByTEXTAttrSText(_core.getBMCatalog(), Trac_SPEC_Stext);
}
void PipeBulkEditDialog::retranslateUi()
{
    Trs("PipeBulkEditDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonApply
        , ui.pushButtonCurrentNode
        , ui.pushButtonDismiss
        , ui.pushButtonUndo
        , ui.tabWidget
        , ui.checkBoxInsulationSpec
        , ui.checkBoxTracingSpec
        , ui.labelComponent
        , ui.labelTube
        , ui.labelNoSpec
        , ui.labelSuccessSelectNewSpec
        , ui.labelFailedToSelectNewSpec
        , ui.checkBoxComponentOff
        , ui.checkBoxComponentOn
        , ui.checkBoxFailedToSelectNewSpecOff
        , ui.checkBoxFailedToSelectNewSpecOn
        , ui.checkBoxNoSpecOff
        , ui.checkBoxNoSpecOn
        , ui.checkBoxSuccessSelectNewSpecOff
        , ui.checkBoxSuccessSelectNewSpecOn
        , ui.checkBoxTubeOff
        , ui.checkBoxTubeOn
        , ui.pushButtonReset
        , &_modifySpecAction
        , &_modifyBoreAction
        , &_modifyInsulationSpecAction
        , &_modifyTracingSpecAction
        , &_selectAllAction
    );
}