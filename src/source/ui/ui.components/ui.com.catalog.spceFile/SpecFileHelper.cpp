#include "SpecFileHelper.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"
#include <fstream>
#include "../../ui.commonLibrary/ui.commonLib.excel/QTableWidget2Excel.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include <filesystem>

bool CheckExt(const std::string& fileName, const std::string& ext)
{
    if (fileName.size() <= ext.size())
        return false;

    for (int i = 0; i < ext.size(); ++i)
    {
        if (fileName[fileName.size() - ext.size() + i] != ext[i])
            return false;
    }
    return true;
}

using CommandLine = std::pair<WD::uint, std::string>;
using CommandLineVector = std::vector<CommandLine>;
// 用空格分隔字符串
void AnalysisStringBySpace(const std::string& commandLine, WD::StringVector& outStrVec)
{
    if (commandLine.empty())
        return;
    // 解析结果
    std::smatch smatch;
    // 匹配轴规则(匹配长度至少为1的空格)
    std::regex partten("\\s{1,}");
    // 解析
    std::sregex_iterator itr(commandLine.begin(), commandLine.end(), partten);
    for (; itr != std::sregex_iterator(); ++itr)
    {
        smatch = *itr;
        const std::string& str = smatch.prefix();
        if (!str.empty())
            outStrVec.push_back(str);
    }
    if (outStrVec.empty())
    {
        outStrVec.push_back(commandLine);
        return;
    }
    const std::string& str = smatch.suffix();
    if (!str.empty())
        outStrVec.push_back(str);
}

SpecFileHelper::SpecFileHelper(WD::WDCore& core) : FileHelper(core)
{
}
bool SpecFileHelper::checkFilePath(const std::string& filePath) const
{
    if (filePath.empty())
        return false;
    if (!std::filesystem::exists(filePath))
        return false;

    // 等级文件后缀名可能是 .spec 和 .txt
    return CheckExt(filePath, ".spec") || CheckExt(filePath, ".txt");
}
bool SpecFileHelper::checkCurrentNode(WD::WDNode& node) const
{
    auto& mgr = _core.getBMCatalog();
    return mgr.findParentWithType(node, "SPEC") != nullptr;
}
bool SpecFileHelper::importFile(WD::WDNode& currentNode, const std::string& fileName)
{
    log.clear();
    if (fileName.empty())
        return false;
    if (!checkCurrentNode(currentNode) || !checkFilePath(fileName))
        return false;
    auto& mgr = _core.getBMCatalog();
    auto pParent = mgr.findParentWithType(currentNode, "SPEC");
    if (pParent == nullptr)
    {
        log.addLog("当前节点不支持挂载SPEC节点!", WD::PluginLog::L_Error);
        return false;
    }
    log.addLog(WD::PluginLog::L_Info, "开始导入文件%s!", QString::fromLocal8Bit(fileName.data()).toUtf8().data());
    // 保存一条命令及其在脚本中的行数
    // 脚本中存在 一条命令用 $ 做为换行符占据多行的情况，这里当作多行处理
    CommandLineVector commands;
    // 读取文件
    {
        std::ifstream fStream(fileName.c_str());
        if (fStream.bad())
        {
            log.addLog(WD::PluginLog::L_Error, "文件%s打开失败!", QString::fromLocal8Bit(fileName.data()).toUtf8());
            return false;
        }
        char bytes[3] = { 0 };
        fStream.read(bytes, 3);
        // 脚本文件格式为带BOM的utf8,所以文件的开头会有BOM的标记,这里判断是否有此标记,有的话则跳过标记
        bool hasBom = (bytes[0] == static_cast<char>(0xEF)) && (bytes[1] == static_cast<char>(0xBB)) && (bytes[2] == static_cast<char>(0xBF));
        if (hasBom)
            fStream.seekg(3, std::ios::beg);
        else
            fStream.seekg(0, std::ios::beg);

        int index = 0;
        // 读取到的行
        std::string strLine;
        while (std::getline(fStream, strLine))
        {
            ++index;
            // 去掉命令行开头可能存在的空格
            while (!strLine.empty() && strLine.front() == ' ')
                strLine.erase(strLine.begin());
            // 去掉命令末尾可能存在的空格
            while (!strLine.empty() && strLine.back() == ' ')
                strLine.pop_back();

            // 以 '$' 开头的行以注释行处理
            if (strLine.empty() || strLine.front() == '$')
            {
                if (!commands.empty() && commands.back().second.back() == '$')
                    commands.back().second.pop_back();
                continue;
            }

            // 添加到命令行列表中
            if (commands.empty())
            {
                commands.push_back(std::make_pair(index, strLine));
                continue;
            }
            std::string& backCmd = commands.back().second;
            // 以 '$' 结尾 表示两行连接
            if (backCmd.back() != '$')
            {
                commands.push_back(std::make_pair(index, strLine));
                continue;
            }
            //  去掉 $
            backCmd.pop_back();
            backCmd += strLine;
        }
    }
    if (commands.empty())
    {
        log.addLog(WD::PluginLog::L_Info, "文件%s内容为空!", QString::fromLocal8Bit(fileName.data()).toUtf8());
        log.addLog(WD::PluginLog::L_Info, "导入完成!");
        return true;
    }
    int i = 0;
    int refCnt = 0;
    // spec节点
    WD::WDNode::SharedPtr pSpecNode;
    // 创建节点
    {
        for (;i < commands.size(); ++i)
        {
            const auto& idx = commands[i].first;
            const auto& str = commands[i].second;
            if (!isNewCommand(str))
                continue;

            WD::StringVector vec;
            AnalysisStringBySpace(str, vec);
            if (vec.size() < 3 || ((_stricmp(vec[1].c_str(), "SPEC") != 0) && (_stricmp(vec[1].c_str(), "SPECIFICATION") != 0)))
            {
                log.addLog(WD::PluginLog::L_Error, "[%d]NEW命令错误或SPEC节点名称为空", idx);
                return false;
            }
            auto& name = vec[2];
            if (name.front() == '/')
                name.erase(name.begin());

            for (auto& pChild : pParent->children())
            {
                if (pChild == nullptr)
                    continue;
                if (pChild->name() == name)
                {
                    log.addLog(WD::PluginLog::L_Error, "[%d]节点创建失败,已有同名节点!", idx);
                    return false;
                }
            }

            pSpecNode = _core.getBMCatalog().create("SPEC", name);
            if (pSpecNode == nullptr)
            {
                log.addLog(WD::PluginLog::L_Error, "[%d]节点创建失败!", idx);
                return false;
            }
            break;
        }
        ++i;
    }
    if (pSpecNode == nullptr)
    {
        log.addLog("脚本中未找到NEW命令创建节点!");
        log.addLog(WD::PluginLog::L_Info, "导入完成!");
        return false;
    }
    // SPEC的问题固定为Type
    pSpecNode->setAttribute("Question", WD::WDBMAttrValue(std::string("TYPE")));

    struct RefAttributeData
    {
        WD::WDNode::SharedPtr pNode;
        WD::WDBMAttrDesc* pAttrDesc = nullptr;
    };
    using RefAttributeDatas = std::vector<RefAttributeData>;
    std::map<std::string, RefAttributeDatas> refAttrs;
    bool ret = true;
    // 处理属性
    {
        auto pTypeDesc = pSpecNode->getTypeDesc();
        if (pTypeDesc == nullptr)
        {
            assert(false);
            log.addLog(WD::PluginLog::L_Error, "意料之外的错误,导入中断!");
            return false;
        }
        const auto& attrDescs = pTypeDesc->attrDescs();
        for (; i < commands.size(); ++i)
        {
            const auto& idx = commands[i].first;
            const auto& str = commands[i].second;

            if (isNewCommand(str))
            {
                log.addLog(WD::PluginLog::L_Error, "[%d]出现意料之外的NEW命令,导入中断!", idx);
                ret = false;
                goto SpecFileImportFinish;
            }

            if (_stricmp(str.c_str(), "HEADING") == 0)
                break;

            WD::StringVector vec;
            AnalysisStringBySpace(str, vec);
            if (vec.size() < 2)
            {
                assert(false && "未处理的属性类型");
                continue;
            }
            const auto& attributeName = vec[0];
            const auto& attributeValue = vec[1];
            if (_stricmp(attributeName.c_str(), "Text") == 0)
            {
                std::string value = attributeValue;
                if (value.front() == '\'' && value.back() == '\'')
                {
                    value.erase(value.begin());
                    value.pop_back();
                }
                auto node = mgr.create(pSpecNode, "TEXT");
                if (node == nullptr)
                    log.addLog(WD::PluginLog::L_Error, "[%d]TEXT节点创建失败!", idx);
                else
                    node->setAttribute("Stext", WD::WDBMAttrValue(std::string(value)));
                continue;
            }

            bool bSuccess = false;
            for (auto& pAttrDesc : attrDescs)
            {
                if (pAttrDesc == nullptr)
                    continue;
                if (_strnicmp(pAttrDesc->name().c_str(), attributeName.c_str(), std::min(pAttrDesc->name().size(), attributeName.size())) != 0)
                    continue;
                // 引用属性需要特殊处理
                if (pAttrDesc->type() == WD::WDBMAttrValueType::T_NodeRef || pAttrDesc->type() == WD::WDBMAttrValueType::T_NodeRefs)
                {
                    if (attributeValue != "=0")
                    {
                        auto refNodeName = attributeValue;
                        if (!refNodeName.empty() && refNodeName.front() == '/')
                            refNodeName.erase(refNodeName.begin());
                        auto itr = refAttrs.find(refNodeName);
                        if (itr == refAttrs.end())
                        {
                            auto emplaceRet = refAttrs.emplace(refNodeName, RefAttributeDatas());
                            if (emplaceRet.second)
                                itr = emplaceRet.first;
                        }
                        if (itr != refAttrs.end())
                        {
                            itr->second.emplace_back(RefAttributeData{ pSpecNode, pAttrDesc });
                            ++refCnt;
                        }
                    }
                    bSuccess = true;
                }
                else
                {
                    bSuccess = pAttrDesc->setValue(*pSpecNode, WD::WDBMAttrValue::FromString(pAttrDesc->type(), attributeValue));
                }
                break;
            }
            if (!bSuccess)
                log.addLog(WD::PluginLog::L_Error, "[%d]属性命令(%s)处理失败!", idx, str.c_str());
        }
    }
    // 处理等级节点的生成
    {
        // 获取SPCO类型节点的类型描述
        auto pTypeDesc = mgr.typeMgr().get("SPCO");
        if (pTypeDesc == nullptr)
        {
            assert(false);
            log.addLog(WD::PluginLog::L_Error, "意料之外的错误,导入中断!");
            goto SpecFileImportFinish;
        }
        // 表头数据信息
        struct HeaderInfo
        {
            std::string valueStr;
            bool isAttribute = false;
            WD::WDBMAttrDesc* pAttrDesc = nullptr;
        };
        for (; i < commands.size(); ++i)
        {
            // 如果是NEW命令,文件结构错误
            if (isNewCommand(commands[i].second))
            {
                log.addLog(WD::PluginLog::L_Error, "[%d]出现意料之外的NEW命令,导入中断!", commands[i].first);
                ret = false;
                goto SpecFileImportFinish;
            }
            // 找到表头命令,表头信息未找到时无法导入有效信息
            if (_stricmp(commands[i].second.c_str(), "HEADING") != 0)
                continue;
            // 下标+1跳过 HEADING
            if (++i >= commands.size())
                continue;
            // 如果是NEW命令,文件结构错误
            if (isNewCommand(commands[i].second))
            {
                log.addLog(WD::PluginLog::L_Error, "[%d]出现意料之外的NEW命令,导入中断!", commands[i].first);
                ret = false;
                goto SpecFileImportFinish;
            }
            WD::StringVector headerNames;
            // 表头的第一项固定是TYPE,第二列固定是NAME
            AnalysisStringBySpace(commands[i].second, headerNames);
            if (headerNames.size() < 2
                || _stricmp(headerNames[0].c_str(), "TYPE") != 0
                || _stricmp(headerNames[1].c_str(), "NAME") != 0)
            {
                log.addLog(WD::PluginLog::L_Error, "[%d]表头数据(%s)处理失败!", commands[i].first, commands[i].second.c_str());
                continue;
            }
            // 记录表头信息
            std::vector<HeaderInfo> header;
            header.reserve(headerNames.size());
            // Type
            header.emplace_back(HeaderInfo());
            header.back().valueStr = headerNames[0];
            header.back().isAttribute = false;
            // Name
            header.emplace_back(HeaderInfo());
            header.back().valueStr = headerNames[1];
            header.back().isAttribute = false;
            for (int headerIdx = 2; headerIdx < headerNames.size(); ++headerIdx)
            {
                const auto& name = headerNames[headerIdx];
                HeaderInfo Info;
                Info.valueStr = name;
                Info.isAttribute = false;
                // 暂时特殊处理 DETAIL 它和属性名称不一样
                if (_stricmp(name.c_str(), "DETAIL") == 0)
                {
                    Info.isAttribute = true;
                    Info.pAttrDesc = pTypeDesc->get("Detref");
                }
                else
                {
                    for (auto& eachAttr : pTypeDesc->attrDescs())
                    {
                        if (eachAttr == nullptr)
                            continue;
                        if (_strnicmp(name.c_str(), eachAttr->name().c_str(), std::min(name.size(), eachAttr->name().size())) != 0)
                            continue;
                        Info.isAttribute = true;
                        Info.pAttrDesc = eachAttr;
                        break;
                    }
                }
                header.emplace_back(Info);
            }
            if (++i >= commands.size())
                continue;
            // 如果是NEW命令,文件结构错误
            if (isNewCommand(commands[i].second))
            {
                log.addLog(WD::PluginLog::L_Error, "[%d]出现意料之外的NEW命令,导入中断!", commands[i].first);
                ret = false;
                goto SpecFileImportFinish;
            }
            // 处理默认值
            if (_stricmp(commands[i].second.c_str(), "DEFAULTS") == 0)
            {
                // +1跳过 DEFAULTS
                if (++i >= commands.size())
                    continue;
                // 如果是NEW命令,文件结构错误
                if (isNewCommand(commands[i].second))
                {
                    log.addLog(WD::PluginLog::L_Error, "[%d]出现意料之外的NEW命令,导入中断!", commands[i].first);
                    ret = false;
                    goto SpecFileImportFinish;
                }
                // 应用默认值,暂时未处理
                const auto& defaultStr = commands[i].second;
                WDUnused(defaultStr);
                // +1跳过默认值行
                if (++i >= commands.size())
                    continue;
                // 如果是NEW命令,文件结构错误
                if (isNewCommand(commands[i].second))
                {
                    log.addLog(WD::PluginLog::L_Error, "[%d]出现意料之外的NEW命令,导入中断!", commands[i].first);
                    ret = false;
                    goto SpecFileImportFinish;
                }
            }
            auto findChildMatchAnswer = [](const WD::WDNode& node, const std::string& question, const std::string& answer) -> WD::WDNode::SharedPtr
            {
                for (auto& each : node.children())
                {
                    if (each == nullptr)
                        continue;
                    auto childAnswer = WD::GetAnswerStr(*each, true, question);
                    if (childAnswer == answer)
                        return each;
                }
                return nullptr;
            };
            // 根据表头信息循环处理生成SPCO节点的命令
            for (; i < commands.size(); ++i)
            {
                // 如果遇到 HEADING ,则跳出本次循环重新获取新的表头信息
                if (_stricmp(commands[i].second.c_str(), "HEADING") == 0)
                {
                    --i;
                    break;
                }
                const auto& idx = commands[i].first;
                const auto& str = commands[i].second;
                // 如果是NEW命令,文件结构错误
                if (isNewCommand(str))
                {
                    log.addLog(WD::PluginLog::L_Error, "[%d]出现意料之外的NEW命令,导入中断!", idx);
                    ret = false;
                    goto SpecFileImportFinish;
                }
                WD::StringVector vec;
                AnalysisStringBySpace(str, vec);
                // 值的数量不能小于表头的数量,大于表头的部分舍去
                if (vec.size() < header.size())
                {
                    log.addLog(WD::PluginLog::L_Error, "[%d]命令(%s)处理失败!", idx, str.c_str());
                    continue;
                }
                struct AnswerInfo
                {
                    std::string question;
                    std::string answer;
                };
                // 保留所有的问答  <问题, 答案>
                std::vector<AnswerInfo> quesVec;
                quesVec.reserve(header.size());
                struct TempRefAttr
                {
                    std::string refValue;
                    WD::WDBMAttrDesc* pAttrDesc = nullptr;
                };
                // 保存引用属性的临时变量
                std::vector<TempRefAttr> tempRefs;
                for (int headerIdx = 2; headerIdx < header.size(); ++headerIdx)
                {
                    const auto& headerInfo = header[headerIdx];
                    const auto& value = vec[headerIdx];
                    if (value == "=0")
                        continue;
                    // 如果当前列不是属性,即是问题和答案
                    if (!headerInfo.isAttribute)
                    {
                        auto ques = headerInfo.valueStr;
                        // 这里特殊处理问题 PBORN (N = [0123456789]*),PBORN即为PBOR
                        if (_strnicmp(ques.c_str(), "PBOR", 4) == 0)
                        {
                            bool bRight = true;
                            for (int quesIdx = 4; quesIdx < ques.size(); quesIdx++)
                            {
                                if (ques[quesIdx] < '0' || ques[quesIdx] > '9')
                                {
                                    bRight = false;
                                    break;
                                }
                            }
                            if (bRight)
                                ques = "PBOR";
                        }
                        // 记录所有的问题和答案
                        quesVec.emplace_back(AnswerInfo{ ques, value });
                        continue;
                    }
                    // 如果当前列时属性,那么理论上属性描述必定不为空
                    auto pAttrDesc = headerInfo.pAttrDesc;
                    if (pAttrDesc == nullptr)
                    {
                        assert(false);
                        continue;
                    }
                    // 引用属性需要特殊处理,这里暂时记录下来,非引用属性则直接设置给节点
                    if (pAttrDesc->type() == WD::WDBMAttrValueType::T_NodeRef || pAttrDesc->type() == WD::WDBMAttrValueType::T_NodeRefs)
                    {
                        // 值 == =0 意为意为引用为空
                        if (value != "=0")
                            tempRefs.emplace_back(TempRefAttr{ value, pAttrDesc });
                    }
                    else
                    {
                        pAttrDesc->setValue(*pSpecNode, WD::WDBMAttrValue::FromString(pAttrDesc->type(), value));
                    }
                }
                // 根据命令查找等级节点,未查找到则创建等级节点
                auto pSeleNode = findChildMatchAnswer(*pSpecNode, header[0].valueStr, vec[0]);
                if (pSeleNode == nullptr)
                {
                    pSeleNode = mgr.create(pSpecNode, "SELE");
                    if (pSeleNode != nullptr)
                        WD::SetAnswerStr(*pSeleNode, vec[0], header[0].valueStr, true);
                }
                if (pSeleNode == nullptr)
                {
                    assert(false);
                    continue;
                }
                // 处理问题和答案
                if (quesVec.empty())
                    continue;
                // 根据问题和答案依次查找对应的等级节点,为空时则创建新的等级节点,注意,最后一个答案是SPCO节点的答案,所以这里只遍历到倒数第二个
                for (int quesIdx = 0; quesIdx < quesVec.size() - 1; ++quesIdx)
                {
                    const auto& each = quesVec[quesIdx];
                    auto pSubSeleNode = findChildMatchAnswer(*pSeleNode, each.question, each.answer);
                    if (pSubSeleNode == nullptr)
                    {
                        pSubSeleNode = mgr.create(pSeleNode, "SELE");
                        if (pSubSeleNode != nullptr)
                        {
                            pSeleNode->setAttribute("Question", WD::WDBMAttrValue(each.question));
                            WD::SetAnswerStr(*pSubSeleNode, each.answer, each.question, true);
                        }
                    }
                    if (pSubSeleNode == nullptr)
                    {
                        assert(false);
                        break;
                    }
                    pSeleNode = pSubSeleNode;
                }
                // 查找对应答案的SPCO节点,未找到则创建SPCO节点
                const auto& each = quesVec.back();
                auto pSpcoNode = findChildMatchAnswer(*pSeleNode, each.question, each.answer);
                if (pSpcoNode == nullptr)
                {
                    auto spcoName = vec[1];
                    auto n = spcoName.find('*');
                    while (n != std::string::npos)
                    {
                        spcoName.erase(spcoName.begin() + n);
                        spcoName.insert(spcoName.begin() + n, pSpecNode->name().begin(), pSpecNode->name().end());
                        n = spcoName.find('*');
                    }
                    pSpcoNode = mgr.create(pSeleNode, "SPCO", spcoName);
                    if (pSpcoNode != nullptr)
                    {
                        pSeleNode->setAttribute("Question", WD::WDBMAttrValue(each.question));
                        WD::SetAnswerStr(*pSpcoNode, each.answer, each.question, true);
                    }
                }
                // 记录当前SPCO节点和它的所有引用属性
                for (auto& eachRef : tempRefs)
                {
                    auto refNodeName = eachRef.refValue;
                    if (!refNodeName.empty() && refNodeName.front() == '/')
                        refNodeName.erase(refNodeName.begin());
                    auto itr = refAttrs.find(refNodeName);
                    if (itr == refAttrs.end())
                    {
                        auto emplaceRet = refAttrs.emplace(refNodeName, RefAttributeDatas());
                        if (emplaceRet.second)
                            itr = emplaceRet.first;
                    }
                    if (itr != refAttrs.end())
                    {
                        itr->second.emplace_back(RefAttributeData{ pSpcoNode, eachRef.pAttrDesc });
                        ++refCnt;
                    }
                }
            }
        }
    }
SpecFileImportFinish:
    auto pRoot = mgr.root();
    // 遍历节点树更新引用
    if (pRoot != nullptr && refCnt != 0)
    {
        WD::WDNode::RecursionHelpterR(*pRoot, [&](WD::WDNode& node)
        {
            if (!node.isNamed())
                return false;
            const auto& name = node.name();
            auto itr = refAttrs.find(name);
            if (itr != refAttrs.end())
            {
                for (auto& each : itr->second)
                {
                    const auto& pNode = each.pNode;
                    const auto& pAttrDesc = each.pAttrDesc;
                    if (pNode == nullptr || pAttrDesc == nullptr)
                    {
                        refCnt--;
                        continue;
                    }
                    pAttrDesc->setValue(*pNode, WD::WDBMAttrValue(WD::WDBMNodeRef(WD::WDNode::ToShared(&node))));
                    refCnt--;
                }
            }
            return refCnt == 0;
        });
    }
    pSpecNode->setParent(pParent);
    pSpecNode->triggerUpdate();
    log.addLog(WD::PluginLog::L_Info, "导入完成!");
    return ret;
}
bool SpecFileHelper::exportFile(const WD::WDNode& currentNode, const std::string& fileName)
{
    WDUnused2(currentNode, fileName);
    log.clear();
    return false;
}
bool SpecFileHelper::isNewCommand(const std::string& command)
{
    // NEW命令的空格不会超过2个
    int cnt = std::count(command.begin(), command.end(), ' ');
    return cnt <= 2 && _strnicmp(command.c_str(), "NEW ", 4) == 0;
}

ExcelFileHelper::ExcelFileHelper(WD::WDCore& core) : FileHelper(core)
{
}
bool ExcelFileHelper::checkFilePath(const std::string& filePath) const
{
    if (filePath.empty())
        return false;
    if (!std::filesystem::exists(filePath))
        return false;

    // 表格文件后缀名可能是 .xls
    return CheckExt(filePath, ".xls");
}
bool ExcelFileHelper::checkCurrentNode(WD::WDNode& node) const
{
    WD::WDNode::SharedPtr pSectNode = WD::WDNode::ToShared(&node);
    while (pSectNode != nullptr)
    {
        if (pSectNode->isType("SECT"))
            break;
        pSectNode = pSectNode->parent();
    }
    return pSectNode != nullptr;
}
bool ExcelFileHelper::importFile(WD::WDNode& currentNode, const std::string& fileName)
{
    log.clear();
    if (fileName.empty())
        return false;
    if (!checkCurrentNode(currentNode) || !checkFilePath(fileName))
        return false;
    WD::WDNode::SharedPtr pSectNode = WD::WDNode::ToShared(&currentNode);
    while (pSectNode != nullptr)
    {
        if (pSectNode->isType("SECT"))
            break;
        pSectNode = pSectNode->parent();
    }

    if (pSectNode == nullptr)
    {
        assert(false);
        log.addLog(WD::PluginLog::L_Error, "节点(%s[%s])不支持导入该文件!", currentNode.name().c_str(), currentNode.uuid().toString().c_str());
        return false;
    }
    log.addLog(WD::PluginLog::L_Info, "开始导入文件%s!", QString::fromLocal8Bit(fileName.data()).toUtf8());
    QTableWidget2Excel excel;
    QTableWidget2Excel::ExcelData excelData;
    excel.getExcelData(QString::fromLocal8Bit(fileName.data()), excelData);

    if (excelData.size() < 2)
    {
        log.addLog(WD::PluginLog::L_Info, "文件%s内容为空!", QString::fromLocal8Bit(fileName.data()).toUtf8());
        return false;
    }

    // 目前认为类型必须是表格的第一列,否则数据错误
    const auto& header = excelData[0];
    if (header.empty() || _stricmp(header.front().c_str(), "TYPE") != 0)
    {
        log.addLog(WD::PluginLog::L_Error, "文件%s表头数据错误!", QString::fromLocal8Bit(fileName.data()).toUtf8());
        return false;
    }
    struct TableData
    {
        // 属性   <属性名称, 属性值>
        using Attr = std::pair<std::string, std::string>;
        using Attrs = std::vector<Attr>;
        Attrs attrs;
        std::string nodeType;
        std::string ownerName;
        std::string nodeName;
    };
    using TableDatas = std::vector<TableData>;
    // 记录表格信息
    TableDatas tableDatas;
    for (int i = 1; i < excelData.size(); ++i)
    {
        const auto& eachData = excelData[i];
        if (eachData.empty())
            continue;
        // 类型不能为空
        const auto& type = eachData[0];
        if (type.empty() || eachData.size() < header.size())
        {
            log.addLog(WD::PluginLog::L_Error, "表格第%d行类型为空!", i + 1);
            continue;
        }
        TableData data;
        data.nodeType = type;
        // 这里根据表头名称记录节点名称,节点挂载的父节点名称和节点的属性
        for (int j = 1; j < header.size(); ++j)
        {
            const auto& headerName = header[j];
            if (_stricmp(headerName.c_str(), "NAME") == 0)
            {
                auto nodeName = eachData[j];
                if (nodeName.front() == '/')
                    nodeName.erase(nodeName.begin());

                data.nodeName = nodeName;
            }
            else if (_stricmp(headerName.c_str(), "OWNER") == 0)
            {
                auto ownerName = eachData[j];
                if (ownerName.front() == '/')
                    ownerName.erase(ownerName.begin());

                data.ownerName = ownerName;
            }
            else
            {
                data.attrs.emplace_back(std::make_pair(headerName, eachData[j]));
            }
        }
        if (data.ownerName.empty())
        {
            log.addLog(WD::PluginLog::L_Error, "表格第%d行父节点名称为空!", i + 1);
            continue;
        }
        tableDatas.emplace_back(data);
    }
    if (tableDatas.empty())
    {
        log.addLog(WD::PluginLog::L_Error, "文件%s内容为空或格式错误!", QString::fromLocal8Bit(fileName.data()).toUtf8());
        return false;
    }
    auto& mgr = _core.getBMCatalog();
    // 保存所有创建的节点 <父节点名称, 节点列表>
    struct TempDatas
    {
        WD::WDNode::SharedPtr pParent;
        WD::WDNode::Nodes children;
        // 保存所有有名子节点的名称,用来做查重判断
        std::set<std::string> names;
    };
    // 保存挂载的SECT节点下的所有CATE节点
    std::map<std::string, TempDatas> newNodes;
    for (auto& each : pSectNode->children())
    {
        if (each != nullptr)
        {
            TempDatas data = TempDatas();
            data.pParent = each;
            
            for (auto& pChild : each->children())
                if (pChild != nullptr && pChild->isNamed())
                    data.names.emplace(pChild->name());

            newNodes.emplace(each->name(), data);
        }
    }
    // 处理表格信息
    for (auto& each : tableDatas)
    {
        auto itr = newNodes.find(each.ownerName);
        if (itr == newNodes.end())
        {
            auto ret = newNodes.emplace(each.ownerName, TempDatas());
            if (!ret.second)
            {
                assert(false);
                continue;
            }
            itr = ret.first;
        }

        const std::string& name = each.nodeName;
        const std::string& type = each.nodeType;
        // 有名节点判断是否已经存在
        if (!name.empty())
        {
            if (itr->second.names.find(name) != itr->second.names.end())
            {
                log.addLog(WD::PluginLog::L_Info, "%s类型节点%s已存在,跳过导入!", type.c_str(), name.c_str());
                continue;
            }
        }

        auto pNewNode = mgr.create(type, name);
        if (pNewNode == nullptr)
            continue;

        auto pTypeDesc = pNewNode->getTypeDesc();
        if (pTypeDesc != nullptr)
        {
            for (auto& eachAttr : each.attrs)
            {
                if (eachAttr.first.empty() || eachAttr.second.empty())
                    continue;
                auto pAttrDesc = pTypeDesc->get(eachAttr.first);
                if (pAttrDesc == nullptr)
                {
                    // 如果名称对不上,这里用前四个字母再次尝试
                    pAttrDesc = pTypeDesc->get(std::string(eachAttr.first.begin(), eachAttr.first.begin() + 4));
                    if (pAttrDesc == nullptr)
                        continue;
                }
                bool bOk = false;
                if (pAttrDesc->type() == WD::WDBMAttrValueType::T_NodeRef || pAttrDesc->type() == WD::WDBMAttrValueType::T_NodeRefs)
                {
                    log.addLog(WD::PluginLog::L_Info, "属性字段%s处理失败,暂不支持引用属性!", eachAttr.second.c_str());
                    assert(false);
                    continue;
                }
                auto value = WD::WDBMAttrValue::FromString(pAttrDesc->type(), eachAttr.second, &bOk);
                if (!bOk)
                {
                    log.addLog(WD::PluginLog::L_Info, "属性字段%s处理失败!", eachAttr.second.c_str());
                    assert(false);
                    continue;
                }
                pAttrDesc->setValue(*pNewNode, value);
            }
        }
        itr->second.children.emplace_back(pNewNode);
    }
    // 收集需要更新节点
    std::set<WD::WDNode::SharedPtr> needUpdateNodes;
    for (auto& each : newNodes)
    {
        auto& pParentName = each.first;
        auto& pParent = each.second.pParent;
        auto& children = each.second.children;
        if (pParent == nullptr)
        {
            pParent = mgr.create(pSectNode, "CATE", pParentName);
            needUpdateNodes.emplace(pParent);
        }
        if (pParent == nullptr)
        {
            assert(false);
            continue;
        }
        if (children.empty())
            continue;

        needUpdateNodes.emplace(pParent);
        // 给无名节点做流水数,用以流水命名
        for (auto& pChild : children)
        {
            if (pChild == nullptr)
                continue;
            pChild->setParent(pParent);
        }
    }
    for (auto& each : needUpdateNodes)
    {
        if (each == nullptr)
            continue;
        each->triggerUpdate(true);
    }
    log.addLog(WD::PluginLog::L_Info, "文件%s导入完成!", QString::fromLocal8Bit(fileName.data()).toUtf8());
    return true;
}
bool ExcelFileHelper::exportFile(const WD::WDNode& currentNode, const std::string& fileName)
{
    WDUnused2(currentNode, fileName);
    log.clear();
    return false;
}