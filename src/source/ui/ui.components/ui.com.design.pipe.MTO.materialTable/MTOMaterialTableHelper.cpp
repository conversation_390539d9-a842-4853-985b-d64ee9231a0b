#include "MTOMaterialTableHelper.h"
#include "MaterialDataGainMethods.h"
#include "core/message/WDMessage.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "log/WDLoggerPort.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/businessModule/catalog/WDBMCatalog.h"

WD_NAMESPACE_USE
class MTOMaterialTableHelperPrivate
{
public:
    std::map<MaterialFlag, MaterialDataGainMethodBase*> methods;
    WDCore& core;
    std::vector<MaterialFlag> allFlags;

    // 这里记录已经被处理的垫片
    std::set<WDNode::SharedPtr> usedGasks;
    MTOMaterialTableHelper& helper;
public:
    // 这里用map保存每种不同类型的节点的所有属性值 
    // <key: 直管段为管径, 其他管件为元件等级的uuid, 该种节点的所有属性值>
    using MValue = std::map<MaterialFlag, std::string>;
    using MValues = std::map<std::string, MValue>;
    // 螺栓数据
    using BoltValues = std::map<BoltKitInfo, MValue>;
public:
    MTOMaterialTableHelperPrivate(WDCore& core, MTOMaterialTableHelper& helper) : core(core), helper(helper)
    {
        addMethod(MaterialFlag::MF_UnitName,        new MaterialDataUnitName(core));
        addMethod(MaterialFlag::MF_PipeSpec,        new MaterialDataPipeSpec(core));
        addMethod(MaterialFlag::MF_ComsType,        new MaterialDataComsType(core));
        addMethod(MaterialFlag::MF_Code,            new MaterialDataCode(core));
        addMethod(MaterialFlag::MF_ScomCode,        new MaterialDataScomCode(core));
        addMethod(MaterialFlag::MF_ScomCodeWXHH,    new MaterialDataScomCodeWXHH(core));
        addMethod(MaterialFlag::MF_MaterialCode,    new MaterialDataMaterialCode(core));
        addMethod(MaterialFlag::MF_MainBore,        new MaterialDataMainBore(core));
        addMethod(MaterialFlag::MF_SubBore,         new MaterialDataSubBore(core));
        addMethod(MaterialFlag::MF_Specifications,  new MaterialDataSpecifications(core));
        addMethod(MaterialFlag::MF_Count,           new MaterialDataCount(core));
        addMethod(MaterialFlag::MF_Unit,            new MaterialDataUnit(core));
        addMethod(MaterialFlag::MF_BoltDiameter,    new MaterialDataBoltDiameter(core));
        addMethod(MaterialFlag::MF_BoltLength,      new MaterialDataBoltLength(core));
        addMethod(MaterialFlag::MF_InsulationSpec,  new MaterialDataInsulationSpec(core));
        addMethod(MaterialFlag::MF_UniqueCode,      new MaterialDataUniqueCode(core));
        addMethod(MaterialFlag::MF_DetailedDesc,    new MaterialDataDetailedDesc(core));
        addMethod(MaterialFlag::MF_MaterialDesc,    new MaterialDataMaterialDesc(core));
        addMethod(MaterialFlag::MF_SingleWeight,    new MaterialDataSingleWeight(core));
        addMethod(MaterialFlag::MF_TotalWeight,     new MaterialDataTotalWeight(core));

        allFlags = {MaterialFlag::MF_UnitName,
        MaterialFlag::MF_PipeSpec,
        MaterialFlag::MF_ComsType,
        MaterialFlag::MF_Code,
        MaterialFlag::MF_ScomCode,
        MaterialFlag::MF_ScomCodeWXHH,
        MaterialFlag::MF_MaterialCode,
        MaterialFlag::MF_MainBore,
        MaterialFlag::MF_SubBore,
        MaterialFlag::MF_Specifications,
        MaterialFlag::MF_Count,
        MaterialFlag::MF_Unit,
        MaterialFlag::MF_BoltDiameter,
        MaterialFlag::MF_BoltLength,
        MaterialFlag::MF_InsulationSpec,
        MaterialFlag::MF_UniqueCode,
        MaterialFlag::MF_DetailedDesc,
        MaterialFlag::MF_MaterialDesc,
        MaterialFlag::MF_SingleWeight,
        MaterialFlag::MF_TotalWeight};
    }
    virtual ~MTOMaterialTableHelperPrivate()
    {
        for (auto& each : methods)
        {
            auto pMethod = each.second;
            if (pMethod != nullptr)
            {
                delete pMethod;
                pMethod = nullptr;
            }
        }
        methods.clear();
    }
    void addMethod(MaterialFlag flag, MaterialDataGainMethodBase* pMethod)
    {
        assert(methods.find(flag) == methods.end());
        methods.emplace(flag, pMethod);
    }
    MaterialDataGainMethodBase* getMethod(MaterialFlag flag) const
    {
        auto itr = methods.find(flag);
        if (itr != methods.end())
            return itr->second;
        return nullptr;
    }
public:
    /**
     * @brief 统计单位节点的材料
    */
    void execUnitNode(WDNode& node, MTOMaterialTableData& data)
    {
        usedGasks.clear();
        MValues values;
        BoltValues boltValues;
        WD::MaterialDataGainMethodBase::Context context;
        context.pUnitNode = WDNode::ToShared(&node);
        context.boltType = MaterialDataGainMethodBase::Context::None;
        WDNode::RecursionHelpter(node, [&](MValues& values, BoltValues& boltValues, MaterialDataGainMethodBase::Context& context, WDNode& node)
        {
            if (checkComNode(node))
            {
                context.boltType = MaterialDataGainMethodBase::Context::None;
                execComsNode(node, values, boltValues, context);
            }
        }, values, boltValues, context);
        if (!values.empty())
        {
            for (auto& each : values)
            {
                const auto& materoialDatas = each.second;
                for (auto& eachMaterial : materoialDatas)
                {
                    if (auto itr = data.find(eachMaterial.first); itr != data.end())
                        itr->second.emplace_back(eachMaterial.second);
                    else
                        data.emplace(eachMaterial.first, StringVector{eachMaterial.second});
                }
            }
        }
        if (!boltValues.empty())
        {
            for (auto& each : boltValues)
            {
                const auto& materoialDatas = each.second;
                for (auto& eachMaterial : materoialDatas)
                {
                    if (auto itr = data.find(eachMaterial.first); itr != data.end())
                        itr->second.emplace_back(eachMaterial.second);
                    else
                        data.emplace(eachMaterial.first, StringVector{eachMaterial.second});
                }
            }
        }
        usedGasks.clear();
    }
    /**
     * @brief 统计一个管件节点的数据
    */
    void execComsNode(WDNode& node, MValues& values, BoltValues& boltValues, MaterialDataGainMethodBase::Context& context)
    {
        std::string key;
        // 获取当前管件在map中的key值,管件使用元件等级的uuid,直管段使用管径
        if (WD::WDBMDPipeUtils::IsPipeComponent(node))
        {
            auto refVal = node.getAttribute("Spref");
            auto pNodeRef = refVal.data<WDBMNodeRef>();
            if (pNodeRef == nullptr)
                return;
            auto pRefNode = pNodeRef->refNode();
            if (pRefNode == nullptr)
                return;
            key = pRefNode->uuid().toString();
        }
        else if (node.isType("TUBI"))
        {
            key = ToString(node.getAttribute("Lbore").convertToInt());
        }
        if (key.empty())
            return;

        auto itr = values.find(key);
        if (itr == values.end())
        {
            auto ret = values.emplace(key, MValue());
            if (!ret.second)
            {
                assert(false);
                return;
            }
            itr = ret.first;
        }
        auto& value = itr->second;
        context.pCurrentNode = WDNode::ToShared(&node);
        context.boltKitInfo.reset();
        // 获取当前管径的所有属性
        for (auto& flag : allFlags)
        {
            if (!helper.flags.hasFlag(flag))
                continue;

            auto pMethod = getMethod(flag);
            if (pMethod == nullptr)
            {
                assert(false);
                continue;
            }
            auto valueItr = value.find(flag);
            if (valueItr == value.end())
                value.emplace(flag, pMethod->exec(context));
            else
                valueItr->second = pMethod->exec(context);
        }

        // 记录从一个法兰到下一个法兰之间的所有节点(包括法兰本身),用于计算螺栓的长度
        if (!node.isType("GASK")
            || usedGasks.find(WD::WDNode::ToShared(&node)) != usedGasks.end())
            return;
        usedGasks.emplace(WDNode::ToShared(&node));
        // 获取分支节点
        auto gaskDEndedNodes = GetBoltsNodesByGask(node, &usedGasks);
        if (!execBoltNode(context, gaskDEndedNodes, boltValues))
        {
            char info[1024] = { 0 };

            std::string text = WD::WDTs("MTOMaterialTableHelper","The valve(%s[%s]) is incorrectly connected to the front flange/valve");
            sprintf_s(info, sizeof(info), text.c_str()
                , node.name().c_str()
                , node.uuid().toString().c_str());
            LOG_INFO << info;
        }
    }

    bool checkComNode(const WDNode& node) const
    {
        if (helper.filterType.find(std::string(node.type().data())) != helper.filterType.end())
            return false;
        // 当管件的MTOC属性为off时跳过当前管件
        auto mtoc = std::string(node.getAttribute("Mtocomponent").toWord());
        if (_stricmp(mtoc.c_str(), "off") == 0)
            return false;

        // 华东院的特殊处理
        {
            // 0TEE不用导出 判断条件：(TEE类型节点) SPCO->Detref->Symbolkey == TESO
            if (node.isType("TEE"))
            {
                auto pSpco = node.getAttribute("Spref").toNodeRef().refNode();
                if (pSpco != nullptr)
                {
                    auto pDetrefNode = pSpco->getAttribute("Detref").toNodeRef().refNode();
                    if (pDetrefNode != nullptr && pDetrefNode->getAttribute("Skey").toString() == "TESO")
                        return false;
                }

            }
            // 0BEND不用导出 判断条件：(BEND类型节点) SPCO->Detref->Symbolkey == PB9D
            else if (node.isType("BEND"))
            {
                auto pSpco = node.getAttribute("Spref").toNodeRef().refNode();
                if (pSpco != nullptr)
                {
                    auto pDetrefNode = pSpco->getAttribute("Detref").toNodeRef().refNode();
                    if (pDetrefNode != nullptr && pDetrefNode->getAttribute("Skey").toString() == "PB9D")
                        return false;
                }
            }
        }
        return true;
    }
    /**
    * @brief 统计螺栓节点
    */
    bool execBoltNode(WD::MaterialDataGainMethodBase::Context& context, const WDNode::Nodes nodes, BoltValues& boltValues) const
    {
        if (!CheckBoltNodes(nodes))
            return false;
        auto& pFlanStart = nodes.front();
        auto& pFlanEnd = nodes.back();
        if (pFlanStart == nullptr || pFlanEnd == nullptr)
            return false;

        auto boltKitInfo = GetBoltNode(core, *pFlanStart, *pFlanEnd);
        if (boltKitInfo.pStudNode != nullptr)
            boltKitInfo.boltLength = GetBoltLength(core, nodes, *boltKitInfo.pStudNode);
        if (!boltKitInfo.valid())
            return false;
        auto itr = boltValues.find(boltKitInfo);
        if (itr == boltValues.end())
        {
            auto ret = boltValues.emplace(boltKitInfo, MValue());
            if (!ret.second)
            {
                assert(false);
                return false;
            }
            itr = ret.first;
        }
        auto& value = itr->second;
        
        auto bCheckBlrefs = [] (WD::WDNode& node)->bool
        {
            auto pSpref = node.getAttribute("Spref").toNodeRef().refNode();
            if (pSpref == nullptr)
                return false;
            auto pCatref = pSpref->getAttribute("Catref").toNodeRef().refNode();
            if (pCatref == nullptr)
                return false;
            return !pCatref->getAttribute("Blrfarray").toNodeRefVector().empty();
        };
        if ((!WDBMDPipeUtils::IsPipeComponent(*pFlanStart) || !bCheckBlrefs(*pFlanStart)) && bCheckBlrefs(*pFlanEnd))
            context.pCurrentNode = pFlanEnd;
        else
            context.pCurrentNode = pFlanStart;

        context.boltKitInfo = boltKitInfo;
        context.boltType = MaterialDataGainMethodBase::Context::Stud;
        // 获取当前管径的所有属性
        for (auto& flag : allFlags)
        {
            if (!helper.flags.hasFlag(flag))
                continue;

            auto pMethod = getMethod(flag);
            if (pMethod == nullptr)
            {
                assert(false);
                continue;
            }
            auto valueItr = value.find(flag);
            if (valueItr == value.end())
                value.emplace(flag, pMethod->exec(context));
            else
                valueItr->second = pMethod->exec(context);
        }
        return true;
    }
};

static WDNode::Nodes FilterNodes(const WDNode::Nodes& nodes, const char* nodeType)
{
    if (nodeType == nullptr || strlen(nodeType) == 0)
        return WDNode::Nodes();
    // 这里用set获取节点列表是为了避免添加重复的节点
    std::set<WDNode*> nodesSet;
    for (auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        WDNode::RecursionHelpter(*pNode, [&nodesSet](const char* nodeType, WDNode& node)
        {
            if (node.isType(nodeType))
                nodesSet.emplace(&node);
        }, nodeType);
    }
    WDNode::Nodes outNodes;
    for (auto& eachNode : nodesSet)
    {
        auto pNode = WDNode::ToShared(eachNode);
        if (pNode != nullptr)
            outNodes.push_back(pNode);
    }
    return outNodes;
}
static WDNode::Nodes FilterNodes(const WDNode::Nodes& nodes, const StatisticsType& type)
{
    return FilterNodes(nodes, MTOMaterialTableHelper::GetTypeStr(type));
}

const char* MTOMaterialTableHelper::FlagToStr(const MaterialFlag& type, const StatisticsType& sType)
{
    switch (type)
    {
    case MaterialFlag::MF_UnitName:
        {
            switch (sType)
            {
            case StatisticsType::ST_BRAN:
                return "BranchName";
            default:
                return "Pipe";
            }
        }
    case MaterialFlag::MF_PipeSpec:
        return "PipeSpec";
    case MaterialFlag::MF_ComsType:
        {
            switch (sType)
            {
            case StatisticsType::ST_BRAN:
                return "ComsType";
            default:
                return "Type";
            }
        }
    case MaterialFlag::MF_Code:
        return "Code";
    case MaterialFlag::MF_ScomCode:
    case MaterialFlag::MF_ScomCodeWXHH:
        return "ScomCode";
    case MaterialFlag::MF_MaterialCode:
        return "MaterialCode";
    case MaterialFlag::MF_MainBore:
        {
            switch (sType)
            {
            case StatisticsType::ST_BRAN:
                return "Primary bore";
            default:
                return "MainBore";
            }
        }
    case MaterialFlag::MF_SubBore:
        {
            switch (sType)
            {
            case StatisticsType::ST_BRAN:
                return "Secondary bore";
            default:
                return "SubBore";
            }
        }
    case MaterialFlag::MF_Specifications:
        return "Specifications";
    case MaterialFlag::MF_Count:
        {
            switch (sType)
            {
            case StatisticsType::ST_BRAN:
                return "Count/Length";
            default:
                return "Count";
            }
        }
    case MaterialFlag::MF_Unit:
        return "Unit";
    case MaterialFlag::MF_BoltDiameter:
        return "BoltDiameter";
    case MaterialFlag::MF_BoltLength:
        return "BoltLength";
    case MaterialFlag::MF_InsulationSpec:
        return "InsulationSpec";
    case MaterialFlag::MF_UniqueCode:
        return "UniqueCode";
    case MaterialFlag::MF_DetailedDesc:
        return "DetailedDesc";
    case MaterialFlag::MF_MaterialDesc:
        return "MaterialDesc";
    case MaterialFlag::MF_SingleWeight:
        return "SingleWeight";
    case MaterialFlag::MF_TotalWeight:
        return "TotalWeight";
    default:
        break;
    }
    assert(false);
    return "";
}

const char* MTOMaterialTableHelper::GetTypeStr(const StatisticsType& type)
{
    switch (type)
    {
    case StatisticsType::ST_BRAN:
        return "BRAN";
    case StatisticsType::ST_PIPE:
        return "PIPE";
    default:
        assert(false);
        break;
    }
    return "";
}
MTOMaterialTableHelper::MTOMaterialTableHelper(WD::WDCore& core) : _core(core)
{
}
MTOMaterialTableHelper::~MTOMaterialTableHelper()
{
}
MTOMaterialTableData MTOMaterialTableHelper::exec(const WDNode::Nodes& nodes)
{
    // 这里根据类型过滤节点 -> 遍历所有节点及其子节点,从中筛选出所有的指定类型的节点作为统计的单元节点
    auto filtNodes = FilterNodes(nodes, type);
    MTOMaterialTableHelperPrivate p(_core, *this);
    MTOMaterialTableData data;
    // 依次统计每个单元节点
    for (auto& pNode : filtNodes)
    {
        if (pNode == nullptr)
            continue;
        // 开始统计一个单元节点
        {
            char info[1024] = { 0 };
            std::string text = WD::WDTs("MTOMaterialTableHelper","Starts counting the data of node %s");
            sprintf_s(info, sizeof(info), text.c_str(), pNode->name().c_str());
            LOG_INFO << info;
        }
        p.execUnitNode(*pNode, data);
        // 一个单元节点统计完成
        {
            char info[1024] = { 0 };
            std::string text = WD::WDTs("MTOMaterialTableHelper", "The data statistics of node %s are completed");
            sprintf_s(info, sizeof(info), text.c_str(), pNode->name().c_str());
            LOG_INFO << info;
        }
    }
    return data;
}

std::vector<StringVector> MTOMaterialTableHelper::execATTA(const WDNode::Nodes& nodes)
{
    // 这里根据类型过滤节点 -> 遍历所有节点及其子节点,从中筛选出所有的指定类型的节点作为统计的单元节点
    auto attaNodes = FilterNodes(nodes, "ATTA");
    MTOMaterialTableHelperPrivate p(_core, *this);

    // 这里保存已经出现过的型号的atta材料信息在stringvector中的下标
    std::map<std::string, size_t> attaMIdx;
    std::vector<std::pair<std::string, int>> attaMDatas;
    // 依次统计每个单元节点
    for (auto& pNode : attaNodes)
    {
        if (pNode == nullptr)
            continue;
        auto skey = pNode->getAttribute("Stext").convertToString();
        if (skey.empty())
            continue;
        auto itr = attaMIdx.find(skey);
        if (itr != attaMIdx.end())
        {
            auto idx = itr->second;
            if (idx >= attaMDatas.size())
            {
                assert(false);
                itr->second = attaMDatas.size();
                attaMDatas.emplace_back(skey, 1);
            }
            else
            {
                ++attaMDatas[idx].second;
            }
        }
        else
        {
            attaMIdx.emplace(skey, attaMDatas.size());
            attaMDatas.emplace_back(skey, 1);
        }
    }
    std::vector<StringVector> data;
    data.reserve(attaMDatas.size() + 1);
    // 第一行输入表头
    StringVector title = {WD::WDTs("MTOMaterialTableDialog", "Stext"), WD::WDTs("MTOMaterialTableDialog", "Count")};
    data.push_back(title);
    for (const auto& each : attaMDatas)
    {
        if (each.first.empty())
            continue;
        data.emplace_back(std::vector<std::string>{each.first, ToString(each.second)});
    }
    return data;
}