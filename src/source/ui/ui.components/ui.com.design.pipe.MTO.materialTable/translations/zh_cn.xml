<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	<context>
		<name>MTOMaterialTableDialog</name>
		<message>
			<source>MTOMaterialTable</source>
			<translation>MTO材料表</translation>
		</message>
		<message>
			<source>Config</source>
			<translation>配置</translation>
		</message>
		<message>
			<source>WXHH</source>
			<translation>无锡恒禾</translation>
		</message>
		<message>
			<source>HDY</source>
			<translation>华东院</translation>
		</message>
		<message>
			<source>AddCurrent</source>
			<translation>添加当前</translation>
		</message>
		<message>
			<source>AddCurrentMember</source>
			<translation>添加当前成员</translation>
		</message>
		<message>
			<source>RemoveChoosed</source>
			<translation>移除已选项</translation>
		</message>
		<message>
			<source>RemoveAll</source>
			<translation>移除所有</translation>
		</message>
		<message>
			<source>GenerateMethod</source>
			<translation>生成方式</translation>
		</message>
		<message>
			<source>GenerateMaterialTable</source>
			<translation>生成材料表</translation>
		</message>
		<message>
			<source>ATTAMaterialSummary</source>
			<translation>支架材料汇总</translation>
		</message>
		
		<message>
			<source>StatisticsMaterialByPIPE</source>
			<translation>以管道统计材料</translation>
		</message>
		<message>
			<source>StatisticsMaterialByBRAN</source>
			<translation>以分支统计材料</translation>
		</message>

		<message>
			<source>BranchName</source>
			<translation>Branch名称</translation>
		</message>
		<message>
			<source>Pipe</source>
			<translation>管道</translation>
		</message>
		<message>
			<source>PipeSpec</source>
			<translation>管道等级</translation>
		</message>
		<message>
			<source>ComsType</source>
			<translation>元件种类</translation>
		</message>
		<message>
			<source>Type</source>
			<translation>类型</translation>
		</message>
		<message>
			<source>Code</source>
			<translation>编码</translation>
		</message>
		<message>
			<source>ScomCode</source>
			<translation>元件编码</translation>
		</message>
		<message>
			<source>MaterialCode</source>
			<translation>材料编码</translation>
		</message>
		<message>
			<source>MaterialDesc</source>
			<translation>材料描述</translation>
		</message>
		<message>
			<source>Specifications</source>
			<translation>规格</translation>
		</message>
		<message>
			<source>MainBore</source>
			<translation>主管径</translation>
		</message>
		<message>
			<source>SubBore</source>
			<translation>次管径</translation>
		</message>
		<message>
			<source>Count</source>
			<translation>数量</translation>
		</message>
		<message>
			<source>Count/Length</source>
			<translation>数量/长度</translation>
		</message>
		<message>
			<source>Unit</source>
			<translation>单位</translation>
		</message>
		<message>
			<source>BoltDiameter</source>
			<translation>螺栓直径</translation>
		</message>
		<message>
			<source>BoltLength</source>
			<translation>螺栓长度</translation>
		</message>
		<message>
			<source>InsulationSpec</source>
			<translation>保温等级</translation>
		</message>
		<message>
			<source>UniqueCode</source>
			<translation>唯一码</translation>
		</message>
		<message>
			<source>DetailedDesc</source>
			<translation>详细描述</translation>
		</message>
		<message>
			<source>SingleWeight</source>
			<translation>单重</translation>
		</message>
		<message>
			<source>TotalWeight</source>
			<translation>总重</translation>
		</message>
		<message>
			<source>SavePath</source>
			<translation>保存路径</translation>
		</message>
		<message>
			<source>GenerateTable</source>
			<translation>生成表格</translation>
		</message>
		<message>
			<source>TablePath</source>
			<translation>表格路径</translation>
		</message>
		<message>
			<source>Count</source>
			<translation>数量</translation>
		</message>
		<message>
			<source>Stext</source>
			<translation>型号</translation>
		</message>
		<message>
			<source>The function is under development</source>
			<translation>该功能正在开发中</translation>
		</message>
		<message>
			<source>Current node is Null!</source>
			<translation>当前节点为空!</translation>
		</message>
		<message>
			<source>Sure remove choose node?</source>
			<translation>确定移除选中的节点吗?</translation>
		</message>
		<message>
			<source>Sure remove all node?</source>
			<translation>确定移除所有节点吗?</translation>
		</message>
		<message>
			<source>Table Generate Success!</source>
			<translation>表格生成成功!</translation>
		</message>
		<message>
			<source>Table Generate Failed!</source>
			<translation>表格生成失败!</translation>
		</message>
		<message>
			<source>File path is empty!</source>
			<translation>文件路径为空!</translation>
		</message>
		<message>
			<source>Table is empty!</source>
			<translation>表格内容为空!</translation>
		</message>
	</context>
	<context>
		<name>MTOMaterialTableHelper</name>
		<message>
			<source>The valve(%s[%s]) is incorrectly connected to the front flange/valve</source>
			<translation>阀门(%s[%s])和前面的法兰/阀门连接有误!</translation>
		</message>
		<message>
			<source>Starts counting the data of node %s</source>
			<translation>开始统计节点%s的数据</translation>
		</message>
		<message>
			<source>The data statistics of node %s are completed</source>
			<translation>节点%s的数据统计完成</translation>
		</message>
		
	</context>
	<context>
		<name>MTOMaterialTableHelper</name>
		<message>
			<source>The bolt set reference for flange/valve is empty</source>
			<translation>法兰/阀门(%s[%s])的螺栓集引用为空!</translation>
		</message>
		<message>
			<source>The bolt under the set reference of flange/valve is empty!</source>
			<translation>法兰/阀门(%s[%s])的螺栓集下的螺栓为空!</translation>
		</message>
		
	</context>
	<context>
		<name>MaterialDataGainMethods</name>
		<message>
			<source>Count</source>
			<translation>个</translation>
		</message>
		<message>
			<source>Meter</source>
			<translation>米</translation>
		</message>
	</context>
	
</TS>
