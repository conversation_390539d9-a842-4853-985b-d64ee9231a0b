#pragma once
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include <QString>
#include "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/WDBDBase.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"

WD_NAMESPACE_BEGIN

/**
 * @brief 注意 : 统计结果返回值类型为 utf-8 编码的字符串
*/
class MaterialDataGainMethodBase
{
public:
    class Context
    {
    public:
        Context()
        {
        }
    public:
        // 记录当前批次的每种类型的 数量/长度 <key:直管段为管径,其他管件为等级的uuid, 直管段为当前批次中同管径的直管段的总长,其他管件为相同元件的管件数量>
        std::map<std::string, double> counts;
        // 保存螺栓的重量
        std::map<std::string, double> weights;
        // 保存当前的单位节点
        WDNode::SharedPtr pUnitNode;
        // 保存当前节点
        WDNode::SharedPtr pCurrentNode;

        enum BoltType
        {
            // 不是螺栓节点
            None,
            // 螺母
            Nut,
            // 垫片
            Wash,
            // (戴帽)螺栓
            Stud,
        };
        BoltType boltType = None;
        // 螺栓等级节点
        BoltKitInfo boltKitInfo;
        struct Key
        {
            WDNode::SharedPtr pNode;
            double length = 0.0;
            Key(WDNode::SharedPtr pNode, double length = 0.0) : pNode(pNode), length(length)
            {
            }
            const bool operator<(const Key& right) const
            {
                if (this->pNode == right.pNode && this->length == right.length)
                    return false;

                if (this->length < right.length)
                    return true;
                return this->length == right.length && pNode < right.pNode;
            }

        };
        // 保存螺栓的数量
        std::map<Key, uint> boltCnts;
        std::map<WD::WDNode::SharedPtr, double> boltDiameters;
        // 保存螺栓的重量
        std::map<Key, double> boltWeights;
    };
public:
    MaterialDataGainMethodBase(WDCore& core)
        :_core(core)
    {
    }
    virtual~MaterialDataGainMethodBase()
    {
    }
public:
    /**
     * @brief 执行函数
     * @param node 节点
     * @param context 上下文
     * @return 处理后获取的值
    */
    std::string exec(Context& context) 
    {
        _bCAttrsLoaded = false;
        return this->onExec(context);
    }
protected:
    WDBMAttrValue getAttribute(Context& context, WDNode::SharedPtr pNode, const std::string& name)
    {
        if (!_bCAttrsLoaded)
        {
            _cAttrs = getCAttrs(context);
            _bCAttrsLoaded = true;
        }
        if (pNode == nullptr)
        {
            assert(false);
            return WDBMAttrValue();
        }
        return _cAttrs.getAttribute(*pNode, name);
    }
protected:
    virtual std::string onExec(Context& context) = 0;
private:
    CAttributeGet getCAttrs(Context& context)
    {
        auto pNode = context.pCurrentNode;
        if (pNode == nullptr)
            return CAttributeGet();

        WD::WDNode::SharedPtr pSPCONode = nullptr;

        switch (context.boltType)
        {
        case Context::Nut:
            pSPCONode = context.boltKitInfo.pNutNode;
            break;
        case Context::Wash:
            pSPCONode = context.boltKitInfo.pWashNode;
            break;
        case Context::Stud:
            pSPCONode = context.boltKitInfo.pStudNode;
            break;
        default:
            pSPCONode = pNode->getAttribute("Spref").toNodeRef().refNode();
            break;
        };
        if (pSPCONode == nullptr)
            return CAttributeGet();
        // 构建属性
        return _core.getBMCatalog().modelBuilder().cAttributeGet(pNode, pSPCONode);
    }
protected:
    WDCore& _core;
    CAttributeGet _cAttrs;
    bool _bCAttrsLoaded = false;
};
/**
 * @brief 获取统计单元(分支/管道)名称
*/
class MaterialDataUnitName : public MaterialDataGainMethodBase
{
public:
    MaterialDataUnitName(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取管道等级
*/
class MaterialDataPipeSpec : public MaterialDataGainMethodBase
{
public:
    MaterialDataPipeSpec(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取元件类型
*/
class MaterialDataComsType : public MaterialDataGainMethodBase
{
public:
    MaterialDataComsType(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取元件编码
*/
class MaterialDataScomCode : public MaterialDataGainMethodBase
{
public:
    MaterialDataScomCode(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取主管径
*/
class MaterialDataMainBore : public MaterialDataGainMethodBase
{
public:
    MaterialDataMainBore(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取次管径
*/
class MaterialDataSubBore : public MaterialDataGainMethodBase
{
public:
    MaterialDataSubBore(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取数量/长度
*/
class MaterialDataCount : public MaterialDataGainMethodBase
{
public:
    MaterialDataCount(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取螺栓直径
*/
class MaterialDataBoltDiameter : public MaterialDataGainMethodBase
{
public:
    MaterialDataBoltDiameter(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取螺栓长度
*/
class MaterialDataBoltLength : public MaterialDataGainMethodBase
{
public:
    MaterialDataBoltLength(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取保温等级
*/
class MaterialDataInsulationSpec : public MaterialDataGainMethodBase
{
public:
    MaterialDataInsulationSpec(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取唯一码
*/
class MaterialDataUniqueCode : public MaterialDataGainMethodBase
{
public:
    MaterialDataUniqueCode(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取详细描述
*/
class MaterialDataDetailedDesc : public MaterialDataGainMethodBase
{
public:
    MaterialDataDetailedDesc(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取编码
*/
class MaterialDataCode : public MaterialDataGainMethodBase
{
public:
    MaterialDataCode(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取元件编码(无锡恒禾)
*/
class MaterialDataScomCodeWXHH : public MaterialDataGainMethodBase
{
public:
    MaterialDataScomCodeWXHH(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取材料编码
*/
class MaterialDataMaterialCode : public MaterialDataGainMethodBase
{
public:
    MaterialDataMaterialCode(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取材料描述
*/
class MaterialDataMaterialDesc : public MaterialDataGainMethodBase
{
public:
    MaterialDataMaterialDesc(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取规格
*/
class MaterialDataSpecifications : public MaterialDataGainMethodBase
{
public:
    MaterialDataSpecifications(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取单位
*/
class MaterialDataUnit : public MaterialDataGainMethodBase
{
public:
    MaterialDataUnit(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取单重
*/
class MaterialDataSingleWeight : public MaterialDataGainMethodBase
{
public:
    MaterialDataSingleWeight(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取总重
*/
class MaterialDataTotalWeight : public MaterialDataGainMethodBase
{
public:
    MaterialDataTotalWeight(WDCore& core) : MaterialDataGainMethodBase(core)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};

WD_NAMESPACE_END