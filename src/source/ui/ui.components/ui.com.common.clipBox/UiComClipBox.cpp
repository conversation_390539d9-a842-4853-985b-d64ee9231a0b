#include    "UiComClipBox.h"

UiComClipBox::UiComClipBox(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    : IUiComponent(mainWindow, attrs)
{
    _pClipBoxDialog = new ClipBoxDialog(mWindow().core(), mWindow().widget());
}
UiComClipBox::~UiComClipBox()
{
    if (_pClipBoxDialog != nullptr)
    {
        delete _pClipBoxDialog;
        _pClipBoxDialog = nullptr;
    }
}

void    UiComClipBox::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        if (pActionNotice->action().is("action.tool.clip.node"))
        {
            if (_pClipBoxDialog->isVisible())
                _pClipBoxDialog->activateWindow();
            else
                _pClipBoxDialog->show();
            // 如果窗口处于最小化，则恢复正常状态
            if (_pClipBoxDialog->isMinimized())
            {
                _pClipBoxDialog->setWindowState(Qt::WindowNoState);
            }
        }
    }
    break;
    default:
        break;
    }
}