#include    "ClipBoxDialog.h"
#include    "viewer/WDViewer.h"
#include    "nodeTree/WDNodeTree.h"
#include    "WDCore.h"
#include    "core/businessModule/WDBMBase.h"
#include    "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include    "core/viewer/primitiveRender/WDLineRender.h"
#include    "core/axis/WDEditSingleAxisMove.h"
#include    "core/businessModule/WDBMProject.h"
#include    "core/viewer/objectAxisEditor/WDAxisEditObjectNode.h"

WD_NAMESPACE_BEGIN

ClipBoxRenderObject::ClipBoxRenderObject(ClipBoxDialog& d)
    : WDRenderObject(WDRenderLayer::RL_Scene)
    , _d(d)
{

}
ClipBoxRenderObject::~ClipBoxRenderObject()
{

}
void ClipBoxRenderObject::updateAabb(WDContext& , const WDScene& )
{
    this->aabbRef() = DAabb3(DVec3(-0.5), DVec3(0.5));
    this->aabbRef().transform(_d._pHelpterNode->localTransform());
}
void ClipBoxRenderObject::update(WDContext& , const WDScene& )
{
}
void ClipBoxRenderObject::render(WDContext & context, const WDScene&)
{
    WDLineRender lineRender;
    WDLineRender::Param param;
    param.color = Color::red;

    auto aabb       = DAabb3(DVec3(-0.5), DVec3(0.5));
    auto center     = FVec3(aabb.center());
    auto size       = FVec3(aabb.size());
    auto halfSize   = FVec3(aabb.size() * 0.5);

    std::vector<FVec3> points;
    //根据给定的位置和大小计算线框的点
    points.resize(4);
    //先加前面
    points[0] = center + FVec3(-halfSize.x, -halfSize.y, -halfSize.z);
    points[1] = center + FVec3(halfSize.x, -halfSize.y, -halfSize.z);
    points[2] = center + FVec3(halfSize.x, -halfSize.y, halfSize.z);
    points[3] = center + FVec3(-halfSize.x, -halfSize.y, halfSize.z);
    lineRender.addLineLoop(points, param);
    //再加后面
    points[0] = center + FVec3(-halfSize.x, halfSize.y, -halfSize.z);
    points[1] = center + FVec3(halfSize.x, halfSize.y, -halfSize.z);
    points[2] = center + FVec3(halfSize.x, halfSize.y, halfSize.z);
    points[3] = center + FVec3(-halfSize.x, halfSize.y, halfSize.z);
    lineRender.addLineLoop(points, param);
    //再加其他的4条的边线
    points.resize(8);
    points[0] = center + FVec3(-halfSize.x, -halfSize.y, -halfSize.z);
    points[1] = points[0] + FVec3(0.0f, size.y, 0.0f);

    points[2] = center + FVec3(halfSize.x, -halfSize.y, -halfSize.z);
    points[3] = points[2] + FVec3(0.0f, size.y, 0.0f);

    points[4] = center + FVec3(halfSize.x, -halfSize.y, halfSize.z);
    points[5] = points[4] + FVec3(0.0f, size.y, 0.0f);

    points[6] = center + FVec3(-halfSize.x, -halfSize.y, halfSize.z);
    points[7] = points[6] + FVec3(0.0f, size.y, 0.0f);
    lineRender.addLineSeg(points, param);

    lineRender.setTransform(FMat4(_d._pHelpterNode->globalTransform()));
    lineRender.render(context, false);
}

WD_NAMESPACE_END

static  const   constexpr   char*   cb_front    =   "clip_box_front";
static  const   constexpr   char*   cb_behind   =   "clip_box_behind";
static  const   constexpr   char*   cb_left     =   "clip_box_left";
static  const   constexpr   char*   cb_right    =   "clip_box_right";
static  const   constexpr   char*   cb_up       =   "clip_box_up";
static  const   constexpr   char*   cb_down     =   "clip_box_down";

ClipBoxDialog::ClipBoxDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _renderObject(*this)
    , _core(core)
    , _ceHelpter(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 添加最大最小化
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowMinMaxButtonsHint, true));
    _ceHelpter.setLineEdit(ui.lineEditCE);
    _ceHelpter.setPushButton(ui.pushButtonCE);

    connect(&_ceHelpter, &UiCESelectHelpter::sigCurrentNodeChanged, this, [=](WD::WDNode* pNode, WD::WDNode* )
        {
            if (pNode != nullptr)
            {
                const auto& aabb = pNode->aabb();
                // 更新到界面
                setToUi(aabb);
                // 更新剖切数据
                updateClip(aabb);
            }
            // 设置节点的剖切标志
            this->setNodeClipFlagByUi();
            // 
            this->uiDataChanged();
        });

    this->retranslateUi();

    _pHelpterNode = WD::ClipHelpterNode::MakeShared();

    QObject::connect(ui.checkBoxClipLocal, &QCheckBox::clicked, this, [=](bool bCheck)
        {
            WDUnused(bCheck);
            // 设置节点的剖切标志
            this->setNodeClipFlagByUi();
            // 
            this->uiDataChanged();
        });
    QObject::connect(ui.checkBoxClipInside, &QCheckBox::clicked, this, [=](bool bCheck)
        {
            WDUnused(bCheck);
            this->uiDataChanged();
        });

    QObject::connect(ui.doubleSpinBoxXLength, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, [=](double value)
        {
            WDUnused(value);
            this->uiDataChanged();
        });
    QObject::connect(ui.doubleSpinBoxYLength, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, [=](double value)
        {
            WDUnused(value);
            this->uiDataChanged();
        });
    QObject::connect(ui.doubleSpinBoxZLength, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, [=](double value)
        {
            WDUnused(value);
            this->uiDataChanged();
        });

    QObject::connect(ui.doubleSpinBoxXPos, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, [=](double value)
        {
            WDUnused(value);
            this->uiDataChanged();
        });
    QObject::connect(ui.doubleSpinBoxYPos, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, [=](double value)
        {
            WDUnused(value);
            this->uiDataChanged();
        });
    QObject::connect(ui.doubleSpinBoxZPos, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, [=](double value)
        {
            WDUnused(value);
            this->uiDataChanged();
        });


    QObject::connect(ui.horizontalSliderRotX, &QSlider::valueChanged, this, [=](int )
        {
            this->uiDataChanged();
        });
    QObject::connect(ui.horizontalSliderRotY, &QSlider::valueChanged, this, [=](int )
        {
            this->uiDataChanged();
        });
    QObject::connect(ui.horizontalSliderRotZ, &QSlider::valueChanged, this, [=](int )
        {
            this->uiDataChanged();
        });

    QObject::connect(ui.pushButtonRotResetX, &QPushButton::clicked, this, [=]()
        {
            ui.horizontalSliderRotX->setValue(0);
        });
    QObject::connect(ui.pushButtonRotResetY, &QPushButton::clicked, this, [=]()
        {
            ui.horizontalSliderRotY->setValue(0);
        });
    QObject::connect(ui.pushButtonRotResetZ, &QPushButton::clicked, this, [=]()
        {
            ui.horizontalSliderRotZ->setValue(0);
        });

    QObject::connect(ui.groupBoxEditEnabled, &QGroupBox::clicked, this, [=](bool )
        {
            this->editStateChanged();
        });
    QObject::connect(ui.radioButtonMove, &QRadioButton::clicked, this, [=](bool)
        {
            this->editStateChanged();
        });
    QObject::connect(ui.radioButtonScale, &QRadioButton::clicked, this, [=](bool)
        {
            this->editStateChanged();
        });
    QObject::connect(ui.radioButtonRotate, &QRadioButton::clicked, this, [=](bool)
        {
            this->editStateChanged();
        });
    QObject::connect(ui.radioButtonPlane, &QRadioButton::clicked, this, [=](bool)
        {
            this->editStateChanged();
        });

    QObject::connect(ui.pushButtonClose, &QPushButton::clicked, this, [=]()
        {
            this->close();
        });
}
ClipBoxDialog::~ClipBoxDialog()
{    
    //移除观察
    if (_pHelpterNode != nullptr)
        _pHelpterNode->observers() -= this;

    //禁用剖切
    auto& clip = _core.viewer().clip();
    clip.flags().setFlag(WD::WDClip::Flag::F_Enabled, false);

    //移除绘制对象
    {
        _core.scene().removeRenderObject(&_renderObject);
        _core.scene().needUpdate();
    }
    //移除场景中的轴
    {
        _core.viewer().singleMoveAxisMgr().removeAxis(cb_front);
        _core.viewer().singleMoveAxisMgr().removeAxis(cb_behind);
        _core.viewer().singleMoveAxisMgr().removeAxis(cb_left);
        _core.viewer().singleMoveAxisMgr().removeAxis(cb_right);
        _core.viewer().singleMoveAxisMgr().removeAxis(cb_up);
        _core.viewer().singleMoveAxisMgr().removeAxis(cb_down);
    }
}

void    ClipBoxDialog::showEvent(QShowEvent* )
{
    // 点击标题栏最小化后，QDialog内部触发改事件，避免最小化时重置包围盒参数
    if(isMinimized())
        return;
    auto& nodeTree  = _core.nodeTree();
    auto& viewer    = _core.viewer();
    // 使用一个默认的包围盒
    WD::DAabb3 aabb = WD::DAabb3(WD::DVec3(-1000.0), WD::DVec3(1000.0));
    // 使用当前树上选中的节点包围盒来构造剖切盒子
    auto pNode = nodeTree.currentNode();
    if (pNode != nullptr)
    {
        aabb = pNode->aabb();
        SetCENodeToUiHelpter(_ceHelpter, pNode);
    }
    // 否则默认使用当前模块根节点包围盒构造剖切盒子
    else
    {
        auto pBMBase = _core.getBMBase(_core.moduleType());
        if (pBMBase != nullptr && pBMBase->root() != nullptr)
        {
            aabb = pBMBase->root()->aabb();
            SetCENodeToUiHelpter(_ceHelpter, pBMBase->root());
        }
        else
        {
            SetCENodeToUiHelpter(_ceHelpter, nullptr);
        }
    }
    // 初始化轴,放在这里而不放在构造是因为构造时core中未包含viewer对象
    this->axisInit(viewer);
    // 更新包围盒数据到界面
    this->setToUi(aabb);
    this->setToNode(aabb);

    auto& clip = viewer.clip();

    // 设置节点的剖切标志
    setNodeClipFlagByUi();
    // 启用剖切
    clip.flags().setFlag(WD::WDClip::Flag::F_Enabled, true);
    // 更新剖切数据
    updateClip(aabb);
    // 判断编辑状态
    editStateChanged();

    // 添加观察
    if (_pHelpterNode != nullptr)
        _pHelpterNode->observers() += this;

    // 添加绘制对象
    _core.scene().addRenderObject(&_renderObject);
}
void    ClipBoxDialog::hideEvent(QHideEvent* evt)
{
    QDialog::hideEvent(evt);
}
void    ClipBoxDialog::closeEvent(QCloseEvent* evt)
{
    WDUnused(evt);

    // 设置当前节点为空
    SetCENodeToUiHelpter(_ceHelpter, nullptr);
    // 取消之前节点的剖切标志
    SetClipFlag(_clipFalgRoot, false);
    _clipFalgRoot.reset();

    // 移除观察
    if (_pHelpterNode != nullptr)
        _pHelpterNode->observers() -= this;

    // 如果正在进行编辑，则取消当前正在进行的编辑
    if (ui.groupBoxEditEnabled->isChecked())
    {
        _core.viewer().objectAxisEditor().setObject(nullptr);
        _core.viewer().objectAxisEditor().setEditType(WD::WDObjectAxisEditor::EditType::ET_None);
    }
    setSingleAxisVisible(_core.viewer(), false);

    // 禁用剖切
    auto& clip = _core.viewer().clip();
    clip.flags().setFlag(WD::WDClip::Flag::F_Enabled, false);

    // 移除绘制对象
    _core.scene().removeRenderObject(&_renderObject);
    _core.scene().needUpdate();

    _core.needRepaint();
}

void    ClipBoxDialog::mAxisLeftChanged(WD::WDEditAxis::EditStatus status, const WD::DVec3& vec, const WD::DVec3& absVec
    , WD::WDEditSingleAxisMove& sender)
{
    WDUnused3(status, absVec, sender);
    auto aabb = getUiData();
    auto center = aabb.center() + vec / 2;

    auto distance = WD::DVec3::Dot(sender.dir(), vec);
    aabb.min.x -= distance;
    if (aabb.min.x > aabb.max.x)
        aabb.max.x = aabb.min.x;

    auto size = aabb.size();
    aabb.min = center - size / 2;
    aabb.max = center + size / 2;
    auto quat = WD::DEuler::ToQuat(WD::DEuler(ui.horizontalSliderRotX->value()
        , ui.horizontalSliderRotY->value()
        , ui.horizontalSliderRotZ->value()));
    setToUi(aabb, quat);
    uiDataChanged();
}
void    ClipBoxDialog::mAxisRightChanged(WD::WDEditAxis::EditStatus status, const WD::DVec3& vec, const WD::DVec3& absVec
    , WD::WDEditSingleAxisMove& sender)
{
    WDUnused3(status, absVec, sender);

    auto aabb = getUiData();
    auto center = aabb.center() + vec / 2;

    auto distance = WD::DVec3::Dot(sender.dir(), vec);
    aabb.max.x += distance;
    if (aabb.max.x < aabb.min.x)
        aabb.min.x = aabb.max.x;

    auto size = aabb.size();
    aabb.min = center - size / 2;
    aabb.max = center + size / 2;
    auto quat = WD::DEuler::ToQuat(WD::DEuler(ui.horizontalSliderRotX->value()
        , ui.horizontalSliderRotY->value()
        , ui.horizontalSliderRotZ->value()));
    setToUi(aabb, quat);
    uiDataChanged();
}
void    ClipBoxDialog::mAxisFrontChanged(WD::WDEditAxis::EditStatus status, const WD::DVec3& vec, const WD::DVec3& absVec
    , WD::WDEditSingleAxisMove& sender)
{
    WDUnused3(status, absVec, sender);
    auto aabb = getUiData();
    auto center = aabb.center() + vec / 2;

    auto distance = WD::DVec3::Dot(sender.dir(), vec);
    aabb.min.y -= distance;
    if (aabb.min.y > aabb.max.y)
        aabb.max.y = aabb.min.y;

    auto size = aabb.size();
    aabb.min = center - size / 2;
    aabb.max = center + size / 2;
    auto quat = WD::DEuler::ToQuat(WD::DEuler(ui.horizontalSliderRotX->value()
        , ui.horizontalSliderRotY->value()
        , ui.horizontalSliderRotZ->value()));
    setToUi(aabb, quat);
    uiDataChanged();
}
void    ClipBoxDialog::mAxisBehindChanged(WD::WDEditAxis::EditStatus status, const WD::DVec3& vec, const WD::DVec3& absVec
    , WD::WDEditSingleAxisMove& sender)
{
    WDUnused3(status, absVec, sender);
    auto aabb = getUiData();
    auto center = aabb.center() + vec / 2;

    auto distance = WD::DVec3::Dot(sender.dir(), vec);
    aabb.max.y += distance;
    if (aabb.max.y < aabb.min.y)
        aabb.min.y = aabb.max.y;

    auto size = aabb.size();
    aabb.min = center - size / 2;
    aabb.max = center + size / 2;
    auto quat = WD::DEuler::ToQuat(WD::DEuler(ui.horizontalSliderRotX->value()
        , ui.horizontalSliderRotY->value()
        , ui.horizontalSliderRotZ->value()));
    setToUi(aabb, quat);
    uiDataChanged();
}
void    ClipBoxDialog::mAxisDownChanged(WD::WDEditAxis::EditStatus status, const WD::DVec3& vec, const WD::DVec3& absVec
    , WD::WDEditSingleAxisMove& sender)
{
    WDUnused3(status, absVec, sender);
    auto aabb = getUiData();
    auto center = aabb.center() + vec / 2;

    auto distance = WD::DVec3::Dot(sender.dir(), vec);
    aabb.min.z -= distance;
    if (aabb.min.z > aabb.max.z)
        aabb.max.z = aabb.min.z;

    auto size = aabb.size();
    aabb.min = center - size / 2;
    aabb.max = center + size / 2;
    auto quat = WD::DEuler::ToQuat(WD::DEuler(ui.horizontalSliderRotX->value()
        , ui.horizontalSliderRotY->value()
        , ui.horizontalSliderRotZ->value()));
    setToUi(aabb, quat);
    uiDataChanged();
}
void    ClipBoxDialog::mAxisUpChanged(WD::WDEditAxis::EditStatus status, const WD::DVec3& vec, const WD::DVec3& absVec
    , WD::WDEditSingleAxisMove& sender)
{
    WDUnused3(status, absVec, sender);
    auto aabb = getUiData();
    auto center = aabb.center() + vec / 2;

    auto distance = WD::DVec3::Dot(sender.dir(), vec);
    aabb.max.z += distance;
    if (aabb.max.z < aabb.min.z)
        aabb.min.z = aabb.max.z;

    auto size = aabb.size();
    aabb.min = center - size / 2;
    aabb.max = center + size / 2;

    auto quat = WD::DEuler::ToQuat(WD::DEuler(ui.horizontalSliderRotX->value()
        , ui.horizontalSliderRotY->value()
        , ui.horizontalSliderRotZ->value()));
    setToUi(aabb, quat);
    uiDataChanged();
}

void    ClipBoxDialog::onNodeUpdateAfter(WD::WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;
    assert(pNode == _pHelpterNode);
    if (pNode != _pHelpterNode)
        return;
    const auto& center = _pHelpterNode->localT();
    const auto& size = _pHelpterNode->localS();
    auto halfSize = size * 0.5;

    auto aabb = WD::DAabb3(center - halfSize, center + halfSize);
    const auto& quat = _pHelpterNode->localR();
    // 更新到界面
    setToUi(aabb, quat);
    // 更新剖切数据
    updateClip(aabb, quat);
}
void    ClipBoxDialog::setToUi(const WD::DAabb3& aabb, const WD::DQuat& rotate)
{
    // 位置
    const auto& center = aabb.center();

    ui.doubleSpinBoxXPos->blockSignals(true);
    ui.doubleSpinBoxYPos->blockSignals(true);
    ui.doubleSpinBoxZPos->blockSignals(true);

    ui.doubleSpinBoxXPos->setValue(center.x);
    ui.doubleSpinBoxYPos->setValue(center.y);
    ui.doubleSpinBoxZPos->setValue(center.z);

    ui.doubleSpinBoxXPos->blockSignals(false);
    ui.doubleSpinBoxYPos->blockSignals(false);
    ui.doubleSpinBoxZPos->blockSignals(false);

    // 尺寸/缩放
    auto size = aabb.size();

    ui.doubleSpinBoxXLength->blockSignals(true);
    ui.doubleSpinBoxYLength->blockSignals(true);
    ui.doubleSpinBoxZLength->blockSignals(true);

    ui.doubleSpinBoxXLength->setValue(size.x);
    ui.doubleSpinBoxYLength->setValue(size.y);
    ui.doubleSpinBoxZLength->setValue(size.z);

    ui.doubleSpinBoxXLength->blockSignals(false);
    ui.doubleSpinBoxYLength->blockSignals(false);
    ui.doubleSpinBoxZLength->blockSignals(false);

    // 旋转
    auto rEuler = WD::Euler::FromQuat(rotate);

    ui.horizontalSliderRotX->blockSignals(true);
    ui.horizontalSliderRotY->blockSignals(true);
    ui.horizontalSliderRotZ->blockSignals(true);

    ui.horizontalSliderRotX->setValue(rEuler.x);
    ui.horizontalSliderRotY->setValue(rEuler.y);
    ui.horizontalSliderRotZ->setValue(rEuler.z);

    ui.horizontalSliderRotX->blockSignals(false);
    ui.horizontalSliderRotY->blockSignals(false);
    ui.horizontalSliderRotZ->blockSignals(false);

}
void    ClipBoxDialog::setToNode(const WD::DAabb3& aabb, const WD::DQuat& rotate)
{
    if (_pHelpterNode == nullptr)
        return;

    // 位置
    const auto& center = aabb.center();
    _pHelpterNode->setLocalT(center);
    // 缩放
    auto size = aabb.size();
    _pHelpterNode->setLocalS(size);
    // 旋转
    _pHelpterNode->setLocalR(rotate);

    // 移除观察者，防止触发更新事件
    _pHelpterNode->observers() -= this;
    _pHelpterNode->update();
    // 更新完之后，重新添加观察者
    _pHelpterNode->observers() += this;
}
void    ClipBoxDialog::updateClip(const WD::DAabb3& aabb, const WD::DQuat& rotate)
{
    auto& viewer    = _core.viewer();
    auto& clip      = viewer.clip();

    // 局部剖切标志
    bool bLocalClip = ui.checkBoxClipLocal->isChecked();
    clip.flags().setFlag(WD::WDClip::Flag::F_Local, bLocalClip);
    // 内剖切标志
    bool bInsideClip = ui.checkBoxClipInside->isChecked();
    clip.flags().setFlag(WD::WDClip::Flag::F_Inside, bInsideClip);

    auto tAabb = aabb;
    // 这里稍微放大或缩小一点包围盒,以避免误差
    if (bInsideClip)
        tAabb.expandByScalar(0.99);
    else
        tAabb.expandByScalar(1.01);

    if (ui.radioButtonPlane->isChecked())
    {
        auto& singleAxisMgr = viewer.singleMoveAxisMgr();
        const auto center = aabb.center();
        auto aabbHalfSz = aabb.size() * 0.5;
        // 保存所有轴的名称,方向和距离中心点的距离 -> 顺序： 前 后 左 右 下 上
        std::array<std::string_view, 6> names = {cb_front, cb_behind, cb_left, cb_right, cb_down, cb_up};
        std::array<WD::DVec3, 6> dirs = {WD::DVec3::AxisNY(), WD::DVec3::AxisY()
            ,  WD::DVec3::AxisNX(),  WD::DVec3::AxisX()
            ,  WD::DVec3::AxisNZ(),  WD::DVec3::AxisZ()};
        std::array<double, 6> lens = {aabbHalfSz.y, aabbHalfSz.y, aabbHalfSz.x, aabbHalfSz.x, aabbHalfSz.z, aabbHalfSz.z};

        for (size_t i = 0; i < 6; ++i)
        {
            auto dir =  rotate * dirs[i];
            auto pos =  center + dir * lens[i];
            auto pAxis = singleAxisMgr.axis(names[i].data());
            if (pAxis == nullptr)
            {
                assert(false);
                continue;
            }
            // 设置各轴的方向,位置和样式
            pAxis->setDir(dir);
            pAxis->setPosition(pos);
        }
    }

    // 使用Aabb包围盒设置剖切数据
    clip.setClipDataByAabb(aabb, rotate);
    // 调用场景更新
    _core.sceneForceUpdate();
    // 重绘
    _core.needRepaint();
}

void    ClipBoxDialog::setNodeClipFlagByUi()
{
    // 取消之前节点的剖切标志
    SetClipFlag(_clipFalgRoot, false);
    // 全局剖切还是局部剖切
    bool bLocalClip = ui.checkBoxClipLocal->isChecked();
    if (bLocalClip)
    {
        auto pNode = _ceHelpter.node();
        auto pBMBase = _core.getBMBase(_core.moduleType());
        if (pNode != nullptr)
            _clipFalgRoot = pNode;
        else if (pBMBase != nullptr && pBMBase->root() != nullptr)
            _clipFalgRoot = pBMBase->root();
        else
            _clipFalgRoot.reset();
    }
    else
    {
        auto pBMBase = _core.getBMBase(_core.moduleType());
        if (pBMBase != nullptr && pBMBase->root() != nullptr)
            _clipFalgRoot = pBMBase->root();
        else
            _clipFalgRoot.reset();
    }

    // 设置剖切标志
    SetClipFlag(_clipFalgRoot, true);
}
void    ClipBoxDialog::editStateChanged()
{
    auto& viewer = _core.viewer();

    setSingleAxisVisible(viewer, false);
    if (this->isVisible() && ui.groupBoxEditEnabled->isChecked())
    {
        viewer.objectAxisEditor().setObject(WD::WDAxisEditObjectNode::MakeShared(_pHelpterNode, false));
        if (ui.radioButtonMove->isChecked())
            viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::ET_Move);
        else if (ui.radioButtonScale->isChecked())
            viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::ET_Scale);
        else if (ui.radioButtonRotate->isChecked())
            viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::ET_Rotate);
        else if (ui.radioButtonPlane->isChecked())
        {
            // 隐藏编辑轴
            viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::ET_None);
            // 面编辑未打开时,面编辑轴数据不会更新,这里打开面编辑前手动调用数据更新
            uiDataChanged();
            setSingleAxisVisible(viewer, true);
        }
        else
            viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::ET_Move);
        viewer.objectAxisEditor().setEditCoordinateSysType(WD::WDObjectAxisEditor::EditCoordinateSysType::ECST_Local);
    }
    else
    {
        viewer.objectAxisEditor().setObject(nullptr);
        viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::ET_None);
    }
    _core.needRepaint();
}

void    ClipBoxDialog::uiDataChanged()
{
    auto aabb = getUiData();
    auto quat = WD::DEuler::ToQuat(WD::DEuler(ui.horizontalSliderRotX->value()
        , ui.horizontalSliderRotY->value()
        , ui.horizontalSliderRotZ->value()));
    // 更新到节点
    setToNode(aabb, quat);
    // 更新剖切数据
    updateClip(aabb, quat);
}
WD::DAabb3  ClipBoxDialog::getUiData()
{
    auto center     = WD::DVec3(ui.doubleSpinBoxXPos->value()
        , ui.doubleSpinBoxYPos->value()
        , ui.doubleSpinBoxZPos->value());
    auto size       = WD::DVec3(ui.doubleSpinBoxXLength->value()
        , ui.doubleSpinBoxYLength->value()
        , ui.doubleSpinBoxZLength->value());
    auto halfSize   = size * 0.5;
    auto aabb       = WD::DAabb3(center - halfSize, center + halfSize);
    return aabb;
}

void    ClipBoxDialog::setSingleAxisVisible(WD::WDViewer& viewer, bool bVisible)
{
    viewer.singleMoveAxisMgr().setAxisVisible(bVisible, cb_front);
    viewer.singleMoveAxisMgr().setAxisVisible(bVisible, cb_behind);
    viewer.singleMoveAxisMgr().setAxisVisible(bVisible, cb_left);
    viewer.singleMoveAxisMgr().setAxisVisible(bVisible, cb_right);
    viewer.singleMoveAxisMgr().setAxisVisible(bVisible, cb_up);
    viewer.singleMoveAxisMgr().setAxisVisible(bVisible, cb_down);
}

void    ClipBoxDialog::SetClipFlag(WD::WDNode::WeakPtr& node, bool bOn)
{
    if (node.expired())
        return;
    auto pNode = node.lock();
    WD::WDNode::RecursionHelpter(*pNode, [bOn](WD::WDNode& node)
        {
            auto flags = node.flags();
            flags.setFlag(WD::WDNode::Flag::F_Clip, bOn);
            node.setFlags(flags);
        });
}

void    ClipBoxDialog::SetCENodeToUiHelpter(UiCESelectHelpter& helpter, WD::WDNode::SharedPtr pNode)
{
    helpter.blockSignals(true);
    helpter.setNode(pNode);
    helpter.blockSignals(false);
}

void    ClipBoxDialog::axisInit(WD::WDViewer& viewer)
{
    if (!bInit)
    {
        WD::WDEditSingleAxisMove::SingleAxisMovePattern pattern(2.0f, 0.0f, 1.0f, 1.0f);

        auto& singleAxisMgr = viewer.singleMoveAxisMgr();
        auto pFrontAxis = singleAxisMgr.addAxis(cb_front);
        assert(pFrontAxis != nullptr);
        if (pFrontAxis != nullptr)
        {
            pFrontAxis->mDelegate() += {this, &ClipBoxDialog::mAxisFrontChanged};
            pFrontAxis->setPattern(pattern);
        }

        auto pBehindAxis = singleAxisMgr.addAxis(cb_behind);
        assert(pBehindAxis != nullptr);
        if (pBehindAxis != nullptr)
        {
            pBehindAxis->mDelegate() += {this, &ClipBoxDialog::mAxisBehindChanged};
            pBehindAxis->setPattern(pattern);
        }

        auto pLeftAxis = singleAxisMgr.addAxis(cb_left);
        assert(pLeftAxis != nullptr);
        if (pLeftAxis != nullptr)
        {
            pLeftAxis->mDelegate() += {this, &ClipBoxDialog::mAxisLeftChanged};
            pLeftAxis->setPattern(pattern);
        }

        auto pRightAxis = singleAxisMgr.addAxis(cb_right);
        assert(pRightAxis != nullptr);
        if (pRightAxis != nullptr)
        {
            pRightAxis->mDelegate() += {this, &ClipBoxDialog::mAxisRightChanged};
            pRightAxis->setPattern(pattern);
        }

        auto pUpAxis = singleAxisMgr.addAxis(cb_up);
        assert(pUpAxis != nullptr);
        if (pUpAxis != nullptr)
        {
            pUpAxis->mDelegate() += {this, &ClipBoxDialog::mAxisUpChanged};
            pUpAxis->setPattern(pattern);
        }

        auto pDownAxis = singleAxisMgr.addAxis(cb_down);
        assert(pDownAxis != nullptr);
        if (pDownAxis != nullptr)
        {
            pDownAxis->mDelegate() += {this, &ClipBoxDialog::mAxisDownChanged};
            pDownAxis->setPattern(pattern);
        }
        bInit = true;
    }

}

void    ClipBoxDialog::retranslateUi()
{
    Trs("UiComClipBox"
        , static_cast<QDialog*>(this)

        , ui.pushButtonCE

        , ui.checkBoxClipLocal
        , ui.checkBoxClipInside

        , ui.groupBox
        , ui.labelX
        , ui.labelY
        , ui.labelZ

        , ui.groupBox_2
        , ui.labelPosX
        , ui.labelPosY
        , ui.labelPosZ

        , ui.groupBox_3
        , ui.labelRotX
        , ui.labelRotY
        , ui.labelRotZ

        , ui.groupBoxEditEnabled
        , ui.radioButtonMove
        , ui.radioButtonScale
        , ui.radioButtonRotate
        , ui.radioButtonPlane

        , ui.pushButtonClose
    );
}
