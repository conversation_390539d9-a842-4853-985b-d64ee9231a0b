#include    "BatchDrawIsoDialog.h"
#include    "core/nodeTree/WDNodeTree.h"
#include    "core/nodeTree/WDNodeList.h"
#include    "core/message/WDMessage.h"
#include    <QFileDialog>
#include    "ui.com.svg.viewer.h"
#include    <QPdfWriter>
#include    "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include    "core/WDBlockingTask.h"
#include    "IsoIndexTable.h"

enum CBBType
{
    CBBT_CE = 0,
    CBBT_List,
};

static QMarginsF SMarginPDF = QMarginsF(0, 0, 0, 0);

BatchDrawIsoDialog::BatchDrawIsoDialog(WD::WDCore& core
    , ISOPaperMgr& isoPaperMgr
    , UIComSvgViewer& viewer
    , const WD::IsoIndexTable::IndexTableInfo& indexTableInfo
    , QWidget *parent)
    : QDialog(parent)
    , _core(core)
    , _isoPaperMgr(isoPaperMgr)
    , _viewer(viewer)
    , indexTableInfo(indexTableInfo)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 设置列表可多选
    ui.listWidget->setSelectionMode(QAbstractItemView::SelectionMode::MultiSelection);
    ui.summaryCheckBox->setChecked(false);
    
    connect(ui.summaryCheckBox, &QCheckBox::stateChanged, this, &BatchDrawIsoDialog::slotCheckBoxSummaryClicked);
    ui.outputPaperSize->addItem("A0", WD::ISOPaper::PT_A0);
    ui.outputPaperSize->addItem("A1", WD::ISOPaper::PT_A1);
    ui.outputPaperSize->addItem("A2", WD::ISOPaper::PT_A2);
    ui.outputPaperSize->addItem("A3", WD::ISOPaper::PT_A3);
    ui.outputPaperSize->addItem("A4", WD::ISOPaper::PT_A4);
    ui.outputPaperSize->setCurrentIndex(2);
    connect(ui.outputPaperSize, QOverload<int>::of(&QComboBox::currentIndexChanged),this,&BatchDrawIsoDialog::slotPaperSizeChanged);

    ui.comboBoxTableFormat->addItem("PDF",   (int)FileFormat::FF_PDF);
    ui.comboBoxTableFormat->addItem("Excel", (int)FileFormat::FF_Excel);
    ui.comboBoxTableFormat->setCurrentIndex(0);
    ui.comboBoxIsoFormat->addItem("PDF", (int)FileFormat::FF_PDF);
    ui.comboBoxIsoFormat->addItem("DXF", (int)FileFormat::FF_DXF);
    ui.comboBoxIsoFormat->setCurrentIndex(0);
    connect(ui.comboBoxIsoFormat, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &BatchDrawIsoDialog::slotIsoFormatChanged);

    // 初始化添加菜单
    _addMenuBar = new QMenuBar(this);
    // 显示边框
    _addMenuBar->setStyleSheet("QMenuBar{border: 1px solid gray;}");
    QMenu* addMenu = _addMenuBar->addMenu("Add");
    QAction* ceAct = new QAction("CE", addMenu);
    connect(ceAct, &QAction::triggered, this, [=](){
        auto pNode = _core.nodeTree().currentNode();
        if (pNode == nullptr || !pNode->isType("PIPE"))
        {
            WD_ERROR_T("UiComSvgViewer", "CheckNodes");
            return;
        }
        bool findFlag = false;
        for(int i = 0; i < ui.listWidget->count(); ++i)
        {
            auto pItem = ui.listWidget->item(i);
            if(pItem == nullptr)
                continue;
            auto pItemNode = getItemUData(*pItem);
            if(pItemNode == pNode)
            {
                findFlag = true;
            }
        }
        if(!findFlag)
        {
            QListWidgetItem* pItem = new QListWidgetItem(QString::fromUtf8(pNode->name().c_str()));
            setItemUData(*pItem, pNode);
            ui.listWidget->addItem(pItem);
        }
    });
    QAction* ceMemberAct = new QAction("Member", addMenu);
    connect(ceMemberAct, &QAction::triggered, this, [=](){
        auto pNode = _core.nodeTree().currentNode();
        if (pNode == nullptr)
            return;
        for(auto pChilNode : pNode->children())
        {
            if(pChilNode == nullptr || !pChilNode->isType("PIPE"))
                continue;
            bool findFlag = false;
            for (int i = 0; i < ui.listWidget->count(); ++i)
            {
                auto pItem = ui.listWidget->item(i);
                if (pItem == nullptr)
                    continue;
                auto pItemNode = getItemUData(*pItem);
                if (pItemNode == pNode)
                {
                    findFlag = true;
                }
            }
            if (!findFlag)
            {
                QListWidgetItem* pItem = new QListWidgetItem(QString::fromUtf8(pChilNode->name().c_str()));
                setItemUData(*pItem, pChilNode);
                ui.listWidget->addItem(pItem);
            }
        }
    });
    QAction* listAct = new QAction("CurrList", addMenu);
    connect(listAct, &QAction::triggered, this, [=](){
        auto pList = _core.nodeListMgr().current();
        if (pList == nullptr)
        {
            WD_ERROR_T("UiComSvgViewer", "CheckListObject");
            return;
        }
        auto nodes = pList->nodes();
        if(nodes.empty())
        {
            WD_ERROR_T("UiComSvgViewer", "CheckListObject");
            return;
        }
        int allCnt = static_cast<int>(nodes.size());
        int addCnt = 0;
        for(auto pNode: nodes)
        {
            if (pNode == nullptr)
                continue;
            if (!pNode->isType("PIPE"))
                continue;
            
            bool findFlag = false;
            for (int i = 0; i < ui.listWidget->count(); ++i)
            {
                auto pItem = ui.listWidget->item(i);
                if (pItem == nullptr)
                    continue;
                auto pItemNode = getItemUData(*pItem);
                if (pItemNode == pNode)
                {
                    findFlag = true;
                }
            }
            if (!findFlag)
            {
                QListWidgetItem* pItem = new QListWidgetItem(QString::fromUtf8(pNode->name().c_str()));
                setItemUData(*pItem,pNode);
                ui.listWidget->addItem(pItem);
                addCnt++;
            }
        }
        if(addCnt != allCnt)
        {
            char buf[1024] = {0};
            sprintf_s(buf, sizeof(buf), WD::WDTs("BatchDrawIsoDialog", "GeneratedInfo").c_str(), allCnt, addCnt);
            WD_WARN(buf);
        }
    });
    addMenu->addAction(ceAct);
    addMenu->addAction(ceMemberAct);
    addMenu->addAction(listAct);

    // 初始化移除菜单
    _removeMenuBar = new QMenuBar(this);
    _removeMenuBar->setStyleSheet(QString("QMenuBar{border: 1px solid gray;}"));
    QMenu* removeMenu = _removeMenuBar->addMenu("Remove");
    QAction* selectedAct = new QAction("Selected", removeMenu);
    connect(selectedAct, &QAction::triggered, this, [=](){
        auto items = ui.listWidget->selectedItems();
    for(auto pItem : items)
    {
        if(pItem == nullptr)
            continue;
        auto pNode = getItemUData(*pItem);
        if (pItem != nullptr)
        {
            delete pItem;
            pItem = nullptr;
        }
    }
    });
    QAction* allAct = new QAction("All", removeMenu);
    connect(allAct, &QAction::triggered, this, [=]() {
        ui.listWidget->clear();
    });
    removeMenu->addAction(selectedAct);
    removeMenu->addAction(allAct);
    ui.verticalLayout->addWidget(_addMenuBar);
    ui.verticalLayout->addWidget(_removeMenuBar);
    ui.verticalLayout->addStretch();
    retranslateUi();
    
    connect(ui.pushButtonSelectPath, &QPushButton::clicked, this, [=]()
    {
        QString dirPath = QFileDialog::getExistingDirectory(
            this
            , QString::fromUtf8("保存目录")
            , "");

        if (dirPath.isEmpty())
            return;

        ui.lineEditPath->setText(dirPath);
    });

    connect(ui.pushButtonGenerate
        , &QPushButton::clicked
        , this
        ,  &BatchDrawIsoDialog::slotPushButtonGenerateClicked);
}

BatchDrawIsoDialog::~BatchDrawIsoDialog()
{
    if(_addMenuBar != nullptr)
    {
        delete _addMenuBar;
        _addMenuBar = nullptr;
    }
    if(_removeMenuBar != nullptr)
    {
        delete _removeMenuBar;
        _removeMenuBar = nullptr;
    }
}

void BatchDrawIsoDialog::slotCheckBoxSummaryClicked(int status)
{
    WDUnused(status);
    _summary = ui.summaryCheckBox->isChecked();
}

void BatchDrawIsoDialog::slotPaperSizeChanged(int index)
{
    WDUnused(index);
    auto size = WD::ISOPaper::GetPaperSize(ui.outputPaperSize->currentData().toInt());
    _paperSize = ISOPaper::Size(size.width(), size.height());
}

void BatchDrawIsoDialog::slotIsoFormatChanged(int index)
{
    WDUnused(index);
    auto num = ui.comboBoxIsoFormat->currentData().toInt();
    if ((int)FileFormat::FF_PDF == num)
    {
        ui.summaryCheckBox->setVisible(true);
        //ui.outputPaperSize->setVisible(true);
    }
    else
    {
        ui.summaryCheckBox->setVisible(false);
        //ui.outputPaperSize->setVisible(false);
    }
}

void BatchDrawIsoDialog::slotPushButtonGenerateClicked()
{
    _taskUserData.reset();
    QString dirStr = ui.lineEditPath->text();
    if(dirStr.isEmpty())
    {
        WD_ERROR_T("UiComSvgViewer", "CheckPath");
        return;
    }
    QDir dir(dirStr);
    if (!dir.exists())
    {
        WD_ERROR_T("UiComSvgViewer", "CheckPath");
        return ;
    }
    // 图纸对象
    WD::ISOPaper* pPaper = _isoPaperMgr.getCurrentPaper();
    pPaper->setOutputSize(_paperSize);
    // 1. 收集所有节点
    _taskUserData.nodes.reserve(ui.listWidget->count());
    for (int i = 0; i < ui.listWidget->count(); ++i)
    {
        auto pItem = ui.listWidget->item(i);
        if (pItem == nullptr)
            continue;
        auto pNode = getItemUData(*pItem);
        if (pNode == nullptr)
            continue;
        _taskUserData.nodes.push_back(pNode);
    }
    if (_taskUserData.nodes.empty())
    {
        _taskUserData.reset();
        WD_ERROR_T("UiComSvgViewer", "CheckNodes");
        return;
    }

    switch (FileFormat(ui.comboBoxIsoFormat->currentData().toInt()))
    {
    case FileFormat::FF_PDF:
    {
        pPaper->_genType = GenPaperType::GPT_ISO_SVG;
        pPaper->setOutputSize(_paperSize);
        GenSvg(pPaper, dirStr);

    }
    break;
    case FileFormat::FF_DXF:
    {
        pPaper->setSize(_paperSize);
        pPaper->_genType = GenPaperType::GPT_ISO_DXF;
        pPaper->setOutputSize(WD::ISOPaper::Size(594.0f, 420.0f));
        GenDxf(pPaper, dirStr);
    }
    break;
    default:
        break;
    }

    assert(pPaper != nullptr);
    if (pPaper == nullptr)
    {
        WD_ERROR_T("UiComSvgViewer", "CheckObject");
        return;
    }

}

void BatchDrawIsoDialog::GenSvg(WD::ISOPaper* pPaper, const QString& outputDir)
{
    auto pTask = _core.blockingTask();
    if (pTask != nullptr && !pTask->isRunning())
    {
        WD::WDBlockingTask::Config taskCfg;
        taskCfg.progress = true;
        taskCfg.progressBarCount = 1;
        pTask->setConfig(taskCfg);

        pTask->start([this, pPaper, outputDir](WD::WDBlockingTask& task)
            {
                // 2. 生成SVG图纸数据
                double width = pPaper->uCvt.paperToPixel(pPaper->size().width());
                double height = pPaper->uCvt.paperToPixel(pPaper->size().height());
                char szView[1024] = { 0 };
                sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height));

                std::vector<std::pair<std::string, WD::StringVector>> pipeIsos;
                std::vector<WD::ISOPaper::PaperInfo> paperInfo;
                std::unordered_map<std::string, std::string> paperInfos;
                pipeIsos.reserve(_taskUserData.nodes.size());
                paperInfo.reserve(_taskUserData.nodes.size() * 10);

                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "OnGenerateISO").c_str(), 0);
                {
                    // 这里用map来对pipe节点进行排序
                    std::map<std::string, WD::WDNode::SharedPtr> pipeNodes;
                    for (const auto& pNode : _taskUserData.nodes)
                    {
                        if (pNode == nullptr || !pNode->isType("PIPE"))
                            continue;

                        std::string pipeNumberPart;
                        auto pipeName = pNode->name();
                        auto vec = StringSplit(pipeName, "-");
                        if (vec.size() >= 4)
                            pipeNumberPart = vec[2] + '-' + vec[3];

                        if (pipeNumberPart.empty())
                            pipeNumberPart = pNode->name();

                        pipeNodes.emplace(pipeNumberPart, pNode);
                    }

                    const double nodeStep = 1.0 / static_cast<double>(_taskUserData.nodes.size());
                    int num = 0;
                    int number = 0;
                    char drawingNumber[1024] = { 0 };
                    std::string drawingNumberStr;
                    int i = -1;
                    for (const auto& each : pipeNodes)
                    {
                        const auto& pPIPENode = each.second;
                        if (pPIPENode == nullptr || !pPIPENode->isType("PIPE"))
                            continue;
                        //先生成一遍索引号
                        for (int idex = 0; idex < pPIPENode->childCount(); ++idex)
                        {
                            const auto& pBRANNode = pPIPENode->childAt(idex);
                            if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
                                continue;
                            sprintf_s(drawingNumber, sizeof(drawingNumber), "ST-00/ISO-%04d", ++number);
                            paperInfos.insert(std::make_pair(pBRANNode->name(), std::string(drawingNumber)));
                        }
                    }

                    i = -1;
                    for (const auto& each : pipeNodes)
                    {
                        ++i;
                        task.setProgress(i * nodeStep);
                        const auto& pPIPENode = each.second;
                        if (pPIPENode == nullptr || !pPIPENode->isType("PIPE"))
                            continue;
                        size_t childCnt = pPIPENode->children().size();
                        WD::StringVector pipeData;
                        pipeData.reserve(childCnt);
                        size_t index = 0;
                        double subStep = nodeStep / static_cast<double>(pPIPENode->childCount());
                        for (int childIdx = 0; childIdx < pPIPENode->childCount(); ++childIdx)
                        {
                            task.setProgress(i * nodeStep + childIdx * subStep);

                            const auto& pBRANNode = pPIPENode->childAt(childIdx);
                            if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
                                continue;
                            WD::WDSvgXmlCreator* pSvg = new   WD::WDSvgXmlCreator("xml version='1.0' encoding='utf-8' standalone='no'");
                            if (pSvg == nullptr)
                                continue;
                            pSvg->root("svg").attr("viewBox", std::string(szView)).attr("xmlns", "http://www.w3.org/2000/svg")
                                .attr("xmlns:xlink", "http://www.w3.org/1999/  xlink");
                            pPaper->update(pBRANNode->aabb());
                            if (!paperInfos.empty())
                            {
                                auto it = paperInfos.find(pBRANNode->name());
                                if (it != paperInfos.end())
                                {
                                    drawingNumberStr = it->second;
                                }
                            }
                            else
                            {
                                sprintf_s(drawingNumber, sizeof(drawingNumber), "ST-00/ISO-%04d", ++num);
                                drawingNumberStr = std::string(drawingNumber);
                            }

                            _viewer.genISOSvgData(*pPaper
                                , *pBRANNode
                                , *pSvg
                                , static_cast<uint>(index)
                                , static_cast<uint>(childCnt)
                                , drawingNumberStr
                                , paperInfos);
                            paperInfo.emplace_back(pPaper->info);
                            pipeData.emplace_back(pSvg->toString());
                            index++;
                        }
                        if (pipeData.empty())
                            continue;
                        pipeIsos.push_back(std::make_pair(each.first, pipeData));
                    }
                }
                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "ISOGenerateDown").c_str(), 1);

                _taskUserData.allCnt = static_cast<int>(pipeIsos.size());
                // 3. 合并保存为PDF
                WD::StringVector allPipeIso;
                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "MargeAsPdf").c_str(), 0);
                {
                    double step = 0.9 / static_cast<double>(pipeIsos.size());
                    for (int i = 0; i < pipeIsos.size(); ++i)
                    {
                        task.setProgress(i * step);
                        const auto& pipeIso = pipeIsos[i];
                        const auto& pipeData = pipeIso.second;
                        if (pipeData.empty())
                            continue;
                        QString fileName = QString::fromUtf8(pipeIso.first.c_str());
                        fileName.replace("/", "_");
                        fileName.replace("\\", "_");
                        fileName = outputDir + "/" + fileName + ".pdf";
                        if (_summary)
                        {
                            for (auto& pipe : pipeData)
                            {
                                allPipeIso.emplace_back(pipe);
                            }
                        }
                        else
                        {
                            SaveDatasMergeAsPDF(*pPaper, pipeData, fileName);
                        }
                        _taskUserData.successCnt++;
                    }
                    task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "GenerateIndexTable").c_str(), 0.9);
                    switch (FileFormat(ui.comboBoxTableFormat->currentData().toInt()))
                    {
                    case FileFormat::FF_Excel:
                    {
                        QString fileName = outputDir + QString::fromUtf8("\\管段图索引表.xlsx");
                        WD::IsoIndexTable::GenExcelIndexTable(fileName.toUtf8().data(), paperInfo, indexTableInfo);
                    }
                    break;
                    case FileFormat::FF_PDF:
                    {
                        QString fileName = outputDir + QString::fromUtf8("\\管段图索引表.pdf");
                        auto datas = WD::IsoIndexTable::GenSvgIndexTable(paperInfo, pPaper->uCvt, indexTableInfo);
                        SaveDatasMergeAsPDF(*pPaper, datas, fileName, std::vector<WD::DVec2>(datas.size()
                            , WD::DVec2(indexTableInfo.size().x, indexTableInfo.size().y)));
                    }
                    break;
                    default:
                        break;
                    }
                    //所有图纸合并保存的pdf
                    if (_summary && !allPipeIso.empty())
                    {
                        QString allName = outputDir + QString::fromUtf8("/sub.pdf");
                        SaveDatasMergeAsPDF(*pPaper, allPipeIso, allName);
                    }
                }
                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "GenerateDown").c_str(), 1);
            }
            , [this](WD::WDBlockingTask&)
            {
                char buf[1024] = { 0 };
                assert(_taskUserData.allCnt >= _taskUserData.successCnt);
                sprintf_s(buf, sizeof(buf), WD::WDTs("BatchDrawIsoDialog", "GeneratedInfo").c_str(), _taskUserData.allCnt, _taskUserData.successCnt);

                _taskUserData.reset();
                WD_INFO(buf);
            });
    }
    else
    {
        // 2. 生成SVG图纸数据
        double width = pPaper->uCvt.paperToPixel(pPaper->size().width());
        double height = pPaper->uCvt.paperToPixel(pPaper->size().height());
        char szView[1024] = { 0 };
        sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height));

        std::vector<std::pair<std::string, WD::StringVector>> pipeIsos;
        std::vector<WD::ISOPaper::PaperInfo> paperInfo;
        pipeIsos.reserve(_taskUserData.nodes.size());
        paperInfo.reserve(_taskUserData.nodes.size() * 10);
        // 这里用map来对pipe节点进行排序
        std::map<std::string, WD::WDNode::SharedPtr> pipeNodes;
        for (const auto& pNode : _taskUserData.nodes)
        {
            if (pNode == nullptr || !pNode->isType("PIPE"))
                continue;

            std::string pipeNumberPart;
            auto pipeName = pNode->name();
            auto vec = StringSplit(pipeName, "-");
            if (vec.size() >= 4)
                pipeNumberPart = vec[2] + '-' + vec[3];

            if (pipeNumberPart.empty())
                pipeNumberPart = pNode->name();

            pipeNodes.emplace(pipeNumberPart, pNode);
        }
        int i = -1;

        int num = 0;
        char drawingNumber[1024] = { 0 };
        for (const auto& each : pipeNodes)
        {
            ++i;
            const auto& pPIPENode = each.second;
            if (pPIPENode == nullptr || !pPIPENode->isType("PIPE"))
                continue;
            size_t childCnt = pPIPENode->children().size();
            StringVector pipeData;
            pipeData.reserve(childCnt);
            size_t index = 0;
            for (const auto& pBRANNode : pPIPENode->children())
            {
                if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
                    continue;
                WD::WDSvgXmlCreator* pSvg = new   WD::WDSvgXmlCreator("xml version='1.0' encoding='utf-8' standalone='no'");
                if (pSvg == nullptr)
                    continue;
                pSvg->root("svg").attr("viewBox", std::string(szView)).attr("xmlns", "http://www.w3.org/2000/svg").attr("xmlns:xlink", "http://www.w3.org/1999/xlink");
                pPaper->update(pBRANNode->aabb());
                sprintf_s(drawingNumber, sizeof(drawingNumber), "ST-00/ISO-%04d", ++num);
                _viewer.genISOSvgData(*pPaper
                    , *pBRANNode
                    , *pSvg
                    , static_cast<uint>(index)
                    , static_cast<uint>(childCnt)
                    , std::string(drawingNumber));
                paperInfo.emplace_back(pPaper->info);
                pipeData.emplace_back(pSvg->toString());
                index++;

                delete pSvg;
            }
            if (pipeData.empty())
                continue;
            pipeIsos.push_back(std::make_pair(each.first, pipeData));
        }
        _taskUserData.allCnt = static_cast<int>(pipeIsos.size());
        // 3. 合并保存为PDF
        WD::StringVector allPipeIso;
        for (const auto& pipeIso : pipeIsos)
        {
            const auto& pipeData = pipeIso.second;
            if (pipeData.empty())
                continue;
            QString fileName = QString::fromUtf8(pipeIso.first.c_str());
            fileName.replace("/", "_");
            fileName.replace("\\", "_");
            fileName = outputDir + "/" + fileName + ".pdf";
            if (_summary)
            {
                for (auto& pipe : pipeData)
                {
                    allPipeIso.emplace_back(pipe);
                }
            }
            else
            {
                SaveDatasMergeAsPDF(*pPaper, pipeData, fileName);
            }
            _taskUserData.successCnt++;
        }

        switch (FileFormat(ui.comboBoxTableFormat->currentData().toInt()))
        {
        case FileFormat::FF_Excel:
        {
            QString fileName = outputDir + QString::fromUtf8("/管段图索引表.xlsx");
            WD::IsoIndexTable::GenExcelIndexTable(fileName.toUtf8().data(), paperInfo, indexTableInfo);
        }
        break;
        case FileFormat::FF_PDF:
        {
            QString fileName = outputDir + QString::fromUtf8("/管段图索引表.pdf");
            auto datas = WD::IsoIndexTable::GenSvgIndexTable(paperInfo, pPaper->uCvt, indexTableInfo);
            SaveDatasMergeAsPDF(*pPaper, datas, fileName, std::vector<WD::DVec2>(datas.size()
                , WD::DVec2(indexTableInfo.size().x, indexTableInfo.size().y)));
        }
        break;
        default:
            break;
        }
        //所有图纸合并保存的pdf
        if (_summary && !allPipeIso.empty())
        {
            QString allName = outputDir + QString::fromUtf8("/sub.pdf");
            SaveDatasMergeAsPDF(*pPaper, allPipeIso, allName);
        }
        char buf[1024] = { 0 };
        assert(_taskUserData.allCnt >= _taskUserData.successCnt);
        sprintf_s(buf, sizeof(buf), WD::WDTs("BatchDrawIsoDialog", "GeneratedInfo").c_str(), _taskUserData.allCnt, _taskUserData.successCnt);
        _taskUserData.reset();
        WD_INFO(buf);
    }
}

void BatchDrawIsoDialog::GenDxf(WD::ISOPaper* pPaper, const QString& outputDir)
{
    auto pTask = _core.blockingTask();
    if (pTask != nullptr && !pTask->isRunning())
    {
        WD::WDBlockingTask::Config taskCfg;
        taskCfg.progress = true;
        taskCfg.progressBarCount = 1;
        pTask->setConfig(taskCfg);

        pTask->start([this, pPaper, outputDir](WD::WDBlockingTask& task)
            {
                // 2. 生成SVG图纸数据
                double width = pPaper->uCvt.paperToPixel(pPaper->size().width());
                double height = pPaper->uCvt.paperToPixel(pPaper->size().height());
                char szView[1024] = { 0 };
                sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height));

                std::vector<std::pair<std::string, WD::StringVector>> pipeIsos;
                std::vector<WD::ISOPaper::PaperInfo> paperInfo;
                std::unordered_map<std::string, std::string> paperInfos;
                pipeIsos.reserve(_taskUserData.nodes.size());
                paperInfo.reserve(_taskUserData.nodes.size() * 10);

                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "OnGenerateISO").c_str(), 0);
                {
                    // 这里用map来对pipe节点进行排序
                    std::map<std::string, WD::WDNode::SharedPtr> pipeNodes;
                    for (const auto& pNode : _taskUserData.nodes)
                    {
                        if (pNode == nullptr || !pNode->isType("PIPE"))
                            continue;

                        std::string pipeNumberPart;
                        auto pipeName = pNode->name();
                        auto vec = StringSplit(pipeName, "-");
                        if (vec.size() >= 4)
                            pipeNumberPart = vec[2] + '-' + vec[3];

                        if (pipeNumberPart.empty())
                            pipeNumberPart = pNode->name();

                        pipeNodes.emplace(pipeNumberPart, pNode);
                    }

                    const double nodeStep = 1.0 / static_cast<double>(_taskUserData.nodes.size());
                    int num = 0;
                    int number = 0;
                    char drawingNumber[1024] = { 0 };
                    std::string drawingNumberStr;
                    int i = -1;
                    for (const auto& each : pipeNodes)
                    {
                        const auto& pPIPENode = each.second;
                        if (pPIPENode == nullptr || !pPIPENode->isType("PIPE"))
                            continue;
                        //先生成一遍索引号
                        for (int idex = 0; idex < pPIPENode->childCount(); ++idex)
                        {
                            const auto& pBRANNode = pPIPENode->childAt(idex);
                            if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
                                continue;
                            sprintf_s(drawingNumber, sizeof(drawingNumber), "ST-00/ISO-%04d", ++number);
                            paperInfos.insert(std::make_pair(pBRANNode->name(), std::string(drawingNumber)));
                        }
                    }

                    i = -1;
                    for (const auto& each : pipeNodes)
                    {
                        ++i;
                        task.setProgress(i * nodeStep);
                        const auto& pPIPENode = each.second;
                        if (pPIPENode == nullptr || !pPIPENode->isType("PIPE"))
                            continue;
                        size_t childCnt = pPIPENode->children().size();
                        WD::StringVector pipeData;
                        pipeData.reserve(childCnt);
                        size_t index = 0;
                        double subStep = nodeStep / static_cast<double>(pPIPENode->childCount());
                        for (int childIdx = 0; childIdx < pPIPENode->childCount(); ++childIdx)
                        {
                            task.setProgress(i * nodeStep + childIdx * subStep);

                            const auto& pBRANNode = pPIPENode->childAt(childIdx);
                            if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
                                continue;
                            WD::WDSvgXmlCreator* pSvg = new   WD::WDSvgXmlCreator("xml version='1.0' encoding='utf-8' standalone='no'");
                            if (pSvg == nullptr)
                                continue;
                            pSvg->root("svg").attr("viewBox", std::string(szView)).attr("xmlns", "http://www.w3.org/2000/svg")
                                .attr("xmlns:xlink", "http://www.w3.org/1999/  xlink");
                            pPaper->update(pBRANNode->aabb());
                            if (!paperInfos.empty())
                            {
                                auto it = paperInfos.find(pBRANNode->name());
                                if (it != paperInfos.end())
                                {
                                    drawingNumberStr = it->second;
                                }
                            }
                            else
                            {
                                sprintf_s(drawingNumber, sizeof(drawingNumber), "ST-00/ISO-%04d", ++num);
                                drawingNumberStr = std::string(drawingNumber);
                            }

                            QString fileName = QString::fromUtf8(drawingNumberStr.c_str());
                            fileName.replace("/", "_");
                            fileName.replace("\\", "_");
                            fileName = outputDir + "/" + fileName + ".dxf";
                            pPaper->_genType = GenPaperType::GPT_ISO_DXF;
                            _viewer.genISODxfData(*pPaper
                                , *pBRANNode
                                , *pSvg
                                ,""
                                , fileName
                                , drawingNumberStr
                                , static_cast<uint>(index)
                                , static_cast<uint>(childCnt)
                            );
                            paperInfo.emplace_back(pPaper->info);
                            pipeData.emplace_back(pSvg->toString());
                            index++;
                        }
                        if (pipeData.empty())
                            continue;
                        pipeIsos.push_back(std::make_pair(each.first, pipeData));
                    }
                }
                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "ISOGenerateDown").c_str(), 1);

                _taskUserData.allCnt = static_cast<int>(pipeIsos.size());
                // 3. 生成索引表
                WD::StringVector allPipeIso;
                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "GenerateIndexTable").c_str(), 0);
                {
                    double step = 0.9 / static_cast<double>(pipeIsos.size());
                    for (int i = 0; i < pipeIsos.size(); ++i)
                    {
                        task.setProgress(i * step);
                        _taskUserData.successCnt++;
                    }
                    task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "GenerateIndexTable").c_str(), 0.9);
                    switch (FileFormat(ui.comboBoxTableFormat->currentData().toInt()))
                    {
                    case FileFormat::FF_Excel:
                    {
                        QString fileName = outputDir + QString::fromUtf8("\\管段图索引表.xlsx");
                        WD::IsoIndexTable::GenExcelIndexTable(fileName.toUtf8().data(), paperInfo, indexTableInfo);
                    }
                    break;
                    case FileFormat::FF_PDF:
                    {
                        QString fileName = outputDir + QString::fromUtf8("\\管段图索引表.pdf");
                        auto datas = WD::IsoIndexTable::GenSvgIndexTable(paperInfo, pPaper->uCvt, indexTableInfo);
                        SaveDatasMergeAsPDF(*pPaper, datas, fileName, std::vector<WD::DVec2>(datas.size()
                            , WD::DVec2(indexTableInfo.size().x, indexTableInfo.size().y)));
                    }
                    break;
                    default:
                        break;
                    }
                }
                task.setProgressText(WD::WDTs("BatchDrawIsoDialog", "GenerateDown").c_str(), 1);
            }
            , [this](WD::WDBlockingTask&)
            {
                char buf[1024] = { 0 };
                assert(_taskUserData.allCnt >= _taskUserData.successCnt);
                sprintf_s(buf, sizeof(buf), WD::WDTs("BatchDrawIsoDialog", "GeneratedInfo").c_str(), _taskUserData.allCnt, _taskUserData.successCnt);

                _taskUserData.reset();
                WD_INFO(buf);
            });
    }
    else
    {
        // 2. 生成SVG图纸数据
        double width = pPaper->uCvt.paperToPixel(pPaper->size().width());
        double height = pPaper->uCvt.paperToPixel(pPaper->size().height());
        char szView[1024] = { 0 };
        sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height));

        std::vector<std::pair<std::string, WD::StringVector>> pipeIsos;
        std::vector<WD::ISOPaper::PaperInfo> paperInfo;
        pipeIsos.reserve(_taskUserData.nodes.size());
        paperInfo.reserve(_taskUserData.nodes.size() * 10);
        // 这里用map来对pipe节点进行排序
        std::map<std::string, WD::WDNode::SharedPtr> pipeNodes;
        for (const auto& pNode : _taskUserData.nodes)
        {
            if (pNode == nullptr || !pNode->isType("PIPE"))
                continue;

            std::string pipeNumberPart;
            auto pipeName = pNode->name();
            auto vec = StringSplit(pipeName, "-");
            if (vec.size() >= 4)
                pipeNumberPart = vec[2] + '-' + vec[3];

            if (pipeNumberPart.empty())
                pipeNumberPart = pNode->name();

            pipeNodes.emplace(pipeNumberPart, pNode);
        }
        int i = -1;

        int num = 0;
        char drawingNumber[1024] = { 0 };
        for (const auto& each : pipeNodes)
        {
            ++i;
            const auto& pPIPENode = each.second;
            if (pPIPENode == nullptr || !pPIPENode->isType("PIPE"))
                continue;
            size_t childCnt = pPIPENode->children().size();
            StringVector pipeData;
            pipeData.reserve(childCnt);
            size_t index = 0;
            for (const auto& pBRANNode : pPIPENode->children())
            {
                if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
                    continue;
                WD::WDSvgXmlCreator* pSvg = new   WD::WDSvgXmlCreator("xml version='1.0' encoding='utf-8' standalone='no'");
                if (pSvg == nullptr)
                    continue;
                pSvg->root("svg").attr("viewBox", std::string(szView)).attr("xmlns", "http://www.w3.org/2000/svg").attr("xmlns:xlink", "http://www.w3.org/1999/xlink");
                pPaper->update(pBRANNode->aabb());
                sprintf_s(drawingNumber, sizeof(drawingNumber), "ST-00/ISO-%04d", ++num);

                QString fileName = QString::fromUtf8(each.first.c_str());
                fileName.replace("/", "_");
                fileName.replace("\\", "_");
                fileName = outputDir + "/" + fileName + ".dxf";
                pPaper->_genType = GenPaperType::GPT_ISO_DXF;
                _viewer.genISODxfData(*pPaper
                    , *pBRANNode
                    , *pSvg
                    , ""
                    , fileName
                    , std::string(drawingNumber)
                    , static_cast<uint>(index)
                    , static_cast<uint>(childCnt)
                );
                paperInfo.emplace_back(pPaper->info);
                pipeData.emplace_back(pSvg->toString());
                index++;

                delete pSvg;
            }
            if (pipeData.empty())
                continue;
            pipeIsos.push_back(std::make_pair(each.first, pipeData));
        }
        _taskUserData.allCnt = static_cast<int>(pipeIsos.size());
        // 3. 合并保存为PDF
        WD::StringVector allPipeIso;
        for (const auto& pipeIso : pipeIsos)
        {
            const auto& pipeData = pipeIso.second;
            if (pipeData.empty())
                continue;
            QString fileName = QString::fromUtf8(pipeIso.first.c_str());
            fileName.replace("/", "_");
            fileName.replace("\\", "_");
            fileName = outputDir + "/" + fileName + ".pdf";
            if (_summary)
            {
                for (auto& pipe : pipeData)
                {
                    allPipeIso.emplace_back(pipe);
                }
            }
            else
            {
                SaveDatasMergeAsPDF(*pPaper, pipeData, fileName);
            }
            _taskUserData.successCnt++;
        }

        switch (FileFormat(ui.comboBoxTableFormat->currentData().toInt()))
        {
        case FileFormat::FF_Excel:
        {
            QString fileName = outputDir + QString::fromUtf8("/管段图索引表.xlsx");
            WD::IsoIndexTable::GenExcelIndexTable(fileName.toUtf8().data(), paperInfo, indexTableInfo);
        }
        break;
        case FileFormat::FF_PDF:
        {
            QString fileName = outputDir + QString::fromUtf8("/管段图索引表.pdf");
            auto datas = WD::IsoIndexTable::GenSvgIndexTable(paperInfo, pPaper->uCvt, indexTableInfo);
            SaveDatasMergeAsPDF(*pPaper, datas, fileName, std::vector<WD::DVec2>(datas.size()
                , WD::DVec2(indexTableInfo.size().x, indexTableInfo.size().y)));
        }
        break;
        default:
            break;
        }
        //所有图纸合并保存的pdf
        if (_summary && !allPipeIso.empty())
        {
            QString allName = outputDir + QString::fromUtf8("/sub.pdf");
            SaveDatasMergeAsPDF(*pPaper, allPipeIso, allName);
        }
        char buf[1024] = { 0 };
        assert(_taskUserData.allCnt >= _taskUserData.successCnt);
        sprintf_s(buf, sizeof(buf), WD::WDTs("BatchDrawIsoDialog", "GeneratedInfo").c_str(), _taskUserData.allCnt, _taskUserData.successCnt);
        _taskUserData.reset();
        WD_INFO(buf);
    }
}

void BatchDrawIsoDialog::retranslateUi()
{
    Trs("BatchDrawIsoDialog"
        , static_cast<QDialog*>(this)
        , ui.labelIndexTableFormat
        , ui.labelISOFormat
        , ui.labelPath
        , ui.groupBox
        , ui.pushButtonGenerate
        , _addMenuBar
        , _removeMenuBar
        , ui.summaryCheckBox
        , ui.outputPaperSize
    );
}