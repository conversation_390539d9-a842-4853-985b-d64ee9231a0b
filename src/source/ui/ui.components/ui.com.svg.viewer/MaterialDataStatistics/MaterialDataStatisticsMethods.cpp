#include "MaterialDataStatisticsMethods.h"
#include "core/businessModule/typeMgr/WDBMAttrValue.h"
#include "MaterialDataStatistics/MaterialDataStatisticsCommon.h"
#include "../ISOPaper.h"
#include "log/WDLoggerPort.h"

WD_NAMESPACE_USE
uint GetBoltCnt(MaterialDataStatisticsMethodBase::Context& context
    , std::optional<MaterialDataStatisticsMethodBase::Context::BoltType> boltType = std::nullopt)
{
    WD::WDNode::SharedPtr pBoltNode = context.boltKitInfo.pStudNode;
    WD::WDNode::SharedPtr pSubBoltNode;
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSubBoltNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSubBoltNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSubBoltNode = context.boltKitInfo.pStudNode;
        break;
    default:
        break;
    }
    if (pSubBoltNode == nullptr || pBoltNode == nullptr)
        return 0;
    MaterialDataStatisticsMethodBase::Context::BoltType type;
    if (boltType)
        type = boltType.value();
    else
        type = context.boltType;
    switch (type)
    {
    case WD::MaterialDataStatisticsMethodBase::Context::Nut:
        {
            auto pBltrefNode = pBoltNode->getAttribute("Bltref").toNodeRef().refNode();
            if (pBltrefNode == nullptr)
                return 0;

            StringVector bItems = pBltrefNode->getAttribute("Bitems").toStringVector();
            uint cnt = 0;
            for (auto& each : bItems)
            {
                if (_stricmp(each.c_str(), "NUT") == 0)
                    cnt++;
            }
            auto boltCnt = GetBoltCnt(context, WD::MaterialDataStatisticsMethodBase::Context::Stud);
            return boltCnt * cnt;
        }
        break;
    case WD::MaterialDataStatisticsMethodBase::Context::Wash:
        {
            auto pBltrefNode = pBoltNode->getAttribute("Bltref").toNodeRef().refNode();
            if (pBltrefNode == nullptr)
                return 0;

            StringVector bItems = pBltrefNode->getAttribute("Bitems").toStringVector();
            uint cnt = 0;
            for (auto& each : bItems)
            {
                if (_stricmp(each.c_str(), "WASH") == 0)
                    cnt++;
            }
            auto boltCnt = GetBoltCnt(context, WD::MaterialDataStatisticsMethodBase::Context::Stud);
            return boltCnt * cnt;
        }
        break;
    case WD::MaterialDataStatisticsMethodBase::Context::Stud:
        {
            auto& pNode = context.pCurrentNode;
            if (pNode == nullptr)
                return 0;
            auto pSprefNode = pNode->getAttribute("Spref").toNodeRef().refNode();
            if (pSprefNode == nullptr)
                return 0;
            auto pCatrefNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
            if (pCatrefNode == nullptr)
                return 0;
            auto blrfs = pCatrefNode->getAttribute("Blrfarray").toNodeRefVector();
            if (blrfs.empty())
                return 0;
            auto pBtse = blrfs[0].refNode();
            if (pBtse == nullptr)
            {
                char info[1024] = { 0 };
                sprintf_s(info, sizeof(info), "法兰/阀门(%s[%s])的螺栓集引用为空!"
                    , pNode->name().c_str()
                    , pNode->uuid().toString().c_str());
                LOG_INFO << info;
                return 0;
            }

            auto cnt = pBtse->getAttribute("Noff").toInt();
            if (cnt != 0)
                return cnt;

            for (auto& pChild : pBtse->children())
            {
                if (pChild->isType("BLTP"))
                    cnt++;
            }
            if (cnt == 0)
            {
                char info[1024] = { 0 };
                sprintf_s(info, sizeof(info), "法兰/阀门(%s[%s])的螺栓集下的螺栓为空!"
                    , pNode->name().c_str()
                    , pNode->uuid().toString().c_str());
                LOG_INFO << info;
            }
            return cnt;
        }
        break;
    default:
        break;
    }
    return 0;
}

std::string MaterialDataDescrip::onExec(Context& context)
{
    auto& pPaper = context.pPaper;
    if (pPaper == nullptr)
        return "";
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "";
    WD::WDNode::SharedPtr pSpcoNode = nullptr;

    // 螺栓的处理
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSpcoNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSpcoNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSpcoNode = context.boltKitInfo.pStudNode;
        break;
    default:
        {
            if (pNode->isType("ATTA"))
                return "Pipe Support";
            pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
        }
        break;
    };
    if (pSpcoNode == nullptr)
        return "";

    std::string text1;
    auto& table = context.pPaper->materialTable;
    // 获取详情数据
    if (table.details != ISOMaterialArea::Details::DT_Null)
    {
        auto pDetrefNode = pSpcoNode->getAttribute("Detref").toNodeRef().refNode();
        if (pDetrefNode != nullptr)
        {
            switch (table.details)
            {
            case ISOMaterialArea::Details::DT_RText:
                text1 = getAttribute(context, pDetrefNode, "Rtext").toString();
                break;
            case ISOMaterialArea::Details::DT_SText:
                text1 = getAttribute(context, pDetrefNode, "SText").toString();
                break;
            case ISOMaterialArea::Details::DT_TText:
                text1 = getAttribute(context, pDetrefNode, "TText").toString();
                break;
            default:
                break;
            }
        }
    }
    // 获取材质数据
    std::string text2;
    if (table.texture != ISOMaterialArea::Texture::TT_Null)
    {
        auto pMatrefNode = pSpcoNode->getAttribute("Matref").toNodeRef().refNode();
        if (pMatrefNode != nullptr)
        {
            switch (table.texture)
            {
            case ISOMaterialArea::Texture::TT_XText:
                text2 += getAttribute(context, pMatrefNode, "XText").toString();
                break;
            case ISOMaterialArea::Texture::TT_YText:
                text2 += getAttribute(context, pMatrefNode, "YText").toString();
                break;
            case ISOMaterialArea::Texture::TT_ZText:
                text2 += getAttribute(context, pMatrefNode, "ZText").toString();
                break;
            default:
                break;
            }
        }
    }
    std::string value;
    if (!text1.empty() && !text2.empty())
        value = text1 + " " + text2;
    else
        value = text1 + text2;

    // 螺栓的处理
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Stud:
        {
            MaterialDataLength lengthHandle(_core, _style);
            std::string length = lengthHandle.exec(context);
            if (length == "0")
                length.clear();
            else
                length.push_back(' ');
            return length + value.c_str();
        }
        break;
        // 垫圈和螺母的处理方式和其他管件一致
    case MaterialDataStatisticsMethodBase::Context::Nut:
    case MaterialDataStatisticsMethodBase::Context::Wash:
    default:
        break;
    }
    return value.c_str();
}

std::string MaterialDataSpec::onExec(Context& context)
{
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "";
    // 螺栓的处理
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
    case MaterialDataStatisticsMethodBase::Context::Wash:
    case MaterialDataStatisticsMethodBase::Context::Stud:
        {
            auto pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
            if (pSpcoNode == nullptr)
                return "";
            auto pScomNode = pSpcoNode->getAttribute("Catref").toNodeRef().refNode();
            if (pScomNode == nullptr)
                return "";
            auto blRefArray = pScomNode->getAttribute("Blrfarray").toNodeRefVector();
            if (blRefArray.empty())
                return "";

            // 构建属性
            auto aGet = _core.getBMCatalog().modelBuilder().cAttributeGet(*pNode);

            double rDouble = 0.0;
            if (blRefArray.empty())
                return "0";

            WDNode::SharedPtr blRefNode;
            if (blRefArray.size() == 2)
            {
                blRefNode = blRefArray[1].refNode();
                if (blRefNode == nullptr)
                    blRefNode = blRefArray[0].refNode();
            }
            else
            {
                if (blRefArray.size() > 2)
                {
                    char info[1024] = { 0 };
                    sprintf_s(info, sizeof(info), "节点(%s[%s])的螺栓集引用数量为:%d,未处理的情况!"
                        , pNode->name().c_str()
                        , pNode->uuid().toString().c_str()
                        , static_cast<int>(blRefArray.size()));
                    LOG_INFO << info;
                }
                blRefNode = blRefArray[0].refNode();
            }

            if (blRefNode == nullptr)
                return "";
            // 优先使用bltp节点的bDiameter
            bool bOk = false;
            for (auto& pChild : blRefNode->children())
            {
                if (pChild == nullptr)
                    continue;
                if (rDouble = aGet.getAttribute(*pChild, "Bdiameter").convertToDouble(&bOk); bOk)
                    return DoubleValueToString(rDouble);
            }
            if (rDouble = aGet.getAttribute(*blRefNode, "Bdiameter").convertToDouble(&bOk); bOk)
                return DoubleValueToString(rDouble);
            return "";
        }
    default:
        break;
    }
    // 非螺栓的普通管件节点

    WD::WDNode::SharedPtr pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
    if (pSpcoNode == nullptr)
        return "";
    auto pCatrefNode = pSpcoNode->getAttribute("Catref").toNodeRef().refNode();
    if (pCatrefNode != nullptr)
    {
        auto funcGetPtNode = [](WDNode& comNode, int number) ->WDNode::SharedPtr
        {
            auto pPTSENode = comNode.getAttribute("Ptref").toNodeRef().refNode();
            if (pPTSENode == nullptr)
                return nullptr;
            for (auto& pPTNode : pPTSENode->children())
            {
                if (pPTNode == nullptr)
                    continue;
                if(pPTNode->getAttribute("Number").toInt() == number)
                    return pPTNode;
            }
            return nullptr;
        };

        auto funcHandleString = [](std::string& str)
        {
            if (str.empty())
                return;
            if (str.find('.') == std::string::npos)
                return;
            while(!str.empty())
            {
                if (str.back() == '0')
                {
                    str.pop_back();
                    continue;
                }

                if (str.back() == '.')
                {
                    str.pop_back();
                    return;
                }
            }
        };

        auto pP1 = funcGetPtNode(*pCatrefNode, 1);
        std::string p1 = getAttribute(context, pP1, "Pbore").convertToString();
        funcHandleString(p1);
        // 三通和支管台的规格为 p1 x p3
        if (pNode->isAnyOfType("TEE", "OLET"))
        {
            auto pP3 = funcGetPtNode(*pCatrefNode, 3);
            std::string p3 = getAttribute(context, pP3, "Pbore").convertToString();
            funcHandleString(p3);
            if (!p1.empty() && !p3.empty())
                return p1 + "×" + p3;
            else if (!p1.empty())
                return p1;
            else if (!p3.empty())
                return p3;
        }
        // 大小头的规格为 p1 x p2
        else if (pNode->isType("REDU"))
        {
            auto pP2 = funcGetPtNode(*pCatrefNode, 2);
            std::string p2 = getAttribute(context, pP2, "Pbore").convertToString();
            funcHandleString(p2);
            if (!p1.empty() && !p2.empty())
                return p1 + "×" + p2;
            else if (!p1.empty())
                return p1;
            else if (!p2.empty())
                return p2;
        }
        // 其他管件都是 p1
        else
        {
            return p1;
        }
    }
    return "";
}

std::string MaterialDataLength::onExec(Context& context)
{
    // 螺栓的处理
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Stud:
        {
            assert(context.boltKitInfo.boltLength > NumLimits<double>::Epsilon);
            return DoubleValueToString(context.boltKitInfo.boltLength);
        }
    default:
        break;
    }
    return "";
}

std::string MaterialDataWidth::onExec(Context&)
{
    return "";
}

std::string MaterialDataMaterial::onExec(Context& context)
{
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "";
    WD::WDNode::SharedPtr pSpcoNode = nullptr;
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSpcoNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSpcoNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSpcoNode = context.boltKitInfo.pStudNode;
        break;
    default:
        pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
        break;
    }
    if (pSpcoNode == nullptr)
        return "";
    auto pMatrefNode = pSpcoNode->getAttribute("Matref").toNodeRef().refNode();
    if (pMatrefNode != nullptr)
        return getAttribute(context, pMatrefNode, "XText").toString();
    return "";
}

std::string MaterialDataMaterialGrade::onExec(Context& context)
{
    if (context.boltType != MaterialDataStatisticsMethodBase::Context::None)
        return "";
    return "";
}

std::string MaterialDataMaterialCode::onExec(Context& context)
{
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "";
    WD::WDNode::SharedPtr pSpcoNode;
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSpcoNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSpcoNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSpcoNode = context.boltKitInfo.pStudNode;
        break;
    default:
        pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
        break;
    }
    if (pSpcoNode == nullptr)
        return "";

    // 这里暂时特殊处理ATTA类型节点,取元件的名称
    if (pNode->isType("ATTA"))
    {
        auto pCatref = pSpcoNode->getAttribute("Catref").toNodeRef().refNode();
        if (pCatref != nullptr)
            return pCatref->name().c_str();
    }

    // 如果节点为无名节点(流水命名的节点),返回空
    if (!pSpcoNode->isNamed())
        return "";
    std::string value = pSpcoNode->name();
    auto index = value.find('/');
    while (index != std::string::npos)
    {
        value = std::string(value.begin() + index + 1, value.end());
        index = value.find('/');
    }

    // 这里暂时特殊处理ATTA类型节点,不去除分隔符后的内容
    if (pNode->isType("ATTA"))
    {
        auto pCatref = pSpcoNode->getAttribute("Catref").toNodeRef().refNode();
        if (pCatref != nullptr)
            return value.c_str();
    }

    std::regex partten("[:@.\\+\\-&]");
    std::sregex_iterator itrRegex(value.begin(), value.end(), partten);

    StringVector vec;
    while (itrRegex != std::sregex_iterator())
    {
        vec.push_back(itrRegex->prefix());
        itrRegex++;
    }
    if (!vec.empty())
        value = StringConcat(vec, "-");
    switch (_style)
    {
    case WD::TableStyleBase::S_HD:
        {
            // 螺栓的处理
            switch (context.boltType)
            {
            case MaterialDataStatisticsMethodBase::Context::Stud:
                {
                    MaterialDataLength lengthHandle(_core, _style);
                    std::string length = lengthHandle.exec(context);
                    if (length == "0")
                        length.clear();
                    return value.c_str() + length;
                }
                break;
                // 垫圈和螺母的处理方式和普通管件一致
            case MaterialDataStatisticsMethodBase::Context::Nut:
            case MaterialDataStatisticsMethodBase::Context::Wash:
            default:
                break;
            }
        }
        break;
    case WD::TableStyleBase::S_SYHG:
        break;
    default:
        break;
    }
    return value.c_str();
}

std::string MaterialDataCount::onExec(Context&  context)
{
    WD::WDNode::SharedPtr pSpcoNode = nullptr;
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSpcoNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSpcoNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSpcoNode = context.boltKitInfo.pStudNode;
        break;
    default:
        {
            // 如果管件类型是TUBI,则累加长度;否则,累加个数
            auto& pNode = context.pCurrentNode;
            if (pNode == nullptr)
                return "0";
            auto& counts = context.counts;
            if (pNode->isType("TUBI"))
            {
                double length = pNode->getAttribute("ltlength").toDouble();
                auto key = ToString(pNode->getAttribute("Lbore").convertToInt());
                auto itr = counts.find(key);
                if (itr == counts.end())
                {
                    counts.emplace(key, length);
                    return DoubleValueToString(length / 1000, 1);
                }
                itr->second += length;
                // 这里转换单位为m
                return DoubleValueToString(itr->second / 1000, 1);
            }
            pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
            if (pSpcoNode == nullptr)
                return "0";
            auto key = pSpcoNode->uuid().toString();
            auto itr = counts.find(key);
            if (itr == counts.end())
            {
                counts.emplace(key, 1);
                return "1";
            }
            auto value = static_cast<int>(itr->second);
            itr->second = ++value;
            return DoubleValueToString(itr->second);
        }
        break;
    }
    if (pSpcoNode == nullptr)
        return "0";

    auto& counts = context.boltCnts;
    auto key = Context::Key(pSpcoNode, context.boltKitInfo.boltLength);
    auto cnt = GetBoltCnt(context);
    auto cntItr = counts.find(key);
    if (cntItr != counts.end())
    {
        auto value = cntItr->second;
        cnt = value + cnt;
        cntItr->second = cnt;
    }
    else
    {
        auto ret = context.boltCnts.emplace(key, cnt);
        if (!ret.second)
            return "0";
    }
    return ToString(static_cast<int>(cnt));
}

std::string MaterialDataWeight::onExec(Context& context)
{
    WD::WDNode::SharedPtr pSpcoNode = nullptr;
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "0";
    // 螺栓的处理
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSpcoNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSpcoNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSpcoNode = context.boltKitInfo.pStudNode;
        break;
    default:
        {
            pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
            if (pSpcoNode == nullptr)
                return "0";
            std::string attributeName;
            std::string key;
            if (pNode->isType("TUBI"))
            {
                key = ToString(pNode->getAttribute("Lbore").convertToInt());
                attributeName = "UWEI";
            }
            else
            {
                key = ToString(pSpcoNode->uuid().toString());
                attributeName = "Cweight";
            }
            auto& weights = context.weights;
            auto itr = weights.find(key);
            if (itr == weights.end())
            {
                auto pRefNode = pSpcoNode->getAttribute("Cmpref").toNodeRef().refNode();
                if (pRefNode == nullptr)
                    return "0";
                bool bOk = false;
                double doubleVar = getAttribute(context, pRefNode, attributeName).convertToDouble(&bOk);
                if (!bOk)
                {
                    assert(false);
                    return "0";
                }
                auto ret = weights.emplace(key, doubleVar);
                if (!ret.second)
                    return "0";
                return DoubleValueToString(doubleVar);
            }
            return DoubleValueToString(itr->second);
        }
        break;
    }
    if (pSpcoNode == nullptr)
        return "0";
    auto& weights = context.boltWeights;
    auto key = Context::Key(pSpcoNode, context.boltKitInfo.boltLength);
    auto itr = weights.find(key);
    if (itr == weights.end())
    {
        auto pRefNode = pSpcoNode->getAttribute("Cmpref").toNodeRef().refNode();
        if (pRefNode == nullptr)
            return "0";
        bool bOk = false;
        double doubleVar = getAttribute(context, pRefNode, "Cweight").convertToDouble(&bOk);
        if (!bOk)
        {
            assert(false);
            return "0";
        }
        auto ret = weights.emplace(key, doubleVar);
        if (!ret.second)
            return "0";
        return DoubleValueToString(doubleVar);
    }
    return DoubleValueToString(itr->second);
}

std::string MaterialDataTotalWeight::onExec(Context&  context)
{
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "0";
    WD::WDNode::SharedPtr pSpcoNode = nullptr;
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSpcoNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSpcoNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSpcoNode = context.boltKitInfo.pStudNode;
        break;
    default:
        {
            pSpcoNode = pNode->getAttribute("Spref").toNodeRef().refNode();
            if (pSpcoNode == nullptr)
                return "0";
            std::string key;
            std::string attributeName;
            if (pNode->isType("TUBI"))
            {
                key = ToString(pNode->getAttribute("Tbore").convertToInt());
                attributeName = "UWEI";
            }
            else
            {
                key = ToString(pSpcoNode->uuid().toString());
                attributeName = "Cweight";
            }
            double ratio = 1.0;
            if (pNode->isType("TUBI"))
            {
                key = ToString(pNode->getAttribute("Lbore").convertToInt());
                // 直管段需要转换单位
                ratio = 0.001;
            }
            else
            {
                key = pSpcoNode->uuid().toString();
            }
            auto& weights = context.weights;
            auto& counts = context.counts;
            auto weightItr = weights.find(key);
            if (weightItr == weights.end())
            {
                auto pRefNode = pSpcoNode->getAttribute("Cmpref").toNodeRef().refNode();
                if (pRefNode == nullptr)
                    return "0";
                bool bOk = false;
                double doubleVar = getAttribute(context, pRefNode, attributeName).convertToDouble(&bOk);
                if (!bOk)
                {
                    assert(false);
                    return "0";
                }
                auto ret = weights.emplace(key, doubleVar);
                if (!ret.second)
                {
                    return "0";
                }
                weightItr = weights.find(key);
            }

            auto countItr = counts.find(key);
            if (countItr == counts.end())
            {
                assert(false);
                return "0";
            }
            return DoubleValueToString(weightItr->second * countItr->second * ratio);
        }
        break;
    }

    if (pSpcoNode == nullptr)
        return "0";
    auto key = pSpcoNode->uuid().toString();
    auto& weights = context.boltWeights;
    auto& counts = context.boltCnts;
    auto& length = context.boltKitInfo.boltLength;
    auto weightItr = weights.find(Context::Key(pSpcoNode, length));
    if (weightItr == weights.end())
        return "0";
    auto countItr = counts.find(Context::Key(pSpcoNode, length));
    if (countItr == counts.end())
    {
        assert(false);
        return "0";
    }
    return DoubleValueToString(weightItr->second * countItr->second);
}

std::string MaterialDataTotalDia::onExec(Context& context)
{
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "";
    WD::WDNode::SharedPtr pSpcoNode = nullptr;
    switch (context.boltType)
    {
    case MaterialDataStatisticsMethodBase::Context::Nut:
        pSpcoNode = context.boltKitInfo.pNutNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Wash:
        pSpcoNode = context.boltKitInfo.pWashNode;
        break;
    case MaterialDataStatisticsMethodBase::Context::Stud:
        pSpcoNode = context.boltKitInfo.pStudNode;
        break;
    default:
    {
        auto bore = pNode->getAttribute("Lbore").toDouble();
        if (bore <= 0.0)
            return "";
        char flag[1024] = { 0 };
        sprintf_s(flag, sizeof(flag), "%d", static_cast<int>(bore));
        return std::string(flag);
    }
    break;
    }
    //螺栓等没有公称直径的说法，直接显示空
   return "";
}

std::string MaterialDataTotalNote::onExec(Context& context)
{
    auto& pNode = context.pCurrentNode;
    if (pNode == nullptr)
        return "0";
    //涂总说暂时没有数据源，先写死，西北院就看个格式效果
    return WD::WDTs("MaterialData", "NoteSameVlan");
}
