#pragma once
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include <QString>
#include "../ISOPaper.h"
#include "core/businessModule/typeMgr/WDBMAttrValue.h"
#include "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/WDBDBase.h"
#include "../ISOTableStyle/TableStyleBase.h"
WD_NAMESPACE_BEGIN

/**
* @brief 注意 : 统计结果返回值类型为 utf8 编码的字符串
*/
class MaterialDataStatisticsMethodBase
{
public:
    class Context
    {
    public:
        // 记录当前批次的每种类型的 数量/长度 <key:直管段为管径,其他管件为等级的uuid, 直管段为当前批次中同管径的直管段的总长,其他管件为相同元件的管件数量>
        std::map<std::string, double> counts;
        // 记录当前批次的每种类型的 重量 <key:直管段为管径,其他管件为等级的uuid, 直管段为当前批次中同管径的直管段的总长,其他管件为相同元件的管件数量>
        std::map<std::string, double> weights;
        // 保存当前的单位节点
        WDNode::SharedPtr pUnitNode;
        // 保存当前节点
        WDNode::SharedPtr pCurrentNode;
        ISOPaper* pPaper = nullptr;

        enum BoltType
        {
            // 不是螺栓节点
            None,
            // 螺母
            Nut,
            // 垫片
            Wash,
            // (戴帽)螺栓
            Stud,
        };
        BoltType boltType = None;
        // 螺栓等级节点
        BoltKitInfo boltKitInfo;
        struct Key
        {
            WDNode::SharedPtr pNode;
            double length = 0.0;
            Key(WDNode::SharedPtr pNode, double length) : pNode(pNode), length(length)
            {
            }
            const bool operator<(const Key& right) const
            {
                if (this->pNode == right.pNode && this->length == right.length)
                    return false;

                if (this->length < right.length)
                    return true;
                return this->length == right.length && pNode < right.pNode;
            }

        };
        // 保存螺栓的数量
        std::map<Key, uint> boltCnts;
        // 保存螺栓的重量
        std::map<Key, double> boltWeights;
    public:
        Context()
        {
        }
    };
public:
    MaterialDataStatisticsMethodBase(WDCore& core, TableStyleBase::Style style) : _core(core), _style(style)
    {
    }
    virtual~MaterialDataStatisticsMethodBase()
    {
    }
public:
    /**
     * @brief 执行函数
     * @param node 节点
     * @param context 上下文
     * @return 处理后获取的值,注意,此处返回值编码格式为ansi
    */
    std::string exec(Context& context)
    {
        _bCAttrsLoaded = false;
        return this->onExec(context);
    }
protected:
    virtual std::string onExec(Context& context) = 0;
protected:
    WDBMAttrValue getAttribute(Context& context, WDNode::SharedPtr pNode, const std::string& name)
    {
        if (!_bCAttrsLoaded)
        {
            _aGet = getCAttrs(context);
            _bCAttrsLoaded = true;
        }
        if (pNode == nullptr) 
        {
            assert(false);
            return WDBMAttrValue();
        }
        return _aGet.getAttribute(*pNode, name);
    }
private:
    CAttributeGet getCAttrs(Context& context)
    {
        auto pNode = context.pCurrentNode;
        if (pNode == nullptr)
            return CAttributeGet();

        WD::WDNode::SharedPtr pSPCONode = nullptr;

        switch (context.boltType)
        {
        case MaterialDataStatisticsMethodBase::Context::Nut:
            pSPCONode = context.boltKitInfo.pNutNode;
            break;
        case MaterialDataStatisticsMethodBase::Context::Wash:
            pSPCONode = context.boltKitInfo.pWashNode;
            break;
        case MaterialDataStatisticsMethodBase::Context::Stud:
            pSPCONode = context.boltKitInfo.pStudNode;
            break;
        default:
            pSPCONode = pNode->getAttribute("Spref").toNodeRef().refNode();
            break;
        };

        if (pSPCONode == nullptr)
            return CAttributeGet();

        // 构建属性
        return _core.getBMCatalog().modelBuilder().cAttributeGet(pNode, pSPCONode);
    }
protected:
    WDCore& _core;
    CAttributeGet _aGet;
    bool _bCAttrsLoaded = false;
    TableStyleBase::Style _style = TableStyleBase::S_SYHG;
};

/**
 * @brief 获取描述
*/
class MaterialDataDescrip : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataDescrip(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取规格
*/
class MaterialDataSpec : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataSpec(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取长度
*/
class MaterialDataLength : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataLength(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取宽度
*/
class MaterialDataWidth : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataWidth(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取材质
*/
class MaterialDataMaterial : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataMaterial(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
* @brief 获取物料等级
*/
class MaterialDataMaterialGrade : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataMaterialGrade(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取物料代码
*/
class MaterialDataMaterialCode : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataMaterialCode(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取数量
*/
class MaterialDataCount : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataCount(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取单重
*/
class MaterialDataWeight : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataWeight(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};
/**
 * @brief 获取总重
*/
class MaterialDataTotalWeight : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataTotalWeight(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};

/**
 * @brief 获取总重
*/
class MaterialDataTotalDia : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataTotalDia(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};

/**
 * @brief 获取总重
*/
class MaterialDataTotalNote : public MaterialDataStatisticsMethodBase
{
public:
    MaterialDataTotalNote(WDCore& core, TableStyleBase::Style style) : MaterialDataStatisticsMethodBase(core, style)
    {
    }
protected:
    virtual std::string onExec(Context& context) override;
};

WD_NAMESPACE_END