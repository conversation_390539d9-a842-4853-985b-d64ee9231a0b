set(TARGET_NAME ui_com_svg_viewer)

macro(SourceGroupByDirRList dir)
	SourceGroupByDir(${dir} TTMP_FILES)
	list(APPEND ALL_FILES_LIST ${TTMP_FILES})
endmacro(SourceGroupByDirRList)

SourceGroupByDirRList("translations")
SourceGroupByDirRList("FigureLegends")
SourceGroupByDirRList("MaterialDataStatistics")
SourceGroupByDirRList("ISOGraphics")
SourceGroupByDirRList("WDPainter")
SourceGroupByDirRList("WDPainter/WDIsoPainter")
SourceGroupByDirRList("ISOTableStyle")
SourceGroupByDirRList("ISOTableStyle/TableStyleHD")
SourceGroupByDirRList("ISOTableStyle/TableStyleSYHG")
SourceGroupByDirRList("ISOTableStyle/TableStyleXB")
SourceGroupByDirRList("ISOTableStyle/TableStyleXB/CSDataJson")
SourceGroupByDirRList("ISOSettingWidgets")
SourceGroupByDirRList("ISODataConfigDialog")
SourceGroupByDirRList("ISOTableCommon")
SourceGroupByDirRList("Resources")
SourceGroupByDirRList("weldTypeTable")
SourceGroupByDirRList("DRW")
SourceGroupByDirRList("DRW/src")
SourceGroupByDirRList("DRW/src/intern")

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets Svg REQUIRED)


set(HEADER_FILES
    "ISOMainWindow.h"
    "ISOSvgView.h"
    "MBDMainWindow.h"
    "MBDSvgView.h"
    "MBDDIMPainter.h"
    "ui.com.svg.viewer.h"
    "ISOUnitConvert.h"
    "ISOUnitConvert.inl"
    "ISODIMPainterSvg.h"
    "ISOBindingBoxComputer.h"
    "ISOPaper.h"
    "ISODIMGenerate.h"
    "ISOTuNode.h"
    "ISOTuLine.h"
    "ISOPaperMgr.h"
    "ISOStyleSheet.h"
    "ISOSvgEditMainWindow.h"
    "ISOSvgEditView.h"
	"ISOSettingWidgets/ISOFrameSettingDialog.h"
    "ISOSettingWidgets/ISOOutputConfigTemplate.h"
    "ISOSettingWidgets/ConfigTemplateManager.h"
    "ISOSettingWidgets/ISOPlotAreaSettingDialog.h"
    "ISOSettingWidgets/ISOPlotAreaEditDialog.h"
    "ISOSettingWidgets/ISOMaterialListSettingDialog.h"
    "ISOSettingWidgets/ISOMaterialListEditDialog.h"
    "ISOSettingWidgets/ISONotesAreaSettingDialog.h"
    "ISOSettingWidgets/ISONotesAreaEditDialog.h"
    "ISOSettingWidgets/ISOTemplateSettingDialog.h"
    "ISOSettingWidgets/ISOTemplateEditDialog.h"
    "ISOSettingWidgets/ISOSettingDialog.h"
    "ISOGraphics/ISOGraphicsHelper.h"
    "ISOGraphics/ISOGraphics.h"
    "ISOGraphics/ISOGraphicsPath.h"
    "ISOGraphics/ISOGraphicsLine.h"
    "ISOGraphics/ISOGraphicsText.h"
    "ISOGraphics/GraphicalObjectManager.h"
    "ISOGraphics/ISOFlags.h"
    "ISOGraphics/ISOGraphicsRect.h"
    "ISOGraphics/ISOGraphicsEllipse.h"
    "ISOGraphics/RescaleGraphicBox.h"
    "MaterialDataStatistics/MaterialDataStatisticsMethods.h"
    "MaterialDataStatistics/MaterialDataStatisticsMethodsMgr.h"
    "MaterialDataStatistics/MaterialDataStatisticsHelper.h"
	"MaterialDataStatistics/MaterialDataStatisticsCommon.h"
	"ISOTableCommon/ISOTableWidget.h"
	"ISOTableCommon/IsoTableCommon.h"
	"ISOTableStyle/TableStyleBase.h"
	"ISOTableStyle/TableStyleMgr.h"
	"ISOTableStyle/TableStyleHD/TableStyleHD.h"
	"ISOTableStyle/TableStyleSYHG/TableStyleSYHG.h"
    "ISOTableStyle/TableStyleXB/TableStyleXB.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/MatDataTableJson.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/CSJsonMgr.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/ConfigurationJson.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/DesignParameterJson.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/MaxStressTableJson.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/ThrustAndMementJson.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/ValnDataJson.h"    
    "ISOTableStyle/TableStyleXB/CSDataJson/NoteExcel.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/PipeDataJson.h"
    "ISOTableStyle/TableStyleXB/CSDataJson/NodeCoordinateJson.h"
	"ISODataConfigDialog/ISODataConfigDialog.h"
    "ISOWeldQueryTable.h"
    "FigureLegends/ISOFigureLegendsMgr.h"
    "FigureLegends/ISOFigureLegend.h"
    "BatchDrawIsoDialog.h"
	"WDPainter/WDAbstractPainter3D.h"
	"WDPainter/WDAbstractPainter2D.h"
	"WDPainter/WDIsoPainter/WDIsoPainter.h"
	"WDPainter/WDIsoPainter/WDIsoSvgPainter.h"
    "WDPainter/WDIsoPainter/WDIsoDxfPainter.h"
	"WDPainter/WDStyle.h"
	"ISOSaveHelper.h"
    "IsoIndexTable.h"   
    "DRW/DxfPainterInterface.h"
    "DRW/dx_iface.h"
    "ISODIMPainterDxf.h"
    "GenInsDxfDialog.h"
    "PipeComsMtoSwitch/PipeComsMtoSwitchDialog.h"
)

set(SOURCE_FILES
    "ISOMainWindow.cpp"
    "ISOSvgView.cpp"
    "MBDMainWindow.cpp"
    "MBDSvgView.cpp"
    "MBDDIMPainter.cpp"
    "ui.com.svg.viewer.cpp"
	"main.cpp"
    "ISODIMPainterSvg.cpp"
    "ISOBindingBoxComputer.cpp"
    "ISOPaper.cpp"
    "ISODIMGenerate.cpp"
    "ISOTuNode.cpp"
    "ISOTuLine.cpp"
	"ISOSettingWidgets/ISOFrameSettingDialog.cpp"
    "ISOSettingWidgets/ISOOutputConfigTemplate.cpp"
    "ISOSettingWidgets/ConfigTemplateManager.cpp"
    "ISOSettingWidgets/ISOPlotAreaSettingDialog.cpp"
    "ISOSettingWidgets/ISOPlotAreaEditDialog.cpp"
    "ISOSettingWidgets/ISOMaterialListSettingDialog.cpp"
    "ISOSettingWidgets/ISOMaterialListEditDialog.cpp"
    "ISOSettingWidgets/ISONotesAreaSettingDialog.cpp"
    "ISOSettingWidgets/ISONotesAreaEditDialog.cpp"
    "ISOSettingWidgets/ISOTemplateSettingDialog.cpp"
    "ISOSettingWidgets/ISOTemplateEditDialog.cpp"
    "ISOSettingWidgets/ISOSettingDialog.cpp"
	"ISOPaperMgr.cpp"
    "ISOSvgEditMainWindow.cpp"
    "ISOSvgEditView.cpp"
    "ISOGraphics/ISOGraphicsHelper.cpp"
    "ISOGraphics/ISOGraphics.cpp"
    "ISOGraphics/ISOGraphicsPath.cpp"
    "ISOGraphics/ISOGraphicsLine.cpp"
    "ISOGraphics/ISOGraphicsText.cpp"
    "ISOGraphics/GraphicalObjectManager.cpp"
    "ISOGraphics/ISOGraphicsRect.cpp"
    "ISOGraphics/ISOGraphicsEllipse.cpp"
    "ISOGraphics/RescaleGraphicBox.cpp"
    "MaterialDataStatistics/MaterialDataStatisticsMethods.cpp"
    "MaterialDataStatistics/MaterialDataStatisticsMethodsMgr.cpp"
    "MaterialDataStatistics/MaterialDataStatisticsHelper.cpp"
	"MaterialDataStatistics/MaterialDataStatisticsCommon.cpp"
	"ISOTableCommon/ISOTableWidget.cpp"
	"ISOTableStyle/TableStyleMgr.cpp"
	"ISOTableCommon/IsoTableCommon.cpp"
	"ISOTableStyle/TableStyleHD/TableStyleHD.cpp"
	"ISOTableStyle/TableStyleSYHG/TableStyleSYHG.cpp"
    "ISOTableStyle/TableStyleXB/TableStyleXB.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/MatDataTableJson.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/CSJsonMgr.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/ConfigurationJson.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/DesignParameterJson.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/MaxStressTableJson.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/ThrustAndMementJson.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/ValnDataJson.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/NoteExcel.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/PipeDataJson.cpp"
    "ISOTableStyle/TableStyleXB/CSDataJson/NodeCoordinateJson.cpp"
	"ISODataConfigDialog/ISODataConfigDialog.cpp"
    "ISOWeldQueryTable.cpp"
    "FigureLegends/ISOFigureLegendsMgr.cpp"
    "FigureLegends/ISOFigureLegend.cpp"
    "BatchDrawIsoDialog.cpp"
	"WDPainter/WDIsoPainter/WDIsoSvgPainter.cpp"
    "WDPainter/WDIsoPainter/WDIsoDxfPainter.cpp"
	"ISOSaveHelper.cpp"
    "IsoIndexTable.cpp"
    "DRW/DxfPainterInterface.cpp"
    "DRW/dx_iface.cpp"
    "ISODIMPainterDxf.cpp"
    "GenInsDxfDialog.cpp"
    "PipeComsMtoSwitch/PipeComsMtoSwitchDialog.cpp"
)

set(FORM_FILES
    "ISOMainWindow.ui" 
    "MBDMainWindow.ui"
	"ISOSettingWidgets/ISOFrameSettingDialog.ui"
    "ISOSettingWidgets/ISOSettingDialog.ui"
    "ISOSettingWidgets/ISOPlotAreaEditDialog.ui"
    "ISOSettingWidgets/ISOMaterialListEditDialog.ui"
    "ISOSettingWidgets/ISONotesAreaEditDialog.ui"
    "ISOSettingWidgets/ISOTemplateEditDialog.ui"
	"ISODataConfigDialog/ISODataConfigDialog.ui"
    "BatchDrawIsoDialog.ui"
    "GenInsDxfDialog.ui"
    "PipeComsMtoSwitch/PipeComsMtoSwitchDialog.ui"
)

set(RCC_FILES "ISOMainWindow.qrc" "MBDMainWindow.qrc" )

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
        ${RCC_FILES}
)

if(MSVC)
target_compile_options(${TARGET_NAME} PRIVATE /bigobj)
endif()

target_link_libraries(${TARGET_NAME} PUBLIC dxfrw)
target_link_libraries(${TARGET_NAME} PUBLIC util.rapidxml util.svgTool util.rapidjson)
target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.property qtpropertybrowser ui.commonLib.custom ui.commonLib.WeakObject ui.commonLib.excel)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets Qt5::Svg)
if (WIN32)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::AxContainer)
endif()
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}) 

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./weldTypeTable
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./weldTypeTable ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/weldTypeTable
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)

DeployQt(${TARGET_NAME})