#pragma once
#include "../TableStyleBase.h"

WD_NAMESPACE_BEGIN

class TableStyleSYHG : public TableStyleBase
{
public:
    // 注释：位置在标题栏上方,编码格式为utf8
    StringVector notes;
    WDFontStyle nodeFont;
    // 合法所有人
    std::string rightfulOwner = WD::WDTs("TableStyle", "HGCompany");
    // 项目名称
    std::string projectName;
    // 区域名称
    std::string areaName;
    // 项目文件号
    std::string projectFileNumber;
    // 阶段
    std::string stage;
    // 版次
    std::string edition;
public:
    TableStyleSYHG(WDCore& core);
    virtual ~TableStyleSYHG();
public:
    virtual void init(ISOPaper& paper) const override;

    inline virtual Style style() const override
    {
        return S_SYHG;
    }
    virtual void exec(WDAbstractPainter2D* painter, WD::WDNode& branNode, ISOPaper& paper, const TuLine& line) const override;
    //virtual void exec(WDAbstractPainter2D* painter, const WD::WDNode::Nodes& pipeNodes, ISOPaper& paper, const std::vector<TuLine>& lines) const override;
    /**
     * @brief 获取json管理指针
     * @return
     */
    virtual DependJsonMgr* dependJsonMgr() override;
    /**
     * @brief 设置数据依赖的json
     * @param pJsonMgr
     */
    virtual void setDependJsonMgr(DependJsonMgr* pJsonMgr) override;
};


WD_NAMESPACE_END
