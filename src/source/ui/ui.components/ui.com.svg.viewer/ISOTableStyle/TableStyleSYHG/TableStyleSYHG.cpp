#include    "TableStyleSYHG.h"
#include    "../../MaterialDataStatistics/MaterialDataStatisticsHelper.h"
#include    <qdatetime.h>
#include    "WDPainter/WDIsoPainter/WDIsoSvgPainter.h"
#include    "WDPainter/WDIsoPainter/WDIsoDxfPainter.h"
WD_NAMESPACE_USE

// 实际大小,便于后续对表格进行缩放
DVec2 StandradTitleBarSize = DVec2(198.0, 35.0);
DVec2 StandradPaperDescAreaSize = DVec2(354.0, 21.0);

class TableStyleSYHGPrivate
{
public:
    // 标题栏项
    struct TitleBarItem
    {
        // 坐标起始点
        DVec2 position;
        // 项的大小
        DVec2 size;
        // 标题
        std::string title;
        // 文本内容
        std::string text;
        // 文本的对齐方式，注意：有标题的项配置对齐方式不生效
        WDAlign textAlign;
        // 文本的字体
        WDFontStyle textStyle;
        // 当前项是否是logo项
        bool isLogo;
        TitleBarItem()
        {
            position = DVec2::Zero();
            size = DVec2::Zero();
            title = "";
            text = "";
            textAlign = {WDAlign::HAlign::HA_Left, WDAlign::VAlign::VA_Center};
            isLogo = false;
        }
        // 项是否有效
        bool valid() const
        {
            return size != DVec2::Zero();
        }
        bool bPaintFrame = true;
    };
    using TitleBarItems = std::vector<TitleBarItem>;
    /**
    * @brief 绘制线框
    */
    static void DrawLineSide(WDAbstractPainter2D& painter, ISOPaper& paper,bool dxf = false)
    {
        auto width = paper.size().width();
        auto height = paper.size().height();

        const auto innerTop      = paper.paintArea().top();
        const auto innerBottom   = paper.paintArea().bottom();
        const auto innerLeft     = paper.paintArea().left();
        const auto innerRight    = paper.paintArea().right();
        
        const auto outerTop      = paper.paintArea().top() - paper.frame.top;
        const auto outerBottom   = paper.paintArea().bottom() + paper.frame.bottom;
        const auto outerLeft     = paper.paintArea().left() - paper.frame.left;
        const auto outerRight    = paper.paintArea().right() + paper.frame.right;

        WDShapeFillStyle shapeStyle;
        shapeStyle.color = Color::white;
        // 背景使用白色填充
        if (!dxf)
        {
            painter.fillRect(DVec2(0), DVec2(width, height), shapeStyle);
        }
        // 内框
        painter.drawRect(DVec2(innerLeft, innerTop), DVec2(innerRight - innerLeft, innerBottom - innerTop), paper.frame.lineStyle);
        // 外框
        painter.drawRect(DVec2(outerLeft, outerTop), DVec2(outerRight - outerLeft, outerBottom - outerTop), paper.frame.lineStyle);

        const auto& text = WD::WDTs("TableStyle", "HGCEI");

        auto pose = DVec2(static_cast<double>(innerRight), static_cast<double>(outerBottom + innerBottom) / 2);
        WDFontStyle fontStyle;
        fontStyle.fontSize = paper.frame.bottom * 0.6;
        painter.drawText(text, pose, fontStyle, { WDAlign::HAlign::HA_Right, WDAlign::VAlign::VA_Center });
    }
    /**
    * @brief 获取标题栏的数据
    */
    static void GetTitleBarData(WDNode& branNode, ISOPaper& paper, TitleBarItems& items, const TableStyleSYHG& style)
    {
        const auto& titleBar = paper.titleBar;
        if (!titleBar.bVisible)
            return;
        const DVec2 size = DVec2(titleBar.width, titleBar.height);
        const double xScale = size.x / StandradTitleBarSize.x;
        const double yScale = size.y / StandradTitleBarSize.y;
        const DVec2 scale = DVec2(xScale, yScale);
        // 这里暂时写死数据,按理说应该是从界面上获取
        DVec2 position = paper.titleBar.position();
        // 标题栏暂时用写死的字体族和大小
        // logo
        items.push_back({});
        items.back().position = position + DVec2(0, 0) * scale;
        items.back().size = DVec2(14) * scale;
        items.back().isLogo = true;
        items.back().bPaintFrame = false;
        // 合法所有人
        items.push_back({});
        items.back().position = position + DVec2(14, 0) * scale;
        items.back().size = DVec2(50, 14) * scale;
        items.back().text = style.rightfulOwner;
        items.back().textStyle.fontSize = 4.0 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};
        items.back().bPaintFrame = false;

        // 项目名称
        items.push_back({});
        items.back().position = position + DVec2(64, 0) * scale;
        items.back().size = DVec2(70, 14) * scale;
        items.back().text = style.projectName;
        items.back().textStyle.fontSize = 4.0 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};

        //区域名称
        items.push_back({});
        items.back().position = position + DVec2(134, 0) * scale;
        items.back().size = DVec2(64, 14) * scale;
        items.back().text = style.areaName;
        items.back().textStyle.fontSize = 4.0 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};

        //图纸信息
        items.push_back({});
        items.back().position = position + DVec2(64, 14) * scale;
        items.back().size = DVec2(70, 21) * scale;
        {
            std::string pipeNumberPart;
            if (branNode.parent() != nullptr)
            {
                auto& pipeName = branNode.parent()->srcName();
                auto vec = StringSplit(pipeName, "-");
                if (vec.size() >= 4)
                    pipeNumberPart = vec[2] + '-' + vec[3];
            }
            char info[1024] = { 0 };
            if (!pipeNumberPart.empty())
                pipeNumberPart.push_back('-');
            if (style.paperIndex <= style.allPaperCnt)
                sprintf_s(info, sizeof(info), "%s(%d/%d)", pipeNumberPart.c_str(), style.paperIndex, style.allPaperCnt);
            else
                sprintf_s(info, sizeof(info), "%s(%d/%d)", pipeNumberPart.c_str(), style.allPaperCnt, style.paperIndex);
            items.back().text = std::string(info);
            paper.info.name = std::string(info);
        }
        items.back().textStyle.weight = WDFontStyle::Bold;
        items.back().textStyle.fontSize = 4.0 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};

        //项目文件号
        items.push_back({});
        items.back().position = position + DVec2(134, 14) * scale;
        items.back().size = DVec2(29, 7) * scale;
        items.back().text = WD::WDTs("TableStyle", "PrjFileNumber");
        items.back().textStyle.fontSize = 4.0 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};

        //项目文件号值
        items.push_back({});
        items.back().position = position + DVec2(163, 14) * scale;
        items.back().size = DVec2(35, 7) * scale;
        items.back().text = style.projectFileNumber;
        items.back().textStyle.fontSize = 4.0 * yScale;
        items.back().textAlign = {WDAlign::HAlign::HA_Center, WDAlign::VAlign::VA_Center};

        //阶段
        items.push_back({});
        items.back().position = position + DVec2(134, 21) * scale;
        items.back().size = DVec2(29, 7) * scale;
        items.back().title = WD::WDTs("TableStyle", "Phase");
        items.back().text = style.stage;
        items.back().textStyle.fontSize = 4.0 * yScale;

        //版次
        items.push_back({});
        items.back().position = position + DVec2(163, 21) * scale;
        items.back().size = DVec2(35, 7) * scale;
        items.back().title = WD::WDTs("TableStyle", "Version");
        items.back().text = style.edition;
        items.back().textStyle.fontSize = 4.0 * yScale;
        paper.info.edition = style.edition;

        //图号
        items.push_back({});
        items.back().position = position + DVec2(134, 28) * scale;
        items.back().size = DVec2(64, 7) * scale;
        items.back().title = WD::WDTs("TableStyle", "ISONumber");
        items.back().text = style.drawingNumber;
        items.back().textStyle.weight = WDFontStyle::Bold;
        items.back().textStyle.fontSize = 4.0 * yScale;
        paper.info.drawingNumber = style.drawingNumber;
    }
    /**
    * @brief 绘制标题栏区
    */
    static void DrawTitleBar(WDAbstractPainter2D& painter, ISOPaper& paper, const TitleBarItems& items)
    {
        auto& titleBar = paper.titleBar;
        if (items.empty() || !titleBar.bVisible)
            return;
        for(auto& item : items)
        {
            if (!item.valid())
                continue;
            DVec2 titlePosS = item.position;
            auto& size = item.size;
            if (item.bPaintFrame)
                painter.drawRect(item.position, size, titleBar.innerBorderLine);
            if (item.title.empty())
            {
                DVec2 textPosS = item.position;
                DVec2 textPosE = item.position + size;
                painter.drawText(item.text, textPosS, textPosE, item.textStyle, item.textAlign, true);
                if (item.isLogo)
                {
                    // 这里计算logo项的size(以短边为宽，做正方形)
                    auto minLeng = Min(size.x, size.y);
                    auto center = item.position + size / 2;
                    char logoPath[1024] = { 0 };
                    sprintf_s(logoPath, sizeof(logoPath), "%s/iso/logo/syhg.png", WD::Core().dataDirPath());
                    WDImage image;
                    if (!image.load(logoPath))
                    {
                        assert(false);
                        continue;
                    }
                    painter.drawImage(center - minLeng / 2, image, DVec2(minLeng, minLeng));
                }
            }
            else
            {
                auto strSize = painter.calcTextSize(item.title, item.textStyle);
                auto titlePosE = titlePosS + DVec2(strSize.x * 1.1, size.y);
                titlePosE.x += 2.0;
                painter.drawText(item.title, titlePosS, titlePosE, item.textStyle);
                auto textPosS = DVec2(titlePosE.x, item.position.y);
                auto textPosE = item.position + size;
                painter.drawText(item.text, textPosS, textPosE, item.textStyle, {}, true);
            }
        }
        // 这里外边框的起始位置取items的第一项的位置
        auto& position = items[0].position;
        painter.drawRect(position, DVec2(titleBar.width, titleBar.height), titleBar.outerBorderLine);
    }
    /**
    * @brief 绘制附注
    */
    static void DrawNotes(WDAbstractPainter2D& painter, const TableStyleSYHG& style, ISOPaper& paper)
    {
        // 表格每项的大小暂时写死
        DVec2 size;
        size.x = (paper.size().width() - paper.margins().right() - paper.titleBar.position().x - 5);
        size.y = 8;
        //注释的部分暂时写死位置固定在材料表上面
        // 这里用表格来实现注释的绘制
        ISOTableWidget table;
        table.bGridVisible = false;
        StringVector notes;
        notes.reserve(style.notes.size());
        for (auto& each : style.notes)
        {
            if (each.empty())
                continue;
            notes.push_back(each);
        }
        table.setColCnt(1);
        table.setRowCnt(notes.size() + 1);
        table.setText(0, 0, WD::WDTs("TableStyle", "ISOAnnotations"));
        table.setVerticalTexts(notes, 0, 1);
        table.setTableWidth(size.x);
        table.setTableHeight(size.y * table.rowCnt());

        table.position = (paper.titleBar.position()) - DVec2(-5, (table.tableHeight() + size.y));

        table.update(painter, style.nodeFont, {WDAlign::HA_Left, WDAlign::VA_Center});
    }
    /**
    * @brief 绘制签署栏区
    */
    static void DrawSignBar(WDAbstractPainter2D& painter, ISOPaper& paper)
    {
        // 图纸说明
        auto& signBar = paper.signBar;
        if (!signBar.bVisible)
            return;
        const auto& titleBar = paper.titleBar;
        const DVec2 size = DVec2(titleBar.width, titleBar.height);
        const double xScale = size.x / StandradTitleBarSize.x;
        const double yScale = size.y / StandradTitleBarSize.y;
        const DVec2 scale = DVec2(xScale, yScale);

        std::vector<double> colVec = signBar.colWidths;
        for (auto& each : colVec)
            each = each * xScale;
        std::vector<double> rowVec = signBar.rowHeights;
        for (auto& each : rowVec)
            each = each * yScale;

        ISOTableWidget signBarTable(IVec2(static_cast<int>(rowVec.size()), static_cast<int>(colVec.size())));
        signBarTable.position = titleBar.position() + DVec2(0, 14.0) * scale;

        signBarTable.bVisible = signBar.bVisible;
        signBarTable.innerBorderLine = signBar.innerBorderLine;
        signBarTable.outerBorderLine = signBar.outerBorderLine;

        signBarTable.tableDistribution = {WD::ISOTableHorizontal::H_AllFixed, WD::ISOTableVertical::V_AllFixed};
        signBarTable.setColWidths(colVec);
        signBarTable.setRowHeights(rowVec);

        signBarTable.setText(0, 0, WD::WDTs("TableStyle", "ISODesign"));
        signBarTable.setText(1, 0, WD::WDTs("TableStyle", "ISOCheck"));


        WDFontStyle fontStyle;
        fontStyle.fontSize = signBar.textFont.fontSize;
        signBarTable.update(painter, fontStyle);
    }
    /**
    * @brief 绘制图纸说明
    */
    static void DrawPaperDesc(WDAbstractPainter2D& painter, WDNode& branch, ISOPaper& paper)
    {
        auto& paperDescArea = paper.paperDescArea;
        if (!paperDescArea.bVisible)
            return ;
        const DVec2 size = DVec2(paperDescArea.width, paperDescArea.height);
        const double xScale = size.x / StandradPaperDescAreaSize.x;
        const double yScale = size.y / StandradPaperDescAreaSize.y;
        const DVec2 scale = DVec2(xScale, yScale);

        /****************** SYHG的图纸说明区由两个表格和几个item组成 ********************/
        const auto position = paperDescArea.position();

        auto pPipeNode = branch.parent();
        struct PaperDescItem
        {
            DVec2 position;
            DVec2 size;
            std::string text;
            WDFontStyle textStyle;
        };
        std::vector<PaperDescItem> items;
        // 管道编号值
        items.push_back({});
        items.back().position = position + DVec2(0.0, 0.0) * scale;
        items.back().size = DVec2(70.0, 7.0) * scale;
        items.back().text = WD::WDTs("TableStyle", "ISOPipeNumber");
        if (pPipeNode != nullptr)
            items.back().text = pPipeNode->name();
        items.back().textStyle.fontSize = yScale * 4.0;

        // 管道编号
        items.push_back({});
        items.back().position = position + DVec2(0.0, 7.0) * scale;
        items.back().size = DVec2(70.0, 14.0) * scale;
        items.back().text = WD::WDTs("TableStyle", "ISOPipeNumber");
        items.back().textStyle.fontSize = yScale * 4.0;

        // 操作条件
        items.push_back({});
        items.back().position = position + DVec2(70.0, 14.0) * scale;
        items.back().size = DVec2(40.0, 7.0) * scale;
        items.back().text = WD::WDTs("TableStyle", "OperationCondition");
        items.back().textStyle.fontSize = yScale * 4.0;

        // 设计条件
        items.push_back({});
        items.back().position = position + DVec2(110.0, 14.0) * scale;
        items.back().size = DVec2(40.0, 7.0) * scale;
        items.back().text = WD::WDTs("TableStyle", "DesignCondition");
        items.back().textStyle.fontSize = yScale * 4.0;

        // 试验压力
        items.push_back({});
        items.back().position = position + DVec2(150.0, 14.0) * scale;
        items.back().size = DVec2(20.0, 7.0) * scale;
        items.back().text = WD::WDTs("TableStyle", "TestPressure");
        items.back().textStyle.fontSize = yScale * 2.7;
        items.back().textStyle.weight = WDFontStyle::Bold;

        // 表1
        ISOTableWidget table1;
        table1.position = position + DVec2(70.0, 0.0) * scale;
        table1.setSizeMatrix(2, 5);
        table1.tableDistribution = {WD::ISOTableHorizontal::H_Uniform, WD::ISOTableVertical::V_Uniform};
        table1.setTableWidth(xScale * 100.0);
        table1.setTableHeight(yScale * 14.0);
        table1.setHorizontalTexts({ WD::WDTs("TableStyle", "Pressure")
            ,  WD::WDTs("TableStyle", "Temperature")
            ,  WD::WDTs("TableStyle", "Pressure")
            ,  WD::WDTs("TableStyle", "Temperature")
            ,  WD::WDTs("TableStyle", "HydrostaticTest")
            }, 1);

        // 表2
        ISOTableWidget table2;
        table2.position = position + DVec2(170.0, 0.0) * scale;
        table2.setSizeMatrix(2, 10);
        std::vector<double> colVec = {20.0, 18.0, 18.0, 18.0, 18.0, 18.0, 18.0, 18.0, 20.0, 18.0};
        for (auto& each : colVec)
            each = xScale * each;
        std::vector<double> rowVec = {7.0, 14.0};
        for (auto& each : rowVec)
            each = yScale * each;
        table2.tableDistribution = {WD::ISOTableHorizontal::H_AllFixed, WD::ISOTableVertical::V_AllFixed};
        table2.setColWidths(colVec);
        table2.setRowHeights(rowVec);

        table2.setHorizontalTexts({
            WD::WDTs("TableStyle", "LeakageExperiment")
            ,  WD::WDTs("TableStyle", "NominalDiameter")
            ,  WD::WDTs("TableStyle", "PipeLevel")
            ,  WD::WDTs("TableStyle", "InsulationThickness")
            ,  WD::WDTs("TableStyle", "PressurePipeLevel")
            ,  WD::WDTs("TableStyle", "NDT")
            ,  WD::WDTs("TableStyle", "FlawDetectionRatio")
            ,  WD::WDTs("TableStyle", "PipelineLevel")
            ,  WD::WDTs("TableStyle", "WeldHeatTreatment")
            ,  WD::WDTs("TableStyle", "Notes")}, 1);
        if (pPipeNode != nullptr)
        {
            // 操作压力
            table1.setText(0, 0, pPipeNode->getAttribute(":FOpePressure").convertToString());
            // 操作温度
            table1.setText(0, 1, pPipeNode->getAttribute(":FOpeTemp").convertToString());
            // 设计压力
            table1.setText(0, 2, pPipeNode->getAttribute(":FDesPressure").convertToString());
            // 设计温度
            table1.setText(0, 3, pPipeNode->getAttribute(":FDesTemp").convertToString());
            // 水压实验
            table1.setText(0, 4, pPipeNode->getAttribute(":WtPressure").convertToString());

            // 泄露实验
            table2.setText(0, 0, pPipeNode->getAttribute(":GtPressure").convertToString());
            // 公称直径
            table2.setText(0, 1, pPipeNode->getAttribute(":PipeMainBore").convertToString());
            // 管道等级
            auto pSpec = pPipeNode->getAttribute("Pspec").toNodeRef().refNode();
            if (pSpec != nullptr)
                table2.setText(0, 2, pSpec->name().c_str());
            else
                table2.setText(0, 2, "");
            // 保温厚度
            table2.setText(0, 3, pPipeNode->getAttribute(":InsuThk").convertToString());
            // 压力管道级别
            table2.setText(0, 4, pPipeNode->getAttribute(":JiBie").convertToString());
            // 无损检测
            table2.setText(0, 5, pPipeNode->getAttribute(":WuSun").convertToString());
            // 探伤比例
            table2.setText(0, 6, pPipeNode->getAttribute(":TanS").convertToString());
            // 管道级别
            table2.setText(0, 7, pPipeNode->getAttribute(":HeGe").convertToString());
            // 焊后热处理
            table2.setText(0, 8, pPipeNode->getAttribute(":HeatTreat").convertToString());
            // 备注
            table2.setText(0, 9, "");
        }
        for (auto& each : items)
        {
            if (paperDescArea.bVisibleGrid)
                painter.drawRect(each.position, each.size, paperDescArea.innerBorderLine);

            painter.drawText(each.text, each.position, each.position + each.size, each.textStyle, {}, true);
        }
        table1.bGridVisible = paperDescArea.bVisibleGrid;
        table2.bGridVisible = paperDescArea.bVisibleGrid;
        table1.innerBorderLine = paperDescArea.innerBorderLine;
        table1.outerBorderLine = paperDescArea.innerBorderLine;
        table2.innerBorderLine = paperDescArea.innerBorderLine;
        table2.outerBorderLine = paperDescArea.innerBorderLine;

        WDFontStyle fontStyle;
        fontStyle.fontSize = yScale * 4.0;
        table1.update(painter, fontStyle);
        table2.update(painter, fontStyle);
        painter.drawRect(position, size, paperDescArea.outerBorderLine);
    }
};

TableStyleSYHG::TableStyleSYHG(WDCore& core) : TableStyleBase(core)
{
    // 注释区
    nodeFont.family = WD::WDTs("Common", "SimSun");
    nodeFont.fontSize = 4.0;

    notes.push_back(WD::WDTs("TableStyle", "HGNoteRow1")); 
    notes.push_back(WD::WDTs("TableStyle", "HGNoteRow2"));
    notes.push_back(WD::WDTs("TableStyle", "HGNoteRow3"));
    notes.push_back(WD::WDTs("TableStyle", "HGNoteRow4"));
    notes.push_back(WD::WDTs("TableStyle", "HGNoteRow5"));
    notes.push_back(WD::WDTs("TableStyle", "HGNoteRow6"));
}

TableStyleSYHG::~TableStyleSYHG()
{
}

void TableStyleSYHG::init(ISOPaper& paper) const
{
    // 图纸大小
    //ISOPaper::Size paperSize = ISOPaper::Size(594.0, 420.0);
    // 图纸边缘留白（原始的7+华东院的4.5，单位mm）
    ISOPaper::Margins paperMargins = ISOPaper::Margins(25.0, 17.0, 11.5, 11.5);
    // 图纸边框大小
    ISOPaper::Margins frameMargins = ISOPaper::Margins(15.0, 7.0, 7.0, 7.0);

    //paper.setSize(paperSize);
    paper.setMargin(paperMargins);
    paper.frame = ISOPaper::Frame(7.0, 7.0, 15.0, 7.0, 0.35, false);
    // 材料表区
    {
        auto& materialTable = paper.materialTable;
        materialTable.innerBorderLine.width = 0.18f;
        materialTable.outerBorderLine.width = 0.18f;
        for (auto& i : ISOMaterialArea::AllFlags)
        {
            if (!materialTable.flags.hasFlag(i))
            {
                continue;
            }
            materialTable.flags.removeFlag(i);
        }
        materialTable.flags.removeFlags();
        materialTable.flags.addFlags(ISOMaterialArea::MPF_Number
            , ISOMaterialArea::MPF_Descrip
            , ISOMaterialArea::MPF_Spec
            , ISOMaterialArea::MPF_MaterialCode
            , ISOMaterialArea::MPF_Count);
        materialTable.details = ISOMaterialArea::DT_RText;
        materialTable.texture = ISOMaterialArea::TT_XText;
        materialTable.align = ISOMaterialArea::MTA_RightTop;
        // 列宽和表头名称应该由界面上根据设置的flag及对应的信息决定，这里暂时写死
        materialTable.colWidths = {12.0, 88.0, 20.0, 56.0, 16.0};
        materialTable.width = 0.0;
        for (auto& each : materialTable.colWidths)
            materialTable.width += each;
        materialTable.defaultRowHeight = 6;
        materialTable.bVisibleGrid = false;
    }

    // 标题栏
    auto& titleBar = paper.titleBar;
    titleBar.innerBorderLine.width = 0.18f;
    titleBar.outerBorderLine.width = 0.3f;
    titleBar.width = 198.0;
    titleBar.height = 35.0;
    // 签名栏
    auto& signBar = paper.signBar;
    signBar.innerBorderLine.width = 0.18f;
    signBar.outerBorderLine.width = 0.18f;
    signBar.height = 21.0;
    signBar.textFont.fontSize = 4.0;
    signBar.colWidths = {22.0, 21.0, 21.0};
    signBar.width = 0.0;
    for (auto& each : signBar.colWidths)
        signBar.width += each;
    signBar.rowHeights = {7.0, 7.0, 7.0};
    signBar.height = 21.0;
    signBar.setPosition(DVec2(379.0, 392.0));
    // 图纸描述区
    auto& paperDescArea = paper.paperDescArea;
    paperDescArea.height = 21.0;
    paperDescArea.width = 354.0;
    paperDescArea.innerBorderLine.width = 0.18f;
    paperDescArea.outerBorderLine.width = 0.18f;
    paperDescArea.setPosition(DVec2(paper.paintArea().left(), paper.paintArea().bottom() - paperDescArea.height));
    paper.update(std::nullopt);
}

void TableStyleSYHG::exec(WDAbstractPainter2D* painter, WD::WDNode& branNode, ISOPaper& paper, const TuLine& line) const
{
    if (painter == nullptr)
    {
        return;
    }
    paper.update(branNode.aabb());
    // 绘制图纸的边框
    auto pGroup = paper.polttingArea.pGroup;
    if (pGroup == nullptr)
    {
        assert(false);
        return;
    }
    auto svgPainter = dynamic_cast<WDISOSVGPainter*>(painter);
    auto tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "sideArea");
    bool dxf = false;
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    else if (dynamic_cast<WDISODxfPainter*>(painter) != nullptr)
    {
        dxf = true;
    }
    TableStyleSYHGPrivate::DrawLineSide(*painter, paper, dxf);
    // 设置材料表属性后更新paper
    paper.update(branNode.aabb());
    // 绘制标题栏区
    TableStyleSYHGPrivate::TitleBarItems items;
    TableStyleSYHGPrivate::GetTitleBarData(branNode, paper, items, *this);
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "titleBar");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleSYHGPrivate::DrawTitleBar(*painter, paper, items);
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "nodes");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleSYHGPrivate::DrawNotes(*painter, *this, paper);
    // 绘制签署栏
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "signBar");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleSYHGPrivate::DrawSignBar(*painter, paper);
    // 绘制图纸说明
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "papterDescription");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    TableStyleSYHGPrivate::DrawPaperDesc(*painter, branNode, paper);
    // 统计并更新材料表
    tmpGroup = pGroup->append("g").attr(SVG_GROUP_ATTR_NAME, "MaterialTable");
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = &tmpGroup;
    }
    UpdateMaterialTable(*painter, _core, &line, paper, this->style());
    if (svgPainter != nullptr)
    {
        svgPainter->pGroup = nullptr;
    }
}

//void TableStyleSYHG::exec(WDAbstractPainter2D* painter, const WD::WDNode::Nodes& pipeNodes, ISOPaper& paper, const std::vector<TuLine>& lines) const
//{
//    WDUnused4(painter, pipeNodes, paper, lines);
//    return;
//}

DependJsonMgr* TableStyleSYHG::dependJsonMgr()
{
    return nullptr;
}

void TableStyleSYHG::setDependJsonMgr(DependJsonMgr* pJsonMgr)
{
    WDUnused(pJsonMgr);
}
