#include    "ui.com.svg.viewer.h"

#include    "core/nodeTree/WDNodeTree.h"
#include    "core/message/WDMessage.h"

#include    <QBuffer>

#include    "MaterialDataStatistics/MaterialDataStatisticsHelper.h"
#include    "ISOTableStyle/TableStyleSYHG/TableStyleSYHG.h"
#include    "log/WDLoggerPort.h"
#include    "ISODIMPainterDxf.h"
#include    "WDPainter/WDIsoPainter/WDIsoDxfPainter.h"
#include    "DRW/DxfPainterInterface.h"

#include    <QFileDialog>
#include    <QDateTime>
#include    <regex>
#include    "ISOTableStyle/TableStyleXB/CSDataJson/CSJsonMgr.h"
#include    "ISOWeldQueryTable.h"
#include    "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"

UIComSvgViewer::UIComSvgViewer(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    :IUiComponent(mainWindow, attrs)
    ,_app(mainWindow.core())
    , _styleMgr(_app)
{
    _frameSettingDialog = new ISOFrameSettingDialog(_mgr);
    _mbdMainWindow = new MBDMainWindow(_app);
    _dataConfigDialog = new ISODataConfigDialog(_app, _styleMgr);
    _batchDrawIsoDialog = new BatchDrawIsoDialog(_app, _mgr, *this, _dataConfigDialog->indexTableInfo(), mainWindow.widget());
    _genInsDialog = new GenInsDxfDialog(_app, _mgr, *this, mainWindow.widget());
    _pPipeComsMtoSwitchDialog = new PipeComsMtoSwitchDialog(_app, mainWindow.widget());
    _isoMainWindow = nullptr;

    QString weldTableCfgFile = this->path() + "/weldTypeTable/config.xml";
    ISOWeldQueryTable::Get().appendFromXml(weldTableCfgFile.toLocal8Bit().data());
    QString iconPath = QString::fromLocal8Bit(_app.dataDirPath()) + QString("iso\\actionsIcon\\FigureLegends");
    _figureLegendsmgr.fromXML(iconPath);
}
UIComSvgViewer::~UIComSvgViewer()
{
    if (_frameSettingDialog != nullptr)
    {
        delete _frameSettingDialog;
        _frameSettingDialog = nullptr;
    }
    if (_isoMainWindow != nullptr)
    {
        delete _isoMainWindow;
        _isoMainWindow = nullptr;
    }
    if (_batchDrawIsoDialog != nullptr)
    {
        delete _batchDrawIsoDialog;
        _batchDrawIsoDialog = nullptr;
    }
    if (_genInsDialog != nullptr)
    {
        delete _genInsDialog;
        _genInsDialog = nullptr;
    }
    if (_pPipeComsMtoSwitchDialog != nullptr)
    {
        delete _pPipeComsMtoSwitchDialog;
        _pPipeComsMtoSwitchDialog = nullptr;
    }
}

void    UIComSvgViewer::onNotice(UiNotice* pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            if (pActionNotice->action().is("action.pipe.ISO.generate")
                || pActionNotice->action().is("action.pipe.ISO.GenerateDxf")
                || pActionNotice->action().is("action.pipe.InstallationDrawing.generate"))
            {   
                GenPaperType generateType = GenPaperType::GPT_ISO_SVG;
                if (pActionNotice->action().is("action.pipe.ISO.GenerateDxf"))
                {
                    generateType = GenPaperType::GPT_ISO_DXF;
                }
                else if(pActionNotice->action().is("action.pipe.InstallationDrawing.generate"))
                {
                    generateType = GenPaperType::GPT_INS_DXF;
                }
                // 获取分支节点
                auto pCurrentNode = _app.nodeTree().currentNode();
                if (pCurrentNode == nullptr)
                    return;

                WD::WDNode::Nodes pBranNodes;
                if (pCurrentNode->isType("BRAN"))
                {
                    pBranNodes.push_back(pCurrentNode);
                }
                else if (WDBMDPipeUtils::IsPipeComponent(*pCurrentNode) || pCurrentNode->isType("TUBI"))
                {
                    pBranNodes.push_back(pCurrentNode->parent());
                }
                else if (pCurrentNode->isType("PIPE"))
                {
                    for (auto& pChild : pCurrentNode->children())
                    {
                        if (pChild == nullptr)
                            continue;
                        if (pChild->isType("BRAN"))
                            pBranNodes.push_back(pChild);
                    }
                }
                if (pBranNodes.empty())
                {
                    WD_INFO_T("UiComSvgViewer", "CheckNodes");
                    return;
                }

                // 图纸对象
                ISOPaper* pPaper = _mgr.getCurrentPaper();
                assert(pPaper != nullptr);
                if (pPaper == nullptr)
                {
                    WD_INFO_T("UiComSvgViewer", "CheckObject");
                    return;
                }
                pPaper->_genType = generateType;
                if (pPaper->_genType == GenPaperType::GPT_INS_DXF)
                {
                    pPaper->setSize(ISOPaper::Size(841.0, 594.0));
                    pPaper->info.pType = ISOPaper::PT_A1;
                }
                else
                {
                    pPaper->setSize(ISOPaper::Size(594.0, 420.0));
                    pPaper->info.pType = ISOPaper::PT_A2;
                }

                if (_isoMainWindow != nullptr)
                {
                    delete  _isoMainWindow;
                    _isoMainWindow = nullptr;
                }

                double width = pPaper->uCvt.paperToPixel(pPaper->size().width());
                double height = pPaper->uCvt.paperToPixel(pPaper->size().height());
                char szView[1024] = { 0 };
                sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height)); 

                _isoMainWindow = new ISOMainWindow(_app, *pPaper, mWindow().widget());
                BranchISODatas isoDatas;
                for (int i = 0; i < pBranNodes.size(); ++i)
                {
                    auto& each = pBranNodes[i];
                    if (each == nullptr)
                    {
                        assert(false);
                        continue;
                    }

                    WD::WDSvgXmlCreator* pSvg = new   WD::WDSvgXmlCreator("xml version='1.0' encoding='utf-8' standalone='no'");
                    if (pSvg == nullptr)
                    {
                        assert(false);
                        continue;
                    }
                    pSvg->root("svg").attr("viewBox", std::string(szView)).attr("xmlns", "http://www.w3.org/2000/svg").attr("xmlns:xlink", "http://www.w3.org/1999/xlink");

                    pPaper->update(each->aabb());

                    genISOSvgData(*pPaper, *each, *pSvg, static_cast<uint>(i), static_cast<uint>(pBranNodes.size()));
                    isoDatas.push_back({ each, pSvg });
                }
                if (isoDatas.empty())
                {
                    WD_INFO_T("UiComSvgViewer", "CheckNodes");
                    return;
                }
                _isoMainWindow->setBranchISODatas(std::move(isoDatas));
                if (pPaper->_genType == GenPaperType::GPT_ISO_SVG)
                {
                    if (_isoMainWindow->isHidden())
                        _isoMainWindow->show();
                    else
                        _isoMainWindow->activateWindow();
                }
            }
            if (pActionNotice->action().is("action.pipe.MBD.generate"))
            {
                // 获取分支节点
                auto pCurrentNode = _app.nodeTree().currentNode();
                if (pCurrentNode == nullptr)
                    return;

                WDNode::SharedPtr pBranNode = nullptr;
                if (pCurrentNode->isType("BRAN"))
                    pBranNode = pCurrentNode;
                else if (WDBMDPipeUtils::IsPipeComponent(*pCurrentNode) || pCurrentNode->isType("TUBI"))
                    pBranNode = pCurrentNode->parent();
                else
                    pBranNode = nullptr;

                if (pBranNode == nullptr)
                {
                    WD_INFO_T("UiComSvgViewer","CheckNodes");
                    return;
                }

                // 图纸对象
                ISOPaper* paper = _mgr.getCurrentPaper();
                assert(paper != nullptr);
                if (paper == nullptr)
                    return;
                // 显示窗口
                if (_mbdMainWindow->isMinimized())
                    _mbdMainWindow->showNormal();
                else if (_mbdMainWindow->isHidden())
                    _mbdMainWindow->show();
                else
                    _mbdMainWindow->activateWindow();

                paper->update(pBranNode->aabb());
                // 生成SVG数据, 并且指定不需要生成数据到绘图区
                _mbdMainWindow->view().painter().reset();
                // 设置显示数据, 需要先显示窗口之后，再设置显示数据
                auto& dimMgr = _mbdMainWindow->dimObjectMgr();
                dimMgr.clearObjects();
                std::string isoData = genMBDSvgData(*paper, *pBranNode, _mbdMainWindow->view().painter(), 1, 1, &dimMgr);
                _mbdMainWindow->setDisplayData(std::move(isoData), *paper, pBranNode);
            }
            else if (pActionNotice->action().is("action.pipe.ISO.FrameSetting.show"))
            {
                if (_frameSettingDialog->isHidden())
                    _frameSettingDialog->show();
                else
                    _frameSettingDialog->activateWindow();
            }
            else if (pActionNotice->action().is("action.pipe.ISO.DataConfig.show"))
            {
                if (_dataConfigDialog->isHidden())
                    _dataConfigDialog->show();
                else
                    _dataConfigDialog->activateWindow();
            }
            //else if (pActionNotice->action().is("action.pipe.ISO.TemplateSetting.show"))
            //{
            //    if (_templateSettingDialog->isHidden())
            //        _templateSettingDialog->show();
            //    else
            //        _templateSettingDialog->activateWindow();
            //}
            //else if (pActionNotice->action().is("action.pipe.ISO.PlotAreaSetting.show"))
            //{
            //    if (_plotAreaSettingDialog->isHidden())
            //        _plotAreaSettingDialog->show();
            //    else
            //        _plotAreaSettingDialog->activateWindow();
            //}
            //else if (pActionNotice->action().is("action.pipe.ISO.MaterialListSetting.show"))
            //{
            //    if (_materialListSettingDialog->isHidden())
            //        _materialListSettingDialog->show();
            //    else
            //        _materialListSettingDialog->activateWindow();
            //}
            //else if (pActionNotice->action().is("action.pipe.ISO.NotesAreaSetting.show"))
            //{
            //    if (_notesAreaSettingDialog->isHidden())
            //        _notesAreaSettingDialog->show();
            //    else
            //        _notesAreaSettingDialog->activateWindow();
            //}
            else if(pActionNotice->action().is("action.pipe.ISO.batchDraw"))
            {
                if (_batchDrawIsoDialog->isHidden())
                    _batchDrawIsoDialog->show();
                else
                    _batchDrawIsoDialog->activateWindow();
            }
            else if (pActionNotice->action().is("action.pipe.ISO.DrawPipeDxf"))
            {
                if (_genInsDialog->isHidden())
                    _genInsDialog->show();
                else
                    _genInsDialog->activateWindow();
            }
            else if (pActionNotice->action().is("action.pipe.ISO.Mtocomponent.Switch"))
            {
                if (_pPipeComsMtoSwitchDialog->isHidden())
                    _pPipeComsMtoSwitchDialog->show();
                else
                    _pPipeComsMtoSwitchDialog->activateWindow();
            }
        }
        break;
    case UiNoticeType::UNT_ReadyUnload:
        {
            if (_isoMainWindow != nullptr)
                _isoMainWindow->close();
        }
        break;
    default:
        break;
    }

}

void UIComSvgViewer::genISOSvgData(ISOPaper& paper
    , WD::WDNode& branchNode
    , WDSvgXmlCreator& svg
    , const uint& paperIdx
    , const uint& paperCnt
    , const std::string& drawingNumber
    , const std::unordered_map<std::string, std::string>& paperInfos)
{
    // 调用图纸对象的更新
    paper.update(branchNode.aabb());
    // QSvgGenerator 使用测试
#if 0
        {
        QSvgGenerator svgGen;
        QBuffer svgBuffer;
        svgGen.setOutputDevice(&svgBuffer);
        svgGen.setDescription(QString::fromUtf8("测试SVG"));
        svgGen.setSize(QSize(static_cast<int>(600), static_cast<int>(500)));
        svgGen.setViewBox(QRect(0, 0, static_cast<int>(600), static_cast<int>(500)));
        QPainter svgPainter;
        svgPainter.begin(&svgGen);
        QRect rect(0, 0, 400, 400);

        QPen pen;
        pen.setColor(Qt::black);
        pen.setWidth(2);
        pen.setStyle(Qt::CustomDashLine);
        pen.setDashPattern({ 17, 3, 10, 3, 2 });
        svgPainter.setPen(pen);
        svgPainter.drawLine(QPoint(0, 0), QPoint(600, 500));

        svgPainter.setBrush(Qt::white);
        svgPainter.drawEllipse(rect);
        QFont tFont0;
        tFont0.setFamily(QString::fromUtf8("宋体"));
        tFont0.setPixelSize(14);
        svgPainter.setFont(tFont0);
        svgPainter.drawText(QPoint(100, 100), QString::fromUtf8("wse为岗位分工gwgh"));

        QFont tFont1;
        tFont1.setFamily(QString::fromUtf8("TureType"));
        tFont1.setPixelSize(14);
        svgPainter.setFont(tFont1);
        svgPainter.drawText(QPoint(100, 200), QString::fromUtf8("噶啊我我二哥i和"));

        svgPainter.end();

        std::string testData = svgBuffer.data();

        QFile file("D:/test.svg");
        if (file.open(QIODevice::OpenModeFlag::ReadWrite))
        {
            file.write(svgBuffer.data());
            file.close();
        }
    }
#endif
    auto svgRoot = svg.root("svg");

    WDSvgXmlCreator::Node tGroup(svg.doc(), svgRoot.native());
    paper.polttingArea.pGroup = &tGroup;
    // 生成管线对象
    {
        // 开始生成ISO图
        char info[1024] = { 0 };
        sprintf_s(info, sizeof(info), "开始生成分支[%s]的轴测图", branchNode.name().c_str());
        LOG_INFO << info;
    }
    TuLine line = TuLine::Generate(branchNode, _figureLegendsmgr);
    TableStyleBase* pObj;
    //
    if (paper._genType == GenPaperType::GPT_ISO_DXF)
    {
        _style = ISOTableStyle::S_SYHG;
        pObj = _styleMgr.getStyleObject(_style);

        DxfPainterInterface interface(paper.uCvt);
        interface._paperHeight = paper.size().height();
        QString name = QString::fromUtf8(branchNode.name().c_str());
        name.replace('/','_');
        name.replace('\\', '_');
        QString fileName = QFileDialog::getSaveFileName(
            nullptr
            , QString::fromUtf8("保存")
            , name
            , QString::fromUtf8("DXF文件(*.dxf)"));
        if (fileName.isEmpty())
        {
            return;
        }

        QString suffix(".dxf");
        if (!fileName.endsWith(suffix, Qt::CaseInsensitive))
            fileName += suffix;

        WDISODxfPainter dxfPainter(paper.uCvt, paper);
        interface.setPath(fileName.toLocal8Bit().data());
        dxfPainter._pInterface = &interface;
        dxfPainter.unitType = ISOUnitType::UT_mm;
        if (pObj != nullptr)
        {
            pObj->init(paper);
            // 传入的页码下标是从0开始,这里转为从1开始
            pObj->paperIndex = paperIdx + 1;
            pObj->allPaperCnt = paperCnt;
            pObj->drawingNumber = drawingNumber;
            pObj->exec(&dxfPainter, branchNode, paper, line);
        }
        //用像素绘制箭头
        dxfPainter.unitType = ISOUnitType::UT_Pixel;
        // 绘制坐标系朝向箭头
        this->drawCoordArrow(dxfPainter, paper);
        // 绘图区绘制
        paper.polttingArea._genType = paper._genType;
        auto pPolttingAreaGroup = std::make_shared<WDSvgXmlCreator::Node>(svgRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "polttingArea"));
        paper.polttingArea.pGroup = pPolttingAreaGroup.get();
        line.drawISODxf(paper.polttingArea, interface, paperInfos);
        {
            // 生成ISO图完成
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "生成分支%s的轴测图完成", branchNode.name().c_str());
            LOG_INFO << info;
        }
        if (!fileName.isEmpty())
        {
            WD_INFO_T("Common", "GenSuccess");
        }
    }
    else if(paper._genType == GenPaperType::GPT_ISO_SVG)
    {
        _style = ISOTableStyle::S_SYHG;
        pObj = _styleMgr.getStyleObject(_style);
        WDISOSVGPainter svgPainter(paper.uCvt);
        if (pObj != nullptr)
        {
            pObj->init(paper);
            // 传入的页码下标是从0开始,这里转为从1开始
            pObj->paperIndex = paperIdx + 1;
            pObj->allPaperCnt = paperCnt;
            pObj->drawingNumber = drawingNumber;
            pObj->exec(&svgPainter, branchNode, paper, line);
        }
        auto tmpGroup = tGroup.append("g").attr(SVG_GROUP_ATTR_NAME, "coordArrow");
        svgPainter.pGroup = &tmpGroup;
        svgPainter.unitType = ISOUnitType::UT_Pixel;
        // 绘制坐标系朝向箭头
        this->drawCoordArrow(svgPainter, paper);
        auto pPolttingAreaGroup = std::make_shared<WDSvgXmlCreator::Node>(svgRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "polttingArea"));
        paper.polttingArea.pGroup = pPolttingAreaGroup.get();
        // 绘图区绘制
        paper.polttingArea._genType = paper._genType;
        line.drawISO(paper.polttingArea, paperInfos);
        {
            // 生成ISO图完成
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "生成分支%s的轴测图完成", branchNode.name().c_str());
            LOG_INFO << info;
        }
    }
}

void UIComSvgViewer::genISODxfData(ISOPaper& paper, WD::WDNode& branchNode, WDSvgXmlCreator& svg, const QString& inputPath, const QString& outPath, const std::string& drawingNumber, const uint& paperIdx, const uint& paperCnt)
{
    // 调用图纸对象的更新
    paper.update(branchNode.aabb());
    auto svgRoot = svg.root("svg");

    WDSvgXmlCreator::Node tGroup(svg.doc(), svgRoot.native());
    paper.polttingArea.pGroup = &tGroup;
    // 生成管线对象
    {
        // 开始生成ISO图
        char info[1024] = { 0 };
        sprintf_s(info, sizeof(info), "开始生成分支[%s]的轴测图", branchNode.name().c_str());
        LOG_INFO << info;
    }
    TuLine line = TuLine::Generate(branchNode, _figureLegendsmgr);
    TableStyleBase* pObj;

    if (paper._genType == GenPaperType::GPT_INS_DXF)
    {
        _style = ISOTableStyle::S_XB;
        pObj = _styleMgr.getStyleObject(_style);
        auto interface = new  DxfPainterInterface(paper.uCvt);
        interface->_paperHeight = paper.size().height();
        if (outPath.isEmpty())
        {
            return;
        }
        QDir dir(QApplication::applicationDirPath());
        dir.cdUp();
        auto folder = dir.absolutePath();
        auto path = folder + "/data/iso/CarserData/output";
        DependJsonMgr mgr(path,inputPath);

        WDISODxfPainter dxfPainter(paper.uCvt, paper);
        interface->setPath(outPath.toLocal8Bit().data());
        dxfPainter._pInterface = interface;
        dxfPainter.unitType = ISOUnitType::UT_mm;
        if (pObj != nullptr)
        {
            pObj->init(paper);
            // 传入的页码下标是从0开始,这里转为从1开始
            pObj->paperIndex = paperIdx + 1;
            pObj->allPaperCnt = paperCnt;
            pObj->drawingNumber = drawingNumber;
            pObj->setDependJsonMgr(&mgr);
            pObj->exec(&dxfPainter, branchNode, paper, line);
        }
        //用像素绘制箭头
        dxfPainter.unitType = ISOUnitType::UT_Pixel;
        // 绘制坐标系朝向箭头
        this->drawCoordArrow(dxfPainter, paper);
        // 绘图区绘制
        paper.polttingArea._genType = paper._genType;
        auto pPolttingAreaGroup = std::make_shared<WDSvgXmlCreator::Node>(svgRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "polttingArea"));
        paper.polttingArea.pGroup = pPolttingAreaGroup.get();
        line.drawISODxf(paper.polttingArea, *interface);
        {
            // 生成ISO图完成
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "生成分支%s的轴测图完成", branchNode.name().c_str());
            LOG_INFO << info;
        }
    }
    else  if (paper._genType == GenPaperType::GPT_ISO_DXF)
    {
        _style = ISOTableStyle::S_SYHG;
        pObj = _styleMgr.getStyleObject(_style);

        auto interface = new  DxfPainterInterface(paper.uCvt);
        interface->_paperHeight = paper.size().height();
        if (outPath.isEmpty())
        {
            return;
        }
        WDISODxfPainter dxfPainter(paper.uCvt, paper);
        interface->setPath(outPath.toLocal8Bit().data());
        dxfPainter._pInterface = interface;
        dxfPainter.unitType = ISOUnitType::UT_mm;
        if (pObj != nullptr)
        {
            pObj->init(paper);
            // 传入的页码下标是从0开始,这里转为从1开始
            pObj->paperIndex = paperIdx + 1;
            pObj->allPaperCnt = paperCnt;
            pObj->drawingNumber = drawingNumber;
            pObj->exec(&dxfPainter, branchNode, paper, line);
        }
        //用像素绘制箭头
        dxfPainter.unitType = ISOUnitType::UT_Pixel;
        // 绘制坐标系朝向箭头
        this->drawCoordArrow(dxfPainter, paper);
        // 绘图区绘制
        paper.polttingArea._genType = paper._genType;
        auto pPolttingAreaGroup = std::make_shared<WDSvgXmlCreator::Node>(svgRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "polttingArea"));
        paper.polttingArea.pGroup = pPolttingAreaGroup.get();
        line.drawISODxf(paper.polttingArea, *interface);
        {
            // 生成ISO图完成
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "生成分支%s的轴测图完成", branchNode.name().c_str());
            LOG_INFO << info;
        }
    }
}

void UIComSvgViewer::genISODxfData(ISOPaper& paper
    , std::vector<WD::WDNode::SharedPtr> nodes
    , WDSvgXmlCreator& svg
    , QString inputPath
    , QString outPath
    , const uint& paperIdx
    , const uint& paperCnt)
{
    paper._genType = GenPaperType::GPT_INS_DXF;
    if (paper._genType == GenPaperType::GPT_INS_DXF)
    {
        paper.setSize(ISOPaper::Size(841.0, 594.0));
        paper.info.pType = ISOPaper::PT_A1;
    }
    auto svgRoot = svg.root("svg");

    if (nodes.empty())
    {
        return;
    }
    WDSvgXmlCreator::Node tGroup(svg.doc(), svgRoot.native());
    paper.polttingArea.pGroup = &tGroup;
    // 生成管线对象
    {
        // 开始生成ISO图
        char info[1024] = { 0 };
        sprintf_s(info, sizeof(info), "开始生成分支的轴测图");
        LOG_INFO << info;
    }
    std::vector<TuLine> lines;
    WDNode* node = nullptr;
    for (auto& i : nodes)
    {
        if (i == nullptr || !i->isType("PIPE"))
            continue;
        for (const auto& pBRANNode : i->children())
        {
            if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
                continue;
            if (lines.empty())
            {
                node = pBRANNode.get();
            }
            TuLine line = TuLine::Generate(*pBRANNode, _figureLegendsmgr);
            lines.emplace_back(line);
        }
    }
    
    TableStyleBase* pObj;
    if (paper._genType == GenPaperType::GPT_INS_DXF)
    {
        _style = ISOTableStyle::S_XB;
        pObj = _styleMgr.getStyleObject(_style);
        auto interface = new  DxfPainterInterface(paper.uCvt);
        interface->_paperHeight = paper.size().height();
        QString name = QString::fromUtf8(nodes.front()->name().c_str());
        //QString name = QString::fromUtf8(branchNode.name().c_str());
        name.replace('/', '_');
        name.replace('\\', '_');
        QString fileName = outPath + name + ".dxf";
        if (fileName.isEmpty() || node == nullptr)
        {
            return;
        }
        auto path = QApplication::applicationDirPath() + "/CSData/output";
        DependJsonMgr mgr(path, inputPath);
        WDISODxfPainter dxfPainter(paper.uCvt, paper);
        interface->setPath(fileName.toUtf8().data());
        dxfPainter._pInterface = interface;
        dxfPainter.unitType = ISOUnitType::UT_mm;
        if (pObj != nullptr)
        {
            pObj->init(paper);
            // 传入的页码下标是从0开始,这里转为从1开始
            pObj->paperIndex = paperIdx + 1;
            pObj->allPaperCnt = paperCnt;
            pObj->drawingNumber = 1;
            pObj->setDependJsonMgr(&mgr);
            pObj->exec(&dxfPainter, *node, paper, lines.front());
        }
        //用像素绘制箭头
        dxfPainter.unitType = ISOUnitType::UT_Pixel;
        // 绘制坐标系朝向箭头
        this->drawCoordArrow(dxfPainter, paper);
        // 绘图区绘制
        paper.polttingArea._genType = paper._genType;
        auto pPolttingAreaGroup = std::make_shared<WDSvgXmlCreator::Node>(svgRoot.append("g").attr(SVG_GROUP_ATTR_NAME, "polttingArea"));
        paper.polttingArea.pGroup = pPolttingAreaGroup.get();
        drawISODxf(lines,paper.polttingArea, *interface, &mgr);
        {
            // 生成ISO图完成
            char info[1024] = { 0 };
            sprintf_s(info, sizeof(info), "生成的安装图完成");
            LOG_INFO << info;
        }
        if (!fileName.isEmpty())
        {
            WD_INFO_T("Common", "GenSuccess");
        }
    }
}

std::string UIComSvgViewer::genMBDSvgData(ISOPaper& paper
    , WD::WDNode& branchNode
    , MBDDIMPainter& painter
    , const uint& paperIdx
    , const uint& paperCnt
    , ISODIMObjectMgr* pOutDimObjectMgr)
{
    // 调用图纸对象的更新
    paper.update(branchNode.aabb());

    auto& uCvt      = paper.uCvt;

    float width     = uCvt.paperToPixel(paper.size().width());
    float height    = uCvt.paperToPixel(paper.size().height());

    char szView[1024] = { 0 };
    sprintf(szView, "%d %d %d %d", 0, 0, static_cast<int>(width), static_cast<int>(height));

    WDSvgXmlCreator  svg("xml version='1.0' encoding='utf-8' standalone='no'");
    svg.root("svg")
        .attr("viewBox", std::string(szView))
        .attr("xmlns", "http://www.w3.org/2000/svg")
        .attr("xmlns:xlink", "http://www.w3.org/1999/xlink");

    auto gGrp = svg.root("svg").append("g");

    WDSvgXmlCreator::Node tGroup(svg.doc(), gGrp.native());
    paper.polttingArea.pGroup = &tGroup;
    painter.pUCvt = &paper.uCvt;
    // 设置材料表属性后更新paper
    paper.update(branchNode.aabb());

    // 生成管线对象
    TuLine line = TuLine::Generate(branchNode, _figureLegendsmgr);
    // 这里MBD三维区域绘制模型时，目前还不支持其他风格的图框，需要后续处理，这里风格固定使用SYHG
    TableStyleBase* pObj =_styleMgr.getStyleObject(ISOTableStyle::S_SYHG);
    WDISOSVGPainter svgPainter(paper.uCvt);
    if (pObj != nullptr)
    {
        pObj->init(paper);
        // 传入的页码下标是从0开始,这里转为从1开始
        pObj->paperIndex = paperIdx + 1;
        pObj->allPaperCnt = paperCnt;
        pObj->exec(&svgPainter, branchNode, paper, line);
    }
    // 绘图区绘制
    line.drawMBD(painter, paper.uCvt, pOutDimObjectMgr);

    std::string xmlStr = svg.toString();
    QString qStr = QString::fromUtf8(xmlStr.c_str());
    std::string utf8 = qStr.toUtf8().data();

    return utf8;
}

void        UIComSvgViewer::drawCoordArrow(WDAbstractPainter2D& painter, ISOPaper& paper)
{
    const auto& uCvt = paper.uCvt;
    // 箭头直线部分长度(图纸单位mm)
    double lineLength = uCvt.pixelToWorld(120.0);
    // 箭头直线部分宽度(图纸单位mm)
    double lineHalfWidth = uCvt.pixelToWorld(20.0) * 0.5;

    // 箭头三角部分高度(图纸单位mm)
    double triHeight = uCvt.pixelToWorld(60.0);
    // 箭头三角部分底边长度(图纸单位mm)
    double triHalfWidth = uCvt.pixelToWorld(50.0) * 0.5;

    // 箭头前方
    DVec3 front = DVec3::AxisY();
    // 箭头右方
    DVec3 right = DVec3::AxisX();
    // 箭头上方
    DVec3 up = DVec3::AxisZ();

    WDDIMLineStyle style;
    style.width = GetLineWidth<double>(uCvt) * 2.0;

    // 箭头顶点, 箭头原点在箭头的尾部
    DVec3 origin = uCvt.camera().eye() + uCvt.camera().frontDir() * (uCvt.camera().zNear() + 100.0);
    std::vector<DVec2> vertices =
    {
          uCvt.worldToScreen(origin + right * lineHalfWidth)
        , uCvt.worldToScreen(origin + front * lineLength + right * lineHalfWidth)
        , uCvt.worldToScreen(origin + front * lineLength + right * triHalfWidth)
        , uCvt.worldToScreen(origin + front * (lineLength + triHeight))
        , uCvt.worldToScreen(origin + front * lineLength - right * triHalfWidth)
        , uCvt.worldToScreen(origin + front * lineLength - right * lineHalfWidth)
        , uCvt.worldToScreen(origin - right * lineHalfWidth)
    };

    // 计算箭头包围盒
    DAabb2 aabb = DAabb2::Null();
    for (const auto& v : vertices)
        aabb.expandByPoint(v);

    WDLineStyle lineStyle;
    lineStyle.width = GetLineWidth<double>(uCvt) * 2.0;
    WDFontStyle fontStyle;
    fontStyle.fontSize = uCvt.paperToPixel(10.0);
    fontStyle.weight = WDFontStyle::Bold;

    // 测量文字尺寸
    QString text    = "N";

    auto textSize   = painter.calcTextSize(text.toUtf8().data(), fontStyle);
    // 计算文字左上角位置
    DVec2 textLT    = aabb.min - textSize * 1.2;

    DVec2 leftTop   = paper.polttingArea.leftTopPixel() * 1.5;
    // 计算文字以及箭头平移到图纸左上角的偏移量
    DVec2 offset    = leftTop - textLT;
    for (auto& v : vertices)
        v += offset;

    // 绘制文字
    WDAlign textAlign = { WDAlign::HA_Left, WDAlign::VA_Top };
    painter.drawText(text.toUtf8().data(), leftTop, fontStyle, textAlign);
    // 绘制箭头
    painter.drawPolygon(vertices, lineStyle);
}

void UIComSvgViewer::drawISOSVG(std::vector<TuLine>& lines, ISOPolttingArea& paper) const
{
    if (lines.empty())
    {
        return;
    }
    ISOStyleSheet style(paper.uCvt);
    //缩放到图纸大小
    
        ISOBindingBoxComputer aabbComputer(paper.uCvt);
        //先分别计算标注方向和更新节点
        for (auto& line : lines)
        {
            line.prepareInsDraw(paper);
            line.drawToPainter(aabbComputer, paper.uCvt, style);
            aabbComputer.update();
        }

        ISOBindingBoxComputer aabbComputer2(paper.uCvt);
        // 尽量保证填充满图纸
        DVec2 sOffScreen = paper.sizePixel() / aabbComputer.aabb().size();
        std::map<TuNode*, std::pair<DVec3, DVec3> > testMap;
        for (auto& line : lines)
        {
            line.adjustSize(sOffScreen);
        }

        for (auto& line : lines)
        {
            line.drawToPainter(aabbComputer2, paper.uCvt, style);
            aabbComputer2.update();
        }
        for (auto& line : lines)
        {
            line.alignCenter(paper, aabbComputer2.aabb().center());
        }

        for (auto& line : lines)
        {
            line.drawToPainter(paper, paper.uCvt, style);
        }
}

void UIComSvgViewer::drawISODxf(std::vector<TuLine>& lines, ISOPolttingArea& paper, DxfPainterInterface& interface, DependJsonMgr* mgr) const
{
    if (lines.empty()|| mgr == nullptr)
    {
        return;
    }
    ISOStyleSheet style(paper.uCvt);
    //缩放到图纸大小
    {
        ISOBindingBoxComputer aabbComputer(paper.uCvt);
        //先分别计算标注方向和更新节点
        for (auto& line : lines)
        {
            line.prepareInsDraw(paper, mgr);
            line.drawToPainter(aabbComputer, paper.uCvt, style);
            aabbComputer.update();
        }

        ISOBindingBoxComputer aabbComputer2(paper.uCvt);
        for (auto& line : lines)
        {
            line.drawToPainter(aabbComputer2, paper.uCvt, style);
            aabbComputer2.update();
        }
        // 尽量保证填充满图纸
        DVec2 sOffScreen = paper.sizePixel() / aabbComputer.aabb().size();
        std::map<TuNode*, std::pair<DVec3, DVec3> > testMap;
        for (auto& line : lines)
        {
            line.adjustSize(sOffScreen);
        }
        for (auto& line : lines)
        {
            for (auto& i : line.nodes())
            {
                auto pa = i->rPtA();
                auto pl = i->rPtL();
                assert(!IsNan(pa.x)
                    && !IsNan(pa.y)
                    && !IsNan(pa.z)
                    && !IsNan(pl.x)
                    && !IsNan(pl.y)
                    && !IsNan(pl.z));
            }
        }
        for (auto& line : lines)
        {
            line.alignCenter(paper, aabbComputer2.aabb().center());
        }
        for (auto& line : lines)
        {
            for (auto& i : line.nodes())
            {
                auto pa = i->rPtA();
                auto pl = i->rPtL();
                assert(!IsNan(pa.x)
                    && !IsNan(pa.y)
                    && !IsNan(pa.z)
                    && !IsNan(pl.x)
                    && !IsNan(pl.y)
                    && !IsNan(pl.z));
            }
        }
        for (auto& line : lines)
        {
            line.drawToPainter(paper, paper.uCvt, style);
        }
        for (auto& line : lines)
        {
            for (auto& i : line.nodes())
            {
                auto pa = i->rPtA();
                auto pl = i->rPtL();
                assert(!IsNan(pa.x)
                    && !IsNan(pa.y)
                    && !IsNan(pa.z)
                    && !IsNan(pl.x)
                    && !IsNan(pl.y)
                    && !IsNan(pl.z));
            }
        }

        if (paper._genType == GenPaperType::GPT_ISO_DXF || paper._genType == GenPaperType::GPT_INS_DXF)
        {
            ISODIMPainterDxf pp(paper.uCvt);
            pp.setInterface(&interface);
            pp.initStyle();
            for (auto& line : lines)
            {
                line.drawToPainter(pp, paper.uCvt, style);
            }
            pp.write(false);
        }
        
    }
}
