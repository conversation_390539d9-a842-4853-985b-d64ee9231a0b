#include    "ISODataConfigDialog.h"
#include    "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include    "core/message/WDMessage.h"
#include    "core/WDCore.h"
#include    "../ISOTableStyle/TableStyleSYHG/TableStyleSYHG.h"
#include    "core/businessModule/WDBMProject.h"
#include    <filesystem>

WD_NAMESPACE_USE

ISODataConfigDialog::ISODataConfigDialog(WD::WDCore& core
    , WD::TableStyleMgr& mgr
    , QWidget *parent)
    : _core(core), _mgr(mgr), QDialog(parent)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    ui.tabWidget->setCurrentIndex(0);
    retranslateUi();
    QObject::connect(ui.pushButtonApply, &QPushButton::clicked, this, &ISODataConfigDialog::slotQPushButtonApplyClicked);
    QObject::connect(ui.pushButtonDismiss, &QPushButton::clicked, this, &ISODataConfigDialog::slotQPushButtonDismissClicked);
}

ISODataConfigDialog::~ISODataConfigDialog()
{
}


static constexpr const char* XML_Root = "Root";
static constexpr const char* XML_Value = "value";

static constexpr const char* XML_ISO = "ISO";
static constexpr const char* XML_ISO_RightfulOwner = "RightfulOwner";
static constexpr const char* XML_ISO_ProjectName = "ProjectName";
static constexpr const char* XML_ISO_AreaName = "AreaName";
static constexpr const char* XML_ISO_ProjectFileNumber = "ProjectFileNumber";
static constexpr const char* XML_ISO_Stage = "Stage";
static constexpr const char* XML_ISO_Edition = "Edition";
static constexpr const char* XML_ISO_Notes = "Notes";
static constexpr const char* XML_ISO_Note = "Note";

static constexpr const char* XML_Index = "Index";
static constexpr const char* XML_Index_TableName = "TableName";
static constexpr const char* XML_Index_CompanyName = "CompanyName";
static constexpr const char* XML_Index_LOGOPath = "LOGOPath";
static constexpr const char* XML_Index_ProjectFileNumber = "ProjectFileNumber";
static constexpr const char* XML_Index_DocumentNumber = "DocumentNumber";
static constexpr const char* XML_Index_Edition = "Edition";
static constexpr const char* XML_Index_EngineeringName = "EngineeringName";
static constexpr const char* XML_Index_UnitName = "UnitName";
static constexpr const char* XML_Index_OwnerFileNumber = "OwnerFileNumber";
static constexpr const char* XML_Index_Footer = "Footer";

std::string GetXMLItemValue(WD::XMLNode& parent, const char* nodeName)
{
    auto pNode = parent.first_node(nodeName);
    if (pNode == nullptr)
        return "";
    auto pValue = pNode->first_attribute(XML_Value);
    if (pValue == nullptr)
        return "";
    return std::string(pValue->value());
}
StringVector GetXMLItemsValue(WD::XMLNode& parent, const char* nodeName)
{
    StringVector ret;
    auto pNode = parent.first_node(nodeName);
    for (; pNode != nullptr; pNode = pNode->next_sibling(nodeName))
    {
        auto pValue = pNode->first_attribute(XML_Value);
        if  (pValue != nullptr)
            ret.push_back(std::string(pValue->value()));
    }
    return ret;
}
void AddXMLItem(WD::XMLDoc& doc, WD::XMLNode& parent, const char* nodeName, const std::string& value)
{
    auto pNewNode = doc.allocate_node(rapidxml::node_element, nodeName);
    if (pNewNode == nullptr)
    {
        assert(false);
        return;
    }
    auto pAttrNode = doc.allocate_attribute(XML_Value, value.c_str());
    if (pAttrNode == nullptr)
    {
        assert(false);
        return;
    }
    pNewNode->append_attribute(pAttrNode);
    parent.append_node(pNewNode);
}
void AddXMLItems(WD::XMLDoc& doc, WD::XMLNode& parent, const char* nodeName, const StringVector& values)
{
    for (auto& value : values)
        AddXMLItem(doc, parent, nodeName, value);
}

void ISODataConfigDialog::slotQPushButtonApplyClicked()
{
    auto pObj = _mgr.getStyleObject(ISOTableStyle::S_SYHG);
    if (pObj != nullptr)
    {
        auto pSYHGObj = dynamic_cast<TableStyleSYHG*>(pObj);
        if (pSYHGObj != nullptr)
        {
            pSYHGObj->projectName       = ui.lineEditProjectName->text().toUtf8().data();
            pSYHGObj->areaName          = ui.lineEditAreaName->text().toUtf8().data();
            pSYHGObj->projectFileNumber = ui.lineEditProjectFileNumber->text().toUtf8().data();
            pSYHGObj->stage             = ui.lineEditStage->text().toUtf8().data();
            pSYHGObj->edition           = ui.lineEditEdition->text().toUtf8().data();
            pSYHGObj->notes.clear();
            auto annotations = ui.textEditAnnotations->toPlainText().split("\n");
            for (auto& each : annotations)
                pSYHGObj->notes.push_back(each.toUtf8().data());
        }
    }
    _indexTableInfo.projectFileNumber   = ui.lineEditProjectFileNumber_2->text().toUtf8().data();
    _indexTableInfo.documentNumber      = ui.lineEditDocumentNumber->text().toUtf8().data();
    _indexTableInfo.edition             = ui.lineEditEdition_2->text().toUtf8().data();
    _indexTableInfo.engineeringName     = ui.lineEditEngineeringName->text().toUtf8().data();
    _indexTableInfo.unitName            = ui.lineEditUnitName->text().toUtf8().data();
    _indexTableInfo.footer              = ui.lineEditFooter->text().toUtf8().data();

    this->saveXML();
    this->accept();
}
void ISODataConfigDialog::slotQPushButtonDismissClicked()
{
    this->reject();
}
void ISODataConfigDialog::showEvent(QShowEvent*)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    this->loadXML();
    this->updateData();
}

void ISODataConfigDialog::hideEvent(QHideEvent*)
{
}
void ISODataConfigDialog::loadXML()
{
    auto projectDir = _core.project().projectDataDir();
    if (!projectDir.empty())
    {
        char isoDataXmlPath[1024] = { 0 };
        sprintf(isoDataXmlPath, "%s/%s", projectDir.c_str(), IsoDataConfigXMLFileName);

        WD::WDFileReader* pFile = new WD::WDFileReader(isoDataXmlPath);
        if (pFile != nullptr && !pFile->isBad())
        {
            pFile->readAll();
            WD::XMLDoc* pDoc = new WD::XMLDoc();
            if (pDoc != nullptr)
            {
                pDoc->parse<0>((char*)pFile->data());
                WD::XMLNode* pRoot = pDoc->first_node(XML_Root);
                if (pRoot != nullptr)
                {
                    auto pObj = _mgr.getStyleObject(ISOTableStyle::S_SYHG);
                    if (pObj != nullptr)
                    {
                        auto pSYHGObj = dynamic_cast<TableStyleSYHG*>( pObj );
                        if (pSYHGObj != nullptr)
                        {
                            auto pISONode = pRoot->first_node(XML_ISO);
                            if (pISONode != nullptr)
                            {
                                pSYHGObj->rightfulOwner = GetXMLItemValue(*pISONode, XML_ISO_RightfulOwner);
                                pSYHGObj->projectName   = GetXMLItemValue(*pISONode, XML_ISO_ProjectName);
                                pSYHGObj->areaName      = GetXMLItemValue(*pISONode, XML_ISO_AreaName);
                                pSYHGObj->projectFileNumber = GetXMLItemValue(*pISONode, XML_ISO_ProjectFileNumber);
                                pSYHGObj->stage         = GetXMLItemValue(*pISONode, XML_ISO_Stage);
                                pSYHGObj->edition       = GetXMLItemValue(*pISONode, XML_ISO_Edition);
                                auto pNotes = pISONode->first_node(XML_ISO_Notes);
                                if (pNotes != nullptr)
                                    pSYHGObj->notes     = GetXMLItemsValue(*pNotes, XML_ISO_Note);
                            }
                        }
                    }
                    auto pIndexNode = pRoot->first_node(XML_Index);
                    if (pIndexNode != nullptr)
                    {
                        _indexTableInfo.tableName = GetXMLItemValue(*pIndexNode, XML_Index_TableName);
                        _indexTableInfo.companyName = GetXMLItemValue(*pIndexNode, XML_Index_CompanyName);
                        _indexTableInfo.projectFileNumber   = GetXMLItemValue(*pIndexNode, XML_Index_ProjectFileNumber);
                        _indexTableInfo.documentNumber  = GetXMLItemValue(*pIndexNode, XML_Index_DocumentNumber);
                        _indexTableInfo.edition = GetXMLItemValue(*pIndexNode, XML_Index_Edition);
                        _indexTableInfo.engineeringName = GetXMLItemValue(*pIndexNode, XML_Index_EngineeringName);
                        _indexTableInfo.unitName    = GetXMLItemValue(*pIndexNode, XML_Index_UnitName);
                        _indexTableInfo.ownerFileNumber = GetXMLItemValue(*pIndexNode, XML_Index_OwnerFileNumber);
                        _indexTableInfo.footer  = GetXMLItemValue(*pIndexNode, XML_Index_Footer);
                    }
                }
                delete pDoc;
                pDoc = nullptr;
            }
            delete pFile;
            pFile = nullptr;
        }
    }
}
void ISODataConfigDialog::saveXML()
{
    auto projectDir = _core.project().projectDataDir();
    if (!projectDir.empty())
    {
        char isoDataXmlPath[1024] = { 0 };
        sprintf(isoDataXmlPath, "%s/%s", projectDir.c_str(), IsoDataConfigXMLFileName);

        WD::XMLDoc* pDoc = new WD::XMLDoc();
        if (pDoc != nullptr)
        {
            pDoc->append_node(pDoc->allocate_node(rapidxml::node_pi, "xml version='1.0' encoding='utf-8'"));
            WD::XMLNode* pRoot = pDoc->allocate_node(rapidxml::node_element, XML_Root);
            if (pRoot != nullptr)
            {
                pDoc->append_node(pRoot);

                auto pObj = _mgr.getStyleObject(ISOTableStyle::S_SYHG);
                if (pObj != nullptr)
                {
                    auto pSYHGObj = dynamic_cast<TableStyleSYHG*>( pObj );
                    if (pSYHGObj != nullptr)
                    {
                        auto pISONode = pDoc->allocate_node(rapidxml::node_element, XML_ISO);
                        if (pISONode != nullptr)
                        {
                            AddXMLItem(*pDoc, *pISONode, XML_ISO_RightfulOwner,     pSYHGObj->rightfulOwner);
                            AddXMLItem(*pDoc, *pISONode, XML_ISO_ProjectName,       pSYHGObj->projectName);
                            AddXMLItem(*pDoc, *pISONode, XML_ISO_AreaName,          pSYHGObj->areaName);
                            AddXMLItem(*pDoc, *pISONode, XML_ISO_ProjectFileNumber, pSYHGObj->projectFileNumber);
                            AddXMLItem(*pDoc, *pISONode, XML_ISO_Stage,             pSYHGObj->stage);
                            AddXMLItem(*pDoc, *pISONode, XML_ISO_Edition,           pSYHGObj->edition);
                            auto pNotes = pDoc->allocate_node(rapidxml::node_element, XML_ISO_Notes);
                            if (pNotes != nullptr)
                            {
                                AddXMLItems(*pDoc, *pNotes, XML_ISO_Note, pSYHGObj->notes);
                                pISONode->append_node(pNotes);
                            }
                            pRoot->append_node(pISONode);
                        }
                    }
                }
                auto pIndexNode = pDoc->allocate_node(rapidxml::node_element, XML_Index);
                if (pIndexNode != nullptr)
                {
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_TableName,         _indexTableInfo.tableName);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_CompanyName,       _indexTableInfo.companyName);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_ProjectFileNumber, _indexTableInfo.projectFileNumber);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_DocumentNumber,    _indexTableInfo.documentNumber);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_Edition,           _indexTableInfo.edition);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_EngineeringName,   _indexTableInfo.engineeringName);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_UnitName,          _indexTableInfo.unitName);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_OwnerFileNumber,   _indexTableInfo.ownerFileNumber);
                    AddXMLItem(*pDoc, *pIndexNode, XML_Index_Footer,            _indexTableInfo.footer);

                    pRoot->append_node(pIndexNode);
                }
            }

            std::string outString;
            rapidxml::print(std::back_inserter(outString), *pDoc, 0);
            auto pFile = fopen(isoDataXmlPath, "w");
            if (pFile != nullptr)
            {
                if (0 == fwrite(outString.c_str(), outString.size(), 1, pFile))
                    assert(false);
                fclose(pFile);
            }
            delete pDoc;
            pDoc = nullptr;
        }
    }
}

void ISODataConfigDialog::updateData()
{
    auto pObj = _mgr.getStyleObject(ISOTableStyle::S_SYHG);
    if (pObj != nullptr)
    {
        auto pSYHGObj = dynamic_cast<TableStyleSYHG*>(pObj);
        if (pSYHGObj != nullptr)
        {
            ui.lineEditProjectName->setText(QString::fromUtf8(pSYHGObj->projectName.c_str()));
            ui.lineEditAreaName->setText(QString::fromUtf8(pSYHGObj->areaName.c_str()));
            ui.lineEditProjectFileNumber->setText(QString::fromUtf8(pSYHGObj->projectFileNumber.c_str()));
            ui.lineEditStage->setText(QString::fromUtf8(pSYHGObj->stage.c_str()));
            ui.lineEditEdition->setText(QString::fromUtf8(pSYHGObj->edition.c_str()));
            ui.textEditAnnotations->clear();
            for (auto& each : pSYHGObj->notes)
                ui.textEditAnnotations->append(QString::fromUtf8(each.c_str()));

        }
    }
    ui.lineEditProjectFileNumber_2->setText(_indexTableInfo.projectFileNumber.c_str());
    ui.lineEditDocumentNumber->setText(_indexTableInfo.documentNumber.c_str());
    ui.lineEditEdition_2->setText(_indexTableInfo.edition.c_str());
    ui.lineEditEngineeringName->setText(_indexTableInfo.engineeringName.c_str());
    ui.lineEditUnitName->setText(_indexTableInfo.unitName.c_str());
    ui.lineEditFooter->setText(_indexTableInfo.footer.c_str());
}
void ISODataConfigDialog::retranslateUi()
{
    Trs("ISODataConfigDialog"
        , static_cast<QDialog*>(this)
        , ui.labelProjectName
        , ui.labelAreaName
        , ui.labelProjectFileNumber
        , ui.labelStage
        , ui.labelEdition
        , ui.labelAnnotations

        , ui.labelProjectFileNumber_2
        , ui.labelDocumentNumber
        , ui.labelEdition_2
        , ui.labelEngineeringName
        , ui.labelUnitName
        , ui.labelFooter

        , ui.tabWidget
        , ui.pushButtonApply
        , ui.pushButtonDismiss
    );
}