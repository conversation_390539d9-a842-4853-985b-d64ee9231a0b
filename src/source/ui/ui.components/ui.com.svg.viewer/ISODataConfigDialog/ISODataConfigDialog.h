#pragma once

#include <QDialog>
#include "ui_ISODataConfigDialog.h"
#include "core/math/Math.hpp"
#include "../ISOTableStyle/TableStyleMgr.h"
#include "../IsoIndexTable.h"
#include "core/WDCore.h"

class ISODataConfigDialog : public QDialog
{
    Q_OBJECT
public:
    ISODataConfigDialog(WD::WDCore& core
        , WD::TableStyleMgr& mgr
        , QWidget *parent = Q_NULLPTR);
    ~ISODataConfigDialog();
public:
    const WD::IsoIndexTable::IndexTableInfo& indexTableInfo() const
    {
        return _indexTableInfo;
    }
protected slots:
    void slotQPushButtonApplyClicked();
    void slotQPushButtonDismissClicked();
protected:
    virtual void showEvent(QShowEvent*) override;
    virtual void hideEvent(QHideEvent*) override;
private:
    void loadXML();
    void saveXML();
private:
    void updateData();
    void retranslateUi();
protected:
    static constexpr const char* IsoDataConfigXMLFileName = "ISODataConfig.xml";
    Ui::ISODataConfigDialog ui;
    WD::TableStyleMgr& _mgr;
    WD::IsoIndexTable::IndexTableInfo _indexTableInfo;
    WD::WDCore& _core;
};