#pragma once

#include    <QWidget>
#include    "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    "core/WDCore.h"

#include    "ISOMainWindow.h"
#include    "MBDMainWindow.h"

#include    "ISOTableCommon/ISOTableWidget.h"
#include    "ISOSettingWidgets/ISOFrameSettingDialog.h"
#include    "ISODataConfigDialog/ISODataConfigDialog.h"
#include    "ISOSettingWidgets/ISOPlotAreaSettingDialog.h"
#include    "ISOSettingWidgets/ISOPlotAreaEditDialog.h"
#include    "ISOSettingWidgets/ISOMaterialListSettingDialog.h"
#include    "ISOSettingWidgets/ISONotesAreaSettingDialog.h"
#include    "ISOSettingWidgets/ISOMaterialListEditDialog.h"
#include    "ISOSettingWidgets/ISONotesAreaEditDialog.h"
#include    "ISOSettingWidgets/ISOTemplateSettingDialog.h"
#include    "ISOSettingWidgets/ISOTemplateEditDialog.h"
#include    "PipeComsMtoSwitch/PipeComsMtoSwitchDialog.h"
#include    "ISOPaper.h"
#include    "ISOTableStyle/TableStyleMgr.h"

#include    "ISOTuLine.h"
#include    "FigureLegends/ISOFigureLegendsMgr.h"
#include    "BatchDrawIsoDialog.h"
#include    "GenInsDxfDialog.h"

WD_NAMESPACE_USE

class   UIComSvgViewer:  public IUiComponent
{
private:
    WDCore& _app;
    // 默认的图纸对象
    ISOPaperMgr _mgr;
    // 设置图框设置窗口
    ISOFrameSettingDialog* _frameSettingDialog;
    //// 绘图区设置窗口
    //ISOPlotAreaSettingDialog* _plotAreaSettingDialog;
    //// 绘图区编辑窗口
    //ISOPlotAreaEditDialog* _plotAreaEditDialog;
    //// 材料表设置窗口
    //ISOMaterialListSettingDialog* _materialListSettingDialog;
    //// 材料表编辑窗口
    //ISOMaterialListEditDialog* _materialListEditDialog;
    //// 图纸说明区设置窗口
    //ISONotesAreaSettingDialog* _notesAreaSettingDialog;
    //// 图纸说明区编辑窗口
    //ISONotesAreaEditDialog* _notesAreaEditDialog;
    //// 模板设置窗口
    //ISOTemplateSettingDialog* _templateSettingDialog;
    //// 模板编辑窗口
    //ISOTemplateEditDialog* _templateEditDialog;
    // 显示ISO数据的主窗口
    ISOMainWindow* _isoMainWindow;
    // 显示MBD数据的主窗口
    MBDMainWindow* _mbdMainWindow;
    // 设置数据窗口
    ISODataConfigDialog* _dataConfigDialog;
    // 表格风格管理
    TableStyleMgr _styleMgr;
    // 风格类型
    ISOTableStyle _style = ISOTableStyle::S_XB;
    //图例管理
    ISOFigureLegendsMgr _figureLegendsmgr;
    // 批量出图界面
    BatchDrawIsoDialog* _batchDrawIsoDialog;
    ////管道出dxf安装图界面
    //PipeDrawInsDxfDialog* _pipeDrawInsDxf;
    GenInsDxfDialog* _genInsDialog;
    // 管件材料开关
    PipeComsMtoSwitchDialog* _pPipeComsMtoSwitchDialog;
public:
    UIComSvgViewer(IMainWindow& mainWindow, const UiComponentAttributes& attrs);
    ~UIComSvgViewer();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
public:
    /**
     * @brief 生成ISO的SVG数据
     * @param paper 图纸对象：注意传入时图纸的genType和size
     * @param branchNode 分支节点
     * @param svg 
     * @param paperIdx 
     * @param paperCnt 
     * @param drawingNumber 
     * @param paperInfos 
     */
    void genISOSvgData(ISOPaper& paper
        , WD::WDNode& branchNode
        , WDSvgXmlCreator& svg
        , const uint& paperIdx = 1
        , const uint& paperCnt = 1
        , const std::string& drawingNumber = std::string()
        , const std::unordered_map<std::string, std::string>& paperInfos = std::unordered_map<std::string, std::string>());
    /**
     * @brief  生成ISO的DXF数据
     * @param paper 图纸对象：注意传入时图纸的genType和size
     * @param branchNode 分支节点
     * @param svg 
     * @param inputPath 数据来源文件夹，可以空
     * @param outPath  输出目标路径，需要精确到xxx.dxf
     * @param drawingNumber 
     * @param paperIdx 
     * @param paperCnt 
     */
    void genISODxfData(ISOPaper& paper
        , WD::WDNode& branchNode
        , WDSvgXmlCreator& svg
        , const QString& inputPath
        , const QString& outPath
        , const std::string& drawingNumber = std::string()
        , const uint& paperIdx = 1
        , const uint& paperCnt = 1);
    /**
     * @brief 生成ISO的Dxf数据(单pipe或者多pipe)
    */
    void genISODxfData(ISOPaper& paper
        , std::vector<WD::WDNode::SharedPtr> nodes
        , WDSvgXmlCreator& svg
        , QString inputPath
        , QString outPath
        , const uint& paperIdx = 1
        , const uint& paperCnt = 1);
    /**
     * @brief 生成MBD的SVG数据
    */
    std::string genMBDSvgData(ISOPaper& paper
        , WD::WDNode& branchNode
        , MBDDIMPainter& painter
        , const uint& paperIdx = 1
        , const uint& paperCnt = 1
        , ISODIMObjectMgr* pOutDimObjectMgr = nullptr);
    // 绘制坐标系朝向箭头
    void drawCoordArrow(WDAbstractPainter2D& painter, ISOPaper& paper);
private:
    /**
     * @brief 绘制
     * @param paper 图纸对象
     * @param painter2D 屏幕坐标对象绘制
    */
    void drawISOSVG(std::vector<TuLine>& lines, ISOPolttingArea& paper) const;
    /**
     * @brief 绘制
     * @param paper 图纸对象
     * @param painter2D 屏幕坐标对象绘制
    */
    void drawISODxf(std::vector<TuLine>& lines,ISOPolttingArea& paper
        , DxfPainterInterface& interface
        , DependJsonMgr* mgr = nullptr
        ) const;
};
