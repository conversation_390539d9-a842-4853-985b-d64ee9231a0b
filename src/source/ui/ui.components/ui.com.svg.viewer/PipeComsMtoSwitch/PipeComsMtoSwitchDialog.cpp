#include    "PipeComsMtoSwitchDialog.h"
#include    "core/nodeTree/WDNodeTree.h"
#include    "core/message/WDMessage.h"
#include    "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include    "core/undoRedo/WDUndoStack.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "../../wizDesignerApp/UiInterface/UiTranslate.h"

PipeComsMtoSwitchDialog::PipeComsMtoSwitchDialog(WD::WDCore& core
, QWidget *parent)
    : QDialog(parent)
    , _core(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    _pCurrentList = nullptr;
    // 初始化添加菜单
    _addMenuBar = new QMenuBar(this);
    // 显示边框
    _addMenuBar->setStyleSheet("QMenuBar{border: 1px solid gray;}");
    QMenu* addMenu = _addMenuBar->addMenu("CE");
    QAction* ceAct = new QAction("CurrNode", addMenu);
    connect(ceAct, &QAction::triggered, this, &PipeComsMtoSwitchDialog::slotActionCurrNodeTriggered);
    QAction* listAct = new QAction("CurrList", addMenu);
    connect(listAct, &QAction::triggered, this, &PipeComsMtoSwitchDialog::slotActionCurrListTriggered);
    addMenu->addAction(ceAct);
    addMenu->addAction(listAct);
    ui.horizontalLayoutCE->addWidget(_addMenuBar);
    ui.horizontalLayoutCE->setStretch(0, 0);
    ui.horizontalLayoutCE->setStretch(1, 1);
    ui.horizontalLayoutCE->setStretch(2, 0);

    this->retranslateUi();
    ui.lineEditCE->setReadOnly(true);

    connect(ui.pushButtonAdd,       &QPushButton::clicked, this, &PipeComsMtoSwitchDialog::slotPushButtonAddClicked);
    connect(ui.pushButtonCancle,    &QPushButton::clicked, this, &PipeComsMtoSwitchDialog::slotPushButtonCancleClicked);
    connect(ui.pushButtonClear,     &QPushButton::clicked, this, &PipeComsMtoSwitchDialog::slotPushButtonClearClicked);
    connect(ui.pushButtonRemove,    &QPushButton::clicked, this, &PipeComsMtoSwitchDialog::slotPushButtonRemoveClicked);

    connect(ui.checkBoxMtoSwitch,   &QCheckBox::toggled, this, &PipeComsMtoSwitchDialog::slotCheckBoxMtoSwitchToggled);

    _allPipeComTypes = {"ELBO", "BEND", "TEE", "VALV", "REDU", "FLAN", "CROS", "GASK"
        , "DUCT", "VENT", "FTUB", "SHU", "COUP", "CLOS", "OLET", "LJSE", "CAP", "FBLI", "VTWA"
        , "VFWA", "TRAP", "FILT", "WELD", "PCOM", "UNIO", "INST", "ATTA", "TAPE", "TRNS", "STRT"
        , "OFST", "PLEN", "PLAT", "THRE", "BRCO", "MESH", "GRIL", "COWL", "DAMP", "HFAN", "SILE"
        , "BATT", "AHU", "STIF", "HACC", "HSAD", "IDAM", "TP", "SPLR", "SKIR", "FLEX", "PTAP"};

    for (auto& eachType : _allPipeComTypes)
    {
        ui.comboBox->addItem(QString::fromUtf8( eachType.c_str() ), QString::fromUtf8( eachType.c_str() ));
    }
    _currType = CT_CurrNode;
}

PipeComsMtoSwitchDialog::~PipeComsMtoSwitchDialog()
{
    _pCurrentList = nullptr;
    if(_addMenuBar != nullptr)
    {
        delete _addMenuBar;
        _addMenuBar = nullptr;
    }
}

std::unordered_set<std::string> PipeComsMtoSwitchDialog::getAllSelectTypes()
{
    std::unordered_set<std::string> allSTypes;
    for (int i = 0; i < ui.listWidget->count(); ++i)
    {
        auto item = ui.listWidget->item(i);
        if (item == nullptr)
            continue;

        auto type = item->data(Qt::UserRole).toString().toUtf8().toStdString();
        if (allSTypes.find(type) != allSTypes.end())
        {
            assert(false);
            continue;
        }
        allSTypes.emplace(type);
    }
    return allSTypes;
}

std::string PipeComsMtoSwitchDialog::getTargetMtocomponentValue()
{
    if (ui.checkBoxMtoSwitch->isChecked())
        return "on";
    return "off";
}
void PipeComsMtoSwitchDialog::slotPushButtonAddClicked()
{
    auto typeQstr = ui.comboBox->currentData().toString();
    auto type = typeQstr.toUtf8().toStdString();
    // 判断类型是否是管件类型
    if (_allPipeComTypes.find(type) == _allPipeComTypes.end())
    {
        WD_WARN_T("PipeComsMtoSwitchDialog", "Type is Error!");
        return;
    }
    // 判断类型是否已经存在在列表中
    auto sTypes = this->getAllSelectTypes();
    if (sTypes.find(type) != sTypes.end())
    {
        WD_WARN_T("PipeComsMtoSwitchDialog", "Type is Exist!");
        return;
    }
    auto item = new QListWidgetItem(typeQstr);
    item->setData(Qt::UserRole, typeQstr);
    ui.listWidget->addItem(item);
}

void PipeComsMtoSwitchDialog::slotPushButtonCancleClicked()
{
    this->reject();
}

void PipeComsMtoSwitchDialog::slotPushButtonClearClicked()
{
    if (ui.listWidget->count() == 0)
        return;
    if (WD_QUESTION_T("PipeComsMtoSwitchDialog", "Sure to clear all items?") != 0)
        return;

    ui.listWidget->clear();
}

void PipeComsMtoSwitchDialog::slotPushButtonRemoveClicked()
{
    if (ui.listWidget->currentItem() == nullptr)
        return;
    if (WD_QUESTION_T("PipeComsMtoSwitchDialog", "Sure to delete current item?") != 0)
        return;

    auto item = ui.listWidget->takeItem(ui.listWidget->currentRow());
    if (item != nullptr)
    {
        delete item;
        item = nullptr;
    }
}

void PipeComsMtoSwitchDialog::slotCheckBoxMtoSwitchToggled()
{
    std::vector<WD::WDUndoCommand*> cmds;
    auto sTypes = this->getAllSelectTypes();
    auto value = this->getTargetMtocomponentValue();

    switch (_currType)
    {
    case PipeComsMtoSwitchDialog::CT_CurrNode:
        {
            auto pCurrentNode = _currentNode.lock();
            if (pCurrentNode == nullptr)
            {
                WD_WARN_T("PipeComsMtoSwitchDialog", "Current Node is Empty!");
                return;
            }

            auto& mgr = _core.getBMDesign();
            cmds.reserve(pCurrentNode->childCount());
            WD::WDNode::RecursionHelpter(*pCurrentNode, [&sTypes, &cmds, &mgr, &value](WD::WDNode& node)
            {
                for (auto& pChild : node.children())
                {
                    if (pChild == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pChild))
                        continue;
                    if (sTypes.find(std::string(pChild->type().data())) != sTypes.end())
                    {
                        cmds.emplace_back(mgr.MakeAttributeSetCommand(pChild, "Mtocomponent", WD::WDBMAttrValue(WD::WDBMWord(value.c_str()))));
                    }
                }
            });
        }
        break;
    case PipeComsMtoSwitchDialog::CT_CurrList:
        {
            if (_pCurrentList == nullptr)
            {
                WD_WARN_T("PipeComsMtoSwitchDialog", "Current List is Null!");
                return;
            }
            const auto& nodes = _pCurrentList->nodes();
            if (nodes.empty())
            {
                WD_WARN_T("PipeComsMtoSwitchDialog", "Current List is Empty!");
                return;
            }
            auto& mgr = _core.getBMDesign();
            cmds.reserve(nodes.size() * 10);

            for (auto& pNode : nodes)
            {
                if (pNode == nullptr)
                    continue;

                WD::WDNode::RecursionHelpter(*pNode, [&sTypes, &cmds, &mgr, &value](WD::WDNode& node)
                {
                    for (auto& pChild : node.children())
                    {
                        if (pChild == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pChild))
                            continue;
                        if (sTypes.find(std::string(pChild->type().data())) != sTypes.end())
                        {
                            cmds.emplace_back(mgr.MakeAttributeSetCommand(pChild, "Mtocomponent", WD::WDBMAttrValue(WD::WDBMWord(value.c_str()))));
                        }
                    }
                });
            }
        }
        break;
    default:
        break;
    }

    _core.undoStack().beginMarco("PipeComsMtoSwitch");
    for (auto& pCmd : cmds)
    {
        if (pCmd == nullptr)
            continue;
        _core.undoStack().push(pCmd);
    }
    _core.undoStack().endMarco();
}

void PipeComsMtoSwitchDialog::slotActionCurrNodeTriggered()
{
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("PipeComsMtoSwitchDialog", "Current Node is Empty!");
        return;
    }
    _currentNode = pCurrentNode;
    _pCurrentList = nullptr;
    _currType = CT_CurrNode;
    ui.lineEditCE->setText(QString::fromUtf8( pCurrentNode->name().c_str() ));
}

void PipeComsMtoSwitchDialog::slotActionCurrListTriggered()
{
    auto pList = _core.nodeListMgr().current();
    if (pList == nullptr)
    {
        WD_ERROR_T("PipeComsMtoSwitchDialog", "Current List is Null!");
        return;
    }
    _currentNode.reset();
    _pCurrentList = pList;
    _currType = CT_CurrList;
    ui.lineEditCE->setText(QString::fromUtf8(_pCurrentList->name().c_str()));
}

void PipeComsMtoSwitchDialog::retranslateUi()
{
    Trs("PipeComsMtoSwitchDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonAdd
        , ui.pushButtonCancle
        , ui.pushButtonClear
        , ui.pushButtonRemove

        , ui.labelCE
        , ui.labelMtoSwitch
        , ui.labelType

        , ui.groupBox

        , _addMenuBar
    );
}