#pragma once

#include <QDialog>
#include "ui_PipeComsMtoSwitchDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include <QMenuBar>
#include "core/nodeTree/WDNodeList.h"

class PipeComsMtoSwitchDialog : public QDialog
{
    Q_OBJECT
public:
    enum class MtoComponentType
    {
        MCT_On,
        MCT_Off,
        MCT_Unset,
    };
public:
    PipeComsMtoSwitchDialog(WD::WDCore& core
        , QWidget *parent = Q_NULLPTR);
    ~PipeComsMtoSwitchDialog();
private:
    std::unordered_set<std::string> getAllSelectTypes();
    std::string getTargetMtocomponentValue();
private:
    void slotPushButtonAddClicked();
    void slotPushButtonCancleClicked();
    void slotPushButtonClearClicked();
    void slotPushButtonRemoveClicked();

    void slotCheckBoxMtoSwitchToggled();

    void slotActionCurrNodeTriggered();
    void slotActionCurrListTriggered();
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::PipeComsMtoSwitchDialog ui;
    WD::WDCore& _core;
    
    enum CurrentType
    {
        CT_CurrNode,
        CT_CurrList,
    };
    WD::WDNode::WeakPtr _currentNode;
    WD::NodeList*       _pCurrentList;
    CurrentType         _currType;

    std::unordered_set<std::string> _allPipeComTypes;
    QMenuBar* _addMenuBar;
};