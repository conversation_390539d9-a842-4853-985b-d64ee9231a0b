<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PipeComsMtoSwitchDialog</class>
 <widget class="QDialog" name="PipeComsMtoSwitchDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>332</width>
    <height>307</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>PipeComsMtoSwitch</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayoutCE" stretch="0,1">
     <item>
      <widget class="QLabel" name="labelCE">
       <property name="text">
        <string>CE</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditCE"/>
     </item>
    </layout>
   </item>
   <item row="1" column="0" alignment="Qt::AlignLeft">
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <widget class="QLabel" name="labelMtoSwitch">
      <property name="text">
       <string>MtoSwitch</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="checkBoxMtoSwitch">
      <property name="text">
       <string/>
      </property>
     </widget>
    </widget>
   </item>
   <item row="2" column="0" colspan="2">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>List</string>
     </property>
     <layout class="QGridLayout" name="gridLayout" columnstretch="0,1,0">
      <item row="3" column="2">
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>156</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="comboBox"/>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="labelType">
        <property name="text">
         <string>Type</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QPushButton" name="pushButtonAdd">
        <property name="focusPolicy">
         <enum>Qt::ClickFocus</enum>
        </property>
        <property name="text">
         <string>Add</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0" rowspan="3" colspan="2">
       <widget class="QListWidget" name="listWidget"/>
      </item>
      <item row="1" column="2">
       <widget class="QPushButton" name="pushButtonRemove">
        <property name="focusPolicy">
         <enum>Qt::ClickFocus</enum>
        </property>
        <property name="text">
         <string>Remove</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QPushButton" name="pushButtonClear">
        <property name="focusPolicy">
         <enum>Qt::ClickFocus</enum>
        </property>
        <property name="text">
         <string>Clear</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="1" alignment="Qt::AlignRight">
    <widget class="QPushButton" name="pushButtonCancle">
     <property name="focusPolicy">
      <enum>Qt::ClickFocus</enum>
     </property>
     <property name="text">
      <string>Cancle</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
