#include "ISOTuLine.h"
#include "ISOWeldQueryTable.h"
#include <QFileDialog>
#include "core/WDCore.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/viewer/dimension/WDDIMQLeader.h"
#include "FigureLegends/ISOFigureLegendsMgr.h"
#include <cmath>
#include "log/WDLoggerPort.h"
#include "libdxfrw.h"
#include "DRW/dx_iface.h"
#include "DRW/dx_data.h"
#include "ISODIMPainterDxf.h"


WD_NAMESPACE_BEGIN
//使用ENU坐标系显示，
//X=E，Y=N，-X=W，-Y=S，Z轴用EL
static bool UseEnuAxis = true;
//阀门手轮朝向生成朝向字符串的误差值
static double ResidualAngle = 0.001;
//阀门手轮朝向判断向量是否共线的误差值
static double Residual = 0.0001;

/**
 * @brief 判断节点是否需要绘制法兰
 * 三通,弯头和直管段的skey以 FL 结尾时需要绘制法兰
*/
static bool IsNodeNeedPaintFlan(WD::WDNode& node)
{
    auto pSprefNode = node.getAttribute("Spref").toNodeRef().refNode();
    if (pSprefNode == nullptr)
        return false;
    auto pCatrefNode = pSprefNode->getAttribute("Catref").toNodeRef().refNode();
    if (pCatrefNode == nullptr)
        return false;
    auto pDtrefNode = pCatrefNode->getAttribute("Dtref").toNodeRef().refNode();
    if (pDtrefNode == nullptr)
        return false;
    auto pDetRefNode = pSprefNode->getAttribute("Detref").toNodeRef().refNode();
    if (pDetRefNode == nullptr)
        return false;
    auto skey = pDetRefNode->getAttribute("Skey").toString();
    auto size = skey.size();
    
    return size >= 2 && (skey[size - 2] == 'F' || skey[size - 2] == 'f') && (skey[size - 1] == 'L' || skey[size - 1] == 'l');
}

//沿管道符号的避让
class PipeIdentifierAvoid
{
public:
    /**
     * @brief 校验方法，如果新的点满足结果，则需要返回true
     */
    using FCheck = std::function<bool(const DVec3& newPos)>;
public:
    PipeIdentifierAvoid(double gridHeight, double gridWidth, int height = 1, int widthLeft = 50, int widthRight = 50)
    {
        if (widthLeft < 0)
        {
            widthLeft = 0;
        }
        if (widthRight < 0)
        {
            widthRight = 0;
        }
        const auto frontDir = DVec3::AxisY();
        const auto rightDir = DVec3::AxisX();
        // 生成方格点
        _gridPts.clear();
        _gridPts.reserve(height * (widthRight + widthLeft));
        for (size_t i = 0; i < height; ++i)
        {
            auto depthOff = (static_cast<double>(i) * gridHeight) * _frontDir;
            for (size_t j = 0; j < widthLeft; ++j)
            {
                auto leftOff = -(static_cast<double>(j) * gridWidth) * _rightDir;
                _gridPts.emplace_back(depthOff + leftOff);
            }
        }
        for (size_t i = 0; i < height; ++i)
        {
            auto depthOff = (static_cast<double>(i) * gridHeight) * _frontDir;
            for (size_t j = 0; j < widthRight; ++j)
            {
                auto rightOff = (static_cast<double>(j) * gridWidth) * _rightDir;
                _gridPts.emplace_back(depthOff + rightOff);
            }
        }
        // 按照最近距离排序
        std::sort(_gridPts.begin(), _gridPts.end(), [this](const DVec3& left, const DVec3& right)
            {
                return left.lengthSq() < right.lengthSq();
            });
    }
    ~PipeIdentifierAvoid()
    {

    }
public:
    //重新分配格子
    void reAllocation(double gridHeight, double gridWidth, int height = 1, int widthLeft = 50, int widthRight = 50)
    {
        _frontDir = DVec3::AxisY();
        _rightDir = DVec3::AxisX();
        // 生成方格点
        _gridPts.clear();
        _gridPts.reserve(height * (widthRight + widthLeft));
        for (size_t i = 0; i < height; ++i)
        {
            auto depthOff = (static_cast<double>(i) * gridHeight) * _frontDir;
            for (size_t j = 0; j < widthLeft; ++j)
            {
                auto leftOff = -(static_cast<double>(j) * gridWidth) * _rightDir;
                _gridPts.push_back(depthOff + leftOff);
            }
        }
        for (size_t i = 0; i < height; ++i)
        {
            auto depthOff = (static_cast<double>(i) * gridHeight) * _frontDir;
            for (size_t j = 0; j < widthRight; ++j)
            {
                auto rightOff = (static_cast<double>(j) * gridWidth) * _rightDir;
                _gridPts.push_back(depthOff + rightOff);
            }
        }
        // 按照最近距离排序
        std::sort(_gridPts.begin(), _gridPts.end(), [this](const DVec3& left, const DVec3& right)
            {
                return left.lengthSq() < right.lengthSq();
            });
    }
    /**
     * @brief 初始化
     * @param srcPos 引线的原始位置(引线箭头指向的位置)
     * @param textSrcPos 文字的初始位置，表示文字未作避让时的位置
     * @param frontDir 避让方向Front
     * @param rightDir 避让方向Right
     * @param tOffset 每次避让的偏移量
     * @param depth 生成的方格深度
     * @param width 生成的方格宽度
     */
    void init(const DVec3& textSrcPos
        , const DVec3& frontDir, const DVec3& rightDir)
    {
        _textSrcPos = textSrcPos;
        _frontDir = frontDir;
        _rightDir = rightDir;
    }
    /**
     * @brief 尝试避让
     * @param func 校验方法，得到一个避让结果之后，使用校验方法来验证结果是否满足，如果满足，该方法应该返回true
     * @return 最终结果，返回true代表找到避让结果，返回false表示尝试了方格的所有点，仍然没有找到避让结果
     */
    bool tryTo(const FCheck& func)
    {
        // 不指定校验函数，证明默认通过校验
        if (!func)
            return true;
        // 构造变换矩阵
        DMat3 rsMat = DMat3::MakeRotationXY(_rightDir, _frontDir);
        DMat4 trsMat = DMat4(rsMat, _textSrcPos);
        // 依次校验每个点
        for (const auto& newPos : _gridPts)
        {
            const auto tNewPos = trsMat * newPos;
            if (func(tNewPos))
                return true;
        }
        // 校验结束，依旧没找到合适的位置, 先跳出
        return false;
    }
private:
    // 文字的初始位置，表示文字未作避让时的位置
    DVec3 _textSrcPos = DVec3::Zero();
    // 避让方向Front
    DVec3 _frontDir = DVec3::AxisY();
    // 避让方向Right
    DVec3 _rightDir = DVec3::AxisX();
    // 临时生成的方格点
    std::vector<DVec3> _gridPts;
};


// 输出坐标分量
void CoordOutPut(char* outBuf, size_t outBufSz, int value, const char* prefix)
{
    // 标高
    if (value > 0)
    {
        if (strcmp(prefix, "E") == 0)
        {
            sprintf_s(outBuf, outBufSz, "%s  %d", "E", Abs(value));
        }
        else if (strcmp(prefix, "N") == 0)
        {
            sprintf_s(outBuf, outBufSz, "%s  %d", "N", Abs(value));
        }
        else
        {
            sprintf_s(outBuf, outBufSz, "%s  +%d", prefix, value);
        }
    }
    else if (value < 0)
    {
        if (strcmp(prefix, "E") == 0)
        {
            sprintf_s(outBuf, outBufSz, "%s  %d", "W", Abs(value));
        }
        else if (strcmp(prefix, "N") == 0)
        {
            sprintf_s(outBuf, outBufSz, "%s  %d", "S", Abs(value));
        }
        else
        {
            sprintf_s(outBuf, outBufSz, "%s  -%d", prefix, Abs(value));
        }
    }
    else
        sprintf_s(outBuf, outBufSz, "%s %d", prefix, Abs(value));
};

/**
 * @brief 绘制管线的保温
*/
void DrawIspecLines(ISODrawContext& cxt
    , const std::vector<DVec3>& brokenLine, const WD::DVec3& rightDir)
{
    WDUnused(rightDir);
    // 
    if (brokenLine.size() < 2)
        return;
    std::vector<DVec3> tmpBrokenLine;
    tmpBrokenLine.reserve(brokenLine.size());
    tmpBrokenLine.push_back(brokenLine.front());
    for(size_t i = 0; i < brokenLine.size(); ++i)
    {
        if (DVec3::Distance(brokenLine[i], tmpBrokenLine.back()) >= NumLimits<float>::Epsilon)
            tmpBrokenLine.push_back(brokenLine[i]);
    }

    // 默认的虚线样式
    const auto& style = cxt.style.defDashLStyle;
    // 到管线中心的距离
    double dis = cxt.style.ispecDisToCen();
    //后面再做
    ////绘制保温的方向还是用tmpright，但是保温线到管线中心的距离使用标注方向来计算，因为投影方向会导致实际宽度不同，与承插符号接不上
    //dis = WD::DVec3::Distance(brokenLine.front() + rightDir * dis, brokenLine.front() - rightDir * dis)/2;

    std::vector<DVec3> brokenLineOut;
    brokenLineOut.reserve(tmpBrokenLine.size());
    std::vector<DVec3> brokenLineIn;
    brokenLineIn.reserve(tmpBrokenLine.size());

    for (size_t i = 1; i < tmpBrokenLine.size(); ++i)
    {
        const DVec3& pt0    = tmpBrokenLine[i - 1];
        const DVec3& pt1    = tmpBrokenLine[i];
        DVec3 tmpFront      = pt1 - pt0;
        if (tmpFront.lengthSq() <= 0.01)
            continue;
        DVec3 tmpRight = DVec3::Cross(tmpFront, cxt.uCvt.camera().frontDir()).normalized();

        if (brokenLineOut.empty())
        {
            brokenLineOut.push_back(pt0 + tmpRight * dis);
            brokenLineOut.push_back(pt1 + tmpRight * dis);
        }
        else
        {
            brokenLineOut.push_back(pt1 + tmpRight * dis);
        }

        if (brokenLineIn.empty())
        {
            brokenLineIn.push_back(pt0 - tmpRight * dis);
            brokenLineIn.push_back(pt1 - tmpRight * dis);
        }
        else
        {
            brokenLineIn.push_back(pt1 - tmpRight * dis);
        }
    }
    cxt.painter.drawBrokenLine(brokenLineOut, style);
    cxt.painter.drawBrokenLine(brokenLineIn, style);
    cxt.collision.addBrokenLine(brokenLineOut
        , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe), style);
    cxt.collision.addBrokenLine(brokenLineIn
        , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe), style);
    //cxt.collisionPipeLine.addBrokenLine(brokenLineOut, style);
    //cxt.collisionPipeLine.addBrokenLine(brokenLineIn, style);
}

/**
 * @brief 指定一个节点，并根据指定的祖先节点的业务数据类型，查找附带该类型数据的祖先节点
 *  如果祖先节点中没有附带该业务数据类型的节点，则返回nullptr
*/
WDNode::SharedPtr QueryParentWithType(WDNode& node, const std::string_view& type)
{
    WDNode::SharedPtr rNode = WDNode::ToShared(&node);
    while (rNode != nullptr)
    {
        if (rNode->isType(type))
            return rNode;
        else
            rNode = rNode->parent();
    }
    return nullptr;
}

/******** TuPosNode *********/
TuPosNode::TuPosNode(ISOFigureLegendsMgr& figuLegMgr, Type type, const DKeyPoint& aPt, const WDBMNodeRef& catRef)
    : TuNode(figuLegMgr)
    , _type(type)
    , _aPt(aPt)
    , _catRef(catRef)
{
}
DKeyPoint TuPosNode::sPt0() const
{
    return DKeyPoint(_aPt.position, DVec3::AxisZ());
}
DKeyPoint TuPosNode::sPtA() const
{
    return _aPt;
}
DKeyPoint TuPosNode::sPtL() const
{
    return DKeyPoint(_aPt.position, -_aPt.direction);
}
std::optional<DKeyPoint> TuPosNode::sPtF() const
{
    return std::nullopt;
}

std::string TuPosNode::cTypeA() const
{
    switch (_type)
    {
    case WD::TuPosNode::T_BranHPos:
        {
            return "";
        }
        break;
    case WD::TuPosNode::T_BranTPos:
        {
            auto pRefNode = _catRef.refNode();
            if (pRefNode == nullptr)
                return "";
            if (WDBMDPipeUtils::IsPipeComponent(*pRefNode))
            {
                auto pPt = pRefNode->keyPoint(pRefNode->getAttribute("Arrive").toInt());
                if (pPt == nullptr)
                    return "";
                return pPt->connType();
            }
            else if (pRefNode->isType("NOZZ"))
            {
                // 管嘴的连接点编号默认是1
                auto pPt = pRefNode->keyPoint(1);
                if (pPt == nullptr)
                    return "";
                return pPt->connType();
            }
            else if (pRefNode->isType("BRAN"))
            {
                return pRefNode->getAttribute("Hconnect").toString();
            }
            else
            {
                assert(false);
                return "";
            }
        }
        break;
    case WD::TuPosNode::T_BreakFrom:
        break;
    case WD::TuPosNode::T_BreakTo:
        break;
    default:
        break;
    }
    return "";
}
std::string TuPosNode::cTypeL() const
{
    switch (_type)
    {
    case WD::TuPosNode::T_BranHPos:
        {
            auto pRefNode = _catRef.refNode();
            if (pRefNode == nullptr)
                return "";
            if (WDBMDPipeUtils::IsPipeComponent(*pRefNode))
            {
                auto pPt = pRefNode->keyPoint(pRefNode->getAttribute("Leave").toInt());
                if (pPt == nullptr)
                    return "";
                return pPt->connType();
            }
            else if (pRefNode->isType("NOZZ"))
            {
                // 管嘴的连接点编号默认是1
                auto pPt = pRefNode->keyPoint(1);
                if (pPt == nullptr)
                    return "";
                return pPt->connType();
            }
            else if (pRefNode->isType("BRAN"))
            {
                return pRefNode->getAttribute("Tconnect").toString();
            }
            else
            {
                assert(false);
                return "";
            }
        }
        break;
    case WD::TuPosNode::T_BranTPos:
        {
            return "";
        }
        break;
    case WD::TuPosNode::T_BreakFrom:
        break;
    case WD::TuPosNode::T_BreakTo:
        break;
    default:
        break;
    }
    return "";
}
std::string TuPosNode::cTypeF() const
{
    return "";
}

std::string TuPosNode::mtotube() const
{
    return "";
}

DKeyPoint TuPosNode::update(ISODrawContext& cxt, const DKeyPoint& sKeyPt)
{
    WDUnused(cxt);

    _rPt0 = sKeyPt.position;
    return sKeyPt;
}
void TuPosNode::updateMBD()
{
    _rPt0 = _aPt.position;
}
void TuPosNode::drawDimInfo(ISODrawContext& cxt, const std::unordered_map<std::string, std::string>& paperInfos) const
{
    const auto& uCvt    = cxt.uCvt;
    const auto& fStyle  = cxt.style.defFStyle;

    const auto gPos         = sPt0().position;
    const IVec3 gPosLL      = IVec3(static_cast<int>(Round(gPos.x))
        , static_cast<int>(Round(gPos.y))
        , static_cast<int>(Round(gPos.z)));

    // 连接信息标注
    auto dimPos       = this->rPt0();
    auto dimTextPos   = dimPos;
    auto dimNumberPos = dimPos;
    switch (_type)
    {
    case WD::TuPosNode::T_BranHPos:
        dimTextPos = dimPos + this->sPtA().direction * uCvt.paperToWorld(20.0);
        dimNumberPos = dimPos + this->sPtA().direction * uCvt.paperToWorld(2.0);
        break;
    case WD::TuPosNode::T_BranTPos:
        dimTextPos = dimPos + this->sPtL().direction * uCvt.paperToWorld(20.0);
        dimNumberPos = dimPos + this->sPtL().direction * uCvt.paperToWorld(2.0);
        break;
    case WD::TuPosNode::T_BreakFrom:
        dimTextPos = dimPos + this->sPtA().direction * uCvt.paperToWorld(20.0);
        dimNumberPos = dimPos + this->sPtA().direction * uCvt.paperToWorld(2.0);
        break;
    case WD::TuPosNode::T_BreakTo:
        dimTextPos = dimPos + this->sPtL().direction * uCvt.paperToWorld(20.0);
        dimNumberPos = dimPos + this->sPtL().direction * uCvt.paperToWorld(2.0);
        break;
    default:
        break;
    }


    WDDIMQLeader::Texts texts;
    texts.reserve(20);
    WDDIMQLeader::Texts applyNumber;
    applyNumber.reserve(20);
    auto pRefNode = _catRef.refNode();
    if (pRefNode != nullptr)
    {
        bool isNozz = pRefNode->isType("NOZZ");
        //2024.12.28连接到TEE和OLET不能显示连接对象的名称，要显示连接对象所在的分支名称
        bool contToTe = pRefNode->isAnyOfType("TEE", "OLET");
        // 连接标识
        if (isNozz)
        {
            if (_applyNumberN)
            {
                char buf[1024] = { 0 };
                // 标号
                sprintf_s(buf, sizeof(buf), "%d", _applyNumberN.value());
                applyNumber.push_back({ buf, fStyle });
                // 添加
                cxt.dimGen.addDIMQLeader(dimPos, dimTextPos, applyNumber,-1 ,-1);
            }
            texts.push_back({ "CONT.TO", fStyle });
        }
        else
            texts.push_back({ "CONT.ON", fStyle });
        auto pTPrevNode = pPrevNode;
        while (pTPrevNode != nullptr)
        {
            if (pTPrevNode->isComNode())
            {
                break;
            }
            pTPrevNode = pTPrevNode->pPrevNode;
        }        
        auto pTNextNode = pNextNode;
        if (pTPrevNode == nullptr)
        {
            while (pTNextNode != nullptr)
            {
                if (pTNextNode->isComNode())
                {
                    break;
                }
                pTNextNode = pTNextNode->pNextNode;
            }
        }
        WDNode::SharedPtr pNode = nullptr;
        if (pTPrevNode != nullptr)
        {
            auto comNode = static_cast<TuComNode*>(pTPrevNode);
            if (comNode != nullptr && comNode->comNode() != nullptr)
            {
                pNode = comNode->comNode();
            }
        }
        else if (pTNextNode != nullptr)
        {
            auto comNode = static_cast<TuComNode*>(pTNextNode);
            if (comNode != nullptr && comNode->comNode() != nullptr)
            {
                pNode = comNode->comNode();
            }
        }
        //如果连接到的是其他管道下的分支，就标注管道号
        bool markPipe = false;
        // 连接对象名称
        if (contToTe && pRefNode->parent() != nullptr && pNode != nullptr)
        {
            if (!isSamePipe(pRefNode->parent(), pNode->parent()))
            {
                markPipe = true;
            }

        }
        else if (pRefNode != nullptr && pNode != nullptr)
        {
            if (!isSamePipe(pRefNode, pNode->parent()))
            {
                markPipe = true;
            }
        }

        if (markPipe)
        {
            if (contToTe)
            {
                auto pBran = pRefNode->parent();
                auto name = pRefNode->name();
                if (pBran != nullptr)
                {
                    name = pBran->name();
                    auto pPipe = pBran->parent();
                    if (pPipe != nullptr)
                        name = pPipe->name();
                }
                texts.push_back({ name, fStyle });
            }
            else
            {
                auto name = pRefNode->name();
                if (pRefNode->parent() != nullptr)
                {
                    name = pRefNode->parent()->name();
                }
                texts.push_back({ name, fStyle });
            }
        }
        else if (contToTe && pRefNode->parent() != nullptr)
        {
            auto name = pRefNode->parent()->name();
            if (!paperInfos.empty())
            {
                auto it = paperInfos.find(name);
                if (it != paperInfos.end())
                {
                    name = it->second;
                }
            }
            texts.push_back({ name, fStyle });
        }
        else if (pRefNode != nullptr)
        {
            auto name = pRefNode->name();
            if (!paperInfos.empty())
            {
                auto it = paperInfos.find(name);
                if (it != paperInfos.end())
                {
                    name = it->second;
                }
            }
            texts.push_back({ name, fStyle });
        }

        // 图纸号
        if (isNozz) //管嘴
        {
            // 获取到所属设备节点(EQUI)的图纸号
            auto pEQUINode = QueryParentWithType(*pRefNode, "EQUI");
            if (pEQUINode != nullptr)
            {
                auto rAttr = pEQUINode->getAttribute(cxt.style.attrNameDrawNumber);
                auto pAttrValue = rAttr.data<std::string>();
                if (pAttrValue != nullptr && !pAttrValue->empty())
                    texts.push_back({ *pAttrValue, fStyle });
            }
        }
        else // 管件或者分支
        {
            // 获取到所属分支节点(BRAN)的图纸号
            auto pBRANNode = QueryParentWithType(*pRefNode, "BRAN");
            if (pBRANNode != nullptr)
            {
                auto rAttr = pBRANNode->getAttribute(cxt.style.attrNameDrawNumber);
                auto pAttrValue = rAttr.data<std::string>();
                if (pAttrValue != nullptr && !pAttrValue->empty())
                    texts.push_back({ *pAttrValue, fStyle });
            }
        }
    }
    char buf[1024] = { 0 };
    if (UseEnuAxis)
    {
        // 坐标值 E
        CoordOutPut(buf, sizeof(buf), gPosLL.x, "E");
        texts.push_back({ buf, fStyle });
        // 坐标值 N
        CoordOutPut(buf, sizeof(buf), gPosLL.y, "N");
        texts.push_back({ buf, fStyle });
        // 坐标值 U
        CoordOutPut(buf, sizeof(buf), gPosLL.z, "EL");
        texts.push_back({ buf, fStyle });
    }
    else
    {
        // 坐标值 X
        CoordOutPut(buf, sizeof(buf), gPosLL.x, "X");
        texts.push_back({ buf, fStyle });
        // 坐标值 Y
        CoordOutPut(buf, sizeof(buf), gPosLL.y, "Y");
        texts.push_back({ buf, fStyle });
        // 坐标值 Z
        CoordOutPut(buf, sizeof(buf), gPosLL.z, "Z");
        texts.push_back({ buf, fStyle });
    }

    // 添加
    cxt.dimGen.addDIMQLeader(dimPos, dimTextPos, texts);
}
void TuPosNode::drawDimInfoMBD(ISODrawContext& cxt) const
{
    this->drawDimInfo(cxt);
}

/******** TuComNode *********/
TuComNode::TuComNode(ISOFigureLegendsMgr& figuLegMgr, WDNode::SharedPtr pComNode)
    : TuNode(figuLegMgr)
    , _pComNode(pComNode)
{
}

DKeyPoint TuComNode::sPt0() const
{
    if (_pComNode == nullptr)
        return DKeyPoint();
    return DKeyPoint(_pComNode->globalTranslation(), DVec3::AxisZ());
}
DKeyPoint TuComNode::sPtA() const
{
    if (_pComNode == nullptr)
        return DKeyPoint();
    auto pPt = _pComNode->keyPoint(_pComNode->getAttribute("Arrive").toInt());
    if (pPt == nullptr)
    {
        assert(false);
        return DKeyPoint();
    }
    return pPt->transformed(_pComNode->globalTransform());
}
DKeyPoint TuComNode::sPtL() const
{
    if (_pComNode == nullptr)
        return DKeyPoint();
    auto pPt = _pComNode->keyPoint(_pComNode->getAttribute("Leave").toInt());
    if (pPt == nullptr)
    {
        assert(false);
        return DKeyPoint();
    }
    return pPt->transformed(_pComNode->globalTransform());
}
std::optional<DKeyPoint> TuComNode::sPtF() const
{
    if (_pComNode == nullptr)
        return std::nullopt;
    // 三通, 支管座 具有连接点
    auto pForkPoint = _pComNode->keyPoint(WDBMDPipeUtils::Fork(*_pComNode));
    if (_pComNode->isAnyOfType("TEE", "OLET") && pForkPoint != nullptr)
    {
        return pForkPoint->transformed(_pComNode->globalTransform());
    }

    // !其他管件的连接点待处理

    return std::nullopt;
}

std::string TuComNode::cTypeA() const
{
    if (_pComNode == nullptr)
        return "";
    auto pPt = _pComNode->keyPoint(_pComNode->getAttribute("Arrive").toInt());
    if (pPt == nullptr)
    {
        assert(false);
        return "";
    }
    return pPt->connType();
}
std::string TuComNode::cTypeL() const
{
    if (_pComNode == nullptr)
        return "";
    auto pPt = _pComNode->keyPoint(_pComNode->getAttribute("Leave").toInt());
    if (pPt == nullptr)
    {
        assert(false);
        return "";
    }
    return pPt->connType();
}
std::string TuComNode::cTypeF() const
{
    if (_pComNode == nullptr)
        return "";
    // 三通, 支管座 具有连接点
    auto pForkPoint = _pComNode->keyPoint(WDBMDPipeUtils::Fork(*_pComNode));
    if (_pComNode->isAnyOfType("TEE", "OLET") && pForkPoint != nullptr)
    {
        return pForkPoint->connType();
    }
    // !其他管件的连接点待处理
    assert(false);
    return "";
}

std::string TuComNode::mtotube() const
{
    if (_pComNode == nullptr)
        return "";
    return _pComNode->getAttribute("Mtotube").toWord();
}

bool TuComNode::sKey(std::string& strKey) const
{
    if (_pComNode == nullptr)
    {
        return false;
    }
    strKey = symbolKey(*_pComNode);
    return !strKey.empty();
}

bool TuComNode::isComNode() const
{
    return true;
}
bool TuComNode::isTypedComNode(const std::string_view& type) const
{
    if (_pComNode == nullptr)
        return false;
    return _pComNode->isType(type);
}

DKeyPoint TuComNode::update(ISODrawContext& cxt, const DKeyPoint& sKeyPt)
{
    // 先将图纸计算的所有关键点位置重置
    reseteRPt();
    if (_pComNode == nullptr)
        return sKeyPt;
    auto& pipeCom       = *_pComNode;
    // 图符建
    std::string sKey    = this->symbolKey(pipeCom);

    // 入口点朝向
    DVec3 dirA          = - sKeyPt.direction;
    // 出口朝向,后面能查到svg会重新计算值
    DVec3 dirL          = this->sPtL().direction;
    // 弯头
    if (pipeCom.isType("ELBO"))
    {
        auto ang = WD::DVec3::Angle(this->sPtA().direction, this->sPtL().direction);
        assert(ang > 0.0);

        auto tLineLen   = cxt.style.elbowSegLen;
        auto tRadiusLen = cxt.style.elbowArcRadius;
        DVec3 tRPtA = sKeyPt.position;
        // 弯头的直线部分终点, 同时也是圆弧的起点
        auto ptL1E = sKeyPt.position - dirA * tLineLen;
        // 圆弧的结束点,默认为直线部分的终点
        DVec3 arcPtE    = ptL1E;
        // P0点默认也是直线部分的终点
        DVec3 tRPt0     = ptL1E;

        //这里对DIrL进行一次更新，原因：因为前面的管件可能由于投影重叠计算，已经被旋转过了出口点，
        //所以到这个管件的时候，入口方向和入口点已经发生变化，不能直接使用spt的点和方向，
        //应该使用传入的入口点和入口方向，计算出新的出口方向和Rpt0，再由新的出口方向和新的Rpt0计算出新的RptL
        WD::DQuat quat = WD::DQuat::FromVectors(sPtA().direction, dirA);
        WD::DMat3 rMat = WD::DMat3::FromQuat(quat);
        dirL = rMat* sPtL().direction;
        //标记方向不要忘记也要转
        this->_dimDir = rMat * this->_dimDir;

        //投影重叠计算开始
        //这里的rptl点只是为了判断投影方向，所以位置只要在dril方向偏移就行，
        //与实际算完弯头的tRPtL点不一致没有关系，后面结尾算出来的tRPtL点才是后面真正这个弯头的出口点，返回给下一个管件的入口点。
        DVec3 tRPtL = tRPt0 + dirL * tLineLen;

        //出入口方向投影共线时，需要调整出口方向
        //如果第一个管件是弯头，并且投影共线，那么调整入口方向和初始的点，这样改动更小
        auto vecPtA2D = (cxt.uCvt.worldToScreen(tRPtA) - cxt.uCvt.worldToScreen(tRPt0)).normalized();
        auto vecPtL2D = (cxt.uCvt.worldToScreen(tRPtL) - cxt.uCvt.worldToScreen(tRPt0)).normalized();
        double angleLF = WD::DVec3::Angle(WD::DVec3(vecPtL2D), WD::DVec3(vecPtA2D));
        bool reCalculateDir = false;
        if (abs(angleLF - 180) < 2 || abs(angleLF) < 2)
        {
            // 已处理:起始弯头和结束弯头，
            // 待处理:中间弯头，或是任何涉及拐点的管件
            //方案一临时处理，先满足功能需要：最后一个弯头出口方向投影和入口方向投影共线，导致两个直管重叠
            //仅处理最后一个弯头,和第一个弯头
            bool isLastELBO = true;
            bool isFirstELBO = true;
            auto pTPrevNode = pPrevNode;
            while (pTPrevNode != nullptr)
            {
                if (pTPrevNode->isComNode())
                {
                    isFirstELBO = false;
                    break;
                }
                pTPrevNode = pTPrevNode->pPrevNode;
            }

            if (isFirstELBO)
            {//如果是第一个，那么不用遍历下一个
                isLastELBO = false;
            }
            auto pTNextNode = pNextNode;
            while (isLastELBO &&pTNextNode != nullptr)
            {
                if (pTNextNode->isComNode())
                {
                    isLastELBO = false;
                    break;
                }
                pTNextNode = pTNextNode->pNextNode;
            }
            //与上面仅处理最后一个弯头不同的是，这里处理所有弯头
            //方案二使用投影重叠一起处理掉这个最后一个弯头(任何涉及到拐点的管件)出口方向投影后重叠的问题
            //这里先注掉，是实现投影重叠以后旋转后要调整出口方向
            //WD::DQuat quat = WD::DQuat::FromVectors(sPtA().direction, dirA);
            //WD::DMat3 rMat = WD::DMat3::FromQuat(quat);
            //dirL = rMat * dirL;
            if (isFirstELBO && ang > 0)
            {
                //如果是第一个弯头，先算出p0点，根据p0点和新方向，改入口方向 和第一个posNode的出口方向和起点，改变第一段直管使其不重叠
                reCalculateDir = true;
            }

            if (!isFirstELBO)
            {
                //如果是不是第一个弯头，改出口方向能改后续绘制的直管
                // 如果入口方向和
                //dirL = WD::DVec3::Cross(dirA, this->_dimDir).normalized();
                //位置偏移矩阵
                WD::DMat4 offMatT = WD::DMat4::MakeTranslation(-tRPt0);
                //位置偏移矩阵的逆
                WD::DMat4 offMatTInv = WD::DMat4::MakeTranslation(tRPt0);
                //旋转矩阵
                WD::DMat4 offMatR = WD::DMat4::MakeRotation(30, dirA.normalized());
                auto mat = offMatTInv * offMatR * offMatT;
                //这里L方向的长度随便给一个就行，只是为了算旋转后的方向,更新下一个节点时会在这个方向上有距离
                dirL = (mat * (tRPt0 + dirL * tLineLen) - mat * tRPt0).normalized();
                this->_dimDir = offMatR* this->_dimDir;
            }
        }

        // 如果角度大于0,则根据弯头的弧线部分重新计算圆弧结束点以及P0点
        if (ang > 0.0)
        {
            auto hAng = DegToRad((180 - ang) * 0.5);
            // 出入口点到P0点的距离
            auto tDisP0 = Tan(hAng) * tRadiusLen;
            // 计算P0点(弧线的P0点）
            tRPt0 = ptL1E - dirA * tDisP0;
            if (reCalculateDir)
            {
                //dirA = WD::DVec3::Cross(dirL, this->_dimDir).normalized() * tLineLen;
                //位置偏移矩阵
                WD::DMat4 offMatT = WD::DMat4::MakeTranslation(-tRPt0);
                //位置偏移矩阵的逆
                WD::DMat4 offMatTInv = WD::DMat4::MakeTranslation(tRPt0);
                //旋转矩阵
                WD::DMat4 offMatR = WD::DMat4::MakeRotation(30, dirL.normalized());
                auto mat = offMatTInv * offMatR * offMatT;
                dirA = (mat*tRPtA - mat*tRPt0).normalized();
                tRPtA = tRPt0 + dirA * (tDisP0 + tLineLen);
                this->_dimDir = offMatR * this->_dimDir;
                auto pTPrevNode = pPrevNode;
                while (pTPrevNode != nullptr)
                {
                    if (!pTPrevNode->isTubiNode())
                    {
                        pTPrevNode->update(cxt, DKeyPoint(mat*pTPrevNode->rPt0(), -dirA.normalized()));
                        break;
                    }
                    pTPrevNode = pTPrevNode->pPrevNode;
                }
            }
            // 圆弧的结束点
            arcPtE      = tRPt0 + dirL * tDisP0;
        }
        // 弯头的直线部分2
        auto ptL2E      = arcPtE + dirL * tLineLen;
        // 弯头出口点
        tRPtL     = ptL2E;

        _rPt0   = tRPt0;
        _vecPtA = tRPtA - tRPt0;
        _vecPtL = tRPtL - tRPt0;
    }
    // 三通
    else if (pipeCom.isType("TEE"))
    {
        auto tLen   = cxt.style.teeKeyPtDis;
        // P0点
        _rPt0       = sKeyPt.position - dirA * tLen;
        // 入口点
        _vecPtA = sKeyPt.position - _rPt0;
        // 出口点
        _vecPtL = dirL * tLen;
        // 连接点
        if (this->sPtF())
        {
            auto vecPtA = this->sPtA().direction;
            auto vecPtL = this->sPtL().direction;
            auto vecPtF = this->sPtF()->direction;
            _vecPtF = vecPtF * tLen;
            //有两种情况，
                //第一种情况：是F连接到其他分支，这条分支从A进L出，F只是画出来看的，A方向和L方向共线
                //第二种情况：F连接方向是本条分支的走向，此时L和F的方向是互换了的，L方向和F方向共线
                //这两个情况都可以无脑算af夹角
            auto vecPtA2D = (cxt.uCvt.worldToScreen(this->sPtA().position) - cxt.uCvt.worldToScreen(this->sPt0().position)).normalized();
            auto vecPtF2D = (cxt.uCvt.worldToScreen(this->sPtF()->position) - cxt.uCvt.worldToScreen(this->sPt0().position)).normalized();
            auto vecPtL2D = (cxt.uCvt.worldToScreen(this->sPtL().position) - cxt.uCvt.worldToScreen(this->sPt0().position)).normalized();
            double angleAF = WD::DVec2::Angle(vecPtA2D, vecPtF2D);
            double angleLF = WD::DVec2::Angle(vecPtL2D, vecPtF2D);
            //但是这里还是保险起见，只算与F不共线的那个方向
            if (WD::DVec3::OnTheSameLine(vecPtL, vecPtA))
            {
                //(图例自身投影遮挡问题)
                if (abs(angleAF - 180) < 2 || abs(angleAF) < 2)
                {
                    vecPtF = WD::DVec3::Cross(vecPtA, vecPtF).normalized();
                }
                _vecPtF = vecPtF * tLen;
            }
            else
            {
                //A与L不共线，需要考虑F的投影方向是否与其中一个方向同向(反向是不会遮挡的)
                //(图例自身投影遮挡问题)
                if (abs(angleLF) < 2 || abs(angleAF) < 2)
                {
                    vecPtF = WD::DVec3::Cross(vecPtA, vecPtF).normalized();
                    _vecPtF = vecPtF * tLen;
                }
                //需要考虑A方向和L方向的管道是否会投影重叠,改变_vecPtL（管道投影重叠问题）同弯头投影重叠处理
                //
                WD::DQuat quat = WD::DQuat::FromVectors(sPtA().direction, dirA);
                WD::DMat3 rMat = WD::DMat3::FromQuat(quat);
                dirL = rMat * sPtL().direction;
                //标记方向不要忘记也要转
                this->_dimDir = rMat * this->_dimDir;
                _vecPtL = dirL * tLen;
            }
        }
    }
    // 垫片
    else if (pipeCom.isType("GASK"))
    {
        // 这里加上垫片厚度
        double halfLen = cxt.style.gaskThickness * 0.5;

        _rPt0 = sKeyPt.position + sKeyPt.direction * halfLen;
        // 入口点
        _vecPtA = sKeyPt.position - _rPt0;
        // 出口点
        _vecPtL = sKeyPt.direction * halfLen;
    }
    // 法兰
    else if (pipeCom.isAnyOfType("FLAN", "FBLI"))
    {
        double halfLen = 0.0;
        if (_strcmpi(sKey.c_str(), "FLWN") == 0
            || _strcmpi(sKey.c_str(), "FOWN") == 0
            || _strcmpi(sKey.c_str(), "FLGF") == 0
            || _strcmpi(sKey.c_str(), "FLSF") == 0)
        {
            auto length = cxt.style.comIconLen * 0.6;
            halfLen = length * 0.5;
        }
        else if (_strcmpi(sKey.c_str(), "FLSC") == 0
            || _strcmpi(sKey.c_str(), "FLSW") == 0
            || _strcmpi(sKey.c_str(), "FLGL") == 0
            || _strcmpi(sKey.c_str(), "FLPF") == 0)
        {
            auto length = cxt.style.comIconLen * 0.6;
            halfLen = length * 0.5;
        }
        else if (_strcmpi(sKey.c_str(), "FLBL") == 0
            || _strcmpi(sKey.c_str(), "FLRC") == 0
            || _strcmpi(sKey.c_str(), "FLRE") == 0
            || _strcmpi(sKey.c_str(), "FLSJ") == 0
            || _strcmpi(sKey.c_str(), "FLSO") == 0
            || _strcmpi(sKey.c_str(), "FOSO") == 0
            || _strcmpi(sKey.c_str(), "FLFF") == 0)
        {
            auto length = cxt.style.comIconLen * 0.3;
            halfLen = length * 0.5;
        }
        else
        {
            auto length = cxt.style.comIconLen * 0.3;
            halfLen = length * 0.5;
        }

        // 出口朝向
        dirL = sKeyPt.direction;
        //法兰的rpt0应该更靠近管件
        _rPt0 = sKeyPt.position + dirL * halfLen;
        // 入口点
        _vecPtA = sKeyPt.position - _rPt0;
        // 出口点
        _vecPtL = dirL * halfLen;

        auto shop = pipeCom.getAttribute("Shop").toBool();
        //是否绘制法兰的突出部分（尖头部分）
        bool drawSharp = true;
        //获取法兰的焊点类型，来做承插形式判断，是否画承插符号
        // 入口点的焊点
        if (pPrevNode != nullptr)
        {
            std::string cT1 = pPrevNode->cTypeL();
            std::string cT2 = this->cTypeA();
            auto wArriveType = ISOWeldQueryTable::Get().query(cT1, cT2, shop);
            if (wArriveType == ISOWeldQueryTable::RWT_FactorySocket || wArriveType == ISOWeldQueryTable::RWT_ScrewedJoint || wArriveType == ISOWeldQueryTable::RWT_SiteSocket)
            {
                drawSharp = false;
            }
        }
        // 出口点的焊点
        if (pNextNode != nullptr)
        {
            std::string cT1 = this->cTypeL();
            std::string cT2 = pNextNode->cTypeA();
            auto wLeaveType = ISOWeldQueryTable::Get().query(cT1, cT2, shop);
            if (wLeaveType == ISOWeldQueryTable::RWT_FactorySocket || wLeaveType == ISOWeldQueryTable::RWT_ScrewedJoint || wLeaveType == ISOWeldQueryTable::RWT_SiteSocket)
            {
                drawSharp = false;
            }
        }
        //头尾的法兰可能没有焊点。就使用法兰连接形式判断，SWF为承插面连接
        if (drawSharp)
        {
            //获取法兰的连接形式，有一个面是承插形式就不用画突出部分,改为画承插符号
            auto tType = QString::fromUtf8(GetTConnType(pipeCom).c_str());
            auto sType = QString::fromUtf8(GetHConnType(pipeCom).c_str());
            if (tType.contains("SWF", Qt::CaseInsensitive) || sType.contains("SWF", Qt::CaseInsensitive))
            {
                drawSharp = false;
            }
        }

        if (!drawSharp && pipeCom.isType("FLAN"))
        {
            _vecPtL /= 8;
        }

        if (pipeCom.isType("FBLI"))
        {
            _vecPtL /= 2;
        }
    }
    // 大小头
    else if (pipeCom.isType("REDU"))
    {
        const double len = cxt.style.comIconLen * 0.6;
        const double halfLen = len * 0.5;

        _rPt0 = sKeyPt.position + sKeyPt.direction * halfLen;
        // 入口点
        _vecPtA = -sKeyPt.direction * halfLen;
        // 出口点
        _vecPtL = -_vecPtA;

        //求偏心还是不偏心，偏心大小头需要重新算一下出口点
        const auto sPtA = this->sPtA();
        const auto sPtL = this->sPtL();
        const auto vecAL = sPtL.position - sPtA.position;
        const auto length = std::fabs(DVec3::Dot(vecAL, sPtL.direction));
        bool concentric = false;
        if (WD::DVec3::OnTheSameLine(vecAL.normalized(), sPtL.direction.normalized()))
        {
            concentric = true;
        }
        if (vecAL.length() > length && !concentric)
        {
            // 入口点
            _vecPtA = -sKeyPt.direction * len;
            // 出口点
            _vecPtL = -_vecPtA;

            // 这里指定偏心大小头的偏心角度的最小值(角度太小时,ISO图中看不出偏心的效果)
            constexpr const double minRAngle = 20;

            const auto sPt0 = this->sPt0();
            const auto vecA0 = sPtA.position - sPt0.position;
            const auto lengthA0 = std::fabs(DVec3::Dot(vecA0, sPtA.direction));

            const auto vecL0 = sPtL.position - sPt0.position;
            const auto lengthL0 = std::fabs(DVec3::Dot(vecL0, sPtL.direction));
            if (lengthA0 != 0)
            {
                auto quat = DQuat::FromVectors(sPtA.direction, vecA0);
                auto realAngle = std::max(minRAngle, quat.angle());
                const auto matA = DMat3::MakeRotation(realAngle, quat.axis());
                _vecPtA = matA * _vecPtA;
                _vecPtA /= cos(WD::DegToRad(realAngle));
                _vecPtA *= lengthA0 / (lengthA0 + lengthL0);
            }
            else
            {
                _vecPtA = -sKeyPt.direction * 0.0001;
            }

            if (lengthL0 != 0)
            {
                auto quat = DQuat::FromVectors(sPtL.direction, vecL0);
                auto realAngle = std::max(minRAngle, quat.angle());
                const auto matL = DMat3::MakeRotation(realAngle, quat.axis());
                _vecPtL = matL * _vecPtL;
                _vecPtL /= cos(WD::DegToRad(realAngle));
                _vecPtA *= lengthL0 / (lengthA0 + lengthL0);
            }
            else
            {
                _vecPtL = sKeyPt.direction * 0.0001;
            }
            _rPt0 = sKeyPt.position - _vecPtA;

            assert(!IsNan(_rPt0.x) && !IsNan(_rPt0.z) && !IsNan(_rPt0.z));
            return DKeyPoint(this->rPtL(), sKeyPt.direction);
        }
    }
    // 支管座
    else if (pipeCom.isType("OLET"))
    {
        _rPt0 = sKeyPt.position;
        // 入口点
        _vecPtA = -sKeyPt.direction * 0.0001;
        // 出口点
        _vecPtL = sKeyPt.direction * 0.0001;
        // 连接点
        auto tLen = cxt.style.teeKeyPtDis;
        // 连接点
        if (this->sPtF())
        {
            auto vecY = this->sPtF()->direction;
            //这条向量只要与投影方向在x0y平面内平行，则需要修改方向
            auto cFrontDir = cxt.uCvt.camera().frontDir();
            auto angle = DVec3::Angle(DVec3(vecY.x, vecY.y, 0), DVec3(cFrontDir.x, cFrontDir.y, 0));
            auto angleX = DVec3::Angle(DVec3(vecY.x, vecY.y, 0), DVec3(1, 0, 0));
            auto rotateAngle = 45;
            if (abs(angle - 180) < 5 || abs(angle) < 5)
            {
                if (angleX <= 45)
                {
                    //投影在x轴左边45度范围内
                    //-45度是绕z轴向右旋转45度
                    rotateAngle = -45;
                }
                else
                {
                    //45度是绕z轴向左旋转45度
                    rotateAngle = 45;
                }

                //旋转矩阵
                WD::DMat4 offMatR = WD::DMat4::MakeRotationZ(rotateAngle);
                vecY = offMatR * vecY;
            }
            _vecPtF = vecY * tLen;
        }

    }
    // BEND 弯管
    else if (pipeCom.isType("BEND"))
    {
        _rPt0 = sKeyPt.position;
        // 入口点
        _vecPtA = -sKeyPt.direction * 0.0001;
        // 出口点
        _vecPtL = this->sPtL().direction * 0.0001;
    }
    // 其他管件需要去图符中获取
    else
    {
        double len = cxt.style.comIconLen;
        auto sDirA = sKeyPt.direction;
        auto sDirL = dirL;
        if (QString::fromUtf8(sKey.c_str()).startsWith("KA", Qt::CaseInsensitive))
        {
            len *= 0.6;
            bool haveNext = false;
            auto pNode = pNextNode;
            while (pNode != nullptr)
            {
                if (pNode->isComNode())
                {
                    haveNext = true;
                    break;
                }
                pNode = pNode->pNextNode;
            }
            //管帽临时处理，管帽的后面不能接管件的，但是他居然能作为开头，但是他又不是出入口同向的管件。
            if (!haveNext)
            {
                sDirA = -sKeyPt.direction;
                dirL = -dirL;
            }
        }
        auto pFigure = _figuLegMgr.findFigureLegend(sKey, this->sPtA().direction, this->sPtL().direction);
        if (pFigure == nullptr)
            return sKeyPt;
        WD::DVec3 resPL;
        //为了临时处理管帽，没办法只能多拷贝一份kPoint了
        DKeyPoint kPoint = sKeyPt;
        kPoint.direction = sDirA;
        _figLegdRSMat = pFigure->caculateSvgMatrix(len, kPoint, this->_dimDir, _rPt0, resPL, dirL);

        auto resPA = sKeyPt.position;
        auto transPt = _figLegdRSMat * DVec3::Zero();
        if (DVec3::DistanceSq(_rPt0, transPt) >= NumLimits<float>::Epsilon)
            _offFigLegdTransPos = transPt - _rPt0;
        // 入口点
        _vecPtA = resPA - _rPt0;
        if (_vecPtA.lengthSq() < NumLimits<float>::Epsilon)
            _vecPtA = -sKeyPt.direction * 0.0001;
        // 出口点
        _vecPtL = resPL - _rPt0;
        if (_vecPtL.lengthSq() < NumLimits<float>::Epsilon)
            _vecPtL = dirL * 0.0001;
        return DKeyPoint(resPL, dirL);
    }

    assert(!IsNan(_rPt0.x) && !IsNan(_rPt0.z) && !IsNan(_rPt0.z));
    return DKeyPoint(this->rPtL(), _vecPtL.normalized());
}
void TuComNode::updateMBD()
{
    reseteRPt();

    _rPt0   = sPt0().position;
    _vecPtA = sPtA().position - sPt0().position;
    _vecPtL = sPtL().position - sPt0().position;
    if (sPtF())
        _vecPtF = sPtF()->position - sPt0().position;
}
void TuComNode::drawBase(ISODrawContext& cxt) const
{
    if (_pComNode == nullptr)
        return ;

    auto& pipeCom       = *_pComNode;
    // 图符建
    std::string sKey    = this->symbolKey(pipeCom);

    // 获取是否具有保温
    bool bIspec         = pipeCom.getAttribute("Ispec").toNodeRef().valid();
    //默认的管线样式（三通和弯头使用管线绘制）
    auto pipeLineStyle = cxt.style.pipeLineStyle;
    //默认的线样式（除了三通和弯头的图例基本上都是用默认线样式绘制）
    auto style = cxt.style.defLStyle;
    //管件材料列表
    QString mtocStr = QString::fromUtf8(pipeCom.getAttribute("Mtocomponent").toWord().c_str());;

    if (mtocStr.contains("DOTD", Qt::CaseInsensitive) || mtocStr.contains("DOTU",Qt::CaseInsensitive))
    {
        style.type = WDDIMLineStyle::LT_Dash;
        pipeLineStyle.type = WDDIMLineStyle::LT_Dash;
    }
    bool bDrawWeld = true;

    auto GetPipeLineSideFlanPoints = [&cxt] (DVec3Vector& points, const DVec3& posS, const DVec3& posE, const DVec3 dir)
    {
        double width = cxt.style.comIconWid;
        auto pointLB = posS - dir * width / 2;
        auto pointLT = posS + dir * width / 2;
        auto pointRB = posE - dir * width / 2;
        auto pointRT = posE + dir * width / 2;

        points.push_back(pointLB);
        points.push_back(pointLT);

        points.push_back(pointLT);
        points.push_back(pointRT);

        points.push_back(pointRT);
        points.push_back(pointRB);

        points.push_back(pointRB);
        points.push_back(pointLB);
    };

    // 弯头
    if (pipeCom.isType("ELBO"))
    {
        auto ang = WD::DVec3::Angle(this->sPtA().direction, this->sPtL().direction);

        auto tLineLen   = cxt.style.elbowSegLen;
        auto tRadiusLen = cxt.style.elbowArcRadius;

        // 弯头的顶点
        std::vector<DVec3> points;
        points.reserve(100);
        // 弯头两端的法兰顶点
        std::vector<DVec3> flanPoints;
        flanPoints.reserve(8);

        double flanWidth = cxt.style.comIconLen * 0.6 * 0.25;
        double flanOffset = cxt.style.pipeLineSideFlanOffset;
        if (IsNodeNeedPaintFlan(pipeCom) && (flanWidth + flanOffset) < tLineLen)
        {
            //TODO2025
            //实现重叠rotate以后这里应该用_vecPtA.normalized()和_vecPtL.normalized()；
            auto directionPtA = _vecPtA.normalized();
            auto directionPtL = _vecPtL.normalized();

            points.push_back(this->rPtA() - directionPtA * tLineLen);
            auto halfAng = DegToRad((180 - ang) * 0.5);
            // 出入口点到P0点的距离
            auto tDisP0 = Tan(halfAng) * tRadiusLen;
            // 圆弧的开始点
            const auto& arcPtS = points.back();
            //计算P0点(弧线的P0点）
            auto tP0 = arcPtS - directionPtA * tDisP0;
            // P0点到圆心点的方向向量
            auto tRDir = (directionPtA + directionPtL).normalized();
            // 圆心点
            auto tDisCen = tRadiusLen / Cos(halfAng);
            auto cen = tP0 + tRDir * tDisCen;
            // 圆弧的结束点
            auto arcPtE = tP0 + directionPtL * tDisP0;

            DVec3 sFlanDir = this->_dimDir;
            DVec3 eFlanDir = this->dimDirNext();
            // 弯头的开始点法兰
            {
                const auto flanPtS = this->rPtA() - directionPtA * flanOffset;
                const auto flanPtE = flanPtS - directionPtA * flanWidth;
                GetPipeLineSideFlanPoints(flanPoints, flanPtS, flanPtE, sFlanDir);
                points.push_back(flanPtE);
            }
            // 弯头的弧线部分
            if (ang > 0.0)
            {
                // 计算圆弧顶点
                {
                    auto arcDirS = (arcPtS - cen).normalized();
                    auto arcDirE = (arcPtE - cen).normalized();
                    //这里绘制的时候半径要由计算值更新（特别重要）
                    tRadiusLen = (arcPtS - cen).length();

                    auto up = DVec3::Cross(arcDirS, arcDirE).normalized();
                    uint cnt = 16;//ArcTravel(ang, tRadiusLen, 0.4);
                    for (uint i = 0; i <= cnt; ++i)
                    {
                        double tAngle = static_cast<double>(i) / static_cast<double>(cnt) * (180 - ang);
                        DMat3 rMat = DMat3::MakeRotation(tAngle, up);
                        DVec3 tmpV = (rMat * arcDirS).normalized();
                        DVec3 pt = cen + tmpV * tRadiusLen;
                        points.push_back(pt);
                    }
                }
            }
            // 弯头的结束点法兰
            {
                // 弯头的直线2起点,同时也是圆弧的终点
                const auto flanPtE = points.back() + directionPtL * (tLineLen - flanOffset);
                // 弯头的直线2终点
                const auto flanPtS = flanPtE - directionPtL * flanWidth;
                points.push_back(flanPtS);

                GetPipeLineSideFlanPoints(flanPoints, flanPtS, flanPtE, eFlanDir);
            }


            // 加入碰撞计算
            cxt.collision.addLines(flanPoints
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None)
                , cxt.style.pipeLineFlanLineStyle);
            // 
            cxt.painter.drawLines(flanPoints, cxt.style.pipeLineFlanLineStyle);
        }
        else
        {
            // 弯头的直线1起点
            points.push_back(this->rPtA());
            // 弯头的直线1终点,同时也是圆弧的起点
            //TODO2025
            //实现重叠rotate以后这里应该用_vecPtA.normalized()和_vecPtL.normalized()；
            auto directionPtA = _vecPtA.normalized();
            auto directionPtL = _vecPtL.normalized();
            points.push_back(this->rPtA() - directionPtA * tLineLen);
            // 弯头的弧线部分
            if (ang > 0.0)
            {
                auto halfAng = DegToRad((180 - ang) * 0.5);
                // 出入口点到P0点的距离
                auto tDisP0 = Tan(halfAng) * tRadiusLen;
                // 圆弧的开始点
                const auto& arcPtS = points.back();
                //计算P0点(弧线的P0点）
                auto tP0 = arcPtS - directionPtA * tDisP0;
                // P0点到圆心点的方向向量
                auto tRDir = (directionPtA + directionPtL).normalized();
                // 圆心点
                auto tDisCen = tRadiusLen / Cos(halfAng);
                auto cen = tP0 + tRDir * tDisCen;
                // 圆弧的结束点
                auto arcPtE = tP0 + directionPtL * tDisP0;
                // 计算圆弧顶点
                {
                    auto arcDirS = (arcPtS - cen).normalized();
                    auto arcDirE = (arcPtE - cen).normalized();
                    //这里绘制的时候半径要由计算值更新（特别重要）
                    tRadiusLen = (arcPtS - cen).length();

                    auto up = DVec3::Cross(arcDirS, arcDirE).normalized();
                    uint cnt = 16;//ArcTravel(ang, tRadiusLen, 0.4);
                    for (uint i = 0; i <= cnt; ++i)
                    {
                        double tAngle = static_cast<double>(i) / static_cast<double>(cnt) * (180 - ang);
                        DMat3 rMat = DMat3::MakeRotation(tAngle, up);
                        DVec3 tmpV = (rMat * arcDirS).normalized();
                        DVec3 pt = cen + tmpV * tRadiusLen;
                        points.push_back(pt);
                    }
                }
            }
            // 弯头的直线2起点,同时也是圆弧的终点
            const auto& arcPtE = points.back();
            // 弯头的直线2终点
            points.push_back(arcPtE + directionPtL * tLineLen);
        }

        // 加入碰撞计算
        cxt.collision.addBrokenLine(points
            , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe), pipeLineStyle);
        //cxt.collisionPipeLine.addBrokenLine(points, pipeLineStyle);
        // 绘制折线
        cxt.painter.drawBrokenLine(points, pipeLineStyle);
        if (bIspec)
        {
            DVec3 axisY = this->_dimDir;
            DrawIspecLines(cxt, points, axisY);
        }
    }
    // 三通
    else if (pipeCom.isType("TEE"))
    {
        DVec3 axisX = (this->rPtL() - this->rPtA()).normalized();
        DVec3 axisY = this->_dimDir;
        DVec3 axisZ = DVec3::Cross(axisX, axisY).normalized();
        if (_strcmpi(sKey.c_str(), "TESO") == 0)
        {
            // 假三通不需要画焊点
            bDrawWeld = false;
            // 加入碰撞计算
            cxt.collision.addLines({ this->rPtA(), this->rPtL() }
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe), pipeLineStyle);
            // 加入碰撞计算
            //cxt.collisionPipeLine.addLines({ this->rPtA(), this->rPtL() }, pipeLineStyle);
            // 入口点到出口点
            cxt.painter.drawLine(this->rPtA(), this->rPtL(), pipeLineStyle);
            if (bIspec)
                DrawIspecLines(cxt, { this->rPtA(), this->rPtL() }, axisY);

            const auto radius = pipeLineStyle.width * 1.2;
            auto lineStyle = cxt.style.defLStyle;
            lineStyle.type = WDDIMLineStyle::LT_Dash;
            if (this->rPtF())
            {
                axisZ = this->rPtF().value();
            }
            cxt.painter.drawCircle(this->rPt0(), radius, axisZ, lineStyle);
        }
        else
        {
            if (IsNodeNeedPaintFlan(pipeCom))
            {
                double flanWidth = cxt.style.comIconLen * 0.6 * 0.25;
                double flanOffset = cxt.style.pipeLineSideFlanOffset;

                DVec3Vector points;
                DVec3Vector flanPoints;
                // 入口点到P0点
                {
                    auto dir = DVec3::Normalize(this->rPt0() - this->rPtA());
                    const auto sFlanSPos = this->rPtA() + dir * flanOffset;
                    const auto sFlanEPos = sFlanSPos + dir * flanWidth;
                    GetPipeLineSideFlanPoints(flanPoints, sFlanSPos, sFlanEPos, this->_dimDir);
                    if (bIspec)
                        DrawIspecLines(cxt, {this->rPt0(), sFlanEPos}, axisX);

                    points.push_back(this->rPt0());
                    points.push_back(sFlanEPos);

                }
                // P0点到连接点
                auto rPtF = this->rPtF();
                if (rPtF)
                {
                    auto dir = DVec3::Normalize(this->rPt0() - rPtF.value());
                    const auto fFlanSPos = rPtF.value() + dir * flanOffset;
                    const auto fFlanEPos = fFlanSPos + dir * flanWidth;
                    GetPipeLineSideFlanPoints(flanPoints, fFlanSPos, fFlanEPos, _vecPtL.normalized());
                    if (bIspec)
                        DrawIspecLines(cxt, { this->rPt0(), fFlanEPos }, axisY);

                    points.push_back(this->rPt0());
                    points.push_back(fFlanEPos);
                }
                // P0点到出口点
                {
                    auto dir = DVec3::Normalize(this->rPt0() - this->rPtL());
                    const auto eFlanSPos = this->rPtL() + dir * flanOffset;
                    const auto eFlanEPos = eFlanSPos + dir * flanWidth;
                    GetPipeLineSideFlanPoints(flanPoints, eFlanSPos, eFlanEPos, this->_dimDir);
                    if (bIspec)
                        DrawIspecLines(cxt, { this->rPt0(), eFlanEPos }, axisX);

                    points.push_back(this->rPt0());
                    points.push_back(eFlanEPos);
                }

                // 加入碰撞计算
                cxt.collision.addLines(points
                    , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                    , pipeLineStyle);
                //cxt.collisionPipeLine.addLines(points, pipeLineStyle);
                cxt.collision.addLines(flanPoints
                    , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None)
                    , cxt.style.pipeLineFlanLineStyle);
                // 
                cxt.painter.drawLines(points, pipeLineStyle);
                cxt.painter.drawLines(flanPoints, cxt.style.pipeLineFlanLineStyle);
            }
            else
            {
                DVec3Vector points;
                // 入口点到P0点
                {
                    points.push_back(this->rPtA());
                    points.push_back(this->rPt0());
                    if (bIspec)
                        DrawIspecLines(cxt, { this->rPtA(), this->rPt0() }, axisX);
                }
                // P0点到连接点
                auto rPtF = this->rPtF();
                if (rPtF)
                {
                    points.push_back(this->rPt0());
                    points.push_back(rPtF.value());
                    if (bIspec)
                        DrawIspecLines(cxt, { this->rPt0(), rPtF.value() }, axisY);
                }
                // P0点到出口点
                {
                    points.push_back(this->rPt0());
                    points.push_back(this->rPtL());
                    if (bIspec)
                        DrawIspecLines(cxt, { this->rPt0(), this->rPtL() }, axisX);
                }
                // 加入碰撞计算
                cxt.collision.addLines(points
                    , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe), pipeLineStyle);
                //cxt.collisionPipeLine.addLines(points, pipeLineStyle);
                cxt.painter.drawLines(points, pipeLineStyle);
            }
        }
    }
    // 垫片, 不做绘制
    else if (pipeCom.isType("GASK"))
    {
    }
    // 法兰
    else if (pipeCom.isAnyOfType("FLAN", "FBLI"))
    {
        DVec3 tRPtA = this->rPtA();
        DVec3 tRPtL = this->rPtL();
        auto checkSwap = [&](DVec3& arrive, DVec3& leave,const WDKeyPoint* pt1, const WDKeyPoint* pt2)
        {
            const auto& gMat = pipeCom.globalTransform();
            if (pt1 != nullptr && pt2 != nullptr)
            {
                auto gPt1 = pt1->transformed(gMat);
                auto gPt2 = pt2->transformed(gMat);
                auto tVec = gPt2.position - gPt1.position;
                // 这里判断长度是因为法兰的出入口点有重合的情况
                if (tVec.lengthSq() >= NumLimits<float>::Epsilon)
                {
                    // 如果两个点的位置构成的向量与出口法相相反，则说明出入口点被交换
                    if (DVec3::Dot(tVec, this->sPtL().direction) < 0.0)
                        std::swap(arrive, leave);
                }
                else if (pPrevNode != nullptr)
                {
                    // 如果前一个的出口点的方向与 关键点2的方向相反，则证明出入口点被交换(关键点2被交换成为入口点)
                    if (DVec3::Dot(pPrevNode->sPtL().direction, gPt2.direction) < 0.0)
                        std::swap(arrive, leave);
                }
                else if (pNextNode != nullptr)
                {
                    // 如果后一个的入口点方向与 关键点1的方向相反，则证明出入口点被交换(关键点1被交换为出口点)
                    if (DVec3::Dot(pNextNode->sPtA().direction, gPt1.direction) < 0.0)
                        std::swap(arrive, leave);
                }
            }
        };
        
        auto shop = pipeCom.getAttribute("Shop").toInt();
        //是否绘制法兰的突出部分（尖头部分）
        bool drawSharp = true;
        //获取法兰的焊点类型，来做承插形式判断，是否画承插符号
        // 入口点的焊点
        if (pPrevNode != nullptr)
        {
            std::string cT1 = pPrevNode->cTypeL();
            std::string cT2 = this->cTypeA();
            auto wArriveType = ISOWeldQueryTable::Get().query(cT1, cT2, shop);
            if (wArriveType == ISOWeldQueryTable::RWT_FactorySocket || wArriveType == ISOWeldQueryTable::RWT_ScrewedJoint || wArriveType == ISOWeldQueryTable::RWT_SiteSocket)
            {
                drawSharp = false;
            }
        }
        // 出口点的焊点
        if (pNextNode != nullptr)
        {
            std::string cT1 = this->cTypeL();
            std::string cT2 = pNextNode->cTypeA();
            auto wLeaveType = ISOWeldQueryTable::Get().query(cT1, cT2, shop);
            if (wLeaveType == ISOWeldQueryTable::RWT_FactorySocket || wLeaveType == ISOWeldQueryTable::RWT_ScrewedJoint || wLeaveType == ISOWeldQueryTable::RWT_SiteSocket)
            {
                drawSharp = false;
            }
        }

        //头尾的法兰可能没有焊点。就使用法兰连接形式判断，SWF为承插面连接
        if (drawSharp)
        {
            //获取法兰的连接形式，有一个面是承插形式就不用画突出部分,改为画承插符号
            auto tType = QString::fromUtf8(GetTConnType(pipeCom).c_str());
            auto sType = QString::fromUtf8(GetHConnType(pipeCom).c_str());
            if (tType.contains("SWF", Qt::CaseInsensitive) || sType.contains("SWF", Qt::CaseInsensitive))
            {
                drawSharp = false;
            }
        }
        // 判断出入口点的位置是否交换
        if (pipeCom.isType("FLAN"))
        {
            auto pPt1 = pipeCom.keyPoint(1);
            auto pPt2 = pipeCom.keyPoint(2);
            checkSwap(tRPtA, tRPtL, pPt1, pPt2);
        }
        // 判断出入口点的位置是否交换
        if (pipeCom.isType("FBLI"))
        {
            auto pPt1 = pipeCom.keyPoint(1);
            auto pPt2 = pipeCom.keyPoint(2);
            checkSwap(tRPtA, tRPtL, pPt1, pPt2);
            drawSharp = true;
        }

        DVec3 axisX = (tRPtL - tRPtA).normalized();
        DVec3 axisY = this->_dimDir;

        if (_strcmpi(sKey.c_str(), "FLWN") == 0
            || _strcmpi(sKey.c_str(), "FOWN") == 0
            || _strcmpi(sKey.c_str(), "FLGF") == 0
            || _strcmpi(sKey.c_str(), "FLSF") == 0)
        {
            auto length = cxt.style.comIconLen * 0.6;
            double width = cxt.style.comIconWid;

            double len0 = length * 0.25;
            double width0 = width;
            DVec2 size0 = DVec2(len0, width0);
            DVec2 halfSize0 = size0 * 0.5;
            DVec3 cen0 = tRPtA + axisX * halfSize0.x;
            if (!drawSharp)
            {
                cen0 = tRPtL - axisX * halfSize0.x;
            }
            std::vector<DVec3> rect0 =
            {
                cen0 + halfSize0.x * axisX + halfSize0.y * axisY,
                cen0 - halfSize0.x * axisX + halfSize0.y * axisY,
                cen0 - halfSize0.x * axisX - halfSize0.y * axisY,
                cen0 + halfSize0.x * axisX - halfSize0.y * axisY,
            };
            cxt.painter.drawLoopLine(rect0, style);
            // 加入碰撞计算
            cxt.collision.addLoopLine(rect0
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                , style);
            //cxt.collisionPipeLine.addLoopLine(rect0, style);
            double len1 = length * 0.75;
            double width1 = width * 0.6;
            DVec2 size1 = DVec2(len1, width1);
            DVec2 halfSize1 = size1 * 0.5;
            DVec3 cen1 = tRPtL - axisX * halfSize1.x;
            if (!drawSharp)
            {
                len1 = cxt.style.socketWeldlength();
                halfSize1.x = len1 * 0.5;
                cen1 = tRPtL + axisX * halfSize1.x;
            }
            double width2 = width * 0.3 * 0.5;
            std::vector<DVec3> rect1 =
            {
                cen1 + axisX * halfSize1.x + axisY * width2,
                cen1 - axisX * halfSize1.x + axisY * halfSize1.y,
                cen1 - axisX * halfSize1.x - axisY * halfSize1.y,
                cen1 + axisX * halfSize1.x - axisY * width2,
            };
            cxt.painter.drawLoopLine(rect1, style);
            // 加入碰撞计算
            cxt.collision.addLoopLine(rect1
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                , style);
            //cxt.collisionPipeLine.addLoopLine(rect1, style);

        }
        else if (_strcmpi(sKey.c_str(), "FLSC") == 0
            || _strcmpi(sKey.c_str(), "FLSW") == 0
            || _strcmpi(sKey.c_str(), "FLGL") == 0
            || _strcmpi(sKey.c_str(), "FLPF") == 0)
        {
            auto length = cxt.style.comIconLen * 0.6;
            double width = cxt.style.comIconWid;

            double len0 = length * 0.25;
            double width0 = width;
            DVec2 size0 = DVec2(len0, width0);
            DVec2 halfSize0 = size0 * 0.5;
            DVec3 cen0 = tRPtA + axisX * halfSize0.x;
            if (!drawSharp)
            {
                cen0 = tRPtL - axisX * halfSize0.x;
            }
            std::vector<DVec3> rect0 =
            {
                cen0 + halfSize0.x * axisX + halfSize0.y * axisY,
                cen0 - halfSize0.x * axisX + halfSize0.y * axisY,
                cen0 - halfSize0.x * axisX - halfSize0.y * axisY,
                cen0 + halfSize0.x * axisX - halfSize0.y * axisY,
            };
            cxt.painter.drawLoopLine(rect0, style);
            // 加入碰撞计算
            cxt.collision.addLoopLine(rect0
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                , style);
            //cxt.collisionPipeLine.addLoopLine(rect0, style);

            double len1 = length * 0.75;
            double width1 = width * 0.6;
            DVec2 size1 = DVec2(len1, width1);
            DVec2 halfSize1 = size1 * 0.5;
            DVec3 cen1 = tRPtL - axisX * halfSize1.x;
            if (!drawSharp)
            {
                len1 = cxt.style.socketWeldlength();
                halfSize1.x = len1 * 0.5;
                cen1 = tRPtL + axisX * halfSize1.x;
            }
            std::vector<DVec3> lines =
            {
                cen1 + axisX * halfSize1.x + axisY * halfSize1.y,
                cen1 - axisX * halfSize1.x + axisY * halfSize1.y,
                cen1 - axisX * halfSize1.x - axisY * halfSize1.y,
                cen1 + axisX * halfSize1.x - axisY * halfSize1.y,
            };
            cxt.painter.drawLines(lines, style);
            // 加入碰撞计算
            cxt.collision.addLines(lines
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                , style);
            //cxt.collisionPipeLine.addLines(lines, style);
        }
        else if (_strcmpi(sKey.c_str(), "FLBL") == 0
            || _strcmpi(sKey.c_str(), "FLRC") == 0
            || _strcmpi(sKey.c_str(), "FLRE") == 0
            || _strcmpi(sKey.c_str(), "FLSJ") == 0
            || _strcmpi(sKey.c_str(), "FLSO") == 0
            || _strcmpi(sKey.c_str(), "FOSO") == 0
            || _strcmpi(sKey.c_str(), "FLFF") == 0)
        {
            auto length = cxt.style.comIconLen * 0.3;
            //上面是0.6*0.25，这里是0.3，所以厚度不一致
            length *= 0.5;
            auto height = cxt.style.comIconWid;
            cxt.painter.drawRect(this->rPt0()
                , DVec2(length, height)
                , _vecPtL.normalized()
                , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
                , style);
            // 加入碰撞计算
            cxt.collision.addRect(this->rPt0()
                , DVec2(length, height)
                , _vecPtL.normalized()
                , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
                , style
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe));
            //cxt.collisionPipeLine.addRect(this->rPt0()
            //    , DVec2(length, height)
            //    , _vecPtL.normalized()
            //    , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
            //    , style);
        }
        else
        {
            auto length = cxt.style.comIconLen * 0.3;
            auto height = cxt.style.comIconWid;
            cxt.painter.drawRect(this->rPt0()
                , DVec2(length, height)
                , _vecPtL.normalized()
                , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
                , style);
            // 加入碰撞计算
            cxt.collision.addRect(this->rPt0()
                , DVec2(length, height)
                , _vecPtL.normalized()
                , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
                , style
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe));
            //cxt.collisionPipeLine.addRect(this->rPt0()
            //    , DVec2(length, height)
            //    , _vecPtL.normalized()
            //    , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
            //    , style);
        }
    }
    // 大小头
    else if (pipeCom.isType("REDU"))
    {
        auto dirL = _vecPtL.normalized();
        // 大小头比较特殊,出入口向量方向与出入口点方向不相同,这里向前找到上一个管件节点,使用上一个管件节点的出口点方向或者找到下一个管件使用其入口点方向
        auto prevNode = this->pPrevNode;
        while (prevNode != nullptr)
        {
            if (prevNode->isComNode())
            {
                auto& comNode = static_cast<TuComNode&>(*prevNode);
                if (comNode._pComNode != nullptr && !comNode._pComNode->isType("REDU"))
                {
                    dirL = comNode._vecPtL.normalized();
                    break;
                }
            }
            prevNode = prevNode->pPrevNode;
        }
        if (prevNode == nullptr)
        {
            auto nextNode = this->pNextNode;
            while (nextNode != nullptr)
            {
                if (nextNode->isComNode())
                {
                    auto& comNode = static_cast<TuComNode&>(*nextNode);
                    if (comNode._pComNode != nullptr && !comNode._pComNode->isType("REDU"))
                    {
                        dirL = -comNode._vecPtA.normalized();
                        break;
                    }
                }
                nextNode = nextNode->pNextNode;
            }
        }
        const auto tempVec      = DVec3::Cross(dirL, this->_dimDir);
        const auto axisDim      = DVec3::Cross(tempVec, dirL);
        const double yLen       = cxt.style.comIconWid;

        auto dVal = yLen * 0.2;

        DVec3 aLeft     = this->rPtA() - axisDim * yLen * 0.5;
        DVec3 aRight    = this->rPtA() + axisDim * yLen * 0.5;
        DVec3 lLeft     = this->rPtL() - axisDim * dVal;
        DVec3 lRight    = this->rPtL() + axisDim * dVal;
        // 判断出入口点的位置是否交换
        {
            auto pPtA = pipeCom.keyPoint(pipeCom.getAttribute("Arrive").toInt());
            auto pPtL = pipeCom.keyPoint(pipeCom.getAttribute("Leave").toInt());
            if (pPtA != nullptr && pPtL != nullptr)
            {
                // 如果入口是小端，则交换点
                bool bOkA = false;
                bool bOkL = false;
                double boreA = FromString<double>(pPtA->bore(), &bOkA);
                double boreL = FromString<double>(pPtL->bore(), &bOkL);
                bool bSwap = bOkA && bOkL && boreA < boreL;
                if (bSwap)
                {
                    aLeft   = this->rPtA() - dVal * axisDim;
                    aRight  = this->rPtA() + dVal * axisDim;
                    lLeft   = this->rPtL() - yLen * axisDim * 0.5;
                    lRight  = this->rPtL() + yLen * axisDim * 0.5;
                }
                auto dotValue = DVec3::Dot(this->_dimDir
                    , DVec3::Normalize(pPtL->transformedPosition(pipeCom.globalTransform()) - pPtA->transformedPosition(pipeCom.globalTransform())));
                assert(dotValue != 1);
                if (dotValue > 0.01)
                {
                    if (bSwap)
                    {
                        // 大头在出口端 顶平
                        aLeft   = this->rPtA() - dVal * axisDim;
                        aRight  = this->rPtA() + dVal * axisDim;
                        lRight  = this->rPtL() + dVal * axisDim;
                        // 计算出口端上顶点
                        auto laVec = this->rPtL() - this->rPtA();
                        auto offset = std::fabs(DVec3::Dot(laVec, dirL));
                        lLeft   = aLeft + dirL * offset;
                    }
                    else
                    {
                        // 大头在入口端 底平
                        aLeft   = this->rPtA() - dVal * axisDim;
                        lLeft   = this->rPtL() - dVal * axisDim;
                        lRight  = this->rPtL() + dVal * axisDim;
                        // 计算出口端下顶点
                        auto laVec = this->rPtL() - this->rPtA();
                        auto offset = std::fabs(DVec3::Dot(laVec, dirL));
                        aRight  = lRight - dirL * offset;
                    }
                }
                else if (dotValue < -0.01)
                {
                    if (bSwap)
                    {
                        // 大头在出口端 底平
                        aLeft   = this->rPtA() - dVal * axisDim;
                        aRight  = this->rPtA() + dVal * axisDim;
                        lLeft   = this->rPtL() - dVal * axisDim;
                        // 计算出口端下顶点
                        auto laVec = this->rPtL() - this->rPtA();
                        auto offset = std::fabs(DVec3::Dot(laVec, dirL));
                        lRight  = aRight + dirL * offset;
                    }
                    else
                    {
                        // 大头在入口端 顶平
                        aRight  = this->rPtA() + dVal * axisDim;
                        lLeft   = this->rPtL() - dVal * axisDim;
                        lRight  = this->rPtL() + dVal * axisDim;
                        // 计算入口端上顶点
                        auto laVec = this->rPtL() - this->rPtA();
                        auto offset = std::fabs(DVec3::Dot(laVec, dirL));
                        aLeft   = lLeft - dirL * offset;
                    }
                }
            }
        }
        cxt.painter.drawLoopLine({ aLeft, aRight,lRight, lLeft}, style);
        // 加入碰撞计算
        cxt.collision.addLoopLine({ aLeft, aRight, lRight, lLeft }
            , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
            , style);
        //cxt.collisionPipeLine.addLoopLine({ aLeft, aRight, lRight, lLeft }, style);
    }
    // 支管座
    else if (pipeCom.isType("OLET"))
    {
        // P0点到连接点
        if (this->sPtF())
        {
            auto tLen = cxt.style.teeKeyPtDis;
            double tXLen = tLen * 0.3;
            double tYLen = tLen * 0.5;
            DVec3 vecX = _vecPtL.normalized();
            DVec3 vecY = _vecPtF.value().normalized();
            auto cFrontDir = cxt.uCvt.camera().frontDir();
            
            std::vector<DVec3> points =
            {
                this->rPt0() + vecX * tXLen,
                this->rPt0() + vecX * tXLen * 1.5 + vecY * tYLen,
                this->rPt0() + vecX * tXLen + vecY * tYLen * 2.0,
                this->rPt0() - vecX * tXLen + vecY * tYLen * 2.0,
                this->rPt0() - vecX * tXLen * 1.5 + vecY * tYLen,
                this->rPt0() - vecX * tXLen,
            };
            cxt.painter.drawLoopLine(points, style);
            cxt.collision.addLoopLine(points
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                , style);
            //cxt.collisionPipeLine.addLoopLine(points, style);
        }
    }
    // BEND 弯管, 不做绘制
    else if (pipeCom.isType("BEND"))
    {
    }
    // 其他管件需要去图符中获取
    else
    {
        auto pFigure = _figuLegMgr.findFigureLegend(sKey, this->sPtA().direction, this->sPtL().direction);
        if (pFigure != nullptr)
        {
            auto tRSMat = DMat4::ToMat3(_figLegdRSMat);
            DMat4 tMat = _figLegdRSMat;
            tMat[3] = DVec4(_rPt0 + _offFigLegdTransPos, 1.0);
            pFigure->Draw(tMat, cxt.painter, style);
            if (pFigure->haveValidDepend())
            {
                if (_pComNode != nullptr && IsValvType(*_pComNode))
                {
                    constexpr static const double errorRange = 0.001;

                    // 阀门手轮的方向是阀门p3点的方向
                    auto pPoint = _pComNode->keyPoint(3);
                    if (pPoint != nullptr)
                    {
                        auto point = pPoint->transformed(_pComNode->globalTransform());
                        if (QString::fromUtf8(sKey.c_str()).startsWith("RA",Qt::CaseInsensitive) && DVec3::InTheOppositeDirection(DVec3::AxisZ(), point.direction, Residual))
                        {
                            point.direction = DVec3::AxisZ();
                        }
                        auto flowDir = this->sPtL().direction;

                        // 默认以世界坐标z轴为上方向,如果管道流向和Z轴在一条线上,则使用Y作为上方向
                        DVec3 zDir = DVec3::AxisZ();
                        if (DVec3::OnTheSameLine(DVec3::AxisZ(), flowDir, Residual))
                            zDir = DVec3::AxisY();
                        // 计算右方向,用上方向叉乘流向方向作为右方向
                        DVec3 rightDir = DVec3::Normalize(DVec3::Cross(zDir, flowDir));
                        auto upDir = DVec3::Normalize(WD::DVec3::Cross(flowDir, rightDir));
                        // iso中手轮最终指向的方向    ->  最终方向会根据和四个方向(上下左右)的角度落在四个方向其中一个,
                        // 优先落在上下两个方向,当与 上/下 方向夹角 <= 45° 时指向 上/下 
                        // 次优先落在左右两个方向,当与 左/右 方向夹角 < 45° 时指向 左/右 方向
                        WD::DVec3 targetDir = WD::DVec3::AxisY();
                        if (auto angle = WD::DVec3::Angle(upDir, point.direction);angle <= (45 + errorRange))
                            targetDir = zDir;
                        else if (angle >= (135 - errorRange))
                            targetDir = -zDir;
                        else if (angle = WD::DVec3::Angle(rightDir, point.direction); angle < (45 - errorRange))
                            targetDir = rightDir;
                        else if (angle > (135 + errorRange))
                            targetDir = -rightDir;
                        else
                            assert(false);
                        // 将最终指向的方向转换到iso的本地坐标系
                        auto rMat = tMat;
                        rMat[3] = DVec4(0.0, 0.0, 0.0, 1.0);
                        targetDir = DVec3::Normalize(rMat.inverse() * targetDir);
                        for (auto& each : pFigure->actuatorItems())
                        {
                            auto& pDepend = each.pFigure;
                            if (pDepend == nullptr)
                                continue;

                            auto originDir = WD::DVec3::Normalize(DVec3(pDepend->dirP0()));
                            // 计算手轮的本地矩阵
                            WD::DMat4 mat = DMat4::Identity();
                            if (originDir != targetDir)
                                mat = DMat4::Compose(DVec3(each.position), DQuat::FromVectors(originDir, targetDir));
                            else
                                mat = DMat4::MakeTranslation(DVec3(each.position));

                            pDepend->drawActuators(tMat * mat, cxt.painter, style);
                        }
                    }
                }
            }
        }
        cxt.collision.addRect(this->rPt0()
            , DVec2(cxt.style.comIconLen, cxt.style.comIconWid)
            , _vecPtL.normalized()
            , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
            , style
            , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe));
        //cxt.collisionPipeLine.addRect(this->rPt0()
        //    , DVec2(cxt.style.comIconLen, cxt.style.comIconWid)
        //    , _vecPtL.normalized()
        //    , DVec3::Cross(_vecPtL.normalized(), this->_dimDir).normalized()
        //    , style);
    }
    // 绘制管件连接处的焊点
    if (bDrawWeld)
        this->drawWeld(cxt, pipeCom);

}
void TuComNode::drawDimLinear(ISODrawContext& cxt) const
{
    if (_pComNode == nullptr)
        return;
    auto& pipeCom = *_pComNode;

    //管件材料列表
    QString mtocStr = QString::fromUtf8(pipeCom.getAttribute("Mtocomponent").toWord().c_str());

    if (mtocStr.contains("DOTU", Qt::CaseInsensitive))
    {
        return;
    }
    //2025.2.18 广群说恢复弯头标注尺寸，记录一下以后做配置弯头是否标注尺寸的时候在这里修改
    //// 2024.12.27 广群：华东院的弯头全部都不标注p0-p1,p0-p2尺寸
    //if (pipeCom.isBDSub<WDBMDPipeELBO>())
    //{
    //    return;
    //}
    // 2024.12.31 魏耀辉：华东院的温度计和压力表不标注p0-p1,p0-p2尺寸
    // 图符建
    QString sKey = QString::fromUtf8(this->symbolKey(pipeCom).data());
    if (sKey.startsWith("id", Qt::CaseInsensitive) || sKey.startsWith("ir", Qt::CaseInsensitive))
    {
        return;
    }
    // 如果管件的出入口点位置重合，则不做标记
    if (DVec3::DistanceSq(this->sPtA().position, this->sPtL().position) <= 0.001)
    {
    }
    // 垫片,跳过
    else if (pipeCom.isType("GASK"))
    {
    }      
    // 支管座，不做标记
    else if (pipeCom.isType("OLET"))
    {

    }
    // 其他管件,根据是否拐点判断如何标注
    else
    {
        if (this->isInflection() && pNextNode != nullptr)
        {
            cxt.dimGen.addDIMLinearT2(this->_dimDir
                , this->rPtA()
                , this->rPt0()
                , DVec3::Distance(this->sPtA().position, this->sPt0().position)
                , this);

            cxt.dimGen.addDIMLinearT2(this->dimDirNext()
                , this->rPt0()
                , this->rPtL()
                , DVec3::Distance(this->sPt0().position, this->sPtL().position)
                , pNextNode);
        }
        else
        {
            cxt.dimGen.addDIMLinearT2(this->_dimDir
                , this->rPtA()
                , this->rPtL()
                , DVec3::Distance(this->sPtA().position, this->sPtL().position)
                , this);
        }
    }
}
void TuComNode::drawDimLinearMBD(ISODrawContext& cxt) const
{
    if (_pComNode == nullptr)
        return;
    auto& pipeCom = *_pComNode;

    //管件材料列表
    QString mtocStr = QString::fromUtf8(pipeCom.getAttribute("Mtocomponent").toWord().c_str());

    // 如果管件的出入口点位置重合，则不做标记
    if (DVec3::DistanceSq(this->sPtA().position, this->sPtL().position) <= 0.001)
    {
    }
    // 垫片,跳过
    else if (pipeCom.isType("GASK"))
    {
    }
    // 支管座，不做标记
    else if (pipeCom.isType("OLET"))
    {

    }
    // 其他管件,根据是否拐点判断如何标注
    else
    {
        if (this->isInflection() && pNextNode != nullptr)
        {
            cxt.dimGen.addDIMLinearT2(this->_dimDir
                , this->rPtA()
                , this->rPt0()
                , DVec3::Distance(this->sPtA().position, this->sPt0().position)
                , this);

            cxt.dimGen.addDIMLinearT2(this->dimDirNext()
                , this->rPt0()
                , this->rPtL()
                , DVec3::Distance(this->sPt0().position, this->sPtL().position)
                , pNextNode);
        }
        else
        {
            cxt.dimGen.addDIMLinearT2(this->_dimDir
                , this->rPtA()
                , this->rPtL()
                , DVec3::Distance(this->sPtA().position, this->sPtL().position)
                , this);
        }
    }
}

void TuComNode::drawDimInfo(ISODrawContext& cxt, const std::unordered_map<std::string, std::string>& paperInfos) const
{
    if (_pComNode == nullptr)
        return;

    char buf[1024] = { 0 };
    auto& pipeCom = *_pComNode;

    const auto& fStyle = cxt.style.defFStyle;
    const auto& fStyleB = cxt.style.defBorderFStyle;

    const auto gPos = sPt0().position;
    const IVec3 gPosLL = IVec3(static_cast<int>(Round(gPos.x))
        , static_cast<int>(Round(gPos.y))
        , static_cast<int>(Round(gPos.z)));

    using Texts = std::vector<std::pair<std::string, WDDIMFontStyle> >;

    ISODIMGenerate::QLeaderUParam qleadUParam;
    qleadUParam.baseLine = DRay(_rPt0,_vecPtL.normalized());
    qleadUParam.textBaseDistance = this->_qleadMinDisN;

    // 这里处理等级分界
    /// 等级分界规则
    /// 当管件(ATTA和INST除外)之间的管子等级发生变化时,在两个管件之间标注出两个等级
    if (WDBMDPipeUtils::IsPipeComponent(pipeCom) && !pipeCom.isAnyOfType("ATTA", "INST"))
    {
        auto pPrevComNode = this->pPrevNode;
        while (pPrevComNode != nullptr)
        {
            if (pPrevComNode->isComNode())
                break;
            pPrevComNode = pPrevComNode->pPrevNode;
        }
        if (pPrevComNode != nullptr)
        {
            auto& prevComNode = static_cast<TuComNode&>(*pPrevComNode);
            if (prevComNode._pComNode != nullptr && !prevComNode._pComNode->isAnyOfType("ATTA", "INST"))
            {
                auto pPrevLstu = prevComNode._pComNode->getAttribute("Lstube").toNodeRef().refNode();
                auto pCurrLstu = _pComNode->getAttribute("Lstube").toNodeRef().refNode();
                if (pPrevLstu != nullptr && pCurrLstu != nullptr && pPrevLstu != pCurrLstu)
                {
                    auto pPrevSpec = pPrevLstu->parent();
                    while (pPrevSpec != nullptr)
                    {
                        if (pPrevSpec->isType("SPEC"))
                            break;
                        pPrevSpec = pPrevSpec->parent();
                    }
                    auto pCurrSpec = pCurrLstu->parent();
                    while (pCurrSpec != nullptr)
                    {
                        if (pCurrSpec->isType("SPEC"))
                            break;
                        pCurrSpec = pCurrSpec->parent();
                    }

                    if (pPrevSpec != pCurrSpec)
                    {
                        const DVec3 pos = (this->rPtA() + prevComNode.rPtL()) / 2 + this->_qleadDir * cxt.style.comIconWid * 0.5;
                        const DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;

                        WDDIMMultiTexts::TextInfos texts;
                        auto flowDir = -this->sPtA().direction;
                        if (pPrevSpec != nullptr)
                        {
                            texts.emplace_back();
                            texts.back().pos = textPos - flowDir * this->_qleadMinDis * 1.2;
                            sprintf_s(buf, sizeof(buf), "%s", pPrevSpec->name().c_str());
                            texts.back().texts.emplace_back("MATL");
                            texts.back().texts.emplace_back(buf);
                            texts.back().style = fStyleB;
                        }

                        if (pCurrSpec != nullptr)
                        {
                            texts.emplace_back();
                            texts.back().pos = textPos + flowDir * this->_qleadMinDis * 1.2;
                            sprintf_s(buf, sizeof(buf), "%s", pCurrSpec->name().c_str());
                            texts.back().texts.emplace_back("MATL");
                            texts.back().texts.emplace_back(buf);
                            texts.back().style = fStyleB;
                        }
                        if (texts.size() == 2)
                            cxt.dimGen.addMultiText(pos, textPos, texts, -1, 1, std::nullopt, std::nullopt, std::nullopt, WDDIMAlign());
                    }
                }
            }
        }
    }

    // 弯头
    if (pipeCom.isType("ELBO"))
    {
        DVec3 tN = DVec3::Normalize(this->_frontDir - this->frontDirNext());
        DVec3 pos = this->rPt0() + tN * cxt.style.halfPipeLineWid();
        DVec3 textPos = pos + tN * this->_qleadMinDis;
        Texts texts;

        if (this->number != -1)
        {
            // 标号
            sprintf_s(buf, sizeof(buf), "%d", this->number);
            texts.push_back({buf, fStyleB});
        }
        // 角度
        auto angle = pipeCom.getAttribute("Angle").toDouble();
        if (Abs(90.0 - angle) > 0.01)
        {
            sprintf_s(buf, sizeof(buf), "%.1lf", angle);
            texts.push_back({ buf, fStyle });
        }
        // 标高
        CoordOutPut(buf, sizeof(buf), gPosLL.z, "EL");
        texts.push_back({ buf, fStyle });
        // 创建标记, 弯头上的标签避让时，不用做镜像避让
        cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1);
        //创建弯头编号标记
        if (_elboNumber)
        {
            texts.clear();
            // 标号
            sprintf_s(buf, sizeof(buf), "a%d", _elboNumber.value());
            texts.push_back({ buf, fStyle });
            // 创建标记, 弯头上的标签避让时，不用做镜像避让
            pos = this->rPt0() - tN * cxt.style.halfPipeLineWid();
            textPos = pos - tN * this->_qleadMinDis;
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1);
        }
        //创建最大应力点标记
        if (_maxStressNumber)
        {
            texts.clear();
            // 标号
            sprintf_s(buf, sizeof(buf), "s%d", _maxStressNumber.value());
            texts.push_back({ buf, fStyle });
            pos = this->rPtL() - tN * cxt.style.halfPipeLineWid();
            textPos = pos - tN * this->_qleadMinDis;
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1);
        }
        // 标识支架信息, 弯头上的标签避让时，不用做镜像避让
        for (const auto& atta : _attas)
        {
            if (atta.attaNode() == nullptr)
            {
                assert(false);
                continue;
            }
            std::string txt = atta.sText();
            if (txt.empty())
                break;
            // 计算标记文本的位置
            WDDIMQLeader::Texts attaTexts = { { txt, cxt.style.defFStyle } };
            cxt.dimGen.addDIMQLeader(pos
                , textPos
                , attaTexts
                , -1
                , 1);
        }
    }
    // 三通
    else if (pipeCom.isType("TEE"))
    {
        // 添加管件标注
        {
            DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.halfPipeLineWid();
            DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;
            Texts texts;
            // 图符建
            std::string sKey = this->symbolKey(pipeCom);
            if (_strcmpi(sKey.c_str(), "TESO") != 0 && this->number != -1)
            {
                // 标号
                sprintf_s(buf, sizeof(buf), "%d", this->number);
                texts.push_back({ buf, fStyleB });
            }
            // 通径, 通径固定从P1，P3点去取
            auto pPt1 = pipeCom.keyPoint(1);
            auto pPt3 = pipeCom.keyPoint(3);
            assert(pPt1 != nullptr && pPt3 != nullptr);
            if (pPt1 != nullptr && pPt3 != nullptr)
            {
                sprintf_s(buf, sizeof(buf), "%sx%sDN", pPt1->bore().c_str(), pPt3->bore().c_str());
                texts.push_back({ buf, fStyle });
            }
            // 标高
            CoordOutPut(buf, sizeof(buf), gPosLL.z, "EL");
            texts.push_back({ buf, fStyle });
            // 连接点朝向
            if (this->sPtF())
            {
                std::string strDir = DDirectionParserENU::OutputStringByDirection(this->sPtF()->direction, 1);
                texts.push_back({ strDir, fStyle });
            }
            // 创建标记
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
               , qleadUParam);
        }
        // 连接信息标注
        if (this->rPtF())
        {
            DVec3 pos = this->rPtF().value();
            DVec3 tDir = this->_qleadDir;
            double tDis = this->_qleadMinDis;
            qleadUParam.textBaseDistance = this->_qleadMinDisN;
            qleadUParam.srcBaseDistance = cxt.style.halfPipeLineWid();
            // 这里投影的目的是计算在图纸上，两个方向是否在管线的同一侧
            DPlane plane(cxt.uCvt.camera().frontDir(), cxt.uCvt.camera().eye());
            DVec3 prjDimDir = plane.project(this->rPt0() + this->_dimDir * 100.0);
            DVec3 prjPtFDir = plane.project(this->rPt0() + this->sPtF()->direction * 100.0);
            DVec3 prjPt0 = plane.project(this->rPt0());
            if (DVec3::Dot(prjDimDir - prjPt0, prjPtFDir - prjPt0) > 0.0)
            {
                tDir = -this->_qleadDir;
                tDis = this->_qleadMinDisN;
                qleadUParam.textBaseDistance = this->_qleadMinDis;
                qleadUParam.srcBaseDistance = cxt.style.halfPipeLineWid();
            }
            DVec3 textPos = pos + tDir * tDis;

            Texts texts;
            auto pRefNode = pipeCom.getAttribute("Cref").toNodeRef().refNode();
            if (pRefNode != nullptr)
            {
                bool isNozz = pRefNode->isType("NOZZ");
                //2024.12.28连接到TEE和OLET不能显示连接对象的名称，要显示连接对象所在的分支名称
                bool contToTe = pRefNode->isAnyOfType("TEE", "OLET");
                // 连接标识
                if (isNozz)
                    texts.push_back({ "CONT.TO", fStyle });
                else
                    texts.push_back({ "CONT.ON", fStyle });
                //如果连接到的是其他管道下的分支，就标注管道号
                bool markPipe = false;

                auto pNode = comNode();
                if (pNode != nullptr)
                    pNode = pNode->parent();
                // 连接对象名称
                if (contToTe && pRefNode->parent() != nullptr && pNode != nullptr)
                {
                    if (!isSamePipe(pRefNode->parent(), pNode))
                    {
                        markPipe = true;
                    }

                }
                else if (pRefNode != nullptr && pNode != nullptr)
                {
                    if (!isSamePipe(pRefNode, pNode))
                    {
                        markPipe = true;
                    }
                }

                if (markPipe)
                {
                    if (contToTe)
                    {
                        auto pBran = pRefNode->parent();
                        auto name = pRefNode->name();
                        if (pBran != nullptr)
                        {
                            name = pBran->name();
                            auto pPipe = pBran->parent();
                            if (pPipe != nullptr)
                                name = pPipe->name();
                        }
                        texts.push_back({ name, fStyle });
                    }
                    else
                    {
                        auto name = pRefNode->name();
                        if (pRefNode->parent() != nullptr)
                        {
                            name = pRefNode->parent()->name();
                        }
                        texts.push_back({ name, fStyle });
                    }
                }
                else if (contToTe && pRefNode->parent() != nullptr)
                {
                    auto name = pRefNode->parent()->name();
                    if (!paperInfos.empty())
                    {
                        auto it = paperInfos.find(name);
                        if (it != paperInfos.end())
                        {
                            name = it->second;
                        }
                    }
                    texts.push_back({ name, fStyle });
                }
                else if (pRefNode != nullptr)
                {
                    auto name = pRefNode->name();
                    if (!paperInfos.empty())
                    {
                        auto it = paperInfos.find(name);
                        if (it != paperInfos.end())
                        {
                            name = it->second;
                        }
                    }
                    texts.push_back({ name, fStyle });
                }
                // 图纸号
                if (isNozz) //管嘴
                {
                    // 获取到所属设备节点(EQUI)的图纸号
                    auto pEQUINode = QueryParentWithType(*pRefNode, "EQUI");
                    if (pEQUINode != nullptr)
                    {
                        auto rAttr = pEQUINode->getAttribute(cxt.style.attrNameDrawNumber);
                        auto pAttrValue = rAttr.data<std::string>();
                        if (pAttrValue != nullptr && !pAttrValue->empty())
                            texts.push_back({ *pAttrValue, fStyle });
                    }
                }
                else // 管件或者分支
                {
                    // 获取到所属分支节点(BRAN)的图纸号
                    auto pBRANNode = QueryParentWithType(*pRefNode, "BRAN");
                    if (pBRANNode != nullptr)
                    {
                        auto rAttr = pBRANNode->getAttribute(cxt.style.attrNameDrawNumber);
                        auto pAttrValue = rAttr.data<std::string>();
                        if (pAttrValue != nullptr && !pAttrValue->empty())
                            texts.push_back({ *pAttrValue, fStyle });
                    }
                }
            }

            if (UseEnuAxis)
            {
                // 坐标值 E
                CoordOutPut(buf, sizeof(buf), gPosLL.x, "E");
                texts.push_back({ buf, fStyle });
                // 坐标值 N
                CoordOutPut(buf, sizeof(buf), gPosLL.y, "N");
                texts.push_back({ buf, fStyle });
                // 坐标值 U
                CoordOutPut(buf, sizeof(buf), gPosLL.z, "EL");
                texts.push_back({ buf, fStyle });
            }
            else
            {
                // 坐标值 X
                CoordOutPut(buf, sizeof(buf), gPosLL.x, "X");
                texts.push_back({ buf, fStyle });
                // 坐标值 Y
                CoordOutPut(buf, sizeof(buf), gPosLL.y, "Y");
                texts.push_back({ buf, fStyle });
                // 坐标值 Z
                CoordOutPut(buf, sizeof(buf), gPosLL.z, "Z");
                texts.push_back({ buf, fStyle });
            }

            // 创建标记
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
                , qleadUParam);
        }
    }
    // 支管坐，需要标记连接点朝向
    else if (pipeCom.isType("OLET"))
    {
        // 添加管件标注
        {
            DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
            DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;
            Texts texts;
            if (this->number != -1)
            {
                // 标号
                sprintf_s(buf, sizeof(buf), "%d", this->number);
                texts.push_back({buf, fStyleB});
            }
            // 通径, 通径固定从P1，P3点去取
            auto pPt1 = pipeCom.keyPoint(1);
            auto pPt3 = pipeCom.keyPoint(3);
            assert(pPt1 != nullptr && pPt3 != nullptr);
            if (pPt1 != nullptr && pPt3 != nullptr)
            {
                sprintf_s(buf, sizeof(buf), "%sx%sDN", pPt1->bore().c_str(), pPt3->bore().c_str());
                texts.push_back({ buf, fStyle });
            }
            // 连接点朝向
            if (this->sPtF())
            {
                std::string strDir = DDirectionParserENU::OutputStringByDirection(this->sPtF()->direction, 1);
                texts.push_back({ strDir, fStyle });
            }
            // 创建标记
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
                , qleadUParam);
        }
        // 连接信息标注
        if (this->rPtF())
        {
            DVec3 pos = this->rPtF().value();
            DVec3 tDir = this->_qleadDir;
            double tDis = this->_qleadMinDis;
            qleadUParam.textBaseDistance = this->_qleadMinDisN;
            qleadUParam.srcBaseDistance = cxt.style.halfPipeLineWid();
            // 这里投影的目的是计算在图纸上，两个方向是否在管线的同一侧
            DPlane plane(cxt.uCvt.camera().frontDir(), cxt.uCvt.camera().eye());
            DVec3 prjDimDir = plane.project(this->rPt0() + this->_dimDir * 100.0);
            DVec3 prjPtFDir = plane.project(this->rPt0() + this->sPtF()->direction * 100.0);
            DVec3 prjPt0 = plane.project(this->rPt0());
            if (DVec3::Dot(prjDimDir - prjPt0, prjPtFDir - prjPt0) > 0.0)
            {
                tDir = -this->_qleadDir;
                tDis = this->_qleadMinDisN;
                qleadUParam.textBaseDistance = this->_qleadMinDis;
                qleadUParam.srcBaseDistance = cxt.style.halfPipeLineWid();
            }
            DVec3 textPos = pos + tDir * tDis;
            Texts texts;

            auto pRefNode = pipeCom.getAttribute("Cref").toNodeRef().refNode();
            if (pRefNode != nullptr)
            {
                bool isNozz = pRefNode->isType("NOZZ");
                //2024.12.28连接到TEE和OLET不能显示连接对象的名称，要显示连接对象所在的分支名称
                bool contToTe = pRefNode->isAnyOfType("TEE", "OLET");
                // 连接标识
                if (isNozz)
                    texts.push_back({ "CONT.TO", fStyle });
                else
                    texts.push_back({ "CONT.ON", fStyle });

                //如果连接到的是其他管道下的分支，就标注管道号
                bool markPipe = false;
                auto pNode = comNode();
                if (pNode != nullptr)
                    pNode = pNode->parent();
                // 连接对象名称
                if (contToTe && pRefNode->parent() != nullptr && pNode != nullptr)
                {
                    if (!isSamePipe(pRefNode->parent(), pNode))
                    {
                        markPipe = true;
                    }

                }
                else if (pRefNode != nullptr && pNode != nullptr)
                {
                    if (!isSamePipe(pRefNode, pNode))
                    {
                        markPipe = true;
                    }
                }

                if (markPipe)
                {
                    if (contToTe)
                    {
                        auto pBran = pRefNode->parent();
                        auto name = pRefNode->name();
                        if (pBran != nullptr)
                        {
                            name = pBran->name();
                            auto pPipe = pBran->parent();
                            if (pPipe != nullptr)
                                name = pPipe->name();
                        }
                        texts.push_back({ name, fStyle });
                    }
                    else
                    {
                        auto name = pRefNode->name();
                        if (pRefNode->parent() != nullptr)
                        {
                            name = pRefNode->parent()->name();
                        }
                        texts.push_back({ name, fStyle });
                    }
                }
                else if (contToTe && pRefNode->parent() != nullptr)
                {
                    auto name = pRefNode->parent()->name();
                    if (!paperInfos.empty())
                    {
                        auto it = paperInfos.find(name);
                        if (it != paperInfos.end())
                        {
                            name = it->second;
                        }
                    }
                    texts.push_back({ name, fStyle });
                }
                else if (pRefNode != nullptr)
                {
                    auto name = pRefNode->name();
                    if (!paperInfos.empty())
                    {
                        auto it = paperInfos.find(name);
                        if (it != paperInfos.end())
                        {
                            name = it->second;
                        }
                    }
                    texts.push_back({ name, fStyle });
                }

                // 图纸号
                if (isNozz) //管嘴
                {
                    // 获取到所属设备节点(EQUI)的图纸号
                    auto pEQUINode = QueryParentWithType(*pRefNode, "EQUI");
                    if (pEQUINode != nullptr)
                    {
                        auto rAttr = pEQUINode->getAttribute(cxt.style.attrNameDrawNumber);
                        auto pAttrValue = rAttr.data<std::string>();
                        if (pAttrValue != nullptr && !pAttrValue->empty())
                            texts.push_back({ *pAttrValue, fStyle });
                    }
                }
                else // 管件或者分支
                {
                    // 获取到所属分支节点(BRAN)的图纸号
                    auto pBRANNode = QueryParentWithType(*pRefNode, "BRAN");
                    if (pBRANNode != nullptr)
                    {
                        auto rAttr = pBRANNode->getAttribute(cxt.style.attrNameDrawNumber);
                        auto pAttrValue = rAttr.data<std::string>();
                        if (pAttrValue != nullptr && !pAttrValue->empty())
                            texts.push_back({ *pAttrValue, fStyle });
                    }
                }
            }
            if (UseEnuAxis)
            {
                // 坐标值 X
                CoordOutPut(buf, sizeof(buf), gPosLL.x, "E");
                texts.push_back({ buf, fStyle });
                // 坐标值 Y
                CoordOutPut(buf, sizeof(buf), gPosLL.y, "N");
                texts.push_back({ buf, fStyle });
                // 坐标值 Z
                CoordOutPut(buf, sizeof(buf), gPosLL.z, "EL");
                texts.push_back({ buf, fStyle });
            }
            else
            {
                // 坐标值 X
                CoordOutPut(buf, sizeof(buf), gPosLL.x, "X");
                texts.push_back({ buf, fStyle });
                // 坐标值 Y
                CoordOutPut(buf, sizeof(buf), gPosLL.y, "Y");
                texts.push_back({ buf, fStyle });
                // 坐标值 Z
                CoordOutPut(buf, sizeof(buf), gPosLL.z, "Z");
                texts.push_back({ buf, fStyle });
            }

            // 创建标记
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1 , 1
                , qleadUParam);
        }
    }
    // 大小头
    else if (pipeCom.isType("REDU"))
    {
        DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
        DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;
        Texts texts;
        if (this->number != -1)
        // 标号
        {
            sprintf_s(buf, sizeof(buf), "%d", this->number);
            texts.push_back({buf, fStyleB});
        }
        // 通径, 通径固定从P1，P2点去取
        auto pPt1 = pipeCom.keyPoint(1);
        auto pPt2 = pipeCom.keyPoint(2);
        assert(pPt1 != nullptr && pPt2 != nullptr);
        if (pPt1 != nullptr && pPt2 != nullptr)
        {
            sprintf_s(buf, sizeof(buf), "%sx%sDN", pPt1->bore().c_str(), pPt2->bore().c_str());
            texts.push_back({ buf, fStyle });
        }
        // 偏心标识
        int offValue = 0;
        auto pSPCONode = pipeCom.getAttribute("Spref").toNodeRef().refNode();
        if (pSPCONode != nullptr)
        {
            auto pSCOMNode = pSPCONode->getAttribute("Catref").toNodeRef().refNode();
            if (pSCOMNode != nullptr)
            {
                auto params = pSCOMNode->getAttribute("Param").toStringVector();
                if (params.size() >= 5)
                    sscanf(params[4].c_str(), "%d", &offValue);
            }
        }
        // 对于非偏心大小头不需要显示"0 OFFSET", 只有偏心大小头需要显示
        if (Abs(offValue) != 0)
        {
            sprintf_s(buf, sizeof(buf), "%d OFFSET", offValue);
            texts.push_back({ buf, fStyle });
        }
        // 创建标记
        cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
            , qleadUParam);
    }
    // 阀门,仪表
    else if (IsValvType(pipeCom))
    {
        QString sKey = QString::fromUtf8(this->symbolKey(pipeCom).data());
        DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
        DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;
        Texts texts;
        if (this->number != -1)
        {
            // 标号
            sprintf_s(buf, sizeof(buf), "%d", this->number);
            texts.push_back({buf, fStyleB});
        }
        {
            // 阀门手轮的方向是阀门p3点的方向
            auto pPoint = pipeCom.keyPoint(3);
            if (pPoint != nullptr)
            {
                auto point = pPoint->transformed(_pComNode->globalTransform());
                auto flowDir = this->sPtL().direction;

                // 默认以世界坐标z轴为上方向,如果管道流向和Z轴在一条线上,则使用Y作为上方向
                DVec3 zDir = DVec3::AxisZ();
                if (DVec3::OnTheSameLine(DVec3::AxisZ(), flowDir, Residual))
                    zDir = DVec3::AxisY();
                // 计算右方向,用上方向叉乘流向方向作为右方向
                DVec3 rightDir = DVec3::Normalize(DVec3::Cross(zDir, flowDir));
                auto upDir = DVec3::Normalize(WD::DVec3::Cross(flowDir, rightDir));
                // 如果p3点方向(手轮朝向)与上方向和右方向不在一条线上,即方向不是标准的轴,则需要标注朝向方向
                if (!DVec3::OnTheSameLine(rightDir, point.direction, Residual)
                    && !DVec3::OnTheSameLine(upDir, point.direction, Residual))
                {
                    auto dirStr = DirectionParserENU::OutputStringByDirection(point.direction, 1, ResidualAngle);
                    if (sKey.startsWith("id", Qt::CaseInsensitive))
                    {
                        sprintf_s(buf, sizeof(buf), "DIAL  FACE %s", dirStr.c_str());
                    }
                    else
                    {
                        sprintf_s(buf, sizeof(buf), "SPINDLE %s", dirStr.c_str());
                    }
                    texts.push_back({ buf, fStyle});
                }
            }
        }
        // 阀门名称
        if (pipeCom.isNamed())
        {
            texts.push_back({ pipeCom.name(), fStyle });
        }
        // 操控杆朝向
        // 创建标记
        cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
            , qleadUParam);
    }
    // 垫片
    else if (pipeCom.isType("GASK"))
    {
        bool bMark = true;

        // 如果前一个或后一个节点是法兰，则不标
        if (pPrevNode != nullptr && (pPrevNode->isTypedComNode("FLAN") || pPrevNode->isTypedComNode("FBLI")))
            bMark = false;
        else if (pNextNode != nullptr && (pNextNode->isTypedComNode("FLAN") || pNextNode->isTypedComNode("FBLI")))
            bMark = false;
        // 如果垫片的上一个非垫片节点有螺栓，且不是法兰，那么垫片标注螺栓
        auto pTPrvNode = pPrevNode;
        while (pTPrvNode != nullptr)
        {
            //这里跳过垫片
            if (!pTPrvNode->isTypedComNode("GASK"))
            {
                break;
            }
            pTPrvNode = pTPrvNode->pPrevNode;
        }

        bool markBolt = false;
        if (pTPrvNode != nullptr
            &&!pTPrvNode->isTypedComNode("FLAN")
            && pTPrvNode->boltNumber)
        {
            markBolt = true;
        }

        if (!pTPrvNode->isComNode()&&!pTPrvNode->isTubiNode())
        {
            //起点垫片也标一下螺栓，连在罐子上的情况
            markBolt = true;
        }

        if (bMark)
        {
            DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
            DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;
            Texts texts;
            // 标号
            if (this->number != -1)
            {
                if (markBolt && pTPrvNode != nullptr)
                {
                    char markBuff[1024] = {0};
                    if (this->boltNumber && this->nutNumber && this->washNumber)
                    {
                        sprintf_s(markBuff, sizeof(markBuff), "G%d B%d/B%d/B%d"
                            , this->number
                            , this->boltNumber.value()
                            , this->nutNumber.value()
                            , this->washNumber.value());
                    }
                    else if (this->boltNumber && this->nutNumber)
                    {
                        sprintf_s(markBuff, sizeof(markBuff), "G%d B%d/B%d"
                            , this->number
                            , this->boltNumber.value()
                            , this->nutNumber.value());
                    }
                    else if (this->boltNumber)
                    {
                        sprintf_s(markBuff, sizeof(markBuff), "G%d B%d"
                            , this->number
                            , this->boltNumber.value());
                    }
                    else
                    {
                        //如果本身没有，标注前一个的螺栓
                        if (pTPrvNode->boltNumber && pTPrvNode->nutNumber && pTPrvNode->washNumber)
                        {
                            sprintf_s(markBuff, sizeof(markBuff), "G%d B%d/B%d/B%d"
                                , this->number
                                , pTPrvNode->boltNumber.value()
                                , pTPrvNode->nutNumber.value()
                                , pTPrvNode->washNumber.value());
                        }
                        else if (pTPrvNode->boltNumber && pTPrvNode->nutNumber)
                        {
                            sprintf_s(markBuff, sizeof(markBuff), "G%d B%d/B%d"
                                , this->number
                                , pTPrvNode->boltNumber.value()
                                , pTPrvNode->nutNumber.value());
                        }
                        else if (pTPrvNode->boltNumber)
                        {
                            sprintf_s(markBuff, sizeof(markBuff), "G%d B%d"
                                , this->number
                                , pTPrvNode->boltNumber.value());
                        }
                        else
                        {
                            sprintf_s(markBuff, sizeof(markBuff), "G%d", this->number);
                        }
                    }
                    texts.push_back({markBuff, fStyleB});
                }
                else
                {
                    sprintf_s(buf, sizeof(buf), "G%d", this->number);
                    texts.push_back({buf, fStyleB});
                }
            }
            // 创建标记
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
               , qleadUParam);
        }
    }
    // 法兰
    else if (pipeCom.isAnyOfType("FLAN", "FBLI"))
    {
        // 添加管件标注
        DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
        DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;
        Texts texts;
        if (this->number != -1)
        {
            if (pipeCom.isType("FLAN"))
            {
                // 如果法兰的上一个非垫片节点没有螺栓，那么不需要标注螺栓，垫片已经标过了
                auto pTPrvNode = pPrevNode;
                while (pTPrvNode != nullptr)
                {
                    //这里跳过垫片
                    if (!pTPrvNode->isTypedComNode("GASK"))
                    {
                        break;
                    }
                    pTPrvNode = pTPrvNode->pPrevNode;
                }
                bool markBolt = true;
                //上一个是仪表且没有螺栓（没有法兰面）
                if (pTPrvNode != nullptr && pTPrvNode->isTypedComNode("INST") && !pTPrvNode->boltNumber)
                {
                    markBolt = false;
                }

                auto geneNumberBuf = [&buf] (int fNumber
                    , int gaskNumber = -1
                    , std::optional<int> boltNumberOpt = std::nullopt
                    , std::optional<int> nutNumberOpt = std::nullopt
                    , std::optional<int> washNumberOpt = std::nullopt)
                {
                    if (gaskNumber != -1)
                        sprintf_s(buf, sizeof(buf), "F%d G%d", fNumber, gaskNumber);
                    else
                        sprintf_s(buf, sizeof(buf), "F%d", fNumber);
                    //如果法兰前一个是垫片，则标注法兰垫片,但是不标注螺栓
                    if (boltNumberOpt)
                    {
                        sprintf_s(buf, sizeof(buf), "%s B%d", buf, boltNumberOpt.value());
                        if (nutNumberOpt)
                        {
                            sprintf_s(buf, sizeof(buf), "%s/B%d", buf, nutNumberOpt.value());
                            if (washNumberOpt)
                                sprintf_s(buf, sizeof(buf), "%s/B%d", buf, washNumberOpt.value());
                        }
                    }
                };

                // 如果法兰前一个是垫片，则标注法兰垫片
                if (pPrevNode != nullptr && pPrevNode->isTypedComNode("GASK"))
                {
                    //如果法兰前一个是垫片，则标注法兰垫片,但是不标注螺栓
                    if (!markBolt)
                        geneNumberBuf(this->number, pPrevNode->number);
                    else
                        geneNumberBuf(this->number, pPrevNode->number, this->boltNumber, this->nutNumber, this->washNumber);
                }
                // 如果法兰前一个是法兰，则只标注当前法兰
                else if (markBolt && pPrevNode != nullptr && pPrevNode->isTypedComNode("FLAN"))
                {
                    geneNumberBuf(this->number, -1, this->boltNumber, this->nutNumber, this->washNumber);
                }
                // 如果法兰的后一个是法兰，则只标识编号
                else if (markBolt && pNextNode != nullptr && pNextNode->isTypedComNode("FLAN"))
                {
                    geneNumberBuf(this->number);
                }
                // 如果法兰的后一个是垫片
                else if (markBolt && pNextNode != nullptr && pNextNode->isTypedComNode("GASK"))
                {
                    // 如果后一个的后一个仍然是法兰，则只标识编号
                    auto pNextNextNode = pNextNode->pNextNode;
                    if (pNextNextNode != nullptr && pNextNextNode->isTypedComNode("FLAN"))
                        geneNumberBuf(this->number);
                    // 否则标识法兰垫片
                    else
                        geneNumberBuf(this->number, pNextNode->number, this->boltNumber, this->nutNumber, this->washNumber);
                }
                // 其他情况均只标识编号
                else
                {
                    geneNumberBuf(this->number);
                }
                texts.push_back({buf, fStyleB});
            }
            else if (pipeCom.isType("FBLI"))
            {
                //盲法兰只标记序号，也不需要标F
                sprintf_s(buf, sizeof(buf), "%d", this->number);
                texts.push_back({buf, fStyleB});
            }
        }

        // 创建标记
        cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
            , qleadUParam);
    }
    // BEND 弯管, 需要标注角度
    else if (pipeCom.isType("BEND"))
    {
        DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
        DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;

        auto angle = pipeCom.getAttribute("Angle").toDouble();
        Texts texts;
        // 标号
        sprintf_s(buf, sizeof(buf), "%.1lf%s", angle, WD::WDTs("ISOTiLine", "Du").c_str());
        texts.push_back({ buf, fStyle});
        // 创建标记
        if (angle > 0.01)
        {
            cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
                , qleadUParam);
        }
    }
    // 其他管件(在线元件)
    else
    {
        DVec3 pos = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
        DVec3 textPos = pos + this->_qleadDir * this->_qleadMinDis;

        Texts texts;
        // 标号
        if (this->number != -1)
        {
            sprintf_s(buf, sizeof(buf), "%d", this->number);
            texts.push_back({buf, fStyleB});
        }
        // 名称
        if (pipeCom.isNamed())
        {
            texts.push_back({ pipeCom.name(), fStyle });
        }
        // 创建标记
        cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1
            , qleadUParam);
    }
}
void TuComNode::drawDimInfoMBD(ISODrawContext& cxt) const
{
    if (_pComNode == nullptr)
        return;

    char buf[1024] = { 0 };
    auto& pipeCom = *_pComNode;

    using Texts     = std::vector<std::pair<std::string, WDDIMFontStyle> >;
    // 弯头
    if (pipeCom.isType("ELBO"))
    {
        DVec3 tN        = DVec3::Normalize(this->_frontDir - this->frontDirNext());
        DVec3 pos       = this->rPt0() + tN * cxt.style.halfPipeLineWid();
        DVec3 textPos   = pos + tN * this->_qleadMinDis;
        Texts texts;
        // 标号
        sprintf_s(buf, sizeof(buf), "%d", this->number);
        texts.push_back({ buf, cxt.style.defCircleBorderFStyle });
        // 创建标记
        cxt.dimGen.addDIMQLeader(pos, textPos, texts,-1, 1);
    }
    else
    {
        DVec3 pos       = this->rPt0() + this->_qleadDir * cxt.style.comIconWid * 0.5;
        DVec3 textPos   = pos + this->_qleadDir * this->_qleadMinDis;

        Texts texts;
        // 标号
        sprintf_s(buf, sizeof(buf), "%d", this->number);
        texts.push_back({ buf, cxt.style.defCircleBorderFStyle });
        // 创建标记
        cxt.dimGen.addDIMQLeader(pos, textPos, texts, -1, 1);
    }
}

bool TuComNode::addATTA(WDNode::SharedPtr pATTANode)
{
    if (pATTANode == nullptr)
    {
        assert(false);
        return false;
    }
   //特殊处理，只有弯头自己加自己的支架
    if (!comNode()->isType("ELBO"))
    {
        return false;
    }
    auto atta = TuAtta(pATTANode, 0.0);
    if (atta.type() != TuAtta::AT_HANG)
    {
        return false;
    }
    _attas.push_back(atta);
    return true;
}

void TuComNode::drawWeld(ISODrawContext& cxt, WDNode& pipeCom) const
{
    /**
     * @brief 用单个连接方式获取焊缝类型的临时代码
    */
    auto getWTypeBySingleCtType = [] (const std::string& ctType, bool bShop)->ISOWeldQueryTable::RWeldType
    {
        if (_stricmp(ctType.c_str(), "BWP") == 0 || _stricmp(ctType.c_str(), "BWD") == 0)
            return bShop ? ISOWeldQueryTable::RWeldType::RWT_FactoryButt : ISOWeldQueryTable::RWeldType::RWT_SiteButt;
        else if (_stricmp(ctType.c_str(), "SWF") == 0 || _stricmp(ctType.c_str(), "SWM") == 0)
            return bShop ? ISOWeldQueryTable::RWeldType::RWT_FactorySocket : ISOWeldQueryTable::RWeldType::RWT_SiteSocket;
        else if (_stricmp(ctType.c_str(), "SCF") == 0 || _stricmp(ctType.c_str(), "SCM") == 0)
            return ISOWeldQueryTable::RWeldType::RWT_ScrewedJoint;

        return ISOWeldQueryTable::RWeldType::RWT_Unknown;
    };

    auto shop = pipeCom.getAttribute("Shop").toBool();
    // 入口点的焊点
    if (pPrevNode != nullptr)
    {
        std::string cT2 = this->cTypeA();
        DVec3 vecX = this->sPtA().direction;// _vecPtA.normalized();
        DVec3 vecY  = this->_dimDir;
        if (this->isInflection())
        {
            if (pNextNode == nullptr)
                vecY = this->_dimDir;
            else
                vecY = pPrevNode->_dimDir;
        }
        DVec3 vecZ  = DVec3::Cross(vecX, vecY).normalized();
        ISOWeldQueryTable::RWeldType wType = ISOWeldQueryTable::RWeldType::RWT_Unknown;
        // 如果上一个节点是分支头,需要特殊处理
        if (pPrevNode->isBranchHPos())
            wType = getWTypeBySingleCtType(cT2, shop);
        else
            wType  = ISOWeldQueryTable::Get().query(pPrevNode->cTypeL(), cT2, shop);
        ISOWeldQueryTable::DrawWeld(cxt, wType, this->rPtA(), vecX, vecZ);
    }
    // 出口点的焊点
    if (pNextNode != nullptr)
    {
        std::string cT1 = this->cTypeL();

        DVec3 vecX = this->sPtL().direction;// _vecPtL.normalized();
        DVec3 vecY  = this->_dimDir;
        if (this->isInflection())
            vecY = this->dimDirNext();
        DVec3 vecZ = DVec3::Cross(vecX, vecY).normalized();
        ISOWeldQueryTable::RWeldType wType = ISOWeldQueryTable::RWeldType::RWT_Unknown;
        // 如果下一个节点是分支尾,需要特殊处理
        if (pNextNode->isBranchTPos())
            wType = getWTypeBySingleCtType(cT1, shop);
        else
            wType  = ISOWeldQueryTable::Get().query(cT1, pNextNode->cTypeA(), shop);

        ISOWeldQueryTable::DrawWeld(cxt, wType, this->rPtL(), vecX, vecZ);
    }

    if (_pComNode != nullptr && _pComNode->isAnyOfType("TEE", "OLET") && _vecPtF != std::nullopt)
    {
        auto ctFork = this->cTypeF();
        auto sptF = this->rPtF();
        if (!ctFork.empty() && sptF)
        {
            const auto& sptFVal = sptF.value();
            DVec3 vecX = _vecPtF.value().normalized();
            DVec3 vecZ = DVec3::Cross(vecX, this->_dimDir).normalized();
            ISOWeldQueryTable::DrawWeld(cxt, getWTypeBySingleCtType(ctFork, shop), sptFVal, vecX, vecZ);
        }
    }

    if (_pComNode != nullptr && _pComNode->isType("CROS"))
    {
        auto ctFork = this->cTypeF();
        if (!ctFork.empty() && _vecPtF)
        {
            const auto sptFVal = _rPt0 + _vecPtF.value();
            DVec3 vecX = _vecPtF.value().normalized();
            DVec3 vecZ = DVec3::Cross(vecX, this->_dimDir).normalized();
            auto wType = getWTypeBySingleCtType(ctFork, shop);
            ISOWeldQueryTable::DrawWeld(cxt, wType, sptFVal, vecX, vecZ);

            const auto sptFNVal = _rPt0 - _vecPtF.value();
            DVec3 vecNX = -vecX;
            ISOWeldQueryTable::DrawWeld(cxt, wType, sptFNVal, vecNX, vecZ);
        }
    }
}
std::string TuComNode::symbolKey(const WDNode& pipeCom) const
{
    std::string rSKey = "";
    auto pSPCONode = pipeCom.getAttribute("Spref").toNodeRef().refNode();
    if (pSPCONode == nullptr)
        return rSKey;
    auto pSDTENode = pSPCONode->getAttribute("Detref").toNodeRef().refNode();
    if (pSDTENode == nullptr)
        return rSKey;
    return pSDTENode->getAttribute("Skey").toString();
}

/******** TuTubiNode *********/
TuTubiNode::TuTubiNode(ISOFigureLegendsMgr& figuLegMgr, WDNode::SharedPtr pNode)
    : TuNode(figuLegMgr), _pNode(pNode)
{

}

bool TuTubiNode::addATTA(WDNode::SharedPtr pATTANode)
{
    if (pATTANode == nullptr)
    {
        assert(false);
        return false;
    }
    if (_pNode == nullptr)
    {
        assert(false);
        return false;
    }
    const DVec3 sP      = this->sPos();
    const DVec3 eP      = this->ePos();

    DVec3 pos           = pATTANode->globalTranslation();
    const DVec3 nor     = (eP - sP);
    double len          = nor.length();
    const DVec3 v       = (pos - sP);
    double value = 0.1;//0.1单位毫米，std::numeric_limits<double>::min()此处不合适 
    //几乎重叠就不用验证共线了
    bool checkSameLine = v.length() > value;
    const DVec3 nV      = v.normalized();
    if (checkSameLine && !DVec3::OnTheSameLine(nor, nV, value))
        return false;
    double vLen = v.length();
    if (vLen > len)
        return false;
    // 计算附点到起点的距离与直管长度之比,用于根据直管长度确定附点对应的位置
    double posT = vLen / len;

    // 需要按照距离，插入排序
    auto fItr = std::lower_bound(_attas.begin(), _attas.end(), posT, [](const TuAtta& atta, double posT)
        {
            return atta.posT() < posT;
        });

    if (fItr == _attas.end())
        _attas.push_back(TuAtta(pATTANode, posT));
    else
        _attas.insert(fItr, TuAtta(pATTANode, posT));
    return true;
}
bool TuTubiNode::addWELD(WDNode::SharedPtr pWELDNode)
{
    if (pWELDNode == nullptr)
    {
        assert(false);
        return false;
    }
    if (_pNode == nullptr)
    {
        assert(false);
        return false;
    }
    const DVec3 sP = this->sPos();
    const DVec3 eP = this->ePos();

    DVec3 pos       = pWELDNode->globalTranslation();
    const DVec3 nor = (eP - sP);
    double len      = nor.length();
    const DVec3 v   = (pos - sP);
    const DVec3 nV  = v.normalized();
    if (!DVec3::OnTheSameLine(nor, nV, 0.1))
        return false;
    double vLen = v.length();
    if (vLen > len)
        return false;
    // 计算附点到起点的距离与直管长度之比,用于根据直管长度确定附点对应的位置
    double posT = vLen / len;

    // 需要按照距离，插入排序
    auto fItr = std::lower_bound(_welds.begin(), _welds.end(), posT
        , [](const std::pair<double, WDNode::SharedPtr>& dt, double posT)
        {
            return dt.first < posT;
        });

    if (fItr == _welds.end())
        _welds.push_back(std::make_pair(posT, pWELDNode));
    else
        _welds.insert(fItr, std::make_pair(posT, pWELDNode));

    return true;
}

DVec3 TuTubiNode::sPos() const
{
    if (_pNode == nullptr)
    {
        assert(false);
        return DVec3::Zero();
    }
    return _pNode->getAttribute("Hposition WRT World").toDVec3();
}
DVec3 TuTubiNode::ePos() const
{
    if (_pNode == nullptr)
    {
        assert(false);
        return DVec3::Zero();
    }
    return _pNode->getAttribute("Tposition WRT World").toDVec3();
}

DKeyPoint TuTubiNode::sPt0() const
{
    if (_pNode == nullptr)
        return DKeyPoint();
    return DKeyPoint((this->sPos() + this->ePos()) * 0.5, DVec3::AxisZ());
}
DKeyPoint TuTubiNode::sPtA() const
{
    if (_pNode == nullptr)
        return DKeyPoint();
    auto sP = this->sPos();
    auto eP = this->ePos();
    auto nV = (sP - eP).normalized();
    return DKeyPoint(sP, nV);
}
DKeyPoint TuTubiNode::sPtL() const
{
    if (_pNode == nullptr)
        return DKeyPoint();
    auto sP = this->sPos();
    auto eP = this->ePos();
    auto nV = (eP - sP).normalized();
    return DKeyPoint(eP, nV);
}
std::optional<DKeyPoint> TuTubiNode::sPtF() const
{
    return std::nullopt;
}

std::string TuTubiNode::cTypeA() const
{
    if (_pNode == nullptr)
        return "";
    auto pPt = _pNode->keyPoint(1);
    if (pPt == nullptr)
        return "";
    return pPt->connType();
}
std::string TuTubiNode::cTypeL() const
{
    if (_pNode == nullptr)
        return "";
    auto pPt = _pNode->keyPoint(1);
    if (pPt == nullptr)
        return "";
    return pPt->connType();
}
std::string TuTubiNode::cTypeF() const
{
    return "";
}

std::string TuTubiNode::mtotube() const
{
    auto pTNode = pPrevNode;
    while (pTNode != nullptr)
    {
        if (pTNode->isComNode() && pTNode != nullptr)
            return pTNode->mtotube();
        pTNode = pTNode->pPrevNode;
    }

    if (_pNode == nullptr || _pNode->parent() == nullptr)
        return "";
    auto val = _pNode->parent()->getAttribute("Mtohead");
    return WD::WDBMAttrValue::ToString(val);
}

bool TuTubiNode::isTubiNode() const
{
    return true;
}

DKeyPoint TuTubiNode::update(ISODrawContext& cxt, const DKeyPoint& sKeyPt)
{
    if (_pNode == nullptr)
    {
        assert(false);
        return sKeyPt;
    }
    double minWLen      = cxt.style.tubiMinLen;
    auto vec            = this->ePos() - this->sPos();
    const auto nVec     = sKeyPt.direction;
    auto vecLen         = vec.length();
    // 依次调用附点的更新
    for (auto& atta : _attas)
    {
        minWLen += atta.getDrawLength(cxt);

        // 如果附点中具有流向箭头，则不需要再绘制流向箭头了
        if (atta.type() == TuAtta::AT_FLOW)
            this->bFlowArrow = false;
    }
    QString flow = QString::fromUtf8(flowDirection().c_str());
    if (flow.contains("Off", Qt::CaseInsensitive))
    {
        this->bFlowArrow = false;
    }
    // 如果需要额外绘制流向箭头，则加上流向箭头的长度
    if (this->bFlowArrow)
        minWLen += cxt.style.pipeLineArrowLen;
    //如果是双向流向箭头，则需要再加两倍的流向箭头长度，（箭头，直线，箭头）
    if (this->bFlowArrow && flow.contains("BOTH",Qt::CaseInsensitive))
        minWLen += cxt.style.pipeLineArrowLen *2;
    //如果直管有高度差，需要增加坡度符号长度
    if (vec.y != 0&& slope)
    {
        minWLen += cxt.style.defSlopeLength;
    }
    //需要通径标识，增加长度
    if (this->bBore)
    {
        minWLen += cxt.style.pipeLineArrowLen * 2;
    }
    // 限制直管的最小和最大长度
    vecLen  = Clamp(vecLen, minWLen, cxt.style.tubiMaxLen);
    vec     = nVec * vecLen;
    auto id = WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None);
    //这里做判断，如果直管跟之前的管道有相交，那么延长上一个拐点之前的直管
    DKeyPoint tsKeyPt = sKeyPt;
    auto pOutItem = new WDDIMCollision::CItem;
    //图纸上的流向
    auto vec2D = cxt.uCvt.worldToScreen(tsKeyPt.position + vec) - cxt.uCvt.worldToScreen(tsKeyPt.position);
    while (cxt.collisionPipe.checkLines({ tsKeyPt.position ,tsKeyPt.position + vec }
        , id
        , cxt.style.pipeLineStyle
        , ISOCollision::T_Text
    , pOutItem)
       )
    {
        if (pOutItem == nullptr)
        {
            break;
        }
        else
        {
            auto p = std::get_if<ISOCollision::CSegment>(&pOutItem->object);
            if (p == nullptr)
                break; 
            auto vecPrev = (p->ePos - p->sPos).normalized();
            if (WD::DVec3::OnTheSameLine(vecPrev, nVec,0.05))
            {
                break;
            }
        }
        //跟之前的管道有相交，那么延长上一个拐点之前的直管
        auto pPrev = pPrevNode;
        while(pPrev != nullptr)
        {
            if (pPrev->isInflection())
            {
                break;
            }
            pPrev = pPrev->pPrevNode;
        }
        if (pPrev != nullptr)
        {
            auto ptA = pPrev->rPtA();
            auto pt0 = pPrev->rPt0();
            //要延长直管长度，就是改变拐点的入口点,
            //将拐点之前的直管延长当前直管的长度，就是延长rpta的距离
            ptA += (pt0 - ptA).normalized()* minWLen;
            tsKeyPt = pPrev->update(cxt, DKeyPoint(ptA, (pt0 - pPrev->rPtA()).normalized()));
            auto pNext = pPrev->pNextNode;
            while (pNext != nullptr && pNext != this)
            {
                tsKeyPt = pNext->update(cxt, tsKeyPt);
                pNext = pNext->pNextNode;
            }
        }
    }

    //将结果加入后续碰撞
    cxt.collisionPipe.addLines({ tsKeyPt.position ,tsKeyPt.position + vec }
    , id
    , cxt.style.pipeLineStyle
    , ISOCollision::T_Text);
    setRPtAL(tsKeyPt.position, tsKeyPt.position + vec);
    return DKeyPoint(tsKeyPt.position + vec, nVec);
}
void TuTubiNode::updateMBD()
{
}

void TuTubiNode::drawBase(ISODrawContext& cxt) const
{
    if (_pNode == nullptr)
    {
        assert(false);
        return ;
    }
    // 直管的起点和终点
    const auto vec      = this->rPtL() - this->rPtA();
    const auto nVec     = vec.normalized();
    const auto lenVec   = vec.length();

    // 管线样式
    auto stype   = cxt.style.pipeLineStyle;
    // 默认的填充样式
    auto sStype  = cxt.style.defSStyle;
    //管段材料列表
    QString mtocStr = mtotube().c_str();
    if (mtocStr.contains("DOTD", Qt::CaseInsensitive) || mtocStr.contains("DOTU", Qt::CaseInsensitive))
    {
        stype.type = WDDIMLineStyle::LT_Dash;
        sStype.type = WDDIMLineStyle::LT_Dash;
    }
    // 标识绘制直管的一条直线
    struct TmpLine
    {
        // 起点
        DVec3 sPos = DVec3::Zero();
        // 终点
        DVec3 ePos = DVec3::Zero();
        // 最后一个附点图形的结束点
        std::optional<DVec3> lastAttaEndPos = std::nullopt;
        DVec3 rightDir = DVec3::Zero();
    };
    using TmpLines = std::vector<TmpLine>;

    TmpLines lines;
    lines.push_back(TmpLine());

    auto GetPipeLineSideFlanPoints = [&cxt] (DVec3Vector& points, const DVec3& posS, const DVec3& posE, const DVec3 dir)
    {
        double width = cxt.style.comIconWid;
        auto pointLB = posS - dir * width / 2;
        auto pointLT = posS + dir * width / 2;
        auto pointRB = posE - dir * width / 2;
        auto pointRT = posE + dir * width / 2;

        points.push_back(pointLB);
        points.push_back(pointLT);

        points.push_back(pointLT);
        points.push_back(pointRT);

        points.push_back(pointRT);
        points.push_back(pointRB);

        points.push_back(pointRB);
        points.push_back(pointLB);
    };
    DVec3Vector flanPoints;
    if (IsNodeNeedPaintFlan(*_pNode))
    {
        double flanWidth = cxt.style.comIconLen * 0.6 * 0.25;
        double flanOffset = cxt.style.pipeLineSideFlanOffset;
        auto dir = DVec3::Normalize(this->rPtL() - this->rPtA());
        auto flanSPos = this->rPtA() + dir * flanOffset;
        auto flanEPos = flanSPos + dir * flanWidth;
        lines.back().sPos = flanEPos;
        GetPipeLineSideFlanPoints(flanPoints, flanSPos, flanEPos, this->_dimDir);
    }
    else
    {
        lines.back().sPos = this->rPtA();
    }
    lines.back().rightDir = this->_dimDir;
    // 判断是否具有保温
    bool bIspec = false;
    if (_pNode != nullptr)
    {
        bIspec = _pNode->getAttribute("Ispec").toNodeRef().valid();
    }
    // 绘制附点,并根据附点是否打断直管来分割直管管线
    for (auto& atta : _attas)
    {
        auto ret = atta.drawBase(cxt, this->rPtA(), this->rPtL(), this->_dimDir, bIspec);
        if (ret.bBreak)
        {
            lines.back().ePos = ret.sPos;
            // 新增一条直线
            lines.push_back(TmpLine());
            lines.back().sPos = ret.ePos;
            lines.back().rightDir = this->_dimDir;
        }
        else if(ret.bDraw)
        {
            // 记录当前焊点的结束点作为当前直线上最后一个焊点的结束点
            lines.back().lastAttaEndPos = ret.ePos;
        }
    }

    if (IsNodeNeedPaintFlan(*_pNode))
    {
        double flanWidth = cxt.style.comIconLen * 0.6 * 0.25;
        double flanOffset = cxt.style.pipeLineSideFlanOffset;
        auto dir = DVec3::Normalize(this->rPtA() - this->rPtL());
        auto flanEPos = this->rPtL() + dir * flanOffset;
        auto flanSPos = flanEPos + dir * flanWidth;
        lines.back().ePos = flanSPos;
        GetPipeLineSideFlanPoints(flanPoints, flanSPos, flanEPos, this->_dimDir);
    }
    else
    {
        lines.back().ePos = this->rPtL();
    }
    if (!flanPoints.empty())
    {
        // 加入碰撞计算
        cxt.collision.addLines(flanPoints
            , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None)
            , cxt.style.pipeLineFlanLineStyle);
        //
        cxt.painter.drawLines(flanPoints, cxt.style.pipeLineFlanLineStyle);
    }

    if (this->bFlowArrow)
    {
        //流向箭头长度
        auto length     = cxt.style.pipeLineArrowLen;
        auto halfLen    = length * 0.5;
        //流向箭头宽度
        auto width      = cxt.style.pipeLineArrowWid;
        auto halfWidth  = width * 0.5;

        auto up         = cxt.uCvt.camera().frontDir();
        auto right      = DVec3::Cross(up, nVec).normalized();
        for (const auto& line: lines)
        {
            // 判断当前线段是否能绘制流向箭头
            const DVec3& tSPos = line.sPos;
            const DVec3& tEPos = line.ePos;
            if (DVec3::Distance(tSPos, tEPos) <= length)
            {
                // 加入碰撞计算
                cxt.collision.addLines({line.sPos, line.ePos}
                    , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                    , stype);

                cxt.painter.drawLine(line.sPos, line.ePos, stype);
                if (bIspec)
                    DrawIspecLines(cxt, { line.sPos, line.ePos }, line.rightDir);
            }
            else
            {
                auto drawTriangle = [&](const WD::DVec3& flowDir, const WD::DVec3& start)
                {
                    auto p0 = start;
                    auto p1 = start + flowDir * halfLen*2;
                    // 箭头三角形
                    std::array<DVec3, 3> vs =
                    {
                        p0 - right * halfWidth
                        , p0 + right * halfWidth
                        , p1
                    };
                    cxt.painter.fillTriangle(vs, sStype);
                    // 流向箭头的碰撞
                    ISOCollision::CRect cRect;
                    cRect.center = (p0 + p1) * 0.5;
                    cRect.xAxis = (p1 - p0).normalized();
                    cRect.planeNormal = cxt.uCvt.camera().frontDir();
                    cRect.size = DVec2(length, width);
                    cRect.style.width = 0.0;
                    cxt.collision.addTObject(cRect
                        , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe));
                };

                auto cen = (tSPos + tEPos) * 0.5;
                auto pt0 = cen - nVec * halfLen;
                auto pt1 = cen + nVec * halfLen;
                QString flow = QString::fromUtf8(flowDirection().c_str());
                if (flow.contains("Both", Qt::CaseInsensitive))
                {
                    pt0 = cen - nVec * halfLen * 3;
                    pt1 = cen + nVec * halfLen * 3;
                }
                // 加入碰撞计算
                cxt.collision.addLines({ line.sPos, pt0 }
                    , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                    , stype);

                // 从线的起点画到箭头起点
                cxt.painter.drawLine(line.sPos, pt0, stype);
                if (bIspec)
                    DrawIspecLines(cxt, { line.sPos, pt0 }, line.rightDir);

                if (flow.contains("Both", Qt::CaseInsensitive))
                {
                    //双向
                    auto end = pt0 + nVec * halfLen*2;
                    auto start = pt1 - nVec * halfLen*2;
                    drawTriangle(-nVec, end);
                    cxt.painter.drawLine(start, end, cxt.style.pipeLineStyle);
                    drawTriangle(nVec, start);
                }
                else if (flow.contains("Back", Qt::CaseInsensitive))
                {
                    //反向
                    drawTriangle(-nVec, pt1);
                }
                else
                {
                    //正向
                    drawTriangle(nVec, pt0);
                }
                // 加入碰撞计算
                cxt.collision.addLines({ pt1, line.ePos }
                    , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                    , stype);

                // 箭头终点画到线的终点
                cxt.painter.drawLine(pt1, line.ePos, stype);
                if (bIspec)
                    DrawIspecLines(cxt, { pt1, line.ePos }, line.rightDir);
            }
        }
    }
    else
    {
        for (const auto& line: lines)
        {
            // 加入碰撞计算
            cxt.collision.addLines({ line.sPos, line.ePos }
                , WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_Pipe)
                , stype);

            cxt.painter.drawLine(line.sPos, line.ePos, stype);
            if (bIspec)
                DrawIspecLines(cxt, { line.sPos, line.ePos }, line.rightDir);
        }
    }

    // 画所有的焊点
    for (const auto& weld : _welds)
    {
        auto pWELDNode = weld.second;
        if (pWELDNode == nullptr)
            continue;
        auto pPt = pWELDNode->keyPoint(1);
        if (pPt == nullptr)
            continue;
        double posT             = weld.first;
        // 获取当前所在直管的连接形式
        std::string cT1         = this->cTypeA();
        const std::string& cT2  = pPt->connType();
        // 这里均为现场焊
        auto wType = ISOWeldQueryTable::Get().query(cT1, cT2, false);
        //
        DVec3 vecX  = -(this->sPtA().direction);
        DVec3 vecY  = this->_dimDir;
        DVec3 vecZ  = DVec3::Cross(vecX, vecY).normalized();
        DVec3 pos   = this->rPtA() + nVec * lenVec * posT;

        ISOWeldQueryTable::DrawWeld(cxt, wType, pos, vecX, vecZ);
    }
}
void TuTubiNode::drawDimLinear(ISODrawContext& cxt) const
{
    if (_pNode == nullptr)
    {
        assert(false);
        return;
    }

    //管段材料列表
    QString mtocStr = mtotube().c_str();
    if (mtocStr.contains("DOTU", Qt::CaseInsensitive))
    {
        return;
    }

    const auto rA   = this->rPtA();
    const auto rL   = this->rPtL();
    const auto sA   = sPtA().position;
    const auto sL   = sPtL().position;

    std::pair<DVec3, DVec3> dimPtS;
    // 第二层标注，用于标识直管长度
    if (pPrevNode != nullptr
        && pPrevNode->isComNode()
        && !pPrevNode->isTypedComNode("GASK")
        && DVec3::DistanceSq(pPrevNode->sPtA().position, pPrevNode->sPtL().position) <= 0.001
        && pPrevNode->pPrevNode != nullptr 
        && !(pPrevNode->pPrevNode->isTubiNode()))
    {
        dimPtS.first    = pPrevNode->rPtA();
        dimPtS.second   = pPrevNode->sPtA().position;
    }
    else
    {
        dimPtS.first    = rA;
        dimPtS.second   = sA;
    }
    
    if (pNextNode != nullptr
        && pNextNode->isComNode()
        && !pNextNode->isTypedComNode("GASK")
        && DVec3::DistanceSq(pNextNode->sPtA().position, pNextNode->sPtL().position) <= 0.001)
    {
        const auto& tRL = pNextNode->rPtL();
        const auto tSL  = pNextNode->sPtL().position;
        cxt.dimGen.addDIMLinearT2(this->_dimDir
            , dimPtS.first
            , tRL
            , DVec3::Distance(dimPtS.second, tSL)
            , this);
    }
    else
    {
        cxt.dimGen.addDIMLinearT2(this->_dimDir
            , dimPtS.first
            , rL
            , DVec3::Distance(dimPtS.second, sL)
            , this);
    }

    // 第一层标注，用于标识穿墙、支架点等信息
    dimPtS = { rA, sA };
    // 第一层标注信息只从附着点上拿
    for (const auto& attr : _attas)
    {
        if (!attr.needDimLinear())
            continue;
        const auto rAttPos = attr.position(rA, rL);
        const auto sAttPos = attr.position(sA, sL);

        cxt.dimGen.addDIMLinearT1(this->_dimDir
            , dimPtS.first
            , rAttPos
            , DVec3::Distance(dimPtS.second, sAttPos)
            , this);

        dimPtS = { rAttPos , sAttPos};
    }
}
void TuTubiNode::drawDimLinearMBD(ISODrawContext& cxt) const
{
    if (_pNode == nullptr)
    {
        assert(false);
        return;
    }

    const auto rA = this->rPtA();
    const auto rL = this->rPtL();
    const auto sA = sPtA().position;
    const auto sL = sPtL().position;

    std::pair<DVec3, DVec3> dimPtS;
    // 第二层标注，用于标识直管长度
    if (pPrevNode != nullptr
        && pPrevNode->isComNode()
        && !pPrevNode->isTypedComNode("GASK")
        && DVec3::DistanceSq(pPrevNode->sPtA().position, pPrevNode->sPtL().position) <= 0.001
        && pPrevNode->pPrevNode != nullptr
        && !(pPrevNode->pPrevNode->isTubiNode()))
    {
        dimPtS.first = pPrevNode->rPtA();
        dimPtS.second = pPrevNode->sPtA().position;
    }
    else
    {
        dimPtS.first = rA;
        dimPtS.second = sA;
    }

    if (pNextNode != nullptr
        && pNextNode->isComNode()
        && !pNextNode->isTypedComNode("GASK")
        && DVec3::DistanceSq(pNextNode->sPtA().position, pNextNode->sPtL().position) <= 0.001)
    {
        const auto& tRL = pNextNode->rPtL();
        const auto tSL = pNextNode->sPtL().position;
        cxt.dimGen.addDIMLinearT2(this->_dimDir
            , dimPtS.first
            , tRL
            , DVec3::Distance(dimPtS.second, tSL)
            , this);
    }
    else
    {
        cxt.dimGen.addDIMLinearT2(this->_dimDir
            , dimPtS.first
            , rL
            , DVec3::Distance(dimPtS.second, sL)
            , this);
    }
}

void TuTubiNode::drawDimInfo(ISODrawContext& cxt, const std::unordered_map<std::string, std::string>& paperInfos) const
{
    WDUnused(paperInfos);
    if (_pNode == nullptr)
    {
        assert(false);
        return;
    }

    const auto& uCvt    = cxt.uCvt;
    const auto& fStyleB = cxt.style.defBorderFStyle;
    const auto& fStyle  = cxt.style.defFStyle;

    // 这里计算直管上 标文字的上方向和右方向
    DPlane plane(uCvt.camera().frontDir(), uCvt.camera().eye());
    auto prjPtA     = plane.project(this->rPtA());
    auto prjPtL     = plane.project(this->rPtL());

    DVec3 tmpRight  = (prjPtL - prjPtA).normalized();
    DVec3 tmpUp     = this->_rightDir;
    auto tmpDot     = DVec3::Dot(tmpRight, uCvt.camera().upDir());
    if (Abs(1.0 - Abs(tmpDot)) <= 0.01)
    {
        tmpUp       = -uCvt.camera().rightDir();
        tmpRight    = uCvt.camera().upDir();
    }
    else
    {
        DVec3 tmpFront  = DVec3::Cross(uCvt.camera().upDir(), tmpRight);
        tmpUp           = DVec3::Cross(tmpRight, tmpFront).normalized();
        if (DVec3::Dot(tmpFront, uCvt.camera().frontDir()) < 0.0)
            tmpRight    = -tmpRight;
    }
    // 判断是否具有保温
    bool bIspec = false;
    if (_pNode != nullptr)
    {
        bIspec = _pNode->getAttribute("Ispec").toNodeRef().valid();
    }   

    //先画斜率符号，这个是必须在管线上的
    //再依次看有没有位置放 管径、管道号，有位置就放管线上，

    char buf[1024] = { 0 };
    auto avoidWidth = cxt.uCvt.worldToPaper(prjPtL - prjPtA).length();
    // 标识坡度
    if (this->slope&& this->slope.value() > 0.00001)
    {
        //坡度单独构造一个avoidSlope，原因：编号和通径都是每个直管都可能有，而坡度标识在拐点之前只有一个
        //找到前面的拐点的出口点
        auto tempPtA = WD::DVec3::Zero();
        auto tempPtL = WD::DVec3::Zero();
        auto pTPrvNode = pPrevNode;
        while (pTPrvNode != nullptr)
        {
            if (pTPrvNode->isInflection())
            {
                tempPtL = pTPrvNode->rPtL();
                break;
            }
            pTPrvNode = pTPrvNode->pPrevNode;
        }
        //找到后面的拐点的入口点
        auto pTNextNode = pPrevNode;
        while (pTNextNode != nullptr)
        {
            if (pTNextNode->isInflection())
            {
                tempPtA = pTNextNode->rPtA();
                break;
            }
            pTNextNode = pTNextNode->pPrevNode;
        }
        auto avoidWidthLeft = cxt.uCvt.worldToPaper((tempPtL - (this->rPtA() + this->rPtL()) * 0.5).length());
        auto avoidWidthRight = cxt.uCvt.worldToPaper((tempPtA - (this->rPtA() + this->rPtL()) * 0.5).length());
        // 坡度小三角的斜边点, 需要计算斜边点在那边
        bool bTmpLeft = this->sPtA().position.z > this->sPtL().position.z;

        // 坡度值
        sprintf_s(buf, sizeof(buf), "SLOPE %0.2lf%%", this->slope.value() * 100.0);
        auto nVec = this->rPtL() - this->rPtA();
        if (nVec.lengthSq() >= NumLimits<float>::Epsilon)
        {
            nVec = nVec.normalized();
            if (DVec3::Dot(nVec, uCvt.camera().rightDir()) < -0.01)
            {
                nVec = -nVec;
                bTmpLeft = !bTmpLeft;
            }
        }
        // 标注坡度小三角直角边的起点
        DVec3 triPt0 = (this->rPtA() + this->rPtL()) * 0.5 + tmpUp * cxt.style.tubiNumberDisToCen();
        // 标注坡度小三角直角边的终点
        DVec3 triPt1 = triPt0 + nVec * cxt.style.defSlopeLength;
        DVec3 triPt2 = (bTmpLeft ? triPt0 : triPt1) + tmpUp * cxt.style.defSlopeHeight;
        // 添加标注
        DVec3 valuePos = (bTmpLeft ? triPt1 : triPt0);// triPt1 + nVec * cxt.uCvt.paperToWorld(0.5) - tmpUp * cxt.style.tubiNumberDisToCen();
        DVec3 textPos = valuePos;
        //计算文字长度
        auto length = cxt.painter.calcTextSize(buf, cxt.style.defFStyle).x;
        //初始化时也要考虑文字长度
        PipeIdentifierAvoid avoidSlope(cxt.uCvt.paperToWorld(1.0), cxt.uCvt.paperToWorld(1.0)
            , 1
            , (avoidWidthLeft - cxt.uCvt.worldToPaper(length)) / 1.0
            , (avoidWidthRight - cxt.uCvt.worldToPaper(length))  / 1.0);
        avoidSlope.init(textPos,tmpUp ,nVec);
        bool bOk = false;
        int outQLeadIdx = 0;
        auto id = WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None);
        // 避让计算, 计算三角形和文字是否与其他对象交叉
        bOk = avoidSlope.tryTo([this
            , &nVec
            , &bTmpLeft
            , &triPt0
            , &triPt1
            , &triPt2
            , &id
            , &buf
            , &textPos
            , tmpRight
            , tmpUp
            , &cxt
            ,&length
            , &outQLeadIdx](const DVec3& newPos)->bool
            {
                triPt0 = newPos;
                triPt1 = triPt0 + nVec * cxt.uCvt.paperToWorld(5.0);
                triPt2 = (bTmpLeft ? triPt0 : triPt1) + tmpUp * cxt.uCvt.paperToWorld(2.0);
                textPos = (bTmpLeft ? triPt1 : triPt0);
                // 文本碰撞对象
                WDDIMCollision::CText textObject;
                if (!bTmpLeft)
                {
                    //当符号右边高，文字在符号左边，那么需要减去nVec*文字长度
                    textPos -= nVec * length - nVec * cxt.style.defSlopeLength;
                }
                else
                {
                    //当符号左边高，文字在符号右边，那么需要加上nVec*文字长度
                    textPos += nVec * length - nVec * cxt.style.defSlopeLength;
                }
                textObject.position = textPos;
                textObject.text = buf;
                textObject.style = cxt.style.defFStyle;
                textObject.rightDir = tmpRight;
                textObject.upDir = tmpUp;
                textObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
                // 校验文字
                if (cxt.collision.checkObject({ textObject,id }))
                    return false;
                if (cxt.collision.checkLoopLine({ triPt0, triPt1, triPt2 }, id, cxt.style.defLStyle))
                    return false;
                return true;
            });
        if (bOk)
        {
            cxt.painter.drawLoopLine({ triPt0, triPt1, triPt2 }, cxt.style.defLStyle);
            cxt.collision.addLoopLine({ triPt0, triPt1, triPt2 }, id, cxt.style.defLStyle);
            // 添加标注

            //有位置就放符号旁边，没有就引线
            cxt.dimGen.addDIMQLeader(valuePos
                , textPos
                , tmpRight
                , tmpUp
                , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Bottom }
                , { {buf, fStyle} }
                , id
                , -1);
            WDDIMCollision::CText tempObject;
            tempObject.position = textPos;
            tempObject.text = buf;
            tempObject.style = fStyleB;
            tempObject.rightDir = tmpRight;
            tempObject.upDir = tmpUp;
            tempObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
            cxt.collision.addObject({ tempObject,id });
        }
        else
        {
            avoidSlope.reAllocation(cxt.uCvt.paperToWorld(1.0), cxt.uCvt.paperToWorld(1.0)
                , 1
                , (avoidWidthLeft - cxt.uCvt.worldToPaper(cxt.style.defSlopeLength)) / 1.0
                , (avoidWidthRight - cxt.uCvt.worldToPaper(cxt.style.defSlopeLength)) / 1.0);
            //这里减五毫米是三角形的长度，临时写在这里
            triPt0 = (this->rPtA() + this->rPtL()) * 0.5 + nVec * 1.2 + tmpUp * cxt.style.tubiNumberDisToCen();
            avoidSlope.init(triPt0, tmpUp, nVec);
            // 标注坡度小三角直角边的终点
            triPt1 = triPt0 + nVec * cxt.style.defSlopeLength;
            triPt2 = (bTmpLeft ? triPt0 : triPt1) + tmpUp * cxt.style.defSlopeHeight;
            // 避让计算, 计算三角形和文字是否与其他对象交叉
            bOk = avoidSlope.tryTo([this
                , &nVec
                , &bTmpLeft
                , &triPt0
                , &triPt1
                , &triPt2
                , &id
                , tmpUp
                , &cxt
                , &outQLeadIdx](const DVec3& newPos)->bool
                {
                    triPt0 = newPos;
                    triPt1 = triPt0 + nVec * cxt.style.defSlopeLength;
                    triPt2 = (bTmpLeft ? triPt0 : triPt1) + tmpUp * cxt.uCvt.paperToWorld(2.0);
                    //这里只校验三角形
                    if (cxt.collision.checkLoopLine({ triPt0, triPt1, triPt2 }, id, cxt.style.defLStyle))
                        return false;
                    return true;
                });
            cxt.painter.drawLoopLine({ triPt0, triPt1, triPt2 }, cxt.style.defLStyle);
            cxt.collision.addLoopLine({ triPt0, triPt1, triPt2 }, id, cxt.style.defLStyle);
            // 添加标注
            ISODIMGenerate::QLeaderUParam qleadUParam;
            //qleadUParam.baseLine = DSegment3(tempPtL, tempPtA);
            qleadUParam.rightDir = tmpRight;
            valuePos = (triPt2 + (bTmpLeft ? triPt1 : triPt0)) / 2;// triPt1 + nVec * cxt.uCvt.paperToWorld(0.5) - tmpUp * cxt.style.tubiNumberDisToCen();
            textPos = valuePos + tmpUp * cxt.style.tubiNumberDisToCen();
            cxt.dimGen.addDIMQLeader(valuePos
                , textPos
                , uCvt.camera().rightDir()
                , uCvt.camera().upDir()
                , { WDDIMAlign::HA_Left, WDDIMAlign::VA_Bottom }
                , { {buf, fStyle} }
                , id
                , 1
            , qleadUParam);
        }
    }
    auto nVec = (rPtL() - rPtA()).normalized();
    // 标识直管编号
    {
        // 标号
        sprintf_s(buf, sizeof(buf), "%d", this->number);
        // 添加标注
        const DVec3 pos = (this->rPtA() + this->rPtL()) * 0.5 - nVec * cxt.painter.calcTextSize(buf, cxt.style.defBorderFStyle).x;
        DVec3 textPos = pos + tmpUp * cxt.style.tubiNumberDisToCen();
        PipeIdentifierAvoid avoidDoc(cxt.uCvt.paperToWorld(1.0), cxt.uCvt.paperToWorld(1.0), 1, 0, avoidWidth);
        avoidDoc.init(textPos, tmpUp, nVec);
        bool bOk = false;
        int outQLeadIdx = 0;
        auto id = WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None);
        // 避让计算, 计算文字是否与其他对象交叉
        bOk = avoidDoc.tryTo([this
            , &id
            , &buf
            , &textPos
            , tmpRight
            , tmpUp
            , &cxt
            , &outQLeadIdx](const DVec3& newPos)->bool
            {
                // 文本碰撞对象
                WDDIMCollision::CText textObject;
                textObject.position = newPos;
                textObject.text = buf;
                textObject.style = cxt.style.defBorderFStyle;
                textObject.rightDir = tmpRight;
                textObject.upDir = tmpUp;
                textObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
                // 校验文字
                if (cxt.collision.checkObject({ textObject,id }))
                    return false;
                textPos = newPos;
                return true;
            });
        if (bOk)
        {
            cxt.dimGen.addDIMQLeader(pos
                , textPos
                , tmpRight
                , tmpUp
                , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Bottom }
                , { {buf, fStyleB} }
                , id
                , -1);
            WDDIMCollision::CText tempObject;
            tempObject.position = textPos;
            tempObject.text = buf;
            tempObject.style = cxt.style.defBorderFStyle;
            tempObject.rightDir = tmpRight;
            tempObject.upDir = tmpUp;
            tempObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
            cxt.collision.addObject({ tempObject,id });
        }
        else 
        {
            avoidDoc.reAllocation(cxt.uCvt.paperToWorld(1.0), cxt.uCvt.paperToWorld(1.0), 1, avoidWidth, 0);
            avoidDoc.init(textPos, tmpUp, nVec);
            // 避让计算, 计算文字是否与其他对象交叉
            bOk = avoidDoc.tryTo([this
                , &id
                , &buf
                , &textPos
                , tmpRight
                , tmpUp
                , &cxt
                , &outQLeadIdx](const DVec3& newPos)->bool
                {
                    // 文本碰撞对象
                    WDDIMCollision::CText textObject;
                    textObject.position = newPos;
                    textObject.text = buf;
                    textObject.style = cxt.style.defBorderFStyle;
                    textObject.rightDir = tmpRight;
                    textObject.upDir = tmpUp;
                    textObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
                    // 校验文字
                    if (cxt.collision.checkObject({ textObject,id }))
                        return false;
                    textPos = newPos;
                    return true;
                });
            if (bOk)
            {
                cxt.dimGen.addDIMQLeader(pos
                    , textPos
                    , tmpRight
                    , tmpUp
                    , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Bottom }
                    , { {buf, fStyleB} }
                    , id
                    , -1);
                WDDIMCollision::CText tempObject;
                tempObject.position = textPos;
                tempObject.text = buf;
                tempObject.style = cxt.style.defBorderFStyle;
                tempObject.rightDir = tmpRight;
                tempObject.upDir = tmpUp;
                tempObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
                cxt.collision.addObject({ tempObject,id });
            }
            else
            {
                avoidDoc.reAllocation(cxt.uCvt.paperToWorld(1.0), cxt.uCvt.paperToWorld(1.0), 1, 0, avoidWidth);
                // 镜像避让计算, 计算文字是否与其他对象交叉
                auto tempTextPos = pos - tmpUp * cxt.style.tubiNumberDisToCen();
                //切记重新init
                avoidDoc.init(tempTextPos, -tmpUp, nVec);
                bOk = avoidDoc.tryTo([this
                    , &id
                    , &buf
                    , &tempTextPos
                    , tmpRight
                    , tmpUp
                    , &cxt
                    , &outQLeadIdx](const DVec3& newPos)->bool
                    {
                        // 文本碰撞对象
                        WDDIMCollision::CText textObject;
                        textObject.position = newPos;
                        textObject.text = buf;
                        textObject.style = cxt.style.defBorderFStyle;
                        textObject.rightDir = tmpRight;
                        textObject.upDir = tmpUp;
                        textObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Top };
                        // 校验文字
                        if (cxt.collision.checkObject({ textObject,id }))
                            return false;
                        tempTextPos = newPos;
                        return true;
                    });
                if (bOk)
                {
                    cxt.dimGen.addDIMQLeader(pos
                        , tempTextPos
                        , tmpRight
                        , tmpUp
                        , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Top }
                        , { {buf, fStyleB} }
                        , id
                        , -1);
                    WDDIMCollision::CText tempObject;
                    tempObject.position = tempTextPos;
                    tempObject.text = buf;
                    tempObject.style = cxt.style.defBorderFStyle;
                    tempObject.rightDir = tmpRight;
                    tempObject.upDir = tmpUp;
                    tempObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Top };
                    cxt.collision.addObject({ tempObject,id });
                }
                else
                {
                    avoidDoc.reAllocation(cxt.uCvt.paperToWorld(1.0), cxt.uCvt.paperToWorld(1.0), 1, avoidWidth, 0);
                    // 镜像避让计算, 计算文字是否与其他对象交叉
                    tempTextPos = pos - tmpUp * cxt.style.tubiNumberDisToCen();
                    //切记重新init
                    avoidDoc.init(tempTextPos, -tmpUp, nVec);
                    bOk = avoidDoc.tryTo([this
                        , &id
                        , &buf
                        , &tempTextPos
                        , tmpRight
                        , tmpUp
                        , &cxt
                        , &outQLeadIdx](const DVec3& newPos)->bool
                        {
                            // 文本碰撞对象
                            WDDIMCollision::CText textObject;
                            textObject.position = newPos;
                            textObject.text = buf;
                            textObject.style = cxt.style.defBorderFStyle;
                            textObject.rightDir = tmpRight;
                            textObject.upDir = tmpUp;
                            textObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Top };
                            // 校验文字
                            if (cxt.collision.checkObject({ textObject,id }))
                                return false;
                            tempTextPos = newPos;
                            return true;
                        });
                    if (bOk)
                    {
                        cxt.dimGen.addDIMQLeader(pos
                            , tempTextPos
                            , tmpRight
                            , tmpUp
                            , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Top }
                            , { {buf, fStyleB} }
                            , id
                            , -1);
                        WDDIMCollision::CText tempObject;
                        tempObject.position = tempTextPos;
                        tempObject.text = buf;
                        tempObject.style = cxt.style.defBorderFStyle;
                        tempObject.rightDir = tmpRight;
                        tempObject.upDir = tmpUp;
                        tempObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Top };
                        cxt.collision.addObject({ tempObject,id });
                    }
                    else
                    {
                        ISODIMGenerate::QLeaderUParam qleadUParam;
                        qleadUParam.baseLine = DRay(rPt0(), (rPtL() - rPt0()).normalized());
                        qleadUParam.rightDir = tmpRight;
                        textPos = pos + tmpUp * cxt.style.tubiNumberDisToCen();
                        cxt.dimGen.addDIMQLeader(pos
                            , textPos
                            , uCvt.camera().rightDir()
                            , uCvt.camera().upDir()
                            , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Bottom }
                            , { {buf, fStyleB} }
                            , id
                            , 1
                            , qleadUParam);

                    }
                }
            }
        }

    }

    // 标识通径
    if (this->bBore)
    {
        // 管径
        sprintf_s(buf, sizeof(buf), "%dDN", _pNode->getAttribute("Lbore").convertToInt());
        // 添加标注
        DVec3 pos = this->rPtA() + nVec * cxt.painter.calcTextSize(buf, cxt.style.defBorderFStyle).x;
        DVec3 textPos = pos + tmpUp * cxt.style.tubiBoreDisToCen();
        WD::DVec2 numberSize = cxt.painter.calcTextSize(buf, fStyleB);

        PipeIdentifierAvoid avoidDoc(cxt.uCvt.paperToWorld(1.0), cxt.uCvt.paperToWorld(1.0), 1, 0, avoidWidth);
        avoidDoc.init(textPos, tmpUp, nVec);
        bool bOk = false;
        int outQLeadIdx = 0;
        auto id = WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None);
        // 避让计算, 计算文字是否与其他对象交叉
        bOk = avoidDoc.tryTo([this
            , &id
            , &buf
            , &textPos
            , tmpRight
            , tmpUp
            , &cxt
            , &outQLeadIdx](const DVec3& newPos
                )->bool
            {
                // 文本碰撞对象
                WDDIMCollision::CText textObject;
                textObject.position = newPos;
                textObject.text = buf;
                textObject.style = cxt.style.defBorderFStyle;
                textObject.rightDir = tmpRight;
                textObject.upDir = tmpUp;
                textObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
                // 校验文字
                if (cxt.collision.checkObject({ textObject,id }))
                    return false;
                textPos = newPos;
                return true;
            });

        if (bOk)
        {
            cxt.dimGen.addDIMQLeader(pos
                , textPos
                , tmpRight
                , tmpUp
                , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Bottom }
                , { {buf, fStyleB} }
                , id
                , -1);
            WDDIMCollision::CText tempObject;
            tempObject.position = textPos;
            tempObject.text = buf;
            tempObject.style = cxt.style.defBorderFStyle;
            tempObject.rightDir = tmpRight;
            tempObject.upDir = tmpUp;
            tempObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Bottom };
            cxt.collision.addObject({ tempObject,id });
        }
        else
        {
            // 镜像避让计算, 计算文字是否与其他对象交叉
            auto tempTextPos = pos - tmpUp * cxt.style.tubiNumberDisToCen();
            //切记重新init
            avoidDoc.init(tempTextPos, -tmpUp, nVec);
            bOk = avoidDoc.tryTo([this
                , &id
                , &buf
                , &tempTextPos
                , tmpRight
                , tmpUp
                , &cxt
                , &outQLeadIdx](const DVec3& newPos)->bool
                {
                    // 文本碰撞对象
                    WDDIMCollision::CText textObject;
                    textObject.position = newPos;
                    textObject.text = buf;
                    textObject.style = cxt.style.defBorderFStyle;
                    textObject.rightDir = tmpRight;
                    textObject.upDir = tmpUp;
                    textObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Top };
                    // 校验文字
                    if (cxt.collision.checkObject({ textObject,id }))
                        return false;
                    tempTextPos = newPos;
                    return true;
                });
            if (bOk)
            {
                cxt.dimGen.addDIMQLeader(pos
                    , tempTextPos
                    , tmpRight
                    , tmpUp
                    , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Top }
                    , { {buf, fStyleB} }
                    , id
                    , -1);
                WDDIMCollision::CText tempObject;
                tempObject.position = tempTextPos;
                tempObject.text = buf;
                tempObject.style = cxt.style.defBorderFStyle;
                tempObject.rightDir = tmpRight;
                tempObject.upDir = tmpUp;
                tempObject.textAlign = { WDDIMAlign::HAlign::HA_Center, WDDIMAlign::VAlign::VA_Top };
                cxt.collision.addObject({ tempObject,id });

            }
            else
            {
                ISODIMGenerate::QLeaderUParam qleadUParam;
                qleadUParam.baseLine = DRay(rPt0(), (rPtL() - rPt0()).normalized());
                qleadUParam.rightDir = tmpRight;
                cxt.dimGen.addDIMQLeader(pos
                    , textPos
                    , uCvt.camera().rightDir()
                    , uCvt.camera().upDir()
                    , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Bottom }
                    , { {buf, fStyleB} }
                    , id
                    , 1
                , qleadUParam);
            }
        }
    }

    // 标识支架和击穿建筑信息
    for (const auto& atta : _attas)
    {
        if (atta.attaNode() == nullptr)
        {
            assert(false);
            continue;
        }
        atta.drawDimInfo(cxt, this->rPtA(), this->rPtL(), _rightDir, _qleadDir, _qleadMinDis, bIspec);
    }
}
void TuTubiNode::drawDimInfoMBD(ISODrawContext& cxt) const
{
    // 标识直管编号
    char buf[1024] = { 0 };
    {
        // 标号
        sprintf_s(buf, sizeof(buf), "%d", this->number);
        // 添加标注
        const DVec3 pos     = (this->rPtA() + this->rPtL()) * 0.5;
        const DVec3 textPos = pos + this->_rightDir * cxt.uCvt.paperToWorld(8.0);

        cxt.dimGen.addDIMQLeader(pos
            , textPos
            , cxt.uCvt.camera().rightDir()
            , cxt.uCvt.camera().upDir()
            , { WDDIMAlign::HA_Center, WDDIMAlign::VA_Bottom }
            , { {buf, cxt.style.defBorderFStyle} }
            , 1);
    }
}

std::string TuTubiNode::flowDirection() const
{
    if (_pNode == nullptr || _pNode->parent() == nullptr)
        return "";
    auto val = _pNode->parent()->getAttribute("Flowdirection");
    return WD::WDBMAttrValue::ToString(val);
}

/**
 * @brief 用来给管件编号
*/
class TypeNumberGen
{
private:
    using TuNodes = std::vector<TuNode*>;
    // 节点类型以及TuNode
    using TypeTuNodes = std::pair<std::string, TuNodes>;
    using TypesTuNodes = std::vector<TypeTuNodes>;
    // 数组下标0存放的是 bShop为false的管件
    // 数组下标1存放的是 bShop为true的管件
    std::array<TypesTuNodes, 2> _typesTuNodes;

    using SPCONumberMap = std::map<WDNode::SharedPtr, int>;
    std::array<std::map<std::string, SPCONumberMap>, 2> _numberMap;
public:
    TypeNumberGen()
    {
        auto types = GetAllPipecomsTypeByNumberPriority();

        for (const auto& type : types)
        {
            _typesTuNodes[0].push_back({type, {}});
        }

        for (const auto& typeTuNodes : _typesTuNodes[0])
        {
            _numberMap[0][typeTuNodes.first] = {};
        }
        _typesTuNodes[1] = _typesTuNodes[0];
        _numberMap[1] = _numberMap[0];
    }
public:
    void gen(WDCore& core, const std::vector<TuNode*>& nodes)
    {
        // 添加到列表中
        for (auto pNode : nodes)
        {
            if (pNode == nullptr)
                continue;
            if (pNode->isTubiNode())
            {
                this->addTypeNode(pNode, "TUBI", true);
            }
            else if (pNode->isComNode())
            {
                TuComNode* pCNode = static_cast<TuComNode*>(pNode);
                this->addTypeNode(pCNode);
            }
        }
        // 生成编号
        int lastNumber = 1;
        for (size_t i = 0; i < _typesTuNodes.size(); ++i)
        {
            for (const auto& typeTuNodes : _typesTuNodes[i])
            {
                SPCONumberMap& map = _numberMap[i][typeTuNodes.first];
                for (auto pNode : typeTuNodes.second)
                {
                    if (pNode == nullptr)
                        continue;
                    std::string mtoStr;
                    if (pNode->isComNode())
                    {
                        TuComNode* pTNode = static_cast<TuComNode*>(pNode);
                        if (pTNode != nullptr && pTNode->comNode() != nullptr)
                            mtoStr = pTNode->comNode()->getAttribute("Mtocomponent").convertToString();
                    }
                    else if (pNode->isTubiNode())
                        mtoStr = pNode->mtotube();
                    // 编号时跳过不出料的节点
                    if (!mtoStr.empty() && _stricmp(mtoStr.c_str(), "ON") != 0 && _stricmp(mtoStr.c_str(), "unset") != 0)
                        continue;

                    auto pSPCONode = this->getSPCONode(pNode);
                    auto fItr = map.find(pSPCONode);
                    if (fItr != map.end())
                    {
                        pNode->number = fItr->second;
                    }
                    else
                    {
                        pNode->number = lastNumber;
                        map[pSPCONode] = lastNumber;
                        lastNumber++;
                    }
                }
            }
        }

        // 继续为螺栓编号
        {
            struct BoltNodeInfo
            {
                TuComNode* gask = nullptr;
                std::vector<WD::WDNode::SharedPtr> nodes;
            };
            std::vector<BoltNodeInfo> boltInfos;
            // 螺栓查找规则
            // 1,从垫片开始,向前后两个方向查找法兰或带法兰的节点(即阀门)
            // 2.向前查找时,如果当前分支没有法兰或者带法兰的节点,继续向上一个分支(即头连接(Href)的节点(所在分支)/(管嘴))查找
            //  如果上一个分支查找完成也没有则将长度置0,日志报错,结束查找
            // 
            // 3.向后查找时,如果当前分支没有法兰或者带法兰的节点,继续向下一个分支(即尾连接(Tref)的节点(所在分支)/(管嘴))查找
            //  如果下一个分支查找完成也没有则将长度置0,日志报错,结束查找
            // 
            // 在任一步骤中如果发现两个相对的法兰面之间连接有弯头或三通则将长度置0,日志报错,结束查找

            // 长度计算
            // 如果相对法兰之间只有一个垫片,  长度 = 两个法兰的厚度 + 螺母 + xtrl + 垫片厚度
            // 如果相对法兰之间有多个垫片,    长度 = 两个法兰的厚度 + 螺母 + xtrl + 第一个垫片的入口点 - 最后一个垫片的出口点距离

            // 这里记录已经被处理的垫片
            std::set<WDNode::SharedPtr> usedGasks;
            // 收集生成螺栓的法兰和阀门节点
            for (size_t i = 0; i < nodes.size(); ++i)
            {
                auto& pNode = nodes[i];
                if (pNode == nullptr || !pNode->isComNode())
                    continue;
                TuComNode* pTmpNode = dynamic_cast<TuComNode*>(pNode);
                if (pTmpNode == nullptr)
                    continue;
                auto pComNode = pTmpNode->comNode();
                if (pComNode == nullptr
                    || !pComNode->isType("GASK")
                    || usedGasks.find(pComNode) != usedGasks.end())
                    continue;

                usedGasks.emplace(pComNode);
                // 获取分支节点
                auto gaskDEndedNodes = GetBoltsNodesByGask(*pComNode, &usedGasks);
                if (gaskDEndedNodes.empty())
                    continue;

                BoltNodeInfo info;
                info.nodes = gaskDEndedNodes;
                info.gask = pTmpNode;
                boltInfos.push_back(info);
            }
            // 为螺栓编号
            {
                std::map<WD::BoltKitInfo, int> boltNumberMap;
                for (auto& each : boltInfos)
                {
                    auto& pGask = each.gask;
                    if (pGask == nullptr)
                    {
                        assert(false);
                        continue;
                    }
                    auto& gaskNodes = each.nodes;
                    if (!WD::CheckBoltNodes(gaskNodes))
                        continue;
                    const auto& pStartNode = gaskNodes.front();
                    const auto& pEndNode = gaskNodes.back();
                    if (pStartNode == nullptr || pEndNode == nullptr)
                    {
                        assert(false);
                        continue;
                    }
                    pGask->boltKitInfo = WD::GetBoltNode(core, *pStartNode, *pEndNode);
                    auto& pStudNode = pGask->boltKitInfo.pStudNode;
                    if (pStudNode != nullptr)
                    {
                        pGask->boltKitInfo.boltLength = WD::GetBoltLength(core, gaskNodes, *pStudNode);
                        pGask->boltKitInfo.pStart = pStartNode;
                        pGask->boltKitInfo.pEnd = pEndNode;
                        auto itr = boltNumberMap.find(pGask->boltKitInfo);
                        if (itr != boltNumberMap.end())
                        {
                            pGask->boltNumber = itr->second;
                        }
                        else
                        {
                            pGask->boltNumber = lastNumber;
                            boltNumberMap[pGask->boltKitInfo] = lastNumber;
                            lastNumber++;
                        }
                        continue;
                    }
                    // 螺栓等级未找到
                    char info[1024] = { 0 };
                    sprintf_s(info, sizeof(info), "法兰/阀门(%s[%s])的螺栓等级查找失败!"
                        , pStartNode->name().c_str()
                        , pStartNode->uuid().toString().c_str());
                    LOG_INFO << info;

                    pGask->boltKitInfo.reset();
                }
            }
            // 为螺母编号
            {
                std::map<WD::BoltKitInfo, int> nutNumberMap;
                for (auto& each : boltInfos)
                {
                    auto& pGask = each.gask;
                    if (pGask == nullptr)
                    {
                        assert(false);
                        continue;
                    }
                    auto& pNutNode = pGask->boltKitInfo.pNutNode;
                    if (pNutNode != nullptr)
                    {
                        int numNumber = 0;
                        auto itr = nutNumberMap.find(pGask->boltKitInfo);
                        if (itr != nutNumberMap.end())
                        {
                            numNumber = itr->second;
                        }
                        else
                        {
                            numNumber = lastNumber++;
                            nutNumberMap[pGask->boltKitInfo] = numNumber;
                        }
                        pGask->nutNumber = numNumber;
                    }
                    else if (!each.nodes.empty())
                    {
                        // 螺母等级未找到
                        const auto& pStartNode = each.nodes.front();
                        assert(pStartNode != nullptr);
                        if (pStartNode != nullptr)
                        {
                            char info[1024] = { 0 };
                            sprintf_s(info, sizeof(info), "法兰/阀门(%s[%s])的螺母等级查找失败!"
                                , pStartNode->name().c_str()
                                , pStartNode->uuid().toString().c_str());
                            LOG_INFO << info;
                        }
                    }
                }
            }
            // 为垫圈编号
            {
                std::map<WD::BoltKitInfo, int> washNumberMap;
                for (auto& each : boltInfos)
                {
                    auto& pGask = each.gask;
                    if (pGask == nullptr)
                    {
                        assert(false);
                        continue;
                    }
                    auto& pWashNode = pGask->boltKitInfo.pWashNode;
                    // 目前的特殊处理,如果螺母为空,垫圈也不处理
                    auto& pNutNode = pGask->boltKitInfo.pNutNode;
                    if (pNutNode == nullptr)
                    {
                        pWashNode = nullptr;
                        continue;
                    }
                    if (pWashNode != nullptr)
                    {
                        int numNumber = 0;
                        auto itr = washNumberMap.find(pGask->boltKitInfo);
                        if (itr != washNumberMap.end())
                        {
                            numNumber = itr->second;
                        }
                        else
                        {
                            numNumber = lastNumber++;
                            washNumberMap[pGask->boltKitInfo] = numNumber;
                        }
                        pGask->washNumber = numNumber;
                    }
                    else if (!each.nodes.empty())
                    {
                        // 垫圈等级未找到
                        const auto& pStartNode = each.nodes.front();
                        assert(pStartNode != nullptr);
                        if (pStartNode != nullptr)
                        {
                            char info[1024] = { 0 };
                            sprintf_s(info, sizeof(info), "法兰/阀门(%s[%s])的垫圈等级查找失败!"
                                , pStartNode->name().c_str()
                                , pStartNode->uuid().toString().c_str());
                            LOG_INFO << info;
                        }
                    }
                }
            }
            auto setNodeNumber = [](const std::vector<TuNode*>& nodes
                , WDNode& node
                , const std::optional<int>& boltNumber
                , const std::optional<int>& nutNumber
                , const std::optional<int>& washNumber)
            {
                for (auto& each : nodes)
                {
                    if (each != nullptr && each->isComNode())
                    {
                        TuComNode* pTmpNode = dynamic_cast<TuComNode*>(each);
                        if (pTmpNode == nullptr)
                            continue;
                        if (pTmpNode->comNode() != nullptr && pTmpNode->comNode()->uuid() == node.uuid())
                        {
                            if (boltNumber)
                                pTmpNode->boltNumber    = boltNumber.value();
                            if (nutNumber)
                                pTmpNode->nutNumber     = nutNumber.value();
                            if (washNumber)
                                pTmpNode->washNumber    = washNumber.value();
                            return;
                        }
                    }
                }
            };
            for (auto& each : boltInfos)
            {
                auto& pGask = each.gask;
                if (pGask == nullptr)
                {
                    assert(false);
                    continue;
                }
                if (each.nodes.front() != nullptr)
                    setNodeNumber(nodes, *each.nodes.front(), pGask->boltNumber, pGask->nutNumber, pGask->washNumber);
                if (each.nodes.back() != nullptr)
                    setNodeNumber(nodes, *each.nodes.back(), pGask->boltNumber, pGask->nutNumber, pGask->washNumber);
            }
        }
        // 继续为附点编号
        {
            std::map<WDNode::SharedPtr, int> attaMap;
            for (auto pNode : nodes)
            {
                if (pNode == nullptr)
                    continue;
                if (!pNode->isTubiNode())
                    continue;
                auto tmpNode = dynamic_cast<TuTubiNode*>(pNode);
                if (tmpNode == nullptr)
                    continue;
                TuAttas& attas = tmpNode->allAttas();
                for (auto& each : attas)
                {
                    auto pChildNode = each.attaNode();
                    if (pChildNode == nullptr)
                        continue;
                    auto pSPCONode = pChildNode->getAttribute("Spref").toNodeRef().refNode();
                    auto fItr = attaMap.find(pSPCONode);
                    if (fItr != attaMap.end())
                    {
                        each.attaNumber = fItr->second;
                    }
                    else
                    {
                        each.attaNumber = lastNumber++;
                        attaMap.emplace(pSPCONode, each.attaNumber);
                    }
                }
            }
        }
    }
private:
    void addTypeNode(TuNode* pNode, const std::string& type, bool bShop)
    {
        if (pNode == nullptr)
            return;
        int index = bShop ? 0 : 1;
        for (auto& typeTuNodes : _typesTuNodes[index])
        {
            if (typeTuNodes.first == type)
            {
                typeTuNodes.second.push_back(pNode);
                return;
            }
        }
        LOG_INFO << "ISO暂不支持编号的节点类型:" + type;
        assert(false);
    }
    void addTypeNode(TuComNode* pNode)
    {
        if (pNode == nullptr)
            return;
        if (pNode->comNode() == nullptr)
        {
            assert(false);
            return;
        }
        this->addTypeNode(pNode, std::string(pNode->comNode()->type()), pNode->comNode()->getAttribute("Shop").toBool());
    }
    WDNode::SharedPtr getSPCONode(TuNode* pTuNode)
    {
        if (pTuNode->isTubiNode())
        {
            TuTubiNode* pTNode = static_cast<TuTubiNode*>(pTuNode);
            if (pTNode->tubiNode() == nullptr)
                return nullptr;
            return pTNode->tubiNode()->getAttribute("Spref").toNodeRef().refNode();
        }
        else if (pTuNode->isComNode())
        {
            TuComNode* pCNode = static_cast<TuComNode*>(pTuNode);
            if (pCNode->comNode() == nullptr)
                return nullptr;
            return pCNode->comNode()->getAttribute("Spref").toNodeRef().refNode();
        }
        else
        {
            assert(false);
            return nullptr;
        }
    }
};


/******** TuLine *********/
TuLine TuLine::Generate(WDNode& branchNode, ISOFigureLegendsMgr& figuLegMgr)
{
    TuLine rLine;
    if (!branchNode.isType("BRAN"))
    {
        assert(false);
        return rLine;
    }
    DKeyPoint hAPt;
    hAPt.position   = branchNode.getAttribute("Hposition WRT World").toDVec3();
    hAPt.direction  = -branchNode.getAttribute("Hdirection WRT World").toDVec3();
    rLine.push<TuPosNode>(figuLegMgr, TuPosNode::T_BranHPos, hAPt, branchNode.getAttribute("Href").toNodeRef());
    // 是否需要标识流向箭头
    bool bFlowArrow = false;
    // 是否需要标识通径
    bool bBore = false;
    for (size_t i = 0; i < branchNode.children().size(); ++i)
    {
        auto pNode = branchNode.children()[i];
        if (pNode == nullptr)
        {
            continue;
        }
        // 附着点, 跳过
        else if (pNode->isType("ATTA"))
        {
            continue;
        }
        // 焊缝, 跳过
        else if (pNode->isType("WELD"))
        {
            continue;
        }
        // 直管
        else if (pNode->isType("TUBI"))
        {
            TuTubiNode* pRNode  = rLine.push<TuTubiNode>(figuLegMgr, pNode);
            pRNode->bFlowArrow  = bFlowArrow;
            bFlowArrow          = false;
            pRNode->bBore       = bBore;
            bBore               = false;
        }
        // 管件
        else if (WDBMDPipeUtils::IsPipeComponent(*pNode))
        {
            rLine.push<TuComNode>(figuLegMgr, pNode);

            // TEE 或者 OLET元件后的第一个TUBI上，需要生成流向箭头
            if (pNode->isAnyOfType("TEE", "OLET"))
                bFlowArrow  = true;
            // 如果管件有更改通径(公称直径)，则下一条直管需要标识通径
            auto pPtA   = pNode->keyPoint(pNode->getAttribute("Arrive").toInt());
            auto pPbL   = pNode->keyPoint(pNode->getAttribute("Leave").toInt());
            if (pPtA != nullptr && pPbL != nullptr && pPtA->bore() != pPbL->bore())
                bBore   = true;
        }
    }

    DKeyPoint tAPt;
    tAPt.position   = branchNode.getAttribute("Tposition WRT World").toDVec3();
    tAPt.direction  = branchNode.getAttribute("Tdirection WRT World").toDVec3();
    rLine.push<TuPosNode>(figuLegMgr, TuPosNode::T_BranTPos, tAPt, branchNode.getAttribute("Tref").toNodeRef());
    // 获取到所有的直管，指定第一条直管需要 标识通径和流向箭头，最后一条直管需要标识通径
    std::vector<TuTubiNode*> tubiNodes;
    tubiNodes.reserve(rLine.nodes().size());
    for (auto pNode : rLine.nodes())
    {
        if (pNode == nullptr)
            continue;
        if (pNode->isTubiNode())
            tubiNodes.push_back(static_cast<TuTubiNode*>(pNode));
    }
    if (!tubiNodes.empty())
    {
        tubiNodes.front()->bBore        = true;
        tubiNodes.front()->bFlowArrow   = true;
        tubiNodes.back()->bBore         = true;
    }
    // 将附点和焊点根据位置计算到对应的直管节点上
    for (auto pNode : branchNode.children())
    {
        if (pNode == nullptr)
            continue;
        if (pNode->isType("ATTA"))
        {
            rLine.addATTANode(pNode);
        }
        else if (pNode->isType("WELD"))
        {
            rLine.addWELDNode(pNode);
        }
    }
    // 指定与前后节点的连接关系
    for (size_t i = 0; i < rLine.nodes().size(); ++i)
    {
        auto pNode = rLine.nodes()[i];
        if (pNode == nullptr)
            continue;
        if (i != 0)
            pNode->pPrevNode = rLine.nodes()[i - 1];
        if (i != rLine.nodes().size() - 1)
            pNode->pNextNode = rLine.nodes()[i + 1];
    }
    // 生成管件、螺栓、螺母、垫圈等的编号
    rLine.genNumber();

    return rLine;
}

bool TuLine::addATTANode(WDNode::SharedPtr pATTANode)
{
    if (pATTANode == nullptr)
        return false;
    if (!pATTANode->isType("ATTA"))
        return false;
    std::map<double, std::vector<TuNode*>> sortNodeMap;
    for (auto pTuNode : _nodes)
    {
        DVec3 pos = pATTANode->globalTranslation();
        TuComNode* pCom = dynamic_cast<TuComNode*>(pTuNode);
        if (pCom != nullptr )
        {
            //与弯头spt0判断距离，可能是弯头的支架就添加
            auto dis = DVec3::Distance(pCom->sPt0().position, pos);
            auto itr = sortNodeMap.find(dis);
            if (itr == sortNodeMap.end())
                sortNodeMap.emplace(dis, std::vector<TuNode*>{pTuNode});
            else
                itr->second.emplace_back(pTuNode);

            continue;
        }
        TuTubiNode* p = dynamic_cast<TuTubiNode*>(pTuNode);
        if (p == nullptr)
            continue;
        const DVec3 sP = p->sPos();
        const DVec3 eP = p->ePos();
        auto dis = std::min(DVec3::Distance(sP, pos), DVec3::Distance(eP, pos));
        auto itr = sortNodeMap.find(dis);
        if (itr == sortNodeMap.end())
            sortNodeMap.emplace(dis, std::vector<TuNode*>{pTuNode});
        else
            itr->second.emplace_back(pTuNode);
    }

    std::string nodname = pATTANode->name();
    for (auto& i : sortNodeMap)
    {
        for (auto& each : i.second)
        {
            TuTubiNode* p = dynamic_cast<TuTubiNode*>(each);
            if (p != nullptr && p->addATTA(pATTANode))
            {
                return true;
            }

            TuComNode* pCom = dynamic_cast<TuComNode*>(each);
            if (pCom == nullptr)
                continue;
            //可能是弯头的支架就添加，里面会过滤，不是弯头的支架就continue；
            if (!pCom->addATTA(pATTANode))
            {
                continue;
            }
            return true;
        }
    }
    return false;
}

bool TuLine::addWELDNode(WDNode::SharedPtr pWELDNode)
{
    if (pWELDNode == nullptr)
        return false;
    if (!pWELDNode->isType("WELD"))
        return false;

    for (auto pTuNode : _nodes)
    {
        TuTubiNode* p = dynamic_cast<TuTubiNode*>(pTuNode);
        if (p == nullptr)
            continue;
        if (p->addWELD(pWELDNode))
            return true;
    }
    return false;
}

void TuLine::genNumber() const
{
    TypeNumberGen typeNumGen;
    typeNumGen.gen(Core(), _nodes);
}

#define ISO_AABB_TEST false

void TuLine::drawISO(ISOPolttingArea& paper, const std::unordered_map<std::string, std::string>& paperInfos) const
{
    if (_nodes.empty())
        return;
    WD::NumberGenerater::Instance().clear();
    ISOStyleSheet style(paper.uCvt);
    // 计算标注方向
    this->calcDimDir(paper.uCvt);
    // 1. 更新所有节点，对于管件节点来说，将生成一个默认的P0点(用于在图纸上绘制)
    this->updateNodes(paper, style);
    // 2. 计算线性标注的朝向
    this->calcDir(paper.uCvt);
    // 3.生成标绘数据，主要是计算其包围盒,再使用包围盒重新缩放大小，尽量保证铺满图纸
#if ISO_AABB_TEST
    auto obb1 = this->calcOBB(paper.uCvt, style);
#endif
    this->adjustSize(paper, style);
    // 4.居中由于缩放等一些原因，导致标会对象未居中，这里进行一次居中操作
#if ISO_AABB_TEST
    auto obb2 = this->calcOBB(paper.uCvt, style);
#endif
    this->alignCenter(paper, style);
    // 5. 再次生成标会对象，并绘制到图纸上
    this->drawToPainter(paper, paper.uCvt, style,paperInfos);

    // 测试绘制AABB
#if ISO_AABB_TEST
    ISOSVGPainter2D painter2D(paper.pGroup);
    
    auto obb3   = this->calcOBB(paper.uCvt);
    
    painter2D.pen.color = Color::yellow;
    painter2D.pen.strokeWidth = 3.0;

    painter2D.drawRect(paper.leftTopPixel(), paper.sizePixel());
    
    painter2D.pen.color = Color::red;
    painter2D.pen.strokeWidth = 1.0;

    std::vector<DVec2> vsObb1(4);
    obb1.first.vertices(vsObb1.data());
    painter2D.drawPolygon(vsObb1);
    
    std::vector<DVec2> vsAabb1(4);
    obb1.second.vertices(vsAabb1.data());
    painter2D.drawPolygon(vsAabb1);
    
    painter2D.pen.color = Color::lime;
    std::vector<DVec2> vsObb2(4);
    obb2.first.vertices(vsObb2.data());
    painter2D.drawPolygon(vsObb2);
    
    std::vector<DVec2> vsAabb2(4);
    auto rV = obb2.second.vertices();
    obb2.second.vertices(vsAabb2.data());
    painter2D.drawPolygon(vsAabb2);
    
    painter2D.pen.color = Color::blue;
    std::vector<DVec2> vsObb3(4);
    obb3.first.vertices(vsObb3.data());
    painter2D.drawPolygon(vsObb3);

    std::vector<DVec2> vsAabb3(4);
    obb3.second.vertices(vsAabb3.data());
    painter2D.drawPolygon(vsAabb3);
    
    
    painter2D.pen.color = Color::black;
#endif
}
void TuLine::drawISODxf(ISOPolttingArea& paper, DxfPainterInterface& interface, const std::unordered_map<std::string, std::string>& paperInfos) const
{
    if (_nodes.empty())
        return;
    WD::NumberGenerater::Instance().clear();
    ISOStyleSheet style(paper.uCvt);
    // 计算标注方向
    this->calcDimDir(paper.uCvt);
    // 1. 更新所有节点，对于管件节点来说，将生成一个默认的P0点(用于在图纸上绘制)
    this->updateNodes(paper, style);
    // 2. 计算线性标注的朝向
    this->calcDir(paper.uCvt);
    // 3.生成标绘数据，主要是计算其包围盒,再使用包围盒重新缩放大小，尽量保证铺满图纸
#if ISO_AABB_TEST
    auto obb1 = this->calcOBB(paper.uCvt, style);
#endif
    this->adjustSize(paper, style);
    // 4.居中由于缩放等一些原因，导致标会对象未居中，这里进行一次居中操作
#if ISO_AABB_TEST
    auto obb2 = this->calcOBB(paper.uCvt, style);
#endif
    this->alignCenter(paper, style);
    // 5. 再次生成标会对象，并绘制到图纸上
    this->drawToPainter(paper, paper.uCvt, style, paperInfos);

    if (paper._genType == GenPaperType::GPT_ISO_DXF || paper._genType == GenPaperType::GPT_INS_DXF)
    {
        ISODIMPainterDxf pp(paper.uCvt);
        pp.setInterface(&interface);
        pp.initStyle();
        this->drawToPainter(pp, paper.uCvt, style, paperInfos);
        pp.write(false);
    }
}

void TuLine::prepareInsDraw(ISOPolttingArea& paper, DependJsonMgr* mgr)
{
    WDUnused(mgr);
    if (_nodes.empty())
        return;
    WD::NumberGenerater::Instance().clear();
    ISOStyleSheet style(paper.uCvt);
    // 计算标注方向
    this->calcDimDir(paper.uCvt);
    // 1. 更新所有节点，对于管件节点来说，将生成一个默认的P0点(用于在图纸上绘制)
    this->updateNodes(paper, style);
    // 2. 计算线性标注的朝向
    this->calcDir(paper.uCvt);
}

void TuLine::adjustSize(const DVec2& scale) const
{
    double sOff = Min(scale.x, scale.y);
    std::map<TuNode*, std::pair<DVec3, DVec3> > testMap;
    for (auto pNode : _nodes)
    {
        if (!pNode->isTubiNode())
            continue;
        testMap[pNode] = std::make_pair(pNode->rPtA(), pNode->rPtL());
    }
    DVec3 prevPt = _nodes.front()->rPtA();
    for (auto pNode : _nodes)
    {
        auto fItr = testMap.find(pNode);
        if (pNode->isTubiNode() && fItr != testMap.end())
        {
            const DVec3& tPtA = fItr->second.first;
            const DVec3& tPtL = fItr->second.second;
            DVec3 vec = (tPtL - tPtA) * sOff;
            auto aPt = prevPt;
            prevPt = prevPt + vec;
            auto lPt = prevPt;
            auto pTuNode = dynamic_cast<TuTubiNode*>(pNode);
            if (pTuNode)
                pTuNode->setRPtAL(aPt, lPt);
        }
        else
        {
            pNode->moveByRPtA(prevPt);
            prevPt = pNode->rPtL();
        }
    }
}

void TuLine::alignCenter(ISOPolttingArea& paper, const DVec2& center) const
{
    DVec2 mOffScreen = paper.centerPixel() - center;
    DVec2 mOffWorld = paper.uCvt.pixelToWorld(mOffScreen);
    DVec3 mOffset = mOffWorld.x * paper.uCvt.camera().rightDir() - mOffWorld.y * paper.uCvt.camera().upDir();
    for (auto pNode : _nodes)
    {
        pNode->move(mOffset);
    }
}

void TuLine::drawMBD(WDDIMPainter& painter
    , ISOUnitConvert& uCvt
    , ISODIMObjectMgr* pOutDimObjectMgr) const
{
    if (_nodes.empty())
        return;
    ISOStyleSheet style(uCvt);
    style.defAStyle.size *= 0.8;
    style.defLStyle.width *= 0.2;
    style.dimLinearDis3 *= 1.5;
    // 计算标注方向
    this->calcDimDir(uCvt);
    // 1. 更新所有节点
    for (auto pNode : _nodes)
    {
        if (pNode == nullptr)
            continue;
        pNode->updateMBD();
    }
    // 2. 计算线性标注的朝向
    this->calcDir(uCvt);
    // 3. 绘制
    this->drawToPainterMBD(painter, uCvt, style, pOutDimObjectMgr);
}

void TuLine::calcDimDir(const ISOUnitConvert& uCvt) const
{
    auto funcLineDir = [](const TuLine& line) ->DVec3
    {
        // 不会存在这种情况
        if (line._nodes.empty())
        {
            assert(false);
            return DVec3::AxisZ();
        }

        // 如果只有一个节点，说明这条线上只有一个拐点，因此需要用拐点入口点的负方向作为这条线的方向
        if (line._nodes.size() == 1)
            return -(line._nodes[0]->sPtA().direction);
        // 否则拿到第一个管件的出口方向作为这条线的方向
        else
            return line._nodes[0]->sPtL().direction;
    };

    auto funcDirAxis = [](const WD::DVec3& dir)->DVec3
    {
        //计算当前方向的水平标注在哪个轴，返回水平标注的三角形出现在当前管线的哪个轴向
        // 根据与水平方向的四个轴(X,-X,Y,-Y)的夹角，判断三角形的第三个顶点
        std::array<DVec3, 4> axes = { DVec3::AxisX(), DVec3::AxisNX(), DVec3::AxisY(), DVec3::AxisNY() };
        double minAngle = NumLimits<double>::Max;
        size_t minIdx = 0;
        //dir看起来是单位向量，乘以1就是点，不影响计算结果向量
        auto origin = WD::DVec3(dir.xy(), 0);
        for (size_t ia = 0; ia < axes.size(); ++ia)
        {
            auto tAngle = DVec3::Angle(origin, axes[ia]);
            if (tAngle < minAngle)
            {
                minIdx = ia;
                minAngle = tAngle;
            }
        }
        //投影在x的正轴，那么三角形出现在当前管线的y负轴方向
        //返回原始点到投影点的向量，即投影方向
        return (WD::DVec3::ProjectOnVector(axes[minIdx], origin) - origin);
    };

    // 根据拐点，分割出多个线
    std::vector<TuLine> lines;
    lines.push_back(TuLine());
    for (auto pNode : _nodes)
    {
        if (pNode == nullptr)
            continue;
        if (pNode->isInflection() && pNode != _nodes.back())
        {
            auto& line = lines.back();
            line.push(pNode);
            lines.push_back(TuLine());
        }
        else
        {
            auto& line = lines.back();
            line.push(pNode);
        }
    }
    struct DirData
    {
        // 标注方向
        DVec3 dimDir;
    };
    using DirDatas = std::vector<DirData>;

    // 计算每个线对应的线性标注方向
    DirDatas dirDatas;
    dirDatas.reserve(lines.size());

    DPlane plane(uCvt.camera().frontDir(), uCvt.camera().eye());
    for (size_t i = 0; i < lines.size(); ++i)
    {
        DVec3 prevDir = DVec3::Zero();
        DVec3 nextDir = DVec3::Zero();

        if (i != 0)
        {
            prevDir = -funcLineDir(lines[i - 1]);
        }
        if (i != lines.size() - 1)
        {
            nextDir = funcLineDir(lines[i + 1]);
        }

        DVec3 dir = funcLineDir(lines[i]);

        dirDatas.push_back({});
        auto& bd = dirDatas.back();
        // 计算标注的方向
        bd.dimDir = CalcDir(prevDir, dir, nextDir);
        //标注方向这条向量只要与投影方向在x0y平面内近似平行，则需要修改方向
        //否则投影方向一致会导致投影出来在一条线
        auto cFrontDir = uCvt.camera().frontDir();
        auto normal = WD::DVec3::Cross(bd.dimDir, dir);
        WD::DPlane planeD(normal, DVec3::Zero());
        //投影后的视口向量在平面的投影与自身的夹角
        auto angleF = DVec3::Angle(cFrontDir, (planeD.project(cFrontDir) - DVec3::Zero()));
        //流向与标注方向构成的平面，与视口方向夹角太小会导致绘制出来的图符和焊点，承插符号都看不清
        if (angleF < 30.0)
        {
            //与视口夹角太小一定伴随着在xy方向有偏角，往没有偏角的一边标注，不影响后续绘制的偏角三角形
            //流向与z共线，竖直管
            if (WD::DVec3::OnTheSameLine(WD::DVec3::AxisZ(), dir , 0.05))
            {
                DVec3 axisPrve = WD::DVec3::Zero();
                DVec3 axisNext = WD::DVec3::Zero();
                //竖直管的标注方向应取决于前后管的偏角，前后管偏左就在右边标注，可以避免标注重叠
                if (WD::DVec3::OnTheSameLine(dir, prevDir, 0.05))
                {
                    //与前管共线就用前面的方向
                    axisPrve = funcDirAxis(prevDir);
                }
                else
                {
                    auto angleHX = DVec3::Angle(DVec3::AxisX(), WD::DVec3(prevDir.xy(), 0));
                    bool bHorizontalMark = (Abs(180.0 - angleHX) > 0.1)
                        && (Abs(90.0 - angleHX) > 0.1)
                        && (Abs(angleHX) > 0.1);
                    if (bHorizontalMark)
                    {
                        //前一个与xy轴向有偏角，直接用前一个向量算出来的投影方向作为这个向量的标注方向
                        axisPrve = funcDirAxis(prevDir);
                    }
                    else
                    {
                        //后一个与xy轴向有偏角，直接用后一个向量算出来的投影方向作为这个向量的标注方向
                        axisNext = funcDirAxis(nextDir);
                    }
                }

                if (!WD::DVec3::IsZero(axisPrve) && !WD::DVec3::IsZero(axisNext))
                {
                    //这里先-y标注，即使跟三角形方向一致也不影响，因为都是-y，线不会冲突
                    bd.dimDir = -WD::DVec3::AxisY();
                }
                else if (!WD::DVec3::IsZero(axisPrve, 0.01))
                {
                    bd.dimDir = axisPrve * -1;
                }
                else if (!WD::DVec3::IsZero(axisNext, 0.01))
                {
                    bd.dimDir = axisNext * -1;
                }
            }
            else 
            {
                // 根据最小角度，判断后续的三角形会标注在哪个轴，
                const DVec3& axis = funcDirAxis(dir); 
                if (!WD::DVec3::IsZero(axis, 0.01))
                    bd.dimDir = axis * -1;
            }

        }
        auto angleX0Y = DVec3::Angle(DVec3(bd.dimDir.x, bd.dimDir.y, 0), DVec3(cFrontDir.x, cFrontDir.y, 0));
        if (abs(angleX0Y - 180) < 5 || abs(angleX0Y) < 5)
        {
            bd.dimDir = WD::DVec3::Cross(bd.dimDir, dir);
        }
        //标注方向这条向量只要与投影方向在y0z平面内近似平行，则需要修改方向
        //否则投影方向一致会导致投影出来在管线的下方被遮挡
        auto angleY0Z = DVec3::Angle(DVec3(0, bd.dimDir.y, bd.dimDir.z), DVec3(0, cFrontDir.y, cFrontDir.z));
        //auto dimDir = this->_dimDir;
        if (abs(angleY0Z - 180) < 5 || abs(angleY0Z) < 5)
        {
            bd.dimDir = WD::DVec3::Cross(bd.dimDir, dir);
        }
    }

    assert(dirDatas.size() == lines.size());
    // 将方向应用到每个Line中的节点上
    for (size_t i = 0; i < lines.size(); ++i)
    {
        auto& line = lines[i];
        for (auto pNode : line._nodes)
        {
            if (pNode == nullptr)
                continue;
            pNode->_dimDir = dirDatas[i].dimDir;
            assert(!IsNan(pNode->_dimDir.x) && !IsNan(pNode->_dimDir.y) && !IsNan(pNode->_dimDir.z));
        }
    }
}

void TuLine::calcDir(const ISOUnitConvert& uCvt) const
{
    auto funcLineDir = [](const TuLine& line) ->DVec3
        {
            // 不会存在这种情况
            if (line._nodes.empty())
            {
                assert(false);
                return DVec3::AxisZ();
            }

            // 如果只有一个节点，说明这条线上只有一个拐点，因此需要用拐点入口点的负方向作为这条线的方向
            if (line._nodes.size() == 1)
                return -(line._nodes[0]->sPtA().direction);
            // 否则拿到第一个管件的出口方向作为这条线的方向
            else
                return line._nodes[0]->sPtL().direction;
        };

    // 根据拐点，分割出多个线
    std::vector<TuLine> lines;
    lines.push_back(TuLine());
    for (auto pNode : _nodes)
    {
        if (pNode == nullptr)
            continue;
        if (pNode->isInflection() && pNode != _nodes.back())
        {
            auto& line = lines.back();
            line.push(pNode);
            lines.push_back(TuLine());
        }
        else
        {
            auto& line = lines.back();
            line.push(pNode);
        }
    }

    struct DirData
    {
        // 在图纸上管线的方向
        DVec3 frontDir;
        // 在图纸上垂直于管线的右方向
        DVec3 rightDir;
        // 默认的引线标注方向, 左方向或右方向中，取与dimDir夹角最大的方向
        DVec3 qleadDir;
        // 当前管件所在管线直线的第一个节点(通常是管线的端点或者拐点)
        TuNode* pLineFirst  = nullptr;
        // 当前管件所在管线直线的最后一个节点(通常是管线的端点或者拐点)
        TuNode* pLineLast   = nullptr;
    };
    using DirDatas = std::vector<DirData>;

    // 计算每个线对应的线性标注方向
    DirDatas dirDatas;
    dirDatas.reserve(lines.size());

    DPlane plane(uCvt.camera().frontDir(), uCvt.camera().eye());
    for (size_t i = 0; i < lines.size(); ++i)
    {
        DVec3 firstPos  = lines[i].nodes().front()->rPt0();
        DVec3 lastPos   = lines[i].nodes().back()->rPt0();
        DVec3 prevDir   = DVec3::Zero();
        DVec3 nextDir   = DVec3::Zero();

        if (i != 0)
        {
            prevDir = -funcLineDir(lines[i - 1]);
        }
        else
        {
            firstPos = lines[i].nodes().front()->rPtA();
            lastPos = lines[i].nodes().back()->rPt0();
        }

        if (i != lines.size() - 1)
        {
            nextDir = funcLineDir(lines[i + 1]);
        }
        else
        {
            if (i != 0)
            {
                firstPos    = lines[i - 1].nodes().back()->rPt0();
                lastPos     = lines[i].nodes().back()->rPt0();
            }
            else
            {
                firstPos = lines[i].nodes().front()->rPtA();
                lastPos = lines[i].nodes().back()->rPtL();
            }
        }

        DVec3 dir       = funcLineDir(lines[i]);
        dirDatas.push_back({});
        auto& bd        = dirDatas.back();
        // 计算标注的方向
        auto dimDir     = CalcDir(prevDir, dir, nextDir);
        // 在图纸上垂直于管线的右方向
        bd.rightDir     = dimDir;
        DVec3 pt0       = plane.project(firstPos);
        DVec3 pt1       = plane.project(lastPos);
        DVec3 vec       = pt1 - pt0;
        if (vec.lengthSq() >= NumLimits<float>::Epsilon)
        {
            bd.frontDir = vec.normalized();
            bd.rightDir = DVec3::Cross(uCvt.camera().frontDir(), bd.frontDir).normalized();
            bd.qleadDir = bd.rightDir;
            DVec3 prjDimDir = plane.project(dimDir * 1000.0);
            double dt = DVec3::Dot(prjDimDir, bd.rightDir);
            //点积结果大于0说明角度小于90度，需要把标注方向反向
            if (dt > 0.0)
                bd.qleadDir = -bd.rightDir;
        }
        else
        {
            // 说明这条线上只有一个管件，并且没有直管，此时直接使用相机的右方向即可
            bd.frontDir = uCvt.camera().rightDir();
            bd.rightDir = DVec3::Cross(uCvt.camera().frontDir(), bd.frontDir).normalized();
            bd.qleadDir = bd.rightDir;
            DVec3 prjDimDir = plane.project(dimDir * 1000.0);
            double dt = DVec3::Dot(prjDimDir, bd.rightDir);
            //点积结果大于0说明角度小于90度，需要把标注方向反向
            if (dt > 0.0)
                bd.qleadDir = -bd.rightDir;
        }
    }

    assert(dirDatas.size() == lines.size());
    // 将方向应用到每个Line中的节点上
    for (size_t i = 0; i < lines.size(); ++i)
    {
        auto& line = lines[i];
        for (auto pNode : line._nodes)
        {
            if (pNode == nullptr)
                continue;

            pNode->_frontDir     = dirDatas[i].frontDir;
            pNode->_rightDir     = dirDatas[i].rightDir;
            pNode->_qleadDir     = dirDatas[i].qleadDir;
            pNode->_pLineFirst   = line.nodes().front();
            pNode->_pLineLast    = line.nodes().back();

            pNode->_qleadMinDis  = uCvt.paperToWorld(5.0);
            pNode->_qleadMinDisN = uCvt.paperToWorld(16.0);
        }
    }
}

void TuLine::updateNodes(ISOPainter& painter, const ISOStyleSheet& style) const
{
    ISODrawContext cxt(painter, painter.uCvt, style);
    DKeyPoint sKeyPt = _nodes.front()->sPtL();
    for (auto pNode : _nodes)
    {
        if (pNode == nullptr)
            continue;
        DKeyPoint tsKeyPt = pNode->update(cxt, sKeyPt);
        assert(!IsNan(tsKeyPt.position.x) 
            && !IsNan(tsKeyPt.position.y) 
            && !IsNan(tsKeyPt.position.z)
            && !IsNan(tsKeyPt.direction.x)
            && !IsNan(tsKeyPt.direction.y)
            && !IsNan(tsKeyPt.direction.z));
        sKeyPt = tsKeyPt;
    }
}

void TuLine::adjustSize(ISOPolttingArea& paper, const ISOStyleSheet& style) const
{
    auto aabb           = calcOBB(paper.uCvt, style).second;
    // 尽量保证填充满图纸
    DVec2 sOffScreen    = paper.sizePixel() / aabb.size();
    double sOff         = Min(sOffScreen.x, sOffScreen.y);
    std::map<TuNode*, std::pair<DVec3, DVec3> > testMap;
    for (auto pNode : _nodes)
    {
        if (!pNode->isTubiNode())
            continue;
        testMap[pNode] = std::make_pair(pNode->rPtA(), pNode->rPtL());
    }
    DVec3 prevPt = _nodes.front()->rPtA();
    for (auto pNode : _nodes)
    {
        auto fItr = testMap.find(pNode);
        if (pNode->isTubiNode() && fItr != testMap.end())
        {
            const DVec3& tPtA = fItr->second.first;
            const DVec3& tPtL = fItr->second.second;
            DVec3 vec   = (tPtL - tPtA) * sOff;
            auto aPt    = prevPt;
            prevPt      = prevPt + vec;
            auto lPt    = prevPt;
            auto pTuNode = dynamic_cast<TuTubiNode*>(pNode);
            if (pTuNode)
                pTuNode->setRPtAL(aPt, lPt);
        }
        else
        {
            pNode->moveByRPtA(prevPt);
            prevPt = pNode->rPtL();
        }
    }
}

void TuLine::alignCenter(ISOPolttingArea& paper, const ISOStyleSheet& style) const
{
    auto aabb = calcOBB(paper.uCvt, style).second;
    DVec2 mOffScreen    = paper.centerPixel() - aabb.center();
    DVec2 mOffWorld     = paper.uCvt.pixelToWorld(mOffScreen);
    DVec3 mOffset       = mOffWorld.x * paper.uCvt.camera().rightDir() - mOffWorld.y * paper.uCvt.camera().upDir();
    for (auto pNode : _nodes)
    {
        pNode->move(mOffset);
    }
}

void TuLine::drawToPainter(WDDIMPainter& painter
    , const ISOUnitConvert& uCvt
    , const ISOStyleSheet& style
    , const std::unordered_map<std::string, std::string>& paperInfos) const
{
    ISODrawContext cxt(painter, uCvt, style);
    // 绘制管线
    for (auto pNode : _nodes)
    {
        if (pNode == nullptr)
            continue;
        pNode->drawBase(cxt);
    }

    // 坡度标注
    if (_nodes.size() >= 2)
    {
        // 找到所有的拐点索引
        std::vector<size_t> iIdices;
        iIdices.reserve(_nodes.size());
        // 加入第一个点
        iIdices.push_back(0);
        // 找到所有的偏心大小头的索引
        std::vector<size_t> indexPipeREDU;
        // 加入中间的拐点
        for (size_t i = 1; i < _nodes.size() - 1; ++i) 
        {
            if (_nodes[i]->isInflection())
                iIdices.push_back(i);
            TuComNode* pComNode = dynamic_cast<TuComNode*>(_nodes[i]);
            if (pComNode != nullptr
                && pComNode->isTypedComNode("REDU")
                && pComNode->comNode()!= nullptr)
            {
                auto pPtA = pComNode->comNode()->keyPoint(pComNode->comNode()->getAttribute("Arrive").toInt());
                auto pPtL = pComNode->comNode()->keyPoint(pComNode->comNode()->getAttribute("Leave").toInt());
                if (pPtA != nullptr && pPtL != nullptr)
                {
                    // 偏心大小头
                    bool bOkA = false;
                    bool bOkL = false;
                    double boreA = FromString<double>(pPtA->bore(), &bOkA);
                    double boreL = FromString<double>(pPtL->bore(), &bOkL);
                    if (boreA != boreL && bOkA && bOkL)
                        indexPipeREDU.emplace_back(i);
                }
            }
        }
        // 加入最后一个点
        iIdices.push_back(_nodes.size() - 1);
        // 记录所有拐点节点的P0点位置
        struct PtInft
        {
            DVec3 rPt;
            DVec3 sPt;
            // 拐点之后的第一个直管, 用于标注坡度
            TuTubiNode* pFirstTubi = nullptr;
        };
        using PtInfts = std::vector<PtInft>;
        PtInfts ptInfts;
        ptInfts.reserve(iIdices.size());
        bool haveREDU = false;
        for (size_t i = 0; i < iIdices.size(); ++i)
        {
            ptInfts.push_back({});
            ptInfts.back().rPt = _nodes[iIdices[i]]->rPt0();
            ptInfts.back().sPt = _nodes[iIdices[i]]->sPt0().position;
            // 查找当前拐点到下一个拐点之间的直管段
            if (i < iIdices.size() - 1) 
            {
                size_t sIdx = iIdices[i];
                size_t eIdx = iIdices[i + 1];
                haveREDU = false;
                for (size_t j = sIdx; j < eIdx; ++j)
                {
                    if (_nodes[j]->isTubiNode())
                        continue;
                    //如果整个直管段上有偏心大小头则不标注斜率
                    if (std::find(indexPipeREDU.begin(), indexPipeREDU.end(), j) != indexPipeREDU.end())
                    {
                        haveREDU = true;
                        break;
                    }
                }
                if (haveREDU)
                {
                    continue;
                }
                for (size_t j = sIdx; j < eIdx; ++j)
                {
                    if (!_nodes[j]->isTubiNode())
                        continue;
                    ptInfts.back().pFirstTubi = dynamic_cast<TuTubiNode*>(_nodes[j]);
                    break;
                }
            }
        }

        // 根据拐点与水平或竖直平面的夹角判断是否需要三角坡度标注
        for (size_t i = 1; i < ptInfts.size(); ++i)
        {
            const auto& prevSPt = ptInfts[i - 1].sPt;
            const auto& currSPt = ptInfts[i].sPt;

            // 水平方向的向量(不包含Z坐标)
            auto horizontalVec          = DVec3(currSPt.xy()) - DVec3(prevSPt.xy());
            // 水平距离
            const auto horizontalDis    = horizontalVec.length();
            // 垂直距离
            auto verticalDis        = Abs(currSPt.z - prevSPt.z);
            // 如果水平距离为0，证明为竖直管或者两点重合，不需要坡度
            //2025.3.10 距离小于1，不标注，视为0bend建管
            if (horizontalDis < 1.0)
                continue;
            // 魏工出图特殊处理，不标注，用完恢复这里的代码
            //if (horizontalDis < 3.0)
            //    continue;

            // 计算坡度值
            const auto slope        = verticalDis / horizontalDis;
            // 计算水平方向与X轴的夹角，如果夹角夹角不是0°或90°或180°, 表示水平方向需要三角标注
            auto angleHX            = DVec3::Angle(DVec3::AxisX(), horizontalVec);
            bool bHorizontalMark    = (Abs(180.0 - angleHX) > 0.1)
                && (Abs(90.0 - angleHX) > 0.1)
                && (Abs(angleHX) > 0.1);
            // 计算垂直方向的标注方式
            bool bSlope         = false;
            bool bVerticalMark  = false;
            if (slope > 0.000001) 
            {
                // 如果水平方向未做三角标注，则根据坡度值是否小于0.075来决定是否为斜率标注
                bSlope          = (!bHorizontalMark) && (slope < 0.075);
                // 魏工出图特殊处理，不标注，用完恢复这里的代码
                //if (bSlope &&slope < 0.002)
                //{
                //    continue;
                //}
                // 如果水平方向做了三角标注，则垂直方向只做三角标注
                bVerticalMark   = !bSlope;
            }
            // 不做任何标注
            if (!bSlope && !bHorizontalMark && !bVerticalMark)
                continue;

            // 经过图纸计算后的点，用于计算标注符号或标注线的绘制的位置
            const auto& prevRPt = ptInfts[i - 1].rPt;
            const auto& currRPt = ptInfts[i].rPt;
            if (DVec3::DistanceSq(prevRPt, currRPt) <= NumLimits<float>::Epsilon) 
            {
                assert(false && "注意: 距离过近，无法做坡度标注!");
                continue;
            }
            // 斜率标注，如果出现了斜率标注了，理论上就不会做三角标注了
            if (bSlope) 
            {
                // 如果存在直管，则将坡度标到直管上
                if (ptInfts[i - 1].pFirstTubi != nullptr) 
                {
                    ptInfts[i - 1].pFirstTubi->slope = slope;
                }
                // 否则将坡度标在两个P0点的中间
                else
                {
                    assert(false && "!注意: 可能是两个管件之间没有直管，需要后续支持；也可能是大小头不需要标注斜率!");
                }
                continue;
            }
            // 垂直方向的三角标注
            if (bVerticalMark)
            {
                // 计算三角形的三个顶点
                auto highPt = prevRPt.z > currRPt.z ? prevRPt : currRPt;
                auto lowPt  = prevRPt.z <= currRPt.z ? prevRPt : currRPt;
                auto tmpPt  = DVec3(highPt.xy(), lowPt.z);
                // 绘制三角形外框
                cxt.painter.drawBrokenLine({ lowPt , tmpPt, highPt }, style.defLStyle);
                auto id = WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None);
                cxt.collision.addBrokenLine({ lowPt , tmpPt, highPt }, id, style.defLStyle);
                // 绘制阴影区
                {
                    std::vector<DVec3> lines;
                    lines.reserve(20);
                    auto tVecA  = (tmpPt - lowPt).normalized();
                    auto tVecB  = (highPt - tmpPt).normalized();
                    auto step   = cxt.uCvt.paperToWorld(2.0);
                    for (size_t j = 2; j < 10; ++j)
                    {
                        auto len    = static_cast<double>(j) * step;
                        // 保证阴影线的起点在三角形内
                        if (len >= (tmpPt - lowPt).length())
                            continue;
                        auto pt0    = lowPt + tVecA * len;

                        auto lineA = WD::TLine3<double>(WD::DVec3(lowPt), (highPt - lowPt).normalized());
                        auto lineB = WD::TLine3<double>(WD::DVec3(pt0), WD::DVec3(tVecB));
                        auto ret = WD::TLine3<double>::Intersect(lineA, lineB, 0.00001);

                        auto pt1 = pt0 + tVecB * slope * len * 0.65;
                        if (ret)
                        pt1 = lineA.at(ret.value().first);

                        if (DVec3::DistanceSq(pt0, pt1) > NumLimits<float>::Epsilon)
                        {
                            lines.push_back(pt0);
                            lines.push_back(pt1);
                        }
                    }
                    if (!lines.empty())
                    {
                        cxt.painter.drawLines(lines, style.defLStyle);
                        cxt.collision.addLines(lines, id, style.defLStyle);
                    }
                }
                // 水平尺寸标注
                if (DVec3::DistanceSq(tmpPt, lowPt) >= NumLimits<float>::Epsilon)
                {
                    cxt.dimGen.addDIMLinear((tmpPt - highPt ).normalized()
                        , lowPt
                        , tmpPt
                        , horizontalDis
                        , cxt.uCvt.paperToWorld(7.0)
                        , nullptr
                        , id);
                }
                // 垂直尺寸标注
                if (DVec3::DistanceSq(tmpPt, highPt) >= NumLimits<float>::Epsilon)
                {
                    cxt.dimGen.addDIMLinear((tmpPt - lowPt).normalized()
                        , tmpPt
                        , highPt
                        , verticalDis
                        , cxt.uCvt.paperToWorld(7.0)
                        , nullptr
                        , id);
                }
            }
            // 水平方向的三角标注
            if (bHorizontalMark)
            {
                // 源坐标，未做图纸换算前的坐标，用于计算尺寸
                auto highPtS    = prevSPt.z > currSPt.z ? prevSPt : currSPt;
                auto lowPtS     = prevSPt.z <= currSPt.z ? prevSPt : currSPt;
                auto tmpPtS     = DVec3(highPtS.xy(), lowPtS.z);
                auto hVecS      = tmpPtS - lowPtS;
                // 计算水平向量
                auto highPt = prevRPt.z > currRPt.z ? prevRPt : currRPt;
                auto lowPt  = prevRPt.z <= currRPt.z ? prevRPt : currRPt;
                auto tmpPt  = DVec3(highPt.xy(), lowPt.z);
                auto hVec   = tmpPt - lowPt;
                auto id = WD::NumberGenerater::Instance().GetNumber(WD::NumberGenerater::NumberType::NT_None);
                // 根据与水平方向的四个轴(X,-X,Y,-Y)的夹角，判断三角形的第三个顶点
                std::array<DVec3, 4> axes = { DVec3::AxisX(), DVec3::AxisNX(), DVec3::AxisY(), DVec3::AxisNY() };
                double minAngle = NumLimits<double>::Max;
                size_t minIdx   = 0;
                for (size_t ia  = 0; ia < axes.size(); ++ia)
                {
                    auto tAngle = DVec3::Angle(hVec, axes[ia]);
                    if (tAngle < minAngle) 
                    {
                        minIdx  = ia;
                        minAngle = tAngle;
                    }
                }
                // 根据最小角度，计算到这个轴向上的投影点，做为三角形的直角边交点
                const DVec3& axis = axes[minIdx];
                // 计算到最小夹角轴的投影点
                auto prjV       = DVec3::ProjectOnVector(axis, hVec);
                auto prjPt      = lowPt + prjV;
                auto prjVS      = DVec3::ProjectOnVector(axis, hVecS);
                auto prjPtS     = lowPtS + prjVS;
                // 绘制三角形外框
                cxt.painter.drawBrokenLine({lowPt, prjPt, tmpPt }, style.defLStyle);
                cxt.collision.addBrokenLine({ lowPt, prjPt, tmpPt }, id, style.defLStyle);
                
                // 绘制阴影区
                {
                    auto tmpSlope = DVec3::Distance(lowPtS, prjPtS) > 0.0 
                        ? DVec3::Distance(prjPtS, tmpPtS) / DVec3::Distance(lowPtS, prjPtS)
                        : 0.0;
                    std::vector<DVec3> lines;
                    lines.reserve(20);
                    auto tVecA  = (prjPt - lowPt).normalized();
                    auto tVecB  = (tmpPt - prjPt).normalized();
                    auto step   = cxt.uCvt.paperToWorld(2.0);
                    for (size_t j = 2; j < 10; ++j)
                    {
                        auto len = static_cast<double>(j) * step;
                        // 保证阴影线的起点在三角形内
                        if (len >= (prjPt - lowPt).length())
                            continue;
                        auto pt0 = lowPt + tVecA * len;
                        //优先用射线求交的方式去画阴影，因为图上的角度不是真角度
                        auto lineA = WD::TLine3<double>(WD::DVec3(lowPt), (highPt - lowPt).normalized());
                        auto lineB = WD::TLine3<double>(WD::DVec3(pt0), WD::DVec3(tVecB));
                        auto ret = WD::TLine3<double>::Intersect(lineA, lineB, 0.00001);

                        auto pt1 = pt0 + tVecB * tmpSlope * len * 0.65;
                        if (ret)
                            pt1 = lineA.at(ret.value().first);
                        if (DVec3::DistanceSq(pt0, pt1) > NumLimits<float>::Epsilon)
                        {
                            lines.push_back(pt0);
                            lines.push_back(pt1);
                        }
                    }
                    if (!lines.empty())
                    {
                        cxt.painter.drawLines(lines, style.defLStyle);
                        cxt.collision.addLines(lines, id,style.defLStyle);
                    }
                }
                // 沿轴向的标注
                if (DVec3::DistanceSq(prjPt, tmpPt) >= NumLimits<float>::Epsilon)
                {
                    cxt.dimGen.addDIMLinear((prjPt - tmpPt).normalized()
                        , lowPt
                        , prjPt
                        , DVec3::Distance(lowPtS, prjPtS)
                        , cxt.uCvt.paperToWorld(7.0)
                        , nullptr
                        , id);
                }
                // 垂直轴向的标注
                if (DVec3::DistanceSq(prjPt, lowPt) >= NumLimits<float>::Epsilon)
                {
                    cxt.dimGen.addDIMLinear((prjPt - lowPt).normalized()
                        , prjPt
                        , tmpPt
                        , DVec3::Distance(prjPtS, tmpPtS)
                        , cxt.uCvt.paperToWorld(7.0)
                        , nullptr
                        , id);
                }
            }
        }
    }

    // 生成线性标注
    {
        // 2024.1.10 魏耀辉：当前管段有温度计或压力表不标注第三层
        // 这里不标注第三层，但是依旧要更新起始点，可能下一段管子要标注
        // 图符建
        bool markDIMLinearT3 = true;
        QString sKeyQStr;
        std::string keyStr;
        // 第三层标注的开始点
        std::pair<DVec3, DVec3> t3PtS = { _nodes.front()->rPt0(), _nodes.front()->sPt0().position };
        for (auto pNode : _nodes)
        {
            if (pNode == nullptr)
                continue;
            if (pNode->isComNode()&& pNode->sKey(keyStr))
            {
                sKeyQStr = QString::fromUtf8(keyStr.c_str());
                if (sKeyQStr.startsWith("id", Qt::CaseInsensitive)|| sKeyQStr.startsWith("ir", Qt::CaseInsensitive))
                {
                    markDIMLinearT3 = false;
                }
            }

            // 线性标注
            pNode->drawDimLinear(cxt);
            // 如果是拐点，则需要生成第三层标注
            if (pNode->isInflection())
            {
                // 前一个标注结束坐标
                std::pair<DVec3, DVec3> t3PtE = { pNode->rPt0(), pNode->sPt0().position };
                if (markDIMLinearT3)
                {
                    //添加标注
                    cxt.dimGen.addDIMLinearT3(pNode->_dimDir
                        , t3PtS.first
                        , t3PtE.first
                        , DVec3::Distance(t3PtS.second, t3PtE.second)
                        , pNode);
                }
                //经过拐点以后重新判断是否要t3
                markDIMLinearT3 = true;
                // 后一个标注开始
                t3PtS = t3PtE;
            }
        }
        // 第三层标注的结束点
        {
            if (markDIMLinearT3)
            {
                auto dimDir = _nodes.back()->_dimDir;
                auto cFrontDir = cxt.uCvt.camera().frontDir();
                auto angle = DVec3::Angle(DVec3(dimDir.x, dimDir.y, 0), DVec3(cFrontDir.x, cFrontDir.y, 0));
                //auto dimDir = this->_dimDir;
                if (abs(angle - 180) < 5 || abs(angle) < 5)
                {
                    //旋转矩阵(这里用90度，是因为同一条直管上的管件是用的-90，这样标注不在同一侧会清晰一点)
                    WD::DMat4 offMatR = WD::DMat4::MakeRotationZ(90);
                    dimDir = offMatR * dimDir;
                }
                std::pair<DVec3, DVec3> t3PtE = { _nodes.back()->rPt0(), _nodes.back()->sPt0().position };
                cxt.dimGen.addDIMLinearT3(dimDir
                    , t3PtS.first
                    , t3PtE.first
                    , DVec3::Distance(t3PtS.second, t3PtE.second), _nodes.back());
            }
        }
    }

    // 
    cxt.dimGen.drawDimLinear(painter, cxt.collision);
    // 计算
    cxt.dimGen.redrawDimLinearTextPosByQlead(painter, cxt.collision);

    // 生成元件标识
    for (auto pNode : _nodes)
    {
        if (pNode == nullptr)
            continue;
        pNode->drawDimInfo(cxt, paperInfos);
    }
    cxt.dimGen.drawDimQlead(painter, cxt.collision);

#if 0
    //绘制所有的obb
    for (const auto& i : cxt.collision.items())
    {
        std::vector<DVec2> testPoints;
        auto array = i.obb.vertices();
        for (auto& pp : array)
        {
            testPoints.emplace_back(pp);
        }
        WDDIMLineStyle obbLine;
        obbLine.color = WD::Color::green;
        cxt.painter.drawLoopLine2D(testPoints, obbLine);
        WDDIMFontStyle font;
        font.color = WD::Color::red;
        cxt.painter.drawText2D(QString::number(i.groupId).toUtf8().toStdString(), testPoints.front(), font);
    }
#endif
    cxt.dimGen.drawMultiText(painter, cxt.collision);
}
void TuLine::drawToPainterMBD(WDDIMPainter& painter
    , const ISOUnitConvert& uCvt
    , const ISOStyleSheet& style
    , ISODIMObjectMgr* pOutDimObjectMgr) const
{
    ISODrawContext cxt(painter, uCvt, style);
    cxt.dimGen.bMBD = true;
    // 生成线性标注
    {
        // 第三层标注的开始点
        std::pair<DVec3, DVec3> t3PtS = { _nodes.front()->rPt0(), _nodes.front()->sPt0().position };
        for (auto pNode : _nodes)
        {
            if (pNode == nullptr)
                continue;
            // 线性标注
            pNode->drawDimLinearMBD(cxt);
            // 如果是拐点，则需要生成第三层标注
            if (pNode->isInflection())
            {
                // 前一个标注结束
                std::pair<DVec3, DVec3> t3PtE = { pNode->rPt0(), pNode->sPt0().position };
                cxt.dimGen.addDIMLinearT3(pNode->_dimDir
                    , t3PtS.first
                    , t3PtE.first
                    , DVec3::Distance(t3PtS.second, t3PtE.second)
                    , pNode);
                // 后一个标注开始
                t3PtS = t3PtE;
            }
        }
        // 第三层标注的结束点
        {
            std::pair<DVec3, DVec3> t3PtE = { _nodes.back()->rPt0(), _nodes.back()->sPt0().position };
            cxt.dimGen.addDIMLinearT3(_nodes.back()->_dimDir
                , t3PtS.first
                , t3PtE.first
                , DVec3::Distance(t3PtS.second, t3PtE.second), _nodes.back());
        }
    }
    // 
    WDDIMCollision c;
    WDDIMCollision cc;
    cxt.dimGen.drawDimLinear(painter, c);

    _nodes.front()->drawDimInfoMBD(cxt);
    _nodes.back()->drawDimInfoMBD(cxt);
    //// 生成元件标识
    //for (auto pNode : _nodes)
    //{
    //    if (pNode == nullptr)
    //        continue;
    //    pNode->drawDimInfoMBD(cxt);
    //}

    cxt.dimGen.drawDimQlead(painter, c);

    if (pOutDimObjectMgr != nullptr)
        cxt.dimGen.getAllDimObjects(*pOutDimObjectMgr);
}

DVec3 TuLine::CalcDir(const DVec3& prevDir, const DVec3& currDir, const DVec3& nextDir)
{
    auto funcCalcUpNext = [](const DVec3& currDir, const DVec3& nextDir)->DVec3
        {
            DVec3 rightDir = DVec3::Cross(nextDir, currDir).normalized();
            DVec3 upDir = DVec3::Cross(rightDir, currDir).normalized();
            return upDir;
        };
    auto funcCalcUpPrev = [](const DVec3& prevDir, const DVec3& currDir)->DVec3
        {
            DVec3 rightDir = DVec3::Cross(currDir, prevDir).normalized();
            DVec3 upDir = DVec3::Cross(rightDir, currDir).normalized();
            return upDir;
        };

    DVec3 textOffDir = DVec3::AxisZ();
    //202.3.12 z轴共线的管线，标注方向固定x改成了固定-y轴方向（参考pdms轴测图的结果
    if (DVec3::OnTheSameLine(currDir, DVec3::AxisZ()))
        textOffDir = -DVec3::AxisY();
    else
        textOffDir = funcCalcUpNext(currDir, DVec3::AxisZ());

    // 前一个方向是否有效
    bool bPrevDirValid = !DVec3::IsZero(prevDir);
    // 后一个方向是否有效
    bool bNextDirValid = !DVec3::IsZero(nextDir);
    // 前一个方向是否与当前方向共线(接近共线)
    bool bPrevDirSameLine = DVec3::OnTheSameLine(currDir, prevDir, 0.05);
    // 后一个方向是否与当前方向共线(接近共线)
    bool bNextDirSameLine = DVec3::OnTheSameLine(currDir, nextDir, 0.05);
    // 前一个方向是否与当前方向垂直(接近垂直)
    bool bPrevDirPrpend = DVec3::OnPrpendicular(currDir, prevDir, 0.05);
    // 后一个方向是否与当前方向垂直(接近垂直)
    bool bNextDirPrpend = DVec3::OnPrpendicular(currDir, nextDir, 0.05);

    if (bPrevDirValid && bNextDirValid) // 前后方向均有效,需要继续计算
    {
        if (!bPrevDirSameLine && !bNextDirSameLine) //前后方向均不共线，需要继续计算
        {
            if (bPrevDirPrpend) // 前一个向量垂直
            {
                textOffDir = -prevDir;
                // 同向
                bool bSameDir = DVec3::InTheSameDirection(textOffDir, nextDir, 0.1);
                if (bSameDir)
                {
                    DMat3 rMat = DMat3::MakeRotation(90.0, currDir);
                    textOffDir = rMat * textOffDir;
                }
            }
            else if (bNextDirPrpend) // 后一个向量垂直
            {
                textOffDir = -nextDir;
                // 同向
                bool bSameDir = DVec3::InTheSameDirection(textOffDir, prevDir, 0.1);
                if (bSameDir)
                {
                    DMat3 rMat = DMat3::MakeRotation(90.0, currDir);
                    textOffDir = rMat * textOffDir;
                }
            }
            else // 均不垂直
            {
                textOffDir = funcCalcUpPrev(prevDir, currDir);
            }
        }
        else if (!bPrevDirSameLine) // 前一个方向不共线，使用前一个方向计算
        {
            if (bPrevDirPrpend)
                textOffDir = -prevDir;
            else
                textOffDir = funcCalcUpPrev(prevDir, currDir);
        }
        else if (!bNextDirSameLine) // 后一个方向不共线，使用后一个方向计算
        {
            if (bNextDirPrpend)
                textOffDir = -nextDir;
            else
                textOffDir = funcCalcUpNext(currDir, nextDir);
        }
        else // 前后方向均与当前方向共线,采用默认计算的方向
        {
        }
    }
    else if (bPrevDirValid && !bPrevDirSameLine) //前一个方向有效且不共线,使用前一个方向计算
    {
        if (bPrevDirPrpend) // 垂直
            textOffDir = -prevDir;
        else //不垂直且不共线
            textOffDir = funcCalcUpPrev(prevDir, currDir);
    }
    else if (bNextDirValid && !bNextDirSameLine) //后一个方向有效且不共线,使用后一个方向计算
    {
        if (bNextDirPrpend)
            textOffDir = -nextDir;
        else//不垂直且不共线
            textOffDir = funcCalcUpNext(currDir, nextDir);
    }
    else  //前后方向均无效，使用默认计算的方向
    {
    }

    return textOffDir;
}


WD_NAMESPACE_END

