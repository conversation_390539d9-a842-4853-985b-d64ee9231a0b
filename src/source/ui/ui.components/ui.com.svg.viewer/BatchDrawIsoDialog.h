#pragma once

#include <QDialog>
#include <QMenuBar>
#include "ui_BatchDrawIsoDialog.h"
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "ISOPaperMgr.h"
#include "ISOPaper.h"
#include "IsoIndexTable.h"

class UIComSvgViewer;
class BatchDrawIsoDialog : public QDialog
{
    Q_OBJECT
public:
    enum class FileFormat
    {
        FF_Excel,
        FF_PDF,
        FF_DXF
    };
public:
    // 索引表信息,批量导出需要生成索引表
    const WD::IsoIndexTable::IndexTableInfo& indexTableInfo;
public:
    BatchDrawIsoDialog(WD::WDCore& core
        , ISOPaperMgr& isoPaperMgr
        , UIComSvgViewer& viewer
        , const WD::IsoIndexTable::IndexTableInfo& indexTableInfo
        , QWidget *parent = Q_NULLPTR);
    ~BatchDrawIsoDialog();
private slots:
    void slotPushButtonGenerateClicked();
    void slotCheckBoxSummaryClicked(int status);
    void slotPaperSizeChanged(int index);
    void slotIsoFormatChanged(int index);
private:
    inline void setItemUData(QListWidgetItem& item, WD::WDNode::SharedPtr pNode)
    {
        QVariant userData;
        userData.setValue(UiWeakObject(pNode));
        item.setData(Qt::UserRole, userData);
    }
    inline WD::WDNode::SharedPtr getItemUData(const QListWidgetItem& item)
    {
        QVariant userData = item.data(Qt::UserRole);
        if (!userData.isValid() || userData.isNull())
            return nullptr;
        auto obj = userData.value<UiWeakObject>();
        return obj.subObject< WD::WDNode>();
    }
    void GenSvg(WD::ISOPaper* pPaper, const QString& outputDir);
    void GenDxf(WD::ISOPaper* pPaper, const QString& outputDir);
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::BatchDrawIsoDialog ui;
    WD::WDCore& _core;
    ISOPaperMgr& _isoPaperMgr;
    UIComSvgViewer& _viewer;
    // 添加菜单
    QMenuBar*   _addMenuBar;
    // 移除菜单
    QMenuBar*   _removeMenuBar;
    //是否汇总输出pdf
    bool _summary = false;
    //输出纸张型号
    WD::ISOPaper::Size _paperSize = WD::ISOPaper::Size(594.0f, 420.0f);
    /**
     * @brief 理论上应该是临时变量
     暂时作为成员变量是因为阻塞式线程目前不支持传入用户自定义变量
    */
    struct TaskUserData
    {
        int allCnt = 0;
        int successCnt = 0;
        WD::WDNode::Nodes nodes;
        void reset()
        {
            allCnt = 0;
            successCnt = 0;
            nodes.clear();
        }
    };
    TaskUserData _taskUserData;
};