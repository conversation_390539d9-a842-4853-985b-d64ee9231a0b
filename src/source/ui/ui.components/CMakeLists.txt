set(CMAKE_INSTALL_RPATH "$ORIGIN:$ORIGIN/../../..:$ORIGIN/../../../lib:$ORIGIN/../../../extensions/components:$ORIGIN/../../../extensions/formats:$ORIGIN/../../../extensions/tools:$ORIGIN/../../../algorithms/system")

add_subdirectory("ui.com.admin.data")
add_subdirectory("ui.com.admin.permission")
add_subdirectory("ui.com.admin.pipe.colorManagement")
add_subdirectory("ui.com.admin.project.customAttribute")
add_subdirectory("ui.com.atomCom.coco")
add_subdirectory("ui.com.atomCom.createClassification")
add_subdirectory("ui.com.capturePositioning")
add_subdirectory("ui.com.catalog.axis")
add_subdirectory("ui.com.catalog.createCatalog")
add_subdirectory("ui.com.catalog.DC")
add_subdirectory("ui.com.catalog.editCatalog")
add_subdirectory("ui.com.catalog.editSpec")
add_subdirectory("ui.com.catalog.nodeTree")
add_subdirectory("ui.com.catalog.spceFile")
add_subdirectory("ui.com.common.clipBox")
add_subdirectory("ui.com.design.common.nodeSort")
add_subdirectory("ui.com.common.messageBox")
add_subdirectory("ui.com.config")
add_subdirectory("ui.com.design.advancedSearch")
add_subdirectory("ui.com.design.cableTray.branConn")
add_subdirectory("ui.com.design.cableTray.create")
add_subdirectory("ui.com.design.collisionCheck")
add_subdirectory("ui.com.design.createAxisNet")
add_subdirectory("ui.com.design.createNodeEquipment")
add_subdirectory("ui.com.design.createNodeGeneral")
add_subdirectory("ui.com.design.createPipeline")
add_subdirectory("ui.com.design.CSVFile")
add_subdirectory("ui.com.design.stru.createSection")
add_subdirectory("ui.com.design.stru.createLadder")
add_subdirectory("ui.com.design.stru.createPanel")
add_subdirectory("ui.com.design.stru.createStructure")
add_subdirectory("ui.com.design.stru.createWall")
add_subdirectory("ui.com.design.stru.createFitting")
add_subdirectory("ui.com.design.support.create")
add_subdirectory("ui.com.design.deviceLibrary")
add_subdirectory("ui.com.design.easyBran")
add_subdirectory("ui.com.design.equi.connect")
add_subdirectory("ui.com.design.equi.createExtrusion")
add_subdirectory("ui.com.design.equi.createPrimitives")
add_subdirectory("ui.com.design.equi.createRevolution")
add_subdirectory("ui.com.design.equi.hyperbola")
add_subdirectory("ui.com.design.equi.nozzle")
add_subdirectory("ui.com.design.equi.position")
add_subdirectory("ui.com.design.file.turnOverExport")
add_subdirectory("ui.com.design.nodeTree")
add_subdirectory("ui.com.common.PDMS")
add_subdirectory("ui.com.design.PCFExport")
add_subdirectory("ui.com.design.pipeConnect")
add_subdirectory("ui.com.design.pipeSlope")
add_subdirectory("ui.com.design.pipe.branSplitAndMerge")
add_subdirectory("ui.com.design.pipe.auto")
add_subdirectory("ui.com.design.pipe.mark3D")
add_subdirectory("ui.com.design.pipe.modifyBend")
add_subdirectory("ui.com.design.pipe.slopeModeling")
add_subdirectory("ui.com.design.pipe.mechanicalAnalysis")
add_subdirectory("ui.com.design.pipe.MTO.materialTable")
add_subdirectory("ui.com.design.pipe.pipeBulkEdit")
add_subdirectory("ui.com.design.pipe.pointInfo")
add_subdirectory("ui.com.design.nodeRotate")
add_subdirectory("ui.com.design.scene.snapshoot")
add_subdirectory("ui.com.design.nodeCopy")
add_subdirectory("ui.com.design.hierarchy")
add_subdirectory("ui.com.design.hvac.createBran")
add_subdirectory("ui.com.design.hvac.createComs")
add_subdirectory("ui.com.design.hvac.createHvac")
add_subdirectory("ui.com.design.modifyInstrument")
add_subdirectory("ui.com.helpAbout")
add_subdirectory("ui.com.measureDistance")
add_subdirectory("ui.com.node.checkName")
add_subdirectory("ui.com.node.delete")
add_subdirectory("ui.com.node.fileFunction")
add_subdirectory("ui.com.nodeAttributte")
add_subdirectory("ui.com.nodeCoordinate")
add_subdirectory("ui.com.python.console")
add_subdirectory("ui.com.scene.viewer")
add_subdirectory("ui.com.svg.viewer")
add_subdirectory("ui.com.DC.tip")
add_subdirectory("ui.com.vertex.edit")
add_subdirectory("ui.com.DC.dataSynchronism")
add_subdirectory("ui.com.node.list")
add_subdirectory("ui.com.node.search")
add_subdirectory("ui.com.createNode")
add_subdirectory("ui.com.design.members")
add_subdirectory("ui.com.design.currentNodeActive")
add_subdirectory("ui.com.design.debugConfig")
add_subdirectory("ui.com.design.nodeAxisEditor")
add_subdirectory("ui.com.design.addNodeByRange")
add_subdirectory("ui.com.design.svg")

get_all_targts(all_uiComponents_targets)
foreach(tgt ${all_uiComponents_targets})
	set_target_properties(${tgt} PROPERTIES FOLDER "ui/ui.components")
	set_target_properties(${tgt} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${tgt})
	set_target_properties(${tgt} PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}/ui/components/${tgt})
endforeach()
set(all_uiComponents_targets ${all_uiComponents_targets} CACHE STRING "" FORCE)
