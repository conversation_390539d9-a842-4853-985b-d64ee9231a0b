<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	<context>
		<name>ErrorDeviceParamSpecialization</name>
		<message>
			<source>You cannot operate the current node!</source>
			<translation>您不能操作当前节点!</translation>
		</message>
		<message>
			<source>Desc cannot Empty!</source>
			<translation>描述不能为空</translation>
		</message>
		<message>
			<source>Device is null!</source>
			<translation>设备为空!</translation>
		</message>
		<message>
			<source>Fail to clone Device!</source>
			<translation>设备模板克隆失败！</translation>
		</message>
		<message>
			<source>Device sample is null!</source>
			<translation>设备模板样本为空！</translation>
		</message>
		<message>
			<source>Unselect node!</source>
			<translation>模型树上未选择节点！</translation>
		</message>
		<message>
			<source>Current node cannot specialization device!</source>
			<translation>当前节点下无法参数化设备！</translation>
		</message>
		<message>
			<source>Device's name is repeat!</source>
			<translation>设备名称重复！</translation>
		</message>
	</context>

	<context>
		<name>DeviceLibraryWidget</name>
		<message>
			<source>DeviceLibrary</source>
			<translation>设备库</translation>
		</message>
		<message>
			<source>Add</source>
			<translation>导入设备</translation>
		</message>
		<message>
			<source>Edit</source>
			<translation>设备参数</translation>
		</message>
		<message>
			<source>Remove</source>
			<translation>移除设备</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>命名</translation>
		</message>
		<message>
			<source>Use</source>
			<translation>特化</translation>
		</message>
		<message>
			<source>Save</source>
			<translation>保存</translation>
		</message>
		<message>
			<source>Load</source>
			<translation>读取</translation>
		</message>
		<message>
			<source>Current node cannot null!</source>
			<translation>当前节点为空！</translation>
		</message>
		<message>
			<source>Current node cannot add in device library!</source>
			<translation>当前选中节点不符合标准！</translation>
		</message>
		<message>
			<source>Fail to add in device library!</source>
			<translation>设备模板添加失败！</translation>
		</message>
		<message>
			<source>Unselect device!</source>
			<translation>未选中设备模板！</translation>
		</message>
		<message>
			<source>Fail to remove!</source>
			<translation>删除失败！</translation>
		</message>
		<message>
			<source>Confirm Save!</source>
			<translation>确认是否保存</translation>
		</message>
		<message>
			<source>Select Device</source>
			<translation>选择设备模板</translation>
		</message>
	</context>

	<context>
		<name>DeviceParamEdit</name>
		<message>
			<source>Type</source>
			<translation>类型</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>modify name failed，exsit the same name</source>
			<translation>修改名称失败, 该名称已存在!</translation>
		</message>
		<message>
			<source>DeviceParamEdit</source>
			<translation>设备参数</translation>
		</message>
		<message>
			<source>DeviceStruct</source>
			<translation>设备结构</translation>
		</message>
		<message>
			<source>StructParams</source>
			<translation>结构参数</translation>
		</message>
		<message>
			<source>ParamFactors</source>
			<translation>设备参数</translation>
		</message>
		<message>
			<source>FactorType</source>
			<translation>类型</translation>
		</message>
		<message>
			<source>FactorName</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Add</source>
			<translation>添加</translation>
		</message>
		<message>
			<source>Remove</source>
			<translation>移除</translation>
		</message>
		<message>
			<source>UpdateParamFactor</source>
			<translation>保存</translation>
		</message>
		<message>
			<source>Name</source>
			<translation>设备名称</translation>
		</message>
		<message>
			<source>GType</source>
			<translation>通用类型</translation>
		</message>
		<message>
			<source>Desc</source>
			<translation>型号</translation>
		</message>
		<message>
			<source>Double</source>
			<translation>数据</translation>
		</message>
		<message>
			<source>Position</source>
			<translation>位置</translation>
		</message>
		<message>
			<source>Direction</source>
			<translation>朝向</translation>
		</message>
		<message>
			<source>StructParamName</source>
			<translation>名称</translation>
		</message>
		<message>
			<source>Default</source>
			<translation>值</translation>
		</message>
		<message>
			<source>Expression</source>
			<translation>表达式</translation>
		</message>
		<message>
			<source>FactorDefault</source>
			<translation>值</translation>
		</message>
		<message>
			<source>FactorDescription</source>
			<translation>描述</translation>
		</message>
		<message>
			<source>UpdateStructParams</source>
			<translation>保存</translation>
		</message>
		<message>
			<source>Legend</source>
			<translation>图例</translation>
		</message>
		<message>
			<source>Path</source>
			<translation>文件名</translation>
		</message>
		<message>
			<source>Ok</source>
			<translation>确认</translation>
		</message>
		<message>
			<source>Only the png in the default directory takes effect</source>
			<translation>仅默认目录下.png生效</translation>
		</message>
		<message>
			<source>GType cannot be empty!</source>
			<translation>通用类型不能为空</translation>
		</message>
		<message>
			<source>Desc cannot be empty!</source>
			<translation>型号不能为空</translation>
		</message>
		<message>
			<source>Device is null!</source>
			<translation>设备模板为空！</translation>
		</message>
		<message>
			<source>Device name repeat!</source>
			<translation>设备名称重复！</translation>
		</message>
		<message>
			<source>Device name cannot be empty!</source>
			<translation>设备名称不能为空！</translation>
		</message>
		<message>
			<source>Row's data abnormal!</source>
			<translation>行数据异常！</translation>
		</message>
		<message>
			<source>ParamFactor name repeat!</source>
			<translation>参数名称重复！</translation>
		</message>
		<message>
			<source>Fail to add ParamFactor</source>
			<translation>参数添加失败</translation>
		</message>
		<message>
			<source>Under the gType has same desc!</source>
			<translation>通用类型下已有相同型号设备模板</translation>
		</message>
		<message>
			<source>Select legend</source>
			<translation>选择图例文件</translation>
		</message>
		<message>
			<source>Device's equi is null!</source>
			<translation>设备模板中的根节点为空</translation>
		</message>
	</context>
	<context>
		<name>DeviceParamSpecialization</name>
		<message>
			<source>key</source>
			<translation>属性名称</translation>
		</message>
		<message>
			<source>value</source>
			<translation >属性值</translation>
		</message>
		<message>
			<source>DeviceParamSpecialization</source>
			<translation>创建参数化设备模型</translation>
		</message>
		<message>
			<source>DeviceName</source>
			<translation>设备名称</translation>
		</message>
		<message>
			<source>GType</source>
			<translation>通用类型</translation>
		</message>
		<message>
			<source>Desc</source>
			<translation>型号</translation>
		</message>
		<message>
			<source>ParamFactors</source>
			<translation>设备参数</translation>
		</message>
		<message>
			<source>Ok</source>
			<translation>确认</translation>
		</message>
		<message>
			<source>Cancel</source>
			<translation>取消</translation>
		</message>
		<message>
			<source>Capture</source>
			<translation>捕捉</translation>
		</message>
		<message>
			<source>Position</source>
			<translation>坐标</translation>
		</message>
		<message>
			<source>SetNozz</source>
			<translation>设置管嘴</translation>
		</message>
		<message>
			<source>Nozz</source>
			<translation>管嘴</translation>
		</message>
		<message>
			<source>Failed to create device by parameterization</source>
			<translation>参数化创建设备失败!</translation>
		</message>
	</context>
	<context>
		<name>NozzInfoEditDialog</name>
		<message>
			<source>NozzInfoEditDialog</source>
			<translation>设置管嘴</translation>
		</message>
		<message>
			<source>Grade Specification</source>
			<translation>等级规格</translation>
		</message>
		<message>
			<source>Standard</source>
			<translation>标准</translation>
		</message>
		<message>
			<source>Type</source>
			<translation>类型</translation>
		</message>
		<message>
			<source>Pipe Diameter</source>
			<translation>管径</translation>
		</message>
		<message>
			<source>Grade</source>
			<translation>等级</translation>
		</message>
		<message>
			<source>Ok</source>
			<translation>确认</translation>
		</message>	
		<message>
			<source>Cancle</source>
			<translation>取消</translation>
		</message>
	</context>
</TS>