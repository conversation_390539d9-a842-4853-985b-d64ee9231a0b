#include "WDDeviceMgr.h"
#include "core/WDObjectCreator.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/common/WDFileReader.hpp"
#include "WDBMNodeSerialize.h"

WD_NAMESPACE_BEGIN

WDDeviceMgr::WDDeviceMgr(WDCore& core)
    : _core(core)
{}
WDDeviceMgr::~WDDeviceMgr()
{
    _devices.clear();
}

WDDevice::SharedPtr WDDeviceMgr::addDevice(WDNode::SharedPtr equi)
{
    if (equi == nullptr)
        return nullptr;

    // 先查询设备模板管理中是否有同名设备模板
    if (this->queryDevice(equi->name()) != nullptr)
        return nullptr;

    // 新增设备模板
    _devices.push_back(WDDevice::MakeShared(_core, _core.getBMDesign().clone(equi)));
    return _devices.back();
}
bool                WDDeviceMgr::addDevice(WDDevice::SharedPtr device)
{
    if (this->queryDevice(device->name()) != nullptr)
    {
        //!TODO: 提示device设备模板名称重复
        return false;
    }

    // 整理已有devices
    std::map<std::string, WDDeviceVector>   mapDevice;
    for(auto& pDevice : _devices)
    {
        if (pDevice == nullptr)
            continue;

        std::string gType   =   pDevice->gType();
        auto        pItr    =   mapDevice.find(gType);
        if (mapDevice.find(gType) == mapDevice.end())
        {
            mapDevice[gType] = {pDevice};
        }
        else
        {
            pItr->second.push_back(pDevice);
        }
    }
     
    // 在相同通用类型的设备模板中校验是否有同描述的参数模板
    std::string gType   =   device->gType();
    auto        pItr    =   mapDevice.find(gType);
    if (pItr == mapDevice.end())
    {
        _devices.push_back(device);
    }
    else
    {
        auto&   tDevices    =   pItr->second;
        for (auto& pTDevice : tDevices)
        {
            if (pTDevice == nullptr)
                continue;

            std::string desc = pTDevice->desc();
            if (desc == device->desc())
            {
                //!TODO: 提示pDevice设备模板在相同通用类型的设备模板中有相同描述的设备模板
                return false;
            }
        }
        _devices.push_back(device);
    }

    return true;
}
size_t              WDDeviceMgr::addDevices(const WDDeviceVector& devices)
{
    size_t count = 0;
    for (auto& pDevice : devices)
    {
        if (this->addDevice(pDevice))
            count++;
    }
    return count;
}
bool                WDDeviceMgr::removeDevice(WDDevice::SharedPtr device)
{
    auto pItr = std::find(_devices.begin(), _devices.end(), device);
    if (pItr == _devices.end())
        return false;
    _devices.erase(pItr);
    return true;
}
WDDeviceVector      WDDeviceMgr::gTypeDevices(const std::string& gType)
{
    WDDeviceVector devices;
    for (auto& pDevice : _devices)
    {
        if (pDevice == nullptr)
            continue;

        if (pDevice->gType() == gType)
        {
            devices.push_back(pDevice);
        }
    }
    return devices;
}
WDDevice::SharedPtr WDDeviceMgr::queryDevice(const std::string& name)
{
    for (auto itr = _devices.begin(); itr != _devices.end(); ++itr)
    {
        WDDevice::SharedPtr pDevice = (*itr);
        if (pDevice == nullptr)
            continue;
        if (pDevice->name() == name)
            return pDevice;
    }
    return nullptr;
}

size_t  WDDeviceMgr::read(const char* fileName)
{
    // 清空设备模板列表
    _devices.clear();

    // 读取文件
    WDFileReader    file(fileName);
    if (file.isBad())
    {
        return  0;
    }
    file.readAll();
    size_t length = file.length();
    if (length == 0)
        return 0;

    // 解析成json文档
    JsonDoc doc;
    doc.Parse((char*)file.data(), file.length());
    if (doc.HasParseError())
        return 0;
    for (auto& deviceObj : doc.GetArray())
    {
        auto pDevice = this->readDevice(deviceObj);
        if (pDevice != nullptr)
            _devices.push_back(pDevice);
    }

    return length;
}
size_t  WDDeviceMgr::write(const char* fileName)
{
    JsonDoc doc;
    doc.SetArray();

    //序列化设备模板
    {
        for (int i = 0; i < _devices.size(); ++i)
        {
            this->writeDevice(doc, _devices.at(i)->toPtr<WDDevice>());
        }
    }

    StringBuffer buffer;
    rapidjson::Writer<StringBuffer> writer(buffer);
    doc.Accept(writer);
    std::string outString = buffer.GetString();

    FILE* fp = fopen(fileName, "w");
    if (fp == nullptr)
        return 0;
    fwrite(outString.data(), 1, outString.size(), fp);
    fclose(fp);
    fp = nullptr;

    return outString.size();
}

WDDevice::SharedPtr WDDeviceMgr::readDevice(const JsonValue& deviceObj)
{
    if (!deviceObj.IsObject() || deviceObj.ObjectEmpty())
        return nullptr;

    WDDevice::SharedPtr pDevice = nullptr;
    // 读取设备节点
    if (deviceObj.HasMember("Equi"))
    {
        auto pBMBase = _core.getBMBase("Design");
        if (pBMBase == nullptr)
            return nullptr;
        WD::WDBMNodeSerialize serial(*pBMBase);
        JsonDoc docEqui;
        // 将equi包装成数组
        JsonDoc docArray;
        JsonValue& arrayObj = docArray.SetArray();
        JsonValue tempObject(rapidjson::kObjectType);
        tempObject.CopyFrom(deviceObj["Equi"], docArray.GetAllocator());
        arrayObj.PushBack(tempObject, docArray.GetAllocator());
        auto res = serial.loadFromJson(docEqui, arrayObj);
        if (!res.empty())
        {
            auto pEquiNode = res.front();
            if (pEquiNode != nullptr)
            {
                pBMBase->updateRefs(*pEquiNode);
                pEquiNode->triggerUpdate();
                pDevice = WDDevice::MakeShared(_core, pEquiNode);
            }
        }
    }
    // 写入设备属性
    pDevice->read(deviceObj);
    return pDevice;
}
void                WDDeviceMgr::writeDevice(JsonDoc& doc, WDDevice::SharedPtr pDevice)
{
    if (pDevice == nullptr)
        return;

    JsonValue deviceObj(rapidjson::kObjectType);
    // 写入设备节点
    JsonDoc docEqui;
    JsonValue& equiArray = docEqui.SetArray();
    auto pBMBase = _core.getBMBase("Design");
    if (pBMBase == nullptr)
        return ;
    WD::WDBMNodeSerialize serial(*pBMBase);
    serial.saveToJson({pDevice->equi()}, docEqui, equiArray);
    if (!equiArray.Empty())
    {
        JsonValue tempObj(rapidjson::kObjectType);
        tempObj.CopyFrom(equiArray[0], doc.GetAllocator());
        // TODO: equiArray[0].Move()是否深拷贝待验证
        deviceObj.AddMember("Equi", tempObj, doc.GetAllocator());
    }
    {
        StringBuffer buffer;
        rapidjson::Writer<StringBuffer> writer(buffer);
        deviceObj.Accept(writer);
        std::string testStr = buffer.GetString();
        if (testStr.empty()){}
    }
    // 写入设备属性
    pDevice->write(doc, deviceObj);
    doc.PushBack(deviceObj, doc.GetAllocator());
}

WD_NAMESPACE_END