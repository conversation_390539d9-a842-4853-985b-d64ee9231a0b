#pragma once

#include "WDCore.h"
#include "core/node/WDNode.h"
#include <unordered_map>
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "WDRapidjson.h"
#include "../../ui.commonLibrary/ui.commonLib.property/property/WDProperties.h"

WD_NAMESPACE_BEGIN

// 参数因子类型
enum ParamFactorType
{
    PF_Unknown = 0,
    // double
    PF_Double,
    // 位置
    PF_Position,
    // 朝向
    PF_Direction
};
constexpr const char*   ParamFactorTypeToName(ParamFactorType type)
{
    switch (type)
    {
    case ParamFactorType::PF_Double:        return "Double";
    case ParamFactorType::PF_Position:      return "Position";
    case ParamFactorType::PF_Direction:     return "Direction";
    default:
        break;
    }

    return "Unknown";
}
ParamFactorType         ParamFactorTypeFromName(const char* name);

// 参数类型
enum DeviceParamType
{
    DP_Unknown = 0,
    // 形状参数
    DP_Shape,
    // TRS参数
    DP_TRS,
};
constexpr const char*   DeviceParamTypeToName(DeviceParamType type)
{
    switch (type)
    {
    case DeviceParamType::DP_Shape:     return "Shape";
    case DeviceParamType::DP_TRS:       return "TRS";
    default:
        break;
    }

    return "Unknown";
}
DeviceParamType         DeviceParamTypeFromName(const char* name);

// 参数因子
class ParamFactor : public WDObject
{
    WD_DECL_OBJECT(ParamFactor)
    
public:
    ParamFactor(ParamFactorType type, const std::string& description = "");
    virtual ~ParamFactor();

public:
    /*
    * brief 获取参数因子类型
    */
    inline ParamFactorType  type() const
    {
        return _type;
    }
    /*
    * brief 值转换为字符串
    */
    virtual inline std::string  valueToString() const
    {
        return "";
    }
    /*
    * brief 字符串转换为值
    */
    virtual inline void         valueFromString(const std::string& value)
    {
        WDUnused(value);
    }
    /*
    * brief 获取描述
    */
    inline const std::string&   description() const
    {
        return _description;
    }
    /*
    * brief 设置描述
    * param description 描述
    */
    inline void                 setDescription(const std::string& description)
    {
        _description = description;
    }

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                toDisplayProperties(WDPropertyGroup& pty);
    virtual void                write(JsonDoc& doc, JsonValue& object) const;
    virtual void                read(const JsonValue& object);

private:
    // 参数因子类型
    ParamFactorType _type;
    // 参数描述
    std::string     _description;
};
using ParamFactorVector = std::vector<ParamFactor::SharedPtr>;

template <typename T>
class ParamFactorT : public ParamFactor
{
    WD_DECL_OBJECT(ParamFactorT)

public:
    ParamFactorT(ParamFactorType type, const std::string& description = "", const T& value = T())
        : ParamFactor(type, description)
        , _value(value)
    {
        _valueText = WDTs("ParamFactor", "Value");
    }
    virtual ~ParamFactorT()
    {}

public:
    /*
    * brief 获取值
    */
    inline T                    value() const
    {
        return _value;
    }
    /*
    * brief 设置值
    * param value 值
    */
    inline void                 setValue(const T& value)
    {
        _value = value;
    }
    /*
    * brief 获取参数值显示文本
    */
    inline const::std::string&  valueText() const
    {
        return _valueText;
    }
    /*
    * brief 设置参数值显示文本
    */
    inline void                 setValueText(const std::string& valueText)
    {
        _valueText = valueText;
    }

public:
    virtual inline std::string  valueToString() const override
    {
        return ToString(_value);
    }
    virtual inline void         valueFromString(const std::string& value) override
    {
        _value = FromString<T>(value);
    }

public:
    virtual void                copy(const WDObject* pSrcObject) override
    {
        const ParamFactorT<T>* pSrc = dynamic_cast<const ParamFactorT<T>*>(pSrcObject);
        if (pSrc == nullptr)
            return;
        ParamFactor::copy(pSrcObject);

        this->_value        =   pSrc->_value;
    }
    virtual WDObject::SharedPtr clone() const override
    {
        auto p = ParamFactorT<T>::MakeShared(PF_Double);
        p->copy(this);
        return p;
    }

    template <typename TData>
    static WDProperty::SharedPtr AddPropertyT(WDPropertyGroup& pty, const std::string& name, const TData& value)
    {
        if constexpr (std::is_same_v<TData, int>)
            return pty.addPropertyInt(name, value);
        else if constexpr (std::is_same_v<TData, float>)
            return pty.addPropertyFloat(name, value);
        else if constexpr (std::is_same_v<TData, double>)
            return pty.addPropertyDouble(name, value);
        else if constexpr (std::is_same_v<TData, DVec3>)
            return pty.addPropertyDVec3(name, value);
        else if constexpr (std::is_same_v<TData, std::string>)
            return pty.addPropertyString(name, value);
        else
            return nullptr;
    }

    virtual void                toDisplayProperties(WDPropertyGroup& pty) override
    {
        //ParamFactor::toDisplayProperties(pty);

        auto pPty = AddPropertyT<T>(pty, _valueText, this->value());
        if (pPty == nullptr)
            return;
        auto pSubPty = WDProperty::As< WDPropertyT<T, GetPropertyDataType<T>()> >(pPty.get());
        if (pSubPty == nullptr)
            return;
        // 值
        pSubPty->setName(name() + "=" + this->description());
        pSubPty->setFunctionOwningObjectValueSet(std::bind(&ParamFactorT<T>::setValue, this, std::placeholders::_1));
    }
    virtual void                write(JsonDoc& doc, JsonValue& object) const override
    {
        ParamFactor::write(doc, object);
        if constexpr (std::is_same_v<T, double>)
        {
            object.AddMember("Value", _value, doc.GetAllocator());
        }
    }
    virtual void                read(const JsonValue& object) override
    {
        ParamFactor::read(object);
        if (object.HasMember("Value"))
        {
            auto& valueObj = object["Value"];
            if constexpr (std::is_same_v<T, double>)
            {
                if (valueObj.IsDouble())
                {
                    _value = valueObj.GetDouble();
                }
            }
        }
    }

private:
    // 参数值
    T               _value;
    // 参数值显示文本
    std::string     _valueText;
};
/**
* @brief double类型参数因子
*/
WD_DECL_CLASS_UUID(ParamFactorDouble,"F02D7670-4B6B-4C7F-A225-1808D75F58A9");
class ParamFactorDouble : public ParamFactorT<double>
{
    WD_DECL_OBJECT(ParamFactorDouble)

public:
    ParamFactorDouble()
        : ParamFactorT<double>(ParamFactorType::PF_Double)
    {}
    virtual ~ParamFactorDouble()
    {}

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
};
using ParamFactorDoubleVector = std::vector<ParamFactorDouble::SharedPtr>;
/**
* @brief Vec3类型参数因子
*/
WD_DECL_CLASS_UUID(ParamFactorVec3,"31B073D5-FF36-45B0-89E6-789C4B8929F6");
class ParamFactorVec3 : public ParamFactorT<Vec3>
{
    WD_DECL_OBJECT(ParamFactorVec3)

public:
    ParamFactorVec3(ParamFactorType type)
        : ParamFactorT<Vec3>(type)
    {}
    virtual ~ParamFactorVec3()
    {}

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
};
using ParamFactorVec3Vector = std::vector<ParamFactorVec3::SharedPtr>;
/**
* @brief 位置参数因子
*/
WD_DECL_CLASS_UUID(ParamFactorPos,"78AB6834-9CE3-455F-BE01-8421C84C8096");
class ParamFactorPos : public ParamFactorVec3
{
    WD_DECL_OBJECT(ParamFactorPos)

public:
    ParamFactorPos()
        : ParamFactorVec3(ParamFactorType::PF_Position)
    {
        this->setValueText(WDTs("ParamFactor", "Position"));
    }
    virtual ~ParamFactorPos()
    {}

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
};
using ParamFactorPosVector = std::vector<ParamFactorPos::SharedPtr>;
/**
* @brief 朝向参数因子
*/
WD_DECL_CLASS_UUID(ParamFactorDir,"59C9C372-9514-4A03-AE50-BFEB6DC3C032");
class ParamFactorDir : public ParamFactorVec3
{
    WD_DECL_OBJECT(ParamFactorDir)

public:
    ParamFactorDir()
        : ParamFactorVec3(ParamFactorType::PF_Direction)
    {}
    virtual ~ParamFactorDir()
    {}

public:
    void setDirection(const std::string& dir);
    std::string direction() const;

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                toDisplayProperties(WDPropertyGroup& pty) override;
};
using ParamFactorDirVector = std::vector<ParamFactorDir::SharedPtr>;


// 设备模板参数
class WDDevice;
class WDDeviceParam : public WDObject
{
    WD_DECL_OBJECT(WDDeviceParam)

public:
    WDDeviceParam(WD::WDCore& core, DeviceParamType type);
    WDDeviceParam(WD::WDCore& core, DeviceParamType type, WDNode& ref);
    virtual ~WDDeviceParam();

public:
    /*
    * brief 获取参数类型
    */
    inline DeviceParamType          type() const
    {
        return _type;
    }
    /**
    * @brief 获取引用节点引用的对象
    */
    inline WDNode::SharedPtr        pRef()
    {
        return _pRef.lock();
    }
    /**
    * @brief 设置引用节点引用的对象
    */
    inline void                     setPRef(WDNode::SharedPtr pRef)
    {
        _pRef.reset();
        _pRef = pRef;
        if (pRef != nullptr)
        {
            _refNodeId = pRef->uuid();
        }
    }

    /**
    * @brief 以指定参数因子列表更新参数引用节点
    * @param factors 参数因子列表
    * @return 是否更新成功
    */
    virtual bool                apply(const ParamFactorVector& factors)
    {
        WDUnused(factors);
        return false;
    }
    /*
    * brief 更新设备数据
    */
    void                        update(WDDevice& device);

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                write(JsonDoc& doc, JsonValue& object) const;
    virtual void                read(const JsonValue& object);
    virtual void                toDisplayProperties(WDPropertyGroup& pty) 
    {
        WDCxtTsBg("WDObject");

        pty.addPropertyString(WDCxtTs("name"), this->name(), true)
            ->setFunctionOwningObjectValueSet(std::bind(&WDObject::setName, this, std::placeholders::_1));

        pty.addPropertyGuid(WDCxtTs("id"), this->uuid(), false);

        WDCxtTsEd();
    }
protected:
    /**
    * @brief 解析表达式
    * @param factors 常量映射数组
    * @param expression 表达式
    */
    std::optional<double>          parseExpression(const ParamFactorDoubleVector& factors, const std::string& expression);

protected:
    WD::WDCore& _core;
private:
    // 引用节点GUID
    WDUuid              _refNodeId;
    // 引用节点
    WDNode::WeakPtr     _pRef;
    // 类型
    DeviceParamType     _type;
};
using WDDeviceParamVector   =   std::vector<WDDeviceParam::SharedPtr>;
// 形状参数
WD_DECL_CLASS_UUID(WDDeviceShapeParam,"3CE323FE-5EB8-47FB-9A7C-486FF945E9C0");
class WDDeviceShapeParam : public WDDeviceParam
{
    WD_DECL_OBJECT(WDDeviceShapeParam)

public:
    WDDeviceShapeParam(WDCore& core);
    WDDeviceShapeParam(WDCore& core, WDNode& node, size_t index, const std::string& expression = "");
    virtual ~WDDeviceShapeParam();

public:
    /**
    * @brief 节点是对应类型的数据节点
    */
    static inline bool  Is(WDDeviceParam::SharedPtr param)
    {
        return dynamic_cast<WDDeviceShapeParam*>(param.get()) != nullptr;
    }

public:
    /**
    * @brief 校验参数引用节点与参数序号是否合理
    * @param node 参数引用节点
    * @param index 参数序号
    */
    static bool     CheckNodeIndex(WDNode& node, size_t index);

public:
    virtual bool                apply(const ParamFactorVector& factors) override;
    /**
    * @brief 获取在所属节点数据中序号
    */
    inline int                  index() const
    {
        return (int)_index;
    }
    /**
    * @brief 获取形状参数表达式
    */
    inline const std::string&   expression() const
    {
        return _expression;
    }
    /**
    * @brief 设置形状参数表达式
    */
    inline void                 setExpression(const std::string& expression)
    {
        _expression = expression;
    }

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                toDisplayProperties(WDPropertyGroup& pty) override;
    virtual void                write(JsonDoc& doc, JsonValue& object) const override;
    virtual void                read(const JsonValue& object) override;

private:
    // 在所属节点数据中序号
    size_t      _index;
    // 形状参数表达式
    std::string _expression;
};
// TRS参数
WD_DECL_CLASS_UUID(WDDeviceTRSParam,"255D7F41-F817-4269-8F54-0E980DADA294");
class WDDeviceTRSParam : public WDDeviceParam
{
    WD_DECL_OBJECT(WDDeviceTRSParam)

public:
    WDDeviceTRSParam(WDCore& core);
    WDDeviceTRSParam(WDCore& core
        , WDNode& node
        , const std::string& posExpressionX = ""
        , const std::string& posExpressionY = ""
        , const std::string& posExpressionZ = ""
        , const std::string& dirExpression = "");
    virtual ~WDDeviceTRSParam();

public:
    /**
    * @brief 节点是对应类型的数据节点
    */
    static inline bool                          Is(WDDeviceParam::SharedPtr param)
    {
        return dynamic_cast<WDDeviceTRSParam*>(param.get()) != nullptr;
    }
    /**
    * @brief 获取节点附带的该类型的数据
    */
    static inline WDDeviceTRSParam::SharedPtr   Data(WDDeviceParam::SharedPtr param)
    {
        return WDDeviceTRSParam::ToShared(dynamic_cast<WDDeviceTRSParam*>(param.get()));
    }

public:
    virtual bool    apply(const ParamFactorVector& factors) override;
    /**
    * @brief 获取位置表达式X
    */
    inline const std::string&   posExpressionX() const
    {
        return _posExpressionX;
    }
    /**
    * @brief 设置位置表达式
    */
    inline void                 setPosExpressionX(const std::string& posExpressionX)
    {
        _posExpressionX = posExpressionX;
    }
    /**
    * @brief 获取位置表达式X
    */
    inline const std::string&   posExpressionY() const
    {
        return _posExpressionY;
    }
    /**
    * @brief 设置位置表达式
    */
    inline void                 setPosExpressionY(const std::string& posExpressionY)
    {
        _posExpressionY = posExpressionY;
    }
    /**
    * @brief 获取位置表达式X
    */
    inline const std::string&   posExpressionZ() const
    {
        return _posExpressionZ;
    }
    /**
    * @brief 设置位置表达式
    */
    inline void                 setPosExpressionZ(const std::string& posExpressionZ)
    {
        _posExpressionZ = posExpressionZ;
    }
    /**
    * @brief 获取朝向表达式
    */
    inline const std::string&   dirExpression() const
    {
        return _dirExpression;
    }
    /**
    * @brief 设置朝向表达式
    */
    inline void                 setDirExpression(const std::string& dirExpression)
    {
        _dirExpression = dirExpression;
    }

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                toDisplayProperties(WDPropertyGroup& pty) override;
    virtual void                write(JsonDoc& doc, JsonValue& object) const override;
    virtual void                read(const JsonValue& object) override;

private:
    // 位置表达式
    std::string _posExpressionX;
    std::string _posExpressionY;
    std::string _posExpressionZ;
    // 朝向表达式
    std::string _dirExpression;
};


// 设备模板
WD_DECL_CLASS_UUID(WDDevice,"B9B62F12-7296-4800-B5A3-6F5D7842CB08");
class WDDevice : public WDObject
{
    WD_DECL_OBJECT(WDDevice)

public:
    WDDevice(WD::WDCore& core);
    WDDevice(WD::WDCore& core, WDNode::SharedPtr equi);
    virtual ~WDDevice();

private:
    using MapParam = std::map<WDNode*, std::vector<WDDeviceParam::SharedPtr>>;

public:
    /*
    * brief 获取图例路径
    */
    static inline const std::string&           LegendPath()
    {
        return s_LegendPath;
    }

public:
    /*
    * brief 获取通用类型
    */
    inline const std::string&                  gType() const
    {
        return _gType;
    }
    /*
    * brief 设置通用类型
    */
    inline const void                          setGType(const std::string& gType)
    {
        _gType = gType;
    }
    /*
    * brief 获取描述
    */
    inline const std::string&                  desc() const
    {
        return _description;
    }
    /*
    * brief 设置描述
    */
    inline const void                          setDesc(const std::string& desc)
    {
        _description = desc;
    }
    /*
    * brief 获取图例
    */
    inline const std::string&                  legend() const
    {
        return _legend;
    }
    /*
    * brief 设置图例
    */
    inline const void                          setLegend(const std::string& legend)
    {
        _legend = legend;
    }

    /*
    * brief 获取设备节点
    */
    inline WDNode::SharedPtr            equi()
    {
        return _pEqui;
    }

    /*
    * brief 定义形状参数(将 设备节点下的子孙节点的形状参数 定义为 设备模板的参数)
    * param descendant 子孙节点
    * param index 形状参数序号
    * param name 参数名称
    * return 定义的参数对象指针
    */
    WDDeviceParam::SharedPtr            defineShapeParam(WDNode& descendant
        , size_t index
        , const std::string& name = std::string()
        , const std::string& expression = std::string());
    /*
    * brief 定义TRS参数(将 设备节点下的子孙节点的TRS信息 定义为 设备模板的参数)
    * param descendant 子孙节点
    * param name 参数名称
    * return 定义的参数对象指针
    */
    WDDeviceParam::SharedPtr            defineTRSParam(WDNode& descendant
        , const std::string& name = std::string()
        , const std::string& posExpressionX = std::string()
        , const std::string& posExpressionY = std::string()
        , const std::string& posExpressionZ = std::string()
        , const std::string& dirExpression = std::string());
    /*
    * brief 取消定义参数
    * param index 参数在参数列表中的序号
    */
    bool                                undefineParam(WDDeviceParam::SharedPtr param);
    /*
    * brief 取消定义引用指定节点的参数
    * param node 节点
    * return 取消定义的参数个数
    */
    size_t                              undefineRefNodeParam(WDNode& node);
    /*
    * brief 取消定义所有参数
    */
    inline bool                         undefineParams()
    {
        _params.clear();
        return true;
    }
    /*
    * brief 获取参数列表
    */
    inline const WDDeviceParamVector&   params() const
    {
        return _params;
    }
    /*
    * brief 查询模板参数
    */
    WDDeviceParam::SharedPtr            queryParam(WDNode& descendant, size_t index);
    WDDeviceParam::SharedPtr            queryParam(WDNode& descendant);

    /*
    * brief 新增Double参数因子
    * param name 参数因子名称
    * param description 参数因子描述
    * param value 参数因子值
    */
    ParamFactor::SharedPtr          addDoubleFactor(const std::string& name, const std::string& description = "", double value = 0.0);
    /*
    * brief 新增位置参数因子
    * param name 参数因子名称
    * param description 参数因子描述
    * param value 参数因子值
    */
    ParamFactor::SharedPtr          addPositionFactor(const std::string& name
        , const std::string& description = ""
        , const Vec3& value = Vec3());
    /*
    * brief 新增朝向参数因子
    * param name 参数因子名称
    * param description 参数因子描述
    * param value 参数因子值
    */
    ParamFactor::SharedPtr          addDirectionFactor(const std::string& name
        , const std::string& description = ""
        , const std::string& value = "");
    /*
    * brief 移除设备模板参数因子
    */
    bool                            removeFactor(ParamFactor::SharedPtr pFactor);
    /*
    * brief 清除设备模板参数因子
    */
    inline void                     clearFactors()
    {
        _factors.clear();
    }
    /*
    * brief 获取参数因子列表
    */
    inline const ParamFactorVector& factors() const
    {
        return _factors;
    }

    /*
    * brief 更新设备
    * -info 将参数因子的值带入到模板参数中计算更新设备节点
    */
    void                            apply();

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    void                        write(JsonDoc& doc, JsonValue& object) const;
    void                        read(const JsonValue& object);

private:
    /*
    * brief 纠正参数引用
    */
    void                        rectifyParam(WDNode* parent1, WDNode* parent2, const MapParam& mapParams);

private:
    // 图例文件路径
    static std::string  s_LegendPath;

    // 设备节点
    WDNode::SharedPtr   _pEqui;
    // 设备参数因子
    ParamFactorVector   _factors;
    // 设备模板参数
    WDDeviceParamVector _params;
    // 通用类型
    std::string         _gType;
    // 描述
    std::string         _description;
    // 图例
    std::string         _legend;
    WDCore& _core;
};
using WDDeviceVector = std::vector<WDDevice::SharedPtr>;
WD_NAMESPACE_END