#include "WDDevice.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/math/DirectionParser.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"

WD_NAMESPACE_BEGIN

ParamFactorType ParamFactorTypeFromName(const char* name)
{
    if (_stricmp(name, "Double") == 0)           return ParamFactorType::PF_Double;
    else if (_stricmp(name, "Position") == 0)    return ParamFactorType::PF_Position;
    else if (_stricmp(name, "Direction") == 0)   return ParamFactorType::PF_Direction;
      
    return ParamFactorType::PF_Unknown;
}

DeviceParamType DeviceParamTypeFromName(const char* name)
{
    if (_stricmp(name, "Shape") == 0)       return DeviceParamType::DP_Shape;
    else if (_stricmp(name, "TRS") == 0)    return DeviceParamType::DP_TRS;
      
    return DeviceParamType::DP_Unknown;
}

    
ParamFactor::ParamFactor(ParamFactorType type, const std::string& description)
    : _type(type)
    , _description(description)
{}
ParamFactor::~ParamFactor()
{}
void                ParamFactor::copy(const WDObject* pSrcObject)
{
    const ParamFactor* pSrc = dynamic_cast<const ParamFactor*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDObject::copy(pSrcObject);

    this->_description  =   pSrc->_description;
}
WDObject::SharedPtr ParamFactor::clone() const
{
    auto p = ParamFactor::MakeShared(ParamFactorType::PF_Unknown);
    p->copy(this);
    return p;
}
void                ParamFactor::toDisplayProperties(WDPropertyGroup& pty)
{
    // 名称
    pty.addPropertyString(WDTs("WDObject", "name"), this->name())
        ->setFunctionOwningObjectValueSet(std::bind(&ParamFactor::setName, this, std::placeholders::_1));

    // 描述
    pty.addPropertyString(WDTs("ParamFactor", "description"), this->description())
        ->setFunctionOwningObjectValueSet(std::bind(&ParamFactor::setDescription, this, std::placeholders::_1));
}
void                ParamFactor::write(JsonDoc& doc, JsonValue& object) const
{
    auto& allocator = doc.GetAllocator();
    // 名称
    object.AddMember("Name", JsonValue(this->name().c_str(), allocator), allocator);
    // 参数因子类型
    object.AddMember("Type", JsonValue(ParamFactorTypeToName(_type), allocator), allocator);
    // 参数因子描述
    object.AddMember("Description", JsonValue(_description.c_str(), allocator), allocator);
}
void                ParamFactor::read(const JsonValue& object)
{
    // 名曾
    if (object.HasMember("Name"))
    {
        this->setName(object["Name"].GetString());
    }
    // 参数描述
    if (object.HasMember("Description"))
    {
        _description = object["Description"].GetString();
    }
}

void                ParamFactorDouble::copy(const WDObject* pSrcObject)
{
    const ParamFactorDouble* pSrc = dynamic_cast<const ParamFactorDouble*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    ParamFactorT<double>::copy(pSrcObject);
}
WDObject::SharedPtr ParamFactorDouble::clone() const
{
    auto p = ParamFactorDouble::MakeShared();
    p->copy(this);
    return p;
}

void                ParamFactorVec3::copy(const WDObject* pSrcObject)
{
    const ParamFactorVec3* pSrc = dynamic_cast<const ParamFactorVec3*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    ParamFactorT<Vec3>::copy(pSrcObject);
}
WDObject::SharedPtr ParamFactorVec3::clone() const
{
    auto p = ParamFactorVec3::MakeShared(ParamFactorType::PF_Unknown);
    p->copy(this);
    return p;
}

void                ParamFactorPos::copy(const WDObject* pSrcObject)
{
    const ParamFactorPos* pSrc = dynamic_cast<const ParamFactorPos*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    ParamFactorVec3::copy(pSrcObject);
}
WDObject::SharedPtr ParamFactorPos::clone() const
{
    auto p = ParamFactorPos::MakeShared();
    p->copy(this);
    return p;
}

void                ParamFactorDir::copy(const WDObject* pSrcObject)
{
    const ParamFactorDir* pSrc = dynamic_cast<const ParamFactorDir*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    ParamFactorVec3::copy(pSrcObject);
}
WDObject::SharedPtr ParamFactorDir::clone() const
{
    auto p = ParamFactorDir::MakeShared();
    p->copy(this);
    return p;
}

void        ParamFactorDir::setDirection(const std::string& dir)
{
    Vec3 direction;
    if (DDirectionParserXYZ::Direction(dir, direction))
        this->setValue(direction);
}
std::string ParamFactorDir::direction() const
{
    return DirectionParser::OutputStringByDirection(this->value());
}

void        ParamFactorDir::toDisplayProperties(WDPropertyGroup& pty)
{
    // 值
    WDPropertyString::SharedPtr pPty = pty.addPropertyString(WDTs("ParamFactor", "Direction"), this->direction());
    if (pPty != nullptr)
    {
        pPty->setName(name() + "=" + this->description());
        pPty->setFunctionOwningObjectValueSet(std::bind(&ParamFactorDir::setDirection, this, std::placeholders::_1));
    }
}


WDDeviceParam::WDDeviceParam(WD::WDCore& core, DeviceParamType type)
    : _core(core), _type(type)
{}
WDDeviceParam::WDDeviceParam(WD::WDCore& core, DeviceParamType type, WDNode& ref)
    : _core(core)
    , _type(type)
    , _pRef(WDNode::ToShared(&ref))
{
    _refNodeId = ref.uuid();
}
WDDeviceParam::~WDDeviceParam()
{
}

void                WDDeviceParam::update(WDDevice& device)
{
    // 更新pRef
    auto pEqui = device.equi();
    if (pEqui != nullptr)
    {
        WDNode::RecursionHelpterR(*pEqui.get(), [this](const WDUuid& refNodeId, WDNode& node)
            {
                if (node.uuid() != refNodeId)
                    return false;
                this->_pRef = WDNode::ToShared(&node);
                return true;

            }, _refNodeId);
    }
}

void                WDDeviceParam::copy(const WDObject* pSrcObject)
{
    const WDDeviceParam* pSrc = dynamic_cast<const WDDeviceParam*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDObject::copy(pSrcObject);

    this->_pRef      =   pSrc->_pRef;
}
WDObject::SharedPtr WDDeviceParam::clone() const
{
    auto p = WDDeviceParam::MakeShared(_core, DeviceParamType::DP_Unknown);
    p->copy(this);
    return p;
}
void                WDDeviceParam::write(JsonDoc& doc, JsonValue& object) const
{
    auto& allocator = doc.GetAllocator();
    // 参数因子类型
    object.AddMember("Type", JsonValue(DeviceParamTypeToName(_type), allocator), allocator);
    // 引用节点GUID
    object.AddMember("RefNodeId", JsonValue(_refNodeId.toString().c_str(), allocator), allocator);
}
void                WDDeviceParam::read(const JsonValue& object)
{
    // 引用节点GUID
    if (object.HasMember("RefNodeId"))
    {
        auto tempStr = object["RefNodeId"].GetString();
        _refNodeId = FromString<WD::WDUuid>(tempStr);
    }
}

CAttributeGet CAttriGet(WD::WDCore& core, const ParamFactorDoubleVector& factors)
{
    // 准备常量表
    std::map<std::string, std::any> vars;
    for (auto& factor : factors)
    {
        if (factor == nullptr)
            continue;
        if (vars.find(factor->name()) != vars.end())
        {
            assert(false && "重复的参数!");
            continue;
        }
        vars.emplace(factor->name(), factor->value());
    }

    return core.getBMCatalog().modelBuilder().cAttributeGet(
        [&vars](const std::string& name, std::any& outValue, const std::optional<int>& index)->bool
        {
            WDUnused(index);
            auto itr = vars.find(name);
            if (itr != vars.end())
            {
                outValue = itr->second;
                return true;
            }
            return false;
        });

}

std::optional<double>  WDDeviceParam::parseExpression(const ParamFactorDoubleVector& factors, const std::string& expression)
{
    auto aGet = CAttriGet(_core, factors);
    // 解析几何体参数表达式
    bool bOk = false;
    auto res = aGet.execExpression(expression).convertToDouble(&bOk);
    if (bOk)
        return res;
    return std::optional<double>();
}


WDDeviceShapeParam::WDDeviceShapeParam(WD::WDCore& core)
    : WDDeviceParam(core, DeviceParamType::DP_Shape)
{}
WDDeviceShapeParam::WDDeviceShapeParam(WD::WDCore& core, WDNode& node, size_t index, const std::string& expression)
    : WDDeviceParam(core, DeviceParamType::DP_Shape, node)
    , _index(index)
    , _expression(expression)
{
}
WDDeviceShapeParam::~WDDeviceShapeParam()
{
}

bool        WDDeviceShapeParam::CheckNodeIndex(WDNode& node, size_t index)
{
    if (node.isType("NOZZ"))
    {
        return index <= 0;
    }
    else if (node.isType("BOX") || node.isType("NBOX"))
    {
        return index <= 2;
    }
    else if (node.isType("CYLI") || node.isType("NCYL"))
    {
        return index <= 1;
    }
    else if (node.isType("DISH") || node.isType("NDIS"))
    {
        return index <= 2;
    }
    else if (node.isType("CTOR") || node.isType("NCTO"))
    {
        return index <= 2;
    }
    else if (node.isType("PYRA") || node.isType("NPYR"))
    {
        return index <= 6;
    }
    else if (node.isType("CONE") || node.isType("NCON"))
    {
        return index <= 2;
    }
    else if (node.isType("RTOR") || node.isType("NRTO"))
    {
        return index <= 3;
    }
    else if (node.isType("SLCY") || node.isType("NSLC"))
    {
        return index <= 5;
    }
    else if (node.isType("SNOU") || node.isType("NSNO"))
    {
        return index <= 4;
    }
    else if (node.isType("SPHE") || node.isType("NSPH"))
    {
        return index <= 0;
    }
    else if (node.isType("ELPS") || node.isType("NELP"))
    {
        return index <= 3;
    }
    else if (node.isType("EXTR") || node.isType("NEXT"))
    {
        return index <= 0;
    }
    else if (node.isType("REVO") || node.isType("NREV"))
    {
        return index <= 0;
    }
    else if (node.isType("POLYHEDRON") || node.isType("NPOLYHEDRON"))
    {
        return index <= 0;
    }
    else
    {
        return false;
    }
}

bool        WDDeviceShapeParam::apply(const ParamFactorVector& factors)
{
    auto pRefNode = this->pRef();
    if (pRefNode == nullptr)
    {
        // !TODO: 提示引用节点为空
        return false;
    }

    // 提取double类型的参数因子
    ParamFactorDoubleVector dFactors;
    for (auto& factor : factors)
    {
        auto dFactor = dynamic_cast<ParamFactorDouble*>(factor.get());
        if (dFactor == nullptr)
            continue;
        dFactors.push_back(ParamFactorDouble::ToShared(dFactor));
    }
    
    // 解析几何体参数表达式
    auto parseRes = this->parseExpression(dFactors, _expression);
    if (!parseRes)
    {
        // !TODO: 提示解析几何体参数表达式失败
        return false;
    }

    std::string attrName = "";
    // 更新引用节点的几何体参数
    if (pRefNode->isType("NOZZ"))
    {
        attrName = "Height";
    }
    else if (pRefNode->isType("BOX") || pRefNode->isType("NBOX"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Xlength";
            break;
        case 1:
            attrName = "Ylength";
            break;
        case 2:
            attrName = "Zlength";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("CYLI") || pRefNode->isType("NCYL"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Diameter";
            break;
        case 1:
            attrName = "Height";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("DISH") || pRefNode->isType("NDIS"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Diameter";
            break;
        case 1:
            attrName = "Radius";
            break;
        case 2:
            attrName = "Height";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("CTOR") || pRefNode->isType("NCTO"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Rinside";
            break;
        case 1:
            attrName = "Routside";
            break;
        case 2:
            attrName = "Angle";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("PYRA") || pRefNode->isType("NPYR"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Xtop";
            break;
        case 1:
            attrName = "Ytop";
            break;
        case 2:
            attrName = "Xbottom";
            break;
        case 3:
            attrName = "Ybottom";
            break;
        case 4:
            attrName = "Height";
            break;
        case 5:
            attrName = "Xoffset";
            break;
        case 6:
            attrName = "Yoffset";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("CONE") || pRefNode->isType("NCON"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Dtop";
            break;
        case 1:
            attrName = "Dbottom";
            break;
        case 2:
            attrName = "Height";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("RTOR") || pRefNode->isType("NRTO"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Rinside";
            break;
        case 1:
            attrName = "Routside";
            break;
        case 2:
            attrName = "Height";
            break;
        case 3:
            attrName = "Angle";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("SLCY") || pRefNode->isType("NSLC"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Diameter";
            break;
        case 1:
            attrName = "Height";
            break;
        case 2:
            attrName = "Xtshear";
            break;
        case 3:
            attrName = "Ytshear";
            break;
        case 4:
            attrName = "Xbshear";
            break;
        case 5:
            attrName = "Ybshear";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("SNOU") || pRefNode->isType("NSNO"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Dtop";
            break;
        case 1:
            attrName = "Dbottom";
            break;
        case 2:
            attrName = "Height";
            break;
        case 3:
            attrName = "Xoffset";
            break;
        case 4:
            attrName = "Yoffset";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("SPHE") || pRefNode->isType("NSPH"))
    {
        switch (_index)
        {
        case 0:
            attrName = "Diameter";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("ELPS") || pRefNode->isType("NELP"))
    {
        switch (_index)
        {
        case 0:
            attrName = "xDiameter";
            break;
        case 1:
            attrName = "yDiameter";
            break;
        case 2:
            attrName = "zDiameter";
            break;
        default:
            break;
        }
    }
    else if (pRefNode->isType("EXTR") || pRefNode->isType("NEXT"))
    {
        attrName = "Height";
    }
    else if (pRefNode->isType("REVO") || pRefNode->isType("NREV"))
    {
        attrName = "Angle";
    }
    else if (pRefNode->isType("POLYHEDRON") || pRefNode->isType("NPOLYHEDRON"))
    {
        attrName = "";
    }
    else
    {
        return false;
    }

    if (attrName.empty())
        return false;

    if(!pRefNode->setAttribute(attrName, parseRes.value()))
        return false;

    pRefNode->triggerUpdate();
    return true;
}

void                WDDeviceShapeParam::copy(const WDObject* pSrcObject)
{
    const WDDeviceShapeParam* pSrc = dynamic_cast<const WDDeviceShapeParam*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDDeviceParam::copy(pSrcObject);

    this->_index        =   pSrc->_index;
    this->_expression   =   pSrc->_expression;
}
WDObject::SharedPtr WDDeviceShapeParam::clone() const
{
    auto p = WDDeviceShapeParam::MakeShared(_core);
    p->copy(this);
    return p;
}
void                WDDeviceShapeParam::toDisplayProperties(WDPropertyGroup& pty)
{
    // 形状参数表达式
    pty.addPropertyString(WDTs("WDDeviceParam", "Expression"), this->expression())
        ->setFunctionOwningObjectValueSet(std::bind(&WDDeviceShapeParam::setExpression, this, std::placeholders::_1));
}
void                WDDeviceShapeParam::write(JsonDoc& doc, JsonValue& object) const
{
    WDDeviceParam::write(doc, object);
    auto& allocator = doc.GetAllocator();
    object.AddMember("Index", (int)_index, allocator);
    object.AddMember("Expression", JsonValue(_expression.c_str(), allocator), allocator);
}
void                WDDeviceShapeParam::read(const JsonValue& object)
{
    WDDeviceParam::read(object);
    if (object.HasMember("Index"))
    {
        _index = object["Index"].GetInt();
    }
    if (object.HasMember("Expression"))
    {
        _expression = object["Expression"].GetString();
    }
}


WDDeviceTRSParam::WDDeviceTRSParam(WD::WDCore& core)
    : WDDeviceParam(core, DeviceParamType::DP_TRS)
{}
WDDeviceTRSParam::WDDeviceTRSParam(WD::WDCore& core
    , WDNode& node
    , const std::string& posExpressionX
    , const std::string& posExpressionY
    , const std::string& posExpressionZ
    , const std::string& dirExpression)
    : WDDeviceParam(core, DeviceParamType::DP_TRS, node)
    , _posExpressionX(posExpressionX)
    , _posExpressionY(posExpressionY)
    , _posExpressionZ(posExpressionZ)
    , _dirExpression(dirExpression)
{
}
WDDeviceTRSParam::~WDDeviceTRSParam()
{
}

bool                WDDeviceTRSParam::apply(const ParamFactorVector& factors)
{
    auto pRefNode = this->pRef();
    if (pRefNode == nullptr)
    {
        // !TODO: 提示引用节点为空
        return false;
    }
    
    // 提取double类型的参数因子
    ParamFactorDoubleVector dFactors;
    for (auto& factor : factors)
    {
        auto dFactor = dynamic_cast<ParamFactorDouble*>(factor.get());
        if (dFactor == nullptr)
            continue;
        dFactors.push_back(ParamFactorDouble::ToShared(dFactor));
    }
    
    // 获取引用节点更新前位置和朝向
    Vec3    lPos    =   pRefNode->getAttribute("Position").toDVec3();
    DQuat   qRot    =   pRefNode->getAttribute("Orientation").toQuat();
    DVec3   gRootZ  =   DVec3(0,0,1);// TODO: 获取设备根节点Z朝向
    DVec3   lDir    =   DMat4::FromQuat(qRot) * gRootZ;
    // 解析位置和朝向表达式
    double  posX    =   lPos.x;
    double  posY    =   lPos.y;
    double  posZ    =   lPos.z;
    if (!_posExpressionX.empty())
    {
        auto    resX    =   this->parseExpression(dFactors, _posExpressionX);
        if (resX)
        {
            posX = resX.value();
        }
    }
    if (!_posExpressionY.empty())
    {
        auto    resY    =   this->parseExpression(dFactors, _posExpressionY);
        if (resY)
        {
            posY = resY.value();
        }
    }
    if (!_posExpressionZ.empty())
    {
        auto    resZ    =   this->parseExpression(dFactors, _posExpressionZ);
        if (resZ)
        {
            posZ = resZ.value();
        }
    }
    lPos = Vec3(posX, posY, posZ);
    //!TODO: 解析朝向
    
    // 更新到引用节点
    pRefNode->setAttribute("Position", lPos);
    auto angle = DVec3::Angle(lDir, gRootZ);
    DMat4 tMat = DMat4::MakeRotationZ(angle);
    // TODO: 更新朝向
    //pRefNode->setAttribute("Orientation", DMat4::ToQuat(tMat));
    //pRefNode->setLocalRotation(DMat4::ToQuat(tMat));
    pRefNode->update();
    return true;
}

void                WDDeviceTRSParam::copy(const WDObject* pSrcObject)
{
    const WDDeviceTRSParam* pSrc = dynamic_cast<const WDDeviceTRSParam*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDDeviceParam::copy(pSrcObject);

    this->_posExpressionX   =   pSrc->_posExpressionX;
    this->_posExpressionY   =   pSrc->_posExpressionY;
    this->_posExpressionZ   =   pSrc->_posExpressionZ;
    this->_dirExpression    =   pSrc->_dirExpression;
}
WDObject::SharedPtr WDDeviceTRSParam::clone() const
{
    auto p = WDDeviceTRSParam::MakeShared(_core);
    p->copy(this);
    return p;
}
void                WDDeviceTRSParam::toDisplayProperties(WDPropertyGroup& pty)
{
    WDUnused(pty);
#if 0
    // 位置表达式
    pty.addPropertyString(WDTs("WDDeviceParam", "PosExpression"), this->posExpression())
        ->setFunctionOwningObjectValueSet(std::bind(&WDDeviceTRSParam::setPosExpression, this, std::placeholders::_1));
    // 朝向表达式
    pty.addPropertyString(WDTs("WDDeviceParam", "DirExpression"), this->dirExpression())
        ->setFunctionOwningObjectValueSet(std::bind(&WDDeviceTRSParam::setDirExpression, this, std::placeholders::_1));
#endif
}
void                WDDeviceTRSParam::write(JsonDoc& doc, JsonValue& object) const
{
    WDDeviceParam::write(doc, object);
    auto& allocator = doc.GetAllocator();
    object.AddMember("PosExpressionX", JsonValue(_posExpressionX.c_str(), allocator), allocator);
    object.AddMember("PosExpressionY", JsonValue(_posExpressionY.c_str(), allocator), allocator);
    object.AddMember("PosExpressionZ", JsonValue(_posExpressionZ.c_str(), allocator), allocator);
    
    object.AddMember("DirExpression", JsonValue(_dirExpression.c_str(), allocator), allocator);
}
void                WDDeviceTRSParam::read(const JsonValue& object)
{
    WDDeviceParam::read(object);
    if (object.HasMember("PosExpressionX"))
    {
        _posExpressionX = object["PosExpressionX"].GetString();
    }
    if (object.HasMember("PosExpressionY"))
    {
        _posExpressionY = object["PosExpressionY"].GetString();
    }
    if (object.HasMember("PosExpressionZ"))
    {
        _posExpressionZ = object["PosExpressionZ"].GetString();
    }
    if (object.HasMember("DirExpression"))
    {
        _dirExpression = object["DirExpression"].GetString();
    }
}

std::string WDDevice::s_LegendPath = Core().dataDirPath() + std::string("deviceLibrary/icon/");

WDDevice::WDDevice(WD::WDCore& core) : _core(core)
{
    _pEqui      =   WDNode::MakeShared();
}
WDDevice::WDDevice(WD::WDCore& core, WDNode::SharedPtr pEqui) : _core(core)
{
    _pEqui      =   pEqui;
    if (_pEqui == nullptr)
    {
        // !TODO: 提示设备节点参数为空
        return ;
    }
    this->setName(_pEqui->name());
    // !TODO: 递归更新所有节点
    for (size_t i = 0; i < _pEqui->childCount(); ++i)
    {
        auto pChild = _pEqui->childAt(i);
        if (pChild == nullptr)
            continue;
        pChild->update();
    }
}
WDDevice::~WDDevice()
{
    _pEqui = nullptr;
}

// 校验child节点是否为parent节点的子孙节点
bool                        isDescendant(WDNode& parent, WDNode& descendant)
{
    return WDNode::RecursionHelpterR(parent, [](WDNode& descendant, WDNode& node)
        {
            return (&node == &descendant);
        }, descendant);
}
WDDeviceParam::SharedPtr    WDDevice::defineShapeParam(WDNode& descendant
        , size_t index
        , const std::string& name
        , const std::string& expression)
{
    // 校验descendant是否合理
    if (!isDescendant(*_pEqui, descendant))
    {
        // !TODO: 提示该节点不是设备节点的子孙节点
        return nullptr;
    }

    // 校验index是否合理
    if (!WDDeviceShapeParam::CheckNodeIndex(descendant, index))
    {
        // !TODO: 提示index不合理
        return nullptr;
    }
    
    // 校验参数是否重定义
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;
        if (param->name() == name)
        {
            // !TODO: 提示已有同名参数
            return nullptr;
        }
        auto tParam = dynamic_cast<WDDeviceShapeParam*>(param.get());
        if (tParam == nullptr)
            continue;
        if (tParam->pRef().get() == &descendant && tParam->index() == index)
        {
            // !TODO: 提示descendant节点指定index参数已定义
            return nullptr;
        }
    }
    
    _params.push_back(WDDeviceShapeParam::MakeShared(_core, descendant, index, expression));
    _params.back()->setName(name);
    return _params.back();
}
WDDeviceParam::SharedPtr    WDDevice::defineTRSParam(WDNode& descendant
        , const std::string& name
        , const std::string& posExpressionX
        , const std::string& posExpressionY
        , const std::string& posExpressionZ
        , const std::string& dirExpression)
{
    // 校验descendant是否合理
    if (!isDescendant(*_pEqui, descendant))
    {
        // !TODO: 提示该节点不是设备节点的子孙节点
        return nullptr;
    }
    
    // 校验参数是否重定义
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;
        if (param->name() == name)
        {
            // !TODO: 提示已有同名参数
            return nullptr;
        }
        if (param->pRef().get() == &descendant)
        {
            // !TODO: 提示descendant节点指定index参数已定义
            return nullptr;
        }
    }
    
    _params.push_back(WDDeviceTRSParam::MakeShared(_core, descendant, posExpressionX, posExpressionY, posExpressionZ, dirExpression));
    _params.back()->setName(name);
    return _params.back();
}
bool                        WDDevice::undefineParam(WDDeviceParam::SharedPtr param)
{
    auto pItr = std::find(_params.begin(), _params.end(), param);
    if (pItr == _params.end())
        return false;
    _params.erase(pItr);
    return true;
}
size_t                      WDDevice::undefineRefNodeParam(WDNode& node)
{
    size_t              count       =   _params.size();
    WDDeviceParamVector afterParams;
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;
        if (param->pRef().get() == &node)
            continue;
        afterParams.push_back(param);
    }
    _params = afterParams;

    return count - _params.size();
}
WDDeviceParam::SharedPtr    WDDevice::queryParam(WDNode& descendant, size_t index)
{
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;

        if (param->pRef().get() != &descendant)
            continue;

        // 排除非shape参数
        auto pShape = dynamic_cast<WDDeviceShapeParam*>(param.get());
        if (pShape == nullptr)
            continue;

        if (pShape->index() == index)
            return param;
    }

    return nullptr;
}
WDDeviceParam::SharedPtr    WDDevice::queryParam(WDNode& descendant)
{
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;

        if (param->pRef().get() != &descendant)
            continue;

        // 排除非TRS参数
        auto pTRS = dynamic_cast<WDDeviceTRSParam*>(param.get());
        if (pTRS == nullptr)
            continue;

        return param;
    }

    return nullptr;
}

ParamFactor::SharedPtr      WDDevice::addDoubleFactor(const std::string& name, const std::string& description, double value)
{
    // 校验是否有同名参数
    for (auto& factor : _factors)
    {
        if (factor->name() == name)
        {
            // !TODO: 提示已有同名参数
            return nullptr;
        }
    }
    auto pFactor = ParamFactorDouble::MakeShared(); 
    _factors.push_back(pFactor);
    pFactor->setName(name);
    pFactor->setDescription(description);
    pFactor->setValue(value);
    return pFactor;
}
ParamFactor::SharedPtr      WDDevice::addPositionFactor(const std::string& name
    , const std::string& description
    , const Vec3& value)
{
    // 校验是否有同名参数
    for (auto& factor : _factors)
    {
        if (factor->name() == name)
        {
            // !TODO: 提示已有同名参数
            return nullptr;
        }
    }
    auto pFactor = ParamFactorPos::MakeShared(); 
    _factors.push_back(pFactor);
    pFactor->setName(name);
    pFactor->setDescription(description);
    pFactor->setValue(value);
    return pFactor;
}
ParamFactor::SharedPtr      WDDevice::addDirectionFactor(const std::string& name
    , const std::string& description
    , const std::string& value)
{
    // 校验是否有同名参数
    for (auto& factor : _factors)
    {
        if (factor->name() == name)
        {
            // !TODO: 提示已有同名参数
            return nullptr;
        }
    }
    auto pFactor = ParamFactorDir::MakeShared(); 
    _factors.push_back(pFactor);
    pFactor->setName(name);
    pFactor->setDescription(description);
    pFactor->setDirection(value);
    return pFactor;
}
bool                        WDDevice::removeFactor(ParamFactor::SharedPtr pFactor)
{
    auto pItr = std::find(_factors.begin(), _factors.end(), pFactor);
    if (pItr == _factors.end())
        return false;
    _factors.erase(pItr);
    return true;
}

void                        WDDevice::apply()
{
    for (auto& param : _params)
    {
        param->apply(_factors);
    }
}

void                WDDevice::copy(const WDObject* pSrcObject)
{
    const WDDevice* pSrc = dynamic_cast<const WDDevice*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDObject::copy(pSrcObject);
    
    // 通用类型
    this->_gType        =   pSrc->_gType;
    // 描述
    this->_description  =   pSrc->_description;
    // 图例
    this->_legend       =   pSrc->_legend;
    // 设备节点
    if (this->_pEqui != nullptr)
    {
        this->_pEqui = Core().getBMDesign().clone(pSrc->_pEqui);
        if (this->_pEqui != nullptr)
        {
            this->_pEqui->update();
        }
    }
    // 设备参数
    this->_params.clear();
    for (auto& param : pSrc->_params)
    {
        this->_params.push_back(WDDeviceParam::SharedCast(param->clone()));
    }
    MapParam mapParam;
    for (auto& param : this->_params)
    {
        if (param == nullptr)
            continue;

        auto pRefNode = param->pRef();
        if (pRefNode == nullptr)
            continue;

        auto pItr = mapParam.find(pRefNode.get());
        if (pItr == mapParam.end()) 
        {
            mapParam[pRefNode.get()] = {param};
        }
        else
        {
            mapParam[pRefNode.get()].push_back(param);
        }
    }
    this->rectifyParam(pSrc->_pEqui.get(), this->_pEqui.get(), mapParam);
    // 参数因子
    this->_factors.clear();
    for (auto& factor : pSrc->_factors)
    {
        this->_factors.push_back(ParamFactor::SharedCast(factor->clone()));
    }
}
WDObject::SharedPtr WDDevice::clone() const
{
    auto p = WDDevice::MakeShared(_core);
    p->copy(this);
    return p;
}
void                WDDevice::write(JsonDoc& doc, JsonValue& object) const
{
    // 设备节点在序列化插件中进行序列化
    auto& allocator = doc.GetAllocator();
    // 名字
    object.AddMember("Name", JsonValue(name().c_str(), allocator).Move(), allocator);
    // 通用类型
    object.AddMember("GType", JsonValue(_gType.c_str(), allocator).Move(), allocator);
    // 描述
    object.AddMember("Desc", JsonValue(_description.c_str(), allocator).Move(), allocator);
    // 图例
    object.AddMember("Legend", JsonValue(_legend.c_str(), allocator).Move(), allocator);
    // 参数列表
    JsonValue arrayParams(rapidjson::kArrayType);
    for (size_t i = 0; i < _params.size(); ++i)
    {
        JsonValue paramObj(rapidjson::kObjectType);
        _params[i]->write(doc, paramObj);
        arrayParams.PushBack(paramObj, allocator);
    }
    object.AddMember("Params", arrayParams, allocator);
    // 参数因子列表
    JsonValue arrayFactors(rapidjson::kArrayType);
    for (size_t i = 0; i < _factors.size(); ++i)
    {
        JsonValue factorObj(rapidjson::kObjectType);
        _factors[i]->write(doc, factorObj);
        arrayFactors.PushBack(factorObj, allocator);
    }
    object.AddMember("Factors", arrayFactors, allocator);
}
void                WDDevice::read(const JsonValue& object)
{
    // 通用类型
    if (object.HasMember("Name"))
    {
        this->setName(object["Name"].GetString());
    }
    // 通用类型
    if (object.HasMember("GType"))
    {
        _gType = object["GType"].GetString();
    }
    // 描述
    if (object.HasMember("Desc"))
    {
        _description = object["Desc"].GetString();
    }
    // 图例
    if (object.HasMember("Legend"))
    {
        _legend = object["Legend"].GetString();
    }
    // 参数列表
    if (object.HasMember("Params"))
    {
        auto& valueParams = object["Params"];
        assert(valueParams.IsArray());
        for (auto& valueParam : valueParams.GetArray())
        {
            if (!valueParam.HasMember("Type"))
                continue;
            auto type = DeviceParamTypeFromName(valueParam["Type"].GetString());
            switch (type)
            {
            case WD::DP_Shape:
                {
                    _params.push_back(WDDeviceShapeParam::MakeShared(_core));
                    _params.back()->read(valueParam);
                    _params.back()->update(*this);
                }
                break;
            case WD::DP_TRS:
                {
                    _params.push_back(WDDeviceTRSParam::MakeShared(_core));
                    _params.back()->read(valueParam);
                    _params.back()->update(*this);
                }
                break;
            default:
                break;
            }

        }
    }
    // 参数因子列表
    if (object.HasMember("Factors"))
    {
        auto& valueParams = object["Factors"];
        assert(valueParams.IsArray());
        for (auto& valueParam : valueParams.GetArray())
        {
            if (!valueParam.HasMember("Type"))
                continue;
            auto type = ParamFactorTypeFromName(valueParam["Type"].GetString());
            switch (type)
            {
            case WD::PF_Double:
                {
                    _factors.push_back(ParamFactorDouble::MakeShared());
                    _factors.back()->read(valueParam);
                }
                break;
            case WD::PF_Position:
                {
                    _factors.push_back(ParamFactorPos::MakeShared());
                    _factors.back()->read(valueParam);
                }
                break;
            case WD::PF_Direction:
                {
                    _factors.push_back(ParamFactorDir::MakeShared());
                    _factors.back()->read(valueParam);
                }
                break;
            default:
                break;
            }
        }
    }
}
void                WDDevice::rectifyParam(WDNode* parent1, WDNode* parent2, const MapParam& mapParams)
{
    if (parent1 == nullptr || parent2 == nullptr)
        return ;

    auto pItr = mapParams.find(parent1);
    if (pItr != mapParams.end())
    {
        auto& params = pItr->second;
        for (auto& param : params)
        {
            if (param == nullptr)
                continue;

            param->setPRef(WDNode::ToShared(parent2));
        }
        
    }

    for (size_t i = 0; i < parent1->childCount(); ++i)
    {
        auto pChild1 = parent1->childAt(i);
        auto pChild2 = parent2->childAt(i);
        if (pChild1 == nullptr || pChild2 == nullptr)
            continue;

        rectifyParam(pChild1.get(), pChild2.get(), mapParams);
    }
}

WD_NAMESPACE_END