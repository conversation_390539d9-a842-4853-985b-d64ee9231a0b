#include "MarkGroup.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/WDBDBase.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "WDCore.h"
#include <QString>
#include <QLocale>

static std::string DoubleValueToString(const double& value, const int& size = 2)
{
    return QString::number(QString::number(value, 'f', size).toDouble(), 'f', QLocale::FloatingPointShortest).toLocal8Bit().data();
}

/**
* @brief 指定直管段节点
*  遍历直管段所在分支,收集分支下所有位置在当前直管段上的ATTA及其与直管段起始点的距离
*/
static std::map<WD::WDNode::SharedPtr, double> GetAttaInTubiNode(const WD::WDNode& tubiNode)
{
    std::map<WD::WDNode::SharedPtr, double> nodes;
    if (!tubiNode.isType("TUBI"))
        return nodes;

    // 获取管道的起始点和终止点
    WD::DVec3 spos = tubiNode.getAttribute("Hposition WRT World").toDVec3();
    WD::DVec3 epos = tubiNode.getAttribute("Tposition WRT World").toDVec3();
    if (WD::DVec3::Distance(spos, epos) < WD::NumLimits<double>::Epsilon)
        return nodes;

    auto pParent = tubiNode.parent();
    if (pParent == nullptr)
        return nodes;
    if (!pParent->isType("BRAN"))
        return nodes;
    // 遍历管道所在分支,获取所有位置在管道上的ATTA节点
    for (auto& pChild : pParent->children())
    {
        if (!pChild->isType("ATTA"))
            continue;
        auto point = pChild->globalTranslation();
        if (!WD::DSegment3::Contains(point, spos, epos, 0.000001))
            continue;
        nodes.emplace(pChild, WD::DVec3::Distance(point, spos));
    }
    return nodes;
}

WD_NAMESPACE_BEGIN

// 收集指定分支下的两个管件之间的所有直管节点
// 如果前一个管件为nullptr，则认为从分支首开始
// 如果后一个管件为nullptr，则认为到分支尾结束
auto CollectTubis(WDNode& branchNode, WDNode::SharedPtr pPrevCom, WDNode::SharedPtr pNextCom)
{
    std::vector<WDNode::SharedPtr> rTubis;
    if (branchNode.childCount() == 0)
        return rTubis;

    size_t sIndex = 0;
    if (pPrevCom != nullptr)
    {
        for (size_t i = 0; i < branchNode.childCount(); ++i)
        {
            if (pPrevCom == branchNode.childAt(i))
            {
                sIndex = i;
                break;
            }
        }
    }
    size_t eIndex = branchNode.childCount() - 1;
    if (pNextCom != nullptr)
    {
        for (size_t i = 0; i < branchNode.childCount(); ++i)
        {
            if (pNextCom == branchNode.childAt(i))
            {
                eIndex = i;
                break;
            }
        }
    }
    for (size_t i = sIndex; i <= eIndex; ++i)
    {
        auto pNode = branchNode.childAt(i);
        if (pNode == nullptr)
            continue;
        if (pNode->isType("TUBI"))
            rTubis.push_back(pNode);
    }
    return rTubis;
};
// 根据前一个流向，当前流向，后一个流向 计算当前流向的标记文本方向
auto CalcTextOffsetDir(const DVec3& prevDir, const DVec3& dir, const DVec3& nextDir)
{
    auto funcCalcUpNext = [](const DVec3& dir, const DVec3& nextDir)->DVec3
        {
            DVec3 rightDir = DVec3::Cross(nextDir, dir).normalized();
            DVec3 upDir = DVec3::Cross(rightDir, dir).normalized();
            return upDir;
        };
    auto funcCalcUpPrev = [](const DVec3& prevDir, const DVec3& dir)->DVec3
        {
            DVec3 rightDir = DVec3::Cross(dir, prevDir).normalized();
            DVec3 upDir = DVec3::Cross(rightDir, dir).normalized();
            return upDir;
        };

    DVec3 textOffDir = DVec3::AxisZ();

    if (DVec3::OnTheSameLine(dir, DVec3::AxisZ()))
        textOffDir = DVec3::AxisX();
    else
        textOffDir = funcCalcUpNext(dir, DVec3::AxisZ());

    // 前一个方向是否有效
    bool bPrevDirValid = !DVec3::IsZero(prevDir);
    // 后一个方向是否有效
    bool bNextDirValid = !DVec3::IsZero(nextDir);
    // 前一个方向是否与当前方向共线(接近共线)
    bool bPrevDirSameLine = DVec3::OnTheSameLine(dir, prevDir, 0.05);
    // 后一个方向是否与当前方向共线(接近共线)
    bool bNextDirSameLine = DVec3::OnTheSameLine(dir, nextDir, 0.05);
    // 前一个方向是否与当前方向垂直(接近垂直)
    bool bPrevDirPrpend = DVec3::OnPrpendicular(dir, prevDir, 0.05);
    // 后一个方向是否与当前方向垂直(接近垂直)
    bool bNextDirPrpend = DVec3::OnPrpendicular(dir, nextDir, 0.05);

    if (bPrevDirValid && bNextDirValid) // 前后方向均有效,需要继续计算
    {
        if (!bPrevDirSameLine && !bNextDirSameLine) //前后方向均不共线，需要继续计算
        {
            if (bPrevDirPrpend) // 前一个向量垂直
            {
                textOffDir = -prevDir;
                // 同向
                bool bSameDir = DVec3::InTheSameDirection(textOffDir, nextDir, 0.1);
                if (bSameDir)
                {
                    DMat3 rMat = DMat3::MakeRotation(90.0, dir);
                    textOffDir = rMat * textOffDir;
                }
            }
            else if (bNextDirPrpend) // 后一个向量垂直
            {
                textOffDir = -nextDir;
                // 同向
                bool bSameDir = DVec3::InTheSameDirection(textOffDir, prevDir, 0.1);
                if (bSameDir)
                {
                    DMat3 rMat = DMat3::MakeRotation(90.0, dir);
                    textOffDir = rMat * textOffDir;
                }
            }
            else // 均不垂直
            {
                textOffDir = funcCalcUpPrev(prevDir, dir);
            }
        }
        else if (!bPrevDirSameLine) // 前一个方向不共线，使用前一个方向计算
        {
            if (bPrevDirPrpend)
                textOffDir = -prevDir;
            else
                textOffDir = funcCalcUpPrev(prevDir, dir);
        }
        else if (!bNextDirSameLine) // 后一个方向不共线，使用后一个方向计算
        {
            if (bNextDirPrpend)
                textOffDir = -nextDir;
            else
                textOffDir = funcCalcUpNext(dir, nextDir);
        }
        else // 前后方向均与当前方向共线,采用默认计算的方向
        {
        }
    }
    else if (bPrevDirValid && !bPrevDirSameLine) //前一个方向有效且不共线,使用前一个方向计算
    {
        if (bPrevDirPrpend) // 垂直
            textOffDir = -prevDir;
        else //不垂直且不共线
            textOffDir = funcCalcUpPrev(prevDir, dir);
    }
    else if (bNextDirValid && !bNextDirSameLine) //后一个方向有效且不共线,使用后一个方向计算
    {
        if (bNextDirPrpend)
            textOffDir = -nextDir;
        else//不垂直且不共线
            textOffDir = funcCalcUpNext(dir, nextDir);
    }
    else  //前后方向均无效，使用默认计算的方向
    {
    }

    return textOffDir;
}

void BranchMarkGroup::update(WDContext& context)
{
    if (_needUpdate)
    {
        this->reflushMarks(context);
        _needUpdate = false;
    }


    auto pBranchNode = this->node();
    if (pBranchNode == nullptr)
        return;

    auto transform = pBranchNode->globalTransform();

    if (_hPosMark != nullptr)
        _hPosMark->frameUpdate(context, transform);

    if (_tPosMark != nullptr)
        _tPosMark->frameUpdate(context, transform);

    for (auto pMark : _tubiLenMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameUpdate(context, transform);
    }
    for (auto pMark : _comLenMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameUpdate(context, transform);
    }

    for (auto pMark : _elbowDisMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameUpdate(context, transform);
    }
}
void BranchMarkGroup::render(WDContext& context)
{
    if (!_visible)
        return;

    auto pBranchNode = this->node();
    if (pBranchNode == nullptr)
        return;

    auto transform = pBranchNode->globalTransform();

    if (_hPosMark != nullptr)
        _hPosMark->frameRender(context, transform);

    if (_tPosMark != nullptr)
        _tPosMark->frameRender(context, transform);

    for (auto pMark : _comLenMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameRender(context, transform);
    }
    for (auto pMark : _tubiLenMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameRender(context, transform);
    }
    for (auto pMark : _elbowDisMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameRender(context, transform);
    }

    _textRender.render(context);
}

void BranchMarkGroup::reflushMarks(WDContext& context)
{
    // 先清除之前的标记
    _hPosMark = nullptr;
    _tPosMark = nullptr;
    _tubiLenMarks.clear();
    _comLenMarks.clear();
    _elbowDisMarks.clear();

    _textRender.reset();

    // 再根据分支节点，生成新的标记
    auto pBranchNode = this->node();
    if (pBranchNode == nullptr || !pBranchNode->isType("BRAN"))
        return;

    auto transform = pBranchNode->globalTransform();
    // 这里出现的所有位置和朝向信息都是在当前分支坐标系下描述的

    struct TmpData
    {
        // 当前位置
        // 如果是管件，则是管件的P0点位置
        // 如果是分支起点或终点，则分别是分支的起点和终点位置
        DVec3 pos = DVec3::Zero();
        // 当前位置的管道出口流向
        // 如果是管件，则是管件的出口点朝向，如果管件的出口点无效，则取值为DVec3::Zero();
        // 如果是分支起点，则是分支起点的方向
        // 如果是分支终点，则是分支终点方向的负
        DVec3 dir = DVec3::Zero();
        // 当前位置是否有对应的管件
        // 如果是分支起点或终点，则pComNode为nullptr
        WDNode::SharedPtr pComNode = nullptr;
        // 当前位置到前一个位置是否有直管连接(目前实际只有一个直管段，但为了后续考虑，这里放了一个数组)
        std::vector<WDNode::SharedPtr> tubis;
    };
    using TmpDatas = std::vector<TmpData>;
    TmpDatas tDatas;
    // 收集数据
    auto coms = WDBMDPipeUtils::PipeComponents(pBranchNode);
    tDatas.reserve(coms.size() + 2);

    const auto lMat     = pBranchNode->localTransform();
    const auto lRSMat   = pBranchNode->localRSTransform();
    // 分支首
    const auto hPos = lMat.inverse() * pBranchNode->getAttribute("Hposition").toDVec3();
    const auto hDir = (lRSMat.inverse() * pBranchNode->getAttribute("Hdirection").toDVec3()).normalized();
    // 分支尾
    const auto tPos = lMat.inverse() * pBranchNode->getAttribute("Tposition").toDVec3();
    const auto tDir = (lRSMat.inverse() * pBranchNode->getAttribute("Tdirection").toDVec3()).normalized();

    // 先加入分支首数据
    tDatas.push_back({ hPos, hDir, nullptr, {} });

    WDNode::SharedPtr pLastCom = nullptr;
    DVec3 firstTextDir = DVec3::Zero();
    DVec3 lastTextDir = DVec3::Zero();
    if (!coms.empty())
    {
        pLastCom = coms.back();
        // 加入各个管件
        for (size_t i = 0; i < coms.size(); ++i)
        {
            auto pos = coms[i]->getAttribute("Position").toDVec3();
            auto tubis = CollectTubis(*pBranchNode, i == 0 ? nullptr : coms[i - 1], coms[i]);
            tDatas.push_back({ pos, DVec3::Zero(), coms[i], tubis });
            // 获取管件的出口点方向
            auto pLeavePt = coms[i]->keyPoint(coms[i]->getAttribute("Leave").toInt());
            if (pLeavePt != nullptr)
            {
                tDatas.back().dir = pLeavePt->direction;
            }
        }
    }
    // 加入分支尾数据
    tDatas.push_back({ tPos, -tDir, nullptr, CollectTubis(*pBranchNode, pLastCom, nullptr) });
    
    // 从第一个点开始，计算标示对象
    for (size_t i = 1; i < tDatas.size(); ++i)
    {
        // 文本方向
        DVec3 textOffDir = DVec3::AxisZ();
        // 标识管件之间的距离
        {
            // 前一个点
            const auto& prevPos = tDatas[i - 1].pos;
            // 当前点
            const auto& currPos = tDatas[i].pos;
            // 前一个点之前的流向,用于计算文本方向
            auto prevDir = DVec3::Zero();
            if (i > 1)
            {
                const auto& prevPrevPos = tDatas[i - 2].pos;
                DVec3 tmpV = prevPrevPos - prevPos;
                if (!DVec3::IsZero(tmpV))
                    prevDir = tmpV.normalized();
            }
            // 如果距离过近，不会标识长度
            auto vec = currPos - prevPos;
            if (vec.lengthSq() < 1.0)
                continue;

            DVec3 cenPos = (prevPos + currPos) * 0.5;
            DVec3 dir = vec.normalized();

            // 当前点之后的流向,用于计算文本方向
            auto nextDir = DVec3::Zero();
            if (i < tDatas.size() - 1)
            {
                auto nextPos = tDatas[i + 1].pos;
                DVec3 tmpV = nextPos - currPos;
                if (!DVec3::IsZero(tmpV))
                    nextDir = tmpV.normalized();
            }

            // 计算文本朝向
            textOffDir = CalcTextOffsetDir(prevDir, dir, nextDir);
            if (firstTextDir.lengthSq() <= NumLimits<float>::Epsilon)
            {
                firstTextDir = textOffDir;
            }
            if (lastTextDir.lengthSq() <= NumLimits<float>::Epsilon)
            {
                lastTextDir = textOffDir;
            }

            auto pMark = MarkSegment::MakeShared();
            pMark->setLocalT(cenPos);
            pMark->setStartPos(prevPos - cenPos);
            pMark->setEndPos(currPos - cenPos);
            pMark->setTextPositionOffset(textOffDir * 500.0);
            pMark->setText(ToString(static_cast<int>(vec.length())));
            pMark->update();
            // 文本绘制
            this->addRenderText(context, transform, *pMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);
            _comLenMarks.push_back(pMark);

            auto& pCurrNode = tDatas[i].pComNode;
            auto& pPrevNode = tDatas[i - 1].pComNode;
            if (pCurrNode != nullptr && pPrevNode != nullptr)
            {
                auto pCurrRefVal = pCurrNode->getAttribute("Lstube");
                auto pCurrRef = pCurrRefVal.data<WDBMNodeRef>();

                auto pPrevRefVal = pPrevNode->getAttribute("Lstube");
                auto pPrevRef = pPrevRefVal.data<WDBMNodeRef>();

                if (pCurrRef != nullptr && pPrevRef != nullptr)
                {
                    auto pCurrRefNode = pCurrRef->refNode();
                    auto pPrevRefNode = pPrevRef->refNode();
                    // 如果前一个管件与当前管件的等级不同,需要进行标注
                    if (pCurrRefNode != nullptr && pPrevRefNode != nullptr && pCurrRefNode->uuid() != pPrevRefNode->uuid())
                    {
                        auto pArrivePt = pCurrNode->keyPoint(pCurrNode->getAttribute("Arrive").toInt());
                        if (pArrivePt != nullptr)
                        {
                            auto position = pArrivePt->position + textOffDir * 700;

                            auto pMarkLine = MarkSegment::MakeShared();
                            pMarkLine->setLocalT(pCurrNode->getAttribute("Position").toDVec3());
                            pMarkLine->arrowStyle = MarkSegment::None;
                            pMarkLine->setStartPos(pArrivePt->position);
                            pMarkLine->setEndPos(position);
                            pMarkLine->update();

                            auto pMarkPrev = MarkSegment::MakeShared();
                            pMarkPrev->setLocalT(pCurrNode->getAttribute("Position").toDVec3());
                            pMarkPrev->arrowStyle = MarkSegment::End;
                            pMarkPrev->setStartPos(position);
                            auto prevEndPos = position + -dir * 100;
                            pMarkPrev->setEndPos(prevEndPos);
                            pMarkPrev->update();

                            auto pMarkCurr = MarkSegment::MakeShared();
                            pMarkCurr->setLocalT(pCurrNode->getAttribute("Position").toDVec3());
                            pMarkCurr->arrowStyle = MarkSegment::End;
                            pMarkCurr->setStartPos(position);
                            auto currEndPos = position + dir * 100;
                            pMarkCurr->setEndPos(currEndPos);
                            pMarkCurr->update();

                            auto globalPrevPos = pBranchNode->globalTransform() * pMarkCurr->localTransform() * prevEndPos;
                            auto globalCurrPos = pBranchNode->globalTransform() * pMarkCurr->localTransform() * currEndPos;

                            if (DVec3::Angle(dir, DVec3::AxisZ()) < 45.0)
                            {
                                this->addRenderText(context
                                    , globalPrevPos
                                    , pPrevRefNode->name()
                                    , Color::white
                                    , 60
                                    , WDText2DRender::HA_Center
                                    , WDText2DRender::VA_Top);
                                this->addRenderText(context
                                    , globalCurrPos
                                    , pCurrRefNode->name()
                                    , Color::white
                                    , 60
                                    , WDText2DRender::HA_Center
                                    , WDText2DRender::VA_Bottom);
                            }
                            else if (DVec3::Angle(dir, DVec3::AxisNZ()) < 45.0)
                            {
                                this->addRenderText(context
                                    , globalPrevPos
                                    , pPrevRefNode->name()
                                    , Color::white
                                    , 60
                                    , WDText2DRender::HA_Center
                                    , WDText2DRender::VA_Bottom);
                                this->addRenderText(context
                                    , globalCurrPos
                                    , pCurrRefNode->name()
                                    , Color::white
                                    , 60
                                    , WDText2DRender::HA_Center
                                    , WDText2DRender::VA_Top);
                            }
                            else
                            {
                                this->addRenderText(context
                                    , globalPrevPos
                                    , pPrevRefNode->name()
                                    , Color::white
                                    , 60
                                    , WDText2DRender::HA_Right);
                                this->addRenderText(context
                                    , globalCurrPos
                                    , pCurrRefNode->name()
                                    , Color::white
                                    , 60
                                    , WDText2DRender::HA_Left);
                            }
                            _comLenMarks.push_back(pMarkLine);
                            _comLenMarks.push_back(pMarkPrev);
                            _comLenMarks.push_back(pMarkCurr);
                        }
                    }
                }
            }
        }

        // 是否有直管，如果有直管，则标识直管长度
        for (auto pTubi : tDatas[i].tubis)
        {
            if (pTubi == nullptr)
                continue;
            const auto sPos = pTubi->getAttribute("Hposition").toDVec3();
            const auto ePos = pTubi->getAttribute("Tposition").toDVec3();
            DVec3 vec = ePos - sPos;
            if (vec.lengthSq() < 1.0)
                continue;
            DVec3 cenPos = (sPos + ePos) * 0.5;
            DVec3 dir = vec.normalized();

            // 如果直管段不是竖向的则需对直管段首尾进行标高
            if (!DVec3::OnTheSameLine(dir, DVec3::AxisZ()))
            {
                // 获取直管段首尾的世界坐标
                const auto sWPos = pTubi->getAttribute("Hposition WRT World").toDVec3();
                const auto eWPos = pTubi->getAttribute("Tposition WRT World").toDVec3();
                // 管底需要用首尾坐标的z值减去直管段的外径 获取外径
                double bore = pTubi->getAttribute("Aodiam").toDouble();
                if (bore > NumLimits<double>::Epsilon)
                {
                    auto high = bore / 2.0;
                    auto zDir = WD::DVec3::Normalize(eWPos - sWPos);
                    auto angle = DVec3::Angle(DVec3::AxisZ(), zDir);
                    if (angle > 90.0)
                        angle = angle - 90;
                    else
                        angle = 90.0 - angle;
                    high = high * cos(DegToRad(angle));
                    // 直管段首坐标管底高度
                    double sBottom = sWPos.z - high;
                    // 直管段尾坐标管底高度
                    double eBottom = eWPos.z - high;
                    DVec3 sTextPos = sWPos + DVec3::AxisNZ() * 200;
                    DVec3 eTextPos = eWPos + DVec3::AxisNZ() * 300;
                    this->addRenderText(context, sTextPos, DoubleValueToString(sBottom), Color::red);
                    this->addRenderText(context, eTextPos, DoubleValueToString(eBottom), Color::blue);

                    // 收集分支下所有位置在当前直管段上的ATTA及其与直管段起始点的距离
                    auto attas = GetAttaInTubiNode(*pTubi);
                    auto length = WD::DVec3::Distance(sWPos, eWPos);
                    for (auto& eachAtta : attas)
                    {
                        auto& pAtta = eachAtta.first;
                        auto& distance = eachAtta.second;
                        // 计算ATTA所在位置的直管段管底高度
                        if (pAtta == nullptr || distance < WD::NumLimits<double>::Epsilon)
                            continue;
                        // 根据ATTA距离起点的位置算出ATTA位置处直管段的世界坐标
                        auto aWPos = sWPos + (eWPos - sWPos) / length * distance;
                        // 获取ATTA处直管段的管底高度
                        auto aBottom = aWPos.z - high;
                        DVec3 aTextPos = aWPos + DVec3::AxisNZ() * 250;
                        this->addRenderText(context, aTextPos, DoubleValueToString(aBottom), Color::green);
                    }
                }
            }

            auto pMark = MarkSegment::MakeShared();
            pMark->setLocalT(cenPos);
            pMark->setStartPos(sPos - cenPos);
            pMark->setEndPos(ePos - cenPos);
            pMark->setTextPositionOffset(textOffDir * 300.0);
            pMark->setText(ToString(static_cast<int>(vec.length())));
            pMark->update();

            // 文本绘制
            this->addRenderText(context, transform, *pMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);

            _tubiLenMarks.push_back(pMark);
        }
        // 如果当前管件为弯头管件，则标识入口点到p0点的距离
        if (tDatas[i].pComNode != nullptr)
        {
            auto pComNode = tDatas[i].pComNode;
            if (pComNode->isType("ELBO"))
            {
                auto pPt = pComNode->keyPoint(pComNode->getAttribute("Arrive").toInt());
                if (pPt != nullptr)
                {
                    const DVec3  sPos = pPt->transformedPosition(pComNode->localTransform());
                    const DVec3  ePos = pComNode->getAttribute("Position").toDVec3();
                    DVec3 vec = ePos - sPos;
                    if (vec.lengthSq() < 1.0)
                        continue;
                    DVec3 cenPos = (sPos + ePos) * 0.5;
                    DVec3 dir = vec.normalized();

                    auto pMark = MarkSegment::MakeShared();
                    pMark->setLocalT(cenPos);
                    pMark->setStartPos(sPos - cenPos);
                    pMark->setEndPos(ePos - cenPos);
                    pMark->setTextPositionOffset(textOffDir * 300.0);
                    pMark->setText(ToString(static_cast<int>(vec.length())));
                    pMark->update();

                    // 文本绘制
                    this->addRenderText(context, transform, *pMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);

                    _elbowDisMarks.push_back(pMark);
                }
            }
        }
        // 如果前一个管件为弯头管件，则标识出口点到p0点的距离
        if (tDatas[i - 1].pComNode != nullptr)
        {
            auto pComNode = tDatas[i - 1].pComNode;
            if (pComNode->isType("ELBO"))
            {
                auto pPt = pComNode->keyPoint(pComNode->getAttribute("Leave").toInt());
                if (pPt != nullptr)
                {
                    const DVec3  sPos = pComNode->getAttribute("Position").toDVec3();
                    const DVec3  ePos = pPt->transformedPosition(pComNode->localTransform());
                    DVec3 vec = ePos - sPos;
                    if (vec.lengthSq() < 1.0)
                        continue;
                    DVec3 cenPos = (sPos + ePos) * 0.5;
                    DVec3 dir = vec.normalized();

                    auto pMark = MarkSegment::MakeShared();
                    pMark->setLocalT(cenPos);
                    pMark->setStartPos(sPos - cenPos);
                    pMark->setEndPos(ePos - cenPos);
                    pMark->setTextPositionOffset(textOffDir * 300.0);
                    pMark->setText(ToString(static_cast<int>(vec.length())));
                    pMark->update();

                    // 文本绘制
                    this->addRenderText(context, transform, *pMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);

                    _elbowDisMarks.push_back(pMark);
                }
            }
        }
    }
    // 分支首文本标签
    {
        _hPosMark = MarkPoint::MakeShared();
        _hPosMark->setLocalT(hPos);
        if (auto hRefNode = pBranchNode->getAttribute("Href").toNodeRef().refNode(); hRefNode != nullptr)
        {
            std::string connName = hRefNode->name();
            std::string text = ToString(IVec3(hPos)) + std::string("\n") + std::string("CONN.") + connName;
            _hPosMark->setText(text);
        }
        else
        {
            std::string text = ToString(IVec3(hPos));
            _hPosMark->setText(text);
        }
        DVec3 textOffDir = WD::Vec3::Normalize(firstTextDir + (-hDir));
        _hPosMark->setTextPositionOffset(textOffDir * 900.0);
        _hPosMark->update();

        // 文本绘制
        this->addRenderText(context, transform, *_hPosMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);
    }
    // 分支尾文本标签
    {
        _tPosMark = MarkPoint::MakeShared();
        _tPosMark->setLocalT(tPos);

        if (auto tRefNode = pBranchNode->getAttribute("Tref").toNodeRef().refNode(); tRefNode != nullptr)
        {
            std::string connName = tRefNode->name();
            std::string text = ToString(IVec3(tPos)) + std::string("\n") + std::string("CONN.") + connName;
            _tPosMark->setText(text);
        }
        else
        {
            std::string text = ToString(IVec3(tPos));
            _tPosMark->setText(text);
        }
        DVec3 textOffDir = WD::Vec3::Normalize(firstTextDir + (-tDir));
        _tPosMark->setTextPositionOffset(textOffDir * 900.0);
        _tPosMark->update();
        // 文本绘制
        this->addRenderText(context, transform, *_tPosMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);
    }
}
void BranchMarkGroup::addRenderText(WDContext& context
    , const DMat4& transform
    , Mark3D& mark
    , const Color& color
    , const int size
    , WDText2DRender::HAlign hAlign
    , WDText2DRender::VAlign vAlign)
{
    DVec3 pos = transform * mark.localTransform() * mark.textPositionOffset();
    addRenderText(context, pos, mark.text(), color, size, hAlign, vAlign);
}

void BranchMarkGroup::addRenderText(WDContext&
    , const DVec3& position
    , const std::string& text
    , const Color& color
    , const int size
    , WDText2DRender::HAlign hAlign
    , WDText2DRender::VAlign vAlign)
{
    std::wstring wText = stringToWString(text);
    _textRender.addFixedDirection(
        wText                   // 文本
        , FVec3(position)       // 文本位置
        , color                 // 文本颜色
        , size                  // 文本尺寸(世界坐标)
        , hAlign                // 水平对齐方式
        , vAlign                // 垂直对齐方式
        , FVec2(1.0f, 2.0f)     // 水平(spacing.x())以及垂直(spacing.y())间距
        , false
    );
}

void SupportMarkGroup::update(WDContext& context)
{
    if (_needUpdate)
    {
        this->reflushMarks(context);
        _needUpdate = false;
    }


    auto pZoneNode = this->node();
    if (pZoneNode == nullptr)
        return;
    auto transform = pZoneNode->globalTransform();

    for (auto pMark : _comLenMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameUpdate(context, transform);
    }
}
void SupportMarkGroup::render(WDContext& context)
{
    if (!_visible)
        return;

    auto pZoneNode = this->node();
    if (pZoneNode == nullptr)
        return;

    auto transform = pZoneNode->globalTransform();

    for (auto pMark : _comLenMarks)
    {
        if (pMark == nullptr)
            continue;
        pMark->frameRender(context, transform);
    }

    _textRender.render(context);
}

void SupportMarkGroup::reflushMarks(WDContext& context)
{
    // 先清除之前的标记
    _comLenMarks.clear();

    _textRender.reset();

    // 再根据支吊架节点，生成新的标记

    auto pZoneNode = this->node();
    if (pZoneNode == nullptr)
        return;

    auto transform = pZoneNode->globalTransform();

    // 拿到两个 SCTN节点 和 一个  PCLA节点
    std::vector<WDNode::SharedPtr> sctnNodes;
    sctnNodes.reserve(2);
    WDNode::SharedPtr pPCLANode = nullptr;
    if (pZoneNode->childCount() == 2)
    {
        // 两个 SCTN节点
        auto pSTRUNode = pZoneNode->childAt(0);
        if (pSTRUNode != nullptr && pSTRUNode->childCount() == 1)
        {
            auto pFRMWNode = pSTRUNode->childAt(0);
            if (pFRMWNode != nullptr && pFRMWNode->childCount() == 2)
            {
                auto pSCTNNode0 = pFRMWNode->childAt(0);
                auto pSCTNNode1 = pFRMWNode->childAt(1);
                if (pSCTNNode0 != nullptr
                    && pSCTNNode1 != nullptr
                    && pSCTNNode0->isType("SCTN")
                    && pSCTNNode1->isType("SCTN")
                    && pSCTNNode0->name() == "SCTN 1"
                    && pSCTNNode1->name() == "SCTN 2")
                {
                    sctnNodes.push_back(pSCTNNode0);
                    sctnNodes.push_back(pSCTNNode1);
                }
            }
        }
        // 一个 PCLA节点
        auto pPipeNode = pZoneNode->childAt(1);
        if (pPipeNode != nullptr && pPipeNode->childCount() == 1)
        {
            auto pRESTNode = pPipeNode->childAt(0);
            if (pRESTNode != nullptr && pRESTNode->childCount() == 5)
            {
                auto pHANGNode = pRESTNode->childAt(0);
                if (pHANGNode != nullptr && pHANGNode->childCount() == 1)
                {
                    auto pTmpPCLANode = pHANGNode->childAt(0);
                    if (pTmpPCLANode != nullptr
                        && pTmpPCLANode->isType("PCLA")
                        && pTmpPCLANode->name() == "PCLA 1")
                    {
                        pPCLANode = pTmpPCLANode;
                    }
                }
            }
        }
    }
    if (sctnNodes.size() != 2 || pPCLANode == nullptr || sctnNodes[0] == nullptr || sctnNodes[1] == nullptr)
        return;

    if (!pPCLANode->isType("PCLA"))
        return;

    auto pPLines0 = sctnNodes[0]->pLines();
    auto pPLines1 = sctnNodes[1]->pLines();
    if (pPLines0 == nullptr || pPLines1 == nullptr)
        return;
    const WDPLine* pPLine0 = nullptr;
    const WDPLine* pPLine1 = nullptr;
    std::string keyLine0 = sctnNodes[0]->getAttribute("Jusline").toWord();
    if (keyLine0.empty())
        keyLine0 = "NA";
    std::string keyLine1 = sctnNodes[1]->getAttribute("Jusline").toWord();
    if (keyLine1.empty())
        keyLine1 = "NA";
    for (const auto& pLine : (*pPLines0))
    {
        if (pLine.key() == keyLine0)
            pPLine0 = &pLine;
    }
    for (const auto& pLine : (*pPLines1))
    {
        if (pLine.key() == keyLine1)
            pPLine1 = &pLine;
    }
    if (pPLine0 == nullptr || pPLine1 == nullptr)
        return;
    // 转换到世界坐标再转换到相对Zone节点的坐标
    DPLine pLine0 = pPLine0->transformed(sctnNodes[0]->globalTransform());
    pLine0.sPosition += DVec3::AxisZ() * 25.0;
    pLine0.ePosition += DVec3::AxisZ() * 25.0;
    pLine0 = pLine0.transformed(transform.inverse());
    DPLine pLine1 = pPLine1->transformed(sctnNodes[1]->globalTransform());
    pLine1 = pLine1.transformed(transform.inverse());
    // 获取到PCLA的P0点(相对Zone节点)
    DVec3 pt = transform.inverse() * pPCLANode->globalTranslation();

    // PCLA P0点到 SCTNData 对齐线的端点的横向和纵向距离
    DVec3 tmpTextOffset = DVec3::Zero();
    {
        DSegment3 seg(pLine0.sPosition, pLine0.ePosition);
        DVec3 prjPt = seg.closestPointToPoint(pt);
        // 横向
        {
            DVec3 vec = pLine0.sPosition - prjPt;
            DVec3 cenPos = (pLine0.sPosition + prjPt) * 0.5;
            DVec3 textOffset = pt - prjPt;
            auto pMark = MarkSegment::MakeShared();
            pMark->setLocalT(cenPos);
            pMark->setStartPos(prjPt - cenPos);
            pMark->setEndPos(pLine0.sPosition - cenPos);
            pMark->setTextPositionOffset(textOffset * 3.5);
            pMark->setText(ToString(static_cast<int>(vec.length())));
            pMark->update();
            _comLenMarks.push_back(pMark);
            // 文本绘制
            this->addRenderText(context, transform, *pMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);
            tmpTextOffset = vec;
        }
        // 纵向
        {
            DVec3 vec = pt - prjPt;
            DVec3 cenPos = (pt + prjPt) * 0.5;
            DVec3 textOffset = pLine0.sPosition - prjPt;
            auto pMark = MarkSegment::MakeShared();
            pMark->setLocalT(cenPos);
            pMark->setStartPos(pt - cenPos);
            pMark->setEndPos(prjPt - cenPos);
            pMark->setTextPositionOffset(-(textOffset * 1.4));
            pMark->setText(ToString(static_cast<int>(vec.length())));
            pMark->update();
            _comLenMarks.push_back(pMark);
            // 文本绘制
            this->addRenderText(context, transform, *pMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);
        }
    }
    // 纵向工字钢长度
    {
        DVec3 vec = pLine1.sPosition - pLine1.ePosition;
        DVec3 cenPos = (pLine1.sPosition + pLine1.ePosition) * 0.5;
        auto pMark = MarkSegment::MakeShared();
        pMark->setLocalT(cenPos);
        pMark->setStartPos(pLine1.sPosition - cenPos);
        pMark->setEndPos(pLine1.ePosition - cenPos);
        pMark->setTextPositionOffset(tmpTextOffset * 1.2);
        pMark->setText(ToString(static_cast<int>(vec.length())));
        pMark->update();
        _comLenMarks.push_back(pMark);
        // 文本绘制
        this->addRenderText(context, transform, *pMark, Color::white, 60, WDText2DRender::HA_Center, WDText2DRender::VA_Top);
    }
}
void SupportMarkGroup::addRenderText(WDContext& context
    , const DMat4& transform
    , Mark3D& mark
    , const Color& color
    , const int size
    , WDText2DRender::HAlign hAlign
    , WDText2DRender::VAlign vAlign)
{
    DVec3 pos = transform * mark.localTransform() * (mark.textPositionOffset() * 1.05);
    addRenderText(context, pos, mark.text(), color, size, hAlign, vAlign);
}
void SupportMarkGroup::addRenderText(WDContext&
    , const DVec3& position
    , const std::string& text
    , const Color& color
    , const int size
    , WDText2DRender::HAlign hAlign
    , WDText2DRender::VAlign vAlign)
{
    std::wstring wText = stringToWString(text);
    _textRender.addFixedDirection(wText     // 文本
        , FVec3(position)                   // 文本位置
        , color                             // 文本颜色
        , size                              // 文本尺寸(世界坐标)
        , hAlign                            // 水平对齐方式
        , vAlign                            // 垂直对齐方式
        , FVec2::Zero()                     // 水平(spacing.x())以及垂直(spacing.y())间距
        , false
    );
}

WD_NAMESPACE_END