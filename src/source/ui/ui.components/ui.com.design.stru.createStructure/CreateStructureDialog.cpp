#include "CreateStructureDialog.h"
#include "core/WDTranslate.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "businessModule/design/WDBMDesign.h"
#include "core/WDCore.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/undoRedo/WDUndoStack.h"

CreateStructureDialog::CreateStructureDialog(const std::string& title
    , const std::string& type
    , WD::WDCore& core
	, QWidget *parent)
	: QDialog(parent), _core(core), _type(type)
    , _nameHelpter(_core.getBMDesign())
{
	ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
	connect(ui.pushButtonCancel, &QPushButton::clicked, this, &CreateStructureDialog::slotCancelButtonClicked);
	connect(ui.pushButtonOk, &QPushButton::clicked, this, &CreateStructureDialog::slotOkButtonClicked);
	this->setWindowTitle(QString::fromStdString(title));
	//翻译
	this->retranslateUi();
    this->initComboBox();
    //
    _nameHelpter.setLineEdit(ui.lineEditName);
}


CreateStructureDialog::~CreateStructureDialog()
{
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, &CreateStructureDialog::onNodeTreeCurrentNodeChanged};
}

void CreateStructureDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    _core.nodeTree().noticeCurrentNodeChanged() += {this, & CreateStructureDialog::onNodeTreeCurrentNodeChanged};
    _nameHelpter.resetName();
}

void CreateStructureDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, & CreateStructureDialog::onNodeTreeCurrentNodeChanged};
}

void CreateStructureDialog::slotOkButtonClicked()
{
    auto pNewNode = this->createStructuresNode();
    if (pNewNode != nullptr)
    {
        _core.nodeTree().setCurrentNode(pNewNode);
        auto cmdCreatedNode = WD::WDBMBase::MakeCreatedCommand({ pNewNode });
        if (cmdCreatedNode != nullptr)
            _core.undoStack().push(cmdCreatedNode);
    }
    this->accept();
}

void CreateStructureDialog::slotCancelButtonClicked()
{
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, &CreateStructureDialog::onNodeTreeCurrentNodeChanged};
	this->reject();
}

void CreateStructureDialog::onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused(pCurrNode);
    WDUnused(pPrevNode);
    WDUnused(sender);
    _nameHelpter.resetName();
}

void CreateStructureDialog::initComboBox()
{
    if ("STRU" == _type)
	{
        ui.comboBoxPurpose->addItem("UNSET",    "");
        ui.comboBoxPurpose->addItem("LEVEL",    "LEVEL");
        ui.comboBoxPurpose->addItem("HS",       "HS");
        ui.comboBoxPurpose->addItem("STL",      "STL");
        ui.comboBoxPurpose->addItem("GRID",      "GRID");
	}
    else if ("SUBS" == _type)
	{
        ui.comboBoxPurpose->addItem("UNSET",    "");
        ui.comboBoxPurpose->addItem("LEVEL",    "LEVEL");
        ui.comboBoxPurpose->addItem("HS",       "HS");
        ui.comboBoxPurpose->addItem("STL",      "STL");
	}
    else if ("FRMW"  == _type)
	{
        ui.comboBoxPurpose->addItem("UNSET",    "");
        ui.comboBoxPurpose->addItem("STRU",     "STRU");
        ui.comboBoxPurpose->addItem("FLOO",     "FLOO");
        ui.comboBoxPurpose->addItem("HS",       "HS");
        ui.comboBoxPurpose->addItem("LADD",     "LADD");
        ui.comboBoxPurpose->addItem("RAMP",     "RAMP");
        ui.comboBoxPurpose->addItem("STAI",     "STAI");
        ui.comboBoxPurpose->addItem("WALK",     "WALK");
        ui.comboBoxPurpose->addItem("WALL",     "WALL");
	}
    else if ("SBFR" == _type)
	{
        ui.comboBoxPurpose->addItem("UNSET",    "");
        ui.comboBoxPurpose->addItem("LADD",     "LADD");
        ui.comboBoxPurpose->addItem("NODE",     "NODE");
        ui.comboBoxPurpose->addItem("RAIL",     "RAIL");
        ui.comboBoxPurpose->addItem("RAMP",     "RAMP");
        ui.comboBoxPurpose->addItem("STAI",     "STAI");
        ui.comboBoxPurpose->addItem("WALK",     "WALK");
	}
    else if ("CWALL" == _type)
    {
        ui.comboBoxPurpose->addItem("UNSET",    "");
    }
    else if ("CFLOOR" == _type)
    {
        ui.comboBoxPurpose->addItem("UNSET",    "");
    }
    else if ("CSCREED" == _type)
    {
        ui.comboBoxPurpose->addItem("UNSET",    "");
    }
    Trs("CreateStructureDialog", ui.comboBoxPurpose);
}

WD::WDNode::SharedPtr CreateStructureDialog::createStructuresNode()
{
    // 业务组件现在不支持purpose所以这里没有加
    // 要添加Purpose应该先将purpose与翻译文件中的中文比对再设置成对应英文 
    // 获取父节点
	auto pParentNode = this->getParentNode();
    if (pParentNode == nullptr)
    {
        WD_WARN_T("ErrorStruCreateStructure", "Parent Node Is Invalid!");
        return nullptr;
    }
    // 获取新建节点的后1个节点
    auto pCurrent = _core.nodeTree().currentNode();
    auto pNext = GetNextNode(pParentNode, pCurrent);

    //  权限校验
    if (!_core.getBMDesign().permissionMgr().check(*pParentNode))
    {
        WD_WARN_T("ErrorStruCreateStructure", "You cannot operate the current node!");
        return nullptr;
    }
    // 申领对象
    if (!_core.getBMDesign().claimMgr().checkAdd(pParentNode, pNext))
        return nullptr;
    // 校验是否存在相同名称
    if (_nameHelpter.exists())
    {
        WD_WARN_T("ErrorStruCreateStructure", "Same Name Exists!");
        return nullptr;
    }
    auto strName = _nameHelpter.name();
    auto newStructureNode = _core.getBMDesign().create(pParentNode, _type, pNext, strName);
    if (newStructureNode == nullptr)
    {
        WD_WARN_T("ErrorStruCreateStructure", "CreateNodeFailed");
        return nullptr;
    }
    // 设置用途
    std::string purpose = ui.comboBoxPurpose->currentData().toString().toUtf8().data();
    newStructureNode->setAttribute("Purpose", WD::WDBMWord(purpose));
    if (_type == "STRU" && purpose == "GRID")
    {
        newStructureNode->setAttribute("Description", "Grid");
        newStructureNode->setAttribute("Function", "STORE");
    }
    return newStructureNode;
}

WD::WDNode::SharedPtr CreateStructureDialog::getParentNode() const
{
    auto pCurrNode = _core.nodeTree().currentNode();
    if (pCurrNode == nullptr)
        return nullptr;
    // 使用当前选中节点以及挂载规则获取管嘴可挂载的父节点
    return _core.getBMDesign().findParentWithType(*pCurrNode, _type);
}

void CreateStructureDialog::retranslateUi()
{
    Trs("StruCreateStructure"
        , static_cast<QDialog*>(this)
        , ui.pushButtonOk
        , ui.pushButtonCancel
        , ui.labelName
        , ui.labelPurpose
	);
}