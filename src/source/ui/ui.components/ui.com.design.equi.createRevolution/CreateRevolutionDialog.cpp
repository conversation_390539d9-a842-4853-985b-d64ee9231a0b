#include <QEvent>
#include <QFileDialog>
#include <QCloseEvent>
#include "CreateRevolutionDialog.h"
#include "core/viewer/WDViewer.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/material/WDDrawHelpter.h"
#include "../../ui.commonLibrary/ui.commonLib.excel/QTableWidget2Excel.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "businessModule/WDBMColorTable.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "undoRedo/WDUndoStack.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMClaimMgr.h"

static auto TrF(const char* str, const char* cxtStr = "CreateRevolutionDialog")
{
    return WD::WDTs(cxtStr, str);
};

WD_NAMESPACE_BEGIN

RevoVertexRenderObject::RevoVertexRenderObject(CreateRevolutionDialog& d)
    : WDRenderObject(RL_Scene)
    , _d(d)
{
    _pGeomSphere = WDGeometrySphere::MakeShared(1.0f);
    _material.addState(WDRenderStateCullFace::MakeShared(true));

    WDPrimitiveSet pri;
    pri.setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_LineLoop);
    pri.setDrawArrayData(uint(0), uint(1));

    _rotateLineMesh.addPrimitiveSet(WDPrimitiveSet::FromData(uint(0), uint(2)
        , WDPrimitiveSet::PrimitiveType::PT_Lines)
        , WD::WDMesh::WireFrame);

    _verticalLineMesh.addPrimitiveSet(WDPrimitiveSet::FromData(uint(0), uint(2)
        , WDPrimitiveSet::PrimitiveType::PT_Lines)
        , WD::WDMesh::WireFrame);

    _rotVerticalLineMesh.addPrimitiveSet(WDPrimitiveSet::FromData(uint(0), uint(2)
        , WDPrimitiveSet::PrimitiveType::PT_Lines)
        , WD::WDMesh::WireFrame);

    _arcLineMesh.addPrimitiveSet(WDPrimitiveSet::FromData(uint(0), uint(12)
        , WDPrimitiveSet::PrimitiveType::PT_LineStrip)
        , WD::WDMesh::WireFrame);

    _loopLineMesh.addPrimitiveSet(WDPrimitiveSet::FromData(uint(0), uint(2)
        , WDPrimitiveSet::PrimitiveType::PT_LineLoop)
        , WD::WDMesh::WireFrame);

    _materialLines.setColor(Color::lime);
    _materialLines.addState(
        WDRenderStateLineStipple::Get(WDRenderStateLineStipple::Style::DashLine)
    );

    _textRender.setRenderType(WDText2DRender::RT_BillBoard);

    _pStateDepthTestDisabled = WDRenderStateDepthTest::MakeShared(false);

}
RevoVertexRenderObject::~RevoVertexRenderObject() 
{

};

void RevoVertexRenderObject::updateAabb(WDContext& , const WDScene& )
{

}
void RevoVertexRenderObject::update(WD::WDContext& context, const WD::WDScene&)
{
    // 清除数据
    _insts.clear();
    _textRender.reset();

    // 获取相机
    WDCamera& camera = context.camera();
    wchar_t textBuf[1024] = { 0 };

    // 旋转轴
    if (_d._rotationLine)
    {
        const auto& seg = _d._rotationLine.value();
        // 旋转轴线
        if (_rotateLineMesh.positions().size() != 2)
            _rotateLineMesh.positions().resize(2);

        _rotateLineMesh.positions()[0] = FVec3(seg.start);
        _rotateLineMesh.positions()[1] = FVec3(seg.end);

        for (size_t i = 0; i < seg.Size; ++i)
        {
            const auto& pt = seg[i];
            FVec3 fPt = FVec3(pt);
            // 计算相机缩放
            float pixelU = static_cast<float>(camera.pixelU(pt));
            // 旋转轴端点
            _insts.push_back({});
            _insts.back()._color = Color::red;
            _insts.back()._local = FMat4::Compose(fPt, FVec3(pixelU * 10.0f));
            // 旋转轴端点文本
            swprintf(textBuf, sizeof(textBuf) / sizeof(textBuf[0]), L"A%d", static_cast<int>(i + 1));
            _textRender.add(textBuf, FVec3(pt), Color::yellow, 12);
        }
    }
    // 绘制旋转平面起点到旋转中心的垂线
    if (_d._rotationLine && _d._start && _d._center)
    {
        // 旋转轴向
        DVec3 rAxis = _d._rotationLine.value().direction();
        FVec3 rAxisF = FVec3(rAxis);
        // 中心点(旋转体原点)
        const DVec3& center = _d._center.value();
        FVec3 centerF = FVec3(center);
        // 旋转平面起始点
        const DVec3& start = _d._start.value();
        FVec3 startF = FVec3(start);
        // 中心点到平面起点的连线
        {
            if (_verticalLineMesh.positions().size() != 2)
                _verticalLineMesh.positions().resize(2);
            _verticalLineMesh.positions()[0] = centerF;
            _verticalLineMesh.positions()[1] = startF;
        }
        // 中心点文本以及旋转平面起点
        {
            _textRender.add(stringToWString(TrF("Origin")), centerF, Color::yellow, 12);
            _textRender.add(stringToWString(TrF("Start")), startF, Color::yellow, 12);
        }
        // 计算垂线方向(旋转体Y轴方向)
        FVec3 vertical = startF - centerF;
        FVec3 verticalNormal = vertical.normalized();
        // 求出垂线长度
        float verticalLength = vertical.length();
        // 旋转角度
        float angle = static_cast<float>(_d.ui.doubleSpinBoxAngle->value());

        // 通过旋转轴，旋转角度旋转后的垂线绘制数据
        {
            // 根据角度，求出旋转辅助轴
            FMat3 rMat = FMat3::MakeRotation(angle, rAxisF);
            // 旋转后的辅助轴线方向
            FVec3 retAxis = rMat * verticalNormal;
            // 求出绘制的端点
            FVec3 retVertex = centerF + (verticalLength * retAxis);

            // 根据角度 旋转后的垂线
            if (_rotVerticalLineMesh.positions().size() != 2)
                _rotVerticalLineMesh.positions().resize(2);
            _rotVerticalLineMesh.positions()[0] = centerF;
            _rotVerticalLineMesh.positions()[1] = retVertex;

            // 旋转结束点
            _textRender.add(stringToWString(TrF("End")), retVertex, Color::yellow, 12);
        }
        // 旋转圆弧以及旋转角度
        {
            if (_arcLineMesh.positions().size() != 12)
                _arcLineMesh.positions().resize(12);
            // 取垂线长度的1/5作为弧半径
            float radius = verticalLength * 0.2f;
            // 求出圆弧顶点起点
            FVec3 arcV0 = radius * verticalNormal;
            // 暂时默认11段, 圆弧生成12个顶点
            size_t step = 11;
            for (size_t i = 0; i <= step; i++)
            {
                // 旋转角度
                float rAngle = angle * static_cast<float>(i) / static_cast<float>(step);
                // 旋转矩阵
                FMat4 offMatR = FMat4::MakeRotation(rAngle, rAxisF);
                // 偏移结果
                FVec3 arcV = centerF + offMatR * arcV0;
                // 设置环顶点
                _arcLineMesh.positions()[i] = arcV;
                // 绘制旋转角度文本
                if (i == step / 2)
                {
                    // 弧度值保留两位小数
                    swprintf(textBuf, sizeof(textBuf) / sizeof(wchar_t), L"%.2lf°", angle);
                    _textRender.add(textBuf, arcV, Color::yellow, 12);
                }
            }
        }
    }

    // 旋转面顶点连接线
    const auto& verts = _d._loopVerticesEditHelpter.points();
    if (!verts.empty())
    {
        FVec3Vector fPts;
        fPts.reserve(verts.size());
        for (const auto& vert : verts)
        {
            fPts.emplace_back(FVec3(vert.xyz()));
        }
        // 环顶点和顶点标签标签
        int vertIndex = 0;
        for (const auto& pt : fPts)
        {
            // 计算相机缩放
            double pixelU = camera.pixelU(DVec3(pt));
            // 设置实例
            _insts.push_back({});
            _insts.back()._color = Color::red;
            _insts.back()._local = FMat4::Compose(pt, FVec3(pixelU * 10.0f));
            // 设置绘制字体内容
            swprintf(textBuf, sizeof(textBuf) / sizeof(wchar_t), L"V%d", vertIndex + 1);
            _textRender.add(textBuf, pt, Color::yellow, 12);
            vertIndex++;
        }
        // 环边线
        _loopLineMesh.setPositions(fPts);
        auto& priSets = _loopLineMesh.primitiveSets(WDMesh::WireFrame);
        if (priSets.size() > 0)
        {
            priSets[0].setDrawArrayData(uint(0), uint(fPts.size()));
            if (fPts.size() >= 3)
                priSets[0].setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_LineLoop);
            else
                priSets[0].setPrimitiveType(WDPrimitiveSet::PrimitiveType::PT_Lines);
        }
    }
}
void RevoVertexRenderObject::render(WD::WDContext& context, const WD::WDScene&)
{
    context._rStateStack.push(_pStateDepthTestDisabled);
    {
        WDInstances insts;
        insts.push_back({});
        insts.back()._color = Color::lime;
        insts.back()._local = FMat4::Identity();

        WDDrawHelpter::Guard dg(context, _materialLines);

        if (_d._rotationLine)
            dg.drawInstance(insts, _rotateLineMesh, WDMesh::WireFrame);

        if (_d._rotationLine && _d._start && _d._center)
        {
            dg.drawInstance(insts, _verticalLineMesh, WDMesh::WireFrame);
            dg.drawInstance(insts, _rotVerticalLineMesh, WDMesh::WireFrame);
            dg.drawInstance(insts, _arcLineMesh, WDMesh::WireFrame);
        }
        if (!_d._loopVerticesEditHelpter.points().empty())
            dg.drawInstance(insts, _loopLineMesh, WDMesh::WireFrame);
    }

    if (!_insts.empty())
    {
        WDDrawHelpter::Guard dg(context, _material);
        dg.drawInstance(_insts, *(_pGeomSphere->mesh()), WDMesh::Solid);
    }

    context._rStateStack.pop();

    _textRender.render(context, false);
}

WD_NAMESPACE_END

CreateRevolutionDialog::CreateRevolutionDialog(WD::WDCore& app, QWidget *parent)
    : QDialog(parent)
    , _app(app)
    , _positionCaptureHelpter(_app)
    , _loopVerticesEditHelpter(_app)
    , _renderObject(*this)
    , _nameHelpter(_app.getBMDesign())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 创建方式
    ui.comboBoxMethod->addItem("Default", Method::M_Default);
    ui.comboBoxMethod->addItem("Capture", Method::M_Capture);
    ui.comboBoxMethod->addItem("Define Rectangle", Method::M_DefineRect);
    ui.comboBoxMethod->addItem("Input Coordinate", Method::M_InputCoord);
    ui.comboBoxMethod->addItem("Polar Coordinate", Method::M_PolarCoord);

    // 空间占有级别
    ui.comboBoxObstruction->addItem(WD::ObstructionLevelTypeToString(WD::WDBMObstructionLevelType::BMOL_None)
        , QVariant(WD::WDBMObstructionLevelType::BMOL_None));
    ui.comboBoxObstruction->addItem(WD::ObstructionLevelTypeToString(WD::WDBMObstructionLevelType::BMOL_Soft)
        , QVariant(WD::WDBMObstructionLevelType::BMOL_Soft));
    ui.comboBoxObstruction->addItem(WD::ObstructionLevelTypeToString(WD::WDBMObstructionLevelType::BMOL_Hard)
        , QVariant(WD::WDBMObstructionLevelType::BMOL_Hard));

    // 默认为硬占有
    ui.comboBoxObstruction->setCurrentIndex(2);
    // 设置表头
    ui.tableWidgetVertices->setColumnCount(4);
    ui.tableWidgetVertices->setHorizontalHeaderLabels({ "X", "Y", "Z", "Fillet Radius" });
    ui.tableWidgetVertices->setSelectionBehavior(QTableWidget::SelectRows);

    // 顶点显示界面帮助
    _loopVerticesEditHelpter.setTableWidget(ui.tableWidgetVertices);

    // 位置捕捉帮助
    _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CaptureTimes::CT_Repeat);
    auto param              = _positionCaptureHelpter.captureParam();
    param.bShowResultCoord  = true;
    _positionCaptureHelpter.setCaptureParam(param);
    // 位置输入界面
    _pPositionInputDialog = new UiPositionInputDialog(_app, this);
    _pPositionInputDialog->setCaptureParam(param);
    _pPositionInputDialog->setWindowTitle("Define Vertex");
    _pPositionInputDialog->setGroupBoxPositionTitle("Position");
    _pPositionInputDialog->setCheckBoxCaptureText("Capture");
    _pPositionInputDialog->setLabelWRTText("WRT");
    _pPositionInputDialog->setPushButtonOkText("Ok");
    _pPositionInputDialog->setPushButtonCancelText("Cancel");
    // 使用极坐标输入位置界面
    _pPolarCoordInputDialog = new UiPolarCoordInputDialog(_app, this);
    _pPolarCoordInputDialog->setWindowTitle("Define Vertex");
    _pPolarCoordInputDialog->setLabelDirectionText("Direction");
    _pPolarCoordInputDialog->setLabelDistanceText("Distance");
    _pPolarCoordInputDialog->setLabelWRTText("WRT");
    _pPolarCoordInputDialog->setPushButtonOkText("Ok");
    _pPolarCoordInputDialog->setPushButtonCancelText("Cancel");
    // 定义矩形界面
    _pDefineRectDialog = new UiDefineRectangleDialog(this);
    _pDefineRectDialog->setWindowTitle("Define Rectangle");
    _pDefineRectDialog->setLableWidthText("Width");
    _pDefineRectDialog->setLableHeightText("Height");
    _pDefineRectDialog->setPushButtonOkText("Ok");
    _pDefineRectDialog->setPushButtonCancelText("Cancel");

    // 界面翻译
    this->retranslateUi();

    connect(ui.pushButtonRotationLine
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonRotationLineClicked);

    connect(ui.pushButtonFlip
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonFlipClicked);

    connect(ui.pushButtonPointOnPlane
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonPointOnPlaneClicked);

    connect(ui.doubleSpinBoxAngle
        , QOverload<double>::of(&QDoubleSpinBox::valueChanged)
        , [this](double) 
        {
            // 触发重绘
            _app.needRepaint();
        });

    connect(ui.checkBoxNegative
        , &QCheckBox::stateChanged
        , this
        , &CreateRevolutionDialog::slotCheckBoxNegativeStateChanged);

    connect(ui.comboBoxMethod
        , QOverload<int>::of(&QComboBox::activated)
        , this
        , &CreateRevolutionDialog::slotComboBoxMethodActived);

    connect(ui.pushButtonAdd
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonAddClicked);
    connect(ui.pushButtonImport
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonImportClicked);
    connect(ui.pushButtonExport
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonExportClicked);
    connect(ui.pushButtonDelete
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonDeleteClicked);
    connect(ui.pushButtonClear
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonClearClicked);

    connect(ui.pushButtonOK
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::slotPushButtonOKClicked);
    connect(ui.pushButtonCancel
        , &QPushButton::clicked
        , this
        , &CreateRevolutionDialog::reject);

    connect(&_loopVerticesEditHelpter
        , &UiLoopVerticesEditHelpter::sigVerticesChanged
        , this
        , [this]()
        {
            const auto& pts = _loopVerticesEditHelpter.points();
            // 更新极坐标原点
            if (pts.empty())
                _pPolarCoordInputDialog->setOrigin(WD::DVec3::Zero());
            else
                _pPolarCoordInputDialog->setOrigin(pts.back().xyz());

            // 触发重绘
            _app.needRepaint();
        });

    connect(_pPositionInputDialog
        , &UiPositionInputDialog::sigApply
        , this
        , [=]()
        {
            if (!this->_rotationLine || !this->_start || !this->_center)
            {
                assert(false);
                return;
            }
            // 添加界面输入的点
            _loopVerticesEditHelpter.addPoint(WD::DVec4(_pPositionInputDialog->position(), 0.0));
        });

    connect(_pPolarCoordInputDialog
        , &UiPolarCoordInputDialog::sigApply
        , this
        , [=]()
        {
            if (!this->_rotationLine || !this->_start || !this->_center)
            {
                assert(false);
                return;
            }
            auto rPos = _pPolarCoordInputDialog->position();
            if (!rPos)
            {
                WD_WARN(TrF("Invalid direction"));
                return;
            }
            // 添加界面输入的点
            _loopVerticesEditHelpter.addPoint(WD::DVec4(rPos.value(), 0.0));
        });

    connect(_pDefineRectDialog
        , &UiDefineRectangleDialog::sigApply
        , this
        , [=]()
        {
            if (!this->_rotationLine || !this->_start || !this->_center)
            {
                WD_ERROR(TrF("Cannot compute plane of profile"));
                return ;
            }
            // 以定义的顶点起点为矩形中心点
            const WD::DVec3& sVertex    = this->_start.value();
            // 定义的X和Y方向的长度是在旋转体坐标系下的
            // 获取X轴方向
            WD::DVec3 xAxis             = this->_rotationLine.value().direction();
            // 获取Y轴方向
            WD::DVec3 yAxis             = WD::DVec3::Normalize(this->_start.value() - this->_center.value());
            // 矩形的宽高
            const WD::DVec2& sz         = _pDefineRectDialog->rectSize();
            if (sz.x <= WD::NumLimits<float>::Epsilon)
            {
                TrF("Invalid rectangle width");
                return;
            }
            if (sz.y <= WD::NumLimits<float>::Epsilon)
            {
                TrF("Invalid rectangle height");
                return;
            }
            // 计算矩形的四个顶点
            WD::DVec4Vector pts;
            pts.reserve(4);
            pts.push_back(WD::DVec4(sVertex + (sz.x *  0.5 * xAxis + sz.y * 0.5  * yAxis), 0.0));
            pts.push_back(WD::DVec4(sVertex + (sz.x * -0.5 * xAxis + sz.y * 0.5  * yAxis), 0.0));
            pts.push_back(WD::DVec4(sVertex + (sz.x * -0.5 * xAxis + sz.y * -0.5 * yAxis), 0.0));
            pts.push_back(WD::DVec4(sVertex + (sz.x *  0.5 * xAxis + sz.y * -0.5 * yAxis), 0.0));
            // 设置环顶点
            _loopVerticesEditHelpter.setPoints(pts);
        });

    _nameHelpter.setLineEdit(ui.lineEditName);
}
CreateRevolutionDialog::~CreateRevolutionDialog()
{
    _app.nodeTree().noticeCurrentNodeChanged() -= {this, & CreateRevolutionDialog::onNodeTreeCurrentNodeChanged};
}

void CreateRevolutionDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);

    // 更新界面显示，目的是更新新生成的节点名称
    _nameHelpter.resetName();
    // 树监听
    _app.nodeTree().noticeCurrentNodeChanged() += {this, & CreateRevolutionDialog::onNodeTreeCurrentNodeChanged};
    // 添加绘制对象
    this->activeRenderObject(true);
    // 更新旋转体坐标轴显隐
    this->updateAxisVisible();


    ui.pushButtonFlip->setEnabled(false);
    ui.pushButtonPointOnPlane->setEnabled(false);
    ui.groupBoxVertices->setEnabled(false);

    if (_rotationLine)
    {
        ui.pushButtonFlip->setEnabled(true);
        ui.pushButtonPointOnPlane->setEnabled(true);
        if (_center && _start)
        {
            ui.groupBoxVertices->setEnabled(true);
        }
    }

    // 触发重绘
    _app.needRepaint();
}
void CreateRevolutionDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);

    // 重置所有数据
    {
        // 重置旋转轴线
        _rotationLine = std::nullopt;
        // 重置保存旋转平面起始(中心)点
        _start = std::nullopt;
        // 重置保存的旋转体中心点
        _center = std::nullopt;
        // 清除顶点
        _loopVerticesEditHelpter.clear();
    }
    // 方式选为默认
    ui.comboBoxMethod->setCurrentIndex(0);
    this->slotComboBoxMethodActived(0);
    // 移除绘制对象
    this->activeRenderObject(false);
    // 取消树监听
    _app.nodeTree().noticeCurrentNodeChanged() -= {this, & CreateRevolutionDialog::onNodeTreeCurrentNodeChanged};
    // 退出位置捕捉
    _positionCaptureHelpter.exit(true);
    // 退出轴线或平面上的点的捕捉
    setCaptureWithMode(CaptureMode::CM_None);
    // 更新旋转体坐标轴显隐
    this->updateAxisVisible();

    // 触发重绘
    _app.needRepaint();
}

void CreateRevolutionDialog::slotPushButtonRotationLineClicked()
{
    this->setCaptureWithMode(CaptureMode::CM_AxisLine);
}
void CreateRevolutionDialog::slotPushButtonFlipClicked()
{
    if (!_rotationLine)
        return;
    _rotationLine = WD::DSegment3(_rotationLine.value().end, _rotationLine.value().start);

    // 触发重绘
    _app.needRepaint();
}
void CreateRevolutionDialog::slotPushButtonPointOnPlaneClicked()
{
    this->setCaptureWithMode(CaptureMode::CM_PlanePoint);
}

void CreateRevolutionDialog::slotCheckBoxNegativeStateChanged(int)
{
    _nameHelpter.resetName();
}
void CreateRevolutionDialog::slotComboBoxMethodActived(int index)
{
    _positionCaptureHelpter.exit(false);
    _pPositionInputDialog->hide();
    _pPolarCoordInputDialog->hide();

    Method mType = static_cast<Method>(ui.comboBoxMethod->itemData(index).toInt());
    switch (mType)
    {
    case CreateRevolutionDialog::M_Default:
    {
        // 啥都不干!!
    }
    break;
    case CreateRevolutionDialog::M_Capture:
    {
        // 开启捕捉
        _positionCaptureHelpter.activeCapture();
    }
    break;
    case CreateRevolutionDialog::M_InputCoord:
    {
        // 显示坐标输入窗口
        if (ui.tableWidgetVertices->rowCount() > 0)
        {
            auto currentRow = ui.tableWidgetVertices->currentRow();
            if (currentRow == -1)
                currentRow = ui.tableWidgetVertices->rowCount() - 1;
            _pPositionInputDialog->setPosition(_loopVerticesEditHelpter.getPoint(currentRow).xyz());
        }
        _pPositionInputDialog->show();
    }
    break;
    case CreateRevolutionDialog::M_PolarCoord:
    {
        // 显示输入极坐标窗口
        _pPolarCoordInputDialog->show();
    }
    break;
    case CreateRevolutionDialog::M_DefineRect:
    {
        // 显示定义矩形窗口
        _pDefineRectDialog->show();
    }
    break;
    default:
        break;
    }
}

void CreateRevolutionDialog::slotPushButtonAddClicked()
{
    WD::DVec4 newPoint = WD::DVec4::Zero();
    if (ui.tableWidgetVertices->rowCount() > 0)
    {
        auto currentRow = ui.tableWidgetVertices->currentRow();
        if (currentRow == -1)
            currentRow = ui.tableWidgetVertices->rowCount() - 1;
        newPoint = _loopVerticesEditHelpter.getPoint(currentRow);
    }
    _loopVerticesEditHelpter.addPoint(newPoint);
}
void CreateRevolutionDialog::slotPushButtonImportClicked()
{
    // 文件对话框
    QString filePath = QFileDialog::getOpenFileName(this
        , QString::fromUtf8(WD::WDTs("CreateRevolutionDialog", "load").data())
        , ""
        , "file(*.xlsx)");
    if (filePath.isEmpty())
        return;

    QTableWidget2Excel::ExcelData   excelData;
    bool flag = false;
    // 新建Excel导入导出对象
    {
        QTableWidget2Excel excel;
        flag = excel.getExcelData(filePath, excelData);
    }
    auto colCount = ui.tableWidgetVertices->columnCount();
    // 导入导出提示
    if (!flag || excelData.size() <= 1 || excelData.front().size() < colCount)
    {
        WD_WARN_T("ErrorCreateRevolutionDialog", "ImportFail!");
        return;
    }
    // 第一个是表头数据
    // 先校验表头数据是否正常
    auto& head = excelData[0];
    for (int i = 0; i < colCount; i++)
    {
        auto    topItem =   ui.tableWidgetVertices->horizontalHeaderItem(i);
        QString text    =   topItem->text();
        if (head[i] != text.toUtf8().data())
        {
            WD_WARN_T("ErrorCreateRevolutionDialog", "Error Meter Header Info!");
            return;
        }
    }
    // 应用导入数据，跳过表头
    for (size_t i = 1; i < excelData.size(); i++)
    {
        auto& vertexData = excelData[i];
        if (vertexData.size() < colCount)
        {
            WD_WARN_T("ErrorCreateRevolutionDialog", "Error Data!");
            return;
        }
        bool bOk = false;
        // 导入的顶点数据是基于当前的WRT节点的
        const double px = WD::FromString<double>(vertexData[0], &bOk);
        if (bOk)
        {
            const double py = WD::FromString<double>(vertexData[1], &bOk);
            if (bOk)
            {
                const double pz = WD::FromString<double>(vertexData[2], &bOk);
                if (bOk)
                {
                    // 圆角半径
                    const double radius = WD::FromString<double>(vertexData[3], &bOk);
                    if (bOk)
                    {
                        _loopVerticesEditHelpter.addPoint(WD::DVec4(px, py, pz, radius));
                        continue;
                    }
                }
            }
        }
        WD_WARN_T("ErrorCreateRevolutionDialog", "Error Data!");
        return;
    }

    WD_INFO_T("ErrorCreateRevolutionDialog", "ImportSucceed!");
}
void CreateRevolutionDialog::slotPushButtonExportClicked()
{
    if (ui.tableWidgetVertices->rowCount() == 0)
    {
        WD_WARN_T("ErrorCreateRevolutionDialog", "Vertices Is Empty!");
        return;
    }
    // 文件对话框
    QString filePath = QFileDialog::getSaveFileName(this
        , QString::fromUtf8(WD::WDTs("CreateRevolutionDialog", "save").data())
        , ""
        , "file(*.xlsx)");
    if (filePath.isEmpty())
        return;

    QString suffix(".xlsx");
    if (!filePath.endsWith(suffix, Qt::CaseInsensitive))
        filePath += suffix;

    // 新建Excel导入导出对象
    bool flag = false;
    {
        QTableWidget2Excel excel;
        flag = excel.exportTableWidgetToExcel(filePath, ui.tableWidgetVertices);
    }
    if(flag)
        WD_INFO_T("ErrorCreateRevolutionDialog", "ExportSucceed!");
    else
        WD_WARN_T("ErrorCreateRevolutionDialog", "ExportFail!");
}
void CreateRevolutionDialog::slotPushButtonDeleteClicked()
{
    int curRow = ui.tableWidgetVertices->currentRow();
    if (curRow < 0)
        return;
    _loopVerticesEditHelpter.removePoint(curRow);
}
void CreateRevolutionDialog::slotPushButtonClearClicked()
{
    _loopVerticesEditHelpter.clear();
}

void CreateRevolutionDialog::slotPushButtonOKClicked()
{
    auto gTRMatOpt = this->globalTRMat();
    if (!gTRMatOpt)
    {
        WD_ERROR(TrF("Cannot compute plane of profile"));
        return ;
    }
    WD::DMat4 gTRMat = gTRMatOpt.value();
    // 获取父节点
    auto pParentNode = this->getParentNode();
    if (pParentNode == nullptr)
    {
        WD_ERROR(TrF("Invalid parent"));
        return;
    }
    //  权限校验
    if (!_app.getBMDesign().permissionMgr().check(*pParentNode))
    {
        WD_WARN_T("ErrorCreateRevolutionDialog", "You cannot operate the current node!");
        return;
    }
    // 申领对象
    if (!_app.getBMDesign().claimMgr().checkAdd(pParentNode))
        return;

    // 校验父节点是否存在相同名称
    if (_nameHelpter.exists())
    {
        WD_ERROR(TrF("Same name exists"));
        return;
    }
    // 获取名称 
    std::string name = _nameHelpter.name();
    // 获取顶点
    const auto& verts = _loopVerticesEditHelpter.points();
    if (verts.size() < 3)
    {
        WD_WARN(TrF("The number of vertices cannot be less tan 3"));
        return;
    }
    // 获取旋转角度
    double angle = ui.doubleSpinBoxAngle->value();
    if (angle <= WD::NumLimits<double>::Epsilon)
    {
        WD_WARN(TrF("The angle must be greater tan 0"));
        return;
    }
    // 校验前三个顶点是否是有效三角形
    WD::DTriangle3 triangle(verts[0].xyz(), verts[1].xyz(), verts[2].xyz());
    if (!triangle.isVaild())
    {
        WD_WARN(TrF("The first three vertices do not from a triangle"));
        return;
    }
    // 是否负实体
    bool isNegative = ui.checkBoxNegative->isChecked();

    // 创建REVO节点
    WD::WDBMDesign& mgr = _app.getBMDesign();
    WD::WDNode::SharedPtr pRevoNode = mgr.create(pParentNode
        , isNegative ? "NREV" : "REVO"
        , name);
    if (pRevoNode == nullptr)
    {
        assert(false);
        return;
    }

    // 设置旋转角度
    pRevoNode->setAttribute("Angle", angle);
    // 空间占有属性
    pRevoNode->setAttribute("Obstruction", ui.comboBoxObstruction->currentData().toInt());

    pRevoNode->setAttribute("Position WRT World", gTRMat[3].xyz());
    pRevoNode->setAttribute("Orientation WRT World", WD::DMat4::ToQuat(gTRMat));
    // 创建LOOP节点
    WD::WDNode::SharedPtr pLoopNode    = mgr.create(pRevoNode, "LOOP");
    if (pLoopNode == nullptr)
    {
        assert(false);
        return;
    }

    // 创建VERT节点
    for (const auto& vert : verts)
    {
        // 创建VERT节点
        WD::WDNode::SharedPtr pVertNode = mgr.create(pLoopNode, "VERT");
        if (pVertNode == nullptr)
        {
            assert(false);
            return;
        }
        // 将世界坐标转换到拉伸体节点下的相对坐标
        WD::DVec3 lPos = gTRMat.inverse() * vert.xyz();
        // 设置顶点圆角半径
        pVertNode->setAttribute("Fradius", vert.w);
        // 设置位置
        pVertNode->setAttribute("Position", lPos);
    }

    // 设置节点的默认颜色
    _app.getBMDesign().colorTable().setNodeColor(*pRevoNode);
    //更新节点
    pRevoNode->triggerUpdate();
    _app.nodeTree().setCurrentNode(pRevoNode);

    auto cmdAddToScene = WD::WDBMBase::MakeSceneAddCommand({pRevoNode});
    auto cmdCreateNode = WD::WDBMBase::MakeCreatedCommand({pRevoNode});
    if (cmdAddToScene != nullptr && cmdCreateNode != nullptr)
    {
        _app.undoStack().beginMarco("Create revolution");
        _app.undoStack().push(cmdCreateNode);
        _app.undoStack().push(cmdAddToScene);
        _app.undoStack().endMarco();
    }

    // 添加节点到场景中
    _app.scene().add(pRevoNode);
    // 触发重绘
    _app.needRepaint();

    _nameHelpter.resetName();
}

std::optional<WD::DMat4> CreateRevolutionDialog::globalTRMat() const
{
    if (!_rotationLine || !_start || !_center)
        return std::nullopt;

    // 旋转轴线为旋转体的X轴正方向
    WD::DVec3 axisX     = _rotationLine.value().direction();
    // 旋转中心到顶点起点方向为旋转体的Y轴正方向
    WD::DVec3 axisY     = WD::DVec3::Normalize(_start.value() - _center.value());
    // 计算旋转
    WD::DMat3 rotation  = WD::DMat3::MakeRotationXY(axisX, axisY);
    // TR矩阵
    return WD::DMat4(rotation, _center.value());
}
void CreateRevolutionDialog::setCaptureWithMode(CaptureMode mode)
{
    if (_captureMode == mode)
        return;

    _captureMode = mode;
    // 激活捕捉工具
    auto& capture = _app.viewer().capturePositioning();

    switch (_captureMode)
    {
    case CreateRevolutionDialog::CM_None:
        {
            capture.deativate();
        }
        break;
    case CreateRevolutionDialog::CM_AxisLine:
        {
            WD::WDCapturePositioningParam param = capture.param();
            param.pMonitor                  = this;
            param.bDisplayCaptureTypeUi     = true;
            param.bEnabledCaptureTypeUi     = true;
            param.bDisplayCaptureOptionsUi  = true;
            param.bEnabledCaptureOptionsUi  = true;
            capture.activate(param);
        }
        break;
    case CreateRevolutionDialog::CM_PlanePoint:
        {
            WD::WDCapturePositioningParam param = capture.param();
            param.pMonitor                  = this;
            param.bDisplayCaptureTypeUi     = true;
            param.bEnabledCaptureTypeUi     = true;
            param.bDisplayCaptureOptionsUi  = true;
            param.bEnabledCaptureOptionsUi  = true;
            capture.activate(param);
        }
        break;
    default:
        {
            capture.deativate();
        }
        break;
    }
}
void CreateRevolutionDialog::activeRenderObject(bool bActive)
{
    if (bActive)
        _app.scene().addRenderObject(&_renderObject);
    else
        _app.scene().removeRenderObject(&_renderObject);
}
void CreateRevolutionDialog::updateAxisVisible()
{
    auto gTRMat = this->globalTRMat();
    bool bShow  = this->isVisible() && gTRMat;
    if (bShow)
    {
        auto& axis = _app.viewer().browseAxisMgr().get("ui_com_design_equi_createRevolution::CreateRevolutionDialog");
        axis.setAxisColor(WD::WDBrowseAxisTransform::ADF_X, WD::Color(255, 0, 0, 150));
        axis.setAxisColor(WD::WDBrowseAxisTransform::ADF_Y, WD::Color(0, 255, 0, 150));
        axis.setAxisColor(WD::WDBrowseAxisTransform::ADF_Z, WD::Color(0, 0, 255, 150));
        axis.setTransform(gTRMat.value());
    }
    else
    {
        _app.viewer().browseAxisMgr().put("ui_com_design_equi_createRevolution::CreateRevolutionDialog");
    }
}
WD::WDNode::SharedPtr CreateRevolutionDialog::getParentNode()
{
    auto pCurrNode = _app.nodeTree().currentNode();
    if (pCurrNode == nullptr)
        return nullptr;
    bool isNegative     = ui.checkBoxNegative->isChecked();
    const char* type    = isNegative ? "NREV" : "REVO";
    return _app.getBMDesign().findParentWithType(*pCurrNode, type);
}

void CreateRevolutionDialog::onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused(pCurrNode);
    WDUnused(pPrevNode);
    WDUnused(sender);
    _nameHelpter.resetName();
}
void CreateRevolutionDialog::retranslateUi()
{
    Trs("CreateRevolutionDialog"
        , static_cast<QDialog*>(this)
        
        , ui.groupBoxAxes
        , ui.pushButtonRotationLine
        , ui.pushButtonFlip
        , ui.pushButtonPointOnPlane

        , ui.groupBoxSettings
        , ui.labelName
        , ui.labelAngle

        , ui.labelObstruction
        , ui.comboBoxObstruction

        , ui.labelNegative

        , ui.groupBoxVertices
        , ui.labelMethod

        , ui.comboBoxMethod

        , ui.tableWidgetVertices

        , ui.pushButtonAdd
        , ui.pushButtonImport
        , ui.pushButtonExport
        , ui.pushButtonDelete
        , ui.pushButtonClear

        , ui.pushButtonOK
        , ui.pushButtonCancel);

    _pPositionInputDialog->retranslateUi("CreateRevolutionDialog");
    _pPolarCoordInputDialog->retranslateUi("CreateRevolutionDialog");
    _pDefineRectDialog->retranslateUi("CreateRevolutionDialog");
}

void CreateRevolutionDialog::onResult(const WD::WDCapturePositioningResult& result
    , bool& existFlag
    , const WD::WDCapturePositioning& sender)
{
    WDUnused(sender);
    switch (_captureMode)
    {
    case CreateRevolutionDialog::CM_None:
        break;
    case CreateRevolutionDialog::CM_AxisLine:
        {
            // 必须拾取到轴线数据
            if (!result.segmentData)
            {
                return;
            }
            // 如果start点已经定义,则重新计算点
            if (_start)
            {
                const WD::DVec3& pt = _start.value();
                WD::DVec3 prjPt     =  result.segmentData.value().closestPointToPoint(pt, false);
                // 平面上的点不能落在旋转轴线上，否则无法确定旋转平面
                if (WD::DVec3::DistanceSq(pt, prjPt) <= WD::NumLimits<float>::Epsilon)
                {
                    WD_ERROR(TrF("Cannot compute plane of profile"));
                    return;
                }
                // 赋值旋转轴线
                _rotationLine   = result.segmentData.value();
                _center         = prjPt;
                // 自动退出捕捉并将捕捉模式设置为 None
                existFlag       = true;
                _captureMode    = CreateRevolutionDialog::CM_None;

                // 确定了旋转平面
                WD::DVec3 axisX = _rotationLine.value().direction();
                WD::DVec3 axisY = WD::DVec3::Normalize(_start.value() - _center.value());
                WD::DVec3 nor   = WD::DVec3::Normalize(WD::DVec3::Cross(axisX, axisY));
                _loopVerticesEditHelpter.setPlane(nor, pt);
            }
            else
            {
                // 赋值旋转轴线
                _rotationLine   = result.segmentData.value();
                // 自动退出捕捉并将捕捉模式设置为 None
                existFlag       = true;
                _captureMode    = CreateRevolutionDialog::CM_None;
            }
            // 说明轴线设置成功，启用轴线翻转按钮
            ui.pushButtonFlip->setEnabled(true);
            // 说明轴线设置成功，平面顶点捕捉按钮
            ui.pushButtonPointOnPlane->setEnabled(true);
        }
        break;
    case CreateRevolutionDialog::CM_PlanePoint:
        {
            // 必须先定义旋转轴线
            if (!_rotationLine)
            {
                assert(false);
                return;
            }
            const WD::DVec3& pt = result.point;
            WD::DVec3 prjPt     = _rotationLine.value().closestPointToPoint(pt, false);
            // 平面上的点不能落在旋转轴线上，否则无法确定旋转平面
            if (WD::DVec3::DistanceSq(pt, prjPt) <= WD::NumLimits<float>::Epsilon)
            {
                WD_ERROR(TrF("Cannot compute plane of profile"));
                return;
            }
            _start              = pt;
            _center             = prjPt;
            // 自动退出捕捉并将捕捉模式设置为 None
            existFlag           = true;
            _captureMode        = CreateRevolutionDialog::CM_None;
            // 确定了旋转平面
            WD::DVec3 axisX     = _rotationLine.value().direction();
            WD::DVec3 axisY     = WD::DVec3::Normalize(_start.value() - _center.value());
            WD::DVec3 nor       = WD::DVec3::Normalize(WD::DVec3::Cross(axisX, axisY));
            _loopVerticesEditHelpter.setPlane(nor, pt);

            // 将起始点添加为顶点列表的第一个顶点
            if (_loopVerticesEditHelpter.points().empty())
                _loopVerticesEditHelpter.addPoint(WD::DVec4(pt, 0.0));
            else
                _loopVerticesEditHelpter.setPoint(0, WD::DVec4(pt, 0.0));
            
            // 已经确定旋转环所在平面，启用顶点编辑界面
            ui.groupBoxVertices->setEnabled(true);
        }
        break;
    default:
        break;
    }

    // 更新旋转体坐标轴显隐
    this->updateAxisVisible();
}