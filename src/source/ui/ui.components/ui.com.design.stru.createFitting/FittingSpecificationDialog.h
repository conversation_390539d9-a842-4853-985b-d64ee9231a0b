#pragma once
#include <QDialog>
#include "core/WDCore.h"
#include "core/node/WDNode.h"
#include "ui_FittingSpecificationDialog.h"

class FittingSpecificationDialog : public QDialog
{
	Q_OBJECT
public:
    /**
     * @brief 附件有三种，分别挂载在面，板和直墙节点下
    */
    enum FittingType
    {
        // 墙附件
        FT_Wall = 0,
        // SCTN附件
        FT_Sctn,
        // 面附件
        FT_Floor,
        // 板附件
        FT_Panel,
        // 空
        FT_Null,
    };

    FittingSpecificationDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
	virtual ~FittingSpecificationDialog();
private:
    /**
     * @brief 获取附件类型的用途字符串
     * @param type 附件类型
    */
    const char* getFittingTypePerpose(const FittingType& type);
    /**
    * @brief 获取附件的父节点(即挂载节点)类型字符串
    * @param type 附件类型
    */
    const char* getFittingParentType(const FittingType& type);
signals:
   /**
    * @brief 发送选中的SPCO节点
   */
   void sigSpcoChoosed(WD::WDNode::SharedPtr pSpco);
public:
    /**
    * @brief 获取当前所有属性值
    * @return 属性集合
    */
    std::vector<double> getProperties();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
public:
    /**
    * @brief 更新GroupBoxSpecificationData中的控件显示，默认是面附件
    */
    void initSpecificationDataGroupBox(const FittingType& type = FT_Wall, WD::WDNode::SharedPtr pNode = nullptr);
private slots:
    /**
     * @brief 等级下拉框点击选项响应槽函数
    */
    void slotComboBoxSpecificationActivated(int index);
    /**
     * @brief 元件节点点击响应槽函数
    */
    void slotlistWidgetNodeListItemPressed(QListWidgetItem* curItem);
    /**
     * @brief 元件节点点击响应槽函数
     * @param curItem 当前选中的item
    */
    void slotlistWidgetNodeHistoryItemPressed(QListWidgetItem* curItem);
    /**
     * @brief 应用按键响应槽函数
    */
    void slotApplyButtonClicked();
    /**
     * @brief 退出按键响应槽函数
    */
    void slotDismissButtonClicked();
    /**
     * @brief 返回按键响应槽函数
    */
    void slotBackButtonClicked();
private:
    struct DataNodeValue
    {
        //  Data数据的编号
        int number;
        //  Data数据的描述
        std::string desc;
        //  Data数据的默认值
        int defaultValue;
        DataNodeValue()
        {
            defaultValue = 0;
            number = 0;
        }
    };
    
    using DataNodeValues = std::vector<DataNodeValue>;
    /**
     * @brief  获取pSpco引用的元件引用的数据集下的所有用途为DESP的数据
     * @param pSpco 
     * @param outValues 存放数据
    */
    void getDataNodeDespValue(WD::WDCore& core, WD::WDNode::SharedPtr pSpco, DataNodeValues& outValues);
    /**
    * @brief 更新comboBoxSpecification
    * @param pSpec 添加到Combox中的SPEC节点
    */
    void updateComboBoxSpecification(WD::WDNode::SharedPtr pSpec);
    /**
    * @brief 更新ListWidget
    * @param node   SELE 或 SPEC 节点
    */
    void updateListWidgetNode(const WD::WDNode& node);
    /**
    * @brief 更新tableWidgetPropertise
    * @param pSpco 添加SELE节点下SPCO节点CatRef属性引用的元件的属性到表格中
    */
    void updateTableWidgetPropertise(const DataNodeValues& pDatas);
    /**
    * @brief 更新ListWidget
    * @param pNode SELE节点或SPEC节点
    */
    void addListHistory(WD::WDNode::SharedPtr pNode);
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
    /**
    * @brief  是否有SPRF节点被选中
    */
    inline bool isSprfChecked()
    {
        return _specStruct.isVisibel();
    }
private:
   // 保存选中的等级与SPRF节点的结构体
   struct SpecStruct
   {
       WD::WDNode::WeakPtr _pSpwl;
       WD::WDNode::WeakPtr _pSpec;
       // SPEC节点到SPCO节点可能有0到多个SELE节点
       std::vector<WD::WDNode::WeakPtr> _pSeles;
       WD::WDNode::WeakPtr _pSpco;
       WD::WDNode::WeakPtr _pSprf;
       // 从配置中读取保存的等级
       bool loadFromCfg(const std::string&);
       // 保存的等级配置
       bool saveToCfg(const std::string&);
       // 设置节点
       bool setNode(WD::WDNode::SharedPtr ptr);
       // 取消弱引用节点
       void clear();
       // 是否有引用空指针
       bool isVisibel();
   };
private:
    Ui::FittingSpecificationDialog ui;
    WD::WDCore& _core;
    //  保存当前界面的附件类型
    FittingType _type;
    //  保存用来更新listWidget的节点
    WD::WDNode::WeakPtr _pListWidgetNode;
    SpecStruct _specStruct;
};

using FittingType = FittingSpecificationDialog::FittingType;