#include "FittingSpecificationDialog.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "core/common/WDConfig.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"

FittingSpecificationDialog::FittingSpecificationDialog(WD::WDCore& core, QWidget *parent)
	: QDialog(parent)
	, _core(core)
{
	ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    _type = FT_Null;
	ui.tableWidgetPropertise->setColumnCount(4);
	//	设置表头不可见
	ui.tableWidgetPropertise->verticalHeader()->setVisible(false);
	ui.tableWidgetPropertise->horizontalHeader()->setVisible(false);
	//设置属性名称列不可修改
    ui.tableWidgetPropertise->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Interactive);
    ui.tableWidgetPropertise->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Interactive);
    ui.tableWidgetPropertise->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
	this->retranslateUi();
	connect(ui.pushButtonApply
		, &QPushButton::clicked
		, this 
		, &FittingSpecificationDialog::slotApplyButtonClicked);
	connect(ui.pushButtonDismiss
		, &QPushButton::clicked
		, this 
		, &FittingSpecificationDialog::slotDismissButtonClicked);
	connect(ui.comboBoxSpecification
		, QOverload<int>::of(&QComboBox::activated)
		, this
		, &FittingSpecificationDialog::slotComboBoxSpecificationActivated);
	connect(ui.listWidgetNodeList
		, &QListWidget::itemPressed
		, this
		, &FittingSpecificationDialog::slotlistWidgetNodeListItemPressed);
	connect(ui.listWidgetNodeHistory
		, &QListWidget::itemPressed
		, this
		, &FittingSpecificationDialog::slotlistWidgetNodeHistoryItemPressed);
	connect(ui.pushButtonBack
		, &QPushButton::clicked
		, this
		, &FittingSpecificationDialog::slotBackButtonClicked);
	connect(ui.pushButtonSpecification
		, &QPushButton::clicked
		, [&](){
        this->initSpecificationDataGroupBox(this->_type);
    });
}
FittingSpecificationDialog::~FittingSpecificationDialog()
{
}
const char* FittingSpecificationDialog::getFittingTypePerpose(const FittingType& type)
{
	switch(type)
	{
	case FT_Wall: return "WFIT";
    case FT_Sctn: return "FFIT";
	case FT_Floor: return "FFIT";
	case FT_Panel: return "PFIT";
	}
	assert(false && "错误的附件类型");
	return	nullptr;
}
const char* FittingSpecificationDialog::getFittingParentType(const FittingType& type)
{
    switch(type)
    {
    case FT_Wall:   return "STWALL";
    case FT_Sctn:   return "SCTN";
    case FT_Floor:  return "FLOOR";
    case FT_Panel:  return "PANE";
    }
    assert(false && "错误的附件类型");
    return	nullptr;
}
std::vector<double> FittingSpecificationDialog::getProperties()
{
    std::vector<double> params;
    // 设计参数不是按顺序排列，看会跳过某些值，这里设置默认值
    double defaultValue = 0.0;

    auto processValue = [&defaultValue](const QTableWidgetItem& item, std::vector<double>& params){
        QVariant pDataValue = item.data(Qt::UserRole);
        int number = pDataValue.value<int>();
        if (number > 0)
        {
            bool bOk;
            auto value = item.text().toDouble(&bOk);
            if (!bOk)
            {
                assert(false && "数据获取失败!");
                return;
            }
            if (number > params.size())
                params.resize(number, defaultValue);
            params[number - 1] = value;
        }
    };
    for (int index = 0; index < ui.tableWidgetPropertise->rowCount(); index++)
    {
        auto pItem = ui.tableWidgetPropertise->item(index, 1);
        if (pItem != nullptr)
            processValue(*pItem, params);
        pItem = ui.tableWidgetPropertise->item(index, 3);
        if (pItem != nullptr)
            processValue(*pItem, params);
    }
    return params;
}
void FittingSpecificationDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
	WDUnused(evt);
}

void FittingSpecificationDialog::hideEvent(QHideEvent* evt)
{
	WDUnused(evt);
}
void FittingSpecificationDialog::initSpecificationDataGroupBox(const FittingType& type, WD::WDNode::SharedPtr pNode)
{
    // 清除旧有的列表信息
    ui.tableWidgetPropertise->clear();
    ui.tableWidgetPropertise->setRowCount(0);
    ui.comboBoxSpecification->clear();
    ui.listWidgetNodeHistory->clear();
    ui.listWidgetNodeList->clear();
    _type = type;
	// 获取Purpose为type的SPEC节点
    auto nodes = WD::GetSPECByAttrPurpose(_core.getBMCatalog(), getFittingTypePerpose(_type));
    if (nodes.empty())
        return;

    // 使用筛选后的SPEC节点更新COMBOBOX
    for (WD::WDNode::SharedPtr eachNode : nodes)
    {
        // 需要保存为弱引用
        this->updateComboBoxSpecification(eachNode);
    }
    if (ui.comboBoxSpecification->count() == 0)
    {
        assert(false && "等级数量为0!");
        return;
    }
    //从配置中读取选中节点
    if (pNode != nullptr)
        _specStruct.setNode(pNode);
    else
        _specStruct.loadFromCfg(getFittingParentType(_type));
    // 若从配置里读取到了配置的等级节点,使用此节点更新界面
    if (isSprfChecked())
    {
        WD::WDNode::SharedPtr pSpecNode = _specStruct._pSpec.lock();
        if (!pSpecNode->isType("SPEC"))
        {
            ui.comboBoxSpecification->setCurrentIndex(0);
            ui.comboBoxSpecification->activated(0);
            return ;
        }
        const std::string specDesc  = pSpecNode->getAttribute("Description").convertToString();
        int index = ui.comboBoxSpecification->findText(QString::fromUtf8(specDesc.c_str()));
        if (index == -1)
            index = 0;
        ui.comboBoxSpecification->setCurrentIndex(index);
        ui.comboBoxSpecification->activated(index);
        auto& seles = _specStruct._pSeles;
        // 因为保存seles节点时是从子节点向上查找父节点，这里是从父节点查找子节点，所以要反向查找
        for (auto seleItr = seles.rbegin(); seleItr != seles.rend(); seleItr++)
        {
            auto pSeleNode = seleItr->lock();
            if (pSeleNode == nullptr)
                continue;
            if (!pSeleNode->isType("SELE"))
                continue;

            auto items = ui.listWidgetNodeList->findItems(QString::fromUtf8(pSeleNode->getAttribute("Description").convertToString().c_str())
                , Qt::MatchExactly);
            assert(items.size() == 1);
            if (items.empty())
                break;
            auto item = items[0];
            ui.listWidgetNodeList->setCurrentItem(item);
            slotlistWidgetNodeListItemPressed(item);
            continue;
        }
        auto pSpco = _specStruct._pSpco.lock();
        if (pSpco != nullptr)
        {
            if (pSpco->isType("SPCO"))
            {
                // 获取引用节点
                auto pRefNode = pSpco->getAttribute("Catref").toNodeRef().refNode();
                if (pRefNode != nullptr)
                {
                    auto items = ui.listWidgetNodeList->findItems(QString::fromUtf8(pRefNode->name().c_str()), Qt::MatchExactly);
                    assert(items.size() == 1);
                    if (!items.empty())
                    {
                        auto item = items[0];
                        ui.listWidgetNodeList->setCurrentItem(item);
                        slotlistWidgetNodeListItemPressed(item);
                        emit sigSpcoChoosed(pSpco);
                    }
                }
            }
        }
    }
    else
    {
        // 若配置节点未找到或者为空，则默认使用第一个SPCO节点引用的元件
        ui.comboBoxSpecification->setCurrentIndex(0);
        ui.comboBoxSpecification->activated(0);
        while (ui.tableWidgetPropertise->rowCount() == 0)
        {
            auto pItem = ui.listWidgetNodeList->item(0);
            if (pItem == nullptr)
                break;
            QVariant userData = pItem->data(Qt::UserRole);
            UiWeakObject weakObject = userData.value<UiWeakObject>();
            WD::WDNode::SharedPtr itemNode = WD::WDNode::SharedCast(weakObject.object());
            if (itemNode == nullptr)
                break;
            if (itemNode->isType("SPCO"))
            {
                ui.listWidgetNodeList->setCurrentItem(pItem);
                slotlistWidgetNodeListItemPressed(pItem);
                break;
            }
            ui.listWidgetNodeList->setCurrentItem(pItem);
            slotlistWidgetNodeListItemPressed(pItem);
        }
    }
}

void FittingSpecificationDialog::slotComboBoxSpecificationActivated(int index)
{
    QVariant userData = ui.comboBoxSpecification->itemData(index, Qt::UserRole);
    UiWeakObject weakObject = userData.value<UiWeakObject>();
    WD::WDNode::SharedPtr pSpec = WD::WDNode::SharedCast(weakObject.object());
    if (pSpec == nullptr)
    {
        assert(false);
        return ;
    }
    if (!pSpec->isType("SPEC"))
    {
        assert(false);
        return ;
    }
    // 更新保存的SPWL节点
    WD::WDNode::SharedPtr pSpwl = pSpec->parent();
    if (pSpwl == nullptr)
    {
        assert(false);
        return ;
    }
    _specStruct._pSpwl = pSpwl;
    // 更新保存的SPEC节点
    _specStruct._pSpec = pSpec;
    this->updateListWidgetNode(*pSpec);
    _pListWidgetNode.reset();
    _pListWidgetNode = pSpec;
    ui.listWidgetNodeHistory->clear();
    addListHistory(pSpec);
}

void FittingSpecificationDialog::slotlistWidgetNodeListItemPressed(QListWidgetItem* curItem)
{
	if (curItem == nullptr)
		return ;
	QVariant userData = curItem->data(Qt::UserRole);
	UiWeakObject weakObject = userData.value<UiWeakObject>();
	WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(weakObject.object());
	if (pNode == nullptr)
	{
		assert(false);
		return ;
	}
	//	如果节点为SELE节点，更新listWidget
	if (pNode->isType("SELE"))
	{
		addListHistory(pNode);
        updateListWidgetNode(*pNode);
		_pListWidgetNode = pNode;
		return;
	}
	//	如果节点为SPCO节点，更新tableWidget
	if (pNode->isType("SPCO"))
	{
        // 更新保存的SPCO节点
        _specStruct._pSpco = pNode;
		DataNodeValues values;
		getDataNodeDespValue(_core, pNode, values);
		if (!values.empty())
			updateTableWidgetPropertise(values);
	}
}

void FittingSpecificationDialog::slotlistWidgetNodeHistoryItemPressed(QListWidgetItem* curItem)
{
	if (curItem == nullptr)
		return ;
	QVariant userData = curItem->data(Qt::UserRole);
	UiWeakObject weakObject = userData.value<UiWeakObject>();
	WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(weakObject.object());
    assert(pNode != nullptr);
    if (_pListWidgetNode.lock() == pNode || pNode == nullptr)
        return;
	//	如果节点不是SELE节点并且不是SPEC节点，返回
	if (!pNode->isAnyOfType("SELE", "SPEC"))
	{
		assert(false);
		return;
	}
    updateListWidgetNode(*pNode);
	_pListWidgetNode = pNode;
	int index = ui.listWidgetNodeHistory->currentIndex().row();
	int size = ui.listWidgetNodeHistory->count();
	for ( ++index; index < size; --size)
	{
		auto* pItem	= ui.listWidgetNodeHistory->takeItem(index);
		if (pItem != nullptr)
		    delete pItem;
	}
}

void FittingSpecificationDialog::slotApplyButtonClicked()
{
    // 保存选择的SPCO节点
    auto item = ui.listWidgetNodeList->currentItem();
    WD::WDNode::SharedPtr pNode = nullptr;
    if (item != nullptr)
        pNode =  WD::WDNode::SharedCast(item->data(Qt::UserRole).value<UiWeakObject>().object());
    emit sigSpcoChoosed(pNode);
    if (pNode != nullptr)
    // 保存配置文件
    _specStruct.saveToCfg(getFittingParentType(_type));
}

void FittingSpecificationDialog::slotDismissButtonClicked()
{
	this->reject();
}

void FittingSpecificationDialog::slotBackButtonClicked()
{
	//返回上一级，需要拿到当前层级节点的祖父节点，这里默认使用listWidget中的第一个节点
	auto pNode = _pListWidgetNode.lock();
	if (pNode == nullptr)
	{
		assert(false);
		return;
	}
    //  返回操作会删除上一个层级记录
	int	index = ui.listWidgetNodeHistory->count() - 1;
    auto* pItem = ui.listWidgetNodeHistory->takeItem(index);
    if (pItem != nullptr)
        delete pItem;

	//	如果上级节点是SPEC节点，不向上更新界面
	if (pNode->isType("SPEC"))
		return;
	auto pParent = pNode->parent();
	if (pParent == nullptr)
	{
		assert(false);
		return;
	}
	//	上级节点不会是SPCO节点
    updateListWidgetNode(*pParent);
	_pListWidgetNode.reset();
	_pListWidgetNode = pParent;
}

void FittingSpecificationDialog::getDataNodeDespValue(WD::WDCore& core, WD::WDNode::SharedPtr pSpco, DataNodeValues& outValues)
{
    if (pSpco == nullptr || !pSpco->isType("SPCO"))
    {
        assert(false);
        return;
    }
	const auto pCatrefNode = pSpco->getAttribute("Catref").toNodeRef().refNode();
	if (pCatrefNode == nullptr)
        return;
    if (!pCatrefNode->isType("SFIT"))
    {
        assert(false && "未处理的引用类型!");
        return;
    }
    const auto pDataSet = pCatrefNode->getAttribute("Dtref").toNodeRef().refNode();
    if (pDataSet == nullptr)
        return;
    DataNodeValue value;
    bool bOk;
    const auto params = pCatrefNode->getAttribute("Param").toStringVector();

    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet(nullptr, pSpco);
    for (auto& child : pDataSet->children())
    {
        if (child == nullptr)
            continue;
        if (!child->isType("DATA"))
            continue;
        auto purpose = child->getAttribute("Purpose").convertToString();
        //	用途为DESP的数据需要设置值并传递给元件
        if (strcmp(purpose.c_str(), "DESP") != 0)
            continue;
        int	number = child->getAttribute("Number").toInt();
        if (number <= 0)
            continue;
        value.number = number;
        value.desc = child->getAttribute("Dtitle").convertToString();
        // 赋默认值
        value.defaultValue = aGet.getAttribute(*child, "Dproperty").convertToInt(&bOk);
        if (!bOk)
            value.defaultValue = 0;
        outValues.push_back(value);
    }
}

void FittingSpecificationDialog::updateComboBoxSpecification(WD::WDNode::SharedPtr pSpec)
{
	if (pSpec == nullptr)
		return;
	if (!pSpec->isType("SPEC"))
		return;
	// 获取该SPEC节点的DESC属性
	std::string descStr = pSpec->getAttribute("Description").convertToString();
	if (descStr.empty())
		return;
	QVariant userData;
	userData.setValue(UiWeakObject(pSpec));
	// 添加到下拉框显示
	ui.comboBoxSpecification->addItem(QString::fromUtf8(descStr.c_str()), userData);
}

void FittingSpecificationDialog::updateListWidgetNode(const WD::WDNode& node)
{
	// 清除旧有的列表信息
	ui.listWidgetNodeList->clear();
    // 清空属性界面
    ui.tableWidgetPropertise->clear();
	// 获取SPEC节点或SELE节点下的SELE节点  或SELE节点下的SPCO节点
	for (auto& pSubNode : node.children())
	{
		if (pSubNode == nullptr)
			continue;
		if (pSubNode->isType("SPCO"))
		{
			// 获取引用节点
			auto pRefNode = pSubNode->getAttribute("Catref").toNodeRef().refNode();
			if (pRefNode == nullptr)
				continue;
			// 获取引用节点名称,添加到列表中
			QVariant userData;
			userData.setValue(UiWeakObject(pSubNode));
			auto item = new QListWidgetItem(QString::fromUtf8(pRefNode->name().c_str()));
			item->setData(Qt::UserRole, userData);
			ui.listWidgetNodeList->addItem(item);
            continue;
		}
		if (pSubNode->isType("SELE"))
		{
			// 获取该SELE节点的DESC属性
            std::string descStr = pSubNode->getAttribute("Description").convertToString();
			if (descStr.empty())
				continue;
			// 获取节点名称,添加到列表中
			QVariant userData;
			userData.setValue(UiWeakObject(pSubNode));
			auto item = new QListWidgetItem(QString::fromUtf8(descStr.c_str()));
			item->setData(Qt::UserRole, userData);
			ui.listWidgetNodeList->addItem(item);
		}
	}
}
void FittingSpecificationDialog::addListHistory(WD::WDNode::SharedPtr pNode)
{
	if (pNode == nullptr)
	{
		assert(false);
		return;
	}
	// 添加到列表中
	QVariant addData;
	addData.setValue(UiWeakObject(pNode));
	// 获取引用节点名称
    auto nodeDesc = pNode->getAttribute("Description").convertToString();
	auto item = new	QListWidgetItem(QString::fromUtf8(nodeDesc.c_str()));
	item->setData(Qt::UserRole, addData);
	ui.listWidgetNodeHistory->addItem(item);
}

void FittingSpecificationDialog::updateTableWidgetPropertise(const DataNodeValues& pDatas)
{
	// tableWidgetPropertise
	ui.tableWidgetPropertise->blockSignals(true);
	// 清除旧有的列表信息
	ui.tableWidgetPropertise->clear();
	ui.tableWidgetPropertise->setRowCount(0);
	int index = 0;
	for (auto& each : pDatas)
	{
		// 获取当前行数
		int curRowCount = ui.tableWidgetPropertise->rowCount();
        QTableWidgetItem* item1 = nullptr;
        QTableWidgetItem* item2 = nullptr;
		//	根据下标判断列是否填满
		if (index++ % 2 == 0)
		{
            item1 = new QTableWidgetItem();
            item1->setTextAlignment(Qt::AlignCenter);
            item1->setFlags(Qt::NoItemFlags);
            item2 = new QTableWidgetItem();
            item2->setTextAlignment(Qt::AlignCenter);
            item2->setFlags(Qt::NoItemFlags);
		    QTableWidgetItem* item3	= new QTableWidgetItem();
		    item3->setTextAlignment(Qt::AlignCenter);
		    item3->setFlags(Qt::NoItemFlags);
		    QTableWidgetItem* item4 = new QTableWidgetItem();
		    item4->setTextAlignment(Qt::AlignCenter);
            item4->setFlags(Qt::NoItemFlags);
			//	新增行
			ui.tableWidgetPropertise->setRowCount(curRowCount + 1);
			ui.tableWidgetPropertise->setItem(curRowCount, 0, item1);
			ui.tableWidgetPropertise->setItem(curRowCount, 1, item2);
            ui.tableWidgetPropertise->setItem(curRowCount, 2, item3);
            ui.tableWidgetPropertise->setItem(curRowCount, 3, item4);
		}
		else
		{
            item1 = ui.tableWidgetPropertise->item(curRowCount - 1, 2);
            item2 = ui.tableWidgetPropertise->item(curRowCount - 1, 3);
		}
        assert(item1 != nullptr);
        assert(item2 != nullptr);
        item1->setFlags(Qt::ItemIsEnabled | Qt::ItemIsSelectable);
        item2->setFlags(Qt::ItemIsEnabled | Qt::ItemIsSelectable | Qt::ItemIsEditable);
        item1->setText(QString::fromUtf8(WD::WDTs("StruCreateFittingCN", each.desc).c_str()));
        item2->setText(QString::number(each.defaultValue));
        QVariant userData;
        userData.setValue(each.number);
        item2->setData(Qt::UserRole, userData);
	}
	ui.tableWidgetPropertise->blockSignals(false);
}
void FittingSpecificationDialog::retranslateUi()
{
    Trs("StruCreateFitting"
        , static_cast<QDialog*>(this)
        , ui.groupBoxSpecData
        , ui.pushButtonSpecification
		, ui.pushButtonBack
        , ui.groupBoxPropertise
        , ui.pushButtonApply
        , ui.pushButtonDismiss
		, ui.tableWidgetPropertise
		, ui.labelNodeLevel
		, ui.labelNodeList
	);
}


bool FittingSpecificationDialog::SpecStruct::loadFromCfg(const std::string& type)
{
	WD::WDNode::SharedPtr pSPCONode = nullptr;
	// 从config文件中读取上次选择的等级引用节点
	// 得到节点的
	auto pCfgValueStrId = WD::Core().cfg().get<std::string>("spec.fitt." + type).value<std::string>();
	if (pCfgValueStrId != nullptr)
	{
		// 根据Id查询获取当前的节点
		WD::WDUuid uuid = WD::WDUuid::FromString(*pCfgValueStrId);
		// 获取元件模块节点树根节点
		pSPCONode = WD::Core().getBMCatalog().findNode(uuid, "SPCO");
	}
    return setNode(pSPCONode);
}

bool FittingSpecificationDialog::SpecStruct::saveToCfg(const std::string& type)
{
    auto node = _pSpco.lock();
    if (node == nullptr)
        return  false;
    // 获取uuid
    std::string strId = node->uuid().toString();
    WD::Core().cfg().get<std::string>("spec.fitt." + type, "").setValue(strId);
    return  true;
}

bool FittingSpecificationDialog::SpecStruct::setNode(WD::WDNode::SharedPtr ptr)
{
    clear();
    if (ptr == nullptr)
        return false;
    if (!ptr->isType("SPCO"))
        return false;
    auto pRefNode = ptr->getAttribute("Catref").toNodeRef().refNode();
    if (pRefNode == nullptr || !pRefNode->isType("SFIT"))
        return false;
    // SPCO的父节点可能是SELE节点和SPEC节点
    auto pParent = ptr->parent();
    // 循环直到找到SPEC节点
    while (true)
    {
        if (pParent == nullptr)
        {
            clear();
            assert(false);
            return false;
        }

        if (pParent->isType("SELE"))
        {
            _pSeles.push_back(pParent);
        }
        else if (pParent->isType("SPEC"))
        {
            _pSpec = pParent;
            // SPEC节点的父节点只能是SPWL节点
            pParent = pParent->parent();
            if (pParent == nullptr || !pParent->isType("SPWL"))
            {
                clear();
                assert(false);
                return false;
            }
            _pSpwl = pParent;
            _pSpco = ptr;
            _pSprf = pRefNode;
            return true;
        }
        else
        {
            clear();
            assert(false);
            return false;
        }
        pParent = pParent->parent();
    }
}

void FittingSpecificationDialog::SpecStruct::clear()
{
    _pSpwl.reset();
    _pSpec.reset();
    _pSeles.clear();
    _pSpco.reset();
    _pSprf.reset();
}

bool FittingSpecificationDialog::SpecStruct::isVisibel()
{
    std::vector<WD::WDNode::SharedPtr> nodes = {_pSpwl.lock()
        , _pSpec.lock()
        , _pSpco.lock()
        , _pSprf.lock()
    };

    for (WD::WDNode::SharedPtr pNode : nodes)
    {
        if (pNode == nullptr)
            return false;
    }

    return true;
}