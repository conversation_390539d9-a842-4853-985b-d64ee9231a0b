<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CreateRectangularGridDialog</class>
 <widget class="QWidget" name="CreateRectangularGridDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>821</width>
    <height>396</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_9">
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBoxManage">
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_7">
      <item row="0" column="0" colspan="3">
       <widget class="QTreeWidget" name="treeWidget">
        <property name="focusPolicy">
         <enum>Qt::StrongFocus</enum>
        </property>
        <property name="headerHidden">
         <bool>true</bool>
        </property>
        <column>
         <property name="text">
          <string notr="true">1</string>
         </property>
        </column>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QPushButton" name="pushButtonRefresh">
        <property name="text">
         <string>Refresh</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QPushButton" name="pushButtonEdit">
        <property name="text">
         <string>Edit</string>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QPushButton" name="pushButtonDelete">
        <property name="focusPolicy">
         <enum>Qt::StrongFocus</enum>
        </property>
        <property name="text">
         <string>Delete</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="Options">
      <attribute name="title">
       <string>Options</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QLabel" name="labelName">
         <property name="text">
          <string>Name</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="lineEditName">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="4">
        <widget class="QGroupBox" name="groupBoxDisplay">
         <property name="title">
          <string>Display</string>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="1">
           <widget class="QRadioButton" name="radioButtonGridline">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string>Gridline IDs</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="checked">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QRadioButton" name="radioButtonCoordinates">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string>Coordinates</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QRadioButton" name="radioButtonSpacings">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string>Spacings</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QRadioButton" name="radioButtonAxes">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string>Axes only</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="0" colspan="2">
        <widget class="QGroupBox" name="groupBoxAxisX">
         <property name="title">
          <string>X Axis</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_3">
          <item row="0" column="0">
           <widget class="QLabel" name="labelX">
            <property name="text">
             <string>Label</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1" colspan="2">
           <widget class="QLineEdit" name="lineEditLabelX">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelIDsX">
            <property name="text">
             <string>IDs</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="labelOffsetsX">
            <property name="text">
             <string>Offsets</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0" colspan="2">
           <widget class="QTextEdit" name="textEditIDsX">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QTextEdit" name="textEditOffsetsX">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="QGroupBox" name="groupBoxAxisY">
         <property name="title">
          <string>Y Axis</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_4">
          <item row="0" column="0">
           <widget class="QLabel" name="labelY">
            <property name="text">
             <string>Label</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1" colspan="2">
           <widget class="QLineEdit" name="lineEditLabelY">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelIDsY">
            <property name="text">
             <string>IDs</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="labelOffsetsY">
            <property name="text">
             <string>Offsets</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0" colspan="2">
           <widget class="QTextEdit" name="textEditIDsY">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QTextEdit" name="textEditOffsetsY">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QGroupBox" name="groupBoxAxisZ">
         <property name="title">
          <string>Z Axis</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_5">
          <item row="0" column="0">
           <widget class="QLabel" name="labelZ">
            <property name="text">
             <string>Label</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1" colspan="2">
           <widget class="QLineEdit" name="lineEditLabelZ">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelIDsZ">
            <property name="text">
             <string>IDs</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="labelOffsetsZ">
            <property name="text">
             <string>Offsets</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0" colspan="2">
           <widget class="QTextEdit" name="textEditIDsZ">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QTextEdit" name="textEditOffsetsZ">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="3" column="0" colspan="4" alignment="Qt::AlignRight">
        <widget class="QSplitter" name="splitter_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <widget class="QCheckBox" name="checkBoxAxisShowOrHide">
          <property name="text">
           <string>AxisShowOrHide</string>
          </property>
         </widget>
         <widget class="QCheckBox" name="checkBoxCloseZAxis">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string>closeZAxis</string>
          </property>
         </widget>
         <widget class="QPushButton" name="pushButtonAutofill">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string>Autofill</string>
          </property>
          <property name="autoDefault">
           <bool>false</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="pushButtonSetView">
          <property name="focusPolicy">
           <enum>Qt::StrongFocus</enum>
          </property>
          <property name="text">
           <string>SetView</string>
          </property>
          <property name="autoDefault">
           <bool>false</bool>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="PositionAndOrientation">
      <attribute name="title">
       <string>PositionAndOrientation</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_8">
       <item row="0" column="0">
        <widget class="QLabel" name="labelNameTwo">
         <property name="text">
          <string>Name</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="3">
        <widget class="QGroupBox" name="groupBoxPosition">
         <property name="title">
          <string>Position</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_6" columnstretch="0,1,0">
          <item row="0" column="1" alignment="Qt::AlignRight">
           <widget class="QLabel" name="labelSnap">
            <property name="text">
             <string>Snap</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QCheckBox" name="checkBoxSnap">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelPosX">
            <property name="text">
             <string>X</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBoxPosX">
            <property name="focusPolicy">
             <enum>Qt::WheelFocus</enum>
            </property>
            <property name="minimum">
             <double>-100000000000000000.000000000000000</double>
            </property>
            <property name="maximum">
             <double>1000000000000000000.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QCheckBox" name="checkBoxPosX">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="labelPosY">
            <property name="text">
             <string>Y</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBoxPosY">
            <property name="minimum">
             <double>-100000000000000000.000000000000000</double>
            </property>
            <property name="maximum">
             <double>1000000000000000000.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QCheckBox" name="checkBoxPosY">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="labelPosZ">
            <property name="text">
             <string>Z</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBoxPosZ">
            <property name="decimals">
             <number>2</number>
            </property>
            <property name="minimum">
             <double>-100000000000000000.000000000000000</double>
            </property>
            <property name="maximum">
             <double>1000000000000000000.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="3" column="2">
           <widget class="QCheckBox" name="checkBoxPosZ">
            <property name="focusPolicy">
             <enum>Qt::StrongFocus</enum>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="3" column="0" colspan="3">
        <layout class="QVBoxLayout" name="verticalLayoutWrt"/>
       </item>
       <item row="2" column="0" colspan="3">
        <widget class="QGroupBox" name="groupBoxOrientation">
         <property name="title">
          <string>Orientation</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QGroupBox" name="groupBoxOriAX">
            <property name="title">
             <string>X Axis</string>
            </property>
            <layout class="QFormLayout" name="formLayout">
             <item row="0" column="0">
              <widget class="QLabel" name="labelOrientationXX">
               <property name="text">
                <string>X</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxXX">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="labelOrientationXY">
               <property name="text">
                <string>Y</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxXY">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="labelOrientationXZ">
               <property name="text">
                <string>Z</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxXZ">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxOriAY">
            <property name="title">
             <string>Y Axis</string>
            </property>
            <layout class="QFormLayout" name="formLayout_2">
             <item row="0" column="0">
              <widget class="QLabel" name="labelOrientationYX">
               <property name="text">
                <string>X</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxYX">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="labelOrientationYY">
               <property name="text">
                <string>Y</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxYY">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="labelOrientationYZ">
               <property name="text">
                <string>Z</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxYZ">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxOriAZ">
            <property name="title">
             <string>Z Axis</string>
            </property>
            <layout class="QFormLayout" name="formLayout_3">
             <item row="0" column="0">
              <widget class="QLabel" name="labelOrientationZX">
               <property name="text">
                <string>X</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxZX">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="labelOrientationZY">
               <property name="text">
                <string>Y</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxZY">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="labelOrientationZZ">
               <property name="text">
                <string>Z</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QDoubleSpinBox" name="doubleSpinBoxZZ">
               <property name="minimum">
                <double>-999999999.000000000000000</double>
               </property>
               <property name="maximum">
                <double>999999999.990000009536743</double>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item row="0" column="2">
        <spacer name="horizontalSpacer_5">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>297</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="lineEditNameTwo">
         <property name="focusPolicy">
          <enum>Qt::StrongFocus</enum>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item row="1" column="0" colspan="2" alignment="Qt::AlignRight">
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <widget class="QPushButton" name="pushButtonSave">
      <property name="focusPolicy">
       <enum>Qt::StrongFocus</enum>
      </property>
      <property name="text">
       <string>Save</string>
      </property>
      <property name="autoDefault">
       <bool>false</bool>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButtonClose">
      <property name="focusPolicy">
       <enum>Qt::StrongFocus</enum>
      </property>
      <property name="text">
       <string>Close</string>
      </property>
      <property name="autoDefault">
       <bool>false</bool>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>tabWidget</tabstop>
  <tabstop>lineEditName</tabstop>
  <tabstop>radioButtonAxes</tabstop>
  <tabstop>radioButtonGridline</tabstop>
  <tabstop>radioButtonSpacings</tabstop>
  <tabstop>radioButtonCoordinates</tabstop>
  <tabstop>lineEditLabelX</tabstop>
  <tabstop>textEditIDsX</tabstop>
  <tabstop>textEditOffsetsX</tabstop>
  <tabstop>lineEditLabelY</tabstop>
  <tabstop>textEditIDsY</tabstop>
  <tabstop>textEditOffsetsY</tabstop>
  <tabstop>lineEditLabelZ</tabstop>
  <tabstop>textEditIDsZ</tabstop>
  <tabstop>textEditOffsetsZ</tabstop>
  <tabstop>checkBoxCloseZAxis</tabstop>
  <tabstop>pushButtonAutofill</tabstop>
  <tabstop>pushButtonSetView</tabstop>
  <tabstop>pushButtonSave</tabstop>
  <tabstop>pushButtonClose</tabstop>
  <tabstop>doubleSpinBoxXX</tabstop>
  <tabstop>doubleSpinBoxXY</tabstop>
  <tabstop>doubleSpinBoxXZ</tabstop>
  <tabstop>doubleSpinBoxYX</tabstop>
  <tabstop>doubleSpinBoxYY</tabstop>
  <tabstop>doubleSpinBoxYZ</tabstop>
  <tabstop>doubleSpinBoxZX</tabstop>
  <tabstop>doubleSpinBoxZY</tabstop>
  <tabstop>doubleSpinBoxZZ</tabstop>
  <tabstop>lineEditNameTwo</tabstop>
  <tabstop>checkBoxSnap</tabstop>
  <tabstop>doubleSpinBoxPosX</tabstop>
  <tabstop>checkBoxPosX</tabstop>
  <tabstop>doubleSpinBoxPosY</tabstop>
  <tabstop>checkBoxPosY</tabstop>
  <tabstop>doubleSpinBoxPosZ</tabstop>
  <tabstop>checkBoxPosZ</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
