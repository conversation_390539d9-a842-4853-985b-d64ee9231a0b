#include "AddNewStructureNodeDialog.h"
#include "core/WDTranslate.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "businessModule/design/WDBMDesign.h"
#include "core/WDCore.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/undoRedo/WDUndoStack.h"

AddNewStructureNodeDialog::AddNewStructureNodeDialog(WD::WDCore& core
	, QWidget *parent)
	: QDialog(parent), _core(core)
    , _nameHelpter(_core.getBMDesign())
{
	ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
	connect(ui.pushButtonCancel, &QPushButton::clicked, this, &AddNewStructureNodeDialog::slotCancelButtonClicked);
	connect(ui.pushButtonOk, &QPushButton::clicked, this, &AddNewStructureNodeDialog::slotOkButtonClicked);
	//翻译
	this->retranslateUi();
    //
    _nameHelpter.setLineEdit(ui.lineEditName);
}


AddNewStructureNodeDialog::~AddNewStructureNodeDialog()
{
}

void AddNewStructureNodeDialog::setStru(WD::WDNode::SharedPtr pStru)
{
    _pNodeStru = pStru;
}

void AddNewStructureNodeDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    _nameHelpter.resetName();
}

void AddNewStructureNodeDialog::hideEvent(QHideEvent* evt)
{
    ui.lineEditDescription->clear();
    WDUnused(evt);
}

void AddNewStructureNodeDialog::slotOkButtonClicked()
{
    // 校验是否存在相同名称
    if (_nameHelpter.exists())
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Same Name Exists!");
        return;
    }
    
    if (_nameHelpter.name() == "")
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Name is empty");
        return;
    }

    if (ui.lineEditDescription->text() == "")
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Description is empty");
        return;
    }
    createStructuresNode();
    this->accept();
}

void AddNewStructureNodeDialog::slotCancelButtonClicked()
{
	this->reject();
}

WD::WDNode::SharedPtr AddNewStructureNodeDialog::createDatum(const std::string& name)
{
    //创建对应的DATUM节点
    WD::WDNode::SharedPtr parent = _pNodeStru->parent();
    if (parent == nullptr)
    {
        return nullptr;
    }
    //  权限校验
    if (!_core.getBMDesign().permissionMgr().check(*parent))
    {
        return nullptr;
    }
    // 申领对象
    if (!_core.getBMDesign().claimMgr().checkAdd(parent, nullptr))
    {
        return nullptr;
    }
    auto newNode = _core.getBMDesign().create(parent, "DATUM", nullptr, name);
    if (newNode == nullptr)
    {
        return nullptr;
    }
    //撤销重做
    auto cmdCreatedNode = WD::WDBMBase::MakeCreatedCommand({ newNode });
    if (cmdCreatedNode != nullptr)
    {
        _core.undoStack().beginMarco("Create DATUM");
        _core.undoStack().push(cmdCreatedNode);
        _core.undoStack().endMarco();
    }
    return newNode;
}

WD::WDNode::SharedPtr AddNewStructureNodeDialog::createStructuresNode()
{
    if (_pNodeStru == nullptr)
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Parent Node Is Invalid!");
        return nullptr;
    }
    //  权限校验
    if (!_core.getBMDesign().permissionMgr().check(*_pNodeStru))
    {
        WD_WARN_T("AddNewStructureNodeDialog", "You cannot operate the current node!");
        return nullptr;
    }
    // 申领对象
    if (!_core.getBMDesign().claimMgr().checkAdd(_pNodeStru, nullptr))
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Check node failed");
        return nullptr;
    }
    auto pNewNode = _core.getBMDesign().create(_pNodeStru, "FRMW", nullptr, _nameHelpter.name());
    if (pNewNode == nullptr)
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Create node failed");
        return nullptr;
    }
    //撤销重做
    _core.undoStack().beginMarco("Create FRMW");
    auto pCmdCreatedNode = WD::WDBMBase::MakeCreatedCommand({ pNewNode });
    if (pCmdCreatedNode != nullptr)
    {       
        _core.undoStack().push(pCmdCreatedNode);        
    }
    // 设置属性
    pNewNode->setAttribute("Function", "System");
    pNewNode->setAttribute("Purpose",WD::WDBMWord("GRID"));
    pNewNode->setAttribute("Description", ui.lineEditDescription->text().toUtf8().data());
    _core.scene().add(pNewNode);
    auto& aabb = pNewNode->aabb();
    if (!aabb.isNull())
    {
        _core.viewer().lookAtAabb(aabb);
    }
    //创建关联的DATUM
    auto pDatum = createDatum(_nameHelpter.name() + "/DATUM");
    if (pDatum == nullptr)
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Creat DATUM failed");
    }
    else
    {
        auto pCmdCreatedDatum = WD::WDBMBase::MakeCreatedCommand({ pDatum });
        if (pCmdCreatedDatum != nullptr)
        {
            _core.undoStack().push(pCmdCreatedDatum);
        }
        _core.scene().add(pDatum);
    }
    _core.undoStack().endMarco();
    _core.needRepaint();
    emit sigNewNodeCreateSuccess(*pNewNode);
    return pNewNode;
}

void AddNewStructureNodeDialog::retranslateUi()
{
    Trs("AddNewStructureNodeDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonOk
        , ui.pushButtonCancel
        , ui.labelName
        , ui.labelDescription
	);
}