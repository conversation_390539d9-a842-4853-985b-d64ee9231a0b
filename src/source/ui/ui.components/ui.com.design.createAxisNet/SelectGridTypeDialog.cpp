#include "SelectGridTypeDialog.h"
#include "ui_SelectGridTypeDialog.h"
#include "CreateRectangularGridDialog.h"
#include "CreateStructureGridDialog.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../wizDesignerApp/UiInterface/ICollaboration.h"
//轴网类型key
static const char* GridTypes[2] = {"GT_ASSISTANT", "GT_STRUCTURE"};
SelectGridTypeDialog::SelectGridTypeDialog(IMainWindow& mainWindow) :
    QDialog(mainWindow.widget()),
    ui(new Ui::SelectGridTypeDialog),
    _mainWindow(mainWindow),
    _pDialogAssistant(nullptr),
    _pDialogStructure(nullptr)
{
    ui->setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    retranslateUi();
    ui->comboBoxType->addItem
    (
        QString::fromUtf8
        (
            WD::WDTs("SelectGridTypeDialog", GridTypes[HandleAxisNet::GridType::GT_ASSISTANT]).c_str()
        )
    );
    ui->comboBoxType->addItem
    (
        QString::fromUtf8
        (
            WD::WDTs("SelectGridTypeDialog", GridTypes[HandleAxisNet::GridType::GT_STRUCTURE]).c_str()
        )
    );
}

SelectGridTypeDialog::~SelectGridTypeDialog()
{
    delete ui;
}

/**
 * @brief 初始化特定类型的轴网对话框
 * @tparam Tdlg 对话框类型
 * @param pDialog 对话框实例
 * @param mainWindow 主窗口
*/
template<class Tdlg>
void Init_Dialog(Tdlg*& pDialog, IMainWindow& mainWindow)
{
    if (pDialog == nullptr)
    {
        pDialog = new Tdlg(mainWindow.core(), mainWindow.widget());
        pDialog->bIsAdmin = !mainWindow.collaboration().actived() || mainWindow.core().getBMDesign().permissionMgr().isAdmin() ||
            (mainWindow.core().getBMDesign().permissionMgr().role() == WD::WDBMPermissionMgr::U_SiteMamager);
    }
    pDialog->show();
}

void SelectGridTypeDialog::on_buttonBox_accepted()
{
    if (ui->comboBoxType->currentIndex() == HandleAxisNet::GridType::GT_ASSISTANT)
    {
        Init_Dialog(_pDialogAssistant, _mainWindow);
    }
    else
    {
        Init_Dialog(_pDialogStructure, _mainWindow);
    }
}

void SelectGridTypeDialog::retranslateUi()
{
    Trs("SelectGridTypeDialog"
        , static_cast<QDialog*>(this)
        , ui->labelType
    );
}
