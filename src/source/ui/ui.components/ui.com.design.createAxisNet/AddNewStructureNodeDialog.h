#pragma once

#include <QDialog>

#include "ui_AddNewStructureNodeDialog.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "nodeTree/WDNodeTree.h"
#include "../../ui.commonLibrary//ui.commonLib.custom/UiNodeNameHelpter.h"

class AddNewStructureNodeDialog : public QDialog
{
	Q_OBJECT

public:
    AddNewStructureNodeDialog(WD::WDCore& app
		, QWidget *parent = Q_NULLPTR);
	~AddNewStructureNodeDialog();

signals:
        /**
         * @brief 新节点创建成功
        */
        void sigNewNodeCreateSuccess(WD::WDNode& newNode);
public:
    /**
     * @brief 设置父节点
     * @param pStru
    */
    void setStru(WD::WDNode::SharedPtr pStru);
    /**
     * @brief 创建DATUM节点
    */
    WD::WDNode::SharedPtr createDatum(const std::string& name);

protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

private slots:
    /**
    * @brief 确定按键槽函数
    */
	void slotOkButtonClicked();
    /**
    * @brief 取消按键槽函数
    */
	void slotCancelButtonClicked();

private:
    /**
    * @brief 创建节点
    */
    WD::WDNode::SharedPtr createStructuresNode();
    /**
    * @brief 界面文本翻译
    */
    void retranslateUi();


private:
	Ui::AddNewStructureNodeDialog  ui;
    WD::WDCore& _core;
    // 节点名称助手
    UiNodeNameHelpter _nameHelpter;
    WD::WDNode::SharedPtr _pNodeStru;
};

