#include "UiComCreateAxisNet.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/viewer/WDViewer.h"
#include "core/scene/WDScene.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "SelectGridTypeDialog.h"
#include "../../wizDesignerApp/UiInterface/ICollaboration.h"

UiComCreateAxisNet::UiComCreateAxisNet(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
    , _pSelectGridTypeDialog(new SelectGridTypeDialog(mainWindow))
{
    //_pCreateRectangularGridDialog = new CreateRectangularGridDialog(mWindow().core(), mWindow().widget());
    //_pCreateRectangularGridDialog->bIsAdmin = !mWindow().isDCMode() || mWindow().core().getBMDesign().permissionMgr().isAdmin() || 
    //    (mWindow().core().getBMDesign().permissionMgr().role() == WD::WDBMPermissionMgr::U_SiteMamager);
}

UiComCreateAxisNet::~UiComCreateAxisNet()
{
    //if (_pCreateRectangularGridDialog != nullptr)
    //{
    //    delete _pCreateRectangularGridDialog;
    //    _pCreateRectangularGridDialog = nullptr;
    //}
    if (_pSelectGridTypeDialog != nullptr)
    {
        delete _pSelectGridTypeDialog;
        _pSelectGridTypeDialog = nullptr;
    }
}

void UiComCreateAxisNet::onNotice(UiNotice* pNotice) 
{
    int nType = pNotice->type();
    switch (nType)
    {
        case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            // 创建自定义辅助轴网
            if (pActionNotice->action().is("action.architectural.axisNet"))
            {
                if (_pSelectGridTypeDialog->isHidden())
                    _pSelectGridTypeDialog->show();
                else
                    _pSelectGridTypeDialog->activateWindow();
            }
        }
        break;
    case UiNoticeType::UNT_AllReady:
        {   
        }
        break;
    default:
        break;
    }
}
