#pragma once

#include    <QObject>
#include    "CreateRectangularGridDialog.h"
#include    "../../wizDesignerApp/UiInterface/UiInterface.h"
class SelectGridTypeDialog;

class UiComCreateAxisNet
	: public QObject
	, public IUiComponent
{
	Q_OBJECT

public:
	UiComCreateAxisNet(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent = nullptr);
    virtual ~UiComCreateAxisNet();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;

private:
    SelectGridTypeDialog* _pSelectGridTypeDialog;
	CreateRectangularGridDialog*     _pCreateRectangularGridDialog;
};

