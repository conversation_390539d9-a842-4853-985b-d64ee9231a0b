#pragma once

#include <QDialog>
#include "ui_AutofillRectangularGridDialog.h"

class AutofillRectangularGridDialog : public QDialog
{
	Q_OBJECT

public:
	AutofillRectangularGridDialog(QWidget *parent = Q_NULLPTR);
	~AutofillRectangularGridDialog();
	//自动填充结构体
	struct AutofillMsg
	{
        //标签名
		QString label;
        //编号
		std::vector<QString> lstIds;
        //偏移量
		std::vector<QString> lstOffset;
	};
private:
	/**
	 * @brief 自动生成编号的规则
	*/
	enum AutoCreateIDRules
	{
		// 数字
		Numeric = 0,
		// 字母
		Alphabetic, 
		// 升序
		Ascending,
		// 降序
		descending
	};
protected:
	virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
public:
	/**
	 * @brief 设置Z轴当前状态
	 * @param state 状态,ture 关闭Z轴 false 取消关闭Z轴
	*/
	inline void setZAxisEnabled(bool state)
	{
        ui.lineEditAxisZ->setEnabled(state);
        ui.spinBoxOffsetsZ->setEnabled(state);
        ui.lineEditFromZ->setEnabled(state);
        ui.spinBoxIDsZ->setEnabled(state);
        ui.spinBoxToZ->setEnabled(state);
        ui.spinBoxIntervalZ->setEnabled(state);
        ui.comboBoxIsAscendZ->setEnabled(state);
        ui.comboBoxIsNumberZ->setEnabled(state);
	}
signals:
	/**
	 * @brief 点击ok按钮后发送的信号
	 * @param lstAutoData 生成轴网的参数
	*/
	void sigAutofillData(const std::vector<AutofillMsg>& lstAutoData);
private slots:
	//确定按键槽函数
	void slotOkButtonClicked();
	//取消按键槽函数
	void slotCancelButtonClicked();
	//X轴下拉框选项改变槽函数
	void slotXComboxChanged(int index);
	//Y轴下拉框选项改变槽函数
	void slotYComboxChanged(int index);
	//Z轴下拉框选项改变槽函数
	void slotZComboxChanged(int index);
    // x轴起点编辑完成
    void slotQLineEditFromXEditingFinished();
    // y轴起点编辑完成
    void slotQLineEditFromYEditingFinished();
    // z轴起点编辑完成
    void slotQLineEditFromZEditingFinished();
private:
	//获取界面数据
	void getAutofillData();
	//打包编号为数字的数据
	void packageNumericData(int isAscend, const QStringList& lstData);
	//打包编号为字母的数据
	void packageAlphabeticData(int isAscend, const QStringList& lstData);
    //初始化界面
    void initDialog();
	//界面文本翻译
	void retranslateUi();
private:
	Ui::AutofillRectangularGridDialog  ui;
	std::vector<AutofillMsg> _lstAutoMsg;
    std::vector<QString> letters = {"A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"};
};

