#include "CreateStructureGridDialog.h"
#include "core/WDTranslate.h"
#include "core/scene/WDScene.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/message/WDMessage.h"
#include "core/math/DirectionParser.h"
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/undoRedo/WDUndoStack.h"
#include "AddNewStructureNodeDialog.h"
#include <queue>

int CreateStructureGridDialog::getFunctionIndex(std::string_view function) const
{
    for (int i = 0; i < ui.comboBox->count(); ++i)
    {
        if (function == ui.comboBox->itemData(i).toString().toUtf8().data())
        {
            return i;
        }
    }
    assert(false && "Bad Function");
    return 0;
}

void CreateStructureGridDialog::OpthionsEditableWidgets::clear()
{
    for (auto widget : lineEditors)
    {
        widget->clear();
    }
    for (auto widget : textEditors)
    {
        widget->clear();
    }
}

void CreateStructureGridDialog::OpthionsEditableWidgets::setEditable(bool able)
{
    for (auto widget : lineEditors)
    {
        widget->setEnabled(able);
    }
    for (auto widget : textEditors)
    {
        widget->setEnabled(able);
    }
    for (auto widget : pushButtons)
    {
        widget->setEnabled(able);
    }
    combox->setEnabled(able);
}

void CreateStructureGridDialog::PosOrieEditableWidgets::clear()
{
    for (auto widget : lineEditors)
    {
        widget->clear();
    }
    for (auto widget : spinners)
    {
        widget->clear();
    }
}

void CreateStructureGridDialog::PosOrieEditableWidgets::setEditable(bool able)
{
    for (auto widget : lineEditors)
    {
        widget->setEnabled(able);
    }
    for (auto widget : spinners)
    {
        widget->setEnabled(able);
    }
    for (auto widget : checkBoxes)
    {
        widget->setEnabled(able);
    }
}

void CreateStructureGridDialog::registWidget()
{
    //选项页控件
    _opWidgets.lineEditors = 
    {
        ui.lineEditName,
        ui.lineEditDescription,
        ui.lineEditLabelX,
        ui.lineEditLabelY,
        ui.lineEditLabelZ
    };
    _opWidgets.textEditors = 
    {
        ui.textEditIDsX,
        ui.textEditOffsetsX,
        ui.textEditIDsY,
        ui.textEditOffsetsY,
        ui.textEditIDsZ,
        ui.textEditOffsetsZ 
    };
    _opWidgets.pushButtons =
    {
        ui.pushButtonAutofill,
        ui.pushButtonSetView
    };
    _opWidgets.combox = ui.comboBox;
    //调整页控件
    _posWidgets.lineEditors =
    {
        ui.lineEditNameTwo,
        ui.lineEditYDir,
        ui.lineEditZdir,
    };
    _posWidgets.spinners =
    {
        ui.doubleSpinBoxPosX,
        ui.doubleSpinBoxPosY,
        ui.doubleSpinBoxPosZ
    };
    _posWidgets.checkBoxes =
    {
        ui.checkBoxSnap,
        ui.checkBoxPosX,
        ui.checkBoxPosY,
        ui.checkBoxPosZ
    };
    //frmw显示类型按钮
    _radioButtons[0] = ui.radioButtonNothing;
    _radioButtons[1] = ui.radioButtonKey;
    _radioButtons[2] = ui.radioButtonPosition;
    _radioButtons[3] = ui.radioButtonLKey;
}

CreateStructureGridDialog::CreateStructureGridDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _app(core)
    , _positionCaptureHelpter(_app)
    , _nameHelpter(_app.getBMDesign())
    , _nameHelpterX(_app.getBMDesign())
    , _nameHelpterY(_app.getBMDesign())
    , _nameHelpterZ(_app.getBMDesign())
    , _pAddNewStructureNodeDialog(new AddNewStructureNodeDialog(_app, this))
    , _pAutofillGridDialog(new AutofillRectangularGridDialog(parent))
{
    ui.setupUi(this);
    //设置radiobutton的类型
    ui.radioButtonNothing->setProperty("type", QVariant(WD::WDBMFGridUtils::FrmwShowType::FST_Nothing));
    ui.radioButtonKey->setProperty("type", QVariant(WD::WDBMFGridUtils::FrmwShowType::FST_Key));
    ui.radioButtonPosition->setProperty("type", QVariant(WD::WDBMFGridUtils::FrmwShowType::FST_Position));
    ui.radioButtonLKey->setProperty("type", QVariant(WD::WDBMFGridUtils::FrmwShowType::FST_LvlKey));
    //注册控件
    registWidget();
    //调整页控件
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 位置捕捉帮助
    _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CaptureTimes::CT_Repeat);
    auto param = _positionCaptureHelpter.captureParam();
    param.bShowResultCoord = true;
    _positionCaptureHelpter.setCaptureParam(param);

    this->initDialog();
    this->retranslateUi();
    ui.tabWidget->setCurrentIndex(0);
    ui.treeWidget->setColumnCount(2);
    ui.treeWidget->header()->setVisible(false);
    ui.treeWidget->header()->setSectionResizeMode(QHeaderView::ResizeMode::Stretch);
    //"自动填充"响应
    connect(ui.pushButtonAutofill, &QPushButton::clicked, this, &CreateStructureGridDialog::slotAutofillButtonClicked);
    //显示效果改变
    connect(ui.radioButtonNothing, &QPushButton::clicked, this, &CreateStructureGridDialog::slotChangeDisplaySet);
    connect(ui.radioButtonKey, &QPushButton::clicked, this, &CreateStructureGridDialog::slotChangeDisplaySet);
    connect(ui.radioButtonPosition, &QPushButton::clicked, this, &CreateStructureGridDialog::slotChangeDisplaySet);
    connect(ui.radioButtonLKey, &QPushButton::clicked, this, &CreateStructureGridDialog::slotChangeDisplaySet);
    //调整视图（居中）
    connect(ui.pushButtonSetView, &QPushButton::clicked, this, &CreateStructureGridDialog::slotSetViewClicked);
    //"应用"按钮的响应
    connect(ui.pushButtonSave, &QPushButton::clicked, this, &CreateStructureGridDialog::slotOkButtonClicked);
    //"关闭"按钮的响应
    connect(ui.pushButtonClose, &QPushButton::clicked, this, &CreateStructureGridDialog::slotCancelButtonClicked);
    //自动填充Ok按钮的响应
    connect(_pAutofillGridDialog, &AutofillRectangularGridDialog::sigAutofillData , this , &CreateStructureGridDialog::slotShowAutofillData);
    //点击"当前节点"
    connect(ui.pushButtonCurrentNode, &QPushButton::clicked, this, &CreateStructureGridDialog::slotCurrentNodeClicked);
    //点击"创建"
    connect(ui.pushButtonCreate, &QPushButton::clicked, this, &CreateStructureGridDialog::slotCreate);
    //点击"复制"的响应
    connect(ui.pushButtonCopy, &QPushButton::clicked, this, &CreateStructureGridDialog::slotCopy);
    //新建FRMW
    connect(_pAddNewStructureNodeDialog, &AddNewStructureNodeDialog::sigNewNodeCreateSuccess, this, &CreateStructureGridDialog::slotNewNodeCreated);
    //选中Item后的响应
    connect(ui.treeWidget, &QTreeWidget::itemClicked, this, &CreateStructureGridDialog::slotSelectTreeWidget);
    //删除按钮的响应
    connect(ui.pushButtonDelete, &QPushButton::clicked, this, &CreateStructureGridDialog::slotPushButtonDeleteClicked);
    //轴网显示CheckBox的勾选响应
    connect(ui.checkBoxAxisShowOrHide, &QCheckBox::stateChanged, this, &CreateStructureGridDialog::slotShowHide);
    //捕获
    _positionCaptureHelpter.setCheckBoxCapture(ui.checkBoxSnap);
    _positionCaptureHelpter.setCheckBoxXYZ(ui.checkBoxPosX, ui.checkBoxPosY, ui.checkBoxPosZ);
    _positionCaptureHelpter.setDoubleSpinBoxXYZ(ui.doubleSpinBoxPosX, ui.doubleSpinBoxPosY, ui.doubleSpinBoxPosZ);
    //
    _nameHelpter.setLineEdit(ui.lineEditName);
    _nameHelpterX.setLineEdit(ui.lineEditLabelX);
    _nameHelpterY.setLineEdit(ui.lineEditLabelY);
    _nameHelpterZ.setLineEdit(ui.lineEditLabelZ);
}
CreateStructureGridDialog::~CreateStructureGridDialog()
{
    if (_pAutofillGridDialog != nullptr)
    {
        delete _pAutofillGridDialog;
        _pAutofillGridDialog = nullptr;
    }   
}

/**
 * @brief 是否为FRMW轴网节点
 * @param node
 * @return
*/
inline bool IsFRMW(WD::WDNode& node)
{
    return node.isType("FRMW") && node.getAttribute("Purpose").toWord() == WD::WDBMWord("GRID");
}

/**
 * @brief 获取关联的DATUM节点
 * @param node 
 * @return 
*/
WD::WDNode::SharedPtr GetDatum(WD::WDNode& node)
{
    if (!IsFRMW(node))
    {
        return nullptr;
    }
    WD::WDNode::SharedPtr parent = node.parent();
    if (parent == nullptr || parent->type() != "STRU")
    {
        return nullptr;
    }
    WD::WDNode::SharedPtr grandParent = parent->parent();
    if (grandParent == nullptr)
    {
        return nullptr;
    }
    for (auto& pChild : grandParent->children())
    {
        if (pChild == nullptr || pChild->type() != "DATUM")
        {
            continue;
        }

        if (pChild->name() == (node.name() + "/DATUM"))
        {
            return pChild;
        }
    }
    return nullptr;
}

WD::WDNode::SharedPtr CreateStructureGridDialog::getSeletedNode() const
{
    QTreeWidgetItem* pSelected = nullptr;
    for (int i = 0; i < ui.treeWidget->topLevelItemCount(); ++i)
    {
        if (ui.treeWidget->topLevelItem(i)->isSelected())
        {
            pSelected = ui.treeWidget->topLevelItem(i);
            break;
        }
    }
    
    if (pSelected == nullptr)
    {
        return nullptr;
    }
    return WD::WDNode::SharedCast(pSelected->data(0, Qt::UserRole).value<UiWeakObject>().object());
}

QTreeWidgetItem* CreateStructureGridDialog::getTreeItemByNode(WD::WDNode::SharedPtr pNode) const
{
    if (pNode == nullptr)
    {
        return nullptr;
    }

    for (int i = 0; i < ui.treeWidget->topLevelItemCount(); ++i)
    {
        QTreeWidgetItem* pItem = ui.treeWidget->topLevelItem(i);
        WD::WDNode::SharedPtr pCurNode = WD::WDNode::SharedCast(pItem->data(0, Qt::UserRole).value<UiWeakObject>().object());
        if (pNode == pCurNode)
        {
            return pItem;
        }
    }
    return nullptr;
}

void CreateStructureGridDialog::slotNewNodeCreated(WD::WDNode& newNode)
{
    refreshFrmwNodes(WD::WDNode::ToShared(&newNode));
}

void CreateStructureGridDialog::slotSelectTreeWidget(QTreeWidgetItem* pItem, int)
{    
    WD::WDNode::SharedPtr pCurNode = WD::WDNode::SharedCast(pItem->data(0, Qt::UserRole).value<UiWeakObject>().object());
    if (pCurNode == nullptr)
    {
        return;
    }
    //结构树同步选中
    _app.nodeTree().setCurrentNode(pCurNode);
    _app.needRepaint();
    //点击已经选中的项，不做任何操作
    if (pCurNode == _pLastModifiedNode)
    {
        return;
    }

    if (!isModified())
    {
        //读取当前编辑节点的参数显示到界面
        showCurrentGridData();
        return;
    }
    //页面数据已经修改,是否切换
    std::string text = WD::WDTs("CreateStructureGridDialog", "Do you want to apply the modified data before switch");
    std::string title = WD::WDTs("CreateStructureGridDialog", "data modified");
    std::string button0 = WD::WDTs("CreateStructureGridDialog", "apply");
    std::string button1 = WD::WDTs("CreateStructureGridDialog", "ignore");
    std::string button2 = WD::WDTs("CreateStructureGridDialog", "cancel");
    int re = WD::Core().message().question(text, title, button0, button1, button2);
    if (re == 2)//取消切换
    {
        ui.treeWidget->clearSelection();
        QTreeWidgetItem* pTreeItem = getTreeItemByNode(_pLastModifiedNode);
        if (pTreeItem != nullptr)
        {
            pTreeItem->setSelected(true);//切换为原本的
        }
    }
    else if(re == 1)//忽略修改
    {
        //读取当前编辑节点的参数显示到界面
        showCurrentGridData();
    }
    else//应用并继续
    {
        if (editSingleFRMW(*_pLastModifiedNode))
        {
            //刷新结构目录树
            refreshFrmwNodes(pCurNode);
        }        
    }
}

void CreateStructureGridDialog::slotShowHide(int bShow)
{
    std::vector<WD::WDNode::SharedPtr> oSelected = getCheckedFrmwNode();
    for (auto& frmw : oSelected)
    {
        if (bShow)
        {
            _app.scene().add(frmw);
        }
        else
        {
            _app.scene().remove(frmw);
        }
    }
    _app.needRepaint();
}

void CreateStructureGridDialog::slotCreate()
{
    //校验当前节点是否支持创建FRMW
    if (_pCurrentNode == nullptr)
    {
        WD_WARN_T("CreateStructureGridDialog", "Current Node Is Null");
        return;
    }
    if (_pCurrentNode->type() != "STRU")
    {
        WD_WARN_T("CreateStructureGridDialog", "Current node type is not STRU");
        return;
    }
    //打开创建界面
    _pAddNewStructureNodeDialog->setStru(_pCurrentNode);
    _pAddNewStructureNodeDialog->show();
}

void CreateStructureGridDialog::slotCopy()
{
    //校验当前节点是否支持创建FRMW
    if (_pCurrentNode == nullptr)
    {
        WD_WARN_T("CreateStructureGridDialog", "Current Node Is Null");
        return;
    }
    if (_pCurrentNode->type() != "STRU")
    {
        WD_WARN_T("CreateStructureGridDialog", "Current node type is not STRU");
        return;
    }
    WD::WDNode::SharedPtr pSrcNode = getSeletedNode();
    if (pSrcNode == nullptr)
    {
        return;
    }
    //打开创建界面
    WD::WDNode::SharedPtr pCopyNode = _app.currentBM()->clone(pSrcNode);
    if (pCopyNode == nullptr)
    {
        WD_WARN_T("CreateStructureGridDialog", "Copy node failed");
        return;
    }
    WD::WDNode::Nodes copyedNodes = {pCopyNode};
    pCopyNode->setName("Copy-of-" + pSrcNode->name());
    pCopyNode->setParent(_pCurrentNode);
    //复制对应的DATUM
    WD::WDNode::SharedPtr pDatum = GetDatum(*pSrcNode);
    if (pDatum != nullptr)
    {
        WD::WDNode::SharedPtr pCopyDatum = _app.currentBM()->clone(pDatum);
        if (pCopyDatum != nullptr)
        {
            copyedNodes.emplace_back(pCopyDatum);
            pCopyDatum->setName(pCopyNode->name() + "/DATUM");
            pCopyDatum->setParent(pDatum->parent());
        }
    }
    //撤销重做
    auto pCmdCopy = WD::WDBMBase::MakeCreatedCommand(copyedNodes);
    if (pCmdCopy != nullptr)
    {
        _app.undoStack().beginMarco("Copy FRMW Grid");
        _app.undoStack().push(pCmdCopy);
        _app.undoStack().endMarco();
    }
    //刷新为新建节点
    refreshFrmwNodes(pCopyNode);
}

void CreateStructureGridDialog::slotCurrentNodeClicked()
{
    auto pNode = _app.nodeTree().currentNode();
    if (pNode == nullptr)
    {
        WD_WARN_T("CreateStructureGridDialog", "Select One Node");
        return;
    }
    //获取子FRMW
    std::vector<WD::WDNode::SharedPtr> frmwNodes = listSubFrmw(*pNode);
    if (!frmwNodes.empty())
    {
        _pCurrentNode = pNode;
    }
    else//没有则获取父节点
    {
        _pCurrentNode = getAncestorStru(*pNode);
    }
    _pAddNewStructureNodeDialog->setStru(_pCurrentNode);
    //刷新Lable
    std::string name = _pCurrentNode != nullptr ? _pCurrentNode->name() : "";
    ui.lineEditCurrentNode->setText(QString::fromUtf8(name.c_str()));
    ui.lineEditCurrentNode->setCursorPosition(0);
    //刷新结构树
    refreshFrmwNodes(nullptr);
}

void CreateStructureGridDialog::showEvent(QShowEvent*)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
}

void CreateStructureGridDialog::hideEvent(QHideEvent*)
{
    _positionCaptureHelpter.exit(true);
    clearWidgetData();
    _showType = WD::WDBMFGridUtils::FrmwShowType::FST_Nothing;
    _pLastModifiedNode = nullptr;
    _pCurrentNode = nullptr;
    ui.treeWidget->clear();
    ui.lineEditCurrentNode->setText("");
}

void CreateStructureGridDialog::slotOkButtonClicked()
{
    if (!isModified())
    {
        return;
    }
    // 仅系统/项目管理员/专业组管理员才能创建轴网
    if (!bIsAdmin)
    {
        WD_WARN_T("CreateStructureGridDialog", "Only admin can create GRIDSY");
        return;
    }
    WD::WDNode::SharedPtr pNode = getSeletedNode();
    if (pNode == nullptr)
    {
        return;
    }

    if (editSingleFRMW(*pNode))
    {
        //刷新结构目录树,选中编辑节点
        refreshFrmwNodes(pNode);
    }    
}

void CreateStructureGridDialog::slotCancelButtonClicked()
{
    this->reject();
}

void CreateStructureGridDialog::slotAutofillButtonClicked()
{
    if (_pAutofillGridDialog->isHidden())
    {
       _pAutofillGridDialog->show();
    }
    else
    {
       _pAutofillGridDialog->activateWindow();
    }
}

WD::WDBMFGridUtils::FrmwShowType CreateStructureGridDialog::getDisplayType(QRadioButton& radioButton) const
{
    QVariant type = radioButton.property("type");
    return WD::WDBMFGridUtils::FrmwShowType(type.value<int>());
}

QRadioButton* CreateStructureGridDialog::getRadioButtonByType(WD::WDBMFGridUtils::FrmwShowType type) const
{
    return _radioButtons[type];
}

void CreateStructureGridDialog::slotChangeDisplaySet()
{
    //获取勾选的项
    WD::WDNode::Nodes checkedNodes = getCheckedFrmwNode();
    //获取选中的项
    WD::WDNode::SharedPtr pSelectedNode = getSeletedNode();
    if (std::find(checkedNodes.begin(), checkedNodes.end(), pSelectedNode) == checkedNodes.end())
    {
        checkedNodes.emplace_back(pSelectedNode);
    }
    if (checkedNodes.size() == 0)
    {
        return;
    }
    QRadioButton* pRadioButton = dynamic_cast<QRadioButton*>(QObject::sender());
    if (pRadioButton == nullptr)
    {
        return;
    }
    WD::WDBMFGridUtils::FrmwShowInfos infos;
    _showType = getDisplayType(*pRadioButton);
    //设置显示类型
    for (auto& frmw : checkedNodes)
    {
        if (frmw == nullptr)
        {
            continue;
        }
        infos.emplace_back(*frmw, _showType);
    }
    WD::WDBMFGridUtils::SetFrmwShowType(_app, infos);
}

void CreateStructureGridDialog::slotSetViewClicked()
{
    WD::WDNode::SharedPtr pNode = getSeletedNode();
    if (pNode == nullptr)
    {
        return;
    }
        
    auto& aabb = pNode->aabb();
    if (!aabb.isNull())
    {
        _app.viewer().lookAtAabb(aabb);
    }        
}

void CreateStructureGridDialog::slotShowAutofillData(const std::vector<AutofillRectangularGridDialog::AutofillMsg>& lstAutoData)
{
    if (lstAutoData.size() != 3)
    {
        WD_WARN_T("CreateStructureGridDialog", "InComplete Data To Fill");
        return;
    }
    typedef std::tuple<UiNodeNameHelpter&, QTextEdit*, QTextEdit*> tdata;
    tdata arr[3] = {
        {_nameHelpterX, ui.textEditIDsX, ui.textEditOffsetsX},
        {_nameHelpterY, ui.textEditIDsY, ui.textEditOffsetsY}, 
        {_nameHelpterZ, ui.textEditIDsZ, ui.textEditOffsetsZ}};

    for (int i = 0; i < 3; ++i)
    {
        tdata& widgets = arr[i];
        const AutofillRectangularGridDialog::AutofillMsg& msg = lstAutoData[i];
        std::get<0>(widgets).setName(msg.label);
        std::get<1>(widgets)->clear();
        for (int j = 0; j < msg.lstIds.size(); ++j)
        {
            std::get<1>(widgets)->append(msg.lstIds[j]);
        }
        std::get<2>(widgets)->clear();
        for (int j = 0; j < msg.lstOffset.size(); ++j)
        {
            std::get<2>(widgets)->append(msg.lstOffset[j]);
        }
    }
}

void CreateStructureGridDialog::setPosition(const WD::DVec3& position)
{
    _positionCaptureHelpter.setPosition(position);
}

WD::DVec3 CreateStructureGridDialog::getPosition() const
{
    return _positionCaptureHelpter.position();
}

void CreateStructureGridDialog::setYZXDirection(const WD::TVec3<double>& directionY, const WD::TVec3<double>& directionZ)
{
    std::string yIs = WD::DDirectionParserENU::OutputStringByDirection(directionY);
    std::string zIs = WD::DDirectionParserENU::OutputStringByDirection(directionZ);
    std::string xIs = "X is " + WD::DDirectionParserENU::OutputStringByDirection(WD::DVec3::Cross(directionY, directionZ));
    ui.lineEditYDir->setText(QString::fromUtf8(yIs.c_str()));
    ui.lineEditZdir->setText(QString::fromUtf8(zIs.c_str()));
    ui.labelXdir->setText(QString::fromUtf8(xIs.c_str()));
}

bool CreateStructureGridDialog::getDirections(WD::TVec3<double>& directionX, WD::TVec3<double>& directionY, WD::TVec3<double>& directionZ) const
{
    std::vector<std::string> canNotParse;
    if (!WD::DDirectionParserENU::Direction(ui.lineEditYDir->text().toUtf8().data(), directionY))
    {
        canNotParse.push_back(ui.lineEditYDir->text().toUtf8().data());
    }
    if (!WD::DDirectionParserENU::Direction(ui.lineEditZdir->text().toUtf8().data(), directionZ))
    {
        canNotParse.push_back(ui.lineEditZdir->text().toUtf8().data());
    }
    if (canNotParse.size() != 0)
    {
        std::string msg;
        for (size_t i = 0; i < canNotParse.size(); i++)
        {
            msg += canNotParse[i];
            if (i != canNotParse.size() - 1)
            {
                msg += ",";
            }
        }
        msg += WD::WDTs("CreateStructureGridDialog", "Can Not Parse Direction");
        WD_WARN(msg);
        return false;
    }
    directionX = WD::DVec3::Cross(directionY, directionZ);
    return true;
}

/**
 * @brief 获取排列信息
 * @param origin 
 * @param oXLines 
 * @param xData 
*/
static void GetArrangeInfo(const WD::DVec3& origin, const std::vector<WD::WDNode::SharedPtr>& oXLines, std::vector<std::pair<size_t, double>>& xData)
{
    for (size_t i = 0; i < oXLines.size(); ++i)
    {
        auto& xline = oXLines[i];
        int num = xline->getAttribute("Number").toInt();
        WD::DVec3 startPnt = xline->getAttribute("Posstart WRT World").toDVec3();
        xData.push_back(std::make_pair(num, WD::DVec3::Distance(origin, startPnt)));
    }
}

void CreateStructureGridDialog::showGridData(WD::WDNode& node)
{
    clearWidgetData();
    _pLastModifiedNode = WD::WDNode::ToShared(&node);
    //获取基点信息
    WD::WDNode::SharedPtr pDatum = GetDatum(node);
    WD::DVec3 origin = WD::DVec3::Zero();
    if (pDatum != nullptr)
    {
        origin = pDatum->getAttribute("Position").toDVec3();
    }    
    setPosition(origin);
    WD::WDBMFGridUtils::FrmwShowType showType = WD::WDBMFGridUtils::GetFrmwShowType(node);
    _radioButtons[showType]->setChecked(true);
    //显示名称
    ui.lineEditName->setText(QString::fromUtf8( node.name().c_str()));
    ui.lineEditNameTwo->setText(QString::fromUtf8(node.name().c_str()));
    //显示功能
    ui.comboBox->setCurrentIndex(getFunctionIndex(node.getAttribute("Function").toString()));
    //显示描述
    ui.lineEditDescription->setText(QString::fromUtf8(node.getAttribute("DESC").toString().c_str()));
    //获取各层轴网SBFR
    std::vector<WD::WDNode::SharedPtr> oSbfrs;
    for (auto& child : node.children())
    {
        if (child == nullptr)
        {
            continue;
        }
        if (child->type() == "SBFR" &&
            child->getAttribute("Function").toString() == "LEVEL" &&
            child->getAttribute("Purpose").toWord() == WD::WDBMWord("GRID"))
        {
            oSbfrs.push_back(child);
        }
    }
    //获取每层的轴线信息
    std::vector<WD::DVec3> origins;
    std::unique_ptr<WD::DVec3> dirY;
    std::vector<std::pair<size_t, double>> xData, yData, zData;
    for (size_t i = 0; i < oSbfrs.size(); ++i)
    {
        auto& sbfr = oSbfrs[i];
        std::vector<WD::WDNode::SharedPtr> oXLines, oYLines;
        for (auto& child : sbfr->children())
        {
            if (child == nullptr)
            {
                continue;
            }
            if (child->type() != "SCTN")
            {
                continue;
            }
            if (child->getAttribute("Gtype").toWord() == WD::WDBMWord("XGRD"))
            {
                oXLines.push_back(child);
            }
            else if (child->getAttribute("Gtype").toWord() == WD::WDBMWord("YGRD"))
            {
                oYLines.push_back(child);
            }
        }
        if (oXLines.size() == 0 || oYLines.size() == 0)
        {
            continue;
        }
        //获取第一个X方向排列的轴线
        const int* number = oXLines[0]->getAttribute("Number").data<int>();
        if (number == nullptr || *number != 1)
        {
            continue;
        }
        //获取起点终点坐标
        const WD::DVec3* start = oXLines[0]->getAttribute("Posstart WRT World").data<WD::DVec3>();
        if (start == nullptr)
        {
            continue;
        }
        origins.push_back(*start);
        const WD::DVec3* end = oXLines[0]->getAttribute("Posend WRT World").data<WD::DVec3>();
        if (end == nullptr)
        {
            continue;
        }
        //计算Y方向
        if (dirY == nullptr)
        {
            dirY = std::make_unique<WD::DVec3>(*end - *start);
        }
        //获取第一层的X,Y方向排列信息
        if (i == 0)
        {
            GetArrangeInfo(*start, oXLines, xData);
            GetArrangeInfo(*start, oYLines, yData);
        }
    }
    if (origins.size() > 2 && dirY != nullptr)
    {
        setYZXDirection(*dirY, origins[1] - origins[0]);
    }
    //高程
    for (size_t i = 0; i < origins.size(); i++)
    {
        zData.push_back(std::make_pair(i + 1, WD::DVec3::Distance(origin, origins[i])));
    }
    auto showFunc = [](QTextEdit& idWidget, QTextEdit& offsetWidget, const std::vector<std::pair<size_t, double>>& offsetData)
    {
        idWidget.clear();
        offsetWidget.clear();
        for (size_t i = 0; i < offsetData.size(); i++)
        {
            idWidget.append(QString::number(offsetData[i].first));
            offsetWidget.append(QString::number(offsetData[i].second));
        }
    };
    showFunc(*ui.textEditIDsX, *ui.textEditOffsetsX, xData);
    showFunc(*ui.textEditIDsY, *ui.textEditOffsetsY, yData);
    showFunc(*ui.textEditIDsZ, *ui.textEditOffsetsZ, zData);
    //记录界面数据
    _lastGirdInfo = getCurrentGridInfo();
}

void CreateStructureGridDialog::showCurrentGridData()
{
    WD::WDNode::SharedPtr pCurrentEditNode = getSeletedNode();
    if (pCurrentEditNode == nullptr)
    {
        return;
    }
    showGridData(*pCurrentEditNode);
}

/**
 * @brief 从node的所有子节点中获取特定类型的节点
 * @param node 输入节点
 * @param strategy4child 筛选子节点的策略
 * @param oNodes 输出
*/
void GetSpecificNodes(WD::WDNode::SharedPtr node, 
    const std::function<bool(WD::WDNode::SharedPtr)>& strategy4child,
    std::vector<WD::WDNode::SharedPtr>& oNodes)
{
    if (node == nullptr)
    {
        return;
    }
    std::queue<WD::WDNode::SharedPtr> que;
    for (auto& child : node->children())
    {
        que.push(child);
    }
    while (!que.empty())
    {
        WD::WDNode::SharedPtr frontNode = que.front();
        if (strategy4child(frontNode))
        {
            oNodes.push_back(frontNode);
        }
        for (auto& child : frontNode->children())
        {
            que.push(child);
        }
        que.pop();
    }
}

void CreateStructureGridDialog::clearOptions()
{
    _opWidgets.clear();
    ui.lineEditLabelX->setText("X");
    ui.lineEditLabelY->setText("Y");
    ui.lineEditLabelZ->setText("EL");
}

void CreateStructureGridDialog::clearPositionOrietation()
{
    _posWidgets.clear();
    _positionCaptureHelpter.setPosition(WD::DVec3::Zero());
    ui.lineEditYDir->setText("N");
    ui.lineEditZdir->setText("U");
    ui.labelXdir->setText("X is E");
}

void CreateStructureGridDialog::ableOptions(bool able)
{
    _opWidgets.setEditable(able);
}

void CreateStructureGridDialog::ablePositionOrietation(bool able)
{
    _posWidgets.setEditable(able);
}


void CreateStructureGridDialog::refreshFrmwNodes(WD::WDNode::SharedPtr node2Select)
{
    std::vector<WD::WDNode::SharedPtr> nodes;
    if (_pCurrentNode != nullptr)
    {
        nodes = listSubFrmw(*_pCurrentNode);
    }
    showFrmwNodes(nodes, node2Select);
}

void CreateStructureGridDialog::slotTreeItemChecked(int state)
{
    WDUnused(state);
    WD::WDNode::Nodes nodes = getCheckedFrmwNode();
    bool enable = (nodes.size() == 0);
    ablePositionOrietation(enable);
    ableOptions(enable);
    ui.pushButtonSave->setEnabled(enable);
}

void CreateStructureGridDialog::showFrmwNodes(const std::vector<WD::WDNode::SharedPtr>& oNodes, WD::WDNode::SharedPtr node2Select)
{
    clearWidgetData();
    ui.treeWidget->clear();
    ablePositionOrietation(true);
    ableOptions(true);
    ui.pushButtonSave->setEnabled(true);
    //显示FRMW的DESC和Function属性到结构树上
    for (auto& frmw : oNodes)
    {
        if (frmw == nullptr)
        {
            continue;
        }
        auto item = new QTreeWidgetItem();
        item->setText(0, QString::fromUtf8(frmw->getAttribute("DESC").toString().c_str()));
        QVariant topUserData;
        topUserData.setValue(UiWeakObject(frmw));
        item->setData(0, Qt::UserRole, topUserData);
        ui.treeWidget->addTopLevelItem(item);
        auto container = new QWidget(ui.treeWidget);
        auto layout = new QHBoxLayout(container);
        layout->setContentsMargins(2, 0, 2, 0);
        std::string frmwFunction = frmw->getAttribute("Function").toString();
        auto label = new QLabel(WD::WDTs("CreateStructureGridDialog", frmwFunction).c_str());
        auto checkBox = new QCheckBox(ui.treeWidget);
        layout->addWidget(label);
        layout->addWidget(checkBox);
        layout->addStretch();
        ui.treeWidget->setItemWidget(item, 1, container);
        if (frmw == node2Select)
        {
            item->setSelected(true);
            //读取当前编辑节点的参数显示到界面
            showGridData(*frmw);
        }
        connect(checkBox, &QCheckBox::stateChanged, this, &CreateStructureGridDialog::slotTreeItemChecked);
    }
    _pLastModifiedNode = node2Select;
}

WD::WDNode::SharedPtr CreateStructureGridDialog::getAncestorStru(WD::WDNode& node) const
{
    WD::WDNode::SharedPtr pNode = WD::WDNode::ToShared(&node);
    while (pNode != nullptr)
    {
        if (pNode->type() == "STRU")
        {
            return pNode;
        }
        pNode = pNode->parent();
    }
    return nullptr;
}

std::vector<WD::WDNode::SharedPtr> CreateStructureGridDialog::listSubFrmw(WD::WDNode& node) const
{
    //获取类型为FRMW，Purpose属性为GRID的节点    
    std::vector<WD::WDNode::SharedPtr> oNodes;
    GetSpecificNodes(WD::WDNode::ToShared(&node),
        [](WD::WDNode::SharedPtr node)
        {
            return node != nullptr && IsFRMW(*node);
        }, oNodes);
    return oNodes;
}

void CreateStructureGridDialog::slotPushButtonDeleteClicked()
{
    //获取选中的节点
    WD::WDNode::Nodes nodes2Delete = getCheckedFrmwNode();
    if (nodes2Delete.empty())
    {
        WD_WARN_T("CreateStructureGridDialog", "Check nodes to delete");
        return;
    }
    if (WD_QUESTION_T("CreateStructureGridDialog", "isDel") != 0)
    {
        return;
    }
    //获取节点对应的datum
    WD::WDNode::Nodes datums;
    datums.reserve(nodes2Delete.size());
    for (auto& pNode : nodes2Delete)
    {
        if (pNode == nullptr)
        {
            continue;
        }
        datums.emplace_back(GetDatum(*pNode));
    }
    nodes2Delete.insert(nodes2Delete.end(), datums.begin(), datums.end());
    DeleteGivenNodes(nodes2Delete, _app, "CreateStructureGridDialog", false);
    //刷新结构树
    refreshFrmwNodes(nullptr);
    clearWidgetData();
}


std::vector<WD::WDNode::SharedPtr> CreateStructureGridDialog::getCheckedFrmwNode() const
{
    std::vector<WD::WDNode::SharedPtr> oSelected;
    for (int i = 0; i < ui.treeWidget->topLevelItemCount(); ++i)
    {
        auto item = ui.treeWidget->topLevelItem(i);
        auto widget = ui.treeWidget->itemWidget(item, 1);
        if (widget == nullptr)
        {
            continue;
        }
        //判断是否被勾选
        bool itemChecked = false;
        for (auto child : widget->children())
        {
            auto checkBox = dynamic_cast<QCheckBox*>(child);
            if (checkBox == nullptr)
            {
                continue;
            }
            if (checkBox->isChecked())
            {
                itemChecked = true;
                break;
            }
        }
        if (!itemChecked)
        {
            continue;
        }
        WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(item->data(0, Qt::UserRole).value<UiWeakObject>().object());
        oSelected.push_back(pNode);
    }
    return oSelected;
}

CreateStructureGridDialog::AxisNetMsg CreateStructureGridDialog::getAxisNetNodes() const
{
    // 获取界面X、Y轴上编号与偏移量的值
    QStringList lstAxisIdsX = ui.textEditIDsX->toPlainText().split("\n");
    QStringList lstOffsetsX = ui.textEditOffsetsX->toPlainText().split("\n");
    QStringList lstAxisIdsY = ui.textEditIDsY->toPlainText().split("\n");
    QStringList lstOffsetsY = ui.textEditOffsetsY->toPlainText().split("\n");
    QStringList lstAxisIdsZ = ui.textEditIDsZ->toPlainText().split("\n");
    QStringList lstOffsetsZ = ui.textEditOffsetsZ->toPlainText().split("\n");
    lstAxisIdsX.removeAll(QString(""));
    lstOffsetsX.removeAll(QString(""));
    lstAxisIdsY.removeAll(QString(""));
    lstOffsetsY.removeAll(QString(""));
    lstAxisIdsZ.removeAll(QString(""));
    lstOffsetsZ.removeAll(QString(""));
    // 判断偏移量与编号个数是否相等
    if (lstAxisIdsX.size() != lstOffsetsX.size() || lstAxisIdsY.size() != lstOffsetsY.size() || lstAxisIdsZ.size() != lstOffsetsZ.size())
    {
        //WD_WARN_T("CreateStructureGridDialog", str);
        return AxisNetMsg();
    }
    //判断编号是否有重复
    HandleAxisNet& handleAxisNet = const_cast<HandleAxisNet&>(_handleAxisNet);
    if (handleAxisNet.judgeIsRepetition(lstAxisIdsX)
        || handleAxisNet.judgeIsRepetition(lstAxisIdsY) || handleAxisNet.judgeIsRepetition(lstAxisIdsZ))
    {
       // WD_WARN_T("CreateStructureGridDialog", str);
        return AxisNetMsg();
    }

    // X轴信息<偏移量, 名称>
    std::map<double, std::string> mapXGRINLN;
    bool bOk = false;
    for (int i = 0; i < lstAxisIdsX.size(); ++i)
    {
        double offset = lstOffsetsX.at(i).toDouble(&bOk);
        // 数据重复, 偏移量为以及数据转换失败为错误情况
        if (mapXGRINLN.find(offset) != mapXGRINLN.end() || offset < 0.0 || !bOk)
        {
            //WD_WARN_T("CreateStructureGridDialog", str);
            return AxisNetMsg();
        }
        mapXGRINLN.emplace(offset, lstAxisIdsX.at(i).toUtf8().toStdString());
    }
    HandleAxisNet::CreateAxisNetMsg axisX;
    axisX.strAxisName = _nameHelpter.name();
    axisX.strLabel = _nameHelpterX.name();
    axisX.strAxis = "X";
    axisX.mapIdOffset = mapXGRINLN;

    // Y轴信息<偏移量, 名称>
    std::map<double, std::string> mapYGRINLN;
    for (int j = 0; j < lstAxisIdsY.size(); ++j)
    {
        double offset = lstOffsetsY.at(j).toDouble(&bOk);
        // 数据重复, 偏移量为以及数据转换失败为错误情况
        if (mapYGRINLN.find(offset) != mapYGRINLN.end() || offset < 0.0 || !bOk)
        {
            //WD_WARN_T("CreateStructureGridDialog", str);
            return AxisNetMsg();
        }
        mapYGRINLN.emplace(offset, lstAxisIdsY.at(j).toUtf8().toStdString());
    }
    HandleAxisNet::CreateAxisNetMsg axisY;
    axisY.strAxisName = _nameHelpter.name();
    axisY.strLabel = _nameHelpterY.name();
    axisY.strAxis = "Y";
    axisY.mapIdOffset = mapYGRINLN;

    AxisNetMsg createMsg;
    createMsg.emplace_back(axisX);
    createMsg.emplace_back(axisY);

    // Z轴信息<偏移量, 名称>
    std::map<double, std::string> mapZGRINLN;
    for (int i = 0; i < lstAxisIdsZ.size(); ++i)
    {
        double offset = lstOffsetsZ.at(i).toDouble(&bOk);
        // 数据重复, 偏移量为以及数据转换失败为错误情况
        if (mapZGRINLN.find(offset) != mapZGRINLN.end() || offset < 0.0 || !bOk)
        {
            //WD_WARN_T("CreateStructureGridDialog", str);
            return AxisNetMsg();
        }
        mapZGRINLN.emplace(offset, lstAxisIdsZ.at(i).toUtf8().toStdString());
    }
    HandleAxisNet::CreateAxisNetMsg axisZ;
    axisZ.strAxisName = _nameHelpter.name();
    axisZ.strLabel = _nameHelpterZ.name();
    axisZ.strAxis = "Z";
    axisZ.mapIdOffset = mapZGRINLN;
    createMsg.emplace_back(axisZ);
    
    return createMsg;
}

void CreateStructureGridDialog::clearWidgetData()
{
    clearOptions();
    clearPositionOrietation();
    ui.comboBox->setCurrentIndex(0);
    _lastGirdInfo = FrmwGridInfo();
}

WD::WDNode::SharedPtr CreateStructureGridDialog::createNode(WD::WDNode& parent, const std::string& name, const std::string& type)
{
    WD::WDNode::SharedPtr pParent = parent.toPtr<WD::WDNode>();
    //  权限校验
    if (!_app.getBMDesign().permissionMgr().check(*pParent))
    {
        WD_WARN_T("AddNewStructureNodeDialog", "You cannot operate the current node!");
        return nullptr;
    }
    // 申领对象
    if (!_app.getBMDesign().claimMgr().checkAdd(pParent, nullptr))
    {
        return nullptr;
    }
    // 校验是否存在相同名称
    if (_app.getBMDesign().nameExists(name))
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Same Name Exists!");
        return nullptr;
    }
    auto newNode = _app.getBMDesign().create(pParent, type, nullptr, name);
    if (newNode == nullptr)
    {
        WD_WARN_T("AddNewStructureNodeDialog", "CreateNodeFailed");
        return nullptr;
    }
    return newNode;
}

WD::WDNode::Nodes CreateStructureGridDialog::createSBFRs(WD::WDNode& frmw, const WD::DVec3& origin, const WD::DVec3& xDir, const WD::DVec3& yDir, const AxisNetMsg& msg)
{
    WD::WDNode::Nodes createdNodes;
    std::string name = frmw.name();
    bool bSuccess = true;
    int elevationIndex = 1;
    WD::DVec3 zDir = WD::DVec3::Cross(xDir, yDir);
    for (auto it = msg[2].mapIdOffset.begin(); it != msg[2].mapIdOffset.end(); ++it, ++elevationIndex)
    {
        std::string ei = std::to_string(elevationIndex);
        std::string eName = name + "/" + ei;
        WD::WDNode::SharedPtr pNewSbfr = createNode(frmw, eName, "SBFR");
        if (pNewSbfr == nullptr)
        {
            bSuccess = false;
            continue;
        }
        createdNodes.emplace_back(pNewSbfr);
        //设置属性
        pNewSbfr->setAttribute("Description", ei);
        pNewSbfr->setAttribute("Function", "LEVEL");
        pNewSbfr->setAttribute("Purpose", WD::WDBMWord("GRID"));
        //创建SCTN
        auto createSctnfunc = [&, this](const HandleAxisNet::CreateAxisNetMsg& msg, bool bX, double length,
            const WD::DVec3& lineDir, const WD::DVec3& arrangeDir)
        {
            int xIndex = 1;
            for (auto xIt = msg.mapIdOffset.begin(); xIt != msg.mapIdOffset.end(); ++xIt, ++xIndex)
            {
                const double& offset = xIt->first;
                const std::string& id = xIt->second;
                std::string xName = eName + "/" + msg.strLabel + std::to_string(xIndex);
                WD::WDNode::SharedPtr pNewSctn = this->createNode(*pNewSbfr, xName, "SCTN");
                if (pNewSctn == nullptr)
                {
                    bSuccess = false;
                    continue;
                }
                //设置属性
                pNewSctn->setAttribute("Description", id);
                pNewSctn->setAttribute("Gtype", WD::WDBMWord(bX ? "XGRD" : "YGRD"));
                pNewSctn->setAttribute("Number", xIndex);
                createdNodes.emplace_back(pNewSctn);
                WD::DVec3 posStart = origin + it->first * zDir + offset * arrangeDir;
                WD::DVec3 posEnd = posStart + length * lineDir;
                // 设置起始点和结束点
                pNewSctn->setAttribute("Posstart", posStart);
                pNewSctn->setAttribute("Posend", posEnd);
            }
        };
        //创建X轴SCTN
        createSctnfunc(msg[0], true, msg[1].mapIdOffset.rbegin()->first, yDir, xDir);
        //创建Y轴SCTN
        createSctnfunc(msg[1], false, msg[0].mapIdOffset.rbegin()->first, xDir, yDir);
    }
    frmw.triggerUpdate();
    if (!bSuccess)
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Create node failed");
    }
    return createdNodes;
}

bool CreateStructureGridDialog::dataValidation(AxisNetMsg& msg, WD::DVec3& xDir, WD::DVec3& yDir) const
{
    msg = getAxisNetNodes();
    if (msg.size() != 3)
    {
        WD_WARN_T("CreateStructureGridDialog", "Parse data failed");
        return false;
    }
    //至少要有一条高程
    if (msg[2].mapIdOffset.size() == 0)
    {
        WD_WARN_T("CreateStructureGridDialog", "Elevations is null");
        return false;
    }
    WD::DVec3 zDir;
    if (!getDirections(xDir, yDir, zDir))
    {
        return false;
    }
    return true;
}

using KeyValues = std::pair<std::string_view, WD::WDBMAttrValue>;
/**
 * @brief 记录需要undo的属性
 * @param node 节点
 * @param keys 键列表
 * @param values 值列表
 * @param modifiedAttrs 需要undo的属性
*/
static void RecordNodeAttributeChanges(WD::WDNode& node,
    const std::vector<KeyValues>& keyValues,
    std::vector<WD::WDBMBase::CmdNodeAttributes>& modifiedAttrs)
{
    std::vector<WD::WDBMBase::CmdAttribute> attrs;
    attrs.reserve(keyValues.size());
    for (size_t i = 0; i < keyValues.size(); ++i)
    {
        auto& key = keyValues[i].first;
        auto& value = keyValues[i].second;
        auto originAttr = node.getAttribute(key);
        if (!originAttr.valid())
        {
            continue;
        }
        if (originAttr == value)
        {
            continue;
        }
        if (!node.setAttribute(key, value))
        {
            continue;
        }
        attrs.emplace_back(key, originAttr);
    }
    modifiedAttrs.emplace_back(WD::WDNode::ToShared(&node), attrs);
}

bool CreateStructureGridDialog::editSingleFRMW(WD::WDNode& frmw2Modify)
{
    std::string name = frmw2Modify.name();
    if (_nameHelpter.name() != name && _nameHelpter.exists())
    {
        WD_WARN_T("AddNewStructureNodeDialog", "Same Name Exists!");
        return false;
    }

    //修改名称前获取关联的DATUM
    WD::WDNode::SharedPtr pDatum = GetDatum(frmw2Modify);
    if (pDatum == nullptr)
    {
        pDatum = _pAddNewStructureNodeDialog->createDatum(name);
    }
    std::vector<WD::WDBMBase::CmdNodeAttributes> modifiedAttrs;//undo属性列表
    // 设置属性
    frmw2Modify.setName(_nameHelpter.name());
    RecordNodeAttributeChanges(frmw2Modify,
        { 
            {"Function", ui.comboBox->currentData().toString().toUtf8().data()},
            {"DESC", ui.lineEditDescription->text().toUtf8().data()},
            {"Purpose", WD::WDBMWord("GRID")}
        }, modifiedAttrs);
    
    WD::WDUndoCommand* pUndoAttrCmd = WD::WDBMBase::MakeAttributesSetedCommand(modifiedAttrs);
    if (pUndoAttrCmd != nullptr)
    {        
        _app.undoStack().beginMarco("Set FRMW Attr");
        _app.undoStack().push(pUndoAttrCmd);
        _app.undoStack().endMarco();
    }
    FrmwGridInfo currentInfo = getCurrentGridInfo();
    //是否需要重新创建节点
    if (FrmwGridInfo::needRecreate(_lastGirdInfo, currentInfo))
    {
        AxisNetMsg msg;
        WD::DVec3 xDir, yDir;
        if (!dataValidation(msg, xDir, yDir))
        {
            return false;
        }
        //删除原有子节点
        WD::WDNode::Nodes nodes2Delete;
        for (auto& sbfr : frmw2Modify.children())
        {
            if (sbfr == nullptr)
            {
                continue;
            }
            if (sbfr->type() != "SBFR" ||
                sbfr->getAttribute("Function").toString() != "LEVEL" ||
                sbfr->getAttribute("Purpose").toWord() != WD::WDBMWord("GRID"))
            {
                continue;
            }
            nodes2Delete.push_back(sbfr);
        }
        if (!nodes2Delete.empty())
        {
            DeleteGivenNodes(nodes2Delete, _app, "CreateStructureGridDialog", false);
        }
        //新建SBFR
        WD::WDNode::Nodes createdNodes = createSBFRs(frmw2Modify, currentInfo.position, xDir, yDir, msg);
        //撤销重做
        auto pCmdCreatedNode = WD::WDBMBase::MakeCreatedCommand(createdNodes);
        if (pCmdCreatedNode != nullptr)
        {
            _app.undoStack().beginMarco("Create SBFR");
            _app.undoStack().push(pCmdCreatedNode);  
            _app.undoStack().endMarco();
        }
        //重新设置显示类型
        WD::WDBMFGridUtils::FrmwShowInfos infos = { {frmw2Modify, _showType} };
        WD::WDBMFGridUtils::SetFrmwShowType(_app, infos);
        frmw2Modify.triggerUpdate(true);
    }

    //设置DATUM的属性
    if (pDatum != nullptr)
    {    
        std::vector<std::pair<std::string, WD::WDBMAttrValue>> attrs = 
        {
            {"Name", _nameHelpter.name() + "/DATUM"},
            {"Height", 500.0},
            {"Position", currentInfo.position},
            {"OrientationStr WRT Owner", 
            std::string("Y is ") + ui.lineEditYDir->text().toUtf8().data() + " and Z is " + ui.lineEditZdir->text().toUtf8().data()}
        };
        _app.undoStack().beginMarco("Set DATUM Attr");
        for (auto& attr : attrs)
        {
            WD::WDUndoCommand* pCommand = WD::WDBMBase::MakeAttributeSetCommand(pDatum, attr.first, attr.second);
            if (pCommand != nullptr)
            {
                _app.undoStack().push(pCommand);
            }            
        }
        _app.undoStack().endMarco();
        pDatum->triggerUpdate(true);
    }
    return true;
}
void CreateStructureGridDialog::initDialog()
{
    ui.comboBox->addItem("System", "System");
    ui.comboBox->addItem("Local", "Local");
    Trs("CreateStructureGridDialog", ui.comboBox);
}

void CreateStructureGridDialog::retranslateUi()
{
    Trs("CreateStructureGridDialog"
        , static_cast<QDialog*>(this)
        , ui.tabWidget
        , ui.radioButtonKey
        , ui.radioButtonPosition
        , ui.radioButtonLKey
        , ui.radioButtonNothing
        , ui.groupBoxAxisX
        , ui.groupBoxAxisY
        , ui.groupBoxAxisZ
        , ui.groupBoxDisplay
        , ui.groupBoxPosition
        , ui.groupBoxOrientation
        , ui.labelX
        , ui.labelY
        , ui.labelZ
        , ui.labelName
        , ui.labelNameTwo
        , ui.labelIDsX
        , ui.labelIDsY
        , ui.labelIDsZ
        , ui.labelOffsetsX
        , ui.labelOffsetsY
        , ui.labelOffsetsZ
        , ui.labelSnap
        , ui.pushButtonAutofill
        , ui.pushButtonSetView
        , ui.pushButtonSave
        , ui.pushButtonClose
        , ui.pushButtonDelete
        , ui.checkBoxAxisShowOrHide
        , ui.pushButtonCurrentNode
        , ui.labelDescription
        , ui.pushButtonCreate
        , ui.pushButtonCopy
    );
}

CreateStructureGridDialog::FrmwGridInfo::FrmwGridInfo()
{
    xye[0].lable = "X";
    xye[1].lable = "Y";
    xye[2].lable = "EL";
    functionIndex = 0;
    yDirection = "N";
    zDirection = "U";
}
/**
 * @brief 比较单个轴的填充数据是否一致
 * @param one 
 * @param another 
 * @return 
*/
bool operator==(const CreateStructureGridDialog::AxisInfo& one, const CreateStructureGridDialog::AxisInfo& another)
{
    return another.lable == one.lable && another.ids == one.ids && another.offsets == one.offsets;
}
/**
 * @brief 比较单个轴的填充数据是否不一致
 * @param one 
 * @param another 
 * @return 
*/
bool operator!=(const CreateStructureGridDialog::AxisInfo& one, const CreateStructureGridDialog::AxisInfo& another)
{
    return !(one == another);
}

bool CreateStructureGridDialog::FrmwGridInfo::needRecreate(const FrmwGridInfo& origin, const FrmwGridInfo& modify)
{
    if (origin.position != modify.position 
        || origin.yDirection != modify.yDirection 
        || origin.zDirection != modify.zDirection)
    {
        return true;
    }
    for (size_t i = 0; i < origin.xye.size(); i++)
    {
        if (origin.xye[i] != modify.xye[i])
        {
            return true;
        }
    }
    return false;
}

/**
 * @brief 比较两个轴网参数是否一致
 * @param one 
 * @param another 
 * @return 
*/
bool operator==(const CreateStructureGridDialog::FrmwGridInfo& one, const CreateStructureGridDialog::FrmwGridInfo& another)
{
    if (one.name != another.name
        || one.functionIndex != another.functionIndex
        || one.description != another.description)
    {
        return false;
    }

    return !CreateStructureGridDialog::FrmwGridInfo::needRecreate(one, another);
}

bool operator!=(const CreateStructureGridDialog::FrmwGridInfo& one, const CreateStructureGridDialog::FrmwGridInfo& another)
{
    return !(one == another);
}


CreateStructureGridDialog::FrmwGridInfo CreateStructureGridDialog::getCurrentGridInfo() const
{
    FrmwGridInfo info;
    info.name = ui.lineEditName->text();
    info.functionIndex = ui.comboBox->currentIndex();
    info.description = ui.lineEditDescription->text();
    info.xye[0].lable = ui.lineEditLabelX->text();
    info.xye[0].ids = ui.textEditIDsX->toPlainText();
    info.xye[0].offsets = ui.textEditOffsetsX->toPlainText();
    info.xye[1].lable = ui.lineEditLabelY->text();
    info.xye[1].ids = ui.textEditIDsY->toPlainText();
    info.xye[1].offsets = ui.textEditOffsetsY->toPlainText();
    info.xye[2].lable = ui.lineEditLabelZ->text();
    info.xye[2].ids = ui.textEditIDsZ->toPlainText();
    info.xye[2].offsets = ui.textEditOffsetsZ->toPlainText();
    info.position = getPosition();
    info.yDirection = ui.lineEditYDir->text();
    info.zDirection = ui.lineEditZdir->text();
    return info;
}

bool CreateStructureGridDialog::isModified() const
{
    CreateStructureGridDialog::FrmwGridInfo current = getCurrentGridInfo();
    return _lastGirdInfo != current;
}
