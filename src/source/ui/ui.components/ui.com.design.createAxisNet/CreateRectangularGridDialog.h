#pragma once

#include <QDialog>
#include "ui_CreateRectangularGridDialog.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiNodeSelect.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiNodeNameHelpter.h"

#include "AutofillRectangularGridDialog.h"
#include "CreateAxisNetCommon.h"

class CreateRectangularGridDialog : public QDialog
{
    Q_OBJECT
private:
    enum class EditType
    {
        ET_Null,
        // 创建
        ET_Create,
        // 单个编辑
        ET_EditSingle,
        // 多个编辑
        ET_EditMulti,
    };
public:
    CreateRectangularGridDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~CreateRectangularGridDialog();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
private:
    using AxisNetMsg = std::vector<HandleAxisNet::CreateAxisNetMsg>;

private slots:
    /**
    * @brief 确定 按钮按下通知响应
    */
    void slotOkButtonClicked();
    /**
    * @brief 取消 按钮按下通知响应
    */
    void slotCancelButtonClicked();
    /**
    * @brief 自动填充 按钮按下通知响应
    */
    void slotAutofillButtonClicked();
    /**
    * @brief 轴网 显示设置 更改通知响应
    */
    void slotChangeDisplaySet();
    /**
    * @brief 调整视图 按钮按下通知响应
    */
    void slotSetViewClicked();
    /**
    * @brief 自动填充界面通知响应
    * @param lstAutoData: 自动填充数据结构体
    */
    void slotShowAutofillData(const std::vector<AutofillRectangularGridDialog::AutofillMsg>& lstAutoData);


    void slotPushButtonEditClicked();

    void slotPushButtonRefreshClicked();

    void slotPushButtonDeleteClicked();
private:
    /**
    * @brief 更新编辑类型
    */
    void updateEditType();
    /**
    * @brief 获取轴网节点数据
    */
    AxisNetMsg getAxisNetNodes();
    /**
    * @brief 调整轴网的基点坐标和方向
    */
    void adjustAxisNetPositionOrientation();
    /**
    * @brief 当前模型树节点改变通知
    */
    void onCurrentNodeChanged();
    /**
    * @brief 清空界面数据
    */
    void clearWidgetData();
    /**
    * @brief 清空"选项"界面数据
    */
    void clearOptionsWidgetData();
    /**
    * @brief 清空"调整"界面数据
    */
    void clearPositionAndOrientationWidgetData();
    /**
    * @brief 显示获取的轴网系统数据
    */
    void showSelectAxisNetNodesData(const AxisNetMsg& selAxisMsg);
    /**
    * @brief 设置显示
    */
    void setDisplay(int flag);
    /**
    * @brief  创建轴节点
    * @param  pGRIDWLNode: 当前GRIDWL节点
    * @param  lstAxisData: 轴网数据
    */
    WD::WDNode::SharedPtr createAxisNetNode(WD::WDNode::SharedPtr pGRIDWLNode, const AxisNetMsg& lstAxisData);
    /**
     * @brief 获取当前选中的节点
     * @return 
    */
    WD::WDNode::SharedPtr currentNode();
public:
    /**
    * @brief 根据模型树当前选中节点生成要创建的轴网名称
    */
    void genNewName();
public:
    /**
     * @brief 轴网创建
     * @param operaNodes 创建的节点
    */
    void axisNetCreate();
    /**
     * @brief 轴网节点单个编辑
     * @param operaNodes 被编辑的节点
    */
    void axisNetEditSingle();
    /**
    * @brief 轴网节点批量编辑
    * @param operaNodes 被编辑的节点列表
    */
    void axisNetEditMulti();
public:
    /**
    * @brief 初始化界面
    */
    void initDialog();
    /**
    * @brief 界面文本翻译
    */
    void retranslateUi();
public:
    bool bIsAdmin = true;
private:
    Ui::CreateRectangularGridDialog      ui;
    WD::WDCore&                         _app;
    // 保存操作的节点               
    std::vector<WD::WDNode::WeakPtr>    _nodes;
    // wrt窗口
    UiNodeSelect*                       _pWrtWidget;
    HandleAxisNet                       _handleAxisNet;
    // 位置捕捉界面辅助
    UiPositionCaptureHelpter            _positionCaptureHelpter;
    // 自动填充轴网界面
    AutofillRectangularGridDialog*      _pAutofillGridDialog;
    //
    EditType                            _editType;
    // 当前点击编辑轴网的初始名称
    std::string                         _axisSystemName;
    // 节点名称助手
    UiNodeNameHelpter                   _nameHelpter;
    UiNodeNameHelpter                   _nameHelpterX;
    UiNodeNameHelpter                   _nameHelpterY;
    UiNodeNameHelpter                   _nameHelpterZ;
};

