#ifndef SELECTGRIDTYPEDIALOG_H
#define SELECTGRIDTYPEDIALOG_H

#include <QDialog>
#include "CreateAxisNetCommon.h"
class IMainWindow;
class CreateRectangularGridDialog;
class CreateStructureGridDialog;
namespace Ui {
class SelectGridTypeDialog;
}

class SelectGridTypeDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SelectGridTypeDialog(IMainWindow& mainWindow);
    ~SelectGridTypeDialog();
private:
    /**
     * @brief 翻译控件
    */
    void retranslateUi();

private slots:
    /**
     * @brief 点击ok
    */
    void on_buttonBox_accepted();

private:
    Ui::SelectGridTypeDialog *ui;
    IMainWindow& _mainWindow;
    CreateRectangularGridDialog* _pDialogAssistant;
    CreateStructureGridDialog* _pDialogStructure;
};

#endif // SELECTGRIDTYPEDIALOG_H
