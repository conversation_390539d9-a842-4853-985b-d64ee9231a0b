#pragma once

#include <QDialog>
#include "ui_CreateStructureGridDialog.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeTree.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiNodeSelectHelpter.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiNodeNameHelpter.h"

#include "AutofillRectangularGridDialog.h"
#include "CreateAxisNetCommon.h"
#include "core/businessModule/design/structures/WDBMFGridUtils.h"


class AddNewStructureNodeDialog;

class CreateStructureGridDialog : public QDialog
{
    Q_OBJECT
private:
    /**
     * @brief 选项页控件的清空与可用状态控制
    */
    struct OpthionsEditableWidgets
    {
        std::array<QLineEdit*, 5> lineEditors;
        std::array<QTextEdit*, 6> textEditors;
        std::array<QPushButton*, 2> pushButtons;
        QComboBox* combox;
        /**
         * @brief 清除控件内容
        */
        void clear();
        /**
         * @brief 设置控件可用或不可用（置灰）
         * @param able 是否可用
        */
        void setEditable(bool able);
    };
    /**
     * @brief 调整页控件的清空与可用状态控制
    */
    struct PosOrieEditableWidgets
    {
        std::array<QLineEdit*, 3> lineEditors;
        std::array<QDoubleSpinBox*, 3> spinners;
        std::array<QCheckBox*, 4> checkBoxes;
        /**
         * @brief 清除控件内容
        */
        void clear();
        /**
         * @brief 设置控件可用或不可用（置灰）
         * @param able 是否可用
        */
        void setEditable(bool able);
    };
    /**
     * @brief 初始化用于清空，设置可用的控件
    */
    void registWidget();

public:
    
    CreateStructureGridDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~CreateStructureGridDialog();
    /**
     * @brief 获取功能类型在combox中的索引
     * @param bShow
    */
    int getFunctionIndex(std::string_view frmwFucntion) const;
    /**
     * @brief 单个轴的填充信息
    */
    struct AxisInfo
    {
        //标签名称
        QString lable;
        //id
        QString ids;
        //偏移
        QString offsets;
    };
    /**
     * @brief 一个结构轴网的所有参数
    */
    struct FrmwGridInfo
    {
        FrmwGridInfo();
        //轴网名称
        QString name;
        //功能
        int functionIndex;
        //轴网描述
        QString description;
        //三个方向上的填充信息
        std::array<AxisInfo, 3> xye;
        //基点位置
        WD::DVec3 position;
        //y方向
        QString yDirection;
        //z方向
        QString zDirection;
        /**
         * @brief 改动是否涉及到重新绘制SCTN
         * @return 
        */
        static bool needRecreate(const FrmwGridInfo& origin, const FrmwGridInfo& modify);
    };
    using AxisNetMsg = std::vector<HandleAxisNet::CreateAxisNetMsg>;
    /**
     * @brief 创建SBFR
     * @param frmw 父节点
     * @param origin 原点
     * @param dirX X方向
     * @param dirY Y方形
     * @param msg 填充信息
    */
    WD::WDNode::Nodes createSBFRs(WD::WDNode& frmw, const WD::DVec3& origin, const WD::DVec3& dirX, const WD::DVec3& dirY, const AxisNetMsg& msg);
    /**
     * @brief 参数校验
     * @param msg
     * @param xDir
     * @param yDir
     * @return
    */
    bool dataValidation(AxisNetMsg& msg, WD::DVec3& xDir, WD::DVec3& yDir) const;
    /**
    * @brief 初始化界面
    */
    void initDialog();
    /**
    * @brief 界面文本翻译
    */
    void retranslateUi();
    /**
     * @brief 通过节点获取对应的树控件
     * @param pNode 
     * @return 
    */
    QTreeWidgetItem* getTreeItemByNode(WD::WDNode::SharedPtr pNode) const;
    /**
     * @brief 获取轴网参数
     * @param  轴网节点
     * @return 特征参数
    */
    FrmwGridInfo getCurrentGridInfo() const;
    /**
     * @brief 当前轴网的参数是否被修改
     * @param frmw2Modify
    */
    bool isModified() const;
    /**
     * @brief 编辑节点
     * @param frmw2Modify 
    */
    bool editSingleFRMW(WD::WDNode& frmw2Modify);
    /**
     * @brief 显示节点数据
     * @param node 
    */
    void showGridData(WD::WDNode& node);
    /**
     * @brief 获取当前选中的树结构对应的Node
    */
    WD::WDNode::SharedPtr getSeletedNode() const;
    /**
     * @brief 获取节点的所属STRU节点
     * @param node 
     * @return 
    */
    WD::WDNode::SharedPtr getAncestorStru(WD::WDNode& node) const;
    /**
     * @brief 刷新结构目录树
     * @param node2Select 刷新后选中节点 
    */
    void refreshFrmwNodes(WD::WDNode::SharedPtr node2Select);
    /**
     * @brief 将结构目录树刷新为FRMW列表
     * @param oNodes 用于显示的节点列表
     * @param node2Select 设置选中节点
    */
    void showFrmwNodes(const std::vector<WD::WDNode::SharedPtr>& oNodes, WD::WDNode::SharedPtr node2Select);
    /**
     * @brief 清空选项页相关参数
    */
    void clearOptions();
    /**
     * @brief 清空调整页相关参数
    */
    void clearPositionOrietation();
    /**
     * @brief 设置选项页的可用状态
     * @param able 
    */
    void ableOptions(bool able);
    /**
     * @brief 设置调整页的可用状态
     * @param able 
    */
    void ablePositionOrietation(bool able);
    /**
     * @brief 在轴网目录树上列出当前节点下的所有满足要求的FRMW节点
     * @param node 父节点
     * @return 子frmw列表 
    */
    std::vector<WD::WDNode::SharedPtr> listSubFrmw(WD::WDNode& node) const;
    /**
     * @brief 显示轴网目录树选中节点的数据
    */
    void showCurrentGridData();
    /**
     * @brief 在节点下创建新节点
     * @param parent 父节点
     * @param name 节点名称
     * @param type 节点类型
     * @return 新建节点
    */
    WD::WDNode::SharedPtr createNode(WD::WDNode& parent, const std::string& name, const std::string& type);
    /**
     * @brief 设置原点位置
     * @param position
    */
    void setPosition(const WD::DVec3& position);
    /**
     * @brief 获取原点位置
    */
    WD::DVec3 getPosition() const;
    /**
     * @brief 设置方向
     * @param direction
    */
    void setYZXDirection(const WD::TVec3<double>& directionY, const WD::TVec3<double>& directionZ);
    /**
     * @brief 获取方向
    */
    bool getDirections(WD::TVec3<double>& directionX, WD::TVec3<double>& directionY, WD::TVec3<double>& directionZ) const;
    /**
     * @brief 获取当前显示类型
     * @param pRadioButton 按钮
     * @return 显示类型
    */
    WD::WDBMFGridUtils::FrmwShowType getDisplayType(QRadioButton& radioButton) const;
    /**
     * @brief 通过显示类型映射到按钮
     * @param type 显示类型
     * @return 按钮
    */
    QRadioButton* getRadioButtonByType(WD::WDBMFGridUtils::FrmwShowType type) const;
protected:
    /**
     * @brief 对话框显示事件
     * @param evt 
    */
    virtual void showEvent(QShowEvent* evt) override;
    /**
     * @brief 对话框隐藏事件
     * @param evt 
    */
    virtual void hideEvent(QHideEvent* evt) override;
private:
    /**
     * @brief 设置选中轴网的显示隐藏
     * @param bShow
    */
    void slotShowHide(int bShow);
    /**
     * @brief 点击“创建”按钮的响应
     * @param bShow
    */
    void slotCreate();
    /**
     * @brief 点击“复制”按钮的响应
    */
    void slotCopy();
    /**
     * @brief 选中树节点
    */
    void slotSelectTreeWidget(QTreeWidgetItem* current, int);
    /**
     * @brief 新的结构节点创建后的响应
     * @param pNewNode 新创建的结构节点
    */
    void slotNewNodeCreated(WD::WDNode& newNode);
    /**
    * @brief 按下“创建节点”的响应
    */
    void slotNewNodeClicked();
    /**
    * @brief 按下“当前节点”的响应
    */
    void slotCurrentNodeClicked();
    /**
    * @brief 确定 按钮按下通知响应
    */
    void slotOkButtonClicked();
    /**
    * @brief 取消 按钮按下通知响应
    */
    void slotCancelButtonClicked();
    /**
    * @brief 自动填充 按钮按下通知响应
    */
    void slotAutofillButtonClicked();
    /**
    * @brief 轴网 显示设置 更改通知响应
    */
    void slotChangeDisplaySet();
    /**
    * @brief 调整视图 按钮按下通知响应
    */
    void slotSetViewClicked();
    /**
    * @brief 自动填充界面通知响应
    * @param lstAutoData: 自动填充数据结构体
    */
    void slotShowAutofillData(const std::vector<AutofillRectangularGridDialog::AutofillMsg>& lstAutoData);        
    /**
     * @brief 删除按钮的响应
    */
    void slotPushButtonDeleteClicked();
    /**
     * @brief frmw节点后的勾选框被选中
    */
    void slotTreeItemChecked(int state);
private:
    /**
     * @brief 获取用户选中的对话框结构树上的对应FRMW节点
     * @param oSelected 选中的节点
    */
    std::vector<WD::WDNode::SharedPtr> getCheckedFrmwNode() const;
    /**
    * @brief 获取轴网节点数据
    */
    AxisNetMsg getAxisNetNodes() const;
    /**
    * @brief 清空右侧参数界面数据
    */
    void clearWidgetData();

public:
    bool bIsAdmin = true;
private:
    Ui::CreateStructureGridDialog      ui;
    WD::WDCore&                         _app;
    // wrt窗口
    HandleAxisNet                       _handleAxisNet;
    // 位置捕捉界面辅助
    UiPositionCaptureHelpter            _positionCaptureHelpter;
    // 自动填充轴网界面
    AutofillRectangularGridDialog*      _pAutofillGridDialog;
    AddNewStructureNodeDialog* _pAddNewStructureNodeDialog;
    // 节点名称助手
    UiNodeNameHelpter                   _nameHelpter;
    UiNodeNameHelpter                   _nameHelpterX;
    UiNodeNameHelpter                   _nameHelpterY;
    UiNodeNameHelpter                   _nameHelpterZ;
    OpthionsEditableWidgets _opWidgets;
    PosOrieEditableWidgets _posWidgets;
    //显示类型按钮
    QRadioButton* _radioButtons[4];
    //当前节点
    WD::WDNode::SharedPtr _pCurrentNode;
    //当前显示的参数
    FrmwGridInfo _lastGirdInfo;
    //最近一次修改的节点
    WD::WDNode::SharedPtr _pLastModifiedNode;
    //当前显示类型
    WD::WDBMFGridUtils::FrmwShowType _showType = WD::WDBMFGridUtils::FrmwShowType::FST_Nothing;
};

