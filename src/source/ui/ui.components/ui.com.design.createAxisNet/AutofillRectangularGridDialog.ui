<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AutofillRectangularGridDialog</class>
 <widget class="QWidget" name="AutofillRectangularGridDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>482</width>
    <height>407</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::StrongFocus</enum>
  </property>
  <property name="windowTitle">
   <string>AutoFill</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBoxAxisX">
     <property name="title">
      <string>X Axis</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="labelX">
        <property name="text">
         <string>Label</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEditAxisX">
        <property name="focusPolicy">
         <enum>Qt::NoFocus</enum>
        </property>
        <property name="text">
         <string>X</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1,0,1,0,1">
        <item>
         <widget class="QLabel" name="labelOffsetsX">
          <property name="text">
           <string>OffsetsFrom</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxOffsetsX">
          <property name="minimum">
           <number>-*********</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelToX">
          <property name="text">
           <string>To</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxToX">
          <property name="minimum">
           <number>-*********</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
          <property name="value">
           <number>10000</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelIntervalX">
          <property name="text">
           <string>Interval</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxIntervalX">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
          <property name="value">
           <number>5000</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="2" column="0" colspan="2">
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1,1,0,1,0,1">
        <item>
         <widget class="QLabel" name="labelIDsX">
          <property name="text">
           <string>IDs format</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxIsNumberX"/>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxIsAscendX"/>
        </item>
        <item>
         <widget class="QLabel" name="labelFromX">
          <property name="text">
           <string>From</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEditFromX">
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string>1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelIntervalIDsX">
          <property name="text">
           <string>Interval</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxIDsX">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBoxAxisY">
     <property name="title">
      <string>Y Axis</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <widget class="QLabel" name="labelY">
        <property name="text">
         <string>Label</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEditAxisY">
        <property name="focusPolicy">
         <enum>Qt::NoFocus</enum>
        </property>
        <property name="text">
         <string>Y</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="0,1,0,1,0,1">
        <item>
         <widget class="QLabel" name="labelOffsetsY">
          <property name="text">
           <string>OffsetsFrom</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxOffsetsY">
          <property name="minimum">
           <number>-*********</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelToY">
          <property name="text">
           <string>To</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxToY">
          <property name="minimum">
           <number>-*********</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
          <property name="value">
           <number>10000</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelIntervalY">
          <property name="text">
           <string>Interval</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxIntervalY">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
          <property name="value">
           <number>5000</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="2" column="0" colspan="2">
       <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,1,1,0,1,0,1">
        <item>
         <widget class="QLabel" name="labelIDsY">
          <property name="text">
           <string>IDs format</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxIsNumberY"/>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxIsAscendY"/>
        </item>
        <item>
         <widget class="QLabel" name="labelFromY">
          <property name="text">
           <string>From</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEditFromY">
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string>1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelIntervalIDsY">
          <property name="text">
           <string>Interval</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxIDsY">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0" colspan="3">
    <widget class="QGroupBox" name="groupBoxAxisZ">
     <property name="title">
      <string>Elevations</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <widget class="QLabel" name="labelZ">
        <property name="text">
         <string>Label</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEditAxisZ">
        <property name="text">
         <string>EL</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="0,1,0,1,0,1">
        <item>
         <widget class="QLabel" name="labelOffsetsZ">
          <property name="text">
           <string>OffsetsFrom</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxOffsetsZ">
          <property name="minimum">
           <number>-*********</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelToZ">
          <property name="text">
           <string>To</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxToZ">
          <property name="minimum">
           <number>-*********</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
          <property name="value">
           <number>10000</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelIntervalZ">
          <property name="text">
           <string>Interval</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxIntervalZ">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
          <property name="value">
           <number>5000</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="2" column="0" colspan="2">
       <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="0,1,1,0,1,0,1">
        <item>
         <widget class="QLabel" name="labelIDsZ">
          <property name="text">
           <string>IDs format</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxIsNumberZ"/>
        </item>
        <item>
         <widget class="QComboBox" name="comboBoxIsAscendZ"/>
        </item>
        <item>
         <widget class="QLabel" name="labelFromZ">
          <property name="text">
           <string>From</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEditFromZ">
          <property name="focusPolicy">
           <enum>Qt::NoFocus</enum>
          </property>
          <property name="text">
           <string>1</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelIntervalIDsZ">
          <property name="text">
           <string>Interval</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="spinBoxIDsZ">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>*********</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>299</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="1">
    <widget class="QPushButton" name="pushButtonOk">
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string>Ok</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="3" column="2">
    <widget class="QPushButton" name="pushButtonCancel">
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="text">
      <string>Cancel</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>lineEditAxisX</tabstop>
  <tabstop>spinBoxOffsetsX</tabstop>
  <tabstop>spinBoxToX</tabstop>
  <tabstop>spinBoxIntervalX</tabstop>
  <tabstop>comboBoxIsNumberX</tabstop>
  <tabstop>comboBoxIsAscendX</tabstop>
  <tabstop>lineEditFromX</tabstop>
  <tabstop>spinBoxIDsX</tabstop>
  <tabstop>lineEditAxisY</tabstop>
  <tabstop>spinBoxOffsetsY</tabstop>
  <tabstop>spinBoxToY</tabstop>
  <tabstop>spinBoxIntervalY</tabstop>
  <tabstop>comboBoxIsNumberY</tabstop>
  <tabstop>comboBoxIsAscendY</tabstop>
  <tabstop>lineEditFromY</tabstop>
  <tabstop>spinBoxIDsY</tabstop>
  <tabstop>lineEditAxisZ</tabstop>
  <tabstop>spinBoxOffsetsZ</tabstop>
  <tabstop>spinBoxToZ</tabstop>
  <tabstop>spinBoxIntervalZ</tabstop>
  <tabstop>comboBoxIsNumberZ</tabstop>
  <tabstop>comboBoxIsAscendZ</tabstop>
  <tabstop>lineEditFromZ</tabstop>
  <tabstop>spinBoxIDsZ</tabstop>
  <tabstop>pushButtonOk</tabstop>
  <tabstop>pushButtonCancel</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
