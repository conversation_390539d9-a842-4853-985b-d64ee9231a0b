#include "CreateRectangularGridDialog.h"
#include "core/WDTranslate.h"
#include "core/scene/WDScene.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/businessModule/design/auxiliaryLine/WDBMDAuxiUtils.h"

static constexpr const char* TypeName_GRIDWL = "GRIDWL";
static constexpr const char* TypeName_GRIDSY = "GRIDSY";
static constexpr const char* TypeName_GRIDAX = "GRIDAX";
static constexpr const char* TypeName_GRIDLN = "GRIDLN";

CreateRectangularGridDialog::CreateRectangularGridDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _app(core)
    , _positionCaptureHelpter(_app)
    , _nameHelpter(_app.getBMDesign())
    , _nameHelpterX(_app.getBMDesign())
    , _nameHelpterY(_app.getBMDesign())
    , _nameHelpterZ(_app.getBMDesign())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 位置捕捉帮助
    _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CaptureTimes::CT_Repeat);
    auto param = _positionCaptureHelpter.captureParam();
    param.bShowResultCoord = true;
    _positionCaptureHelpter.setCaptureParam(param);
    _editType = EditType::ET_Null;

    _pAutofillGridDialog = new AutofillRectangularGridDialog(parent);

    this->initDialog();
    this->retranslateUi();
    ui.tabWidget->setCurrentIndex(0);
    ui.treeWidget->setColumnCount(2);
    ui.treeWidget->header()->setVisible(false);
    ui.treeWidget->header()->setSectionResizeMode(QHeaderView::ResizeMode::Stretch);
    // 绑定事件通知响应
    connect(ui.pushButtonAutofill
        , &QPushButton::clicked
        , this
        , &CreateRectangularGridDialog::slotAutofillButtonClicked);
    //显示效果改变
    connect(ui.radioButtonAxes
        , &QPushButton::toggled
        , this
        , &CreateRectangularGridDialog::slotChangeDisplaySet);
    connect(ui.radioButtonGridline
        , &QPushButton::toggled
        , this
        , &CreateRectangularGridDialog::slotChangeDisplaySet);
    connect(ui.radioButtonSpacings
        , &QPushButton::toggled
        , this
        , &CreateRectangularGridDialog::slotChangeDisplaySet);
    connect(ui.radioButtonCoordinates
        , &QPushButton::toggled
        , this
        , &CreateRectangularGridDialog::slotChangeDisplaySet);
    //调整视图（居中）
    connect(ui.pushButtonSetView
        , &QPushButton::clicked
        , this
        , &CreateRectangularGridDialog::slotSetViewClicked);
    //应用
    connect(ui.pushButtonSave
        , &QPushButton::clicked
        , this
        , &CreateRectangularGridDialog::slotOkButtonClicked);
    connect(ui.pushButtonClose
        , &QPushButton::clicked
        , this
        , &CreateRectangularGridDialog::slotCancelButtonClicked);
    connect(_pAutofillGridDialog
        , &AutofillRectangularGridDialog::sigAutofillData
        , this
        , &CreateRectangularGridDialog::slotShowAutofillData);

    _positionCaptureHelpter.setCheckBoxCapture(ui.checkBoxSnap);
    _positionCaptureHelpter.setCheckBoxXYZ(ui.checkBoxPosX, ui.checkBoxPosY, ui.checkBoxPosZ);
    _positionCaptureHelpter.setDoubleSpinBoxXYZ(ui.doubleSpinBoxPosX, ui.doubleSpinBoxPosY, ui.doubleSpinBoxPosZ);

    //设置选中状态
    connect(ui.treeWidget, &QTreeWidget::itemClicked, [&](QTreeWidgetItem* current, int)
    {
        if (current == nullptr)
            return;
        auto* widget = ui.treeWidget->itemWidget(current, 1);
        if (widget == nullptr)
            return;
        auto* checkBox = static_cast<QCheckBox*>(widget);
        if (checkBox == nullptr)
            return;
        checkBox->setChecked(!checkBox->isChecked());
    });
    connect(ui.pushButtonRefresh,   &QPushButton::clicked, this, &CreateRectangularGridDialog::slotPushButtonRefreshClicked);
    connect(ui.pushButtonEdit,      &QPushButton::clicked, this, &CreateRectangularGridDialog::slotPushButtonEditClicked);
    connect(ui.pushButtonDelete,    &QPushButton::clicked, this, &CreateRectangularGridDialog::slotPushButtonDeleteClicked);

    //关闭Z轴参数设置
    connect(ui.checkBoxCloseZAxis, &QCheckBox::toggled, [=](bool bChecked)
    {
        bool bZAxisEnable = !bChecked;
        //true为关闭,false为打开
        ui.lineEditLabelZ->setEnabled(bZAxisEnable);
        ui.textEditIDsZ->setEnabled(bZAxisEnable);
        ui.textEditOffsetsZ->setEnabled(bZAxisEnable);
        // 设置Z轴当前状态,决定是否自动填充Z轴
        _pAutofillGridDialog->setZAxisEnabled(bZAxisEnable);
    });

    _nameHelpter.setLineEdit(ui.lineEditName);
    _nameHelpterX.setLineEdit(ui.lineEditLabelX);
    _nameHelpterY.setLineEdit(ui.lineEditLabelY);
    _nameHelpterZ.setLineEdit(ui.lineEditLabelZ);
}
CreateRectangularGridDialog::~CreateRectangularGridDialog()
{
    if (_pAutofillGridDialog != nullptr)
    {
        delete _pAutofillGridDialog;
        _pAutofillGridDialog = nullptr;
    }   
    if (_pWrtWidget != nullptr)
    {
        delete _pWrtWidget;
        _pWrtWidget = nullptr;
    }
}

void CreateRectangularGridDialog::showEvent(QShowEvent*)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    // 默认WRT节点为世界根节点
    _pWrtWidget->setSelectedNode(WD::Core().getBMDesign().root());

    _nodes.clear();
    auto pNode = _app.nodeTree().currentNode();
    if (pNode != nullptr)
    {
        // 当前节点不是GRIDWL节点或其子孙节点,如果不是则报错
        auto pTNode = pNode;
        while (pTNode != nullptr) 
        {
            if (pTNode->isType(TypeName_GRIDWL)) 
                break;
            pTNode = pTNode->parent();
        }
        // 说明不是轴网系统的子孙节点
        if (pTNode != nullptr)
            _nodes.push_back(pNode);
    }
    this->updateEditType();
}

void CreateRectangularGridDialog::hideEvent(QHideEvent*)
{
    _positionCaptureHelpter.exit(true);
}

void CreateRectangularGridDialog::slotOkButtonClicked()
{
    // 仅系统/项目管理员/专业组管理员才能创建轴网
    if (!bIsAdmin)
    {
        WD_WARN_T("CreateRectangularGridDialog", "Only admin can create GRIDSY");
        return;
    }

    switch (_editType)
    {
    case CreateRectangularGridDialog::EditType::ET_Create:
        {
            this->axisNetCreate();
        }
        break;
    case CreateRectangularGridDialog::EditType::ET_EditSingle:
        {
            this->axisNetEditSingle();
        }
        break;
    case CreateRectangularGridDialog::EditType::ET_EditMulti:
        {
            this->axisNetEditMulti();
        }
        break;
    default:
        break;
    }
    // 调整/创建轴网后重新绘制
    _app.needRepaint();
}

void CreateRectangularGridDialog::slotCancelButtonClicked()
{
    this->reject();
}

void CreateRectangularGridDialog::slotAutofillButtonClicked()
{
    if (_pAutofillGridDialog->isHidden())
        _pAutofillGridDialog->show();
    else
        _pAutofillGridDialog->activateWindow();
}

WD::WDNode::SharedPtr CreateRectangularGridDialog::currentNode()
{
    if (_nodes.size() == 0)
    {
        return nullptr;
    }
    return _nodes[0].lock();
}

void CreateRectangularGridDialog::slotChangeDisplaySet()
{
    QRadioButton* pSender = dynamic_cast<QRadioButton*>(QObject::sender());
    if (pSender == nullptr)
        return;

    //测试代码
    auto pNode = currentNode();
    if (pNode == nullptr)
        return;
    WD::WDBMDAuxiUtils::LabelDisplayType type = WD::WDBMDAuxiUtils::LabelDisplayType::LDT_None;
    if (pSender == ui.radioButtonAxes)
        type = WD::WDBMDAuxiUtils::LabelDisplayType::LDT_AxesOnly;
    else if (pSender == ui.radioButtonGridline)
        type = WD::WDBMDAuxiUtils::LabelDisplayType::LDT_GridlineIDs;
    else if (pSender == ui.radioButtonSpacings)
        type = WD::WDBMDAuxiUtils::LabelDisplayType::LDT_Spacings;
    else if (pSender == ui.radioButtonCoordinates)
        type = WD::WDBMDAuxiUtils::LabelDisplayType::LDT_Coordinates;

    if (type == WD::WDBMDAuxiUtils::LabelDisplayType::LDT_None)
        return assert(false);

    WD::WDBMDAuxiUtils::SetLabelDisplayType(*pNode, type);
}

void CreateRectangularGridDialog::slotSetViewClicked()
{
    auto pNode = currentNode();
    if (pNode == nullptr)
        return;
    auto& aabb = pNode->aabb();
    if (!aabb.isNull())
        _app.viewer().lookAtAabb(aabb);
}

void CreateRectangularGridDialog::slotShowAutofillData(const std::vector<AutofillRectangularGridDialog::AutofillMsg>& lstAutoData)
{
    this->clearOptionsWidgetData();
    if (lstAutoData.size() < 2)
        return;
    _nameHelpterX.setName(lstAutoData.at(0).label);
    for (int i = 0; i < lstAutoData.at(0).lstIds.size(); ++i)
    {
        ui.textEditIDsX->append(lstAutoData.at(0).lstIds.at(i));
        ui.textEditOffsetsX->append(lstAutoData.at(0).lstOffset.at(i));
    }

    _nameHelpterY.setName(lstAutoData.at(1).label);
    for (int i = 0; i < lstAutoData.at(1).lstIds.size(); ++i)
    {
        ui.textEditIDsY->append(lstAutoData.at(1).lstIds.at(i));
        ui.textEditOffsetsY->append(lstAutoData.at(1).lstOffset.at(i));
    }
    if (lstAutoData.size() > 2)
    {
        _nameHelpterZ.setName(lstAutoData.at(2).label);
        for (int i = 0; i < lstAutoData.at(2).lstIds.size(); ++i)
        {
            ui.textEditIDsZ->append(lstAutoData.at(2).lstIds.at(i));
            ui.textEditOffsetsZ->append(lstAutoData.at(2).lstOffset.at(i));
        }
    }
    else
    {
        ui.textEditIDsZ->clear();
        ui.textEditOffsetsZ->clear();
    }
}

void CreateRectangularGridDialog::slotPushButtonEditClicked()
{
    _nodes.clear();
    if (ui.treeWidget->topLevelItemCount() != 0)
    {
        for (int idx = 0; idx < ui.treeWidget->topLevelItemCount(); ++idx)
        {
            auto item = ui.treeWidget->topLevelItem(idx);
            if (item == nullptr)
                return;

            auto* topWidget = ui.treeWidget->itemWidget(item, 1);
            if (topWidget == nullptr)
                continue;
            for (int index = 0; index < item->childCount(); ++index)
            {
                auto each = item->child(index);
                if (each == nullptr)
                    continue;
                auto* widget = ui.treeWidget->itemWidget(each, 1);
                if (widget == nullptr)
                    continue;
                auto* checkBox = static_cast<QCheckBox*>(widget);
                if (checkBox == nullptr || !checkBox->isChecked())
                    continue;
                WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(each->data(0, Qt::UserRole).value<UiWeakObject>().object());
                if (pNode == nullptr)
                    continue;
                _nodes.push_back(pNode);
            }
        }
    }
    if (_nodes.empty())
    {
        auto pNode = _app.nodeTree().currentNode();
        if (pNode == nullptr)
        {
            WD_WARN_T("CreateRectangularGridDialog", "The selected node is null");
            return;
        }
        // 当前节点不是GRIDWL节点或其子孙节点,如果不是则报错
        auto pTNode = pNode;
        while (pTNode != nullptr) 
        {
            if (pTNode->isAnyOfType(TypeName_GRIDSY, TypeName_GRIDWL)) 
                break;
            pTNode = pTNode->parent();
        }
        // 说明不是轴网系统的子孙节点
        if (pTNode == nullptr)
        {
            WD_WARN_T("CreateRectangularGridDialog", "The selected node is invalid");
            return;
        }
        _nodes.push_back(pTNode);
    }
    updateEditType();
}

void CreateRectangularGridDialog::slotPushButtonRefreshClicked()
{
    ui.treeWidget->clear();
    auto root = _app.getBMDesign().root();
    if (root == nullptr)
        return;
    for (auto& pGRIDWLNode : root->children())
    {
        if (pGRIDWLNode != nullptr && pGRIDWLNode->isType(TypeName_GRIDWL))
        {
            auto* item = new QTreeWidgetItem();
            item->setText(0, pGRIDWLNode->name().c_str());
            QVariant topUserData;
            topUserData.setValue(UiWeakObject(pGRIDWLNode));
            item->setData(0, Qt::UserRole, topUserData);
            ui.treeWidget->addTopLevelItem(item);
            for (auto& pGRIDSYNode : pGRIDWLNode->children())
            {
                if (pGRIDSYNode != nullptr && pGRIDSYNode->isType(TypeName_GRIDSY))
                {
                    auto* subItem = new QTreeWidgetItem();
                    subItem->setText(0, pGRIDSYNode->name().c_str());
                    QVariant userData;
                    userData.setValue(UiWeakObject(pGRIDSYNode));
                    subItem->setData(0, Qt::UserRole, userData);
                    item->addChild(subItem);
                    auto checkBox = new QCheckBox(ui.treeWidget);
                    connect(checkBox, &QCheckBox::toggled, [&](bool bChecked){
                        if (bChecked)
                            return;
                        auto item = ui.treeWidget->currentItem();
                        while (item != nullptr)
                        {
                            auto* itemWidget = ui.treeWidget->itemWidget(item, 1);
                            if (itemWidget == nullptr)
                                return;
                            auto* itemCheckBox = static_cast<QCheckBox*>(itemWidget);
                            if (itemCheckBox == nullptr)
                                return;
                            itemCheckBox->blockSignals(true);
                            itemCheckBox->setChecked(false);
                            itemCheckBox->blockSignals(false);
                            item = item->parent();
                        }
                    });
                    ui.treeWidget->setItemWidget(subItem, 1, checkBox);
                }
            }
            auto checkBox = new QCheckBox(ui.treeWidget);
            ui.treeWidget->setItemWidget(item, 1, checkBox);
            connect(checkBox, &QCheckBox::toggled, [&](bool bChecked){
                auto item = ui.treeWidget->currentItem();
                if (item == nullptr)
                    return;
                for (int i = 0; i < item->childCount(); ++i)
                {
                    auto each = item->child(i);
                    if (each == nullptr)
                        continue;
                    auto* widget = ui.treeWidget->itemWidget(each, 1);
                    if (widget == nullptr)
                        continue;
                    auto* checkBox = static_cast<QCheckBox*>(widget);
                    if (checkBox == nullptr)
                        continue;
                    checkBox->setChecked(bChecked);
                }
            });
        }
    }
}

void CreateRectangularGridDialog::slotPushButtonDeleteClicked()
{
    if (ui.treeWidget->topLevelItemCount() == 0)
    {
        WD_WARN_T("CreateRectangularGridDialog", "CurrentListIsNull!");
        return;
    }
    if (WD_QUESTION_T("CreateRectangularGridDialog", "isDel") != 0)
        return;
    WD::WDNode::Nodes nodes;
    for (int idx = 0; idx < ui.treeWidget->topLevelItemCount(); ++idx)
    {
        auto item = ui.treeWidget->topLevelItem(idx);
        if (item == nullptr)
            return;

        auto* topWidget = ui.treeWidget->itemWidget(item, 1);
        if (topWidget == nullptr)
            continue;
        auto* topCheckBox = static_cast<QCheckBox*>(topWidget);
        if (topCheckBox == nullptr)
            continue;
        if (topCheckBox->isChecked())
        {
            auto topItem = ui.treeWidget->takeTopLevelItem(idx);
            if (topItem != nullptr)
            {
                WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(topItem->data(0, Qt::UserRole).value<UiWeakObject>().object());
                if (pNode == nullptr)
                    continue;
                nodes.push_back(pNode);
                delete topItem;
            }
            // 删掉了顶层item,idx-1
            --idx;
            continue;
        }
        for (int index = 0; index < item->childCount(); ++index)
        {
            auto each = item->child(index);
            if (each == nullptr)
                continue;
            auto* widget = ui.treeWidget->itemWidget(each, 1);
            if (widget == nullptr)
                continue;
            auto* checkBox = static_cast<QCheckBox*>(widget);
            if (checkBox == nullptr || !checkBox->isChecked())
                continue;
            WD::WDNode::SharedPtr pNode = WD::WDNode::SharedCast(each->data(0, Qt::UserRole).value<UiWeakObject>().object());
            if (pNode == nullptr)
                continue;
            nodes.push_back(pNode);
            item->removeChild(each);
            if (each != nullptr)
                delete each;
            index--;
        }
    }

    if (nodes.empty())
    {
        WD_WARN_T("CreateRectangularGridDialog", "CurrentNotSelectNode");
        return;
    }
    DeleteGivenNodes(nodes, _app, "CreateRectangularGridDialog", false);
}

void CreateRectangularGridDialog::updateEditType()
{
    // 获取所有的有效节点
    WD::WDNode::Nodes validNodes;
    validNodes.reserve(_nodes.size());
    for (auto& pWNode : _nodes)
    {
        auto pNode = pWNode.lock();
        if (pNode != nullptr)
            validNodes.push_back(pNode);
    }
    if (validNodes.empty())
    {
        this->setWindowTitle(QString::fromUtf8(WD::WDTs("CreateRectangularGridDialog", "title").c_str()));
        ui.lineEditNameTwo->clear();
        this->clearWidgetData();
        _editType = EditType::ET_Null;
        return;
    }

    // 编辑单个节点
    if (validNodes.size() == 1)
    {
        // 保存操作节点
        auto& pNode = validNodes.front();
        // 判断当前节点类型是否为GRINLN、GRIDAX、GRIDSY中的一种
        auto axisState = !_handleAxisNet.judgeNodesType(pNode);
        // 创建轴网模式
        if (axisState)
        {
            this->setWindowTitle(QString::fromUtf8(WD::WDTs("CreateRectangularGridDialog", "title").c_str()));
            ui.lineEditNameTwo->clear();
            this->clearWidgetData();
            this->genNewName();
            ui.pushButtonSave->setText(QString::fromUtf8(WD::WDTs("CreateRectangularGridDialog", "Create").c_str()));
            _editType = EditType::ET_Create;
        }
        // 编辑轴网模式
        else
        {
            this->setWindowTitle(QString::fromUtf8(WD::WDTs("CreateRectangularGridDialog", "editTitle").c_str()));
            //获取选中的轴网系统数据显示在界面上
            AxisNetMsg selAxisMsg = _handleAxisNet.getSelectAxisNetNodesData(pNode);
            // 显示获取的轴网系统数据
            this->showSelectAxisNetNodesData(selAxisMsg);
            // 根据关闭Z轴状态控制Z轴的GRINLN层显示
            /*_handleAxisNet.setAxisZHidden(pNode, ui.checkBoxCloseZAxis->isChecked());*/
            ui.pushButtonSave->setText(QString::fromUtf8(WD::WDTs("CreateRectangularGridDialog", "Save").c_str()));
            _editType = EditType::ET_EditSingle;
        }
    }
    // 编辑多个节点
    else
    {
        this->setWindowTitle(QString::fromUtf8(WD::WDTs("CreateRectangularGridDialog", "editTitle").c_str()));
        this->clearWidgetData();
        this->genNewName();
        ui.pushButtonSave->setText(QString::fromUtf8(WD::WDTs("CreateRectangularGridDialog", "Save").c_str()));
        _editType = EditType::ET_EditMulti;
    }
}

CreateRectangularGridDialog::AxisNetMsg CreateRectangularGridDialog::getAxisNetNodes()
{
    std::string str = "";
    switch (_editType)
    {
    case EditType::ET_Create:
        str = "createAxisFailed";
        break;
    case EditType::ET_EditSingle:
    case EditType::ET_EditMulti:
        str = "editAxisFailed";
        break;
    default:
        break;
    }
    // 获取界面X、Y轴上编号与偏移量的值
    QStringList lstAxisIdsX = ui.textEditIDsX->toPlainText().split("\n");
    QStringList lstOffsetsX = ui.textEditOffsetsX->toPlainText().split("\n");
    QStringList lstAxisIdsY = ui.textEditIDsY->toPlainText().split("\n");
    QStringList lstOffsetsY = ui.textEditOffsetsY->toPlainText().split("\n");
    QStringList lstAxisIdsZ = ui.textEditIDsZ->toPlainText().split("\n");
    QStringList lstOffsetsZ = ui.textEditOffsetsZ->toPlainText().split("\n");
    lstAxisIdsX.removeAll(QString(""));
    lstOffsetsX.removeAll(QString(""));
    lstAxisIdsY.removeAll(QString(""));
    lstOffsetsY.removeAll(QString(""));
    lstAxisIdsZ.removeAll(QString(""));
    lstOffsetsZ.removeAll(QString(""));
    // 判断偏移量与编号个数是否相等
    if (lstAxisIdsX.size() != lstOffsetsX.size() || lstAxisIdsY.size() != lstOffsetsY.size() || lstAxisIdsZ.size() != lstOffsetsZ.size())
    {
        WD_WARN_T("CreateRectangularGridDialog", str);
        return AxisNetMsg();
    }
    //判断编号是否有重复
    if (_handleAxisNet.judgeIsRepetition(lstAxisIdsX)
        || _handleAxisNet.judgeIsRepetition(lstAxisIdsY) || _handleAxisNet.judgeIsRepetition(lstAxisIdsZ))
    {
        WD_WARN_T("CreateRectangularGridDialog", str);
        return AxisNetMsg();
    }

    // X轴信息<偏移量, 名称>
    std::map<double, std::string> mapXGRINLN;
    bool bOk = false;
    for (int i = 0; i < lstAxisIdsX.size(); ++i)
    {
        double offset = lstOffsetsX.at(i).toDouble(&bOk);
        // 数据重复, 偏移量为以及数据转换失败为错误情况
        if (mapXGRINLN.find(offset) != mapXGRINLN.end() || offset < 0.0 || !bOk)
        {
            WD_WARN_T("CreateRectangularGridDialog", str);
            return AxisNetMsg();
        }
        mapXGRINLN.emplace(offset, lstAxisIdsX.at(i).toUtf8().toStdString());
    }
    HandleAxisNet::CreateAxisNetMsg axisX;
    axisX.strAxisName = _nameHelpter.name();
    axisX.strLabel = _nameHelpterX.name();
    axisX.strAxis = "X";
    axisX.mapIdOffset = mapXGRINLN;

    // Y轴信息<偏移量, 名称>
    std::map<double, std::string> mapYGRINLN;
    for (int j = 0; j < lstAxisIdsY.size(); ++j)
    {
        double offset = lstOffsetsY.at(j).toDouble(&bOk);
        // 数据重复, 偏移量为以及数据转换失败为错误情况
        if (mapYGRINLN.find(offset) != mapYGRINLN.end() || offset < 0.0 || !bOk)
        {
            WD_WARN_T("CreateRectangularGridDialog", str);
            return AxisNetMsg();
        }
        mapYGRINLN.emplace(offset, lstAxisIdsY.at(j).toUtf8().toStdString());
    }
    HandleAxisNet::CreateAxisNetMsg axisY;
    axisY.strAxisName = _nameHelpter.name();
    axisY.strLabel = _nameHelpterY.name();
    axisY.strAxis = "Y";
    axisY.mapIdOffset = mapYGRINLN;

    AxisNetMsg createMsg;
    createMsg.emplace_back(axisX);
    createMsg.emplace_back(axisY);

    // 取消关闭Z轴,才获取界面上Z轴信息
    if (!ui.checkBoxCloseZAxis->isChecked())
    {
        // Z轴信息<偏移量, 名称>
        std::map<double, std::string> mapZGRINLN;
        for (int i = 0; i < lstAxisIdsZ.size(); ++i)
        {
            double offset = lstOffsetsZ.at(i).toDouble(&bOk);
            // 数据重复, 偏移量为以及数据转换失败为错误情况
            if (mapZGRINLN.find(offset) != mapZGRINLN.end() || offset < 0.0 || !bOk)
            {
                WD_WARN_T("CreateRectangularGridDialog", str);
                return AxisNetMsg();
            }
            mapZGRINLN.emplace(offset, lstAxisIdsZ.at(i).toUtf8().toStdString());
        }
        HandleAxisNet::CreateAxisNetMsg axisZ;
        axisZ.strAxisName = _nameHelpter.name();
        axisZ.strLabel = _nameHelpterZ.name();
        axisZ.strAxis = "Z";
        axisZ.mapIdOffset = mapZGRINLN;
        createMsg.emplace_back(axisZ);
    }
    return createMsg;
}

void CreateRectangularGridDialog::adjustAxisNetPositionOrientation()
{
    WD::WDNode::SharedPtr pNode = currentNode();
    if (pNode == nullptr)
        return;
    //  权限校验
    if (!_app.getBMDesign().permissionMgr().check(*pNode))
    {
        WD_WARN_T("CreateRectangularGridDialog", "You cannot operate the current node!");
        return;
    }
    // 申领对象
    bool bCancelMd = false;
    if (!_app.getBMDesign().claimMgr().checkUpdate(pNode, { "Position" , "Orientation" }, bCancelMd))
        return;
    if (bCancelMd)
        return;
    if (!pNode->isType(TypeName_GRIDSY))
    {
        WD_WARN_T("CreateRectangularGridDialog", "selAxisSystemNode");
        return;
    }

    const WD::WDBMAttrValue gPosVal = WD::WDBMAttrValue(_positionCaptureHelpter.position());

    WD::DVec3 dirctionX = WD::DVec3(ui.doubleSpinBoxXX->value(), ui.doubleSpinBoxXY->value(), ui.doubleSpinBoxXZ->value());
    WD::DVec3 dirctionY = WD::DVec3(ui.doubleSpinBoxYX->value(), ui.doubleSpinBoxYY->value(), ui.doubleSpinBoxYZ->value());

    // 检测轴网基向量是否存在 共线 或 包含零向量 的情况
    auto rCrossV = WD::DVec3::Cross(dirctionX, dirctionY);
    WD::DQuat matR = pNode->getAttribute("Orientation WRT Owner").toQuat();
    if (rCrossV.lengthSq() > WD::NumLimits<float>::Epsilon)
        matR = WD::DMat3::MakeRotationXY(dirctionX, dirctionY).toQuat();

    const WD::WDBMAttrValue matRVal = WD::WDBMAttrValue(matR);

    auto cmdPosChange = WD::WDBMBase::MakeAttributeSetCommand(pNode, "Position WRT World", gPosVal);
    auto cmdOriChange = WD::WDBMBase::MakeAttributeSetCommand(pNode, "Orientation WRT Owner", matRVal);
    if (cmdPosChange != nullptr && cmdOriChange != nullptr)
    {
        _app.undoStack().beginMarco("CreateRectangularGrid");
        cmdPosChange->setNoticeAfterUndo([pNode] (const WD::WDUndoCommand& sender)
        {
            WDUnused(sender);
            if (pNode != nullptr)
                pNode->update();
        });

        _app.undoStack().push(cmdPosChange);
        _app.undoStack().push(cmdOriChange);


        cmdOriChange->setNoticeAfterRedo([pNode] (const WD::WDUndoCommand& sender)
        {
            WDUnused(sender);
            if (pNode != nullptr)
                pNode->update();
        });
        _app.undoStack().endMarco();
    }
    else
    {
        if (cmdPosChange != nullptr)
        {
            delete cmdPosChange;
            cmdPosChange = nullptr;
        }
        if (cmdOriChange != nullptr)
        {
            delete cmdOriChange;
            cmdOriChange = nullptr;
        }
        // 修改轴网方向 
        // 修改轴网基点坐标
        pNode->setAttribute("Position WRT World", gPosVal);
        pNode->setAttribute("Orientation WRT Owner", matRVal);
        pNode->update();
    }
}

void CreateRectangularGridDialog::clearWidgetData()
{
    clearOptionsWidgetData();
    clearPositionAndOrientationWidgetData();
}

void CreateRectangularGridDialog::clearOptionsWidgetData()
{
    ui.textEditIDsX->clear();
    ui.textEditOffsetsX->clear();
    ui.textEditIDsY->clear();
    ui.textEditOffsetsY->clear();
    ui.textEditIDsZ->clear();
    ui.textEditOffsetsZ->clear();
    //默认标签名X、Y、EL
    _nameHelpterX.setName(QString::fromUtf8("X"));
    _nameHelpterY.setName(QString::fromUtf8("Y"));
    _nameHelpterZ.setName(QString::fromUtf8("EL"));
}

void CreateRectangularGridDialog::clearPositionAndOrientationWidgetData()
{
    _positionCaptureHelpter.exit(true);
}

void CreateRectangularGridDialog::showSelectAxisNetNodesData(const AxisNetMsg& selAxisMsg)
{
    // 清空界面数据
    this->clearWidgetData();
    if (selAxisMsg.size() < 2)
        return;
    // 保持轴网系统名称
    _axisSystemName = selAxisMsg.at(0).strAxisName;
    //显示标签名
    _nameHelpterX.setName(QString::fromUtf8(selAxisMsg.at(0).strLabel.data()));
    _nameHelpterY.setName(QString::fromUtf8(selAxisMsg.at(1).strLabel.data()));
    //显示轴网系统名
    _nameHelpter.setName(QString::fromUtf8(selAxisMsg.at(0).strAxisName.c_str()));
    ui.lineEditNameTwo->setText(QString::fromUtf8(selAxisMsg.at(0).strAxisName.c_str()));
    //显示轴网编号与偏移量
    for (auto itId = selAxisMsg.at(0).mapIdOffset.begin(); itId != selAxisMsg.at(0).mapIdOffset.end(); itId++)
    {
        ui.textEditIDsX->append(QString::fromUtf8(itId->second.data()));
        ui.textEditOffsetsX->append(QString::number(itId->first));
    }
    for (auto itId = selAxisMsg.at(1).mapIdOffset.begin(); itId != selAxisMsg.at(1).mapIdOffset.end(); itId++)
    {
        ui.textEditIDsY->append(QString::fromUtf8(itId->second.data()));
        ui.textEditOffsetsY->append(QString::number(itId->first));
    }
    // 有Z轴
    if (selAxisMsg.size() > 2)
    {
        for (auto itId = selAxisMsg.at(2).mapIdOffset.begin(); itId != selAxisMsg.at(2).mapIdOffset.end(); itId++)
        {
            ui.textEditIDsZ->append(QString::fromUtf8(itId->second.data()));
            if (itId->second.empty())
                ui.textEditOffsetsZ->append("");
            else
                ui.textEditOffsetsZ->append(QString::number(itId->first));
        }
        _nameHelpterZ.setName(QString::fromUtf8(selAxisMsg.at(2).strLabel.data()));
    }
    else
    {
        ui.textEditIDsZ->clear();
        ui.textEditOffsetsZ->clear();
    }
    //更改当前显示设置
    this->setDisplay(selAxisMsg.at(0).displayFlag);

    if (currentNode() == nullptr)
        return;
    //获取轴网系统节点
    auto pCurGRIDSY = _app.getBMDesign().findParentWithType(*currentNode(), TypeName_GRIDAX);

    _positionCaptureHelpter.exit(true);
    if (pCurGRIDSY != nullptr)
    {
        _positionCaptureHelpter.setPosition(pCurGRIDSY->getAttribute("Position WRT World").toDVec3());

        WD::DMat3 mGRIDSY = WD::DMat3::FromQuat(pCurGRIDSY->getAttribute("Orientation").toQuat());
        ui.doubleSpinBoxXX->setValue(mGRIDSY.axisX().x);
        ui.doubleSpinBoxXY->setValue(mGRIDSY.axisX().y);
        ui.doubleSpinBoxXZ->setValue(mGRIDSY.axisX().z);
        ui.doubleSpinBoxYX->setValue(mGRIDSY.axisY().x);
        ui.doubleSpinBoxYY->setValue(mGRIDSY.axisY().y);
        ui.doubleSpinBoxYZ->setValue(mGRIDSY.axisY().z);
        ui.doubleSpinBoxZX->setValue(mGRIDSY.axisZ().x);
        ui.doubleSpinBoxZY->setValue(mGRIDSY.axisZ().y);
        ui.doubleSpinBoxZZ->setValue(mGRIDSY.axisZ().z);
    }
    else
    {
        ui.doubleSpinBoxXX->setValue(0.0);
        ui.doubleSpinBoxXY->setValue(0.0);
        ui.doubleSpinBoxXZ->setValue(0.0);
        ui.doubleSpinBoxYX->setValue(0.0);
        ui.doubleSpinBoxYY->setValue(0.0);
        ui.doubleSpinBoxYZ->setValue(0.0);
        ui.doubleSpinBoxZX->setValue(0.0);
        ui.doubleSpinBoxZY->setValue(0.0);
        ui.doubleSpinBoxZZ->setValue(0.0);
    }
}

void CreateRectangularGridDialog::setDisplay(int flag)
{
    switch (flag)
    {
    case 1:
        ui.radioButtonAxes->setChecked(true);
        break;
    case 2:
        ui.radioButtonGridline->setChecked(true);
        break;
    case 3:
        ui.radioButtonSpacings->setChecked(true);
        break;
    case 4:
        ui.radioButtonCoordinates->setChecked(true);
        break;
    default:
        break;
    }
}

WD::WDNode::SharedPtr CreateRectangularGridDialog::createAxisNetNode(WD::WDNode::SharedPtr pGRIDWLNode, const AxisNetMsg& lstAxisData)
{
    if (pGRIDWLNode == nullptr)
        return nullptr;
    WD::WDBMDesign& mgr = _app.getBMDesign();
    std::string name = lstAxisData.at(0).strAxisName;

    WD::WDNode::SharedPtr newGRIDSYNode = nullptr;
    //if (_axisState)
    //{
    //    // 轴网系统名称不能为空,轴网创建规则
    //    if (name.empty())
    //    {
    //        WD_ERROR_T("CreateRectangularGridDialog", "Create failed, GRIDSY name can not empty");
    //        return nullptr;
    //    }
    //    if (!_app.getBMDesign().nameValid(name))
    //    {
    //        WD_ERROR_T("CreateRectangularGridDialog", "Create failed, GRIDSY name is not Valid");
    //        return nullptr;
    //    }
    //    // 校验轴网系统名称不能重复
    //    if (mgr.nameExists(name))
    //    {
    //        WD_WARN_T("CreateRectangularGridDialog", "SameNameExists");
    //        return nullptr;
    //    }

    //    // 新建轴网系统节点
    //    newGRIDSYNode = mgr.create(pGRIDWLNode, TypeName_GRIDSY, name);
    //    if (newGRIDSYNode == nullptr)
    //    {
    //        WD_WARN_T("CreateRectangularGridDialog", "CreateNodeFailed");
    //        return nullptr;
    //    }
    //}
    //else
    //{
    //    newGRIDSYNode = pGRIDWLNode;
    //}

    if (newGRIDSYNode == nullptr)
        return nullptr;

    // 目前仅支持创建矩形轴网,默认为AXIS
    newGRIDSYNode->setAttribute("Purpose", WD::WDBMAttrValue(WD::WDBMWord("AXIS")));

    bool xyCreateSFlag = true;
    for (int i = 0; i < lstAxisData.size(); ++i)
    {
        // PXXS 创建轴的逻辑：
        // 拼接的名称若重复，当前节点使用默认名称创建，后续轴节点不再创建
        
        // 轴的名称 = 轴网系统名称+X/Y/Z
        std::string newGRIDAXNodeName = lstAxisData.at(0).strAxisName + lstAxisData.at(i).strAxis;
        if (newGRIDAXNodeName.empty() || mgr.nameExists(newGRIDAXNodeName))
        {
            // 使用默认名称创建轴，并中断后续创建轴线和其他轴
            WD::WDNode::SharedPtr newGRIDAXNode = mgr.create(newGRIDSYNode, TypeName_GRIDAX);
            {
                std::string info = newGRIDAXNodeName + WD::WDTs("CreateRectangularGridDialog", "AxisNameIsExist");
                WD_WARN(info);
            }
            xyCreateSFlag = false;
            break;
        }

        // 新建轴（GRIDAX）节点
        WD::WDNode::SharedPtr newGRIDAXNode = mgr.create(newGRIDSYNode, TypeName_GRIDAX, newGRIDAXNodeName);
        if (newGRIDAXNode == nullptr)
        {
            WD_WARN_T("CreateRectangularGridDialog", "CreateNodeFailed");
            return nullptr;
        }
        newGRIDAXNode->setAttribute("GRDLBL", lstAxisData.at(i).strLabel);
        newGRIDAXNode->setAttribute("GRDAXE", lstAxisData.at(i).strAxis);

        // 创建轴的子节点轴线 
        for (auto iter = lstAxisData.at(i).mapIdOffset.begin(); iter != lstAxisData.at(i).mapIdOffset.end(); iter++)
        {
            QString strName = QString::fromUtf8((lstAxisData.at(0).strAxisName + lstAxisData.at(i).strAxis + iter->second).data());
            std::string newGRINLNNodeName = strName.toUtf8().data();
            WD::WDNode::SharedPtr newGRINLNNode = mgr.create(newGRIDAXNode, TypeName_GRIDLN, newGRINLNNodeName);
            if (newGRINLNNode == nullptr)
            {
                WD_WARN_T("CreateRectangularGridDialog", "CreateNodeFailed");
                return nullptr;
            }
            newGRINLNNode->setAttribute("GRDOFF", iter->first);
            newGRINLNNode->setAttribute("GRDID", iter->second);
        }
    }
    // 此时关闭Z轴,则只创建GRIDAX层(只创建Z轴，不创建Z轴的轴线)
    // 只有XY轴任意轴未创建成功都会中断Z轴的创建
    if (xyCreateSFlag && lstAxisData.size() != 3)
    {
        QString strAxisNameX = QString::fromUtf8((lstAxisData.at(0).strAxisName + "Z").data());
        std::string newGRIDAXNodeName = strAxisNameX.toUtf8().data();
        WD::WDNode::SharedPtr newGRIDAXNode = mgr.create(newGRIDSYNode, TypeName_GRIDAX, newGRIDAXNodeName);
        if (newGRIDAXNode == nullptr)
        {
            WD_WARN_T("CreateRectangularGridDialog", "CreateNodeFailed");
            return nullptr;
        }
        newGRIDAXNode->setAttribute("GRDAXE", "Z");

    }
    //创建一个网格的材质组件
    newGRIDSYNode->setAttribute("AutoColor", WD::Color(WD::Color::lawnGreen));

    newGRIDSYNode->triggerUpdate();
    _app.nodeTree().setCurrentNode(newGRIDSYNode);

    return newGRIDSYNode;
}

void CreateRectangularGridDialog::genNewName()
{
    auto pCurrentNode = currentNode();
    if (pCurrentNode == nullptr)
        return;
    auto& mgr = _app.getBMDesign();
    if (pCurrentNode == mgr.root())
    {
        for (auto& pChild : pCurrentNode->children())
        {
            if (pChild != nullptr && pChild->isType(TypeName_GRIDWL))
            {
                auto pParentNode = mgr.findParentWithType(*pChild, TypeName_GRIDSY);
                // 如果父节点有效，根据父节点更新将要创建的轴网的名称
                _nameHelpter.resetName();
                return;
            }
        }
    }
    auto pParentNode = mgr.findParentWithType(*pCurrentNode, TypeName_GRIDSY);
    // 如果父节点有效，根据父节点更新将要创建的轴网的名称
    _nameHelpter.resetName();
}


void CreateRectangularGridDialog::axisNetCreate()
{
    // 判断选项页数据是否填充，未填充则无法创建
    if (ui.textEditIDsX->toPlainText().split("\n").size() < 2 || ui.textEditOffsetsX->toPlainText().split("\n").size() < 2
        ||  ui.textEditIDsY->toPlainText().split("\n").size() < 2 || ui.textEditOffsetsY->toPlainText().split("\n").size() < 2)
    {
        WD_WARN_T("CreateRectangularGridDialog", "dataNotEnough");
        return;
    }
    if ((!ui.checkBoxCloseZAxis->isChecked())
        && (ui.textEditIDsZ->toPlainText().split("\n").size() < 2 || ui.textEditOffsetsZ->toPlainText().split("\n").size() < 2))
    {
        WD_WARN_T("CreateRectangularGridDialog", "dataNotEnough");
        return;
    }
    // 创建轴网节点
    auto lstAxisData = this->getAxisNetNodes();
    // 轴网至少有X、Y轴数据
    if (lstAxisData.size() < 2)
        return;
    auto& mgr = _app.getBMDesign();
    WD::WDNode::SharedPtr rootNode = mgr.root();
    if (rootNode == nullptr)
        return;
    //if (pGRIDWLNode == nullptr)
    //{
    //    // 先尝试在根节点下查找轴网管理节点
    //    for (auto& pChild : rootNode->children())
    //    {
    //        if (pChild != nullptr && pChild->isType(TypeName_GRIDWL))
    //        {
    //            pGRIDWLNode = pChild;
    //            break;
    //        }
    //    }
    //    // 没找到轴网管理节点,则创建
    //    if (pGRIDWLNode == nullptr)
    //    {
    //        // 申领对象
    //        if (!WD::Core().getBMDesign().claimMgr().checkAdd(rootNode))
    //            return;
    //        pGRIDWLNode = mgr.create(rootNode, TypeName_GRIDWL);
    //        // 手动添加新创建的轴网管理节点，原因：新建节点在redo中添加到新增列表，但在这之前就在该节点下创建新节点了
    //        // 这就导致向服务器发送申领父节点请求时，服务器没有该节点，从而申领失败
    //        if (pGRIDWLNode != nullptr)
    //            _app.getBMDesign().claimMgr().newCreatedNodes().add({ pGRIDWLNode });
    //    }
    //}

    //// 申领对象
    //if (!WD::Core().getBMDesign().claimMgr().checkAdd(pGRIDWLNode))
    //    return;
    //this->createAxisNetNode(pGRIDWLNode, lstAxisData);

    //pGRIDWLNode->triggerUpdate(true);

    //if (_axisState)
    //{
    //    _app.nodeTree().setCurrentNode(pGRIDWLNode);

    //    auto cmdCreatedNode = _app.getBMDesign().makeCreatedCommand({pGRIDWLNode});
    //    WD::WDUndoCommand* cmdAddToScene = nullptr;
    //    if (ui.checkBoxAxisShowOrHide->isChecked())
    //        cmdAddToScene = _app.getBMDesign().makeSceneAddCommand({pGRIDWLNode});

    //    if (cmdCreatedNode != nullptr)
    //    {
    //        _app.undoStack().beginMarco("CreateRectangularGrid");
    //        _app.undoStack().push(cmdCreatedNode);
    //        if (cmdAddToScene != nullptr)
    //            _app.undoStack().push(cmdAddToScene);
    //        _app.undoStack().endMarco();
    //    }

    //}



    //this->addAxisNetNode(this->getAxisNetNodes(), nullptr);
    genNewName();
}
void CreateRectangularGridDialog::axisNetEditSingle()
{
    // 判断选项页数据是否填充，未填充则无法创建
    if (ui.textEditIDsX->toPlainText().split("\n").size() < 2 || ui.textEditOffsetsX->toPlainText().split("\n").size() < 2
        ||  ui.textEditIDsY->toPlainText().split("\n").size() < 2 || ui.textEditOffsetsY->toPlainText().split("\n").size() < 2)
    {
        WD_WARN_T("CreateRectangularGridDialog", "dataNotEnough");
        return;
    }
    if ((!ui.checkBoxCloseZAxis->isChecked())
        && (ui.textEditIDsZ->toPlainText().split("\n").size() < 2 || ui.textEditOffsetsZ->toPlainText().split("\n").size() < 2))
    {
        WD_WARN_T("CreateRectangularGridDialog", "dataNotEnough");
        return;
    }

    WD::WDNode::SharedPtr pNode = nullptr;
    for (auto& pWNode : _nodes)
    {
        pNode = pWNode.lock();
        if (pNode == nullptr)
        {
            assert(false);
            continue;
        }
        break;
    }
    if (pNode == nullptr)
    {
        WD_WARN_T("CreateRectangularGridDialog", "The selected node is null");
        return;
    }
    if (!pNode->isType(TypeName_GRIDSY))
    {
        WD_WARN_T("CreateRectangularGridDialog", "editAxisFailed");
        return;
    }

    // 编辑轴网
    // 获取现有值
    AxisNetMsg axisMsg = this->getAxisNetNodes();
    if (axisMsg.empty())
        return;
    // 获取当前设置的轴网系统节点名称并设置
    std::string strNewName = _nameHelpter.name();
    // 轴网系统名称不能为空,轴网创建规则
    if (strNewName.empty())
    {
        WD_ERROR_T("CreateRectangularGridDialog", "Edit failed, GRIDSY name can not empty");
        return;
    }
    if (!_app.getBMDesign().nameValid(strNewName))
    {
        WD_ERROR_T("CreateRectangularGridDialog", "Edit failed, GRIDSY name is not Valid");
        return;
    }

    // 校验strNewName是否存在
    if(pNode->name() != strNewName)
    {
        if (_app.getBMDesign().nameExists(strNewName))
        {
            // 若有相同名称,轴网系统节点恢复原来名称
            _nameHelpter.setName(QString::fromUtf8(_axisSystemName.c_str()));
            WD_WARN_T("CreateRectangularGridDialog", "Edit exist same name");
            return;
        }
        pNode->setAttribute("Name", strNewName);
    }

    // 删除轴网轴节点
    for (int index = 0, cnt = int(pNode->childCount());index < cnt;)
    {
        auto pChild = pNode->childAt(index);
        if ((pChild != nullptr) && pChild->isType(TypeName_GRIDAX))
            _app.getBMDesign().destroy(pChild);
        else
            index++;

        cnt = int(pNode->childCount());
    }

    // 显示设置
    if (ui.radioButtonAxes->isChecked())
        WD::WDBMDAuxiUtils::SetLabelDisplayType(*pNode, WD::WDBMDAuxiUtils::LDT_AxesOnly);
    else if (ui.radioButtonGridline->isChecked())
        WD::WDBMDAuxiUtils::SetLabelDisplayType(*pNode, WD::WDBMDAuxiUtils::LDT_GridlineIDs);
    else if (ui.radioButtonSpacings->isChecked())
        WD::WDBMDAuxiUtils::SetLabelDisplayType(*pNode, WD::WDBMDAuxiUtils::LDT_Spacings);
    else if (ui.radioButtonCoordinates->isChecked())
        WD::WDBMDAuxiUtils::SetLabelDisplayType(*pNode, WD::WDBMDAuxiUtils::LDT_Coordinates);
    //添加轴节点
    /*this->addAxisNetNode(axisMsg, pNode);*/
    this->adjustAxisNetPositionOrientation();
    pNode->update(true);

    
    

    _app.viewer().lookAtAabb(pNode->aabb());
}
void CreateRectangularGridDialog::axisNetEditMulti()
{

}



void CreateRectangularGridDialog::initDialog()
{
    _pWrtWidget = new UiNodeSelect(WD::Core(), this);
    ui.verticalLayoutWrt->addWidget(_pWrtWidget);
    //默认显示轴ID
    ui.radioButtonGridline->setChecked(true);
    //默认显示轴网
    ui.checkBoxAxisShowOrHide->setChecked(true);
    //默认关闭Z轴参数
    ui.checkBoxCloseZAxis->setChecked(true);
    _pAutofillGridDialog->setZAxisEnabled(false);
    ui.lineEditLabelZ->setEnabled(false);
    ui.textEditIDsZ->setEnabled(false);
    ui.textEditOffsetsZ->setEnabled(false);
    // 设置轴网方向的Z轴为只读
    ui.doubleSpinBoxZX->setReadOnly(true);
    ui.doubleSpinBoxZY->setReadOnly(true);
    ui.doubleSpinBoxZZ->setReadOnly(true);
}

void CreateRectangularGridDialog::retranslateUi()
{
    Trs("CreateRectangularGridDialog"
        , static_cast<QDialog*>(this)
        , ui.tabWidget
        , ui.radioButtonAxes
        , ui.radioButtonGridline
        , ui.radioButtonSpacings
        , ui.radioButtonCoordinates
        , ui.groupBoxAxisX
        , ui.groupBoxAxisY
        , ui.groupBoxAxisZ
        , ui.groupBoxOriAX
        , ui.groupBoxOriAY
        , ui.groupBoxOriAZ
        , ui.groupBoxDisplay
        , ui.groupBoxPosition
        , ui.groupBoxOrientation
        , ui.labelX
        , ui.labelY
        , ui.labelZ
        , ui.labelName
        , ui.labelNameTwo
        , ui.labelIDsX
        , ui.labelIDsY
        , ui.labelIDsZ
        , ui.labelOffsetsX
        , ui.labelOffsetsY
        , ui.labelOffsetsZ
        , ui.labelSnap
        , ui.pushButtonAutofill
        , ui.pushButtonSetView
        , ui.pushButtonSave
        , ui.pushButtonClose
        , ui.pushButtonRefresh
        , ui.pushButtonEdit
        , ui.pushButtonDelete
        , ui.checkBoxAxisShowOrHide
        , ui.checkBoxCloseZAxis
    );
    _pWrtWidget->setSelectedNodeLabel(WD::WDTs("CreateRectangularGridDialog", "Wrt"));
}
