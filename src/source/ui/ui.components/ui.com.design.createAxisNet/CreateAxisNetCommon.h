#pragma once

#include    <QObject>

#include	"core/WDCore.h"
#include	"core/businessModule/design/WDBMDesign.h"
#include    "../../wizDesignerApp/UiInterface/UiInterface.h"

/**
 * @brief 处理轴网数据
*/
class HandleAxisNet
	: public QObject
{
	Q_OBJECT

public:
	HandleAxisNet(QObject *parent = nullptr);
    ~HandleAxisNet();
	//轴网类型
	enum GridType
	{
		//辅助轴网
		GT_ASSISTANT = 0,
		//结构轴网
		GT_STRUCTURE
	};
	// 当前轴网状态
	enum CurrentState
	{
		// 创建轴网
		CS_CREATE = 0,
		// 编辑轴网
		CS_EDIT
	};
	// 轴网数据结构体
	struct CreateAxisNetMsg
	{
		// 轴网系统名称
		std::string strAxisName;
		// 轴网轴标签名
		std::string strLabel;
		// 轴网轴名
		std::string strAxis;
		// key: 轴网线偏移量 value:轴网线编号
		std::map<double, std::string> mapIdOffset;
		// 显示设置
		int displayFlag;
	};

public:
	/**
	* @brief 判断当前节点类型是否为GRINLN、GRIDAX、GRIDSY中的一种
	*/
	bool judgeNodesType(WD::WDNode::SharedPtr pNode);
	/**
	 * @brief 获取轴网系统数据
	 * @param pNode 当前选中节点
	*/
	std::vector<CreateAxisNetMsg> getSelectAxisNetNodesData(WD::WDNode::SharedPtr pNode);
	/**
	 * @brief 设置Z轴GRINLN节点显隐
	 * @param pCurrentNode 当前选中节点
	 * @param hide true 隐藏, false 显示
	*/
	void setAxisZHidden(WD::WDNode::SharedPtr pCurrentNode, bool hide);
public:
	/**
	* @brief 判断是否有重复
	*/
	bool judgeIsRepetition(const QStringList& lstData);
private:
	// key: 辅助轴网轴uuid, value: 辅助轴网轴下的辅助轴网线GRINLN
	using mapNodes = std::map<std::string, std::vector<WD::WDNode::SharedPtr>>;
	mapNodes _mapZ;
};

