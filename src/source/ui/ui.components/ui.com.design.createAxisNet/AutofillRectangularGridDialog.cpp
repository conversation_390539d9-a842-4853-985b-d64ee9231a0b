#include "AutofillRectangularGridDialog.h"
#include "core/WDTranslate.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

AutofillRectangularGridDialog::AutofillRectangularGridDialog(QWidget *parent)
    : QDialog(parent)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    this->initDialog();
    this->retranslateUi();
    connect(ui.pushButtonOk, &QPushButton::clicked, this, &AutofillRectangularGridDialog::slotOkButtonClicked);
    connect(ui.pushButtonCancel, &QPushButton::clicked, this, &AutofillRectangularGridDialog::slotCancelButtonClicked);
    connect(ui.lineEditFromX, &QLineEdit::editingFinished, this, &AutofillRectangularGridDialog::slotQLineEditFromXEditingFinished);
    connect(ui.lineEditFromY, &QLineEdit::editingFinished, this, &AutofillRectangularGridDialog::slotQLineEditFromYEditingFinished);
    connect(ui.lineEditFromZ, &QLineEdit::editingFinished, this, &AutofillRectangularGridDialog::slotQLineEditFromZEditingFinished);
    connect(ui.comboBoxIsNumberX, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AutofillRectangularGridDialog::slotXComboxChanged);
    connect(ui.comboBoxIsNumberY, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AutofillRectangularGridDialog::slotYComboxChanged);
    connect(ui.comboBoxIsNumberZ, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AutofillRectangularGridDialog::slotZComboxChanged);
    connect(ui.comboBoxIsAscendX, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AutofillRectangularGridDialog::slotXComboxChanged);
    connect(ui.comboBoxIsAscendY, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AutofillRectangularGridDialog::slotYComboxChanged);
    connect(ui.comboBoxIsAscendZ, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AutofillRectangularGridDialog::slotZComboxChanged);
}


AutofillRectangularGridDialog::~AutofillRectangularGridDialog()
{
}

void AutofillRectangularGridDialog::showEvent(QShowEvent*)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
}

void AutofillRectangularGridDialog::hideEvent(QHideEvent*)
{
}

void AutofillRectangularGridDialog::slotOkButtonClicked()
{
    this->getAutofillData();
    assert(!_lstAutoMsg.empty());
    if (!_lstAutoMsg.empty())
        emit sigAutofillData(_lstAutoMsg);
    this->accept();
}

void AutofillRectangularGridDialog::slotCancelButtonClicked()
{
    this->reject();
}

void AutofillRectangularGridDialog::slotXComboxChanged(int)
{
    // 获取编号类型
    int type = ui.comboBoxIsNumberX->currentData().toInt();
    switch (type)
    {
    case AutoCreateIDRules::Numeric:
        {
            int sort = ui.comboBoxIsAscendX->currentData().toInt();
            switch (sort)
            {
            case AutoCreateIDRules::Ascending:
                ui.lineEditFromX->setText(QString::number(1));
                break;
            case AutoCreateIDRules::descending:
                ui.lineEditFromX->setText(QString::number(999));
                break;
            default:
                assert(false);
                break;
            }
        }
        break;
    case AutoCreateIDRules::Alphabetic:
        {
            int sort = ui.comboBoxIsAscendX->currentData().toInt();
            switch (sort)
            {
            case AutoCreateIDRules::Ascending:
                ui.lineEditFromX->setText(letters.front());
                break;
            case AutoCreateIDRules::descending:
                ui.lineEditFromX->setText(letters.back());
                break;
            default:
                assert(false);
                break;
            }
        }
        break;
    default:
        assert(false);
        break;
    }
}

void AutofillRectangularGridDialog::slotYComboxChanged(int)
{
    // 获取编号类型
    int type = ui.comboBoxIsNumberY->currentData().toInt();
    switch (type)
    {
    case AutoCreateIDRules::Numeric:
        {
            int sort = ui.comboBoxIsAscendY->currentData().toInt();
            switch (sort)
            {
            case AutoCreateIDRules::Ascending:
                ui.lineEditFromY->setText(QString::number(1));
                break;
            case AutoCreateIDRules::descending:
                ui.lineEditFromY->setText(QString::number(999));
                break;
            default:
                assert(false);
                break;
            }
        }
        break;
    case AutoCreateIDRules::Alphabetic:
        {
            int sort = ui.comboBoxIsAscendY->currentData().toInt();
            switch (sort)
            {
            case AutoCreateIDRules::Ascending:
                ui.lineEditFromY->setText(letters.front());
                break;
            case AutoCreateIDRules::descending:
                ui.lineEditFromY->setText(letters.back());
                break;
            default:
                assert(false);
                break;
            }
        }
        break;
    default:
        assert(false);
        break;
    }
}

void AutofillRectangularGridDialog::slotZComboxChanged(int)
{
    // 获取编号类型
    int type = ui.comboBoxIsNumberZ->currentData().toInt();
    switch (type)
    {
    case AutoCreateIDRules::Numeric:
        {
            int sort = ui.comboBoxIsAscendZ->currentData().toInt();
            switch (sort)
            {
            case AutoCreateIDRules::Ascending:
                ui.lineEditFromZ->setText(QString::number(1));
                break;
            case AutoCreateIDRules::descending:
                ui.lineEditFromZ->setText(QString::number(999));
                break;
            default:
                assert(false);
                break;
            }
        }
        break;
    case AutoCreateIDRules::Alphabetic:
        {
            int sort = ui.comboBoxIsAscendZ->currentData().toInt();
            switch (sort)
            {
            case AutoCreateIDRules::Ascending:
                ui.lineEditFromZ->setText(letters.front());
                break;
            case AutoCreateIDRules::descending:
                ui.lineEditFromZ->setText(letters.back());
                break;
            default:
                assert(false);
                break;
            }
        }
        break;
    default:
        assert(false);
        break;
    }
}

void AutofillRectangularGridDialog::slotQLineEditFromXEditingFinished()
{
    auto str = ui.lineEditFromX->text();
    if (str.isEmpty())
        return slotXComboxChanged(ui.comboBoxIsAscendX->currentIndex());
    auto isNumberType = ui.comboBoxIsNumberX->currentData().toInt();
    if (isNumberType == Numeric)
    {
        // 依次判断每个字符是否是数字
        bool isNumber = true;
        for (auto each : str)
        {
            if (each < '0' || each > '9')
            {
                isNumber = false;
                break;
            }
        }
        if (isNumber)
            return;
        return slotXComboxChanged(ui.comboBoxIsAscendX->currentIndex());
    }
    else if (isNumberType == Alphabetic)
    {
        // 判断字符是否是字母
        if (str.size() != 1 || (str[0] < 'a' || str[0] > 'z') && (str[0] < 'A' || str[0] > 'Z'))
            return slotXComboxChanged(ui.comboBoxIsAscendX->currentIndex());
        return;
    }
    assert(false);
}

void AutofillRectangularGridDialog::slotQLineEditFromYEditingFinished()
{
    auto str = ui.lineEditFromY->text();
    if (str.isEmpty())
        return slotYComboxChanged(ui.comboBoxIsAscendY->currentIndex());
    auto isNumberType = ui.comboBoxIsNumberY->currentData().toInt();
    if (isNumberType == Numeric)
    {
        // 依次判断每个字符是否是数字
        bool isNumber = true;
        for (auto each : str)
        {
            if (each < '0' || each > '9')
            {
                isNumber = false;
                break;
            }
        }
        if (isNumber)
            return;
        return slotYComboxChanged(ui.comboBoxIsAscendY->currentIndex());
    }
    else if (isNumberType == Alphabetic)
    {
        // 判断字符是否是字母
        if (str.size() != 1 || (str[0] < 'a' || str[0] > 'z') && (str[0] < 'A' || str[0] > 'Z'))
            return slotYComboxChanged(ui.comboBoxIsAscendY->currentIndex());
        return;
    }
    assert(false);
}

void AutofillRectangularGridDialog::slotQLineEditFromZEditingFinished()
{
    auto str = ui.lineEditFromZ->text();
    if (str.isEmpty())
        return slotZComboxChanged(ui.comboBoxIsAscendZ->currentIndex());
    auto isNumberType = ui.comboBoxIsNumberZ->currentData().toInt();
    if (isNumberType == Numeric)
    {
        // 依次判断每个字符是否是数字
        bool isNumber = true;
        for (auto each : str)
        {
            if (each < '0' || each > '9')
            {
                isNumber = false;
                break;
            }
        }
        if (isNumber)
            return;
        return slotZComboxChanged(ui.comboBoxIsAscendZ->currentIndex());
    }
    else if (isNumberType == Alphabetic)
    {
        // 判断字符是否是字母
        if (str.size() != 1 || (str[0] < 'a' || str[0] > 'z') && (str[0] < 'A' || str[0] > 'Z'))
            return slotZComboxChanged(ui.comboBoxIsAscendZ->currentIndex());
        return;
    }
    assert(false);
    return slotZComboxChanged(ui.comboBoxIsAscendZ->currentIndex());
}

void AutofillRectangularGridDialog::getAutofillData()
{
    _lstAutoMsg.clear();
    QStringList lstDataX, lstDataY, lstDataZ;
    lstDataX << ui.lineEditAxisX->text() << ui.spinBoxOffsetsX->text() << ui.spinBoxToX->text() << ui.spinBoxIntervalX->text() << ui.lineEditFromX->text() << ui.spinBoxIDsX->text();
    lstDataY << ui.lineEditAxisY->text() << ui.spinBoxOffsetsY->text() << ui.spinBoxToY->text() << ui.spinBoxIntervalY->text() << ui.lineEditFromY->text() << ui.spinBoxIDsY->text();
    lstDataZ << ui.lineEditAxisZ->text() << ui.spinBoxOffsetsZ->text() << ui.spinBoxToZ->text() << ui.spinBoxIntervalZ->text() << ui.lineEditFromZ->text() << ui.spinBoxIDsZ->text();
    for (auto& each : lstDataX)
    {
        if (each.isEmpty())
        {
            WD_WARN_T("AutofillRectangularGridDialog", "DataCannotEmpty");
            return;
        }
    }
    for (auto& each : lstDataY)
    {
        if (each.isEmpty())
        {
            WD_WARN_T("AutofillRectangularGridDialog", "DataCannotEmpty");
            return;
        }
    }

    for (auto& each : lstDataZ)
    {
        if (each.isEmpty())
        {
            WD_WARN_T("AutofillRectangularGridDialog", "DataCannotEmpty");
            return;
        }
    }
    //X
    if (ui.comboBoxIsNumberX->currentData().toInt() == Numeric)  //数字
        this->packageNumericData(ui.comboBoxIsAscendX->currentData().toInt(), lstDataX);
    else if (ui.comboBoxIsNumberX->currentData().toInt() == Alphabetic)//字母
        this->packageAlphabeticData(ui.comboBoxIsAscendX->currentData().toInt(), lstDataX);
    else
        return assert(false);
    //Y
    if (ui.comboBoxIsNumberY->currentData().toInt() == Numeric)
        this->packageNumericData(ui.comboBoxIsAscendY->currentData().toInt(), lstDataY);
    else if (ui.comboBoxIsNumberY->currentData().toInt() == Alphabetic)
        this->packageAlphabeticData(ui.comboBoxIsAscendY->currentData().toInt(), lstDataY);
    else
        return assert(false);
    //Z
    if (ui.comboBoxIsNumberZ->currentData().toInt() == Numeric)
        this->packageNumericData(ui.comboBoxIsAscendZ->currentData().toInt(), lstDataZ);
    else if (ui.comboBoxIsNumberZ->currentData().toInt() == Alphabetic)
        this->packageAlphabeticData(ui.comboBoxIsAscendZ->currentData().toInt(), lstDataZ);
    else
        return assert(false);
    
}

void AutofillRectangularGridDialog::packageNumericData(int isAscend, const QStringList& lstData)
{
    if (lstData.size() != 6)
    {
        assert(false);
        return;
    }

    auto    begin       =   lstData.at(1).toInt();
    auto    end         =   lstData.at(2).toInt();
    auto    interval    =   lstData.at(3).toInt();
    auto    beginIDs    =   lstData.at(4).toInt();
    auto    intervalIDs =   lstData.at(5).toInt();

    AutofillMsg autofill;
    auto&   ids     =   autofill.lstIds;
    auto&   offset  =   autofill.lstOffset;
    switch (isAscend)
    {
    case Ascending:
        {
            for (int i = begin; i <= end;)
            {
                ids.push_back(QString::number(beginIDs));
                offset.push_back(QString::number(i));
                beginIDs += intervalIDs;
                i += interval;
            }
        }
        break;
    case descending:
        {
            for (int i = begin; i <= end;)
            {
                ids.push_back(QString::number(beginIDs));
                offset.push_back(QString::number(i));
                beginIDs -= intervalIDs;
                i += interval;
            }
        }
        break;
    default:
        assert(false);
        break;
    }
    autofill.label = lstData.at(0);
    _lstAutoMsg.push_back(autofill);
}

void AutofillRectangularGridDialog::packageAlphabeticData(int isAscend, const QStringList& lstData)
{
    if (lstData.size() != 6)
    {
        assert(false);
        return;
    }
    auto    begin       =   lstData.at(1).toInt();
    auto    end         =   lstData.at(2).toInt();
    auto    interval    =   lstData.at(3).toInt();
    auto&   beginIDs    =   lstData.at(4);
    auto    intervalIDs =   lstData.at(5).toInt();

    AutofillMsg autofill;
    auto&   ids     =   autofill.lstIds;
    auto&   offset  =   autofill.lstOffset;

    int index = -1;
    for (int i = 0; i < letters.size(); ++i)
    {
        auto& letter = letters[i];
        if (letter == beginIDs)
        {
            index = i;
            break;
        }
    }
    if (index < 0)
    {
        assert(false);
        return;
    }

    switch (isAscend)
    {
    case Ascending:
        {
            for (int i = begin; i <= end;)
            {
                while (index >= int(letters.size()))
                    index -= int(letters.size());
                while (index < 0)
                    index += int(letters.size());
                ids.push_back(letters[index]);
                offset.push_back(QString::number(i));
                index += intervalIDs;
                i += interval;
            }
        }
        break;
    case descending:
        {
            for (int i = begin; i <= end;)
            {
                while (index >= int(letters.size()))
                    index -= int(letters.size());
                while (index < 0)
                    index += int(letters.size());
                ids.push_back(letters[index]);
                offset.push_back(QString::number(i));
                index -= intervalIDs;
                i += interval;
            }
        }
        break;
    default:
        assert(false);
        break;
    }

    autofill.label = lstData.at(0);
    _lstAutoMsg.push_back(autofill);
}

void AutofillRectangularGridDialog::initDialog()
{
    ui.comboBoxIsNumberX->addItem(tr("Numeric"), AutoCreateIDRules::Numeric);
    ui.comboBoxIsNumberX->addItem(tr("Alphabetic"), AutoCreateIDRules::Alphabetic);
    ui.comboBoxIsAscendX->addItem(tr("Ascending"), AutoCreateIDRules::Ascending);
    ui.comboBoxIsAscendX->addItem(tr("descending"), AutoCreateIDRules::descending);
    ui.comboBoxIsNumberY->addItem(tr("Numeric"), AutoCreateIDRules::Numeric);
    ui.comboBoxIsNumberY->addItem(tr("Alphabetic"), AutoCreateIDRules::Alphabetic);
    ui.comboBoxIsAscendY->addItem(tr("Ascending"), AutoCreateIDRules::Ascending);
    ui.comboBoxIsAscendY->addItem(tr("descending"), AutoCreateIDRules::descending);
    ui.comboBoxIsNumberZ->addItem(tr("Numeric"), AutoCreateIDRules::Numeric);
    ui.comboBoxIsNumberZ->addItem(tr("Alphabetic"), AutoCreateIDRules::Alphabetic);
    ui.comboBoxIsAscendZ->addItem(tr("Ascending"), AutoCreateIDRules::Ascending);
    ui.comboBoxIsAscendZ->addItem(tr("descending"), AutoCreateIDRules::descending);

    slotXComboxChanged(ui.comboBoxIsAscendX->currentIndex());
    slotYComboxChanged(ui.comboBoxIsAscendY->currentIndex());
    slotZComboxChanged(ui.comboBoxIsAscendZ->currentIndex());
}

void AutofillRectangularGridDialog::retranslateUi()
{
    Trs("AutofillRectangularGridDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonOk
        , ui.pushButtonCancel
        , ui.groupBoxAxisX
        , ui.groupBoxAxisY
        , ui.groupBoxAxisZ
        , ui.labelX
        , ui.labelY
        , ui.labelZ
        , ui.labelOffsetsX
        , ui.labelOffsetsY
        , ui.labelOffsetsZ
        , ui.labelToX
        , ui.labelToY
        , ui.labelToZ
        , ui.labelIntervalX
        , ui.labelIntervalY
        , ui.labelIntervalZ
        , ui.labelIntervalIDsX
        , ui.labelIntervalIDsY
        , ui.labelIntervalIDsZ
        , ui.labelIDsX
        , ui.labelIDsY
        , ui.labelIDsZ
        , ui.labelFromX
        , ui.labelFromY
        , ui.labelFromZ
        , ui.comboBoxIsNumberX
        , ui.comboBoxIsAscendX
        , ui.comboBoxIsNumberY
        , ui.comboBoxIsAscendY
        , ui.comboBoxIsNumberZ
        , ui.comboBoxIsAscendZ
    );
}