#include    "ui.com.scene.viewer.h"
#include    "core/WDObjectCreator.h"
#include    "core/message/WDMessage.h"
#include    "core/extension/WDPluginToolSet.h"
#include    "core/viewer/WDViewerCube.h"
#include    "core/scene/WDScene.h"
#include    "core/WDTranslate.h"
#include    "core/events/WDInputEvent.h"
#include    "core/GL/WDShareContext.h"
#include    "core/WDCore.h"
#include    <QOffscreenSurface>
#include    <QTimer>
#include    <QThread>
#include    <QFileDialog>
#include    <QDateTime>
#include    "OGLCtxQt.h"
#include    "core/selections/WDNodeSelection.h"
#include    "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include    "log/WDLoggerPort.h"

WD::WDInputEvent::KeyBoardModifiers    Modifiers(QInputEvent* evt)
{
    WD::WDInputEvent::KeyBoardModifiers   m;

    if (evt->modifiers() & Qt::ShiftModifier)       m.addFlag(WD::WDInputEvent::KBM_Shift);
    if (evt->modifiers() & Qt::ControlModifier)     m.addFlag(WD::WDInputEvent::KBM_Control);
    if (evt->modifiers() & Qt::AltModifier)         m.addFlag(WD::WDInputEvent::KBM_Alt);
    if (evt->modifiers() & Qt::MetaModifier)        m.addFlag(WD::WDInputEvent::KBM_Meta);
    if (evt->modifiers() & Qt::KeypadModifier)      m.addFlag(WD::WDInputEvent::KBM_Keypad);
    if (evt->modifiers() & Qt::GroupSwitchModifier) m.addFlag(WD::WDInputEvent::KBM_GroupSwitch);

    return  m;

}

UIComSceneViewer::UIComSceneViewer(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    : IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
{
    _pActionSelectCurrent = nullptr;

    LOG_INFO << "创建主三维视图";
    _pComPRCoordinate = new UiComPRCoordinate(mainWindow, "action.view.scene.pickup", this);

    Core().objectCreator().regist<OGLCtxQt>();

    setMouseTracking(true);
    //设置窗口部件可以接收拖入,默认不接收
    setAcceptDrops(true);

    {
        QSurfaceFormat fmt = this->format();
        fmt.setSamples(_core.viewer().multiSample());
        this->setFormat(fmt);

        _core.viewer().nativeNeedRepaint()    =   {   this, &UIComSceneViewer::onNeedRepaint  };
        _core.viewer().makeCurrent()          =   {   this, &UIComSceneViewer::onMakeCurrent  };

        // 激活视图
        _core.viewer().active();
    }


    std::string     value   =   WD::WDTs("UIComSceneViewer","Titile");
    setWindowTitle(QString::fromUtf8(value.c_str()));

    _pMenu = new QMenu();
    _pActionGroup = new QActionGroup(this);
    connect(_pActionGroup
        , SIGNAL(triggered(QAction*))
        , this
        , SLOT(slotActionGroupTriggered(QAction*)));
    // 初始化右键菜单
    this->initViewerContextMenu();

    //注册qt类型，并绑定信号槽
    qRegisterMetaType<CallBack>("CallBack");
    connect(this, SIGNAL(sigDoRunable(CallBack, void*, void*))
        , this, SLOT(slotDoRunable(CallBack, void*, void*)));
    //保存该实例到全局core上，方便其他地方调用
    mWindow().core().setApiDelegate(this);
}
UIComSceneViewer::~UIComSceneViewer()
{
    if (_pComPRCoordinate != nullptr)
    {
        delete _pComPRCoordinate;
        _pComPRCoordinate = nullptr;
    }  
    if (_pActionGroup != nullptr)
    {
        delete _pActionGroup;
        _pActionGroup = nullptr;
    }
    if (_pMenu != nullptr)
    {
        delete _pMenu;
        _pMenu = nullptr;
    }

    _core.viewer().nativeNeedRepaint() = {};
    _core.viewer().makeCurrent() = {};
}

void UIComSceneViewer::onNotice(UiNotice * pNotice)
{
    //TODO: 待完善
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
        {
            LOG_INFO << "三维视图插件准备就绪";
        }
        break;
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            this->onAction(pActionNotice);
        }
        break;
    break;
    default:
        break;
    }
}

QWidget*UIComSceneViewer::getWidget(const char* name)
{
    if (name == nullptr || strlen(name) == 0)
    {
        LOG_INFO << "获取三维视图中央窗口";
        return  (QWidget*)this;
    }
    return  nullptr;
}

void UIComSceneViewer::initializeGL()
{
    LOG_INFO << "初始化默认视图对象";
    _core.viewer().init();
    LOG_INFO << "默认视图对象初始化完成";
}

void UIComSceneViewer::resizeGL(int w, int h)
{
    _core.viewer().resize({ w, h });
}

void UIComSceneViewer::paintGL()
{
    _core.viewer().repaint();
}

void UIComSceneViewer::mousePressEvent(QMouseEvent *evt)
{
    setFocus();

    QOpenGLWidget::mousePressEvent(evt);

    WD::WDMouseEvent::MouseButton btn = WD::WDMouseEvent::MouseButton::MB_NoButton;
    switch(evt->button())
    {
    case Qt::LeftButton:
        btn = WD::WDMouseEvent::MouseButton::MB_LeftButton;
        break;
    case Qt::RightButton:
        btn = WD::WDMouseEvent::MouseButton::MB_RightButton;

        break;
    case Qt::MidButton:
        btn = WD::WDMouseEvent::MouseButton::MB_MiddleButton;
        break;
    default:
        break;
    }

    auto    modifier    =   Modifiers(evt);

    WD::WDMouseEvent e(
          WD::IVec2(evt->x(), evt->y())
        , WD::WDMouseEvent::MouseState::MS_MouseButtonPress
        , btn
        , WD::WDMouseEvent::MouseButton::MB_NoButton
        , modifier);

    _core.viewer().onEvent(&e);

    // 按下开始时间
    _startTime = QDateTime::currentMSecsSinceEpoch();
    _pScreenPos = QCursor::pos();

    this->repaint();
}

void UIComSceneViewer::mouseReleaseEvent(QMouseEvent *evt)
{
    QOpenGLWidget::mouseReleaseEvent(evt);

    qint64 durationTime = 0;
    QPoint releasePos = QCursor::pos();
    WD::WDMouseEvent::MouseButton btn = WD::WDMouseEvent::MouseButton::MB_NoButton;;
    switch(evt->button())
    {
    case Qt::LeftButton:
        btn = WD::WDMouseEvent::MouseButton::MB_LeftButton;
        break;
    case Qt::RightButton:
        btn = WD::WDMouseEvent::MouseButton::MB_RightButton;
        break;
    case Qt::MidButton:
        btn = WD::WDMouseEvent::MouseButton::MB_MiddleButton;
        break;
    default:
        break;
    }

    auto    modifier    =   Modifiers(evt);

    WD::WDMouseEvent e(
          WD::IVec2(evt->x(), evt->y())
        , WD::WDMouseEvent::MouseState::MS_MouseButtonRelease
        , btn
        , WD::WDMouseEvent::MouseButton::MB_NoButton
        , modifier);
    auto& viewer = _core.viewer();
    if (viewer.onEvent(&e))
    {
        switch (evt->button())
        {
        case Qt::RightButton:
        {
            durationTime = QDateTime::currentMSecsSinceEpoch() - _startTime;
            if ((durationTime <= 1000)
                && (qAbs(releasePos.x() - _pScreenPos.x()) < 1)
                && (qAbs(releasePos.y() - _pScreenPos.y()) < 1))
            {
                // 拾取节点，判断是否需要弹出选择当前按钮
                _currentSelectedNode.reset();
                if (viewer.camera() != nullptr)
                {
                    WD::DRay ray = viewer.camera()->createRayFromScreen(DVec2(evt->x(), evt->y()), viewer.size());
                    // 拾取
                    WDNodePickupResult tRet;
                    if (_core.scene().pickup(viewer, ray, tRet, WDScene::SLAM_None)) 
                    {
                        auto pRetNode = tRet.node.lock();
                        if (pRetNode != nullptr) 
                        {
                            // 如果是直管，则处理过滤
                            if (pRetNode->isType("TUBI") && pRetNode->parent() != nullptr) 
                            {
                                WD::WDNode::SharedPtr pFNode = nullptr;
                                bool bBack = false;
                                for (auto pNode : pRetNode->parent()->children())
                                {
                                    if (pNode == nullptr)
                                        continue;
                                    if (pNode->isType("TUBI") && pNode == pRetNode)
                                    {
                                        // 如果已经找到了前一个管件，则跳出
                                        if (pFNode != nullptr)
                                            break;
                                        // 否则继续查找后一个管件
                                        else
                                            bBack = true;
                                    }
                                    // 如果是管件节点，则赋值
                                    if (WD::WDBMDPipeUtils::IsPipeComponent(*pNode) && !pNode->isType("ATTA"))
                                        pFNode = pNode;
                                    // 如果是查找后面的管件的话，只要pFNode有效，则直接跳出
                                    if (bBack && pFNode != nullptr)
                                        break;
                                }
                                // 赋值节点
                                if (pFNode != nullptr)
                                    _currentSelectedNode = pFNode;
                                else
                                    _currentSelectedNode = pRetNode->parent();
                            }
                            else
                            {
                                // 赋值节点
                                _currentSelectedNode = pRetNode;
                            }
                        }
                    }
                }
                _pActionSelectCurrent->setVisible(!_currentSelectedNode.expired());

                //当前鼠标位置弹出菜单
                _pMenu->exec(QCursor::pos());
            }
        }
        break;
        default:
            break;
        }
    }

    this->repaint();
}

void UIComSceneViewer::mouseDoubleClickEvent(QMouseEvent *evt)
{
    QOpenGLWidget::mouseDoubleClickEvent(evt);

    WD::WDMouseEvent::MouseButton btn = WD::WDMouseEvent::MouseButton::MB_NoButton;;
    switch(evt->button())
    {
    case Qt::LeftButton:
        btn = WD::WDMouseEvent::MouseButton::MB_LeftButton;
        break;
    case Qt::RightButton:
        btn = WD::WDMouseEvent::MouseButton::MB_RightButton;
        break;
    case Qt::MidButton:
        btn = WD::WDMouseEvent::MouseButton::MB_MiddleButton;
        break;
    default:
        break;
    }

    auto    modifier    =   Modifiers(evt);

    WD::WDMouseEvent e(
          WD::IVec2(evt->x(), evt->y())
        , WD::WDMouseEvent::MouseState::MS_MouseButtonDblClick
        , btn
        , WD::WDMouseEvent::MouseButton::MB_NoButton
        , modifier);

    _core.viewer().onEvent(&e);

    this->repaint();
}

void UIComSceneViewer::mouseMoveEvent(QMouseEvent *evt)
{
    QOpenGLWidget::mouseMoveEvent(evt);

    WD::WDMouseEvent::MouseButton btn = WD::WDMouseEvent::MouseButton::MB_NoButton;
    switch(evt->button())
    {
    case Qt::LeftButton:
        btn = WD::WDMouseEvent::MouseButton::MB_LeftButton;
        break;
    case Qt::RightButton:
        btn = WD::WDMouseEvent::MouseButton::MB_RightButton;
        break;
    case Qt::MidButton:
        btn = WD::WDMouseEvent::MouseButton::MB_MiddleButton;
        break;
    default:
        break;
    }

    auto    modifier   =   Modifiers(evt);

    WD::WDMouseEvent e(
          WD::IVec2(evt->x(), evt->y())
        , WD::WDMouseEvent::MouseState::MS_MouseMove
        , btn
        , WD::WDMouseEvent::MouseButton::MB_NoButton
        , modifier);

    _core.viewer().onEvent(&e);

    this->repaint();
}

void UIComSceneViewer::wheelEvent(QWheelEvent *evt)
{
    QOpenGLWidget::wheelEvent(evt);

    auto    modifier    =   Modifiers(evt);

    WD::WDWheelEvent e(
          WD::IVec2(evt->x(), evt->y())
        , evt->delta()
        , WD::WDMouseEvent::MouseButton::MB_NoButton
        , modifier);

    _core.viewer().onEvent(&e);

    this->repaint();
}

void UIComSceneViewer::keyPressEvent(QKeyEvent *evt)
{
    QOpenGLWidget::keyPressEvent(evt);

    if (!evt->isAutoRepeat())
    {
        WD::WDInputEvent::KeyBoardModifiers modifiers;
        if (evt->modifiers() & Qt::ShiftModifier)       modifiers.addFlag(WD::WDInputEvent::KBM_Shift);
        if (evt->modifiers() & Qt::ControlModifier)     modifiers.addFlag(WD::WDInputEvent::KBM_Control);
        if (evt->modifiers() & Qt::AltModifier)         modifiers.addFlag(WD::WDInputEvent::KBM_Alt);

        if (evt->modifiers() & Qt::MetaModifier)        modifiers.addFlag(WD::WDInputEvent::KBM_Meta);
        if (evt->modifiers() & Qt::KeypadModifier)      modifiers.addFlag(WD::WDInputEvent::KBM_Keypad);
        if (evt->modifiers() & Qt::GroupSwitchModifier) modifiers.addFlag(WD::WDInputEvent::KBM_GroupSwitch);

        WD::WDKeyEvent e(evt->key(), WD::WDKeyEvent::KeyState::KS_KeyPress,modifiers);
        
        
        _core.viewer().onEvent(&e);
    }
    this->repaint();
}

void UIComSceneViewer::keyReleaseEvent(QKeyEvent *evt)
{
    QOpenGLWidget::keyReleaseEvent(evt);

    if (!evt->isAutoRepeat())
    {
        WD::WDInputEvent::KeyBoardModifiers modifiers;
        if (evt->modifiers() & Qt::ShiftModifier)       modifiers.addFlag(WD::WDInputEvent::KBM_Shift);
        if (evt->modifiers() & Qt::ControlModifier)     modifiers.addFlag(WD::WDInputEvent::KBM_Control);
        if (evt->modifiers() & Qt::AltModifier)         modifiers.addFlag(WD::WDInputEvent::KBM_Alt);

        if (evt->modifiers() & Qt::MetaModifier)        modifiers.addFlag(WD::WDInputEvent::KBM_Meta);
        if (evt->modifiers() & Qt::KeypadModifier)      modifiers.addFlag(WD::WDInputEvent::KBM_Keypad);
        if (evt->modifiers() & Qt::GroupSwitchModifier) modifiers.addFlag(WD::WDInputEvent::KBM_GroupSwitch);

        WD::WDKeyEvent e(evt->key(), WD::WDKeyEvent::KeyState::KS_KeyRelease,modifiers);

        _core.viewer().onEvent(&e);
    }
    this->repaint();
}

void UIComSceneViewer::focusInEvent(QFocusEvent * evt)
{
    QOpenGLWidget::focusInEvent(evt);

    this->repaint();
}

void UIComSceneViewer::focusOutEvent(QFocusEvent * evt)
{
    QOpenGLWidget::focusOutEvent(evt);

    this->repaint();
}

void UIComSceneViewer::enterEvent(QEvent * evt)
{
    QOpenGLWidget::enterEvent(evt);

    this->repaint();
}

void UIComSceneViewer::leaveEvent(QEvent * evt)
{
    QOpenGLWidget::leaveEvent(evt);

    this->repaint();
}

void UIComSceneViewer::paintEvent(QPaintEvent * e)
{
    WDUnused(e);

    makeCurrent();
    _core.viewer().repaint();
}

void UIComSceneViewer::resizeEvent(QResizeEvent*evt)
{
    QOpenGLWidget::resizeEvent(evt);

    _core.viewer().resize(WD::IVec2(evt->size().width(), evt->size().height()));

    WD::WDResizeEvent e(WD::IVec2(evt->size().width(), evt->size().height())
        , WD::IVec2(evt->oldSize().width(), evt->oldSize().height()));

    this->repaint();
}

void UIComSceneViewer::closeEvent(QCloseEvent *event)
{
    WD::WDCloseEvent e;
    _core.viewer().onEvent(&e);

    QOpenGLWidget::closeEvent(event);
}

void UIComSceneViewer::dropEvent(QDropEvent * event)
{
    const QMimeData* pData = event->mimeData();   
    if (pData == nullptr)
    {
        event->ignore();
        return;
    }
    if (pData->objectName() ==QString::fromUtf8("NodesMimeData"))
    {
        const TMyQMimeData<std::vector<WDNode::WeakPtr> >* pSubData = dynamic_cast<const TMyQMimeData<std::vector<WDNode::WeakPtr> >* >(pData);
        if (pSubData == nullptr)
        {
            event->ignore();
            return;
        }
        
        const std::vector<WDNode::WeakPtr>& nodes = pSubData->myData();
        for (auto pNode : nodes)
        {
            this->addNodeToScene(pNode.lock());
        }
        
    }
    else if (pData->objectName() == QString::fromUtf8("Material"))
    {
        // 拾取节点
        DRay ray = _core.viewer().camera()->createRayFromScreen(DVec2(event->posF().x(), event->posF().y()), _core.viewer().size());
        WDNodePickupResult tRet;
        if (!_core.scene().pickup(_core.viewer(), ray, tRet))
        {
            event->ignore();
            return ;
        }

        WD::WDNode::SharedPtr pNode = tRet.node.lock();
        const TMyQMimeData<WD::WDMaterial*>* pSubData = dynamic_cast<const TMyQMimeData<WD::WDMaterial* >* >(pData);
        if (pSubData == nullptr)
        {
            event->ignore();
            return ;
        }

        auto pMatPhong = dynamic_cast<WDMaterialPhong*>(pSubData->myData());
        if (pMatPhong == nullptr)
        {
            event->ignore();
            return ;
        }
        pNode->setMaterial(WDMaterialPhong::ToShared(pMatPhong));
        pNode->setAttribute("AutoColor", pMatPhong->diffuse());
        pNode->setFlags(pNode->flags().addFlag(WD::WDNode::F_Update));
    }
    event->accept();
}

void UIComSceneViewer::dragEnterEvent(QDragEnterEvent * event)
{
    //如果有自己定义的MIME类型数据,则进行移动操作
    if (event->mimeData()->objectName() == QString::fromUtf8("NodesMimeData")
        || event->mimeData()->objectName() == QString::fromUtf8("Material"))
    {
        event->setDropAction(Qt::MoveAction);
        event->accept();
    }
    else
    {
        event->ignore();
    }
}

void UIComSceneViewer::addNodeToScene(WD::WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;
    mWindow().core().scene().add(pNode);
    mWindow().core().needRepaint();
}

void UIComSceneViewer::addNodeConnectionsToScene(WD::WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return;

    // 添加连接时的最小添加单元为 BRAN， EQUI， REST
    auto addNode = [](WD::WDScene& scene, WD::WDNode& node)
    {
        auto pParent = WD::WDNode::ToShared(&node);
        while (pParent != nullptr)
        {
            if (pParent->isAnyOfType("BRAN", "EQUI", "REST"))
            {
                scene.add(pParent);
                return;
            }
            pParent = pParent->parent();
        }
        scene.add(WD::WDNode::ToShared(&node));
    };
    addNode(_core.scene(), *pNode);
    WD::WDNode::RecursionHelpter(*pNode, [&addNode](WD::WDScene& scene, WD::WDNode& node)
    {
        auto pConnectVal = node.getAttribute("Connections");
        if (!pConnectVal.valid())
            return;
        auto pConnections = pConnectVal.data<WD::WDBMNodeRefs>();
        if (pConnections == nullptr || pConnections->empty())
            return;
        for (auto& connect : *pConnections)
        {
            auto pNode = connect.refNode();
            if (pNode != nullptr)
                addNode(scene, *pNode);
        }
    }, _core.scene());
    _core.needRepaint();
}

void UIComSceneViewer::onAction(UiActionNotice * pAction)
{
    if (pAction == nullptr)
        return ;

    if (pAction->action().is("action.view.scene.add"))   // 场景：添加
    {
        auto& nodeTree = mWindow().core().nodeTree();
        auto pNode = nodeTree.currentNode();
        if (pNode != nullptr)
            addNodeToScene(pNode);
    }
    else if (pAction->action().is("action.view.scene.addConnections"))   // 场景：添加连接
    {
        auto& nodeTree = mWindow().core().nodeTree();
        auto pNode = nodeTree.currentNode();
        if (pNode != nullptr)
            addNodeConnectionsToScene(pNode);
    }
    else if (pAction->action().is("action.view.scene.remove"))   // 场景：移除
    {
        onActionRemoveFromSceneTriggered();
    }
    else if (pAction->action().is("action.view.scene.screenshot")) // 场景： 截图
    {
        onActionScreenShotTriggered();
    }
    else if (pAction->action().is("action.view.scene.clear"))   // 场景：清除
    {
        onActionClearSceneTriggered();
    }
    else if (pAction->action().is("action.view.scene.axis"))   // 场景：场景轴
    {
        onActionSceneAxisVisible(pAction->action().checked());
    }
    else if (pAction->action().is("action.view.location.top"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_Top);
    }
    else if (pAction->action().is("action.view.location.bottom"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_Bottom);
    }
    else if (pAction->action().is("action.view.location.front"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_Front);
    }
    else if (pAction->action().is("action.view.location.back"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_Back);
    }
    else if (pAction->action().is("action.view.location.left"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_Left);
    }
    else if (pAction->action().is("action.view.location.right"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_Right);
    }
    else if (pAction->action().is("action.view.location.ISO1"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_ISO1);
    }
    else if (pAction->action().is("action.view.location.ISO2"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_ISO2);
    }
    else if (pAction->action().is("action.view.location.ISO3"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_ISO3);
    }
    else if (pAction->action().is("action.view.location.ISO4"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_ISO4);
    }
    else if (pAction->action().is("action.view.location.North"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_North);
    }
    else if (pAction->action().is("action.view.location.South"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_South);
    }
    else if (pAction->action().is("action.view.location.East"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_East);
    }
    else if (pAction->action().is("action.view.location.West"))
    {
        this->lookAt(WD::WDViewer::LookAtDir::LAD_West);
    }
    else if (pAction->action().is("action.view.location.center"))
    {
        auto pCurrNode = _core.nodeTree().currentNode();

        DAabb3 tAabb = DAabb3::Null();
        if (pCurrNode == nullptr)
        {
            const auto& tree = mWindow().core().nodeTree();
            for (size_t i = 0; i < tree.topLevelNodeCount(); ++i)
            {
                auto pNode = tree.topLevelNode(i);
                if (pNode == nullptr)
                    continue;
                tAabb.unions(pNode->aabb());
            }
        }
        else
        {
            tAabb = pCurrNode->aabb();
        }
        _core.viewer().lookAtAabb(tAabb);
    }
    else if (pAction->action().is("action.view.location.center.limits"))
    {
        auto pCurrNode = _core.nodeTree().currentNode();

        DAabb3 tAabb = DAabb3::Null();
        if (pCurrNode == nullptr)
        {
            const auto& tree = mWindow().core().nodeTree();
            for (size_t i = 0; i < tree.topLevelNodeCount(); ++i)
            {
                auto pNode = tree.topLevelNode(i);
                if (pNode == nullptr)
                    continue;
                tAabb.unions(pNode->aabb());
            }
        }
        else
        {
            tAabb = pCurrNode->aabb();
        }
        _core.viewer().lookAtAabb(tAabb, true);
    }
    else if (pAction->action().is("action.view.scene.pickup"))
    {
        if (!pAction->action().checked())
        {
            _pComPRCoordinate->hide();
        }
        else
        {
            _pComPRCoordinate->init();
            _pComPRCoordinate->show();
        }
    }
}

void UIComSceneViewer::initViewerContextMenu()
{
    // 获取主题字符串
    auto themeStr = mWindow().themeType();
    // 模块字符串
    auto moduleStr = QString::fromUtf8(mWindow().moduleType().c_str());
    // 图标
    QString iconPath = qApp->applicationDirPath() + "/../data" + "/mainWindowIcon/" + themeStr + "/ribbonBar/" + moduleStr;
    // 选择当前
    _pActionSelectCurrent = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Select Current").c_str()));
    _pMenu->addAction(_pActionSelectCurrent);
    _pActionSelectCurrent->setVisible(false);
    _pActionSelectCurrent->setIcon(QIcon(iconPath + "/View/" + "SELECT_CURRENT.png"));
    connect(_pActionSelectCurrent, &QAction::triggered, [=]()
        {
            auto pNode = _currentSelectedNode.lock();
            if (pNode == nullptr)
                return;
            // 设置模型树当前节点
            _core.nodeTree().setCurrentNode(pNode);
            // 设置选择的节点列表
            _core.scene().setSelectedNodes({pNode});
            // 选择的节点发生改变了，触发重绘
            _core.needRepaint();
        });
    // 居中
    QAction* pActionCenter = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Center").c_str()));
    _pMenu->addAction(pActionCenter);
    pActionCenter->setIcon(QIcon(iconPath + "/View/" + "CENTER_VIEW.png"));
    connect(pActionCenter, &QAction::triggered, [=]() 
        {
            auto aabb = this->getLookAtAabb();
            mWindow().core().viewer().lookAtAabb(aabb);
        });
    // 移除
    if (mWindow().moduleType() == "Design")
    {
        QAction* pActionRemove = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Remove").c_str()));
        _pMenu->addAction(pActionRemove);
        pActionRemove->setIcon(QIcon(iconPath + "/View/" + "CLEAR_VIEW.png"));
        connect(pActionRemove, &QAction::triggered, [=]()
            {
                // 获取场景中具有选中标志的节点
                auto sNodes = mWindow().core().scene().selectedNodes();
                if (sNodes.empty())
                    return;
                // 批量移除
                mWindow().core().scene().remove(sNodes);
                mWindow().core().needRepaint();
            });
        // 清除
        QAction* pActionClear = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Clear").c_str()));
        _pMenu->addAction(pActionClear);
        pActionClear->setIcon(QIcon(iconPath + "/View/" + "CLEAR_VIEW.png"));
        connect(pActionClear, &QAction::triggered, [=]() {
            this->onActionClearSceneTriggered();
            });
    }

    // Look
    QAction* pActionLook = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Look").c_str()));
    _pMenu->addAction(pActionLook);
    pActionLook->setIcon(QIcon(iconPath + "/Common/" + "NODE_TREE.png"));
    // 上
    QAction* pActionTop = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Top").c_str()));
    pActionTop->setIcon(QIcon(iconPath + "/View/" + "TOP_VIEW.png"));
    pActionTop->setData(WD::WDViewer::LookAtDir::LAD_Top);
    _pActionGroup->addAction(pActionTop);
    _subMenuLook.addAction(pActionTop);
    // 下
    QAction* pActionBottom = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Bottom").c_str()));
    pActionBottom->setIcon(QIcon(iconPath + "/View/" + "BOTTOM_VIEW.png"));
    pActionBottom->setData(WD::WDViewer::LookAtDir::LAD_Bottom);
    _pActionGroup->addAction(pActionBottom);
    _subMenuLook.addAction(pActionBottom);
    // 前
    QAction* pActionFront = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Front").c_str()));
    pActionFront->setIcon(QIcon(iconPath + "/View/" + "FRONT_VIEW.png"));
    pActionFront->setData(WD::WDViewer::LookAtDir::LAD_Front);
    _pActionGroup->addAction(pActionFront);
    _subMenuLook.addAction(pActionFront);
    // 后
    QAction* pActionBack = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Back").c_str()));
    pActionBack->setIcon(QIcon(iconPath + "/View/" + "BACK_VIEW.png"));
    pActionBack->setData(WD::WDViewer::LookAtDir::LAD_Back);
    _pActionGroup->addAction(pActionBack);
    _subMenuLook.addAction(pActionBack);
    // 左
    QAction* pActionLeft = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Left").c_str()));
    pActionLeft->setIcon(QIcon(iconPath + "/View/" + "LEFT_VIEW.png"));
    pActionLeft->setData(WD::WDViewer::LookAtDir::LAD_Left);
    _pActionGroup->addAction(pActionLeft);
    _subMenuLook.addAction(pActionLeft);
    // 右
    QAction* pActionRight = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Right").c_str()));
    pActionRight->setIcon(QIcon(iconPath + "/View/" + "RIGHT_VIEW.png"));
    pActionRight->setData(WD::WDViewer::LookAtDir::LAD_Right);
    _pActionGroup->addAction(pActionRight);
    _subMenuLook.addAction(pActionRight);
    // 将二级菜单添加为一级菜单项Look的子菜单
    pActionLook->setMenu(&_subMenuLook);
    // Plan
    QAction* pActionPlan = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "Plan").c_str()));
    _pMenu->addAction(pActionPlan);
    pActionPlan->setIcon(QIcon(iconPath + "/Common/" + "NODE_TREE.png"));
    // 北
    QAction* pActionNorth = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "North").c_str()));
    pActionNorth->setIcon(QIcon(iconPath + "/View/" + "NORTH_VIEW.png"));
    pActionNorth->setData(WD::WDViewer::LookAtDir::LAD_North);
    _pActionGroup->addAction(pActionNorth);
    _subMenuPlan.addAction(pActionNorth);
    // 南
    QAction* pActionSouth = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "South").c_str()));
    pActionSouth->setIcon(QIcon(iconPath + "/View/" + "SOUTH_VIEW.png"));
    pActionSouth->setData(WD::WDViewer::LookAtDir::LAD_South);
    _pActionGroup->addAction(pActionSouth);
    _subMenuPlan.addAction(pActionSouth);
    // 东
    QAction* pActionEast = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "East").c_str()));
    pActionEast->setIcon(QIcon(iconPath + "/View/" + "EAST_VIEW.png"));
    pActionEast->setData(WD::WDViewer::LookAtDir::LAD_East);
    _pActionGroup->addAction(pActionEast);
    _subMenuPlan.addAction(pActionEast);
    // 西
    QAction* pActionWest = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "West").c_str()));
    pActionWest->setIcon(QIcon(iconPath + "/View/" + "WESTERN_VIEW.png"));
    pActionWest->setData(WD::WDViewer::LookAtDir::LAD_West);
    _pActionGroup->addAction(pActionWest);
    _subMenuPlan.addAction(pActionWest);
    // 将二级菜单添加为一级菜单项Plan的子菜单
    pActionPlan->setMenu(&_subMenuPlan);
    // ISO
    QAction* pActionIso = new QAction(QString::fromUtf8(WD::WDTs("UIComSceneViewer", "ISO").c_str()));
    _pMenu->addAction(pActionIso);
    pActionIso->setIcon(QIcon(iconPath + "/Common/" + "NODE_TREE.png"));
    // ISO1
    QAction* pActionIso1 = new QAction("ISO1");
    pActionIso1->setIcon(QIcon(iconPath + "/View/" + "ISO1.png"));
    pActionIso1->setData(WD::WDViewer::LookAtDir::LAD_ISO1);
    _pActionGroup->addAction(pActionIso1);
    _subMenuIso.addAction(pActionIso1);
    // ISO2
    QAction* pActionIso2 = new QAction("ISO2");
    pActionIso2->setIcon(QIcon(iconPath + "/View/" + "ISO2.png"));
    pActionIso2->setData(WD::WDViewer::LookAtDir::LAD_ISO2);
    _pActionGroup->addAction(pActionIso2);
    _subMenuIso.addAction(pActionIso2);
    // ISO3
    QAction* pActionIso3 = new QAction("ISO3");
    pActionIso3->setIcon(QIcon(iconPath + "/View/" + "ISO3.png"));
    pActionIso3->setData(WD::WDViewer::LookAtDir::LAD_ISO3);
    _pActionGroup->addAction(pActionIso3);
    _subMenuIso.addAction(pActionIso3);
    // ISO4
    QAction* pActionIso4 = new QAction("ISO4");
    pActionIso4->setIcon(QIcon(iconPath + "/View/" + "ISO4.png"));
    pActionIso4->setData(WD::WDViewer::LookAtDir::LAD_ISO4);
    _pActionGroup->addAction(pActionIso4);
    _subMenuIso.addAction(pActionIso4);
    // 将二级菜单添加为一级菜单项ISO的子菜单
    pActionIso->setMenu(&_subMenuIso);
}


void UIComSceneViewer::slotActionGroupTriggered(QAction* action)
{
    if (action == nullptr)
        return;

    int type = action->data().toInt();
    this->lookAt(static_cast<WD::WDViewer::LookAtDir>(type));
}

void UIComSceneViewer::onActionRemoveFromSceneTriggered()
{
    auto pNode = mWindow().core().nodeTree().currentNode();
    if (pNode == nullptr)
        return;
    mWindow().core().scene().remove(pNode);
    mWindow().core().needRepaint();
}

void UIComSceneViewer::onActionClearSceneTriggered()
{
    mWindow().core().scene().clear();
    mWindow().core().needRepaint();
}


void UIComSceneViewer::onActionSceneAxisVisible(bool visible)
{
    // 设置场景轴显隐
    mWindow().core().viewer().viewerSceneAxis().setVisible(visible);
    mWindow().core().viewer().needRepaint();
}

void UIComSceneViewer::onActionScreenShotTriggered()
{
    auto vSize  = mWindow().core().viewer().size();
    auto image  = mWindow().core().viewer().getSnapShotImage(0, 0, vSize[0], vSize[1]);
    if (image.data().empty())
        return;

    QString savePath = QFileDialog::getSaveFileName(this, QString::fromUtf8(WD::WDTs("UIComSceneViewer", "save").c_str()), "", "png(*.png)");
    if (savePath.size() == 0) return;

    QString suffix(".png");
    if (!savePath.endsWith(suffix, Qt::CaseInsensitive))
        savePath += suffix;

    //垂直翻转图像（一些接口如glReadPixels等，读取顺序为从下往上，因此需要调整图像）
    image.verticalFlip();
    //选择文件保存路径
    //保存到图片
    QString inf;
    image.save(savePath.toLocal8Bit().data()) ? inf = "succeed" : inf = "fail";
    WD_INFO_T("UIComSceneViewer", inf.toUtf8().toStdString());
}

DAabb3 UIComSceneViewer::getLookAtAabb() const
{
    {
        auto& scene = mWindow().core().scene();
        DAabb3 tAabb = DAabb3::Null();
        // 先尝试从场景中获取选中对象的Aabb
        auto sNodes = scene.selectedNodes();
        for (auto pNode : sNodes) 
        {
            if (pNode == nullptr)
                continue;
            tAabb.unions(pNode->aabb());
        }
        if (!tAabb.isNull())
            return tAabb;
        // 如果没有选中对象，检查场景Aabb是否有效，如果有效，则使用场景的Aabb
        if (!scene.aabb().isNull())
            return scene.aabb();
    }
    // 否则使用模型树根节点Aabb
    DAabb3 tAabb = DAabb3::Null();
    const auto& tree = mWindow().core().nodeTree();
    for (size_t i = 0; i < tree.topLevelNodeCount(); ++i)
    {
        auto pNode = tree.topLevelNode(i);
        if (pNode == nullptr)
            continue;
        tAabb.unions(pNode->aabb());
    }
    if (!tAabb.isNull())
        return tAabb;

    // 否则使用场景Aabb的默认值
    return WD::WDScene::DefaultAabb();
}
void UIComSceneViewer::lookAt(WD::WDViewer::LookAtDir dir) const
{
    auto aabb = this->getLookAtAabb();
    mWindow().core().viewer().lookAtAabb(aabb, dir);
    mWindow().core().viewer().needRepaint();
}