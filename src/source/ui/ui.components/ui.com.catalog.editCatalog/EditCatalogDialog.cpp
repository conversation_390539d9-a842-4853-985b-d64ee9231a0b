#include "EditCatalogDialog.h"
#include "core/WDCore.h"
#include "EditCatalogWidget/EditComCommon.h"
#include "viewer/WDViewer.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/dataType/WDBMGVars.h"
#include "core/common/WDConfig.h"
#include "core/businessModule/catalog/WDBMCataUtils.h"
#include "core/geometry/WDGeometryBoolean.h"

static constexpr const char* Purpose_DESP = "DESP";

EditCatalogDialog::EditCatalogDialog(WD::WDCore& core, WD::WDBMCatalog& catalogMgr, QWidget* parent)
    :QDialog(parent)
    , _core(core)
    , _catalogMgr(catalogMgr)
    , _widgeCataTab(catalogMgr, this)
    , _widgetCataInfo(core, this)
    , _widgetModeRef(catalogMgr, this)
    , _widgetCataPars(this)
    , _pNodeAttrWidget(core.getBMCatalog(), NodeAttributeWidget::F_None)
    , _widgetEditVertex(this)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    //翻译
    this->retranslateUi();
    setLayout(ui.gridLayout_6);
    //将各子窗口添加到主窗口布局中
    ui.verticalLayoutModelRefW->addWidget(&_widgetModeRef);
    ui.groupBoxModelData->setHidden(true);
    ui.groupBoxDataAttrs->setHidden(true);

    ui.verticalLayoutComParsW->addWidget(&_widgetCataPars);
    _widgeCataTab.hide();
    _widgetCataInfo.hide();
    _widgetEditVertex.hide();

    //数据属性窗口表头翻译
    _pNodeAttrWidget.trFunction() = [](const std::string& key)-> std::string
    {
        return WD::WDTs("EditCatalogDialog", key);
    };
    ui.verticalLayoutAttrsW->addWidget(_pNodeAttrWidget.getWidget());
    _pStackedWids = new QStackedWidget();
    _pStackedWids->addWidget(&_widgeCataTab);
    _pStackedWids->addWidget(&_widgetCataInfo);
    ui.verticalLayoutLabel->addWidget(_pStackedWids);

    //模型引用注册类型与集属性
    _widgetModeRef.add("SCOM", {"Ptref", "Gmref", "Dtref", "Blrfarray" });
    _widgetModeRef.add("SPRF", {"Pstref", "Gstref", "Dtref" });
    _widgetModeRef.add("JOIN", {"Pstref", "Ptref", "Gmref", "Ngmref", "Dtref" });
    _widgetModeRef.add("SFIT", {"Ptref", "Gmref", "Ngmref", "Dtref" });

    //元件信息窗口注册类型与集属性
    std::vector<std::string> cataComAttr = { "Name", "Description", "Param", "Gtype" };
    _widgetCataInfo.group().add("SCOM", {cataComAttr, {"Ptref", "Gmref", "Dtref", "Blrfarray" }});
    _widgetCataInfo.group().add("SPRF", {cataComAttr, {"Pstref", "Gstref", "Dtref" }});
    _widgetCataInfo.group().add("JOIN", {cataComAttr, {"Pstref", "Ptref", "Gmref", "Ngmref", "Dtref" }});
    _widgetCataInfo.group().add("SFIT", {cataComAttr, {"Ptref", "Gmref", "Ngmref", "Dtref" }});

    //模型数据窗口--集窗口的创建和信号绑定
    PointSetWidget* pPointSetWidget = new PointSetWidget(_core, this);
    _setWidgetMgr.add("PTSE", pPointSetWidget);
    connect(this, &EditCatalogDialog::sigModeRefCurrentItemName, pPointSetWidget, &PointSetWidget::slotDynamicNodeTree);

    GeometrySetWidget* pScomGeomSetWidget = new GeometrySetWidget(_core, this);
    _setWidgetMgr.add("GMSE", pScomGeomSetWidget);
    connect(this, &EditCatalogDialog::sigModeRefCurrentItemName, pScomGeomSetWidget, &GeometrySetWidget::slotDynamicNodeTree);

    StruGeometrySetWidget* pStruGeomSetWidget = new StruGeometrySetWidget(_core, this);
    _setWidgetMgr.add("GMSS", pStruGeomSetWidget);
    connect(this, &EditCatalogDialog::sigModeRefCurrentItemName, pStruGeomSetWidget, &StruGeometrySetWidget::slotDynamicNodeTree);

    StruNGeometrySetWidget* pStruNGeomSetWidget = new StruNGeometrySetWidget(_core, this);
    _setWidgetMgr.add("NGMS", pStruNGeomSetWidget);
    connect(this, &EditCatalogDialog::sigModeRefCurrentItemName, pStruNGeomSetWidget, &StruNGeometrySetWidget::slotDynamicNodeTree);

    DataSetWidget* pDataSetWidget = new DataSetWidget(_core, this);
    _setWidgetMgr.add("DTSE", pDataSetWidget);
    connect(this, &EditCatalogDialog::sigModeRefCurrentItemName, pDataSetWidget, &DataSetWidget::slotDynamicNodeTree);

    PLineSetWidget* pPLineSetWidget = new PLineSetWidget(_core, this);
    _setWidgetMgr.add("PTSS", pPLineSetWidget);
    connect(this, &EditCatalogDialog::sigModeRefCurrentItemName, pPLineSetWidget, &PLineSetWidget::slotDynamicNodeTree);

    BoltSetWidget* pBolotSetWidget = new BoltSetWidget(_core, this);
    _setWidgetMgr.add("BTSE", pBolotSetWidget);
    connect(this, &EditCatalogDialog::sigModeRefCurrentItemName, pBolotSetWidget, &BoltSetWidget::slotDynamicNodeTree);

    //注册所有类型的集子节点类型和子节点需要展示的属性
    // PTSE
    std::vector<std::string> keyPtBaseANames = { "Name", "Number" , "Pconnect" , "Pbore" , "Pskey" , "Pzaxis" };
    _attrConvert.add("PTCA", { keyPtBaseANames, {"Px", "Py", "Pz", "Ptcdirection"} });
    _attrConvert.add("PTAX", { keyPtBaseANames, { "Paxis", "Pdistance" } });
    _attrConvert.add("PTMI", { keyPtBaseANames, {"Px", "Py", "Pz", "Paxis"} });
    _attrConvert.add("PTPOS", { keyPtBaseANames, {"Ptcposition", "Ptcdirection","Purpose", "Pvifilter","AllAngle", "Pwidth","Pheight", "OffTolerance"} });
    // GMSE
    std::vector<std::string> geomBaseANames = { "Name", "Obstruction" , "Level" , "Purpose", "Clflag" , "Tuflag"};
    _attrConvert.add("SBOX", { geomBaseANames, {"Px", "Py", "Pz", "Pxlength", "Pylength", "Pzlength"} });
    _attrConvert.add("BOXI", { geomBaseANames, {"Paxis", "Pxlength", "Pzlength"}});
    _attrConvert.add("LSNO", { geomBaseANames, {"Ptdistance", "Pbdistance", "Ptdiameter", "Pbdiameter", "Paaxis", "Pbaxis", "Poffset"}});
    _attrConvert.add("SCON", { geomBaseANames, {"Pdistance", "Pdiameter", "Paxis"}});
    _attrConvert.add("SSPH", { geomBaseANames, {"Pdistance", "Pdiameter", "Paxis"}});
    _attrConvert.add("LCYL", { geomBaseANames, {"Ptdistance", "Pbdistance", "Pdiameter", "Paxis"}});
    _attrConvert.add("SCYL", { geomBaseANames, {"Pdistance", "Pdiameter", "Pheight", "Paxis"} });
    _attrConvert.add("SSLC", { geomBaseANames, {"Pdiameter", "Pheight", "Pdistance", "Paxis", "Pxtshear", "Pytshear", "Pxbshear", "Pybshear"}});
    _attrConvert.add("SCTO", { geomBaseANames, {"Paaxis", "Pbaxis", "Pdiameter"}});
    _attrConvert.add("SRTO", { geomBaseANames, {"Paaxis", "Pbaxis", "Pdiameter", "Pheight"}});
    _attrConvert.add("TUBE", { geomBaseANames, {"Paxis", "Pdiameter"}});
    _attrConvert.add("LPYR", { geomBaseANames, {"Pbaxis", "Pcaxis", "Paaxis", "Pboffset", "Pcoffset", "Ptdistance", "Pbdistance", "Pbtplength", "Pctplength", "Pbbtlength", "Pcbtlength"}});
    _attrConvert.add("SDIS", { geomBaseANames, {"Pdistance", "Pdiameter", "Paxis"}});
    _attrConvert.add("SDSH", { geomBaseANames, {"Pdiameter", "Pheight", "Pdistance", "Paxis"}});
    _attrConvert.add("LINE", { geomBaseANames, {"Pts", "Diameter"}});
    _attrConvert.add("SEXT", { geomBaseANames, {"Px", "Py", "Pz", "Paaxis", "Pbaxis", "Pheight"}});
    _attrConvert.add("SREV", { geomBaseANames, {"Px", "Py", "Pz", "Paaxis", "Pbaxis", "Pangle"}});
    _attrConvert.add("SLOO", { "Purpose"});
    _attrConvert.add("SVER", { "Px", "Py", "Pradius" });
    // GMSS
    std::vector<std::string> struGeomBaseANames = { "Name", "Clflag" , "Tuflag" , "Obstruction" , "Level" , "Purpose" };
    _attrConvert.add("SREC", { struGeomBaseANames, { "Px", "Py", "Pxlength", "Pylength", "Plaxis", "Dx", "Dy", "Dxl",  "Dyl"} });
    _attrConvert.add("SANN", { struGeomBaseANames, { "Px", "Py", "Plaxis", "Pangle", "Pradius", "Pwidth","Dx", "Dy", "Dradius" , "Dwidth"}});
    _attrConvert.add("SPRO", { struGeomBaseANames, { "Plaxis"}});
    _attrConvert.add("SPVE", { "Name", "Px", "Py", "Pradius", "Dx", "Dy", "Dradius"} );
    //NGMS
    std::vector<std::string> ngeomBaseANames = { "Name", "Clflag" , "Tuflag" , "Obstruction" , "Level" , "Purpose" };
    _attrConvert.add("NSBO", { ngeomBaseANames, {"Px", "Py", "Pz", "Pxlength", "Pylength", "Pzlength"}});
    _attrConvert.add("NLSN", { ngeomBaseANames, {"Ptdistance", "Pbdistance", "Ptdiameter", "Pbdiameter", "Paaxis", "Pbaxis", "Poffset"}});
    _attrConvert.add("NSCO", { ngeomBaseANames, {"Pdistance", "Pdiameter", "Paxis"}});
    _attrConvert.add("NSSP", { ngeomBaseANames, {"Pdistance", "Pdiameter", "Paxis"}});
    _attrConvert.add("NLCY", { ngeomBaseANames, {"Ptdistance", "Pbdistance", "Pdiameter", "Paxis"} });
    _attrConvert.add("NSCY", { ngeomBaseANames, {"Pdistance", "Pdiameter", "Pheight", "Paxis"}});
    _attrConvert.add("NSSL", { ngeomBaseANames, {"Pdiameter", "Pheight", "Pdistance", "Paxis", "Pxtshear", "Pytshear", "Pxbshear", "Pybshear"}});
    _attrConvert.add("NSCT", { ngeomBaseANames, {"Paaxis", "Pbaxis", "Pdiameter"}});
    _attrConvert.add("NSRT", { ngeomBaseANames, {"Paaxis", "Pbaxis", "Pdiameter", "Pheight"}});
    _attrConvert.add("NLPY", { ngeomBaseANames, {"Pbaxis", "Pcaxis", "Paaxis", "Pboffset", "Pboffset", "Pcoffset", "Ptdistance", "Pbdistance", "Pbtplength", "Pctplength", "Pbbtlength","Pcbtlength"}});
    _attrConvert.add("NSDS", { ngeomBaseANames, {"Pdiameter", "Pheight", "Pradius", "Pdistance", "Paxis"}});
    _attrConvert.add("NSEX", { ngeomBaseANames, {"Px", "Py", "Pz", "Paaxis", "Pbaxis", "Pheight", "Bvsegn", "BVCODN"}});
    _attrConvert.add("SLOO", { "Name", "Purpose"});
    _attrConvert.add("SVER", { "Name", "Px", "Py", "Pradius"});
    //DTSE
    _attrConvert.add("DATA", { "Name", "Dtitle", "Dkey", "Purpose","Number", "Lhide", "Ptype", "Dproperty", "Pproperty"});
    //PTSS
    _attrConvert.add("PLIN", { "Name", "Pkey", "Px", "Py", "Dx", "Dy", "Plaxis", "Level", "Cconnect"});
    //BTSE
    _attrConvert.add("BLTP", { "Name", "Description", "Number", "Bdiameter", "Btype", "Bthkness" });

    //各子界面的信号信号槽绑定
    connect(&_widgetModeRef, &ModelRefWidget::sigCurrentItemChanged, this, &EditCatalogDialog::slotModelRefWItemChanged);
    connect(&_widgetCataPars, SIGNAL(sigDataChanged()), this, SLOT(slotSubWidgetUpdate()));
    connect(&_widgeCataTab, SIGNAL(sigDataChanged()), this, SLOT(slotSubWidgetUpdate()));
    //元件表当前项改变时，
    connect(&_widgeCataTab
        , &CataTableWidget::sigCurrentCataObjectChanged
        , this
        , [&](WD::WDNode::SharedPtr node) {

        _pCataNode = node;
        _widgetModeRef.updateWidget(_pCataNode.lock());
        _widgetCataPars.updateWidget(_pCataNode.lock());
        updateSceneCataModel(true);
    });
    //修改元件信息界面， 更新模型引用窗口
    connect(&_widgetCataInfo
        , &CataInfoWidget::signalUpdate
        , this
        , [&](WD::WDNode::SharedPtr& node) {
        auto cateNode = node->parent();
    if (cateNode == nullptr)
        return;
    _widgetModeRef.updateWidget(node);
    _widgetCataPars.updateWidget(node);
    });
    //元件参数个数改变信号
    connect(&_widgetCataPars, &CataParamsWidget::sigParamsCountChanged, &_widgeCataTab, &CataTableWidget::slotParamsCountChanged);
    connect(ui.pushButtonAttrApply, SIGNAL(clicked()), this , SLOT(slotAttrApply()));
    connect(ui.pushButtonCE, SIGNAL(clicked()), this, SLOT(slotCEBtn()));
}
EditCatalogDialog::~EditCatalogDialog()
{
    _pCataNode.reset();
}

bool EditCatalogDialog::updateWidget(WD::WDNode::SharedPtr pNode)
{
    //为空移除模型
    if(pNode == nullptr)
    {
        updateSceneCataModel(true);
        return false;
    }
    //节点树当前节点为元件类型更新界面
    if (IsComponentNode(*pNode))
    {
        _pCataNode = pNode;
    }
    else
    {
        //向上查找目录节点
        auto cateNode = findCategoryNode(pNode);
        if (cateNode == nullptr)
            return false;
        //获取目录下的第一个元件节点
        auto pCataNode = firstCataObjectCategory(cateNode);
        if (pCataNode == nullptr)
            return false;
        _pCataNode = pCataNode;

    }  
    // 更新所有子窗口
    updateSubWidgets();
    // 更新模型
    updateSceneCataModel(true);
 
    return true;
}

static void DespToGVars(WD::WDNode& scom, WD::WDBMGVars& gVars)
{
    char buf[1024] = { 0 };
    // 元件引用的数据集来作为更新参数的默认值
    auto pDtse = scom.getAttribute("Dtref").toNodeRef().refNode();
    if (pDtse != nullptr)
    {
        for (size_t i = 0; i < pDtse->childCount(); i++)
        {
            auto pDTNode = pDtse->childAt(i);
            if (pDTNode == nullptr)
                continue;
            auto purpose = pDTNode->getAttribute("Purpose").toWord();
            if (purpose != Purpose_DESP)
                continue;

            auto    dPty    =   pDTNode->getAttribute("Dproperty").toString();
            auto    number  =   pDTNode->getAttribute("Number").toInt();
            // 解析DESP默认值
            if (number >= 1)
            {
                if (!dPty.empty())
                {
                    sprintf(buf, "DESP %d", number);
                    gVars.setValue(buf, dPty);
                }
            }
        }
    }
    // 再用设计参数管理中的值来覆盖
    for (const auto& desp : WD::Core().getBMCatalog().despMgr().desps())
    {
        sprintf_s(buf, sizeof(buf), "DESP %d", desp.first);
        gVars.setValue(buf, desp.second);
    }
}
void EditCatalogDialog::updateSceneCataModel(bool reLocationView)
{
    auto& scene = _core.scene();
    // 场景清空
    scene.clear();
    // 解除上一次点集型集数据与对应节点的关联
    this->removeDataAssociatedNode();

    //模型中加载元件
    WD::WDNode::SharedPtr pCataNode = _pCataNode.lock();
    if (pCataNode == nullptr)
        return;

    auto& modelBuilder = _catalogMgr.modelBuilder();
    //创建元件模型,拿到点集型集数据
    std::map<std::string, std::any> varMap;
    // 构建元件模型
    _sCataCmData = modelBuilder.build(pCataNode, [&varMap, pCataNode](const std::string& name, std::any& outValue, const std::optional<int>& index) 
        {
            WDUnused(index);
            if (varMap.empty())
            {
                varMap["DDANGLE"] = 90.0f;
                varMap["DDHEIGHT"] = 100.0f;
                varMap["DDANGLE"] = 90.0f;
                // DDRADIUS默认值为第一个参数公称直径的两倍（从PDMS的数据找规律找出来的。。。)
                double  defaultRadius = 0.0;
                auto paramValues = pCataNode->getAttribute("Param").toStringVector();
                if (!paramValues.empty())
                {
                    bool bOk = false;
                    auto tValue = 2.0 * WD::FromString<double>(paramValues.at(0), &bOk);
                    if (bOk)
                        defaultRadius = tValue;
                }
                varMap["DDRADIUS"] = defaultRadius;
            }
            // 部分全局变量
            auto fItr = varMap.find(name);
            if (fItr != varMap.end())
            {
                outValue = fItr->second;
                return true;
            }
            // IParam系列
            if (_stricmp("IPARAM", name.c_str()) == 0
                || _stricmp("IPAR", name.c_str()) == 0)
            {
                outValue = 0.0f;
                return true;
            }
            // 其他
            assert(false && "!注意: 出现了不支持的变量");
            return false;
        });
    
    WD::DAabb3 totalAabb = WD::DAabb3::Null();
    // 点集
    for (const auto& keyPoint : _sCataCmData.keyPoints)
    {
        auto pNode = keyPoint.node.lock();
        if (pNode == nullptr)
            continue;
        // 设置点集数据到节点，用于场景绘制
        WD::WDBMCataUtils::SetKeyPoints(*pNode, { keyPoint.keyPoint });
        
        // 更新节点
        pNode->update(true);
        // 添加到场景
        scene.add(pNode);
        // 合并包围盒
        totalAabb.unions(pNode->aabb());
    }
    // PLine
    for (const auto& pline : _sCataCmData.pLines)
    {
        auto pNode = pline.node.lock();
        if (pNode == nullptr)
            continue;
        // 设置PLines数据到节点，用于场景绘制
        WD::WDBMCataUtils::SetPLines(*pNode, { pline.pLine });
        // 更新节点
        pNode->update(true);
        // 添加到场景
        scene.add(pNode);
        // 合并包围盒
        totalAabb.unions(pNode->aabb());
    }
    // 是否开孔
    bool bHoles = _core.holesDrawn().first;
    WD::MeshLODSelection holesLod = WD::MeshLODSelection(_catalogMgr.core().holesDrawn().second);
    // 获取配置中是否绘制软碰撞模型
    auto& sceneCfg = _core.cfg().get<std::string>("scene");
    const float* pObst = sceneCfg.get<float>("model.obstruction", 0.0f).value<float>();
    bool bObst = (pObst != nullptr && (*pObst) >= 0.001f);
    // 获取配置中是否绘制保温模型
    const float* pInsu = sceneCfg.get<float>("pipe.insulation", 0.0f).value<float>();
    bool bInsu = (pInsu != nullptr && (*pInsu) >= 0.001f);
    // 型集
    for (const auto& gm : _sCataCmData.geoms)
    {
        auto pNode = gm.node.lock();
        if (pNode == nullptr || gm.pGeom == nullptr)
            continue;

        const auto& flags = gm.pGeom->gFlags();
        bool bTmpAdd = false;

        // 负实体
        if (flags.hasFlag(WD::WDGeometry::GF_Negative))
        {
            // 如果未开孔，则加入负实体
            if (!bHoles) 
                bTmpAdd = true;
        }
        // 保温
        else if (flags.hasFlag(WD::WDGeometry::GF_Insu))
        {
            // 开启了保温绘制且只有带有实体/中心线标记的保温模型才会被加入
            if (bInsu && flags.hasAnyOfFlags(WD::WDGeometry::GF_Entity, WD::WDGeometry::GF_CenterLine))
                bTmpAdd = true;
        }
        // 实体
        else if (flags.hasFlag(WD::WDGeometry::GF_Entity))
        {
            bTmpAdd = true;
        }
        // 软碰撞或硬碰撞
        else if (flags.hasAnyOfFlags(WD::WDGeometry::GF_CSoft, WD::WDGeometry::GF_CHard))
        {
            // 开启了软碰撞绘制
            if (bObst)
                bTmpAdd = true;
        }
        if (!bTmpAdd)
            continue;

        // 校验层级
        if (!WD::WDBMLevelRange::Check(gm.pGeom->level(), WD::WDBMLevelRange::DefaultLevel))
            continue;

        // 不开孔
        if (!bHoles)
        {
            // 设置型集数据到节点，用于场景绘制
            WD::WDBMCataUtils::SetGeometries(*pNode, { gm.pGeom });

            // 设置负型数据到节点, 用于场景绘制
            for (const auto& ngm : gm.nGeoms)
            {
                auto pNNode = ngm.node.lock();
                if (pNNode == nullptr || ngm.pGeom == nullptr)
                    continue;
                // 设置型集数据到节点，用于场景绘制
                WD::WDBMCataUtils::SetGeometries(*pNNode, { ngm.pGeom });
            }
        }
        // 开孔
        else
        {
            // 统计负型数据, 用于开孔
            WD::WDGeometries nGeoms;
            nGeoms.reserve(gm.nGeoms.size());
            for (const auto& ngm : gm.nGeoms)
            {
                auto pNNode = ngm.node.lock();
                if (pNNode == nullptr || ngm.pGeom == nullptr)
                    continue;
                nGeoms.push_back(ngm.pGeom);
            }
            auto pRGeom = WD::WDGeometryBoolean::Diff(_catalogMgr.core().geometryMgr()
                , gm.pGeom
                , { std::make_pair(std::nullopt, nGeoms)}
                , holesLod
                , gm.pGeom->gFlags()
                , gm.pGeom->level()
                , true);
            // 开孔成功
            if (pRGeom != nullptr) 
            {
                WD::WDBMCataUtils::SetGeometries(*pNode, { pRGeom });
            }
            // 如果开孔失败，则设置为原来的几何体
            else
            {
                WD::WDBMCataUtils::SetGeometries(*pNode, { gm.pGeom });
            }
        }
        // 更新节点
        pNode->update(true);
        // 添加到场景
        scene.add(pNode);
        // 合并包围盒
        totalAabb.unions(pNode->aabb());
    }
    // 负型集, 不开孔时，加入负型集
    if (!bHoles)
    {
        for (const auto& ngm : _sCataCmData.nGeoms)
        {
            auto pNode = ngm.node.lock();
            if (pNode == nullptr || ngm.pGeom == nullptr)
                continue;
            // 设置负型集数据到节点，用于场景绘制
            WD::WDBMCataUtils::SetGeometries(*pNode, { ngm.pGeom });
            // 更新节点
            pNode->update(true);
            // 添加到场景
            scene.add(pNode);
            // 合并包围盒
            totalAabb.unions(pNode->aabb());
        }
    }
    // 相机对准到节点包围盒
    if (!totalAabb.isNull() && reLocationView)
        _core.viewer().lookAtAabb(totalAabb);

    // 触发重绘
    _core.needRepaint();
}

void EditCatalogDialog::onSetNodeChanged(WD::WDNode::SharedPtr pSetNode)
{
    if (pSetNode == nullptr)
        return;

    auto pSetWidget = _setWidgetMgr.getWidget(*pSetNode);
    if (pSetWidget != nullptr)
    {
        ui.groupBoxModelData->setHidden(false);
        ui.groupBoxDataAttrs->setHidden(false);

        pSetWidget->setNoticeItemClicked(std::bind(&EditCatalogDialog::onSetWidgetItemClicked, this));
        pSetWidget->setNoticeCurrentChanged(std::bind(&EditCatalogDialog::onSetWidgetCurrentNodeChanged, this, std::placeholders::_1, std::placeholders::_2));


        if (_dynModeDWidget != nullptr)
        {
            _dynModeDWidget->hide();
            ui.verticalLayoutModelData->removeWidget(_dynModeDWidget);
        }
        _dynModeDWidget = pSetWidget;
        ui.verticalLayoutModelData->addWidget(_dynModeDWidget);
        pSetWidget->reflushByNode(*pSetNode);
        _dynModeDWidget->show();

        //当前集窗口为GMSS结构型集 绑定点击按钮打开轮廓点槽函数
        auto pW = dynamic_cast<StruGeometrySetWidget*>(pSetWidget);
        if(pW != nullptr)
        {
            pW->setCallBackOpenVW(std::bind(&EditCatalogDialog::showVertexW, this, std::placeholders::_1));
        }
        else
        {
            //非GMSS类型的不展示轮廓点窗口
            showVertexW(false);
        }
    }
}
void EditCatalogDialog::onSetWidgetCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode, WD::WDNode::SharedPtr pPrevNode)
{
    WDUnused(pPrevNode);
    if (pCurrNode == nullptr)
        return;
    auto pBMBase = pCurrNode->getBMBase();
    if (pBMBase == nullptr)
        return;
    auto rAttrs = _attrConvert.query(*pCurrNode);
    //更新属性窗口
    _pNodeAttrWidget.initWidget(rAttrs);
    _pNodeAttrWidget.setNode(pCurrNode);
    //更新轮廓点窗口
    _widgetEditVertex.initVertexEditTable(pCurrNode);
}
void EditCatalogDialog::onSetWidgetItemClicked()
{
    emit sigModeRefCurrentItemName(_widgetModeRef.getCurrentItemName());
}
void EditCatalogDialog::removeDataAssociatedNode()
{
    // 点集
    for (const auto& keyPoint : _sCataCmData.keyPoints)
    {
        auto pNode = keyPoint.node.lock();
        if (pNode == nullptr)
            continue;
        WD::WDBMCataUtils::ResetKeyPoints(*pNode);
        pNode->update(true);
    }
    // PLine
    for (const auto& pline : _sCataCmData.pLines)
    {
        auto pNode = pline.node.lock();
        if (pNode == nullptr)
            continue;
        WD::WDBMCataUtils::ResetPLines(*pNode);
        pNode->update(true);
    }
    // 型集
    for (const auto& gm : _sCataCmData.geoms)
    {
        auto pNode = gm.node.lock();
        if (pNode == nullptr)
            continue;

        // 负型
        for (const auto& ngm : gm.nGeoms)
        {
            auto pNNode = ngm.node.lock();
            if (pNNode == nullptr)
                continue;
            WD::WDBMCataUtils::ResetGeometries(*pNNode);
        }

        WD::WDBMCataUtils::ResetGeometries(*pNode);
        pNode->update(true);
    }
    // 负型集
    for (const auto& nEdgeGeometry : _sCataCmData.nGeoms)
    {
        auto pNode = nEdgeGeometry.node.lock();
        if (pNode == nullptr)
            continue;
        WD::WDBMCataUtils::ResetGeometries(*pNode);
        pNode->update(true);
    }
}
void EditCatalogDialog::closeEvent(QCloseEvent* e)
{
    e->ignore();
    this->reject();
    // 当前节点置空
    _pCataNode.reset();
    // 移除场景模型
    _catalogMgr.core().scene().clear();
    // 移除关联的绘制数据
    removeDataAssociatedNode();
}
void EditCatalogDialog::retranslateUi()
{
    Trs("EditCatalogDialog"
        , static_cast<QDialog*>(this)
        , ui.groupBoxModelRef
        , ui.groupBoxComObjTab
        , ui.groupBoxModelData
        , ui.groupBoxDataAttrs
        , ui.pushButtonAttrApply
        , ui.pushButtonCE
    );
}
void EditCatalogDialog::showVertexW(bool flag)
{
    if(flag)
    {
        ui.verticalLayoutVecP->addWidget(&_widgetEditVertex);
        _widgetEditVertex.show();
        auto pCurrNode = _catalogMgr.core().nodeTree().currentNode();
        if (pCurrNode != nullptr)
        {
            if (pCurrNode->isType("SPRO"))
            {
                _widgetEditVertex.initVertexEditTable(pCurrNode);
            }
        }
    }
    else
    {
        _widgetEditVertex.hide();
        ui.verticalLayoutVecP->removeWidget(&_widgetEditVertex);
    }
}
void EditCatalogDialog::slotModelRefWItemChanged(const std::string& itemName)
{
    if (itemName == "Category")
    {
        _pStackedWids->setCurrentIndex(0);
        auto cataNode = _pCataNode.lock();
        if(cataNode == nullptr)
            return;
        auto pCateNode = cataNode->parent();
        if(pCateNode == nullptr)
            return;
        //展示元件表
        _pStackedWids->setCurrentIndex(0);

        //不展示轮廓点
        if(!_widgetEditVertex.isHidden())
            showVertexW(false);
    }
    else if (itemName == "Component")
    {
        _pStackedWids->setCurrentIndex(1);
        auto cataNode = _pCataNode.lock();
        if(cataNode == nullptr)
            return;
        //展示元件信息
        _pStackedWids->setCurrentIndex(2);

        //不展示轮廓点
        if (!_widgetEditVertex.isHidden())
            showVertexW(false);
    }
    else if (itemName == "Blrfarray")
    {
        auto pCataNode = _pCataNode.lock();
        if (pCataNode == nullptr)
            return;
        auto value = pCataNode->getAttribute(itemName);
        auto p = value.data<WD::WDBMNodeRefs>();
        if(p != nullptr)
        {
            if(p->size()<= 0)
            {
                return;
            }
            else if (!p->at(0).empty() && (p->at(0)).refNode() != nullptr)
            {
                onSetNodeChanged((p->at(0)).refNode());
                //集有效则把集所在的groupBox标题改为对应的名称
                auto pBMBase = pCataNode->getBMBase();
                if (pBMBase != nullptr)
                    ui.groupBoxModelData->setTitle(QString::fromUtf8(pBMBase->trA(itemName).c_str()));
                //不展示轮廓点
                if (!_widgetEditVertex.isHidden())
                    showVertexW(false);
            }
        }
    }
    else
    {

        auto pCataNode = _pCataNode.lock();
        if (pCataNode == nullptr)
            return;

        auto value = pCataNode->getAttribute(itemName);
        auto p = value.data<WD::WDBMNodeRef>();
        if (p != nullptr && p->refNode() != nullptr)
        {
            auto pSetNode = p->refNode();
            onSetNodeChanged(pSetNode);
            //集有效则把集所在的groupBox标题改为对应的名称
            auto pBMBase = pCataNode->getBMBase();
            if (pBMBase != nullptr)
                ui.groupBoxModelData->setTitle(QString::fromUtf8(pBMBase->trA(itemName).c_str()));
        }
    }
}
void EditCatalogDialog::slotAttrApply()
{
    //应用后刷新模型数据窗口，且刷新三维视图（修改的数据可能会改变模型）
    _pNodeAttrWidget.apply();
    if(_dynModeDWidget != nullptr)
    {
        auto pCataNode = _pCataNode.lock();
        if(pCataNode == nullptr)
        {
            assert(false && "setNode is null!");
            return;
        }
        auto pModelDWidget = static_cast<SetWidget*>(_dynModeDWidget);
        pModelDWidget->reflushByCataNode(*pCataNode);
        updateSceneCataModel(false);
    }
}
void EditCatalogDialog::slotCEBtn()
{
    auto currentNode = _catalogMgr.core().nodeTree().currentNode();
    this->updateWidget(currentNode);
}
void EditCatalogDialog::slotSubWidgetUpdate()
{
    // 某个子窗口更新了，更新除了当前子窗口的其他所有子窗口
    updateSubWidgets(QObject::sender());
    // 更新模型
    updateSceneCataModel(false);
}

void EditCatalogDialog::updateSubWidgets(QObject* pExcludeWidget)
{
    WD::WDNode::SharedPtr pCataNode = _pCataNode.lock();
    if (pCataNode == nullptr)
        return;

    if (&_widgetModeRef != pExcludeWidget)
    {
        _widgetModeRef.updateWidget(pCataNode);
    }
    if (&_widgetCataPars != pExcludeWidget)
    {
        _widgetCataPars.updateWidget(pCataNode);
    }
    if (&_widgeCataTab != pExcludeWidget)
    {
        auto pCateNode = pCataNode->parent();
        if (pCateNode != nullptr)
            _widgeCataTab.updateWidget(pCateNode, pCataNode);
    }
    if (&_widgetCataInfo != pExcludeWidget)
    {
        _widgetCataInfo.updateWidget(pCataNode);
    }

    //当前元件切换前的集属性，默认展示一个有值的集窗口
    //都没有就折叠
    if (_dynModeDWidget != nullptr && _dynModeDWidget != pExcludeWidget)
    {
        SetWidget* setWidget = dynamic_cast<SetWidget*>(_dynModeDWidget);
        if (setWidget != nullptr)
        {
            //该类型的元件 没有刷新前的引用属性，刷新失败
            if (!setWidget->reflushByCataNode(*pCataNode))
            {
                //模型数据窗口移除前一个集窗口
                ui.verticalLayoutModelData->removeWidget(setWidget);
                setWidget->hide();
                //获取元件的所有引用属性，用找到的第一个引用属性属性模型数据窗口，
                //如果所有引用属性都没有值，则隐藏模型数据窗口和数据属性窗口
                auto nodeRefAttrNameVec = _widgetModeRef.queryAttrVecByNode(*pCataNode);
                if (nodeRefAttrNameVec.size() <= 0)
                {
                    ui.groupBoxModelData->setHidden(true);
                    ui.groupBoxDataAttrs->setHidden(true);
                }
                else
                {
                    for (auto attrName : nodeRefAttrNameVec)
                    {
                        auto attrValue = pCataNode->getAttribute(attrName);
                        auto  p = attrValue.data<WD::WDBMNodeRef>();
                        if (p == nullptr)
                            continue;
                        auto pRefNode = p->refNode();
                        if (pRefNode == nullptr)
                            continue;
                        onSetNodeChanged(pRefNode);
                        //集有效则把集所在的groupBox标题改为对应的名称
                        auto pBMBase = pCataNode->getBMBase();
                        if (pBMBase != nullptr)
                            ui.groupBoxModelData->setTitle(QString::fromUtf8(pBMBase->trA(attrName).c_str()));

                        return;
                    }
                    ui.groupBoxModelData->setHidden(true);
                    ui.groupBoxDataAttrs->setHidden(true);
                }
            }
        }
    }
}

