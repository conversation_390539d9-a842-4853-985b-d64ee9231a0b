#include "UiComEditCatalog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "viewer/WDViewer.h"

UiComEditCatalog::UiComEditCatalog(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject* parent)
    :QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    _editCataDialog = new EditCatalogDialog(mWindow().core(), mWindow().core().getBMCatalog(), mWindow().widget());
}
UiComEditCatalog::~UiComEditCatalog()
{
    if(_editCataDialog != nullptr)
    {
        delete _editCataDialog;
        _editCataDialog = nullptr;
    }
}

void UiComEditCatalog::onNotice(UiNotice* pNotice)
{
    auto nType = pNotice->type();
    switch (nType)
    {
        case UiNoticeType::UNT_Action:
            {
                UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
                if (pActionNotice->action().is("action.catalog.catalog.edit") && _editCataDialog != nullptr)
                {
                    WD::WDNode::SharedPtr   pCurNode = mWindow().core().nodeTree().currentNode();
                    if (pCurNode == nullptr)
                        return;
                    bool isPrepareOk = _editCataDialog->updateWidget(pCurNode);
                    if (isPrepareOk)
                    {
                        if(_editCataDialog->isHidden())
                            _editCataDialog->show();
                        else
                            _editCataDialog->activateWindow();
                    }
                }
            }
            break;
        case UiNoticeType::UNT_Command:
            {
                UiCommandNotice* pCommandNotice = static_cast<UiCommandNotice*>(pNotice);
                if (pCommandNotice->commandName() == "command.catalog.show.catalog.edit")
                {
                    WD::WDNode::SharedPtr   pCurNode = mWindow().core().nodeTree().currentNode();
                    if (pCurNode == nullptr)
                        return;
                    bool isPrepareOk = _editCataDialog->updateWidget(pCurNode);
                    if (isPrepareOk)
                    {
                        if (_editCataDialog->isHidden())
                            _editCataDialog->show();
                        else
                            _editCataDialog->activateWindow();
                    }
                }
            }
            break;
            case UiNoticeType::UNT_AllReady: //所有组件准备完毕通知
            {
                // bool运算配置
                WD::WDConfig& cfg = mWindow().core().cfg();
                auto pCfgItemHolesDrawn = cfg.query("HolesDrawn");
                if (pCfgItemHolesDrawn != nullptr)
                    pCfgItemHolesDrawn->bindFunction({ this, &UiComEditCatalog::onCfgHolesDrawnValueChanged });
                auto pCfgItemArcTolerance = cfg.query("ArcTolerance");
                if (pCfgItemArcTolerance != nullptr)
                    pCfgItemArcTolerance->bindFunction({ this, &UiComEditCatalog::onCfgArcToleranceValueChanged });

                // 如果是元件模块，向视图盒子中设置元件节点包围盒的获取方法
                if (mWindow().moduleType() == "Catalog")
                {
                    auto func = [=](WD::WDNode& node) ->WD::DAabb3
                        {
                            const auto& cmData = this->_editCataDialog->currCMData();
                            // 当前的元件节点
                            if (&node == cmData.node.lock().get())
                            {
                                WD::DAabb3 rAabb = WD::DAabb3::Null();
                                for (const auto& dt : cmData.geoms)
                                {
                                    if (dt.node.expired())
                                        continue;
                                    rAabb.unions(dt.node.lock()->aabb());
                                }
                                for (const auto& dt : cmData.keyPoints)
                                {
                                    if (dt.node.expired())
                                        continue;
                                    rAabb.expandByPoint(dt.keyPoint.position);
                                }
                                for (const auto& dt : cmData.pLines)
                                {
                                    if (dt.node.expired())
                                        continue;
                                    rAabb.expandByPoint(dt.pLine.sPosition);
                                    rAabb.expandByPoint(dt.pLine.ePosition);
                                }
                                for (const auto& dt : cmData.nGeoms)
                                {
                                    if (dt.node.expired())
                                        continue;
                                    rAabb.unions(dt.node.lock()->aabb());
                                }
                                return rAabb;
                            }
                            else
                            {
                                for (const auto& dt : cmData.geoms)
                                {
                                    if (&node == dt.node.lock().get())
                                        return node.aabb();
                                }
                                for (const auto& dt : cmData.keyPoints)
                                {
                                    if (&node == dt.node.lock().get())
                                        return node.aabb();
                                }
                                for (const auto& dt : cmData.pLines)
                                {
                                    if (&node == dt.node.lock().get())
                                        return node.aabb();
                                }
                                for (const auto& dt : cmData.nGeoms)
                                {
                                    if (&node == dt.node.lock().get())
                                        return node.aabb();
                                }
                                return WD::DAabb3::Null();
                            }
                        };
                    mWindow().core().viewer().viewerCube().addNodeAabbGetFunction(func);
                }
            }
            break;
            case UiNoticeType::UNT_ReadyUnload: //所有组件即将卸载通知
            {
                // bool运算配置
                WD::WDConfig& cfg = mWindow().core().cfg();
                auto pCfgItemHolesDrawn = cfg.query("HolesDrawn");
                if (pCfgItemHolesDrawn != nullptr)
                    pCfgItemHolesDrawn->unbindFunction({ this, &UiComEditCatalog::onCfgHolesDrawnValueChanged });
                auto pCfgItemArcTolerance = cfg.query("ArcTolerance");
                if (pCfgItemArcTolerance != nullptr)
                    pCfgItemArcTolerance->unbindFunction({ this, &UiComEditCatalog::onCfgArcToleranceValueChanged });
            }
            break;
        default:
            break;
    }
}


void UiComEditCatalog::onCfgHolesDrawnValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<bool>();
    if (pValue == nullptr)
        return;
    const bool& value = *pValue;

    // 这里只处理元件模块时的更新
    auto moduleType = mWindow().moduleType();
    if (moduleType != "Catalog")
        return;

    WD::WDCore& app = this->mWindow().core();
    // 获取app中的配置值
    WD::WDCore::HolesDrawn& holesDrawn = app.holesDrawn();
    if (value == holesDrawn.first)
        return;
    // 修改app中的配置值
    holesDrawn.first = value;
    // 更新场景
    this->_editCataDialog->updateSceneCataModel(false);
}
void UiComEditCatalog::onCfgArcToleranceValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<float>();
    if (pValue == nullptr)
        return;
    const float& value = *pValue;

    // 这里只处理元件模块时的更新
    auto moduleType = mWindow().moduleType();
    if (moduleType != "Catalog")
        return;

    WD::WDCore& app = this->mWindow().core();

    // 获取app中的配置值
    WD::WDCore::HolesDrawn& holesDrawn = app.holesDrawn();
    // 值未改变，不触发更新
    if (holesDrawn.second == value)
        return;

    // 修改app中的配置值
    holesDrawn.second = value;

    // 未开启孔洞时，不触发更新
    if (!holesDrawn.first)
        return;
    // 更新场景
    this->_editCataDialog->updateSceneCataModel(false);
}