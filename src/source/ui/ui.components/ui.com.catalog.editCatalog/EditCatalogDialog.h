#pragma once

#include <QDialog>
#include "ui_EditCatalogDialog.h"
#include "EditCatalogWidget/ModelRefWidget.h"
#include "EditCatalogWidget/CataTableWidget.h"
#include "EditCatalogWidget/EditVertexWidget.h"
#include "EditCatalogWidget/CataParamsWidget.h"
#include "EditCatalogWidget/CataInfoWidget.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/node/WDNode.h"
#include "EditCatalogWidget/SetWidgetBaseAndAttrsCvrt.h"
#include "../../ui.commonLibrary/ui.commonLib.attributte/NodeAttributeWidget.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include <QCloseEvent>
#include <QStackedWidget>
/**
 * @brief 编辑元件主界面（包含元件对象表、元件信息界面、模型引用、模型数据、数据属性、轮廓点界面）
*/
class EditCatalogDialog : public QDialog
{
    Q_OBJECT
public:
    EditCatalogDialog(WD::WDCore& core, WD::WDBMCatalog& catalogMgr, QWidget* parent = Q_NULLPTR);
    ~EditCatalogDialog();
public:
    /**
    * @brief 更新界面
    * @param node 节点(CATE或STCA类型及以下的节点生效)
    * @return 有效节点，并更新返回true;否则返回false
    */
    bool updateWidget(WD::WDNode::SharedPtr pNode);
    /**
     * @brief 获取当前缓存的元件模型数据
    */
    inline const WD::WDBMCModelBuilder::CMNData& currCMData()const 
    {
        return _sCataCmData;
    }
    /**
     * @brief 更新场景中的元件模型
     * @param 是否重新定位视角
    */
    void updateSceneCataModel(bool reLocationView);
private:
    /**
     * @brief 模型引用中集节点改变，模型数据动态加载匹配的集窗口
     * @param pSetNode 集类型节点。eg：型集类型节点、数据集类型节点...
    */
    void onSetNodeChanged(WD::WDNode::SharedPtr pSetNode);
    /**
     * @brief 模型数据界面当前项改变，刷新数据属性窗口。如果模型数据当前项为spro类型，显示轮廓点窗口，否则不显示轮廓点窗口
     * @param pCurrNode 当前项对应的节点
     * @param pPrevNode 前一个项对应的节点
    */
    void onSetWidgetCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode, WD::WDNode::SharedPtr pPrevNode);
    /**
     * @brief 模型数据界面项点击回调函数，获取模型引用当前项属性文本（英文），给集窗口去判断
    */
    void onSetWidgetItemClicked();
    /**
    * @brief 解除点集型集数据与对应节点的关联
    */
    void removeDataAssociatedNode();
    /**
     * @brief 重写窗口关闭时间
     * @param e 关闭事件
    */
    virtual void closeEvent(QCloseEvent* e) override;
    /**
    * @brief 界面文本翻译
    */
    void retranslateUi();
    /**
     * @brief 显示轮廓点界面
     * @param flag 
    */
    void showVertexW(bool flag = true);
signals:
    /**
     * @brief 发送模型引用窗口当前项的集名称
    */
    void sigModeRefCurrentItemName(const std::string);
public slots:
    /**
     * @brief 模型引用窗口当前项改变槽函数
     * @param item 模型引用窗口当前项对应的属性名称。eg:Ptref，Gmref...
    */
    void slotModelRefWItemChanged(const std::string& item);
    /**
     * @brief 属性界面应用槽函数
    */
    void slotAttrApply();
    /**
     * @brief 模型引用窗口的CE按钮槽函数
    */
    void slotCEBtn();
    /**
     * @brief 子窗口更新，主窗口刷新
    */
    void slotSubWidgetUpdate();
private:
    // 更新所有的子窗口
    // pExcluderWidget需要排除的窗口对象, 排除后，该窗口对象不更新
    void updateSubWidgets(QObject* pExcludeWidget = nullptr);
private:
    //UI界面
    Ui::EditCatalogDialog    ui;
    //Core对象
    WD::WDCore&              _core;
    // 元件库管理
    WD::WDBMCatalog&        _catalogMgr;
    //元件对象表
    CataTableWidget         _widgeCataTab;
    //元件信息窗口
    CataInfoWidget          _widgetCataInfo;
    //模型引用窗口
    ModelRefWidget          _widgetModeRef;
    //元件参数窗口
    CataParamsWidget        _widgetCataPars;
    // 属性窗口
    NodeAttributeWidget     _pNodeAttrWidget;
    //结构元件的轮廓点表窗口
    EditVertexWidget        _widgetEditVertex;
    // 集窗口管理
    SetWidgetManager        _setWidgetMgr;
    // 节点属性转换
    Node2AttrGroup          _attrConvert;
    //动态加载窗口
    QStackedWidget*         _pStackedWids = nullptr;
    //动态模型数据窗口
    QWidget*                _dynModeDWidget = nullptr;
    // 缓存上一次点集型集数据
    WD::WDBMCModelBuilder::CMNData _sCataCmData;
    // 当前元件对象指针
    WD::WDNode::WeakPtr    _pCataNode;
};