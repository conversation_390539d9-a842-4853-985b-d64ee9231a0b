#pragma once

#include <QObject>
#include "../../wizDesignerApp/UiInterface/UiInterface.h"
#include "EditAxisSettingDialog.h"
#include "MoveOffsetInputDialog.h"
#include "RotateOffsetInputDialog.h"
#include "EditValueFastInputDialog.h"
#include "AlignCaptureMonitor.h"
#include "core/viewer/objectAxisEditor/WDAxisEditObjectNode.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiPositionCaptureHelpter.h"

class UiComNodeAxisEditor : public IUiComponent
{
public:
    UiComNodeAxisEditor(IMainWindow& mainWindow, const UiComponentAttributes& attrs);
    ~UiComNodeAxisEditor();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
private:
    void onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
    void onSceneSelectedNodeListChanged(WD::WDScene& scene);

    void onSceneSelectedNodeFilter(const std::vector<WD::WDNode::SharedPtr>& nodes
        , std::vector<WD::WDNode::SharedPtr>& outNodes
        , WD::WDScene& sender);

    bool onObjectEditStart(WD::WDObjectAxisEditor::Object::SharedPtr pObject
        , WD::WDObjectAxisEditor& sender);
    bool onObjectEditFinished(WD::WDObjectAxisEditor::Object::SharedPtr pObject
        , WD::WDObjectAxisEditor& sender)
    {
        WDUnused(sender);
        _bEditting = false;
        return true;
    }

    void onAxisMContextMenu(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxisMove& sender);
    void onAxisRContextMenu(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxisRotate& sender);
    void onAxisSContextMenu(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxisScale& sender);
    void onAxisMRContextMenu(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxisMoveRotate& sender);
    void onAxisSingleMRContextMenu(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditSingleAxisMoveRotate& sender);

    void onAxisMClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisMove& sender);
    void onAxisMDBClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisMove& sender);

    void onAxisRClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisRotate& sender);
    void onAxisRDBClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisRotate& sender);

    void onAxisSClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisScale& sender);
    void onAxisSDBClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisScale& sender);

    void onAxisMRClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisMoveRotate& sender);
    void onAxisMRDBClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditAxisMoveRotate& sender);

    void onAxisSingleMRClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditSingleAxisMoveRotate& sender);
    void onAxisSingleMRDBClicked(int axis
        , const WD::IVec2& screenPos
        , WD::WDEditAxis::MouseButton btn
        , WD::WDEditSingleAxisMoveRotate& sender);

public:
    void setEditType(WD::WDObjectAxisEditor::EditType editType, WD::WDNode::SharedPtr pEditNode = nullptr);
    void setEditObj(WD::WDObjectAxisEditor::Object::SharedPtr, WD::WDNode::Nodes currentNodes = WD::WDNode::Nodes());
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    EditAxisSettingDialog* _pAxisSettingDialog;

    EditValueFastInputDialog* _pFastInputDialog;

    // 移动轴右键菜单
    QMenu*  _pMAxisMenu;
    // 输入值
    QAction* _pActMEnterValue;
    // 位置对齐
    QAction* _pActMAlignWithFeature;
    // 基准点
    QAction* _pActMBasePoint;
    // 移动到
    QAction* _pActMMoveTo;
    // 取消
    QAction* _pActMCancel;

    // 旋转轴右键菜单
    QMenu* _pRAxisMenu;
    // 输入值
    QAction* _pActREnterValue;
    // 朝向对齐
    QAction* _pActRAlignWithDirection;
    // 基准点
    QAction* _pActRBasePoint;
    // 取消
    QAction* _pActRCancel;


    // 移动值输入界面
    MoveOffsetInputDialog*      _pMInputDialog;
    // 旋转值输入界面
    RotateOffsetInputDialog*    _pRInputDialog;

    // 位置对齐捕捉监听者
    WD::AlignCaptureMonitor     _alignCaptureMonitor;
    // 基准点捕捉监听者
    WD::BasePointCaptureMonitor _basePointCaptureMonitor;
    // 移动到捕捉监听者
    WD::MoveToCaptureMonitor    _moveToCaptureMonitor;

    // 记录当前正在编辑的状态
    bool _bEditting = false;

};
