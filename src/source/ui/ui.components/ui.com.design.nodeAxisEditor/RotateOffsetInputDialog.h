#pragma once

#include <QtWidgets/QDialog>
#include "ui_RotateOffsetInputDialog.h"
#include "core/viewer/objectAxisEditor/WDObjectAxisEditor.h"
#include "core/WDCore.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

class RotateOffsetInputDialog : public QDialog
{
    Q_OBJECT
public:
    RotateOffsetInputDialog(WD::WDCore& app, WD::WDObjectAxisEditor* pEditor, QWidget *parent = nullptr);
    ~RotateOffsetInputDialog();
public:
    void setRotateParam(int editType, int editAxis, const WD::DVec3& axis, const WD::DVec3& center)
    {
        _editType = editType;
        _editAxis = editAxis;
        _axis = axis;
        _center = center;
    }
protected:
    virtual	void showEvent(QShowEvent* evt) override; 
    virtual	void hideEvent(QHideEvent* evt) override;
private slots:
    void slotPushButtonPreviewClicked();
    void slotPushButtonOkClicked();
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::RotateOffsetInputDialog ui;
    WD::WDCore& _core;
    WD::WDObjectAxisEditor* _pEditor;
    int _editType;
    int _editAxis;
    WD::DVec3 _axis;
    WD::DVec3 _center;

    WD::WDUndoCommand* _pPreviewCommand = nullptr;
};
