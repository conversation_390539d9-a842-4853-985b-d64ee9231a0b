#include "UiComNodeAxisEditor.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBDBase.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "core/message/WDMessage.h"
#include "core/viewer/objectAxisEditor/WDAxisEditObjectNode.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include <QMenu>

#include "core/common/WDDeviation.h"

// 1. 设备子节点过滤，如果是设备子节点，则默认选中为设备
bool EquiFilter(WD::WDNode& node, WD::WDNode::Nodes& outNodes)
{
    WD::WDNode::SharedPtr pEqui = WD::WDNode::ToShared(&node);
    while (pEqui != nullptr)
    {
        if (pEqui->isType("EQUI"))
            break;
        pEqui = pEqui->parent();
    }

    if (pEqui == nullptr)
        return false;

    outNodes.push_back(pEqui);
    return true;
}
// 2. 管道的直管过滤
//  果当前选中的是直管，需要把直管所在直线的所有管件设置为选择状态
//  直管的前一个管件做为树上的当前节点, 如果直管没有前一个管件，则默认选中直管所属分支
bool PipeTUBIFilter(WD::WDNode& node, WD::WDNode::Nodes& outNodes)
{
    // 如果是直管，默认选到直管的前一个管件, 如果是第一个直管，则默认选到分支
    if (!node.isType("TUBI"))
        return false;
    auto pParent = node.parent();
    if (pParent == nullptr || !pParent->isType("BRAN"))
        return false;
    // 获取前一个管件
    int tubiIndex = -1;
    const auto& children = pParent->children();
    for (size_t i = 0; i < children.size(); ++i)
    {
        if (children[i] == nullptr)
            continue;
        if (children[i].get() == &node)
        {
            tubiIndex = static_cast<int>(i);
            break;
        }
    }
    // 索引为-1，出现异常情况，直接返回false
    if (tubiIndex == -1)
        return false;

    WD::WDNode::SharedPtr pTreeCurrent = nullptr;
    // 向前查找一个管件, 作为树上当前选中的管件
    for (int i = tubiIndex - 1; i >= 0; --i)
    {
        auto pNode = children[i];
        if (pNode == nullptr)
            continue;
        // 跳过直管和ATTA点
        if (pNode->isAnyOfType("TUBI", "ATTA"))
            continue;
        pTreeCurrent = pNode;
        break;
    }
    // 如果没查到，则向后查找一个管件，作为树上当前选中的管件
    if (pTreeCurrent == nullptr) 
    {
        for (int i = tubiIndex + 1; i < children.size(); ++i) 
        {
            auto pNode = children[i];
            if (pNode == nullptr)
                continue;
            // 跳过直管和ATTA点
            if (pNode->isAnyOfType("TUBI", "ATTA"))
                continue;
            pTreeCurrent = pNode;
            break;
        }
    }
    // 还是没查到, 直接用直管所属的分支
    if (pTreeCurrent == nullptr) 
        pTreeCurrent = pParent;

    // 先加入树上选中的当前节点
    outNodes.push_back(pTreeCurrent);
    // 加入当前直管
    outNodes.push_back(WD::WDNode::ToShared(&node));

    auto funcTubiLineAdd = [](WD::WDNode::SharedPtr pNode
            , const WD::DVec3& tubiDir
            , WD::WDNode::SharedPtr pExcludeNode
            , std::vector<WD::WDNode::SharedPtr>& outNodes)->bool
        {
            // 直管，直接加入
            if (pNode->isType("TUBI"))
            {
                outNodes.push_back(pNode);
                return true;
            }
            // 否则可能是管件，判断出入口点
            auto pPtA = pNode->keyPoint(pNode->getAttribute("Arrive").toInt());
            auto pPtL = pNode->keyPoint(pNode->getAttribute("Leave").toInt());
            // 出入口点无效，直接跳出
            if (pPtA == nullptr || pPtL == nullptr)
                return false;
            // 先加入当前节点
            if (pNode != pExcludeNode)
                outNodes.push_back(pNode);
            // 校验出入口点共线, 只要有一个不共线，直接跳出循环
            auto aDir = pPtA->transformedDirection(pNode->localTransform());
            auto lDir = pPtL->transformedDirection(pNode->localTransform());
            if (!WD::DVec3::OnTheSameLine(aDir.normalized(), tubiDir, 0.00001)
                || !WD::DVec3::OnTheSameLine(lDir.normalized(), tubiDir, 0.00001))
                return false;
            return true;
        };

    auto tubiSPos = node.getAttribute("Hposition").toDVec3();
    auto tubiEPos = node.getAttribute("Tposition").toDVec3();
    if (WD::DVec3::DistanceSq(tubiSPos, tubiEPos) > WD::NumLimits<float>::Epsilon)
    {
        auto tubiDir = (tubiEPos - tubiSPos).normalized();
        // 根据索引向前查找，如果出入口流向与直管在同一直线，则加入到选中列表, 否则，跳出循环
        for (int i = tubiIndex - 1; i >= 0; --i)
        {
            auto pNode = children[i];
            if (pNode == nullptr)
                continue;
            // 校验并添加， 如果返回false,表明不再继续添加了
            if (!funcTubiLineAdd(pNode, tubiDir, pTreeCurrent, outNodes))
                break;
        }
        // 根据索引向后查找
        for (int i = tubiIndex + 1; i < children.size(); ++i)
        {
            auto pNode = children[i];
            if (pNode == nullptr)
                continue;
            // 校验并添加， 如果返回false,表明不再继续添加了
            if (!funcTubiLineAdd(pNode, tubiDir, pTreeCurrent, outNodes))
                break;
        }
    }
    return true;
}
// 2. 管道的管件相关过滤
//      1).如果选择的管件是可能构成套件的管件，需要进行套件选择，根据以下节点类型以及规则来判断是否套件
//      所有满足套件的节点均设置为选中状态，当前点击的管件做为树上的当前节点
//      需要判断管件两端是否有法兰和垫片连接的常见管件类型如下（不限于这些）
//          - VALV-普通阀门    法兰+垫片+VALV+垫片+法兰
//          - INST-仪表阀门    法兰+垫片+INST+垫片+法兰
//          - PCOM-特殊件      法兰+垫片+PCOM+垫片+法兰
//          - FILT-过滤器      法兰+垫片+FILT+垫片+法兰
//          - FLAN-法兰        法兰+垫片+法兰
//          - GASK-垫片        法兰+垫片+法兰
//          - FBLI-盲法兰      法兰+垫片+盲法兰
//      2). 如果选择的管件与已经设置了选中状态中的某个管件之间只是连了一条直管，则将它们之间的直管也设置为选中状态
bool PipeComsFilter(WD::WDNode& node, WD::WDNode::Nodes& outNodes)
{
    if (!WD::WDBMDPipeUtils::IsPipeComponent(node))
        return false;

    auto pParent = node.parent();
    if (pParent == nullptr || !pParent->isType("BRAN"))
        return false;

    WD::WDNode::SharedPtr pComNode = WD::WDNode::ToShared(&node);
    // 确定当前管件在子列表中的索引
    int idx = -1;
    const auto& children = pParent->children();
    for (size_t i = 0; i < children.size(); ++i)
    {
        if (children[i] == nullptr)
            continue;
        if (children[i] == pComNode)
        {
            idx = static_cast<int>(i);
            break;
        }
    }
    if (idx == -1)
        return false;
    // 先加入当前管件
    outNodes.push_back(pComNode);
    // 校验管件是否改变流向(校验出入口点的方向是否共线)
    auto funcSameLineCheck = [](const WD::WDNode& node)
        {
            auto pPtA = node.keyPoint(node.getAttribute("Arrive").toInt());
            auto pPtL = node.keyPoint(node.getAttribute("Leave").toInt());
            if (pPtA == nullptr || pPtL == nullptr)
                return false;
            return WD::DVec3::OnTheSameLine(pPtA->direction, pPtL->direction, 0.0001);
        };
    // prev: 流向方向上的前一个管件
    auto funcConnCheck = [](const WD::WDNode& prev, const WD::WDNode& next)
        {
            // 拿到前一个的出口点和后一个的入口点
            auto pPrevPtL = prev.keyPoint(prev.getAttribute("Leave").toInt());
            auto pNextPtA = next.keyPoint(next.getAttribute("Arrive").toInt());
            if (pPrevPtL == nullptr || pNextPtA == nullptr)
                return false;
            auto prevPtL = pPrevPtL->transformed(prev.localTransform());
            auto nextPtA = pNextPtA->transformed(next.localTransform());
            // 如果两个关键点的位置离的很近，则认为校验通过
            auto rDisSq = WD::DVec3::DistanceSq(prevPtL.position, nextPtA.position);
            return rDisSq <= 1.0;
        };

    // 先校验当前管件是否改变流向，如果改变了，则直接返回
    if (!funcSameLineCheck(*pComNode))
        return true;

    // 根据索引向前查找
    {
        auto pCurr = pComNode;
        for (int i = idx - 1; i >= 0; --i)
        {
            auto pPrev = children[i];
            if (pPrev == nullptr)
                continue;
            // 如果是直管，则跳过
            if (pPrev->isType("TUBI"))
                break;
            // 校验共线
            if (!funcSameLineCheck(*pPrev))
                break;
            // 校验连接
            if (!funcConnCheck(*pPrev, *pCurr))
                break;
            outNodes.push_back(pPrev);
            pCurr = pPrev;
        }
    }
    // 根据索引向后查找
    {
        auto pCurr = pComNode;
        for (int i = idx + 1; i < children.size(); ++i)
        {
            auto pNext = children[i];
            if (pNext == nullptr)
                continue;
            // 如果是直管，则跳过
            if (pNext->isType("TUBI"))
                break;
            // 校验共线
            if (!funcSameLineCheck(*pNext))
                break;
            // 校验连接
            if (!funcConnCheck(*pCurr, *pNext))
                break;
            outNodes.push_back(pNext);
            pCurr = pNext;
        }
    }

    return true;
}
// 判断过滤出的管件(filterRetNodes)与场景中已选择的管件(selectedNodes)中，是否是相邻管件(两个中间只有一根直管)
// 如果是，则将这根直管也设置选择标志
void PipeTUBIBetweenComsFilter(const WD::WDNode::Nodes& selectedNodes
    , const WD::WDNode::Nodes& filterRetNodes
    , WD::WDNode::Nodes& outTubis)
{
    WD::WDNode::Nodes tNodes;
    for (auto pSelectedNode : selectedNodes) 
    {
        if (pSelectedNode == nullptr)
            continue;
        if (!WD::WDBMDPipeUtils::IsPipeComponent(*pSelectedNode))
            continue;
        auto pBRANNode = pSelectedNode->parent();
        if (pBRANNode == nullptr || !pBRANNode->isType("BRAN"))
            continue;
        for (auto pFilterRetNode : filterRetNodes) 
        {
            if (pFilterRetNode == nullptr)
                continue;
            // 不属于同一个分支, 直接跳过
            if (pFilterRetNode->parent() != pBRANNode)
                continue;
            // 不是管件，跳过
            if (!WD::WDBMDPipeUtils::IsPipeComponent(*pSelectedNode))
                continue;
            // 检测两个管件之间是否只存在一个直管
            bool bStarted = false;
            tNodes.clear();
            for (auto pNode : pBRANNode->children()) 
            {
                if (pNode == nullptr)
                    continue;
                if (bStarted)
                {
                    // 说明已经到第二个节点了，跳出
                    if (pNode == pSelectedNode || pNode == pFilterRetNode)
                        break;
                    // 加入
                    tNodes.push_back(pNode);
                    // 如果结果大于1个，证明两个管件之间肯定不止是一个直管，不用再找了，不满足条件，直接跳出
                    if (tNodes.size() > 1)
                        break;
                }
                else
                {
                    // 找到第一个节点，开始处理
                    if (pNode == pSelectedNode || pNode == pFilterRetNode)
                        bStarted = true;
                }
            }
            // 只有一个结果且是直管
            if (tNodes.size() == 1
                && tNodes.front() != nullptr
                && tNodes.front()->isType("TUBI")) 
            {
                outTubis.push_back(tNodes.front());
            }
        }
    }
}



bool CheckCurrentAxisIsCheckIn(WD::WDCore& core)
{
    auto pObj = core.viewer().objectAxisEditor().object();
    if (pObj != nullptr)
    {
        auto pNodeObj = dynamic_cast<WD::WDAxisEditObjectNode*>(pObj.get());
        if (pNodeObj != nullptr)
        {
            auto& nodes = pNodeObj->nodes();
            WD::WDNode::Nodes nodeSPtrs;
            nodeSPtrs.reserve(nodes.size());
            for (auto& each : nodes)
            {
                auto pNode = each.lock();
                if (pNode != nullptr)
                    nodeSPtrs.emplace_back(pNode);
            }
            if (!nodeSPtrs.empty())
                return WD::WDBMBase::NodeMRCheckUpdate(nodeSPtrs);
        }
    }
    return false;
}


struct PipeComsConstraintData
{
    WD::WDSingleAxisMRConstraint constraint;
    double distance = 0.0;
    bool bDataValid = false;
};
static PipeComsConstraintData GetPipeComsConstraint(const WD::WDNode::Nodes& nodes)
{
    constexpr double minTubiLength = 1.0;

    PipeComsConstraintData retData;
    if (nodes.empty())
        return retData;
    auto pFirstNode = nodes[0];
    if (pFirstNode == nullptr)
        return retData;

    WD::WDNode::SharedPtr pBranchNode = pFirstNode->parent();
    if (pBranchNode == nullptr || !pBranchNode->isType("BRAN"))
        return retData;

    // 如果分支中有虚线存在,不允许使用单轴移动旋转编辑
    auto pBranchData = pBranchNode->getBDBase();
    if (pBranchData != nullptr && pBranchData->graphableSupporter() != nullptr)
    {
        auto gLines = pBranchData->graphableSupporter()->gLines();
        if (gLines != nullptr)
        {
            for (auto& each : *gLines)
            {
                auto& points = each.points;
                if (!points.empty())
                    return retData;
            }
        }
    }

    // 判断所有管件是否在一段直管上, 即管件之前没有除直管段的其他类型管件
    if (nodes.size() > 1)
    {
        int index = 1;

        std::set<WD::WDNode::SharedPtr> nodesSet;
        for (const auto& pNode : nodes)
        {
            if (pNode == nullptr || pNode->isAnyOfType("TUBI"))
                continue;
            if (( pNode->parent() != pBranchNode ) || pNode->isAnyOfType("ELBO", "BEND", "CROS", "REDU"))
                return retData;
            if (pNode->isType("TEE"))
            {
                auto arrive = pNode->getAttribute("Arrive").toInt();
                auto leave = pNode->getAttribute("Leave").toInt();
                // 目前认为如果三通的PA和PL是p1和p2点并且没有连接其他分支时可以双轴编辑
                if (( arrive != 1 || leave != 2 ) && ( arrive != 2 || leave != 1 ))
                    return retData;
                auto connectNode = pNode->getAttribute("Cref").toNodeRef().refNode();
                if (connectNode != nullptr)
                    return retData;
            }

            if (nodesSet.find(pNode) == nodesSet.end())
                nodesSet.emplace(pNode);
        }
        std::map<int, WD::WDNode::SharedPtr> pipeComsIndexs;
        for (auto& pChild : pBranchNode->children())
        {
            if (pChild == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pChild))
                continue;
            if (nodesSet.find(pChild) != nodesSet.end())
                pipeComsIndexs.emplace(index, pChild);

            ++index;
        }
        // 所有管件必须在同一分支下
        if (nodesSet.size() != pipeComsIndexs.size())
            return retData;

        auto preItr = pipeComsIndexs.begin();
        auto nexItr = pipeComsIndexs.begin();
        if (nexItr == pipeComsIndexs.end())
        {
            assert(false);
            return retData;
        }
        ++nexItr;
        for ( ;nexItr != pipeComsIndexs.end();++preItr, ++nexItr)
        {
            if (( preItr->first + 1 ) != nexItr->first)
                return retData;
        }
        WD::WDNode::SharedPtr pFirstValidNode;
        for (auto begin = pipeComsIndexs.begin(); begin != pipeComsIndexs.end(); ++begin)
        {
            if (begin->second != nullptr)
            {
                pFirstValidNode = begin->second;
                break;
            }
        }

        WD::WDNode::SharedPtr pLastValidNode;
        for (auto begin = pipeComsIndexs.rbegin(); begin != pipeComsIndexs.rend(); ++begin)
        {
            if (begin->second != nullptr)
            {
                pLastValidNode = begin->second;
                break;
            }
        }
        if (pFirstValidNode == nullptr || pLastValidNode == nullptr)
            return retData;

        auto pFirstArrivePt = pFirstValidNode->keyPoint(pFirstValidNode->getAttribute("Arrive").toInt());
        if (pFirstArrivePt == nullptr)
            return retData;

        auto pLastLeavePt = pLastValidNode->keyPoint(pLastValidNode->getAttribute("Leave").toInt());
        if (pLastLeavePt == nullptr)
            return retData;

        auto start = pFirstArrivePt->transformedPosition(pFirstValidNode->globalTransform());
        auto end = pFirstArrivePt->transformedPosition(pLastValidNode->globalTransform());
        retData.distance = WD::DVec3::Distance(start, end);
    }
    else
    {
        if (pFirstNode->isAnyOfType("ELBO", "BEND", "CROS", "REDU"))
            return retData;
        if (pFirstNode->isType("TEE"))
        {
            auto arrive = pFirstNode->getAttribute("Arrive").toInt();
            auto leave = pFirstNode->getAttribute("Leave").toInt();
            // 目前认为如果三通的PA和PL是p1和p2点并且没有连接其他分支时可以双轴编辑
            if (( arrive != 1 || leave != 2 ) && ( arrive != 2 || leave != 1 ))
                return retData;
            auto connectNode = pFirstNode->getAttribute("Cref").toNodeRef().refNode();
            if (connectNode != nullptr)
                return retData;
        }
    }

    struct TempCons
    {
        WD::DVec3 posS;
        WD::DVec3 posE;
        double bore = 0.0;
        WD::WDNode::SharedPtr pPrevNode;
        WD::DVec3 dir() const
        {
            if (posE != posS)
                return WD::DVec3::Normalize(posE - posS);
            return WD::DVec3::Zero();
        }
    };
    std::vector<TempCons> temps;
    WD::WDNode::SharedPtr pPrevNode = nullptr;
    // 子节点是否在当前节点后面
    bool bAfterCurrNode = false;
    for (int i = 0; i < pBranchNode->childCount(); ++i)
    {
        auto pChild = pBranchNode->childAt(i);
        if (pChild == nullptr)
            continue;

        if (pChild->isType("TUBI"))
        {
            if (bAfterCurrNode)
            {
                bAfterCurrNode = false;
                assert(!temps.empty());
                if (!temps.empty())
                {
                    temps.back().posE = pChild->getAttribute("Tposition WRT World").toDVec3();
                    temps.back().bore = std::fmax(temps.back().bore, pChild->getAttribute("Abore").toDouble());
                        
                }
            }
            else
            {
                WD::DVec3 posS = pChild->getAttribute("Hposition WRT World").toDVec3();
                WD::DVec3 posE = pChild->getAttribute("Tposition WRT World").toDVec3();
                if (WD::DVec3::Distance(posS, posE) > minTubiLength)
                {
                    temps.emplace_back(TempCons{ posS, posE,  pChild->getAttribute("Lbore").toDouble(), pPrevNode});
                    if (temps.back().pPrevNode == nullptr)
                        temps.back().pPrevNode = pBranchNode;
                }
            }
        }
        else if (std::find(nodes.begin(), nodes.end(), pChild) != nodes.end())
        {
            if (bAfterCurrNode)
            {
                assert(!temps.empty());
                if (!temps.empty())
                {
                    auto pCurrLeavePoint = pChild->keyPoint(pChild->getAttribute("Leave").toInt());
                    if (pCurrLeavePoint != nullptr)
                        temps.back().posE = pCurrLeavePoint->transformedPosition(pChild->globalTransform());
                }
            }
            else
            {
                bAfterCurrNode = true;
                if (pPrevNode == nullptr)
                {
                    auto posS = pBranchNode->getAttribute("Hposition WRT World").toDVec3();
                    auto posE = posS;
                    auto pointL = pChild->keyPoint(pChild->getAttribute("Leave").toInt());
                    if (pointL != nullptr)
                        posE = pointL->transformedPosition(pChild->globalTransform());
                    temps.emplace_back(TempCons{ posS, posE, 0.0, pBranchNode});
                }
                else if (pPrevNode->isType("TUBI"))
                {
                    assert(!temps.empty());
                    if (!temps.empty())
                    {
                        auto pCurrLeavePoint = pChild->keyPoint(pChild->getAttribute("Leave").toInt());
                        if (pCurrLeavePoint != nullptr)
                            temps.back().posE = pCurrLeavePoint->transformedPosition(pChild->globalTransform());
                    }
                }
                else
                {
                    auto pPrevLeavePoint = pPrevNode->keyPoint(pPrevNode->getAttribute("Leave").toInt());
                    auto pCurrLeavePoint = pChild->keyPoint(pChild->getAttribute("Leave").toInt());
                    if (pPrevLeavePoint != nullptr)
                    {
                        auto posS = pPrevLeavePoint->transformedPosition(pPrevNode->globalTransform());
                        auto posE = posS;
                        if (pCurrLeavePoint != nullptr)
                            posE = pCurrLeavePoint->transformedPosition(pChild->globalTransform());
                        temps.emplace_back(TempCons{ posS, posE, 0.0, pPrevNode});
                    }
                }
            }
        }
        else if (!pChild->isAnyOfType("GASK", "ATTA"))
        {
            if (bAfterCurrNode)
            {
                bAfterCurrNode = false;
                assert(!temps.empty());
                if (!temps.empty())
                {
                    auto pArrivePoint = pChild->keyPoint(pChild->getAttribute("Arrive").toInt());
                    if (pArrivePoint != nullptr)
                        temps.back().posE = pArrivePoint->transformedPosition(pChild->globalTransform());
                }
            }
        }
        pPrevNode = pChild;
    }

    if (bAfterCurrNode)
    {
        bAfterCurrNode = false;
        assert(!temps.empty());
        if (!temps.empty())
            temps.back().posE = pBranchNode->getAttribute("Tposition WRT World").toDVec3();
    }

    if (temps.empty())
        return retData;
    WD::WDSingleAxisMRConstraint ret;
    if (temps.size() == 1)
    {
        ret.constraintLines.emplace_back(WD::WDSingleAxisMRConstraint::ConstraintLine());
        ret.constraintLines.back().posStart         = temps[0].posS;
        ret.constraintLines.back().posEnd           = temps[0].posE;
        ret.constraintLines.back().maxErrorRange    = temps[0].bore;
        ret.constraintLines.back().prevNode         = temps[0].pPrevNode;
    }
    else
    {
        TempCons prev = temps[0];
        double error = 1.0;
        for (int i = 1; i < temps.size(); ++i)
        {
            auto& curr = temps[i];
            if ((curr.bore != prev.bore) || !WD::DVec3::InTheSameDirection(curr.dir(), prev.dir(), 0.000001))
            {
                ret.constraintLines.emplace_back(WD::WDSingleAxisMRConstraint::ConstraintLine());
                ret.constraintLines.back().posStart         = prev.posS;
                ret.constraintLines.back().posEnd           = prev.posE;
                ret.constraintLines.back().maxErrorRange    = prev.bore;
                ret.constraintLines.back().prevNode         = prev.pPrevNode;

                prev = curr;
                continue;
            }

            if (WD::DVec3::Distance(curr.posS, prev.posE) <= error)
            {
                prev.posE = curr.posE;
            }
            else if (WD::DVec3::Distance(curr.posE, prev.posS) <= error)
            {
                prev.posS = curr.posS;
                prev.pPrevNode = curr.pPrevNode;
            }
        }
        ret.constraintLines.emplace_back(WD::WDSingleAxisMRConstraint::ConstraintLine());
        ret.constraintLines.back().posStart         = prev.posS;
        ret.constraintLines.back().posEnd           = prev.posE;
        ret.constraintLines.back().maxErrorRange    = prev.bore;
        ret.constraintLines.back().prevNode         = prev.pPrevNode;
    }
    retData.constraint = ret;
    retData.bDataValid = true;
    return retData;
}

UiComNodeAxisEditor::UiComNodeAxisEditor(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    : IUiComponent(mainWindow, attrs)
    , _alignCaptureMonitor(mainWindow.core())
{
    _pAxisSettingDialog = new EditAxisSettingDialog(mainWindow.core(), mainWindow.widget());

    WD::WDObjectAxisEditor* pAxisEditor = nullptr;
    auto& viewer = mainWindow.core().viewer();
    pAxisEditor = &(viewer.objectAxisEditor());

    _pMInputDialog = new MoveOffsetInputDialog(mainWindow.core(), pAxisEditor, mainWindow.widget());
    _pRInputDialog = new RotateOffsetInputDialog(mainWindow.core(), pAxisEditor, mainWindow.widget());

    _pFastInputDialog = new EditValueFastInputDialog(mWindow().widget());

    // 移动轴右键菜单
    {
        _pMAxisMenu = new QMenu();
        // 输入值
        _pActMEnterValue = new QAction("Enter Value");
        QObject::connect(_pActMEnterValue, &QAction::triggered, [=]()
            {
                _pMInputDialog->exec();
            });
        _pActMAlignWithFeature = new QAction("Align With Feature");
        QObject::connect(_pActMAlignWithFeature, &QAction::triggered, [=]()
            {
                if (pAxisEditor == nullptr)
                    return;

                _alignCaptureMonitor.aType      = WD::AlignCaptureMonitor::AlignType::AT_Position;
                _alignCaptureMonitor.object     = pAxisEditor->object();

                auto eType = pAxisEditor->editType();
                switch (eType)
                {
                case WD::WDObjectAxisEditor::ET_Move:
                    {
                        //const auto& axisM = pAxisEditor->getAxisM();
                    }
                    break;
                case WD::WDObjectAxisEditor::ET_MoveRotate:
                    {
                        auto& axisMR = pAxisEditor->getAxisMR();
                        axisMR.beginPreviewAlign(axisMR.hoveredAxis(), axisMR.axisArcPart());
                    }
                    break;
                case WD::WDObjectAxisEditor::ET_SingleMoveRotate:
                    {
                        auto& axisSingleMR = pAxisEditor->getAxisSingleMR();
                        axisSingleMR.beginPreviewAlign(axisSingleMR.hoveredAxis());
                    }
                    break;
                default:
                    break;
                }
                // 开始捕捉
                WD::WDCapturePositioningParam param;
                param.bDisplayCaptureOptionsUi  =   true;
                param.bDisplayCaptureTypeUi     =   true;
                param.bEnabledCaptureTypeUi     =   true;
                param.bEnabledCaptureOptionsUi  =   true;
                param.pMonitor                  =   &_alignCaptureMonitor;

                mWindow().core().viewer().capturePositioning().activate(param);
            });
        // 基准点
        _pActMBasePoint = new QAction("Base Point");
        QObject::connect(_pActMBasePoint, &QAction::triggered, [=]()
        {
            // 开始捕捉
            WD::WDCapturePositioningParam param;
            param.bDisplayCaptureOptionsUi  =   true;
            param.bDisplayCaptureTypeUi     =   true;
            param.bEnabledCaptureTypeUi     =   true;
            param.bEnabledCaptureOptionsUi  =   true;
            param.pMonitor                  =   &_basePointCaptureMonitor;
            mWindow().core().viewer().capturePositioning().activate(param);
        });
        // 移动到
        _pActMMoveTo = new QAction("Move To");
        QObject::connect(_pActMMoveTo, &QAction::triggered, [=]()
        {
            // 开始捕捉
            WD::WDCapturePositioningParam param;
            param.bDisplayCaptureOptionsUi  =   true;
            param.bDisplayCaptureTypeUi     =   true;
            param.bEnabledCaptureTypeUi     =   true;
            param.bEnabledCaptureOptionsUi  =   true;
            param.pMonitor                  =   &_moveToCaptureMonitor;
            mWindow().core().viewer().capturePositioning().activate(param);
        });
        // 取消
        _pActMCancel = new QAction("Cancel");
        QObject::connect(_pActMCancel, &QAction::triggered, [=]()
            {
                _pMAxisMenu->close();
            });
    }

    // 旋转轴右键菜单
    {
        _pRAxisMenu = new QMenu();
        // 输入值
        _pActREnterValue = new QAction("Enter Value");
        QObject::connect(_pActREnterValue, &QAction::triggered, [=]()
            {
                _pRInputDialog->exec();
            });
        _pActRAlignWithDirection = new QAction("Align With Direction");
        QObject::connect(_pActRAlignWithDirection, &QAction::triggered, [=]()
            {
                if (pAxisEditor == nullptr)
                    return;

                _alignCaptureMonitor.aType      = WD::AlignCaptureMonitor::AlignType::AT_Direction;
                _alignCaptureMonitor.object     = pAxisEditor->object();

                auto eType = pAxisEditor->editType();
                switch (eType)
                {
                case WD::WDObjectAxisEditor::ET_Rotate:
                    {
                        //const auto& axisR = pAxisEditor->getAxisR();
                    }
                    break;
                case WD::WDObjectAxisEditor::ET_MoveRotate:
                    {
                        auto& axisMR = pAxisEditor->getAxisMR();
                        axisMR.beginPreviewAlign(axisMR.hoveredAxis(), axisMR.axisArcPart());
                    }
                    break;
                case WD::WDObjectAxisEditor::ET_SingleMoveRotate:
                    {
                        auto& axisSingleMR = pAxisEditor->getAxisSingleMR();
                        axisSingleMR.beginPreviewAlign(axisSingleMR.hoveredAxis());
                    }
                    break;
                default:
                    break;
                }
                // 开始捕捉
                WD::WDCapturePositioningParam param;
                param.bDisplayCaptureOptionsUi  =   true;
                param.bDisplayCaptureTypeUi     =   true;
                param.bEnabledCaptureTypeUi     =   true;
                param.bEnabledCaptureOptionsUi  =   true;
                param.pMonitor                  =   &_alignCaptureMonitor;
               
                mWindow().core().viewer().capturePositioning().activate(param);
            });
        // 基准点
        _pActRBasePoint = new QAction("Base Point");
        QObject::connect(_pActRBasePoint, &QAction::triggered, [=]()
        {
            // 开始捕捉
            WD::WDCapturePositioningParam param;
            param.bDisplayCaptureOptionsUi  =   true;
            param.bDisplayCaptureTypeUi     =   true;
            param.bEnabledCaptureTypeUi     =   true;
            param.bEnabledCaptureOptionsUi  =   true;
            param.pMonitor                  =   &_basePointCaptureMonitor;
            mWindow().core().viewer().capturePositioning().activate(param);
        });
        // 取消
        _pActRCancel = new QAction("Cancel");
        QObject::connect(_pActRCancel, &QAction::triggered, [=]()
            {
                _pRAxisMenu->close();
            });
    }


    this->retranslateUi();
}
UiComNodeAxisEditor::~UiComNodeAxisEditor()
{
    if (_pAxisSettingDialog != nullptr)
    {
        delete _pAxisSettingDialog;
        _pAxisSettingDialog = nullptr;
    }

    if (_pFastInputDialog != nullptr)
    {
        delete _pFastInputDialog;
        _pFastInputDialog = nullptr;
    }

    if (_pMInputDialog != nullptr)
    {
        delete _pMInputDialog;
        _pMInputDialog = nullptr;
    }
    if (_pRInputDialog != nullptr)
    {
        delete _pRInputDialog;
        _pRInputDialog = nullptr;
    }

    _pMAxisMenu->clear();
    if (_pMAxisMenu != nullptr)
    {
        delete _pMAxisMenu;
        _pMAxisMenu = nullptr;
    }

    _pRAxisMenu->clear();
    if (_pRAxisMenu != nullptr)
    {
        delete _pRAxisMenu;
        _pRAxisMenu = nullptr;
    }

    std::vector<QAction*> _actions = {
        _pActMEnterValue
        , _pActMAlignWithFeature
        , _pActMBasePoint
        , _pActMMoveTo
        , _pActMCancel

        , _pActREnterValue
        , _pActRAlignWithDirection
        , _pActRBasePoint
        , _pActRCancel};
    for (auto& each : _actions)
    {
        if (each != nullptr)
        {
            delete each;
            each = nullptr;
        }
    }
}

void UiComNodeAxisEditor::onNotice(UiNotice * pNotice)
{
    auto& core = mWindow().core();
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
        {
            // 监听树当前节点改变事件
            // core.nodeTree().noticeCurrentNodeChanged()
            //     += {this, & UiComNodeAxisEditor::onNodeTreeCurrentNodeChanged};

            {
                auto& viewer = mWindow().core().viewer();
                viewer.objectAxisEditor().getAxisM().mContextMenuDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisMContextMenu};
                viewer.objectAxisEditor().getAxisM().mClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisMClicked};
                viewer.objectAxisEditor().getAxisM().mDBClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisMDBClicked};

                viewer.objectAxisEditor().getAxisR().mContextMenuDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisRContextMenu};
                viewer.objectAxisEditor().getAxisR().mClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisRClicked};
                viewer.objectAxisEditor().getAxisR().mDBClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisRDBClicked};

                viewer.objectAxisEditor().getAxisS().mContextMenuDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisSContextMenu};
                viewer.objectAxisEditor().getAxisS().mClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisSClicked};
                viewer.objectAxisEditor().getAxisS().mDBClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisSDBClicked};

                viewer.objectAxisEditor().getAxisMR().mContextMenuDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisMRContextMenu};
                viewer.objectAxisEditor().getAxisMR().mClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisMRClicked};
                viewer.objectAxisEditor().getAxisMR().mDBClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisMRDBClicked};

                viewer.objectAxisEditor().getAxisSingleMR().mContextMenuDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisSingleMRContextMenu};
                viewer.objectAxisEditor().getAxisSingleMR().mClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisSingleMRClicked};
                viewer.objectAxisEditor().getAxisSingleMR().mDBClickedDelegate() +=
                {this, & UiComNodeAxisEditor::onAxisSingleMRDBClicked};

                auto& scene = mWindow().core().scene();
                {
                    scene.noticeSelectedNodeListChanged()
                        += {this, &UiComNodeAxisEditor::onSceneSelectedNodeListChanged};

                    scene.addSelectedNodeFilter({ this, &UiComNodeAxisEditor::onSceneSelectedNodeFilter });
                }

                viewer.objectAxisEditor().objectEditStartDelegate()
                    += {this, & UiComNodeAxisEditor::onObjectEditStart};
                viewer.objectAxisEditor().objectEditFinishedDelegate()
                    += {this, & UiComNodeAxisEditor::onObjectEditFinished};
            }

        }
        break;
    case UiNoticeType::UNT_ReadyUnload:
        {
            //core.nodeTree().noticeCurrentNodeChanged()
            //    -= {this, & UiComNodeAxisEditor::onNodeTreeCurrentNodeChanged};

            {
                auto& viewer = mWindow().core().viewer();
                viewer.objectAxisEditor().objectEditStartDelegate()
                    -= {this, & UiComNodeAxisEditor::onObjectEditStart};
                viewer.objectAxisEditor().objectEditFinishedDelegate()
                    -= {this, & UiComNodeAxisEditor::onObjectEditFinished};

                auto& scene = mWindow().core().scene();
                {
                    scene.noticeSelectedNodeListChanged() -= {this
                        , & UiComNodeAxisEditor::onSceneSelectedNodeListChanged};

                    scene.removeSelectedNodeFilter({this, &UiComNodeAxisEditor ::onSceneSelectedNodeFilter});
                }

                viewer.objectAxisEditor().getAxisM().mContextMenuDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisMContextMenu};
                viewer.objectAxisEditor().getAxisM().mClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisMClicked};
                viewer.objectAxisEditor().getAxisM().mDBClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisMDBClicked};

                viewer.objectAxisEditor().getAxisR().mContextMenuDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisRContextMenu};
                viewer.objectAxisEditor().getAxisR().mClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisRClicked};
                viewer.objectAxisEditor().getAxisR().mDBClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisRDBClicked};

                viewer.objectAxisEditor().getAxisS().mContextMenuDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisSContextMenu};
                viewer.objectAxisEditor().getAxisS().mClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisSClicked};
                viewer.objectAxisEditor().getAxisS().mDBClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisSDBClicked};

                viewer.objectAxisEditor().getAxisMR().mContextMenuDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisMRContextMenu};
                viewer.objectAxisEditor().getAxisMR().mClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisMRClicked};
                viewer.objectAxisEditor().getAxisMR().mDBClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisMRDBClicked};

                viewer.objectAxisEditor().getAxisSingleMR().mContextMenuDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisSingleMRContextMenu};
                viewer.objectAxisEditor().getAxisSingleMR().mClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisSingleMRClicked};
                viewer.objectAxisEditor().getAxisSingleMR().mDBClickedDelegate() -=
                {this, & UiComNodeAxisEditor::onAxisSingleMRDBClicked};
            }
        }
        break;
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            //模型编辑功能，将移动和旋转操作合并
            if (pActionNotice->action().is("action.design.node.editAxis.modelEditor"))
            {
                auto& viewer = core.viewer();
                if (pActionNotice->action().checked())
                {
                    auto pNode = core.nodeTree().currentNode();
                    if (pNode == nullptr)
                    {
                        WD_WARN_T("UiComNodeAxisEditor", "Please check a valid node!");
                        return;
                    }
                    //  权限校验
                    if (!core.getBMDesign().permissionMgr().check(*pNode))
                    {
                        WD_WARN_T("UiComNodeAxisEditor", "You cannot operate the current node!");
                        return;
                    }
                    //// 申领对象
                    //if (!core.getBMDesign().claimMgr().checkIn(*pNode))
                    //    return;
                    this->setEditType(WD::WDObjectAxisEditor::EditType::ET_MoveRotate, pNode);
                    viewer.objectAxisEditor().setEditCoordinateSysType(WD::WDObjectAxisEditor::ECST_Local);
                }
                else
                {
                    this->setEditType(WD::WDObjectAxisEditor::EditType::ET_None);
                }

                viewer.needRepaint();
            }
            //编辑轴步长设置
            else if (pActionNotice->action().is("action.design.node.editAxisStepSetting"))
            {
                if (_pAxisSettingDialog->isHidden())
                    _pAxisSettingDialog->show();
                else
                    _pAxisSettingDialog->activateWindow();
            }
            //场景选择节点还是树上选择节点的切换
            else if (pActionNotice->action().is("action.design.node.editAxisEditCurrent"))
            {
                auto& viewer = core.viewer();

                if (pActionNotice->action().checked()) 
                {
                    // 监听树当前节点改变事件
                    core.nodeTree().noticeCurrentNodeChanged()
                         += {this, & UiComNodeAxisEditor::onNodeTreeCurrentNodeChanged};

                    if (viewer.scene() != nullptr)
                    {
                        viewer.scene()->noticeSelectedNodeListChanged()
                            -= {this, & UiComNodeAxisEditor::onSceneSelectedNodeListChanged};
                    }


                    auto pCurrNode = core.nodeTree().currentNode();
                    if (pCurrNode != nullptr && pCurrNode->flags().hasFlag(WD::WDNode::F_InTheScene))
                    {
                        auto pEditObj = WD::WDAxisEditObjectNode::MakeShared(pCurrNode);
                        this->setEditObj(pEditObj, { pCurrNode });
                    }
                    else
                    {
                        this->setEditObj(nullptr);
                    }

                    mWindow().core().needRepaint();
                }
                else 
                {
                    // 监听树当前节点改变事件
                    core.nodeTree().noticeCurrentNodeChanged()
                        -= {this, & UiComNodeAxisEditor::onNodeTreeCurrentNodeChanged};

                    if (viewer.scene() != nullptr)
                    {
                        viewer.scene()->noticeSelectedNodeListChanged()
                            += {this, & UiComNodeAxisEditor::onSceneSelectedNodeListChanged};

                        auto nodes = viewer.scene()->selectedNodes();
                        if (nodes.empty())
                        {
                            this->setEditObj(nullptr);
                        }
                        else
                        {
                            auto pEditObj = WD::WDAxisEditObjectNode::MakeShared(nodes);
                            this->setEditObj(pEditObj, nodes);
                        }
                    }
                }
            }
        }
        break;
    default:
        break;
    }
}

void UiComNodeAxisEditor::onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused2(pPrevNode, sender);

    if (pCurrNode != nullptr && pCurrNode->flags().hasFlag(WD::WDNode::F_InTheScene))
    {
        auto pEditObj = WD::WDAxisEditObjectNode::MakeShared(pCurrNode);
        this->setEditObj(pEditObj, { pCurrNode });
    }
    else
    {
        this->setEditObj(nullptr);
    }

    mWindow().core().needRepaint();
}
void UiComNodeAxisEditor::onSceneSelectedNodeListChanged(WD::WDScene& scene)
{
    // 正在编辑，不会改变编辑对象
    if (_bEditting)
        return;

    auto nodes = scene.selectedNodes();
    if (nodes.empty())
    {
        this->setEditObj(nullptr);
    }
    else
    {
        // 这里做一个过滤，如果nodes中同时包含节点以及其祖先节点，则只保留祖先节点
        std::set<WD::WDNode::SharedPtr> tNodes;
        for (auto pNode : nodes) 
        {
            if (pNode == nullptr)
                continue;
            tNodes.insert(pNode);
        }
        // 遍历每一个节点，查找改节点的祖先是否存在于当前列表中
        std::vector<bool> rmFlags;
        rmFlags.resize(nodes.size(), false);
        size_t rmCnt = 0;
        for (size_t i = 0; i < nodes.size(); ++i)
        {
            auto pNode = nodes[i];
            if (pNode == nullptr)
            {
                rmFlags[i] = true;
                rmCnt++;
                continue;
            }
            // 查找是否存在祖先节点
            auto pParent = pNode->parent();
            while (pParent != nullptr) 
            {
                auto fItr = tNodes.find(pParent);
                if (fItr != tNodes.end())
                {
                    rmFlags[i] = true;
                    rmCnt++;
                    break;
                }
                pParent = pParent->parent();
            }
        }
        // 将具有移除标记的节点从列表中移除, 保留最终结果
        std::vector<WD::WDNode::SharedPtr> rNodes;
        rNodes.reserve(nodes.size() - rmCnt);
        for (size_t i = 0; i < nodes.size(); ++i) 
        {
            if (rmFlags[i])
                continue;
                rNodes.push_back(nodes[i]);
        }
        auto pEditObj = WD::WDAxisEditObjectNode::MakeShared(rNodes);
        this->setEditObj(pEditObj, rNodes);
    }
}

void UiComNodeAxisEditor::onSceneSelectedNodeFilter(const WD::WDNode::Nodes& nodes
    , WD::WDNode::Nodes& outNodes
    , WD::WDScene& sender)
{
    if (nodes.empty())
        return ;

    for (auto pNode : nodes)
    {
        if (pNode == nullptr)
            continue;

        // 1. 设备过滤
        if (EquiFilter(*pNode, outNodes))
            continue;
        // 2. 管道过滤
        {
            WD::WDNode::Nodes tOutNodes;
            if (PipeTUBIFilter(*pNode, tOutNodes)       // 直管过滤
                || PipeComsFilter(*pNode, tOutNodes))   // 管件过滤
            {
                // 这里还需要判断过滤出的管件与场景中已选择的管件中，是否只有一根直管，如果是，则将这根直管也设置选择标志
                WD::WDNode::Nodes outTubis;
                PipeTUBIBetweenComsFilter(sender.selectedNodes(), tOutNodes, outTubis);
                // 加入过滤出的结果
                outNodes.insert(outNodes.end(), tOutNodes.begin(), tOutNodes.end());
                if (!outTubis.empty())
                    outNodes.insert(outNodes.end(), outTubis.begin(), outTubis.end());
                continue;
            }
        }
        // 3. 其他不参与过滤了，直接添加到返回列表
        outNodes.push_back(pNode);
    }
}

bool UiComNodeAxisEditor::onObjectEditStart(WD::WDObjectAxisEditor::Object::SharedPtr pObject
    , WD::WDObjectAxisEditor& sender)
{
    WDUnused(sender);
    // 申领对象
    auto pEditObj = WD::WDAxisEditObjectNode::As(pObject);
    if (pEditObj == nullptr)
        return false;
    WD::WDNode::Nodes pNodes;
    const auto& wNodes = pEditObj->nodes();
    for (const auto& node : wNodes)
    {
        auto pNode = node.lock();
        if (pNode == nullptr)
            continue;
        pNodes.push_back(pNode);
    }

    _bEditting = true;
    if (!WD::WDBMBase::NodeMRCheckUpdate(pNodes))
    {
        _bEditting = false;
        return false;
    }

    return true;
}

void UiComNodeAxisEditor::onAxisMContextMenu(int axis
    , const WD::IVec2 & screenPos
    , WD::WDEditAxisMove& sender)
{
    WDUnused(screenPos);
    WDUnused(sender);

    _pMAxisMenu->clear();
    _pMAxisMenu->addAction(_pActMEnterValue);
    _pMAxisMenu->addAction(_pActMAlignWithFeature);
    _pMAxisMenu->addAction(_pActMCancel);
    if (!CheckCurrentAxisIsCheckIn(mWindow().core()))
        return;

    auto mFlags = MoveOffsetInputDialog::AxisFlags();

    _pMInputDialog->setAxisDirection(sender.axisX().normalized()
        , sender.axisY().normalized()
        , sender.axisZ().normalized());

    switch (axis)
    {
    case WD::WDEditAxisMove::A_X:
        mFlags.addFlag(MoveOffsetInputDialog::AF_X);
        break;
    case WD::WDEditAxisMove::A_Y:
        mFlags.addFlag(MoveOffsetInputDialog::AF_Y);
        break;
    case WD::WDEditAxisMove::A_Z:
        mFlags.addFlag(MoveOffsetInputDialog::AF_Z);
        break;
    case WD::WDEditAxisMove::A_XY:
        mFlags.addFlags(MoveOffsetInputDialog::AF_X, MoveOffsetInputDialog::AF_Y);
        break;
    case WD::WDEditAxisMove::A_YZ:
        mFlags.addFlags(MoveOffsetInputDialog::AF_Y, MoveOffsetInputDialog::AF_Z);
        break;
    case WD::WDEditAxisMove::A_ZX:
        mFlags.addFlags(MoveOffsetInputDialog::AF_X, MoveOffsetInputDialog::AF_Z);
        break;
    default:
        {
            assert(false);
            return;
        }
        break;
    }
    _pMInputDialog->setAxisFlags(mFlags);
    _pMInputDialog->setMoveParam(WD::WDObjectAxisEditor::EditType::ET_Move, axis);
    if (mFlags != MoveOffsetInputDialog::AF_None)
    {
        _pActMAlignWithFeature->setVisible(false);
        _pMAxisMenu->exec(QCursor::pos());
    }
}
void UiComNodeAxisEditor::onAxisRContextMenu(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxisRotate& sender)
{
    WDUnused(screenPos);
    WDUnused(sender);
    _pRAxisMenu->clear();
    _pRAxisMenu->addAction(_pActREnterValue);
    _pRAxisMenu->addAction(_pActRAlignWithDirection);
    _pRAxisMenu->addAction(_pActRCancel);
    if (!CheckCurrentAxisIsCheckIn(mWindow().core()))
        return;

    WD::DVec3 rotAxis = sender.axisX().normalized();

    switch (axis)
    {
    case WD::WDEditAxisRotate::A_X:
        rotAxis = sender.axisX().normalized();
        break;
    case WD::WDEditAxisRotate::A_Y:
        rotAxis = sender.axisY().normalized();
        break;
    case WD::WDEditAxisRotate::A_Z:
        rotAxis = sender.axisZ().normalized();
        break;
    case WD::WDEditAxisRotate::A_BL:
    case WD::WDEditAxisRotate::A_SC:
        {
            assert(false);
            return;
        }
    default:
        break;
    }

    _pRInputDialog->setRotateParam(WD::WDObjectAxisEditor::EditType::ET_Rotate, axis, rotAxis, sender.position());

    _pActRAlignWithDirection->setVisible(false);
    _pRAxisMenu->exec(QCursor::pos());
}
void UiComNodeAxisEditor::onAxisSContextMenu(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxisScale& sender)
{
    WDUnused(axis);
    WDUnused(screenPos);
    WDUnused(sender);
    if (!CheckCurrentAxisIsCheckIn(mWindow().core()))
        return;

}
void UiComNodeAxisEditor::onAxisMRContextMenu(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxisMoveRotate& sender)
{
    WDUnused(screenPos);
    WDUnused(sender);

    _pMAxisMenu->clear();
    _pMAxisMenu->addAction(_pActMEnterValue);
    _pMAxisMenu->addAction(_pActMAlignWithFeature);
    _pMAxisMenu->addAction(_pActMBasePoint);
    _pMAxisMenu->addAction(_pActMMoveTo);
    _pMAxisMenu->addAction(_pActMCancel);

    _pRAxisMenu->clear();
    _pRAxisMenu->addAction(_pActREnterValue);
    _pRAxisMenu->addAction(_pActRAlignWithDirection);
    _pRAxisMenu->addAction(_pActRBasePoint);
    _pRAxisMenu->addAction(_pActRCancel);
    if (!CheckCurrentAxisIsCheckIn(mWindow().core()))
        return;

    auto mFlags = MoveOffsetInputDialog::AxisFlags();

    _pMInputDialog->setAxisDirection(sender.axisX().normalized()
        , sender.axisY().normalized()
        , sender.axisZ().normalized());

    WD::DVec3 rotAxis = sender.axisX().normalized();

    switch (axis)
    {
    case WD::WDEditAxisMoveRotate::A_MX:
        mFlags.addFlag(MoveOffsetInputDialog::AF_X);
        break;
    case WD::WDEditAxisMoveRotate::A_MY:
        mFlags.addFlag(MoveOffsetInputDialog::AF_Y);
        break;
    case WD::WDEditAxisMoveRotate::A_MZ:
        mFlags.addFlag(MoveOffsetInputDialog::AF_Z);
        break;
    case WD::WDEditAxisMoveRotate::A_MXY:
        mFlags.addFlags(MoveOffsetInputDialog::AF_X, MoveOffsetInputDialog::AF_Y);
        break;
    case WD::WDEditAxisMoveRotate::A_MYZ:
        mFlags.addFlags(MoveOffsetInputDialog::AF_Y, MoveOffsetInputDialog::AF_Z);
        break;
    case WD::WDEditAxisMoveRotate::A_MZX:
        mFlags.addFlags(MoveOffsetInputDialog::AF_Z, MoveOffsetInputDialog::AF_X);
        break;
    case WD::WDEditAxisMoveRotate::A_RX:
        rotAxis = sender.axisX().normalized();
        break;
    case WD::WDEditAxisMoveRotate::A_RY:
        rotAxis = sender.axisY().normalized();
        break;
    case WD::WDEditAxisMoveRotate::A_RZ:
        rotAxis = sender.axisZ().normalized();
        break;
    default:
        {
            assert(false);
            return ;
        }
        break;
    }

    if (mFlags != MoveOffsetInputDialog::AF_None)
    {
        _pMInputDialog->setAxisFlags(mFlags);
        _pMInputDialog->setMoveParam(WD::WDObjectAxisEditor::EditType::ET_MoveRotate, axis);
        _pActMAlignWithFeature->setVisible(true);
        _pMAxisMenu->exec(QCursor::pos());
    }
    else
    {
        _pRInputDialog->setRotateParam(WD::WDObjectAxisEditor::EditType::ET_MoveRotate, axis, rotAxis, sender.position());
        _pActRAlignWithDirection->setVisible(true);
        _pRAxisMenu->exec(QCursor::pos());
    }
}
void UiComNodeAxisEditor::onAxisSingleMRContextMenu(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditSingleAxisMoveRotate& sender)
{
    WDUnused(axis);
    WDUnused(screenPos);
    WDUnused(sender);

    _pMAxisMenu->clear();
    _pMAxisMenu->addAction(_pActMEnterValue);
    _pMAxisMenu->addAction(_pActMAlignWithFeature);
    _pMAxisMenu->addAction(_pActMCancel);

    _pRAxisMenu->clear();
    _pRAxisMenu->addAction(_pActREnterValue);
    _pRAxisMenu->addAction(_pActRAlignWithDirection);
    _pRAxisMenu->addAction(_pActRCancel);
    if (!CheckCurrentAxisIsCheckIn(mWindow().core()))
        return;

    auto mFlags = MoveOffsetInputDialog::AxisFlags();

    _pMInputDialog->setAxisDirection(sender.axisX().normalized()
        , sender.axisY().normalized()
        , sender.axisZ().normalized());

    WD::DVec3 rotAxis = sender.axisX().normalized();

    switch (axis)
    {
    case WD::WDEditSingleAxisMoveRotate::A_MAxis:
        mFlags.addFlag(MoveOffsetInputDialog::AF_X);
        break;
    case WD::WDEditSingleAxisMoveRotate::A_RAxis:
        rotAxis = sender.axisX().normalized();
        break;
    default:
        {
            assert(false);
            return;
        }
        break;
    }
    if (mFlags != MoveOffsetInputDialog::AF_None)
    {
        _pMInputDialog->setAxisFlags(mFlags);
        _pMInputDialog->setMoveParam(WD::WDObjectAxisEditor::EditType::ET_SingleMoveRotate, axis);
        _pActMAlignWithFeature->setVisible(true);
        _pMAxisMenu->exec(QCursor::pos());
    }
    else
    {
        _pRInputDialog->setRotateParam(WD::WDObjectAxisEditor::EditType::ET_SingleMoveRotate, axis, rotAxis, sender.position());
        _pActRAlignWithDirection->setVisible(true);
        _pRAxisMenu->exec(QCursor::pos());
    }
}

void UiComNodeAxisEditor::onAxisMClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisMove& sender)
{
    WDUnused(axis);
    WDUnused(screenPos);
    WDUnused(btn);
    WDUnused(sender);
    _pFastInputDialog->accept();
}
void UiComNodeAxisEditor::onAxisMDBClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisMove& sender)
{
    WDUnused(screenPos);
    WDUnused(btn);

    if (!CheckCurrentAxisIsCheckIn(mWindow().core()) || btn != WD::WDEditAxis::MouseButton::MB_Left)
        return;

    switch (axis)
    {
    case  WD::WDEditAxisMove::A_X:
    case  WD::WDEditAxisMove::A_Y:
    case  WD::WDEditAxisMove::A_Z:
    case  WD::WDEditAxisMove::A_XY:
    case  WD::WDEditAxisMove::A_YZ:
    case  WD::WDEditAxisMove::A_ZX:
        break;
    default:
        return;
        break;
    }

    EditValueFastInputDialog::InputParam param;
    _pFastInputDialog->setInputParam(param);
    _pFastInputDialog->setAxis(axis);

    _pFastInputDialog->noticeFinished() = [&sender](int axis, double offset)
        {
            if (WD::Abs(offset) <= std::numeric_limits<float>::epsilon())
                return;
            WD::WDEditAxisMove::Axis tAxis = (WD::WDEditAxisMove::Axis)(axis);
            sender.execMove(tAxis, offset);
        };

    _pFastInputDialog->move(QCursor::pos());
    _pFastInputDialog->show();
}

void UiComNodeAxisEditor::onAxisRClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisRotate& sender)
{
    WDUnused(axis);
    WDUnused(screenPos);
    WDUnused(btn);
    WDUnused(sender);
    _pFastInputDialog->accept();
}
void UiComNodeAxisEditor::onAxisRDBClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisRotate& sender)
{
    WDUnused(screenPos);
    if (!CheckCurrentAxisIsCheckIn(mWindow().core()) || btn != WD::WDEditAxis::MouseButton::MB_Left)
        return;

    switch (axis)
    {
    case  WD::WDEditAxisRotate::A_X:
    case  WD::WDEditAxisRotate::A_Y:
    case  WD::WDEditAxisRotate::A_Z:
        break;
    default:
        return;
        break;
    }

    EditValueFastInputDialog::InputParam param;
    _pFastInputDialog->setInputParam(param);
    _pFastInputDialog->setAxis(axis);

    _pFastInputDialog->noticeFinished() = [&sender](int axis, double offset)
        {
            if (WD::Abs(offset) <= std::numeric_limits<float>::epsilon())
                return;
            WD::WDEditAxisRotate::Axis tAxis = (WD::WDEditAxisRotate::Axis)(axis);
            sender.execRotate(tAxis, offset);
        };

    _pFastInputDialog->move(QCursor::pos());
    _pFastInputDialog->show();
}

void UiComNodeAxisEditor::onAxisSClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisScale& sender)
{
    WDUnused(axis);
    WDUnused(screenPos);
    WDUnused(btn);
    WDUnused(sender);
    _pFastInputDialog->accept();
}
void UiComNodeAxisEditor::onAxisSDBClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisScale& sender)
{
    WDUnused(screenPos);

    if (!CheckCurrentAxisIsCheckIn(mWindow().core()) || btn != WD::WDEditAxis::MouseButton::MB_Left)
        return;

    switch (axis)
    {
    case  WD::WDEditAxisScale::A_XYZ:
        break;
    default:
        return;
        break;
    }

    EditValueFastInputDialog::InputParam param;
    param.minimum = 0.1;
    param.singleStep = 0.1;
    param.defValue = 1.0;

    _pFastInputDialog->setInputParam(param);
    _pFastInputDialog->setAxis(axis);

    _pFastInputDialog->noticeFinished() = [&sender](int axis, double offset)
        {
            if (WD::Abs(offset) <= std::numeric_limits<float>::epsilon())
                return;
            WD::WDEditAxisScale::Axis tAxis = (WD::WDEditAxisScale::Axis)(axis);
            sender.execScale(tAxis, offset);
        };

    _pFastInputDialog->move(QCursor::pos());
    _pFastInputDialog->show();
}

void UiComNodeAxisEditor::onAxisMRClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisMoveRotate& sender)
{
    WDUnused(axis);
    WDUnused(screenPos);
    WDUnused(btn);
    WDUnused(sender);
    _pFastInputDialog->accept();
}
void UiComNodeAxisEditor::onAxisMRDBClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditAxisMoveRotate& sender)
{
    WDUnused(screenPos);
    WDUnused(sender);

    if (!CheckCurrentAxisIsCheckIn(mWindow().core()) || btn != WD::WDEditAxis::MouseButton::MB_Left)
        return;

    switch (axis)
    {
    case WD::WDEditAxisMoveRotate::A_MX:
    case WD::WDEditAxisMoveRotate::A_MY:
    case WD::WDEditAxisMoveRotate::A_MZ:
    case WD::WDEditAxisMoveRotate::A_MXY:
    case WD::WDEditAxisMoveRotate::A_MYZ:
    case WD::WDEditAxisMoveRotate::A_MZX:
    case WD::WDEditAxisMoveRotate::A_RX:
    case WD::WDEditAxisMoveRotate::A_RY:
    case WD::WDEditAxisMoveRotate::A_RZ:
        break;
    default:
        return;
        break;
    }

    EditValueFastInputDialog::InputParam param;
    _pFastInputDialog->setInputParam(param);
    _pFastInputDialog->setAxis(axis);

    _pFastInputDialog->noticeFinished() = [&sender](int axis, double offset)
        {
            if (WD::Abs(offset) <= std::numeric_limits<float>::epsilon())
                return;
            WD::WDEditAxisMoveRotate::Axis tAxis = (WD::WDEditAxisMoveRotate::Axis)(axis);
            sender.exec(tAxis, offset);
        };

    _pFastInputDialog->move(QCursor::pos());
    _pFastInputDialog->show();
}

void UiComNodeAxisEditor::onAxisSingleMRClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditSingleAxisMoveRotate& sender)
{
    WDUnused(axis);
    WDUnused(screenPos);
    WDUnused(btn);
    WDUnused(sender);
    _pFastInputDialog->accept();
}
void UiComNodeAxisEditor::onAxisSingleMRDBClicked(int axis
    , const WD::IVec2& screenPos
    , WD::WDEditAxis::MouseButton btn
    , WD::WDEditSingleAxisMoveRotate& sender)
{
    WDUnused(screenPos);
    WDUnused(sender);
    if (!CheckCurrentAxisIsCheckIn(mWindow().core()) || btn != WD::WDEditAxis::MouseButton::MB_Left)
        return;

    EditValueFastInputDialog::InputParam param;
    _pFastInputDialog->setInputParam(param);
    _pFastInputDialog->setAxis(axis);

    _pFastInputDialog->noticeFinished() = [&sender](int axis, double offset)
    {
        if (WD::Abs(offset) <= std::numeric_limits<float>::epsilon())
            return;
        WD::WDEditSingleAxisMoveRotate::Axis tAxis = (WD::WDEditSingleAxisMoveRotate::Axis)(axis);
        sender.exec(tAxis, offset);
    };
    _pFastInputDialog->move(QCursor::pos());
    _pFastInputDialog->show();
}

void UiComNodeAxisEditor::setEditType(WD::WDObjectAxisEditor::EditType editType, WD::WDNode::SharedPtr pEditNode)
{
    auto& core = mWindow().core();
    auto& viewer = core.viewer();

    if (pEditNode != nullptr)
    {
        switch (editType)
        {
        case WD::WDObjectAxisEditor::ET_None:
        case WD::WDObjectAxisEditor::ET_Move:
        case WD::WDObjectAxisEditor::ET_Rotate:
        case WD::WDObjectAxisEditor::ET_Scale:
            {
                viewer.objectAxisEditor().setEditType(editType);
            }
            break;
        case WD::WDObjectAxisEditor::ET_MoveRotate:
        case WD::WDObjectAxisEditor::ET_SingleMoveRotate:
            {
                // 暂时写死部分类型的管件使用特殊 单移动旋转轴
                viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::EditType::ET_MoveRotate);
                // 如果节点类型为阀门套件其中一种且当前节点所在分支的约束有效
                if (viewer.objectAxisEditor().getAxisSingleMR().constraint().valid())
                    viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::EditType::ET_SingleMoveRotate);
            }
            break;
        default:
            break;
        }
    }
    else
    {
        viewer.objectAxisEditor().setEditType(editType);
    }
}

void UiComNodeAxisEditor::setEditObj(WD::WDObjectAxisEditor::Object::SharedPtr pObj, WD::WDNode::Nodes currentNodes)
{
    auto& viewer = mWindow().core().viewer();
    auto& permMgr = WD::Core().getBMDesign().permissionMgr();
    if (!currentNodes.empty())
    {
        auto pCurrentNode = currentNodes[0];
        if (pCurrentNode != nullptr)
        {
            if (!permMgr.check(*pCurrentNode))
                return;
            {
                auto& pAxisSMR = viewer.objectAxisEditor().getAxisSingleMR();
                pAxisSMR.setConstraintEnabled(true);
                auto constraintData = GetPipeComsConstraint(currentNodes);
                if (constraintData.bDataValid)
                {
                    pAxisSMR.setConstraint(constraintData.constraint);
                    pAxisSMR.setObjectThickness(constraintData.distance);
                }
                else
                {
                    pAxisSMR.setConstraint(WD::WDSingleAxisMRConstraint());
                    pAxisSMR.setObjectThickness(0.0);
                }
            }
            // 暂时写死部分类型的管件使用特殊 单移动旋转轴
            if (viewer.objectAxisEditor().editType() == WD::WDObjectAxisEditor::EditType::ET_MoveRotate
                || viewer.objectAxisEditor().editType() == WD::WDObjectAxisEditor::EditType::ET_SingleMoveRotate)
            {
                this->setEditType(WD::WDObjectAxisEditor::EditType::ET_MoveRotate, pCurrentNode);
            }
        }
    }
    viewer.objectAxisEditor().setObject(pObj);
    // 禁用缩放
    if (viewer.objectAxisEditor().editType() == WD::WDObjectAxisEditor::EditType::ET_Scale)
    {
        viewer.objectAxisEditor().setEditType(WD::WDObjectAxisEditor::EditType::ET_None);
    }
}

void UiComNodeAxisEditor::retranslateUi()
{
    Trs("UiComNodeAxisEditor"

        , _pActMEnterValue
        , _pActMAlignWithFeature
        , _pActMBasePoint
        , _pActMMoveTo
        , _pActMCancel

        , _pActREnterValue
        , _pActRAlignWithDirection
        , _pActRBasePoint
        , _pActRCancel
    );
}