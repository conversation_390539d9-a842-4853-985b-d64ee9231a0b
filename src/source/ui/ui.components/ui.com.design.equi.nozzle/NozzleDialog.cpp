#include "NozzleDialog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "businessModule/WDBMColorTable.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include <QCheckBox>
#include "core/math/DirectionParser.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/businessModule/WDBMClaimMgr.h"

static auto TrF(const char* str, const char* cxtStr = "NozzleDialog")
{
    return WD::WDTs(cxtStr, str);
};


class NozzUpdateCommand : public WD::WDUndoCommand
{
public:
    struct AttributeInfo
    {
        std::string attrName;
        WD::WDBMAttrValue value;
        AttributeInfo(std::string attrName, WD::WDBMAttrValue value = WD::WDBMAttrValue()) : attrName(attrName), value(value)
        {
        }
    private:
        friend class NozzUpdateCommand;
        void update(WD::WDNode& node)
        {
            auto tmpVal = node.getAttribute(this->attrName);
            node.setAttribute(this->attrName, this->value);
            this->value = tmpVal;
        }
    };
private:
    WD::WDCore& _core;
    AttributeInfo _Catref;
    AttributeInfo _Height;

    AttributeInfo _Position;
    AttributeInfo _Rotate;

    // before属性
    WD::WDNode::WeakPtr _pNode;
public:
    NozzUpdateCommand(WD::WDCore& core
        , WD::WDNode::WeakPtr pNode
        , const AttributeInfo& catref
        , const AttributeInfo& height
        , const AttributeInfo& position
        , const AttributeInfo& rotate)
        :WDUndoCommand("Nozzle Update Command")
        , _core(core)
        , _pNode(pNode)
        , _Catref(catref)
        , _Height(height)
        , _Position(position)
        , _Rotate(rotate)
    {

    }
public:
    /**
    * @brief 重做
    *   如果存在子命令，将按照顺序执行所有子命令的redo
    *   子类可重写
    */
    virtual void redoP() override
    {
        auto pNozz = _pNode.lock();
        if (pNozz != nullptr)
        {
            _Catref.update(*pNozz);
            _Height.update(*pNozz);
            _Position.update(*pNozz);
            _Rotate.update(*pNozz);
            //更新节点
            pNozz->triggerUpdate();
            _core.needRepaint();
        }
        WD::WDUndoCommand::redoP();
    }
    /**
    * @brief 撤销
    *   如果存在子命令，将按照顺序执行所有子命令的undo
    *   子类可重写
    */
    virtual void undoP() override
    {
        WD::WDUndoCommand::undoP();
        auto pNozz = _pNode.lock();
        if (pNozz != nullptr)
        {
            _Catref.update(*pNozz);
            _Height.update(*pNozz);
            _Position.update(*pNozz);
            _Rotate.update(*pNozz);
            //更新节点
            pNozz->triggerUpdate();
            _core.needRepaint();
        }
    }
};

NozzleDialog::NozzleDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _core(core)
    , _positionCaptureHelpter(_core)
    , _nameHelpter(_core.getBMDesign())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 默认操作类型为创建
    _operationType  =   OT_Create;
    // 初始化操作类型窗口栈
    _stackedWidget  =   new QStackedWidget();
    ui.horizontalLayout_2->addWidget(_stackedWidget);
    _createWidget   =   new QWidget();
    auto pLayout = new QHBoxLayout();
    _createWidget->setLayout(pLayout);
    _labelName      =   new QLabel("Name");
    _lineEditName   =   new QLineEdit();
    pLayout->addWidget(_labelName, 0);
    pLayout->addWidget(_lineEditName, 1);
    _editWidget = ICustomWidgets::CreateNodeNameCEWidget(_core, this, false, "NozzleDialog");
    _stackedWidget->addWidget(_createWidget);
    _mapOTIdx[OT_Create] = 0;
    _stackedWidget->addWidget(_editWidget);
    _mapOTIdx[OT_Edit] = 1;

    _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CT_Repeat);
    // 设置坐标拾取对象
    _positionCaptureHelpter.setDoubleSpinBoxXYZ(
        ui.doubleSpinBoxPosX
        , ui.doubleSpinBoxPosY
        , ui.doubleSpinBoxPosZ
    );
    _positionCaptureHelpter.setCheckBoxCapture(ui.checkBox);
    _dotRender.setColor(WD::Color::red);

    this->retranslateUi();

    connect(ui.standardComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(onStandardComboBoxCurrentIndexChanged(int)));
    connect(ui.typeComboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(onTypeComboBoxCurrentIndexChanged(int)));
    connect(ui.pipeDiametercomboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(onPipeDiametercomboBoxCurrentIndexChanged(int)));

    connect(ui.okButton, &QPushButton::clicked, this, &NozzleDialog::onOkButtonClicked);
    connect(ui.cancelButton, &QPushButton::clicked, this, &NozzleDialog::reject);
	
    connect(&_positionCaptureHelpter
        , &UiPositionCaptureHelpter::sigPositionChanged
        , [&]()
    {
        // 调整辅助点的位置
        _dotRender.setPosition(WD::FVec3(_positionCaptureHelpter.position()));
        // 强制刷新视图
        _core.needRepaint();
    });
    connect(_editWidget, &UiNodeNameCE::sigCurrentNodeChanged, this, &NozzleDialog::slotCurrentNodeChanged);

    _nameHelpter.setLineEdit(_lineEditName);
}
NozzleDialog::~NozzleDialog()
{
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, & NozzleDialog::onNodeTreeCurrentNodeChanged};
}

void NozzleDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
    _core.nodeTree().noticeCurrentNodeChanged() += {this, & NozzleDialog::onNodeTreeCurrentNodeChanged};

    // 添加绘制对象
    _core.scene().addRenderObject(&_dotRender);
}
void NozzleDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, & NozzleDialog::onNodeTreeCurrentNodeChanged};

    // 关闭捕捉
    _positionCaptureHelpter.exit(true);
    // 移除绘制对象
    _core.scene().removeRenderObject(&_dotRender);
}

void NozzleDialog::onStandardComboBoxCurrentIndexChanged(int )
{
    ui.typeComboBox->clear();
    auto userData   = ui.standardComboBox->currentData();
    auto weakObject = userData.value<UiWeakObject>();
    auto pSPECNode  = weakObject.subObject<WD::WDNode>();
    if (pSPECNode == nullptr)
        return;
    initTypeList(*pSPECNode);
}
void NozzleDialog::onTypeComboBoxCurrentIndexChanged(int )
{
    ui.pipeDiametercomboBox->clear();

    auto userData   = ui.typeComboBox->currentData();
    auto weakObject = userData.value<UiWeakObject>();
    auto pSELENode  = weakObject.subObject<WD::WDNode>();
    if (pSELENode == nullptr)
        return;
    initPipeDiameterList(*pSELENode);
}
void NozzleDialog::onPipeDiametercomboBoxCurrentIndexChanged(int )
{
    ui.levelLineEdit->clear();

    auto pSPCONode = this->getSpcoNode();
    if (pSPCONode == nullptr)
        ui.levelLineEdit->clear();
    else
        ui.levelLineEdit->setText(QString::fromUtf8(pSPCONode->name().c_str()));
}

void NozzleDialog::onOkButtonClicked()
{
    switch (_operationType)
    {
    case NozzleDialog::OT_Create:
        {
            // 创建管嘴节点
            this->createNozzNode();
            // 生成新的管嘴名称
            _nameHelpter.resetName();
        }
        break;
    case NozzleDialog::OT_Edit:
        {
            this->updateNozzNode();
        }
        break;
    default:
        break;
    }
}

void NozzleDialog::slotCurrentNodeChanged(UiListSelectHelpter::Nodes preNodes, UiListSelectHelpter::Nodes currNodes)
{
    // 还原成之前的节点，并进行提示
    auto func = [this, preNodes](WD::WDNode::SharedPtr pCurrent)
    {
        if(pCurrent != nullptr)
        {
            WD_WARN_T("UiComNozzle", "Current node type is not NOZZ, can not edit!");
        }

        if(preNodes.empty())
            return;

        auto pPreNode = preNodes.front().lock();
        if(pPreNode == nullptr)
            return;
        _editWidget->setNode(pPreNode);
    };

    if(currNodes.empty())
    {
        func(nullptr);
        return ;
    }

    auto pCurrent = currNodes.front().lock();
    if(pCurrent == nullptr)
    {
        func(nullptr);
    }

    if(pCurrent->isType("NOZZ"))
    {
        // 使用当前管嘴节点更新界面
        this->updateWidget(pCurrent);
    }
    else
    {
        func(pCurrent);
    }
}

void NozzleDialog::setOperationType(OperationType type)
{
    _operationType = type;
    // 设置不同界面标题
    switch (_operationType)
    {
    case NozzleDialog::OT_Create:
        {
            this->updateWidget(nullptr);
        }
        break;
    case NozzleDialog::OT_Edit:
        {
            this->updateWidget(_core.nodeTree().currentNode());
        }
        break;
    default:
        break;
    }
}

WD::WDNode::SharedPtr NozzleDialog::getParentNode() const
{
    auto pCurrNode = _core.nodeTree().currentNode();
    if (pCurrNode == nullptr)
        return nullptr;
    // 使用当前选中节点以及挂载规则获取管嘴可挂载的父节点
    return _core.getBMDesign().findParentWithType(*pCurrNode, "NOZZ");
}

bool NozzleDialog::namedChildExist(WD::WDNode& parentNode, const std::string& name)
{
    return WD::WDNode::ChildrenTraversalHelpterR(parentNode
        , [](const std::string& name, WD::WDNode& node)
        {
            return node.name() == name;
        }, name);
}

void NozzleDialog::initSPECList()
{
    clearUi();
    //查询所有管嘴用途的等级
    auto specs = WD::GetSPECByAttrPurpose(_core.getBMCatalog(), "NOZZ");
    //添加等级显示
    for (auto pSPECNode : specs)
    {
        if (pSPECNode == nullptr)
            continue;
        QString strName = QString::fromUtf8(pSPECNode->name().c_str());
        auto strDescr   = pSPECNode->getAttribute("Description").toString();
        if (!strDescr.empty())
            strName = QString::fromUtf8(strDescr.c_str());

        QVariant userData;
        userData.setValue(UiWeakObject(pSPECNode));
        ui.standardComboBox->addItem(strName, userData);
    }
}

void NozzleDialog::clearUi()
{
    ui.standardComboBox->clear();
    ui.typeComboBox->clear();
    ui.pipeDiametercomboBox->clear();
    ui.levelLineEdit->clear();
    ui.doubleSpinBoxHeight->setValue(0.0);
    _positionCaptureHelpter.setPosition({ 0, 0, 0 }, true);
    ui.lineEditDirectionP1->setText("Z");
    //ui.okButton->setFocusPolicy(Qt::StrongFocus);
}

void NozzleDialog::initTypeList(const WD::WDNode& pSPECNode)
{
    ui.typeComboBox->clear();
    for (size_t i = 0; i < pSPECNode.childCount(); ++i)
    {
        auto pSELENode = pSPECNode.childAt(i);
        if (pSELENode == nullptr)
            continue;

        QString strName = QString::fromUtf8(pSELENode->name().c_str());
        auto strDescr   = pSELENode->getAttribute("Description").toString();
        if (!strDescr.empty())
            strName = QString::fromUtf8(strDescr.c_str());

        QVariant tUserData;
        tUserData.setValue(UiWeakObject(pSELENode));
        ui.typeComboBox->addItem(strName, tUserData);
    }
    ui.typeComboBox->setCurrentIndex(0);
}

void NozzleDialog::initPipeDiameterList(const WD::WDNode& pSELENode)
{
    ui.pipeDiametercomboBox->clear();
    for (auto pSPCONode : pSELENode.children()) 
    {
        if (pSPCONode == nullptr)
            continue;  
        auto strBore = pSPCONode->getAttribute("Answer").toString();
        if (strBore.empty())
        {
            assert(false);
            continue;
        }
        QVariant tUserData;
        tUserData.setValue(UiWeakObject(pSPCONode));
        ui.pipeDiametercomboBox->addItem(QString::fromUtf8(strBore.c_str()), tUserData);
    }
    ui.pipeDiametercomboBox->setCurrentIndex(0);
}

WD::WDNode::SharedPtr NozzleDialog::getSpcoNode() const
{
    auto userData   = ui.pipeDiametercomboBox->currentData();
    auto weakObject = userData.value<UiWeakObject>();
    auto pSPCONode  = weakObject.subObject<WD::WDNode>();
    return pSPCONode;
}

void NozzleDialog::createNozzNode()
{
    // 获取父节点
    auto pParentNode = this->getParentNode();
    if (pParentNode == nullptr)
    {
        WD_ERROR(TrF("Invalid parent"));
        return;
    }
    //  权限校验
    if (!_core.getBMDesign().permissionMgr().check(*pParentNode))
    {
        WD_WARN_T("NozzleDialog", "You cannot operate the current node!");
        return;
    }
    // 申领对象
    if (!_core.getBMDesign().claimMgr().checkAdd(pParentNode))
        return;

    // 校验父节点是否存在相同名称
    if (_nameHelpter.exists())
    {
        WD_ERROR(TrF("Same name exists"));
        return;
    }
    std::string name = _nameHelpter.name();
    double height = ui.doubleSpinBoxHeight->value();
    // 校验高度必须大于0
    if (height <= 0.0)
    {
        WD_ERROR(TrF("The height must be greater than 0"));
        return;
    }

    std::string dirStr = ui.lineEditDirectionP1->text().toUtf8().data();
    WD::DVec3 direction;
    // 校验方向必须有效
    if (!WD::DDirectionParserXYZ::Direction(dirStr, direction))
    {
        WD_ERROR(TrF("Invalid direction"));
        return;
    }
    WD::WDNode::SharedPtr pSPCONode = this->getSpcoNode();
    if (pSPCONode == nullptr)
    {
        WD_ERROR(TrF("Invalid SPCO"));
        return;
    }
    // 创建管嘴节点
    auto pNozzNode = _core.getBMDesign().create(pParentNode, "NOZZ", name);
    if (pNozzNode == nullptr)
    {
        WD_ERROR(TrF("Create failed"));
        return;
    }
    // 设置元件库等级引用
    pNozzNode->setAttribute("Catref", WD::WDBMNodeRef(pSPCONode));
    //设置高度数据
    pNozzNode->setAttribute("Height", height);
    //更新管嘴业务数据
    pNozzNode->updateModel();
    // 设置位置数据
    pNozzNode->setAttribute("Position WRT World", _positionCaptureHelpter.position());
    _positionCaptureHelpter.setPosition({ 0, 0, 0 }, true);

    //计算并设置节点朝向数据
    auto pKeyPoint = pNozzNode->keyPoint(1);
    if (pKeyPoint != nullptr)
    {
        WD::DQuat quat = WD::DQuat::FromVectors(pKeyPoint->direction, direction);
        pNozzNode->setAttribute("Orientation", quat);
    }
    else
    {
        WD_ERROR(TrF("Nozzle set rotate failed"));
    }

    // 设置节点的默认颜色
    _core.getBMDesign().colorTable().setNodeColor(*pNozzNode);
    //更新节点
    pNozzNode->triggerUpdate();

    _core.nodeTree().setCurrentNode(pNozzNode); // 会触发信号

    auto cmdNodeCreated = WD::WDBMBase::MakeCreatedCommand({ pNozzNode });
    auto cmdAddToScnen = WD::WDBMBase::MakeSceneAddCommand({ pNozzNode });
    if (cmdAddToScnen != nullptr && cmdNodeCreated != nullptr)
    {
        _core.undoStack().beginMarco("Create nozz");
        _core.undoStack().push(cmdNodeCreated);
        _core.undoStack().push(cmdAddToScnen);
        _core.undoStack().endMarco();
    }
}
void NozzleDialog::updateNozzNode()
{
    auto curNodes = _editWidget->curNodes();
    if (curNodes.empty() || curNodes.front().expired())
    {
        WD::WDTs("NozzleDialog", "Must select node!");
        return ;
    }
    auto pNozz = curNodes.front().lock();
    if(pNozz == nullptr)
        return;

    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pNozz, { "Position", "Orientation", "Height", "Catref" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    double height = ui.doubleSpinBoxHeight->value();
    // 校验高度必须大于0
    if (height <= 0.0)
    {
        WD_ERROR(TrF("The height must be greater than 0"));
        return;
    }

    std::string dirStr = ui.lineEditDirectionP1->text().toUtf8().data();
    WD::DVec3 direction;
    // 校验方向必须有效
    if (!WD::DDirectionParserXYZ::Direction(dirStr, direction))
    {
        WD_ERROR(TrF("Invalid direction"));
        return;
    }
    WD::WDNode::SharedPtr pSPCONode = this->getSpcoNode();
    if (pSPCONode == nullptr)
    {
        WD_ERROR(TrF("Invalid SPCO"));
        return;
    }

    NozzUpdateCommand::AttributeInfo catrefInfo("Catref", WD::WDBMAttrValue(WD::WDBMNodeRef(pSPCONode)));
    //设置高度数据
    NozzUpdateCommand::AttributeInfo heightInfo("Height", WD::WDBMAttrValue(height));
    //更新管嘴业务数据
    pNozz->updateModel();
    // 设置位置数据
    NozzUpdateCommand::AttributeInfo positionInfo("Position WRT World", WD::WDBMAttrValue(_positionCaptureHelpter.position()));
    //计算并设置节点朝向数据
    NozzUpdateCommand::AttributeInfo rotateInfo("Orientation WRT Owner", pNozz->getAttribute("Orientation WRT Owner"));
    {
        auto pKeyPoint = pNozz->keyPoint(1);
        if (pKeyPoint != nullptr)
            rotateInfo.value = WD::DQuat::FromVectors(pKeyPoint->direction, direction);
        else
            WD_ERROR(TrF("Nozzle set rotate failed"));
    }
    //更新节点
    pNozz->triggerUpdate();

    _core.undoStack().push(new NozzUpdateCommand(_core, pNozz, catrefInfo, heightInfo, positionInfo, rotateInfo));

    // 触发重绘
    _core.needRepaint();
}
void NozzleDialog::setSpcoNode(WD::WDNode::SharedPtr pSpco)
{
    // 设置levelLineEdit文本
    if (pSpco == nullptr)
    {
        ui.levelLineEdit->clear();
        return;
    }
    ui.levelLineEdit->setText(QString::fromUtf8(pSpco->name().c_str()));
    // 设置pipeDiametercomboBox选中项
    for (int i = 0; i < ui.pipeDiametercomboBox->count(); ++i)
    {
        auto    userData    =   ui.pipeDiametercomboBox->itemData(i);
        auto    pNode       =   userData.value<UiWeakObject>().subObject<WD::WDNode>();
        if (pNode.get() == pSpco.get())
        {
            ui.pipeDiametercomboBox->blockSignals(true);
            ui.pipeDiametercomboBox->setCurrentIndex(i);
            ui.pipeDiametercomboBox->blockSignals(false);
            break;
        }
    }
    // 设置typeComboBox选中项
    auto pType = pSpco->parent();
    if (pType == nullptr)
        return ;
    for (int i = 0; i < ui.typeComboBox->count(); ++i)
    {
        auto    userData    =   ui.typeComboBox->itemData(i);
        auto    pNode       =   userData.value<UiWeakObject>().subObject<WD::WDNode>();
        if (pNode == pType)
        {
            ui.typeComboBox->blockSignals(true);
            ui.typeComboBox->setCurrentIndex(i);
            ui.typeComboBox->blockSignals(false);
            break;
        }
    }
    // 设置standardComboBox选中项
    auto pSpec = pType->parent();
    if (pSpec == nullptr)
        return ;
    for (int i = 0; i < ui.standardComboBox->count(); ++i)
    {
        auto    userData    =   ui.standardComboBox->itemData(i);
        auto    pNode       =   userData.value<UiWeakObject>().subObject<WD::WDNode>();
        if (pNode == pSpec)
        {
            ui.standardComboBox->blockSignals(true);
            ui.standardComboBox->setCurrentIndex(i);
            ui.standardComboBox->blockSignals(false);
            break;
        }
    }
}

/**
 * @brief 获取二级父节点
 * @param pNode 
*/
static const WD::WDNode::SharedPtr GetSecondParent(WD::WDNode::SharedPtr pNode)
{
    if (pNode != nullptr&& pNode->parent() != nullptr
        && pNode->parent()->parent() != nullptr)
    {
        return pNode->parent()->parent();
    }
    return nullptr;
}
/**
 * @brief 获取父节点
 * @param pNode 
*/
static const WD::WDNode::SharedPtr GetParent(WD::WDNode::SharedPtr pNode)
{
    if (pNode != nullptr && pNode->parent() != nullptr)
    {
        return pNode->parent();
    }
    return nullptr;
}

void NozzleDialog::updateWidget(WD::WDNode::SharedPtr pNode)
{
    // 根据操作类型显示不同界面
    _stackedWidget->setCurrentIndex(_mapOTIdx[_operationType]);
    // 初始化SPEC下拉列表
    this->initSPECList();
    if (pNode == nullptr)
    {
        // 设置窗口标题
        this->setWindowTitle(QString::fromUtf8(WD::WDTs("NozzleDialog", "Create Nozzle").c_str()));
        setCaptureTransform();
        _positionCaptureHelpter.setPosition({ 0, 0, 0 }, true);
    }
    else
    {
        // 设置窗口标题
        this->setWindowTitle(QString::fromUtf8(WD::WDTs("NozzleDialog", "Edit Nozzle").c_str()));
        setCaptureTransform();
        // 设置当前选中节点
        _editWidget->setNode(pNode);

        auto pCatRefNode = pNode->getAttribute("Catref").toNodeRef().refNode();
        // 初始化SELE下拉列表
        auto pSParent = GetSecondParent(pCatRefNode);
        if (pSParent != nullptr)
            this->initTypeList(*pSParent);
        // 初始化管径下拉列表
        auto pParent = GetParent(pCatRefNode);
        if (pParent != nullptr)
            this->initPipeDiameterList(*pParent);
        // 设置spco节点用于更新界面
        this->setSpcoNode(pCatRefNode);
        // 设置管嘴高度
        auto height = pNode->getAttribute("Height").toDouble();
        ui.doubleSpinBoxHeight->setValue(height);
        // 设置位置
        const auto position = pNode->getAttribute("Position").toDVec3();
        _positionCaptureHelpter.setPosition(position, true);
        // 设置朝向
        {
            auto pKeyPoint = pNode->keyPoint(1);
            if (pKeyPoint != nullptr)
            {
                const auto quat = pNode->getAttribute("Orientation").toQuat();
                WD::DMat4   mat = WD::DMat4::Compose(quat, WD::DVec3(1.0, 1.0, 1.0));
                auto        direction = mat * pKeyPoint->direction;
                auto        dirStr = WD::DDirectionParser::OutputStringByDirection(direction);
                ui.lineEditDirectionP1->setText(QString::fromUtf8(dirStr.c_str()));
            }
        }
    }
}
void NozzleDialog::setCaptureTransform()
{
    // 获取合法父节点
    auto parent  = getParentNode();
    if(parent == nullptr)
    {
        if(_operationType == OperationType::OT_Create)
            _nameHelpter.resetName();
        ui.lineEditWrt->clear();
    }
    else
    {
        // 获取原捕捉工具的坐标
        auto position = _positionCaptureHelpter.position(true);
        _positionCaptureHelpter.setTransform(parent->globalTransform());
        switch (_operationType)
        {
        case NozzleDialog::OT_Create:
            {       
                _nameHelpter.resetName();
                // 捕捉工具的参考系改变后，会改变捕捉工具显示的坐标，还原原始数据
                _positionCaptureHelpter.setPosition(position, true);
            }
            break;
        case NozzleDialog::OT_Edit:
            {
                // 默认设置成当前管嘴节点的相对于父节点的坐标
                auto pNozzNode = _core.nodeTree().currentNode();
                if(pNozzNode == nullptr || !pNozzNode->isType("NOZZ"))
                {
                    // 当前节点为空或非管嘴节点，则捕捉工具置空
                    _positionCaptureHelpter.setPosition({ 0,0,0 }, true);
                }
                else
                {
                    // 当前节点为管嘴节点，默认设置为管嘴节点相对于父节点的坐标
                    auto nozzPos = pNozzNode->getAttribute("Position").toDVec3();
                    _positionCaptureHelpter.setPosition(nozzPos, true);
                }
            }
            break;
        default:
            break;
        }
        _dotRender.setPosition(WD::FVec3(_positionCaptureHelpter.position()));
        // 显示当前合法父节点
        ui.lineEditWrt->setText(parent->name().c_str());
    }  
}

void NozzleDialog::onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused(pCurrNode);
    WDUnused(pPrevNode);
    WDUnused(sender);
    switch(_operationType)
    {
    case OperationType::OT_Create:
        {
            setCaptureTransform(); 
        }break;
    case OperationType::OT_Edit:
        {}
        break;
    }
}

void NozzleDialog::showParentInfo()
{
    auto parent = getParentNode();
    if(parent == nullptr)
    {
        ui.lineEditWrt->clear();
        return;
    }
    ui.lineEditWrt->setText(QString::fromUtf8(parent->name().c_str()));
}

void NozzleDialog::retranslateUi()
{
    Trs("NozzleDialog"

        , static_cast<QDialog*>(this)

        , _labelName
        , ui.groupBoxGradeSpecification
        , ui.labelStandard
        , ui.labelType
        , ui.labelPipeDiameter
        , ui.labelGrade
        , ui.labelHeight
        , ui.groupBoxCoordinate
        , ui.labelP1Direction
        , ui.labelWrt
        , ui.checkBox

        , ui.okButton
        , ui.cancelButton);
}