#include "Common.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/WDCore.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"

static constexpr const char*    Joint_Cata_Name     =   "CADCHVACCATA";
static constexpr const char*    Joint_DESC_DTitle   =   "Description";
static constexpr const char*    Joint_ALPH_DTitle   =   "Alpha";
static constexpr const char*    Joint_NUME_DTitle   =   "Numeric";
static constexpr const char*    Joint_HVJA_DTitle   =   "A Dimension";
static constexpr const char*    Joint_HVJB_DTitle   =   "B Dimension";
static constexpr const char*    Joint_HVJC_DTitle   =   "C Dimension";
static constexpr const double   Epsilon             =   0.001;

static std::array<std::string, 90> s_DefDesc;
JointShape              JointShapeFromName(const char* name)
{
    if (_stricmp(name, "HJRM") == 0)
        return JS_Rectangular;
    else if (_stricmp(name, "HJCM") == 0)
        return JS_Circular;
    else if (_stricmp(name, "HJFM") == 0)
        return JS_FlatOval;
    else
        return JS_Rectangular;
}
std::string             ConnNameToShapeName(const std::string& name)
{
    if (name == "RECT")
        return "HJRM";
    else if (name == "CIRC")
        return "HJCM";
    else if (name == "FOVA")
        return "HJFM";
    return "";
}

std::set<std::string>   ShapeNameSet(CatelogType type)
{
    switch (type)
    {
    case CT_Rectangular:
        return {"RSTRA", "RCAP", "RTAPER", "RCRANKTAPER", "RSWAN", "RCRANK", "RMOFFS", "RMELBOW", "RSBEND", "RRBEND"
            , "RMAT", "RSTIF", "RSTIF3", "RSTIF2", "RSTIF1", "RGASKET", "RACCE", "RATURN", "RASPLI", "RBSPLI"
            , "RRSPLI", "RDEFL", "RTHOLE3", "RTHOLE1", "RTHOLE2", "RTHOLE4", "RTHOLE5", "RTHOLE6", "RTHOLE7"
            , "RTHOLE8", "RMESH", "RSBD", "RSKIRT", "RSTHRE", "RRTHRE", "RTTHRE", "RBREECH"};
    case CT_Circular:
        return {"CSTRA", "CMCOUP", "CCAP", "CTAPER", "CMOFFS", "CELBOW", "CRBEND", "C3SEGB", "C5SEGB", "C7SEGB"
            , "C4SEGB", "CMAT", "CBENDFLEX", "CFLEX", "CSTIF", "CGASKET", "CTHOLE1", "CTHOLE2", "CMESH", "CCOWL"
            , "CSBD", "RSADD", "CRTHRE", "CBREECH", "CBREECHA", "CSBTEEP"};
    case CT_FlatOval:
        return {"FSTRA", "FMCOUP", "FCAP", "FTAPER" "FMOFFSB", "FMOFFS", "FELBOW", "FELBOWB", "F3SEGB", "F3SEGBB"
            , "F4SEGB", "F4SEGBB", "F5SEGB", "F5SEGBB", "FSTIF", "FGASKET"};
    case CT_Transformations:
        return {"SQRD", "SQFO", "FORD", "FOFO", "CRSPIGBOX", "CRLINPLEN", "CRSPIGPLT", "FRSPIGPLT"};
    case CT_BranchConnectors:
        return {"RBOOT", "CBOOT", "FABOOT", "FBBOOT", "RSBRCO", "CSBRCO", "FBSBRCO", "FASBRCO", "RFISH", "CFISH"
            , "CCONIC", "RANGBRCO", "CANGBRCO", "SQRDBRCO", "TAPRBRCO", "CMITB", "FAFISH", "FBFISH", "FAANGBRCO"
            , "FBANGBRCO"};
    case CT_InlinePlantEquipment:
        return {"RFIRED", "CFIRED", "FFIRED", "RVCD", "RMVCD", "CRVCD", "CRFJFD", "RFJFD", "RSILE", "CSILE", "RSILBEN"
            , "HBATT", "CBATT", "RGRILOFF", "CGRILOFF", "RGRILIN", "CGRILIN", "CAXFAN", "LHCENTRIFAN", "RHCENTRIFAN"
            , "GENPLNT", "CGENPLNT", "STDAHU"};
    case CT_ExtraInlineEquipment:
        return {"PINHOOD", "PEXHOOD", "PCDIFCI", "PRDIFCI", "PRGRIRI", "PSADISU", "PCADISU", "PRADISU", "PRRADISU"
            , "PFLVTER", "PFLVTERR", "PLVTFCM", "PCBCICO", "PCBCIRO", "PCBCIROT", "PCBCIROF"};
    case CT_EquipmentNozzles:
        return {};
    case CT_UserDefinedFittings:
        return {};
    case CT_Assemblies:
        return {};
    default:
        break;
    }

    return std::set<std::string>();
}
WD::WDNode::Nodes       CollectByShape(WD::WDNode::SharedPtr pSpec, CatelogType type)
{
    WD::WDNode::Nodes result;

    if (pSpec == nullptr)
        return result;

    // 获取形状对应的节点名称集合
    auto names = ShapeNameSet(type);
    if (names.empty())
        return result;

    WD::WDNode::RecursionHelpter(*pSpec
        ,[&](WD::WDNode& node)
        {
            if (node.childCount() != 0)
                return ;

            // 取名称最后/分隔的字符串
            auto nodeNames = WD::StringSplit(node.name(), "/");
            if (nodeNames.empty())
                return ;

            if (names.find(nodeNames.back()) != names.end())
                result.push_back(WD::WDNode::ToShared(&node));
        });

    return result;
}
WD::WDNode::SharedPtr   HvacCata()
{
    return WD::Core().getBMCatalog().findNode(Joint_Cata_Name, "CATA");
}
std::string             GetArriveShapeStr(WD::WDNode& com)
{
    return ConnNameToShapeName(WD::GetHConnType(com));
}
std::string             GetLeaveShapeStr(WD::WDNode& com)
{
    return ConnNameToShapeName(WD::GetTConnType(com));
}
std::string             GetThirdShapeStr(WD::WDNode& com)
{
    // 获取第三口连接方式
    auto pPt = com.keyPoint(WD::WDBMDPipeUtils::Fork(com));
    if (pPt == nullptr)
        return "";
    auto& hConn = pPt->connType();
    return ConnNameToShapeName(hConn);
}
WD::WDNode::SharedPtr   ExtractArriveJoint(WD::WDCore& core, WD::WDNode& com)
{
    if (!WD::WDBMDPipeUtils::IsPipeComponent(com))
        return nullptr;
    
    // 获取元件入口点形状的连接点SECT节点
    WD::WDNode::SharedPtr pSect   =   nullptr;
    auto        pCata   =   HvacCata();
    if (pCata == nullptr)
        return nullptr;
    auto        shape   =   GetArriveShapeStr(com);
    for (size_t i = 0; i < pCata->childCount(); ++i)
    {
        auto pChild = pCata->childAt(i);
        if (pChild == nullptr)
            continue;
        if (pChild->getAttribute("Purpose").toWord() == shape)
        {
            pSect = pChild;
            break;
        }
    }
    if (pSect == nullptr)
        return nullptr;

    // SECT节点下查询指定名称的Joint节点
    auto    desParam    =   com.getAttribute("Desparam").toStringVector();
    std::string temp1;
    std::string temp2;
    if (auto opt = GetParamValue<std::string>(desParam, 57))
        temp1 = opt.value();
    if (auto opt = GetParamValue<std::string>(desParam, 67))
        temp2 = opt.value();

    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    auto dtseName = aGet.execExpression(temp1).convertToString() + aGet.execExpression(temp2).convertToString();
    for (size_t i = 0; i < pSect->childCount(); ++i)
    {
        auto pChild = pSect->childAt(i);
        if (pChild == nullptr)
            continue;

        auto names = WD::StringSplit(pChild->name(), "/");
        if (names.empty())
            continue;
        if (names.back() == dtseName)
            return pChild;
    }

    return nullptr;
}
WD::WDNode::SharedPtr   ExtractLeaveJoint(WD::WDCore& core, WD::WDNode& com)
{
    // 获取元件出口点形状的连接点SECT节点
    WD::WDNode::SharedPtr pSect   =   nullptr;
    auto        pCata   =   HvacCata();
    if (pCata == nullptr)
        return nullptr;
    auto        shape   =   GetArriveShapeStr(com);
    for (size_t i = 0; i < pCata->childCount(); ++i)
    {
        auto pChild = pCata->childAt(i);
        if (pChild == nullptr)
            continue;
        
        if (pChild->getAttribute("Purpose").toWord() == shape)
        {
            pSect = pChild;
            break;
        }
    }
    if (pSect == nullptr)
        return nullptr;

    // SECT节点下查询指定名称的Joint节点
    auto    desParam    =   com.getAttribute("Desparam").toStringVector();
    std::string temp1;
    std::string temp2;
    if (auto opt = GetParamValue<std::string>(desParam, 58))
        temp1 = opt.value();
    if (auto opt = GetParamValue<std::string>(desParam, 68))
        temp2 = opt.value();
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    auto    dtseName    = aGet.execExpression(temp1).convertToString() + aGet.execExpression(temp2).convertToString();
    for (size_t i = 0; i < pSect->childCount(); ++i)
    {
        auto pChild = pSect->childAt(i);
        if (pChild == nullptr)
            continue;

        auto names = WD::StringSplit(pChild->name(), "/");
        if (names.empty())
            continue;
        if (names.back() == dtseName)
            return pChild;
    }

    return nullptr;
}
WD::WDNode::SharedPtr   ExtractThirdJoint(WD::WDCore& core, WD::WDNode& com)
{
    // 获取元件出口点形状的连接点SECT节点
    WD::WDNode::SharedPtr pSect   =   nullptr;
    auto        pCata   =   HvacCata();
    if (pCata == nullptr)
        return nullptr;
    auto        shape   =   GetArriveShapeStr(com);
    for (size_t i = 0; i < pCata->childCount(); ++i)
    {
        auto pChild = pCata->childAt(i);
        if (pChild == nullptr)
            continue;

        if (pChild->getAttribute("Purpose").toWord() == shape)
        {
            pSect = pChild;
            break;
        }
    }
    if (pSect == nullptr)
        return nullptr;

    // SECT节点下查询指定名称的Joint节点
    auto    desParam    =   com.getAttribute("Desparam").toStringVector();
    std::string temp1;
    std::string temp2;
    if (auto opt = GetParamValue<std::string>(desParam, 59))
        temp1 = opt.value();
    if (auto opt = GetParamValue<std::string>(desParam, 69))
        temp2 = opt.value();
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    auto dtseName = aGet.execExpression(temp1).convertToString() + aGet.execExpression(temp2).convertToString();
    for (size_t i = 0; i < pSect->childCount(); ++i)
    {
        auto pChild = pSect->childAt(i);
        if (pChild == nullptr)
            continue;

        auto names = WD::StringSplit(pChild->name(), "/");
        if (names.empty())
            continue;
        if (names.back() == dtseName)
            return pChild;
    }

    return nullptr;
}
WD::WDNode::SharedPtr   GetSpecJoint(WD::WDNode::SharedPtr pCata, JointShape shape)
{
    std::string typeStr = JointShapeToName(shape);
    // 查找对应形状的SECT节点
    WD::WDNode::SharedPtr pSect = nullptr;
    for (size_t i = 0; i < pCata->childCount(); ++i)
    {
        auto pChild = pCata->childAt(i);
        if (pChild == nullptr || !pChild->isType("SECT"))
            continue;
        if (pChild->getAttribute("Purpose").toWord() == typeStr)
        {
            pSect = pChild;
            break;
        }
    }
    if (pSect == nullptr)
        return nullptr;

    // SECT节点下获取function为spec的节点
    for (size_t i = 0; i < pSect->childCount(); ++i)
    {
        auto    pChild  =   pSect->childAt(i);
        if (pChild == nullptr || !pChild->isType("DTSE"))
            continue;
        if (pChild->getAttribute("Function").toString() == Joint_Spec)
            return pChild;
    }

    return nullptr;
}
WD::WDNode::Nodes       GetJoints(WD::WDNode::SharedPtr pCata, JointShape shape)
{
    WD::WDNode::Nodes result;
    std::string typeStr = JointShapeToName(shape);
    // 查找对应形状的SECT节点
    WD::WDNode::SharedPtr pSect = nullptr;
    for (size_t i = 0; i < pCata->childCount(); ++i)
    {
        auto pChild = pCata->childAt(i);
        if (pChild == nullptr)
            continue;
        if (pChild == nullptr || !pChild->isType("SECT"))
            continue;
        if (pChild->getAttribute("Purpose").toWord() == typeStr)
        {
            pSect = pChild;
            break;
        }
    }
    if (pSect == nullptr)
        return result;

    // 获取SECT节点下所有DTSE节点
    for (size_t i = 0; i < pSect->childCount(); ++i)
    {
        auto    pChild  =   pSect->childAt(i);
        if (pChild == nullptr || !pChild->isType("DTSE"))
            continue;
        result.push_back(pChild);
    }

    return result;
}
std::string             GetJointDesc(WD::WDCore& core, WD::WDNode& joint)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        if (pChild->getAttribute("Dtitle").toString() != Joint_DESC_DTitle)
            continue;

        // TODO: '(xxxxxxxxxxx)'提取出xxxxxxx字符串
        auto pPtyStr = pChild->getAttribute("Pproperty").toString();
        return aGet.execExpression(pPtyStr).convertToString();
    }

    return "";
}
std::string             GetJointDisp(WD::WDCore& core, WD::WDNode& joint)
{
    WD::WDNode::SharedPtr pALPH = nullptr;
    WD::WDNode::SharedPtr pNUME = nullptr;
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        auto dTitleStr = pChild->getAttribute("Dtitle").toString();
        if (dTitleStr == Joint_ALPH_DTitle)
        {
            pALPH = pChild;
        }
        else if (dTitleStr == Joint_NUME_DTitle)
        {
            pNUME = pChild;
        }
    }
    if (pALPH == nullptr || !pALPH->isType("DATA") || pNUME == nullptr || !pNUME->isType("DATA"))
        return "";
    auto pPtyA = pALPH->getAttribute("Pproperty").toString();
    auto pPtyB = pNUME->getAttribute("Pproperty").toString();
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    return aGet.execExpression(pPtyA).convertToString() + aGet.execExpression(pPtyB).convertToString();
}
void                    UpdateARRJointParam(WD::WDCore& core, WD::WDNode& joint, WD::WDPropertyGroup& group, std::vector<size_t>& indexs)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        auto pPtyStr = pChild->getAttribute("Pproperty").toString();
        pPtyStr = aGet.execExpression(pPtyStr).convertToString();
        auto dTitleStr  = pChild->getAttribute("Dtitle").toString();
        if (dTitleStr == Joint_ALPH_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 57) == indexs.end())
            {
                indexs.emplace_back(57);
                group.addPropertyString(s_DefDesc[57], pPtyStr);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[57]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_NUME_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 67) == indexs.end())
            {
                indexs.emplace_back(67);
                group.addPropertyString(s_DefDesc[67], pPtyStr);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[67]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJA_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 20) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[20], pPtyStr);
                indexs.emplace_back(20);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[20]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJB_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 21) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[21], pPtyStr);
                indexs.emplace_back(21);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[21]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJC_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 22) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[22], pPtyStr);
                indexs.emplace_back(22);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[22]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
    }
}
void                    UpdateARRJointParam(WD::WDCore& core, WD::WDNode& joint, WD::StringVector& desParam)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        
        auto pPtyStr = pChild->getAttribute("Pproperty").toString();
        pPtyStr = aGet.execExpression(pPtyStr).convertToString();
        auto dTitleStr = pChild->getAttribute("Dtitle").toString();

        if (dTitleStr == Joint_ALPH_DTitle)
        {
            SetParamValue(desParam, 57, pPtyStr);
        }
        else if (dTitleStr == Joint_NUME_DTitle)
        {
            SetParamValue(desParam, 67, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJA_DTitle)
        {
            SetParamValue(desParam, 20, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJB_DTitle)
        {
            SetParamValue(desParam, 21, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJC_DTitle)
        {
            SetParamValue(desParam, 22, pPtyStr);
        }
    }
}
void                    UpdateLEAJointParam(WD::WDCore& core, WD::WDNode& joint, WD::WDPropertyGroup& group, std::vector<size_t>& indexs)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        
        auto pPtyStr = pChild->getAttribute("Pproperty").toString();
        pPtyStr = aGet.execExpression(pPtyStr).convertToString();
        auto dTitleStr = pChild->getAttribute("Dtitle").toString();

        if (dTitleStr == Joint_ALPH_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 58) == indexs.end())
            {
                indexs.emplace_back(58);
                group.addPropertyString(s_DefDesc[58], pPtyStr);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[58]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_NUME_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 68) == indexs.end())
            {
                indexs.emplace_back(68);
                group.addPropertyString(s_DefDesc[68], pPtyStr);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[68]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJA_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 23) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[23], pPtyStr);
                indexs.emplace_back(23);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[23]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJB_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 24) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[24], pPtyStr);
                indexs.emplace_back(24);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[24]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJC_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 25) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[25], pPtyStr);
                indexs.emplace_back(25);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[25]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
    }
}
void                    UpdateLEAJointParam(WD::WDCore& core, WD::WDNode& joint, WD::StringVector& desParam)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        
        auto pPtyStr = pChild->getAttribute("Pproperty").toString();
        pPtyStr = aGet.execExpression(pPtyStr).convertToString();
        auto dTitleStr = pChild->getAttribute("Dtitle").toString();

        if (dTitleStr == Joint_ALPH_DTitle)
        {
            SetParamValue(desParam, 58, pPtyStr);
        }
        else if (dTitleStr == Joint_NUME_DTitle)
        {
            SetParamValue(desParam, 68, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJA_DTitle)
        {
            SetParamValue(desParam, 23, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJB_DTitle)
        {
            SetParamValue(desParam, 24, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJC_DTitle)
        {
            SetParamValue(desParam, 25, pPtyStr);
        }
    }
}
void                    UpdateThiJointParam(WD::WDCore& core, WD::WDNode& joint, WD::WDPropertyGroup& group, std::vector<size_t>& indexs)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        
        auto pPtyStr = pChild->getAttribute("Pproperty").toString();
        pPtyStr = aGet.execExpression(pPtyStr).convertToString();;
        auto dTitleStr = pChild->getAttribute("Dtitle").toString();

        if (dTitleStr == Joint_ALPH_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 59) == indexs.end())
            {
                indexs.emplace_back(59);
                group.addPropertyString(s_DefDesc[59], pPtyStr);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[59]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_NUME_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 69) == indexs.end())
            {
                indexs.emplace_back(69);
                group.addPropertyString(s_DefDesc[69], pPtyStr);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[69]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJA_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 26) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[26], pPtyStr);
                indexs.emplace_back(26);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[26]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJB_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 27) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[27], pPtyStr);
                indexs.emplace_back(27);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[27]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
        else if (dTitleStr == Joint_HVJC_DTitle)
        {
            if (std::find(indexs.begin(), indexs.end(), 28) == indexs.end())
            {
                group.addPropertyString(s_DefDesc[28], pPtyStr);
                indexs.emplace_back(28);
            }
            else
            {
                auto pPty = group.findProperty(s_DefDesc[28]);
                if (pPty != nullptr)
                {
                    pPty->valueFromString(pPtyStr);
                }
            }
        }
    }
}
void                    UpdateThiJointParam(WD::WDCore& core, WD::WDNode& joint, WD::StringVector& desParam)
{
    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet();
    for (size_t i = 0; i < joint.childCount(); ++i)
    {
        auto pChild = joint.childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        
        auto pPtyStr = pChild->getAttribute("Pproperty").toString();
        pPtyStr = aGet.execExpression(pPtyStr).convertToString();
        auto dTitleStr = pChild->getAttribute("Dtitle").toString();

        if (dTitleStr == Joint_ALPH_DTitle)
        {
            SetParamValue(desParam, 59, pPtyStr);
        }
        else if (dTitleStr == Joint_NUME_DTitle)
        {
            SetParamValue(desParam, 69, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJA_DTitle)
        {
            SetParamValue(desParam, 26, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJB_DTitle)
        {
            SetParamValue(desParam, 27, pPtyStr);
        }
        else if (dTitleStr == Joint_HVJC_DTitle)
        {
            SetParamValue(desParam, 28, pPtyStr);
        }
    }
}

void InitDefaultDESP()
{
    s_DefDesc[0] = "Description (Word)";
    s_DefDesc[1] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Entrance width");
    s_DefDesc[2] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Entrance depth");
    s_DefDesc[3] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Exit width");
    s_DefDesc[4] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Exit depth");
    s_DefDesc[5] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Length");
    s_DefDesc[6] = "Branch length";
    s_DefDesc[7] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Angle");
    s_DefDesc[8] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Inner diameter");
    s_DefDesc[9] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Horizontal offset");
    s_DefDesc[10] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Vertical offset");
    s_DefDesc[11] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Inlet piping");
    s_DefDesc[12] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Outlet piping");
    s_DefDesc[13] = "No. of segments";
    s_DefDesc[14] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Arrival extension");
    s_DefDesc[15] = "Arrive notch";
    s_DefDesc[16] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Departure extension");
    s_DefDesc[17] = "Leave notch";
    s_DefDesc[18] = "Branch extension";
    s_DefDesc[19] = "Branch notch";
    s_DefDesc[20] = "A of arrive joint";
    s_DefDesc[21] = "B of arrive joint";
    s_DefDesc[22] = "C of arrive joint";
    s_DefDesc[23] = "A of leave joint";
    s_DefDesc[24] = "B of leave joint";
    s_DefDesc[25] = "C of leave joint";
    s_DefDesc[26] = "A of branch joint";
    s_DefDesc[27] = "B of branch joint";
    s_DefDesc[28] = "C of branch joint";
    s_DefDesc[29] = "Face (RECT, CIRC, OVAL)";
    s_DefDesc[30] = "Item Number";
    s_DefDesc[31] = "Material";
    s_DefDesc[32] = "Gauge";
    s_DefDesc[33] = "Longitudinal seam (Word)";
    s_DefDesc[34] = "Stock number";
    s_DefDesc[35] = "Works fitted (TRUE, FALS)";
    s_DefDesc[36] = "Splitters";
    s_DefDesc[37] = "Sealant (Word)";
    s_DefDesc[38] = "Swage (TRUE, FALS)";
    s_DefDesc[39] = "Shape (RECT, CIRC, OVAL, TRAN)";
    s_DefDesc[40] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Branch pipe outlet width");
    s_DefDesc[41] = "Ductsize B of branch";
    s_DefDesc[42] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Secondary inner diameter");
    s_DefDesc[43] = "Airturns";
    s_DefDesc[44] = "Airturn size";
    s_DefDesc[45] = "Rectangular extension";
    s_DefDesc[46] = "Circular extension";
    s_DefDesc[47] = "Oval extension";
    s_DefDesc[48] = "Centreline height";
    s_DefDesc[49] = "Status";
    s_DefDesc[50] = "Manufacturer (Word)";
    s_DefDesc[51] = "General text (Word)";
    s_DefDesc[52] = "Note (Word)";
    s_DefDesc[53] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Secondary inlet piping");
    s_DefDesc[54] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Secondary outlet piping");
    s_DefDesc[55] = "Width Left";
    s_DefDesc[56] = "Bottom";
    s_DefDesc[57] = "Arrive joint (Word)";
    s_DefDesc[58] = "Leave joint (Word)";
    s_DefDesc[59] = "Branch joint (Word)";
    s_DefDesc[60] = "Radius C";
    s_DefDesc[61] = "Radius D";
    s_DefDesc[62] = "Angle B";
    s_DefDesc[63] = "Fixing joint (Word)";
    s_DefDesc[64] = "Fixing joint size";
    s_DefDesc[65] = "Item type (Word)";
    s_DefDesc[66] = "Item subtype (Word)";
    s_DefDesc[67] = "Arrive joint size";
    s_DefDesc[68] = "Leave joint size";
    s_DefDesc[69] = "Branch joint size";
    s_DefDesc[70] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Collision length");
    s_DefDesc[71] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Left-side collision length");
    s_DefDesc[72] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Right-side collision length");
    s_DefDesc[73] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Vertical upper collision length");
    s_DefDesc[74] = WD::WDTs("UiComDesignHvacComsCreateCommon", "Vertical lower collision length");
    s_DefDesc[75] = "Miscellaneous";
    s_DefDesc[76] = "Miscellaneous";
    s_DefDesc[77] = "No of holes in arrive flange";
    s_DefDesc[78] = "No of holes in leave flange";
    s_DefDesc[79] = "No of holes in branch flange";
    s_DefDesc[80] = "Internal surface area (m2)";
    s_DefDesc[81] = "External surface area (m2)";
    s_DefDesc[82] = "Total surface area (m2)";
    s_DefDesc[83] = "Mass density (kg/m3)";
    s_DefDesc[84] = "Sheet weight (kg)";
    s_DefDesc[85] = "Flanges weight (kg)";
    s_DefDesc[86] = "Total component weight (kg)";
    s_DefDesc[87] = "Surface Units";
    s_DefDesc[88] = "Insulation weight";
    s_DefDesc[89] = "Miscellaneous";
}

void DesignParamsToProp(const WD::StringVector& desParam, const std::vector<size_t>& indexs, WD::WDPropertyGroup& group)
{
    if (indexs.empty())
        return ;

    for (size_t i = 0; i < indexs.size(); ++i)
    {
        // 参数下标
        size_t  index   =   indexs.at(i);

        // 设计参数值
        auto valRes = GetParamValue<double>(desParam, index);
        if (valRes)
        {
            group.addPropertyDouble(s_DefDesc[index], valRes.value());
        }
        else
        {
            if (auto opt = GetParamValue<std::string>(desParam, index))
                group.addPropertyString(s_DefDesc[index], opt.value());
            else
                group.addPropertyString(s_DefDesc[index], "");
        }
    }
}
std::vector<size_t> GetDesParamsIndexs(const std::string& desiType)
{
    std::vector<size_t> result;

    // 入口宽度和深度
    result.push_back(1);
    result.push_back(2);
    if (desiType == "BEND")
    {
        // 出口宽度深度
        result.push_back(3);
        result.push_back(4);
        // 角度
        result.push_back(7);
        // 内径
        result.push_back(8);
        // 到达延展
        result.push_back(14);
        // 离开延展
        result.push_back(16);
    }
    else if(desiType == "AHU"
        || desiType == "BATT"
        || desiType == "COWL"
        || desiType == "FLEX"
        || desiType == "GRIL"
        || desiType == "HACC"
        || desiType == "HFAN"
        || desiType == "HSAD"
        || desiType == "IDAM"
        || desiType == "MESH"
        || desiType == "OFST"
        || desiType == "PLAT"
        || desiType == "PLEN"
        || desiType == "SILE"
        || desiType == "SKIR"
        || desiType == "SPLR"
        || desiType == "STRT")
    {
        // 出口宽度深度
        result.push_back(3);
        result.push_back(4);
        // 直管段长度
        result.push_back(5);
    }
    else if (desiType == "TAPE")
    {
        // 出口宽度深度
        result.push_back(3);
        result.push_back(4);
        // 直管段长度
        result.push_back(5);
        // 水平垂直偏移
        result.push_back(9);
        result.push_back(10);
        // 到达延展和离开延展
        result.push_back(14);
        result.push_back(16);
    }
    else if (desiType == "DAMP")
    {
        // 出口宽度深度
        result.push_back(3);
        result.push_back(4);
        // 直管段长度
        result.push_back(5);
        // 碰撞长度
        result.push_back(70);
        // 左侧碰撞长度
        result.push_back(71);
        // 右侧碰撞长度
        result.push_back(72);
        // 竖向上部碰撞长度
        result.push_back(73);
        // 竖向下部碰撞长度
        result.push_back(74);
    }
    else if (desiType == "THRE")
    {
        // 出口宽度深度
        result.push_back(3);
        result.push_back(4);
        // 支管出口宽度深度
        result.push_back(40);
        result.push_back(41);
        // 右边到达拐点
        result.push_back(11);
        // 右边离开拐点
        result.push_back(12);
        // 右边内径
        result.push_back(8);
        // 左边到达拐点
        result.push_back(53);
        // 左边离开拐点
        result.push_back(54);
        // 左边内径
        result.push_back(42);
    }
    else if (desiType == "TP"
        || desiType == "TRNS"
        || desiType == "BRCO")
    {
    }
    return result;
}

WD_NAMESPACE_BEGIN

WDNode::SharedPtr   ParentNode(WDBMDesign& mgr, WDNode::SharedPtr pNode, const char* type)
{
    return mgr.findParentWithType(*pNode, type);
}

WDNode::SharedPtr   GetBranPspec(WDNode::SharedPtr pNode)
{
    // 获取管件节点业务数据
    if (pNode == nullptr || !pNode->isType("BRAN"))
        return nullptr;
    return pNode->getAttribute("Pspec").toNodeRef().refNode();
}
void                SetBranPspec(WDNode::SharedPtr pNode, WDNode::SharedPtr pPspec)
{
    // 获取管件节点业务数据
    if (pNode == nullptr || !pNode->isType("BRAN"))
        return ;
    pNode->setAttribute("Pspec", WDBMNodeRef(pPspec));
}

DVec3               GetOtherDir(WDNode& com)
{
    DVec3 zDir = DVec3::AxisZ();

    auto pPt = com.keyPoint(6);
    if (pPt == nullptr)
        return zDir;

    return pPt->transformedDirection(com.globalRSTransform());
}
void                SetSTRAOtherDir(WDNode& com, const DVec3& dir)
{
    // 获取出入口点朝向
    auto    pArrive =   com.keyPoint(com.getAttribute("Arrive").toInt());
    auto    pLeave  =   com.keyPoint(com.getAttribute("Leave").toInt());
    if (pArrive == nullptr || pLeave == nullptr)
        return ;
    auto    hDir    =   pArrive->transformedDirection(com.globalRSTransform());
    auto    tDir    =   pLeave->transformedDirection(com.globalRSTransform());

    // 校验目标朝向与入口朝向是否共线
    if (DVec3::OnTheSameLine(hDir, dir, Epsilon))
        return ;

    // 其他朝向
    auto    otherDir=   GetOtherDir(com);
    // 校验其他朝向与入口朝向是否共线
    if (DVec3::OnTheSameLine(otherDir, hDir, Epsilon))
        return ;
        
    // 分别获取其他朝向与目标朝向投影到 以入口朝向为法线的平面 上的分量
    auto    tPlane  =   Plane(hDir, DVec3(0));
    auto    pprjSrc =   tPlane.project(otherDir);
    auto    pprjTar =   tPlane.project(dir);
    // 校验两分量是否同方向
    if (DVec3::InTheSameDirection(pprjSrc, pprjTar, Epsilon))
        return ;

    // 旋转
    auto    rotAxis =   DVec3::Cross(pprjSrc, pprjTar);
    auto    angle   =   DVec3::Angle(pprjSrc, pprjTar);
    com.rotate(rotAxis, angle);
    com.update();
}
void                SetBENDLeaveDir(WDNode& com, const DVec3& dir)
{
    // 获取出入口点朝向
    auto    pArrive = com.keyPoint(com.getAttribute("Arrive").toInt());
    auto    pLeave  = com.keyPoint(com.getAttribute("Leave").toInt());
    if (pArrive == nullptr || pLeave == nullptr)
        return ;
    auto    hDir    =   pArrive->transformedDirection(com.globalRSTransform());
    auto    tDir    =   pLeave->transformedDirection(com.globalRSTransform());
    
    // 校验目标朝向与入口朝向是否共线
    if (DVec3::OnTheSameLine(hDir, dir, Epsilon))
        return ;

    // 校验出口朝向与入口朝向是否共线
    if (DVec3::OnTheSameLine(tDir, hDir, Epsilon))
        return ;
        
    // 分别获取出口朝向与目标朝向投影到 以入口朝向为法线的平面 上的分量
    auto    tPlane  =   Plane(hDir, DVec3(0));
    auto    pprjSrc =   tPlane.project(tDir);
    auto    pprjTar =   tPlane.project(dir);
    // 校验两分量是否同方向
    if (DVec3::InTheSameDirection(pprjSrc, pprjTar, Epsilon))
        return ;

    // 旋转
    auto    rotAxis =   DVec3::Cross(pprjSrc, pprjTar);
    auto    angle   =   DVec3::Angle(pprjSrc, pprjTar);
    com.rotate(rotAxis, angle);
    com.update();
}
bool                RestoreConnForward(WD::WDNode::SharedPtr pCom, double spoolDistance)
{
    if (pCom == nullptr)
        return false;

    // 当前桥件所属的分支节点
    auto pBran = pCom->parent();
    if (pBran == nullptr)
        return false;

    // 上游流出方向和流出位置
    WD::DVec3   preLeaveDir;
    WD::DVec3   preLeavePos;
    auto        pPrev   =   WD::WDBMDPipeUtils::PrevPipeComponent(pBran, pCom);
    if (pPrev == nullptr)
    {
        preLeaveDir =   WD::GetBranHDir(*pBran, WD::WDWRTType::WRT_World);
        auto tarArriveDir = -preLeaveDir;
        preLeavePos =   WD::GetBranHPos(*pBran, WD::WDWRTType::WRT_World);

        // 桥件流入方向 旋转至 与上游流出方向一致
        auto arriveDir = WD::GetComsHDir(*pCom, WD::WDWRTType::WRT_World);
        if (WD::DVec3::InTheSameDirection(tarArriveDir, arriveDir))
        {
            // 同向则不处理
        }
        else if (WD::DVec3::InTheOppositeDirection(tarArriveDir, arriveDir))
        {
            // 反向则以 任意一个与出口点方向垂直的向量 为旋转轴 旋转180°
            WD::DVec3 rotAxis = WD::DVec3(tarArriveDir.y, tarArriveDir.x, 0.0);
            pCom->rotate(rotAxis, 180.0);
            pCom->update();
        }
        else
        {
            // 桥件流入方向 叉乘 上游流出方向反方向 得出 旋转轴
            auto    rotAxis =   WD::DVec3::Cross(arriveDir, tarArriveDir);
            // 将 桥件流入方向 以 旋转轴 旋转至 上游流出方向反方向
            auto    angle   =   WD::DVec3::Angle(arriveDir, tarArriveDir);
            pCom->rotate(rotAxis, angle);
            pCom->update();
        }

        // 调整其他朝向(分支内没有管件时默认E)
        auto otherDir = DVec3::AxisX();
        if (WDBMDPipeUtils::IsBendComponent(*pCom))
        {
            SetBENDLeaveDir(*pCom, otherDir);
        }
        else
        {
            SetSTRAOtherDir(*pCom, otherDir);
        }
    }
    else
    {
        preLeaveDir =   WD::GetComsTDir(*pPrev, WD::WDWRTType::WRT_World);
        auto tarArriveDir = -preLeaveDir;
        preLeavePos =   WD::GetComsTPos(*pPrev, WD::WDWRTType::WRT_World);

        // 桥件流入方向 旋转至 与上游流出方向一致
        auto arriveDir = WD::GetComsHDir(*pCom, WD::WDWRTType::WRT_World);
        if (WD::DVec3::InTheSameDirection(tarArriveDir, arriveDir))
        {
            // 同向则不处理
        }
        else if (WD::DVec3::InTheOppositeDirection(tarArriveDir, arriveDir))
        {
            // 反向则以 任意一个与出口点方向垂直的向量 为旋转轴 旋转180°
            WD::DVec3 rotAxis = WD::DVec3(tarArriveDir.y, tarArriveDir.x, 0.0);
            pCom->rotate(rotAxis, 180.0);
            pCom->update();
        }
        else
        {
            // 桥件流入方向 叉乘 上游流出方向反方向 得出 旋转轴
            auto    rotAxis =   WD::DVec3::Cross(arriveDir, tarArriveDir);
            // 将 桥件流入方向 以 旋转轴 旋转至 上游流出方向反方向
            auto    angle   =   WD::DVec3::Angle(arriveDir, tarArriveDir);
            pCom->rotate(rotAxis, angle);
            pCom->update();
        }

        // 调整其他朝向
        auto otherDir = GetOtherDir(*pPrev);
        if (WDBMDPipeUtils::IsBendComponent(*pCom))
        {
            SetBENDLeaveDir(*pCom, otherDir);
        }
        else
        {
            SetSTRAOtherDir(*pCom, otherDir);
        }
    }

    // 距离调整(顺上游流出方向)
    auto    dist    =   preLeaveDir * spoolDistance;
    auto    offHpos =   pCom->globalTranslation() - WD::GetComsHPos(*pCom, WD::WDWRTType::WRT_World);
    pCom->setAttribute("Position WRT World", preLeavePos + dist + offHpos);
    pCom->update();

    //更新分支连接
    return WD::WDBMDPipeUtils::UpdateConnectionCt(WD::Core(), pBran);
}
WD_NAMESPACE_END
