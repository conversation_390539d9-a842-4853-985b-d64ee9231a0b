#include "ComsPropDialog.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "Common.h"
#include "QtPropertyBrowser/qttreepropertybrowser.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/viewer/WDViewer.h"
#include "core/businessModule/WDBMColorTable.h"
#include "core/math/DirectionParser.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/undoRedo/WDUndoStack.h"


static constexpr const char*    DATA_DESP_Purpose  =   "DESP";

static std::string GetComDesParam(WD::WDCore& core, WD::WDNode::SharedPtr pSpco, int number)
{
    if (pSpco == nullptr || !pSpco->isType("SPCO"))
        return "0";
    auto    pScom       =   pSpco->getAttribute("Catref").toNodeRef().refNode();
    if (pScom == nullptr)
        return "0";
    // 获取引用DtRef
    auto    pDt         =   pScom->getAttribute("Dtref").toNodeRef().refNode();
    if (pDt == nullptr)
        return "0";

    auto aGet = core.getBMCatalog().modelBuilder().cAttributeGet(nullptr, pSpco);
    // 遍历DATA节点获取length
    for (size_t i = 0; i < pDt->childCount(); ++i)
    {
        auto    pChild  =   pDt->childAt(i);
        if (pChild == nullptr || !pChild->isType("DATA"))
            continue;
        auto purposeStr = pChild->getAttribute("Purpose").toWord();
        int tNumber     = pChild->getAttribute("Number").toInt();
        if (purposeStr == DATA_DESP_Purpose && tNumber == number + 1)
        {
            auto dPtyStr = aGet.getAttribute(*pChild, "Dproperty").convertToString();
            return dPtyStr;
        }
    }

    return "";
}

ComsPropDialog::ComsPropDialog(WD::WDCore& core, QDialog *parent)
    : QDialog(parent)
    , _core(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 连接点选择窗口
    _pJointDialog        =   new JointDialog(_core, this);

    // 属性窗口初始化
    _pPropertyWidget    =   new ObjectPropertyWidget(false, "ComsPropDialog");
    // 属性组
    _pGroup             =   WD::WDPropertyGroup::MakeShared();

    // 默认交换类型为入口交换
    _transType          =   TT_Arrive;
    _operationType      =   WD::OperationType::OT_Create;

    // 计算最小高度
    _minHeight          =   ui.verticalLayout->sizeHint().height();

    // 交换类型初始化
    this->initTransTypeCombo();

    // 窗口最小高度
    _minHeight = this->height();

    // 绑定事件通知响应
    connect(ui.comboBoxALType
        , QOverload<int>::of(&QComboBox::currentIndexChanged)
        , this
        , &ComsPropDialog::slotOnTransTypeChanged);
    connect(ui.pushButtonTrans, &QPushButton::clicked, this, &ComsPropDialog::slotOnTransClick);
    connect(ui.pushButtonPrev,  &QPushButton::clicked, this, &ComsPropDialog::slotOnPrevClick);
    connect(ui.pushButtonChoose,&QPushButton::clicked, this, &ComsPropDialog::slotOnChooseClick);
    connect(ui.pushButtonOk,    &QPushButton::clicked, this, &ComsPropDialog::slotOnOkClick);
    connect(ui.pushButtonCancel,&QPushButton::clicked, this, &ComsPropDialog::slotOnCancelClick);
    connect(_pJointDialog
        , &JointDialog::sigJointChoosed
        , this
        , &ComsPropDialog::slotOnJointChoosed);
}

ComsPropDialog::~ComsPropDialog()
{
    ui.verticalLayout_2->removeWidget(_pPropertyWidget->widget());
	_pPropertyWidget->deleteLater();
}

void ComsPropDialog::retranslateUi(const std::string& ctxStr)
{
    Trs(ctxStr.c_str()
        , static_cast<QDialog*>(this)
        , ui.comboBoxALType
        , ui.pushButtonTrans
        , ui.labelArrive
        , ui.labelLeave
        , ui.labelBranch
        , ui.pushButtonPrev
        , ui.pushButtonChoose
        , ui.labelDir
        , ui.pushButtonOk
        , ui.pushButtonCancel
    );

    // 子界面翻译
    _pJointDialog->retranslateUi(ctxStr);
}

bool ComsPropDialog::updateWidget(WD::WDNode::SharedPtr pSpco)
{
    // 区分操作类型
    if (pSpco == nullptr)
        _operationType = WD::OT_Modify;
    else
        _operationType = WD::OT_Create;

    if (_pGroup == nullptr)
        return false;
    // 清除属性组
    _pGroup->clear();

    // 新建元件置空
    _pCom = nullptr;

    // 获取当前选中节点所属分支
    auto pBran = WD::ParentNode(_core.getBMDesign(), _core.nodeTree().currentNode(), "TUBI");
    if (pBran == nullptr)
    {
        WD_ERROR_T("ErrorComsPropDialog", "Fail to find suitable branch node!");
        return false;
    }
    _pBran = pBran;

    // 元件类型
    std::string dType = "";

    // 设置方向默认值为最后一个元件的其他朝向
    auto pComs = WD::WDBMDPipeUtils::PipeComponents(pBran);
    if (pComs.empty())
    {
        ui.lineEditDir->setText(QString("E"));
    }
    else
    {
        const auto& pLast = pComs.back();
        if (pLast != nullptr)
        {
            auto dirStr = WD::DirectionParserENU::OutputStringByDirection(WD::GetOtherDir(*pLast));
            ui.lineEditDir->setText(QString::fromUtf8(dirStr.c_str()));
        }
    }
    
    auto& mgr = _core.getBMDesign();
    // 编辑时不传入等级节点
    if (pSpco == nullptr)
    {
        // 当前选中元件设计参数
        auto    pCur        =   _core.nodeTree().currentNode();
        if (pCur == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCur))
            return false;
        _pCom = pCur;
        
        // 设计参数属性对应下标
        _indexs         =   GetDesParamsIndexs(std::string(_pCom->type()));
        // 设计参数属性组
        _pGroup->clear();
        DesignParamsToProp(pCur->getAttribute("Desparam").toStringVector(), _indexs, *_pGroup);

        // 连接点
        auto    arriveShape =   JointShapeFromName(GetArriveShapeStr(*pCur).c_str());
        auto    leaveShape  =   JointShapeFromName(GetLeaveShapeStr(*pCur).c_str());
        auto    thirdShape  =   JointShapeFromName(GetThirdShapeStr(*pCur).c_str());
        auto    pArrJ       =   ExtractArriveJoint(_core, *pCur);
        auto    pLeaJ       =   ExtractLeaveJoint(_core, *pCur);
        auto    pThiJ       =   ExtractThirdJoint(_core, *pCur);
        if (pArrJ == nullptr)
            pArrJ = GetSpecJoint(HvacCata(), arriveShape);
        if (pLeaJ == nullptr)
            pLeaJ = GetSpecJoint(HvacCata(), leaveShape);
        if (pThiJ == nullptr)
            pThiJ = GetSpecJoint(HvacCata(), thirdShape);
        if (pArrJ == nullptr || pLeaJ == nullptr || pThiJ == nullptr)
            return false;
        _pArrJ = pArrJ;
        _pLeaJ = pLeaJ;
        _pThiJ = pThiJ;
    }
    else
    {
        // 从spco等级节点向上查找元件类型
        auto    pType       =   pSpco;
        std::string type    =   "";
        while (pType)
        {
            auto pParent = pType->parent();
            if (pParent == nullptr)
                break;
            if (pParent->isType("SPEC"))
            {
                bool bOk = false;
                auto typeStr = WD::GetAnswerStr(*pType, true, std::nullopt, &bOk);
                if (bOk)
                {
                    type = typeStr;
                    break;
                }
            }
            pType = pParent;
        }

        // 创建元件
        _pCom = this->createComs(pBran, type, pSpco);
        if (_pCom == nullptr)
            return false;
        
        // 设置节点默认颜色
        mgr.colorTable().setNodeColor(*_pCom);
        _pCom->update();

        // 元件属性界面
        if (!WD::WDBMDPipeUtils::IsPipeComponent(*_pCom))
            return false;
        // 设计参数属性对应下标
        _indexs = GetDesParamsIndexs(type);

        // 准备设计参数属性组
        DesignParamsToProp(_pCom->getAttribute("Desparam").toStringVector(), _indexs, *_pGroup);

        // 连接点
        auto    arriveShape =   JointShapeFromName(GetArriveShapeStr(*_pCom).c_str());
        auto    leaveShape  =   JointShapeFromName(GetLeaveShapeStr(*_pCom).c_str());
        auto    thirdShape  =   JointShapeFromName(GetThirdShapeStr(*_pCom).c_str());
        auto    pArrJ       =   ExtractArriveJoint(_core, *_pCom);
        auto    pLeaJ       =   ExtractLeaveJoint(_core, *_pCom);
        auto    pThiJ       =   ExtractThirdJoint(_core, *_pCom);
        if (pArrJ == nullptr)
            pArrJ = GetSpecJoint(HvacCata(), arriveShape);
        if (pLeaJ == nullptr)
            pLeaJ = GetSpecJoint(HvacCata(), leaveShape);
        if (pThiJ == nullptr)
            pThiJ = GetSpecJoint(HvacCata(), thirdShape);
        if (pArrJ == nullptr || pLeaJ == nullptr || pThiJ == nullptr)
            return false;
        _pArrJ = pArrJ;
        _pLeaJ = pLeaJ;
        _pThiJ = pThiJ;
        dType           = _pCom->type();
    }

    if (_pGroup->propertys().size() != _indexs.size())
        return false;

    // 更新属性窗口
    this->updatePropWidget();
    // 设置出入口点文本
    ui.lineEditArrive->setText(QString::fromUtf8(GetJointDisp(_core, *_pArrJ.lock()).c_str()));
    ui.lineEditLeave->setText(QString::fromUtf8(GetJointDisp(_core, *_pLeaJ.lock()).c_str()));
    ui.lineEditBranch->setText(QString::fromUtf8(GetJointDisp(_core, *_pThiJ.lock()).c_str()));
    return true;
}

void ComsPropDialog::showEvent(QShowEvent* evt)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    WDUnused(evt);
}

void ComsPropDialog::hideEvent(QHideEvent* evt)
{
    WDUnused(evt);
    emit sigClose();
}

void ComsPropDialog::slotOnTransTypeChanged(int index)
{
    WDUnused(index);
    auto userData = ui.comboBoxALType->currentData();
    if (!userData.isValid())
        return ;

    _transType = (TransType)userData.value<int>();
}

void ComsPropDialog::slotOnTransClick()
{
    if (_pGroup == nullptr)
        return ;

    WD::WDProperty::SharedPtr pLeftPty    =   nullptr;
    WD::WDProperty::SharedPtr pRightPty   =   nullptr;
    auto&   pPtys   =   _pGroup->propertys();
    auto    pItrPty =   pPtys.begin();
    auto    pItrIdx =   _indexs.begin();
    switch (_transType)
    {
    case ComsPropDialog::TT_Arrive:
        {
            for (pItrIdx; pItrIdx != _indexs.end(); )
            {
                if (*pItrIdx == 1)
                {
                    pLeftPty = *pItrPty;
                }
                else if (*pItrIdx == 2)
                {
                    pRightPty = *pItrPty;
                    break;
                }
                pItrIdx ++ ;
                pItrPty ++ ;
            }
        }
        break;
    case ComsPropDialog::TT_Leave:
        {
            for (pItrIdx; pItrIdx != _indexs.end(); )
            {
                if (*pItrIdx == 3)
                {
                    pLeftPty = *pItrPty;
                }
                else if (*pItrIdx == 4)
                {
                    pRightPty = *pItrPty;
                    break;
                }
                pItrIdx ++ ;
                pItrPty ++ ;
            }
        }
        break;
    case ComsPropDialog::TT_Branch:
        {
            for (pItrIdx; pItrIdx != _indexs.end(); )
            {
                if (*pItrIdx == 40)
                {
                    pLeftPty = *pItrPty;
                }
                else if (*pItrIdx == 41)
                {
                    pRightPty = *pItrPty;
                    break;
                }
                pItrIdx ++ ;
                pItrPty ++ ;
            }
        }
        break;
    default:
        break;
    }

    // 交换属性值，并更新界面
    if (pLeftPty != nullptr && pRightPty != nullptr)
    {
        // 值交换
        auto leftValue = pLeftPty->valueToString();
        pLeftPty->valueFromString(pRightPty->valueToString());
        pRightPty->valueFromString(leftValue);

        // 界面更新
        WD::WDPropertyGroup tGroup = *_pGroup;
        excludeNotDispParam(tGroup, std::string(_pCom->type()));
	    _pPropertyWidget->update(tGroup);
        _core.needRepaint();
    }
}

void ComsPropDialog::slotOnPrevClick()
{
    // 获取当前选中分支
    auto    pCur    =   _core.nodeTree().currentNode();
    auto    pBran   =   WD::ParentNode(_core.getBMDesign(), pCur, "STRT");

    // 获取前一个元件
    WD::WDNode::SharedPtr pPrev = nullptr;
    switch (_operationType)
    {
    case WD::OT_Create:
        {
            auto pComs = WD::WDBMDPipeUtils::PipeComponents(pBran);
            if (!pComs.empty())
            {
                pPrev = pComs.back();
            }
        }
        break;
    case WD::OT_Modify:
        {
            pPrev = WD::WDBMDPipeUtils::PrevPipeComponent(pBran, pCur);
        }
        break;
    default:
        break;
    }

    // 入口点更改
    if (pPrev == nullptr)
    {
        _pArrJ = GetSpecJoint(HvacCata(), JS_Rectangular);
    }
    else
    {
        // 获取前一个元件设计参数
        _pArrJ = ExtractLeaveJoint(_core, *pPrev);
    }
    if (_pArrJ.lock() != nullptr)
    {
        ui.lineEditArrive->setText(QString::fromUtf8(GetJointDisp(_core, *_pArrJ.lock()).c_str()));
    }
    else
    {
        WD_ERROR_T("ErrorComsPropDialog", "Fail to find arrive joint!");
    }
    _core.needRepaint();
}

void ComsPropDialog::slotOnChooseClick()
{
    _pJointDialog->show();
}

void ComsPropDialog::slotOnOkClick()
{
    if (_pGroup == nullptr)
        return ;
    auto pArrJ = _pArrJ.lock();
    auto pLeaJ = _pLeaJ.lock();
    auto pThiJ = _pThiJ.lock();
    if (pArrJ == nullptr || pLeaJ == nullptr)
        return ;

    // 属性窗口更新到属性组
    _pPropertyWidget->applyChanges();

    // 更新连接点参数
    UpdateARRJointParam(_core, *pArrJ, *_pGroup, _indexs);
    UpdateLEAJointParam(_core, *pLeaJ, *_pGroup, _indexs);
    UpdateThiJointParam(_core, *pThiJ, *_pGroup, _indexs);
    
    if (_pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*_pCom))
        return ;
    // 获取元件设计参数
    auto    desParam=   _pCom->getAttribute("Desparam").toStringVector();

    // 更新设计参数
    auto&   pPtys   =   _pGroup->propertys();
    if (_indexs.size() != pPtys.size())
        return ;
    // 入口宽度值字符串
    std::string hWidthStr;
    std::string hHeightStr;
    auto        pItrIdx =   _indexs.begin();
    auto        pItrPty =   pPtys.begin();
    for (pItrIdx; pItrIdx != _indexs.end(); )
    {
        auto    index   =   *pItrIdx++;
        auto&   pPty    =   *pItrPty++;
        if (pPty == nullptr)
            continue;

        // 这几种设计参数的值如果小于50，则提示此处最小值为50，并修改为90
        if (index == 7 || index == 8 || index == 14 || index == 16)
        {
            auto pTPty = pPty->toPtr<WD::WDPropertyFloat>();
            if (pTPty != nullptr)
            {
                if (pTPty->getValue() < 50.0f)
                {
                    std::string info = WD::WDTs("ErrorComsPropDialog", pTPty->name())
                        + WD::WDTs("ErrorComsPropDialog", "Value is unexpected! at least greater than 50");
                    WD_WARN(info);
                    pTPty->setValue(90.0f);
                    
                    // 更新属性界面
                    this->updatePropWidget();
                    return ;
                }
            }
        }

        // TODO: 类型校验
        SetParamValue(desParam, index, pPty->valueToString());

        // 入口宽度深度字符串
        if (index == 1)
        {
            hWidthStr = pPty->valueToString();
        }
        if (index == 2)
        {
            hHeightStr = pPty->valueToString();
        }
    }
    // 出口及分支口宽度深度
    if(_pCom->isAnyOfType("GASK"
        , "FLAN"
        , "VALV"
        , "ELBO"
        , "ATTA"
        , "INST"
        , "WELD"
        , "BEND"
        , "CAP"
        , "CLOS"
        , "COUP"
        , "CROS"
        , "FTUB"
        , "OLET"
        , "PCOM"
        , "DUCT"
        , "LJSE"
        , "FILT"
        , "TRAP"
        , "UNIO"
        , "FBLI"
        , "SHU"
        , "VENT"
        , "HELE"
        , "INSU"
        , "TRAC"
        , "TUBE"
        , "AHU"
        , "BATT"
        , "COWL"
        , "DAMP"
        , "FLEX"
        , "GRIL"
        , "HACC"
        , "HFAN"
        , "HSAD"
        , "IDAM"
        , "MESH"
        , "OFST"
        , "PLAT"
        , "PLEN"
        , "SILE"
        , "SKIR"
        , "SPLR"
        , "STRT"
        , "TP"
        , "BRCO"))
    {
        SetParamValue(desParam, 3, hWidthStr);
        SetParamValue(desParam, 4, hHeightStr);
    }
    else if (_pCom->isType("TAPE"))
    {

    }
    else if (_pCom->isType("REDU"))
    {
    }
    else if (_pCom->isType("TEE"))
    {
    }
    else if (_pCom->isType("VFWA"))
    {
    }
    else if (_pCom->isType("VTWA"))
    {
    }
    else if (_pCom->isType("THRE"))
    {
        SetParamValue(desParam, 4, hHeightStr);
        SetParamValue(desParam, 41, hHeightStr);
    }
    else if (_pCom->isType("TRNS"))
    {

    }
    // 更新元件的设计参数
    _pCom->setAttribute("Desparam", WD::WDBMAttrValue(desParam));

    switch (_operationType)
    {
    case WD::OT_Create:
        {
            // 新建元件加入到父属分支
            auto pBran = _pBran.lock();
            if (pBran != nullptr)
            {
                // 设置元件的父节点
                pBran->addChild(_pCom);
                // 设置元件的默认颜色
                _core.getBMDesign().colorTable().setNodeColor(*_pCom);

                auto funcRedo = [this, pBran]()
                {                     
                    // 选中新创建的管件
                    _core.nodeTree().setCurrentNode(_pCom);
                    pBran->triggerUpdate();
                    // 更新元件连接
                    WD::RestoreConnForward(_pCom, 0.0);
                    // 更新元件朝向
                    this->updateComDirection();
                    _core.needRepaint(); 
                };

                funcRedo();

                _core.undoStack().beginMarco("");

                auto pCmdCreatedNode = WD::WDBMBase::MakeCreatedCommand({ _pCom});
                if(pCmdCreatedNode != nullptr)
                {
                    pCmdCreatedNode->setNoticeAfterRedo([funcRedo](const WD::WDUndoCommand&)
                    {
                        funcRedo();
                    });
                    pCmdCreatedNode->setNoticeAfterUndo([this](const WD::WDUndoCommand&)
                    {
                        _core.needRepaint();
                    });

                    _core.undoStack().push(pCmdCreatedNode);
                }
                auto pCmdAdd2Scene = WD::WDBMBase::MakeSceneAddCommand({ _pCom });
                if(pCmdAdd2Scene != nullptr)
                {
                    _core.undoStack().push(pCmdAdd2Scene);
                }
                _core.undoStack().endMarco();
                
            }
            this->accept();
        }
        break;
    case WD::OT_Modify:
        {
            // 更新管件业务数据
            _pCom->triggerUpdate(true);
            // 更新元件朝向
            this->updateComDirection();
        }
        break;
    default:
        break;
    }
    _core.needRepaint();
}

void ComsPropDialog::slotOnCancelClick()
{
    this->reject();
}

void ComsPropDialog::slotOnJointChoosed(JointType type)
{
    auto pJoint = _pJointDialog->joint();
    if (pJoint == nullptr)
    {
        WD_ERROR_T("ErrorComsPropDialog", "Fail to find arrive joint!");
        return ;
    }
    switch (type)
    {
    case JT_Arrive:
        {
            _pArrJ = pJoint;
            ui.lineEditArrive->setText(QString::fromUtf8(GetJointDisp(_core, *pJoint).c_str()));
    }
        break;
    case JT_Leave:
    {
        _pLeaJ = pJoint;
        ui.lineEditLeave->setText(QString::fromUtf8(GetJointDisp(_core, *pJoint).c_str()));
    }
    break;
    case JT_Third:
    {
        _pThiJ = pJoint;
        ui.lineEditBranch->setText(QString::fromUtf8(GetJointDisp(_core, *pJoint).c_str()));
        }
        break;
    default:
        break;
    }
    _core.needRepaint();
}

void ComsPropDialog::initTransTypeCombo()
{
    ui.comboBoxALType->addItem("Arrive", TT_Arrive);
    ui.comboBoxALType->addItem("Leave", TT_Leave);
    ui.comboBoxALType->addItem("Branch", TT_Branch);

    // 默认设置第一个
    ui.comboBoxALType->setCurrentIndex(0);
}

size_t ComsPropDialog::excludeNotDispParam(WD::WDPropertyGroup& group, const std::string& dType)
{
    auto&   pPtys   =   group.propertys();
    if (pPtys.size() != _indexs.size())
        return 0;
    
    // 不显示分支口
    ui.labelBranch->setVisible(false);
    ui.lineEditBranch->setVisible(false);

    WD::WDPropertyGroup::Propertys  rPtys;
    auto    pItrPty =   pPtys.begin();
    auto    pItrIdx =   _indexs.begin();
    for (pItrIdx; pItrIdx != _indexs.end(); )
    {
        if (*pItrIdx == 20
            || *pItrIdx == 21
            || *pItrIdx == 22
            || *pItrIdx == 57
            || *pItrIdx == 67
            || *pItrIdx == 23
            || *pItrIdx == 24
            || *pItrIdx == 25
            || *pItrIdx == 58
            || *pItrIdx == 68
            || *pItrIdx == 26
            || *pItrIdx == 27
            || *pItrIdx == 28
            || *pItrIdx == 59
            || *pItrIdx == 69)
        {
            rPtys.push_back(*pItrPty);
        }

        // 根据不同的类型排除不同的属性
        if (dType == "GASK"
            || dType == "FLAN"
            || dType == "VALV"
            || dType == "ELBO"
            || dType == "ATTA"
            || dType == "INST"
            || dType == "WELD"
            || dType == "BEND"
            || dType == "CAP"
            || dType == "CLOS"
            || dType == "COUP"
            || dType == "CROS"
            || dType == "FTUB"
            || dType == "OLET"
            || dType == "PCOM"
            || dType == "DUCT"
            || dType == "LJSE"
            || dType == "FILT"
            || dType == "TRAP"
            || dType == "UNIO"
            || dType == "FBLI"
            || dType == "SHU"
            || dType == "VENT"
            || dType == "HELE"
            || dType == "INSU"
            || dType == "TRAC"
            || dType == "TUBE"
            || dType == "AHU"
            || dType == "BATT"
            || dType == "COWL"
            || dType == "DAMP"
            || dType == "FLEX"
            || dType == "GRIL"
            || dType == "HACC"
            || dType == "HFAN"
            || dType == "HSAD"
            || dType == "IDAM"
            || dType == "MESH"
            || dType == "OFST"
            || dType == "PLAT"
            || dType == "PLEN"
            || dType == "SILE"
            || dType == "SKIR"
            || dType == "SPLR"
            || dType == "STRT"
            || dType == "TP"
            || dType == "BRCO")
        {
            if (*pItrIdx == 3 || *pItrIdx == 4)
            {
                rPtys.push_back(*pItrPty);
            }
        }
        else if (dType == "TAPE")
        {
        }
        else if (dType == "REDU")
        {
        }
        else if (dType == "TEE")
        {
        }
        else if (dType == "VFWA")
        {
        }
        else if (dType == "VTWA")
        {
        }
        else if (dType == "THRE")
        {
            if (*pItrIdx == 4 || *pItrIdx == 41)
            {
                rPtys.push_back(*pItrPty);
            }

            // 显示分支口
            ui.labelBranch->setVisible(true);
            ui.lineEditBranch->setVisible(true);
        }
        else if (dType == "TRNS")
        {
            break;
        }
        else
        {
        }

        pItrIdx++;
        pItrPty++;
    }
    for (const auto& rPty : rPtys)
    {
        group.removeProperty(rPty);
    }

    return rPtys.size();
}

void ComsPropDialog::prepareComsData(WD::WDNode& bran, WD::WDNode& com)
{
    // 设计参数
    auto    desParam=   com.getAttribute("Desparam").toStringVector();
    // 默认初始化90个
    SetParamValue(desParam, 90, std::string(""));
    for (size_t i = 0; i < 90; ++i)
    {
        SetParamValue(desParam, i, std::string("0"));
    }

    // 获取最后一个元件
    WD::WDNode::SharedPtr   pLast   =   nullptr;
    auto                    pComs   =   WD::WDBMDPipeUtils::PipeComponents(WD::WDNode::ToShared(&bran));
    if (!pComs.empty())
    {
        pLast = pComs.back();
    }
    // 获取元件上游宽度和高度
    float   hWidth  =   0.0;
    float   hHeight =   0.0;
    if (pLast == nullptr)
    {
        // 分支宽度高度
        if (bran.isType("BRAN"))
        {
            hWidth  =   bran.getAttribute("HeadWidth").toDouble();
            hHeight =   bran.getAttribute("HeadHeight").toDouble();
        }
    }
    else
    {
        // 上游元件宽度和高度
        if (WD::WDBMDPipeUtils::IsPipeComponent(*pLast))
        {
            // 元件设计参数: 元件出口宽度和高度保存在index为4和5的设计参数中
            auto    preDesParam =   pLast->getAttribute("Desparam").toStringVector();
            auto    param4Res   =   GetParamValue<float>(preDesParam, 3);
            if (param4Res)
            {
                hWidth = param4Res.value();
            }
            auto    param5Res   =   GetParamValue<float>(preDesParam, 4);
            if (param5Res)
            {
                hHeight = param5Res.value();
            }
        }
    }
    SetParamValue(desParam, 1, hWidth);
    SetParamValue(desParam, 2, hHeight);

    // 连接点参数
    auto    arrShape    =   JointShapeFromName(GetArriveShapeStr(com).c_str());
    auto    leaShape    =   JointShapeFromName(GetLeaveShapeStr(com).c_str());
    auto    pARRJ       =   GetSpecJoint(HvacCata(), arrShape);
    auto    pLEAJ       =   GetSpecJoint(HvacCata(), leaShape);
    if (pARRJ != nullptr && pLEAJ != nullptr)
    {
        UpdateARRJointParam(_core, *pARRJ, desParam);
        UpdateLEAJointParam(_core, *pLEAJ, desParam);
    }
    auto    thiShape    =   JointShapeFromName(GetThirdShapeStr(com).c_str());
    auto    pThiJ       =   GetSpecJoint(HvacCata(), thiShape);
    if (pThiJ != nullptr)
    {
        UpdateThiJointParam(_core, *pThiJ, desParam);
    }

    auto pSprefNode = com.getAttribute("Spref").toNodeRef().refNode();
    // 元件参数
    if (com.isType("BEND"))
    {
        // 出口宽度深度
        float   tWidth  =   hWidth;
        float   tHeight =   hHeight;
        SetParamValue(desParam, 3, tWidth);
        SetParamValue(desParam, 4, tHeight);
        // 角度
        auto    angle   =   GetComDesParam(_core, pSprefNode, 7);
        SetParamValue(desParam, 7, angle);
        // 内径
        auto    insiRadi=   GetComDesParam(_core, pSprefNode, 8);
        SetParamValue(desParam, 8, insiRadi);
        // 到达延展
        auto    arriExte=   GetComDesParam(_core, pSprefNode, 14);
        SetParamValue(desParam, 14, arriExte);
        // 离开延展
        auto    leavExte=   GetComDesParam(_core, pSprefNode, 16);
        SetParamValue(desParam, 16, leavExte);
    }
    else if (com.isAnyOfType(
          "AHU"
        , "BATT"
        , "COWL"
        , "FLEX"
        , "GRIL"
        , "HACC"
        , "HFAN"
        , "HSAD"
        , "IDAM"
        , "MESH"
        , "OFST"
        , "PLAT"
        , "PLEN"
        , "SILE"
        , "SKIR"
        , "SPLR"
        , "STRT"))
    {
        // 出口宽度深度
        float   tWidth  =   hWidth;
        float   tHeight =   hHeight;
        SetParamValue(desParam, 3, tWidth);
        SetParamValue(desParam, 4, tHeight);
        // 直管段长度
        auto    length = GetComDesParam(_core, pSprefNode, 5);
        SetParamValue(desParam, 5, length);
    }
    else if (com.isType("TAPE"))
    {
        // 出口宽度深度
        SetParamValue(desParam, 3, GetComDesParam(_core, pSprefNode, 3));
        SetParamValue(desParam, 4, GetComDesParam(_core, pSprefNode, 4));
        // 直管段长度
        SetParamValue(desParam, 5, GetComDesParam(_core, pSprefNode, 5));
        // 水平垂直偏移
        SetParamValue(desParam, 9, GetComDesParam(_core, pSprefNode, 9));
        SetParamValue(desParam, 10, GetComDesParam(_core, pSprefNode, 10));
        // 到达延展和离开延展
        SetParamValue(desParam, 14, GetComDesParam(_core, pSprefNode, 14));
        SetParamValue(desParam, 16, GetComDesParam(_core, pSprefNode, 16));
    }
    else if (com.isType("DAMP"))
    {
        // 出口宽度深度
        SetParamValue(desParam, 3, hWidth);
        SetParamValue(desParam, 4, hHeight);
        // 直管段长度
        SetParamValue(desParam, 5, GetComDesParam(_core, pSprefNode, 5));
        // 碰撞长度
        SetParamValue(desParam, 70, GetComDesParam(_core, pSprefNode, 70));
        // 左侧碰撞长度
        SetParamValue(desParam, 71, GetComDesParam(_core, pSprefNode, 71));
        // 右侧碰撞长度
        SetParamValue(desParam, 72, GetComDesParam(_core, pSprefNode, 72));
        // 竖向上部碰撞长度
        SetParamValue(desParam, 73, GetComDesParam(_core, pSprefNode, 73));
        // 竖向下部碰撞长度
        SetParamValue(desParam, 74, GetComDesParam(_core, pSprefNode, 74));
    }
    else if (com.isType("THRE"))
    {
        // 出口宽度深度
        SetParamValue(desParam, 3, GetComDesParam(_core, pSprefNode, 3));
        SetParamValue(desParam, 4, hHeight);
        // 支管出口宽度深度
        SetParamValue(desParam, 40, GetComDesParam(_core, pSprefNode, 40));
        SetParamValue(desParam, 41, GetComDesParam(_core, pSprefNode, 41));
        // 右边到达拐点
        SetParamValue(desParam, 11, GetComDesParam(_core, pSprefNode, 11));
        // 右边离开拐点
        SetParamValue(desParam, 12, GetComDesParam(_core, pSprefNode, 12));
        // 右边内径
        SetParamValue(desParam, 8, GetComDesParam(_core, pSprefNode, 8));
        // 左边到达拐点
        SetParamValue(desParam, 53, GetComDesParam(_core, pSprefNode, 53));
        // 左边离开拐点
        SetParamValue(desParam, 54, GetComDesParam(_core, pSprefNode, 54));
        // 左边内径
        SetParamValue(desParam, 42, GetComDesParam(_core, pSprefNode, 42));
    }
    else if (com.isAnyOfType("TP", "TRNS", "BRCO"))
    {
    }
    // 面向截面
    //desParam.setValue(30, GetComDesParam(pSprefNode, 30));
    // 形状
    //desParam.setValue(40, GetComDesParam(pSprefNode, 40));

    // 更新元件的设计参数
    com.setAttribute("Desparam", WD::WDBMAttrValue(desParam));
}

WD::WDNode::SharedPtr ComsPropDialog::createComs(WD::WDNode::SharedPtr pBran
                                               , const std::string& type
                                               , WD::WDNode::SharedPtr pSpco)
{
    // 校验COCO连接并创建管件节点
    auto pCom = _core.getBMDesign().create(type);
    if (pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCom))
        return nullptr;

    pCom->setAttribute("Oriflag", true);
    pCom->setAttribute("Posflag", true);

    // 设置管件元件专业等级引用
    pCom->setAttribute("Spref", WD::WDBMNodeRef(pSpco));
    // 准备元件数据
    this->prepareComsData(*pBran, *pCom);

    return pCom;
}

void ComsPropDialog::updatePropWidget()
{
    // 更新属性窗口
    auto pWidget = _pPropertyWidget->widget();
	ui.verticalLayout_2->removeWidget(pWidget);
    WD::WDPropertyGroup tGroup = *_pGroup;
    this->excludeNotDispParam(tGroup, std::string(_pCom->type()));
	_pPropertyWidget->update(tGroup);
	ui.verticalLayout_2->addWidget(pWidget);
    if (pWidget != nullptr)
    {
        auto parHeight = _pPropertyWidget->widgetHeight();
        pWidget->setMinimumHeight(parHeight);
        auto tMin = _minHeight;
        if (!ui.labelBranch->isVisible() && !ui.lineEditBranch->isVisible())
        {
            // 分支口不显示
            tMin -= 25;
        }
        this->setMinimumHeight(tMin + parHeight);
    }
}

void ComsPropDialog::updateComDirection()
{
    if (_pCom == nullptr)
        return ;

    // 获取朝向
    std::string dirStr  =   ui.lineEditDir->text().toUtf8().data();
    WD::DVec3   tDir;
    if (!WD::DDirectionParserXYZ::Direction(dirStr, tDir))
        if (!WD::DirectionParserENU::Direction(dirStr, tDir))
            return ;
    
    // 调整朝向
    if (WD::WDBMDPipeUtils::IsBendComponent(*_pCom))
    {
        WD::SetBENDLeaveDir(*_pCom, tDir);
    }
    else
    {
        // 参考PDMS得知直通型管件不修改朝向
        //WD::SetSTRAOtherDir(*_pCom, tDir);
    }
}