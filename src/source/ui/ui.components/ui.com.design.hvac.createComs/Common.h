#pragma once
#include <optional>
#include "core/WDCore.h"
#include "node/WDNode.h"
#include "core/businessModule/design/WDBMDesign.h"
#include <unordered_set>
#include "core/common/WDStringConvert.h"
#include "../../ui.commonLibrary/ui.commonLib.property/property/WDProperties.h"

static constexpr const char*    Joint_Spec  =   "Spec";

/**
 * @brief 目录类型
*/
enum CatelogType
{
    // 矩形
    CT_Rectangular = 0,
    // 圆
    CT_Circular,
    // 椭圆
    CT_FlatOval,
    // 转换
    CT_Transformations,
    CT_BranchConnectors,
    CT_InlinePlantEquipment,
    CT_ExtraInlineEquipment,
    CT_EquipmentNozzles,
    CT_UserDefinedFittings,
    CT_Assemblies
};
/**
 * @brief 连接点类型
*/
enum JointShape
{
    // 矩形
    JS_Rectangular = 0,
    // 圆
    JS_Circular,
    // 椭圆
    JS_FlatOval,
};
/**
* @brief 桥架分支连接策略类型类型转换为字符串名称
*/
constexpr const char*   JointShapeToName(JointShape type)
{
    switch (type)
    {
    case JS_Rectangular:    return "HJRM";
    case JS_Circular:       return "HJCM";
    case JS_FlatOval:       return "HJFM";
    default:
        break;
    }
    return "";
}
/**
* @brief 字符串名称转换到桥架分支连接策略类型
*/
JointShape              JointShapeFromName(const char* name);
std::string             ConnNameToShapeName(const std::string& name);

/**
    * @brief 连接点类型
*/
enum JointType
{
    // 入口点
    JT_Arrive = 0,
    // 出口点
    JT_Leave,
    // 第三口点
    JT_Third,
};

/**
 * @brief 形状对应的名称集合
 * @param type 形状类型
*/
std::set<std::string>   ShapeNameSet(CatelogType type);
/**
 * @brief 收集指定形状的spco等级节点
 * @param type 形状类型
*/
WD::WDNode::Nodes       CollectByShape(WD::WDNode::SharedPtr pSpec, CatelogType type);
WD::WDNode::SharedPtr   HvacCata();
std::string             GetArriveShapeStr(WD::WDNode& com);
std::string             GetLeaveShapeStr(WD::WDNode& com);
std::string             GetThirdShapeStr(WD::WDNode& com);
template <typename T>
std::optional<T>        GetParamValue(const WD::StringVector& params, size_t index)
{
    if (index >= params.size())
        return std::nullopt;

    std::string str = params.at(index);

    bool    bOk     =   false;
    auto    tValue  =   WD::FromString<T>(str, &bOk);
    if (!bOk)
        return std::nullopt;
    return tValue;
}

template <typename T>
void                    SetParamValue(WD::StringVector& params, size_t index, const T& value)
{
    std::string tValue = WD::ToString(value);
    
    if (index >= params.size())
    {
        params.resize(index);

        if (index < params.size())
            params[index] = tValue;
    }
    else
    {
        if (index < params.size())
            params[index] = tValue;
    }
}
/**
    * @brief 提取入口点节点
*/
WD::WDNode::SharedPtr   ExtractArriveJoint(WD::WDCore& core, WD::WDNode& com);
/**
    * @brief 提取出口点节点
*/
WD::WDNode::SharedPtr   ExtractLeaveJoint(WD::WDCore& core, WD::WDNode& com);
/**
    * @brief 提取第三口点节点
*/
WD::WDNode::SharedPtr   ExtractThirdJoint(WD::WDCore& core, WD::WDNode& com);

WD::WDNode::SharedPtr   GetSpecJoint(WD::WDNode::SharedPtr pCata, JointShape shape);
WD::WDNode::Nodes       GetJoints(WD::WDNode::SharedPtr pCata, JointShape shape);
std::string             GetJointDesc(WD::WDCore& core, WD::WDNode& joint);
std::string             GetJointDisp(WD::WDCore& core, WD::WDNode& joint);
/**
 * @brief 更新入口连接点参数
 * @param joint 连接点
*/
void                    UpdateARRJointParam(WD::WDCore& core, WD::WDNode& joint, WD::WDPropertyGroup& group, std::vector<size_t>& indexs);
void                    UpdateARRJointParam(WD::WDCore& core, WD::WDNode& joint, WD::StringVector& desParam);
/**
 * @brief 更新出口连接点参数
 * @param joint 连接点
*/
void                    UpdateLEAJointParam(WD::WDCore& core, WD::WDNode& joint, WD::WDPropertyGroup& group, std::vector<size_t>& indexs);
void                    UpdateLEAJointParam(WD::WDCore& core, WD::WDNode& joint, WD::StringVector& desParam);
/**
 * @brief 更新第三口连接点参数
 * @param joint 连接点
*/
void                    UpdateThiJointParam(WD::WDCore& core, WD::WDNode& joint, WD::WDPropertyGroup& group, std::vector<size_t>& indexs);
void                    UpdateThiJointParam(WD::WDCore& core, WD::WDNode& joint, WD::StringVector& desParam);

/**
 * @brief 初始化默认设计参数
*/
void InitDefaultDESP();

void DesignParamsToProp(const WD::StringVector& desParam, const std::vector<size_t>& indexs, WD::WDPropertyGroup& group);
std::vector<size_t> GetDesParamsIndexs(const std::string& desiType);

WD_NAMESPACE_BEGIN

static constexpr const char*    SPEC_Purpose_Hvac   =   "TRAY";

static constexpr const char*    SPEC_Purpose_Insu   =   "INSU";
static constexpr const char*    SPEC_Purpose_Trac   =   "TRAC";

static constexpr const char*    Spec_Question_STYP  =   "STYP";

/**
 * @brief 操作类型
*/
enum OperationType
{
    // 创建
    OT_Create = 0,
    // 修改
    OT_Modify
};

WDNode::SharedPtr   ParentNode(WDBMDesign& mgr, WDNode::SharedPtr pNode, const char* type);

WDNode::SharedPtr   GetBranPspec(WDNode::SharedPtr pNode);
void                SetBranPspec(WDNode::SharedPtr pNode, WDNode::SharedPtr pPspec);

/**
 * @brief 获取管件节点其他朝向(用于作为控制节点旋转的第二个朝向)
 * @info 一般用于已经出入口点已对齐后
 * @param com 管件节点对象
*/
DVec3               GetOtherDir(WDNode& com);
/**
 * @brief 设置直通管件节点其他朝向(用于作为控制节点旋转的第二个朝向)
 * @info 一般用于已经出入口点已对齐后
 * @param com 管件节点对象
 * @param dir 目标朝向，必须是单位向量
*/
void                SetSTRAOtherDir(WDNode& com, const DVec3& dir);
/**
 * @brief 设置弯通管件节点出口朝向(用于作为控制节点旋转的第二个朝向)
 * @info 一般用于已经出入口点已对齐后
 * @param com 管件节点对象
 * @param dir 目标朝向，必须是单位向量
*/
void                SetBENDLeaveDir(WDNode& com, const DVec3& dir);

/**
 * @brief 顺流向重新连接桥件
 * @param bran 桥架分支节点
*/
bool                RestoreConnForward(WD::WDNode::SharedPtr pCom, double spoolDistance);

WD_NAMESPACE_END
