#include    "NodeSortDialog.h"
#include    "nodeTree/WDNodeTree.h"
#include    "core/message/WDMessage.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include    "core/common/WDStringConvert.h"
#include    "core/undoRedo/WDUndoStack.h"

WD_NAMESPACE_BEGIN

static bool StringToDouble(const std::string& str, double& outValue)
{
    if (str.empty())
        return false;
    bool bHaveDot = false;
    int i = 0;
    if (str[0] == '-')
    {
        if (str.size() == 1)
            return false;
        i = 1;
    }
    for (; i < str.size(); ++i)
    {
        auto& eachC = str[i];
        if (eachC == '.')
        {
            if (bHaveDot)
                return false;
            bHaveDot = true;
        }
        if (eachC < '0' || eachC > '9')
            return false;
    }
    bool ret = false;
    outValue = FromString<double>(str, &ret);
    return ret;
}

NodeSortDialog::NodeSortDialog(WD::WDCore& core, QWidget *parent)
    : QDialog(parent)
    , _core(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    this->retranslateUi();

    connect(ui.pushButtonApply,             &QPushButton::clicked, this, &NodeSortDialog::slotPushButtonApplyClicked);
    connect(ui.pushButtonApplyRule,         &QPushButton::clicked, this, &NodeSortDialog::slotPushButtonApplyRuleClicked);
    connect(ui.pushButtonAscendingOrder,    &QPushButton::clicked, this, &NodeSortDialog::slotPushButtonAscendingOrderClicked);
    connect(ui.pushButtonCancle,            &QPushButton::clicked, this, &NodeSortDialog::slotPushButtonCancleClicked);
    connect(ui.pushButtonCE,                &QPushButton::clicked, this, &NodeSortDialog::slotPushButtonCEClicked);
    connect(ui.pushButtonDescendingOrder,   &QPushButton::clicked, this, &NodeSortDialog::slotPushButtonDescendingOrderClicked);


    ui.comboBoxNodeType->addItem(QString::fromLocal8Bit("SITE"), QString::fromLocal8Bit("SITE"));
    ui.comboBoxNodeType->addItem(QString::fromLocal8Bit("ZONE"), QString::fromLocal8Bit("ZONE"));
    ui.comboBoxNodeType->addItem(QString::fromLocal8Bit("PIPE"), QString::fromLocal8Bit("PIPE"));
    ui.comboBoxNodeType->addItem(QString::fromLocal8Bit("EQUI"), QString::fromLocal8Bit("EQUI"));
    ui.comboBoxNodeType->addItem(QString::fromLocal8Bit("FRMW"), QString::fromLocal8Bit("FRMW"));
    ui.comboBoxNodeType->setCurrentIndex(2);

    ui.listWidget->setDragEnabled(true);
    ui.listWidget->setDragDropMode(QListWidget::InternalMove);
    ui.listWidget->setDefaultDropAction(Qt::MoveAction);
}
NodeSortDialog::~NodeSortDialog()
{    
}

void    NodeSortDialog::showEvent(QShowEvent* )
{
}
void    NodeSortDialog::hideEvent(QHideEvent*)
{
}

void    NodeSortDialog::slotPushButtonApplyClicked()
{
    auto pCurrentNode = _currentNode.lock();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("NodeSortDialog", "Current Node is Empty!");
        return;
    }
    auto nodes = nodesReorder(pCurrentNode->children(), this->listWidgetNodeIndexs());
    auto pCommand = new WD::WDUndoCommand("NodeSort");
    const auto& prevChildren = pCurrentNode->children();

    if (prevChildren == nodes)
        return;

    pCommand->setNoticeAfterRedo([pCurrentNode, nodes](const WD::WDUndoCommand&)
    {
        if (pCurrentNode == nullptr)
        {
            assert(false);
            return;
        }
        pCurrentNode->reorder([pCurrentNode, nodes](const WD::WDNode::Nodes& children, WDNode&)->WD::WDNode::Nodes
        {
            std::unordered_set<WD::WDNode::SharedPtr> childrenSet;
            for (auto& pChild : children)
            {
                if (pChild == nullptr)
                {
                    assert(false);
                    return children;
                }
                if (childrenSet.find(pChild) != childrenSet.end())
                {
                    assert(false);
                    return children;
                }
                childrenSet.emplace(pChild);
            }
            for (auto& pNode : nodes)
            {
                if (pNode == nullptr || childrenSet.find(pNode) == childrenSet.end())
                {
                    assert(false);
                    return children;
                }
            }
            return nodes;
        });
    });
    pCommand->setNoticeAfterUndo([prevChildren, pCurrentNode](const WD::WDUndoCommand&)
    {
        if (pCurrentNode == nullptr)
        {
            assert(false);
            return;
        }
        pCurrentNode->reorder([pCurrentNode, prevChildren](const WD::WDNode::Nodes& children, WDNode&)->WD::WDNode::Nodes
        {
            std::unordered_set<WD::WDNode::SharedPtr> childrenSet;
            for (auto& pChild : children)
            {
                if (pChild == nullptr)
                {
                    assert(false);
                    return children;
                }
                if (childrenSet.find(pChild) != childrenSet.end())
                {
                    assert(false);
                    return children;
                }
                childrenSet.emplace(pChild);
            }
            for (auto& pNode : prevChildren)
            {
                if (pNode == nullptr || childrenSet.find(pNode) == childrenSet.end())
                {
                    assert(false);
                    return children;
                }
            }
            return prevChildren;
        });
    });
    _core.undoStack().push(pCommand);

    this->updateListWidget(pCurrentNode->children());
}

void    NodeSortDialog::slotPushButtonApplyRuleClicked()
{
    auto pCurrentNode = _currentNode.lock();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("NodeSortDialog", "Current Node is Empty!");
        return;
    }
    // 获取分割符
    std::string delimiter = ui.lineEditDelimiter->text().toUtf8().data();

    std::array<uint, 4> indexs = { static_cast<uint>(ui.spinBoxN1->value())
    , static_cast<uint>(ui.spinBoxN2->value())
    , static_cast<uint>(ui.spinBoxN3->value())
    , static_cast<uint>(ui.spinBoxN4->value())};

    std::array<bool, indexs.size()> bByNumbers = {ui.checkBoxN1ByNumber->isChecked()
        , ui.checkBoxN2ByNumber->isChecked()
        , ui.checkBoxN3ByNumber->isChecked()
        , ui.checkBoxN4ByNumber->isChecked()};

    if (indexs[0] < 1 || indexs[1] < 1 || indexs[2] < 1 || indexs[3] < 1 )
    {
        WD_WARN_T("NodeSortDialog", "Index cannot less than 1!");
        return;
    }
    auto tmpInedxsSet = std::unordered_set<uint>({ indexs[0], indexs[1], indexs[2], indexs[3] });
    if (tmpInedxsSet.size() != indexs.size())
    {
        WD_WARN_T("NodeSortDialog", "Indexs cannot be repeat!");
        return;
    }

    std::string nodeType = ui.comboBoxNodeType->currentData().toString().toUtf8().toStdString();
    try
    {
        // 不参与排序的节点
        WDNode::Nodes otherNodes;
        // 参与排序的节点
        WDNode::Nodes sortNodes;

        auto nodes = nodesReorder(pCurrentNode->children(), this->listWidgetNodeIndexs());
        for (auto& pChild : nodes)
        {
            if (pChild == nullptr)
                continue;
            if (!pChild->isType(nodeType))
            {
                otherNodes.push_back(pChild);
                continue;
            }
            sortNodes.push_back(pChild);
        }

        std::sort(sortNodes.begin(), sortNodes.end(), [&](WDNode::SharedPtr left, WDNode::SharedPtr right) ->bool
        {
            if (left == nullptr && right == nullptr)
                return false;
            if (left == nullptr)
                return true;
            if (right == nullptr)
                return false;

            if (!left->isNamed() && !right->isNamed())
                return false;
            // 这里认为无值大于有值
            if (!left->isNamed())
                return false;
            if (!right->isNamed())
                return true;

            const auto& lName = left->srcName();
            const auto& rName = right->srcName();

            auto lNameVec = StringSplit(lName, delimiter);
            auto rNameVec = StringSplit(rName, delimiter);

            for (int i = 0; i < indexs.size(); ++i)
            {
                auto index = indexs[i] - 1;
                // 如果下标出字符串不存在或者下标错误,跳过当前下标
                if (( ( index >= lNameVec.size() ) && ( index >= rNameVec.size() ) ))
                    continue;
                
                // 这里认为无值大于有值
                if (index >= lNameVec.size())
                    return false;
                if (index >= rNameVec.size())
                    return true;

                auto& bByNumber = bByNumbers[i];
                if (bByNumber)
                {
                    double lVar = 0.0;
                    double rVar = 0.0;
                    if (!StringToDouble(lNameVec[index], lVar))
                    {
                        char info[10240] = {0};
                        sprintf(info, "/%s %s! \"%s\"%s!"
                            , lName.c_str()
                            , WDTs("NodeSortDialog", "Sort by number failed").c_str()
                            , lNameVec[index].c_str()
                            , WDTs("NodeSortDialog", "is not a number").c_str());
                        throw std::logic_error(info);
                    }

                    if (!StringToDouble(rNameVec[index], rVar))
                    {
                        char info[10240] = {0};
                        sprintf(info, "/%s %s! \"%s\"%s!"
                            , rName.c_str()
                            , WDTs("NodeSortDialog", "Sort by number failed").c_str()
                            , rNameVec[index].c_str()
                            , WDTs("NodeSortDialog", "is not a number").c_str());
                        throw std::logic_error(info);
                    }
                    if (lVar < rVar)
                        return true;
                    if (lVar > rVar)
                        return false;

                    continue;
                }

                if (lNameVec[index] < rNameVec[index])
                    return true;
                if (lNameVec[index] > rNameVec[index])
                    return false;
            }
            return lName < rName;
        });
        sortNodes.insert(sortNodes.end(), otherNodes.begin(), otherNodes.end());
        updateListWidget(sortNodes);
    }
    catch (std::logic_error& e)
    {
        WD_WARN(e.what());
    }
    catch (...)
    {
        WD_WARN_T("NodeSortDialog", "Sort failed!");
    }
}

void    NodeSortDialog::slotPushButtonAscendingOrderClicked()
{
    ui.listWidget->sortItems();
}

void    NodeSortDialog::slotPushButtonCancleClicked()
{
    this->reject();
}

void    NodeSortDialog::slotPushButtonCEClicked()
{
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("NodeSortDialog", "Current Node is Empty!");
        return;
    }
    _currentNode = pCurrentNode;
    updateListWidget(pCurrentNode->children());
}

void    NodeSortDialog::slotPushButtonDescendingOrderClicked()
{
    ui.listWidget->sortItems(Qt::DescendingOrder);
}

void    NodeSortDialog::updateListWidget(const WD::WDNode::Nodes& nodes)
{
    auto pCurrentNode = _currentNode.lock();
    if (pCurrentNode == nullptr)
    {
        assert(false);
        return;
    }
    std::unordered_map<WD::WDNode::SharedPtr, uint> indexs;
    auto childCount = pCurrentNode->childCount();
    for (int index = 0; index < childCount; ++index)
    {
        auto pChild = pCurrentNode->childAt(index);
        if (pChild == nullptr)
            continue;
        assert(indexs.find(pChild) == indexs.end());
        indexs[pChild] = index;
    }

    ui.listWidget->clear();
    for (const auto& pNode : nodes)
    {
        if (pNode == nullptr)
            continue;
        auto itr = indexs.find(pNode);
        if (itr == indexs.end())
        {
            assert(false);
            continue;
        }
        auto pItem = new QListWidgetItem();
        pItem->setText(QString::fromUtf8(pNode->name().c_str()));
        pItem->setData(Qt::UserRole, itr->second);
        ui.listWidget->addItem(pItem);
    }
}
std::vector<uint>   NodeSortDialog::listWidgetNodeIndexs()
{
    std::vector<uint> indexs;
    indexs.reserve(ui.listWidget->count());
    for (int i = 0; i < ui.listWidget->count(); ++i)
    {
        auto pItem = ui.listWidget->item(i);
        if (pItem == nullptr)
            continue;
        indexs.push_back(static_cast<uint>(pItem->data(Qt::UserRole).toInt()));
    }
    assert(indexs.size() == ui.listWidget->count());
    return indexs;
}

WD::WDNode::Nodes   NodeSortDialog::nodesReorder(const WD::WDNode::Nodes& nodes, const std::vector<uint>& indexs)
{
    if (nodes.size() != indexs.size())
    {
        assert(false);
        return nodes;
    }
    WD::WDNode::Nodes outNodes;
    outNodes.reserve(nodes.size());
    for (auto& index : indexs)
    {
        if (index >= nodes.size())
        {
            assert(false);
            return nodes;
        }
        outNodes.push_back(nodes[index]);
    }
    if (nodes.size() != outNodes.size())
    {
        assert(false);
        return nodes;
    }
    return outNodes;
}

void    NodeSortDialog::retranslateUi()
{
    Trs("NodeSortDialog"
        , static_cast<QDialog*>(this)
        , ui.pushButtonApply
        , ui.pushButtonApplyRule
        , ui.pushButtonAscendingOrder
        , ui.pushButtonCancle
        , ui.pushButtonCE
        , ui.pushButtonDescendingOrder

        , ui.labelDelimiter
        , ui.labelNodeType
        , ui.labelTip

        , ui.checkBoxN1ByNumber
        , ui.checkBoxN2ByNumber
        , ui.checkBoxN3ByNumber
        , ui.checkBoxN4ByNumber

        , ui.groupBox
        , ui.groupBox_2
    );
}

WD_NAMESPACE_END