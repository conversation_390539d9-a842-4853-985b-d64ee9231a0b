#include "UiComNodeSort.h"

UiComNodeSort::UiComNodeSort(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    : IUiComponent(mainWindow, attrs)
{
    _pNodeSortDialog = new WD::NodeSortDialog(mainWindow.core(), mainWindow.widget());
}
UiComNodeSort::~UiComNodeSort()
{
    if (_pNodeSortDialog != nullptr)
    {
        delete _pNodeSortDialog;
        _pNodeSortDialog = nullptr;
    }
}

void    UiComNodeSort::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        if (pActionNotice->action().is("action.design.common.node.sort"))
        {
            if (_pNodeSortDialog->isHidden())
                _pNodeSortDialog->show();
            else
                _pNodeSortDialog->activateWindow();
        }
    }
    break;
    default:
        break;
    }
}