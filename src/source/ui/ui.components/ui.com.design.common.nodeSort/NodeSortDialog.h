#pragma once
#include    <QDialog>
#include    "ui_NodeSortDialog.h"
#include    "core/WDCore.h"
#include    "core/node/WDNode.h"

WD_NAMESPACE_BEGIN

class NodeSortDialog : public QDialog
{
    Q_OBJECT
public:
    NodeSortDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    ~NodeSortDialog();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;
private:
    void slotPushButtonApplyClicked();
    void slotPushButtonApplyRuleClicked();
    void slotPushButtonAscendingOrderClicked();
    void slotPushButtonCancleClicked();
    void slotPushButtonCEClicked();
    void slotPushButtonDescendingOrderClicked();

private:
    void updateListWidget(const WD::WDNode::Nodes& nodes);
    std::vector<uint> listWidgetNodeIndexs();
    WD::WDNode::Nodes nodesReorder(const WD::WDNode::Nodes& nodes, const std::vector<uint>& indexs);
private:
    /**
     * @brief 界面翻译
    */
    void retranslateUi();
private:
    Ui::NodeSortDialog ui;
    WD::WDCore& _core;
    WD::WDNode::WeakPtr _currentNode;
};

WD_NAMESPACE_END