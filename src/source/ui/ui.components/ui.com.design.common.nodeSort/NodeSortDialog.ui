<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NodeSortDialog</class>
 <widget class="QDialog" name="NodeSortDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>545</width>
    <height>551</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>NodeSort</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4" rowstretch="1,0" columnstretch="1,0,0,0">
   <item row="1" column="2" colspan="2" alignment="Qt::AlignRight">
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <widget class="QPushButton" name="pushButtonApply">
      <property name="focusPolicy">
       <enum>Qt::ClickFocus</enum>
      </property>
      <property name="text">
       <string>Apply</string>
      </property>
      <property name="autoDefault">
       <bool>false</bool>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButtonCancle">
      <property name="focusPolicy">
       <enum>Qt::ClickFocus</enum>
      </property>
      <property name="text">
       <string>Cancle</string>
      </property>
      <property name="autoDefault">
       <bool>false</bool>
      </property>
     </widget>
    </widget>
   </item>
   <item row="0" column="0" colspan="2">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>MembersList</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="0">
       <widget class="QListWidget" name="listWidget"/>
      </item>
      <item row="1" column="0" alignment="Qt::AlignLeft">
       <widget class="QSplitter" name="splitter_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <widget class="QPushButton" name="pushButtonCE">
         <property name="focusPolicy">
          <enum>Qt::ClickFocus</enum>
         </property>
         <property name="text">
          <string>CE</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="pushButtonAscendingOrder">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="focusPolicy">
          <enum>Qt::ClickFocus</enum>
         </property>
         <property name="text">
          <string>AscendingOrder</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="pushButtonDescendingOrder">
         <property name="focusPolicy">
          <enum>Qt::ClickFocus</enum>
         </property>
         <property name="text">
          <string>DescendingOrder</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="2" colspan="2">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>RuleSort</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2" rowstretch="0,0,0,0,0,1" columnstretch="0,1,1">
      <item row="0" column="0" colspan="2">
       <widget class="QLabel" name="labelTip">
        <property name="text">
         <string>split name and sort by weights</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="labelDelimiter">
        <property name="text">
         <string>Delimiter</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1" colspan="2">
       <widget class="QLineEdit" name="lineEditDelimiter">
        <property name="text">
         <string>-</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0" colspan="3">
       <layout class="QGridLayout" name="gridLayout">
        <item row="3" column="3">
         <widget class="QCheckBox" name="checkBoxN4ByNumber">
          <property name="text">
           <string>ByNumber</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="labelN3">
          <property name="text">
           <string>N3</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QCheckBox" name="checkBoxN1ByNumber">
          <property name="text">
           <string>ByNumber</string>
          </property>
         </widget>
        </item>
        <item row="2" column="3">
         <widget class="QCheckBox" name="checkBoxN3ByNumber">
          <property name="text">
           <string>ByNumber</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QSpinBox" name="spinBoxN1">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>1</number>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLabel" name="labelN2">
          <property name="text">
           <string>N2</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QSpinBox" name="spinBoxN2">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>2</number>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="labelN4">
          <property name="text">
           <string>N4</string>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QSpinBox" name="spinBoxN3">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>3</number>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="labelN1">
          <property name="text">
           <string>N1</string>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QSpinBox" name="spinBoxN4">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>4</number>
          </property>
         </widget>
        </item>
        <item row="1" column="3">
         <widget class="QCheckBox" name="checkBoxN2ByNumber">
          <property name="text">
           <string>ByNumber</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0" rowspan="2">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>75</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="2" column="4">
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="labelNodeType">
        <property name="text">
         <string>NodeType</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1" colspan="2">
       <widget class="QComboBox" name="comboBoxNodeType"/>
      </item>
      <item row="4" column="1" colspan="2" alignment="Qt::AlignRight">
       <widget class="QPushButton" name="pushButtonApplyRule">
        <property name="focusPolicy">
         <enum>Qt::ClickFocus</enum>
        </property>
        <property name="text">
         <string>ApplyRule</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="5" column="2">
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>238</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
 <slots>
  <slot>sliderX(int)</slot>
 </slots>
</ui>
