#pragma once
#include    "NodeSortDialog.h"
#include    "../../wizDesignerApp/UiInterface/UiInterface.h"

class UiComNodeSort 
    : public IUiComponent
{
public:
    UiComNodeSort(IMainWindow& mainWindow, const UiComponentAttributes& attrs);
    ~UiComNodeSort();
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
private:
    WD::NodeSortDialog* _pNodeSortDialog;
};
