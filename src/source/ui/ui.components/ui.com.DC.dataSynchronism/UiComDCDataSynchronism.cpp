#include "UiComDCDataSynchronism.h"
#include "core/message/WDMessage.h"
#include "../../wizDesignerApp/UiInterface/ICollaboration.h"
#include "core/log/WDLoggerPort.h"
#include "core/WDTranslate.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMPermissionMgr.h"
#include "core/WDBlockingTask.h"
#include "ReleaseDeclarationDialog.h"
#include <QMessageBox>
#include <filesystem>
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/message/WDMessage.h"

UiComDCDataSynchronism::UiComDCDataSynchronism(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    : IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
    , _runningPull(true)
{
    _pObjDeclDialog = new ObjectDeclarationDialog(mainWindow.core(), mainWindow.collaboration(), mainWindow.widget());
    _pRelDeclDialog = new ReleaseDeclarationDialog(mainWindow.core(), mainWindow.collaboration(), mainWindow.widget());

    // 设置文件自动保存
    _autoSave           =   false;
    _autoSaveSpacing    =   30 * 60 * 1000;
    _timer              =   new QTimer();
    connect(_timer, &QTimer::timeout, this, &UiComDCDataSynchronism::slotRepeatSave);
    auto pCfgAutoSave = _core.cfg().query("auto.save");
    if (pCfgAutoSave != nullptr)
    {
        // 配置值
        auto pValue = pCfgAutoSave->value<bool>();
        if (pValue != nullptr)
        {
            _autoSave = *pValue;
        }
        pCfgAutoSave->bindFunction({this, &UiComDCDataSynchronism::onCfgAutoSaveValueChanged});
    }
    auto pCfgAutoSaveSpacing = _core.cfg().query("auto.save.spacing");
    if (pCfgAutoSaveSpacing != nullptr)
    {
        // 配置值
        auto pValue = pCfgAutoSaveSpacing->value<int>();
        if (pValue != nullptr)
        {
            _autoSaveSpacing = *pValue * 60 * 1000;
        }
        pCfgAutoSaveSpacing->bindFunction({this, &UiComDCDataSynchronism::onCfgAutoSaveSpacingValueChanged});
        pCfgAutoSaveSpacing->setMinimum(5);
        pCfgAutoSaveSpacing->setMaximum(999999);
    }
    _timer->setInterval(_autoSaveSpacing);
    _timer->start();

    _immediatelyPullThread = new std::thread([&]()
        {
            while (_runningPull)
            {
                bool needUpdate = mWindow().collaboration().modelServer().pull();
                if (needUpdate)
                    emit sigUpdateLocalCache();
            }
        });

    _pCheckLoginThread = new HTCheckLoginThread();
    // TODO: NEWDC
    //_pCheckLoginThread->applyFunc() = std::bind(&INetworkApi::applyCheckLogin, &mWindow().networkApi(), std::placeholders::_1);

    QObject::connect(this, &UiComDCDataSynchronism::sigUpdateLocalCache, this, &UiComDCDataSynchronism::slotUpdateLocalCache);
    QObject::connect(_pCheckLoginThread, &HTCheckLoginThread::sigHasLoggedIn, this, &UiComDCDataSynchronism::slotQuitSys);
    QObject::connect(this, &UiComDCDataSynchronism::sigDownloadProjectEnd, this, &UiComDCDataSynchronism::slotDownloadProjectEnd);
    QObject::connect(this, &UiComDCDataSynchronism::sigCommitProjectEnd, this, &UiComDCDataSynchronism::slotCommitProjectEnd);
    QObject::connect(this, &UiComDCDataSynchronism::sigInitProjectEnd, this, &UiComDCDataSynchronism::slotInitProjectEnd); 
}

UiComDCDataSynchronism::~UiComDCDataSynchronism()
{
    if (_pObjDeclDialog != nullptr)
    {
        delete _pObjDeclDialog;
        _pObjDeclDialog = nullptr;
    }
    if (_pRelDeclDialog != nullptr)
    {
        delete _pRelDeclDialog;
        _pRelDeclDialog = nullptr;
    }
    if (_timer != nullptr)
    {
        _timer->deleteLater();
        _timer = nullptr;
    }
    if (_immediatelyPullThread != nullptr)
    {
        _runningPull = false;
        if (_immediatelyPullThread->joinable())
        {
            _immediatelyPullThread->join();
        }
        delete _immediatelyPullThread;
        _immediatelyPullThread = nullptr;
    }
    // 退出同账号登录检测的线程
    if (_pCheckLoginThread != nullptr)
    {
        _pCheckLoginThread->terminate();
        delete _pCheckLoginThread;
        _pCheckLoginThread = nullptr;
    }
}

void UiComDCDataSynchronism::onNotice(UiNotice * pNotice)
{
    auto& collaboration = mWindow().collaboration();
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
        {
            if (collaboration.actived())
            {
                // TODO: NEWDC
#if 1
                // 签入用户已经签入的节点
                //inetworkApi.modelServerApi().initClaimedNodes();
                // 初始化申明回调
                collaboration.modelServer().initClaimCallback();
                //if (!inetworkApi.projectInfo().initialized)
                //    this->createAddLocal();
                if (collaboration.cfgInfo().immediately_Pull)
                {
                    if (_immediatelyPullThread != nullptr)
                        _immediatelyPullThread->detach();
                }
#endif
            }
        }
        break;
    case UiNoticeType::UNT_Action:
        {
            auto pANotice = static_cast<UiActionNotice*>(pNotice);
            if (pANotice->action().is("action.DC.object.declaration"))
            {
                // 更新
                if (_pObjDeclDialog->isHidden())
                    _pObjDeclDialog->show();
                else
                    _pObjDeclDialog->activateWindow();
            }
            else if (pANotice->action().is("action.DC.data.synchronism"))
            {
                auto pTask = _core.blockingTask();
                if (pTask == nullptr || pTask->isRunning())
                {
                    WD_ERROR_T("UiComDCDataSynchronism", "other task");
                    return ;
                }

                WD::WDBlockingTask::Config cfg;
                cfg.closable = true;
                cfg.progressBarCount = 2;
                cfg.progress = true;
                cfg.decimals = 0;
                pTask->setConfig(cfg);

                bool bSuccess = false;
                pTask->start([this, &bSuccess](WD::WDBlockingTask&)
                    {
                        bSuccess = mWindow().collaboration().modelServer().push();
                    }, [this, &bSuccess](WD::WDBlockingTask&)
                        {
                            emit sigCommitProjectEnd(bSuccess);
                        });
            }
            else if (pANotice->action().is("action.DC.data.download"))
            {
                mWindow().collaboration().modelServer().pull();
            }
            //else if (pANotice->action().is("action.DC.project.reinit"))
            //{
            //    auto pAction = mWindow().queryAction("action.DC.project.reinit");
            //    if (pAction != nullptr)
            //        pAction->setEnabled(false);
            //    // 将当前选中节点序列化到json格式
            //    auto pCurr = mWindow().core().nodeTree().currentNode();
            //    if (pCurr == nullptr)
            //    {
            //        mWindow().core().message().info("没有选中目标节点!");
            //        break;
            //    }
            //    JsonDoc doc;
            //    auto& rootArray = doc.SetArray();
            //    auto& serializer = mWindow().core().getBMDesign().serialize();
            //    serializer.saveToJson({pCurr}, doc, rootArray);

            //    // 重新初始化项目
            //    bool bReinit = mWindow().networkApi().modelServerApi().reinitServerProject(doc);
            //    if (bReinit)
            //    {
            //        LOG_INFO << "重新初始化成功!";
            //        mWindow().core().message().info("重新初始化成功!");
            //    }
            //    else
            //    {
            //        LOG_INFO << "重新初始化失败!";
            //        mWindow().core().message().error("重新初始化失败!");
            //    }
            //    if (pAction != nullptr)
            //        pAction->setEnabled(true);
            //}
            else if (pANotice->action().is("action.DC.debugger.initData"))
            {
                auto pAction = mWindow().queryAction("action.DC.debugger.initData");
                if (pAction != nullptr)
                    pAction->setEnabled(false);

                // TODO: NEWDC
                //mWindow().collaboration().modelServer().debugInitData();

                if (pAction != nullptr)
                    pAction->setEnabled(true);
            }
            else if (pANotice->action().is("action.DC.declaration.release"))
            {
#if 0
                if (_pRelDeclDialog == nullptr)
                    break;
                // 显示界面，以模态对话框的方式
                if (_pRelDeclDialog->isHidden())
                    _pRelDeclDialog->exec();
                else
                    _pRelDeclDialog->activateWindow();
#endif
                mWindow().collaboration().modelServer().clear();
            }
        }
        break;
    case UiNoticeType::UNT_MainWindowShowed:
        {
#if 0
            auto isDcMode = mWindow().collaboration().actived();
            auto pActionInit = mWindow().queryAction("action.DC.project.init");
            if (pActionInit != nullptr)
            {
                if (!_core.getBMDesign().permissionMgr().isAdmin() || !isDcMode)
                {
                    pActionInit->setVisible(false);
                }
                else
                {
                    pActionInit->setVisible(true);
                    if (mWindow().collaboration().modelServer().projectInitialed())
                    {
                        pActionInit->setEnabled(false);
                    }
                    else
                    {
                        pActionInit->setEnabled(true);
                    }
                }
            }

            // 如果不是协同模式，或者当前用户不是设计人员，就屏蔽掉《对象声明》按钮
            auto pActionDeclaration = mWindow().queryAction("action.DC.object.declaration");
            if (pActionDeclaration != nullptr &&
                (!isDcMode || _core.getBMDesign().permissionMgr().role() != WD::WDBMPermissionMgr::U_Member))
            {
                pActionDeclaration->setVisible(false);
            }
            auto pActionSynchronism = mWindow().queryAction("action.DC.data.synchronism");
            if (pActionSynchronism != nullptr)
            {
                // 非协同或者项目未初始化之前不显示保存到服务器按钮
                if (!isDcMode || !mWindow().collaboration().modelServer().projectInitialed())
                    pActionSynchronism->setVisible(false);
            }
            auto pActionDownload = mWindow().queryAction("action.DC.data.download");
            if (pActionDownload != nullptr && !isDcMode)
            {
                pActionDownload->setVisible(false);
            }
            auto pActionRelease = mWindow().queryAction("action.DC.declaration.release");
            if (pActionRelease != nullptr && !isDcMode)
            {
                pActionRelease->setVisible(false);
            }
            // 主界面启动后，如果是协同模式那么启动同账号检测登录线程
            if (isDcMode && _pCheckLoginThread != nullptr)
                _pCheckLoginThread->start();
#endif
        }
        break;
    case UiNoticeType::UNT_ReadyUnload:
        {
            // 系统退出前，向服务端发送账号登出请求
            auto& collaboration = mWindow().collaboration();
            if (collaboration.actived())
            {
                // 退出同账号登录检测的线程
                if (_pCheckLoginThread != nullptr)
                {
                    _pCheckLoginThread->terminate();
                    delete _pCheckLoginThread;
                    _pCheckLoginThread = nullptr;
                }
                // TODO: NEWDC
                //networkApi.applyCheckLogin(INetworkApi::LT_Logout);
            }
        }
        break;
    default:
        break;
    }
}

void UiComDCDataSynchronism::slotRepeatSave()
{
    // 是否自动保存
    if (!_autoSave || !mWindow().collaboration().actived())
        return ;

    // 询问是否提交到服务器
    auto res = WD_QUESTION_T("ErrorUiComDCDataSynchronism", "Do commit?");
    if (res != 0)
        return ;
    
    this->commitLocalProjectToServer();
}
void UiComDCDataSynchronism::slotDownloadProjectEnd(bool bSuccess)
{
    // 同步项目文件
    if (!bSuccess)
    {
        LOG_INFO << "数据下载失败!";
        WD_WARN_T("UiComDCDataSynchronism", "download failure");
    }
    else
    {
        LOG_INFO << "数据下载成功!";
        WD_INFO_T("UiComDCDataSynchronism", "download success");
    }
    auto pAction = mWindow().queryAction("action.DC.data.download");
    if (pAction != nullptr)
        pAction->setEnabled(true);
}
void UiComDCDataSynchronism::slotCommitProjectEnd(bool bSuccess)
{
    if (bSuccess)
    {
        LOG_INFO << "本地数据提交成功!";

        auto pCfgAutoRelDecl= _core.cfg().query("auto.releaseDeclaration");
        // 配置不可见，或者自动释放权限设置为false，说明不需要释放权限
        if (pCfgAutoRelDecl == nullptr || !pCfgAutoRelDecl->visible() || !(*pCfgAutoRelDecl->visible()))
            return;
        auto pValue = pCfgAutoRelDecl->value<bool>();
        if (pValue == nullptr || !(*pValue))
            return;
        // 运行到此处说明配置中选择了自动释放权限，故直接释放所有的权限
        bool bCancelCheckOut = false;
        _core.currentBM()->claimMgr().checkOutAll(bCancelCheckOut);
        if (bCancelCheckOut)
            return ;
    }
    else
    {
        LOG_INFO << "本地数据提交失败!";
        WD_ERROR_T("UiComDCDataSynchronism", "commit failure");
    }
}
void UiComDCDataSynchronism::slotInitProjectEnd(bool bSuccess)
{
    if (bSuccess)
    {
        LOG_INFO << "项目初始化成功!";
        WD_INFO_T("UiComDCDataSynchronism", "initialization success");
        // 初始化成功显示保存到服务器按钮，隐藏保存到本地按钮
        auto pActionSynchronism = mWindow().queryAction("action.DC.data.synchronism");
        if (pActionSynchronism != nullptr)
        {
            pActionSynchronism->setVisible(true);
        }
        auto pActionXmlSave = mWindow().queryAction("action.node.xmlSave");
        if (pActionXmlSave != nullptr)
        {
            pActionXmlSave->setVisible(false);
        }
    }
    else
    {
        LOG_INFO << "项目初始化失败!";
        WD_INFO_T("UiComDCDataSynchronism", "initialization failed");

        auto pAction = mWindow().queryAction("action.DC.project.init");
        if (pAction != nullptr)
            pAction->setEnabled(true);
    }
}
void UiComDCDataSynchronism::slotQuitSys(const QString& msg)
{
    // 检测到该账号已经被登录的提示信息
    int result = WD::Core().message().question(msg.toUtf8().toStdString() + "\n\n" 
        + WD::WDTs("ErrorUiComDCDataSynchronism", "DoCommitAndExit?"));
    if (result == 0)
    {
        // 保存项目到服务器
        if (mWindow().widget() == nullptr)
            return;

        // 在保存项目到服务器后，退出项目。即使保存失败，在弹出提示后仍然会退出项目。
        bool isSuccess = true;
        auto moduleType = mWindow().moduleType();
        // 提交本地的数据到服务
        if (moduleType == "Design")
        {
            // TODO: NEWDC
            //isSuccess = mWindow().collaboration().modelServer().push();
        }
        else if (moduleType == "Catalog")
        {
            // TODO: NEWDC
            //isSuccess = mWindow().collaboration().modelServer().push();
        }

        // 保存失败，向用户弹出提示信息
        if (!isSuccess)
        {
#if 0
            // 获取当前项目的绝对路径
            QString dataDir = QString::fromUtf8(mWindow().networkApi().projectInfo().projectDataDir().c_str());
            std::filesystem::path relativePath = dataDir.toUtf8().toStdString();
            std::filesystem::path absolutePath = std::filesystem::absolute(relativePath);

            // 该提示框需要文本可被选择
            std::string text = WD::WDTs("ErrorUiComDCDataSynchronism", "ErrorToCommit") + "\n( " + absolutePath.string() + " )";
            std::string title = WD::WDTs("ErrorUiComDCDataSynchronism", "Warning");
            QMessageBox msgBox(QMessageBox::Warning, title.c_str(), text.c_str(), QMessageBox::Ok);
            msgBox.setTextInteractionFlags(Qt::TextSelectableByMouse | Qt::TextSelectableByKeyboard);
            msgBox.exec();
#endif
        }
    }
    // TODO: NEWDC
    // 无论是否成功保存项目到服务器，最后都将退出系统
    // 把isSave设置为false，是为了单独处理同账号登录冲突异常的情况，这样就会避免进入重载closeEvent后的逻辑处理（暂时这么处理）
    //mWindow().networkApi().setSave(false);
    mWindow().widget()->close();
}
void UiComDCDataSynchronism::slotUpdateLocalCache()
{
    mWindow().collaboration().modelServer().updateLocalCahche();
}

void UiComDCDataSynchronism::onCfgAutoSaveValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<bool>();
    if (pValue == nullptr)
        return ;

    _autoSave = *pValue;
}
void UiComDCDataSynchronism::onCfgAutoSaveSpacingValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<int>();
    if (pValue == nullptr)
        return ;

    _autoSaveSpacing = *pValue * 60 * 1000;
    if (_timer != nullptr)
    {
        _timer->setInterval(_autoSaveSpacing);
    }
}

void UiComDCDataSynchronism::commitLocalProjectToServer()
{
    // TODO: NEWDC
    // 更新登录有权限uuid
    //mWindow().networkApi().modelServerApi().initLoginGovern();

    auto pTask = _core.blockingTask();
    if (pTask == nullptr || pTask->isRunning())
    {
        // TODO: NEWDC
        //bool bSuccess = mWindow().collaboration().modelServer().push();
        //emit sigCommitProjectEnd(bSuccess);
        return ;
    }

    WD::WDBlockingTask::Config cfg;
    cfg.closable = true;
    cfg.progressBarCount = 2;
    cfg.progress = true;
    cfg.decimals = 0;
    pTask->setConfig(cfg);

    bool bSuccess = false;
    pTask->start([this, &bSuccess](WD::WDBlockingTask&)
        {
            // TODO: NEWDC
            //bSuccess = mWindow().collaboration().modelServer().push();
        }, [this, &bSuccess](WD::WDBlockingTask&)
        {
            emit sigCommitProjectEnd(bSuccess);
        });
}
void UiComDCDataSynchronism::downloadProjectFromServer()
{
    auto pAction = mWindow().queryAction("action.DC.data.download");
    if (pAction != nullptr)
        pAction->setEnabled(false);

    auto pTask = _core.blockingTask();
    if (pTask == nullptr || pTask->isRunning())
    {
        _bDownloadSuccess = mWindow().collaboration().modelServer().pull();

        emit sigDownloadProjectEnd(_bDownloadSuccess);
    }
    if (pTask != nullptr && !pTask->isRunning())
    {
        WD::WDBlockingTask::Config cfg;
        cfg.closable = true;
        cfg.progressBarCount = 2;
        cfg.progress = true;
        cfg.decimals = 0;
        pTask->setConfig(cfg);

        _bDownloadSuccess = false;
        pTask->start([&](WD::WDBlockingTask&)
            {
                _bDownloadSuccess = mWindow().collaboration().modelServer().pull();
            }, [&](WD::WDBlockingTask&)
            {
                emit sigDownloadProjectEnd(_bDownloadSuccess);
            });
    }
}
void UiComDCDataSynchronism::createAddLocal()
{
    WD::WDBMBase* currentMgr = _core.currentBM();
    if (currentMgr == nullptr)
    {
        return;
    }
    currentMgr->claimMgr().newCreatedNodes().add(currentMgr->root()->children());
}