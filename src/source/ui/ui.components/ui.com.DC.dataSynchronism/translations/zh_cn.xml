<?xml version="1.0" encoding="utf-8"?>

<TS language="简体中文">
	<context>
		<name>UiComDCDataSynchronism</name>
		<message>
			<source>download failure</source>
			<translation>下载失败!</translation>
		</message>
		<message>
			<source>download success</source>
			<translation>下载成功!</translation>
		</message>
		<message>
			<source>commit success</source>
			<translation>提交成功!</translation>
		</message>
			<message>
			<source>commit failure</source>
			<translation>提交失败!</translation>
		</message>
		<message>
			<source>initialization success</source>
			<translation>初始化成功!</translation>
		</message>
		<message>
			<source>initialization failed</source>
			<translation>初始化失败!</translation>
		</message>
		<message>
			<source>add</source>
			<translation>新增</translation>
		</message>
		<message>
			<source>delete</source>
			<translation>删除</translation>
		</message>
		<message>
			<source>modify</source>
			<translation>修改</translation>
		</message>
		<message>
			<source>other task</source>
			<translation>有其他的阻塞任务正在执行</translation>
		</message>
	</context>
	<context>
		<name>ObjectDeclarationDialog</name>
		<message>
			<source>ObjectDeclaration</source>
			<translation>对象声明</translation>
		</message>
		<message>
			<source>add</source>
			<translation>添加</translation>
		</message>
		<message>
			<source>remove</source>
			<translation>移除</translation>
		</message>
		<message>
			<source>clear</source>
			<translation>清除</translation>
		</message>
		<message>
			<source>view</source>
			<translation>查看</translation>
		</message>
		<message>
			<source>ok</source>
			<translation>确定</translation>
		</message>
		<message>
			<source>cancel</source>
			<translation>取消</translation>
		</message>
		<message>
			<source>selected node is null</source>
			<translation>选中的节点为空!</translation>
		</message>
		<message>
			<source>selected node is not a review objectr and cannot be added</source>
			<translation>选中的节点不是校审对象, 不允许添加!</translation>
		</message>
		<message>
			<source>node</source>
			<translation>节点</translation>
		</message>
		<message>
			<source>no permission</source>
			<translation>没有权限</translation>
		</message>
		<message>
			<source>has been declared</source>
			<translation>已被声明</translation>
		</message>
		<message>
			<source>current node has been added</source>
			<translation>当前选中节点已被添加!</translation>
		</message>
		<message>
			<source>please select node</source>
			<translation>请选择节点!</translation>
		</message>
		<message>
			<source>number of declare object</source>
			<translation>声明对象个数</translation>
		</message>
		<message>
			<source>failed to declare the object</source>
			<translation>声明对象失败!</translation>
		</message>
		<message>
			<source>number of object to be unclaimed</source>
			<translation>取消声明对象个数</translation>
		</message>
		<message>
			<source>failed to unclaimed</source>
			<translation>取消声明对象失败!</translation>
		</message>
	</context>
	
	<context>
		<name>ReleaseDeclarationDialog</name>
		<message>
			<source>ReleaseDeclarationDialog</source>
			<translation>释放申领</translation>
		</message>
		<message>
			<source>Release</source>
			<translation>释放</translation>
		</message>
		<message>
			<source>ReleaseAll</source>
			<translation>释放所有</translation>
		</message>
		<message>
			<source>Cancel</source>
			<translation>取消</translation>
		</message>	
	</context>

	<context>
		<name>WarnTips</name>
		<message>
			<source>PleaseSelectNodes</source>
			<translation>请选择需要释放申领的节点</translation>
		</message>	
		<message>
			<source>CommitToServerOrNot</source>
			<translation>有数据和服务器不一致，需要先保存到服务器才能释放，是否保存？</translation>
		</message>	
		<message>
			<source>NewCreated</source>
		<translation>【新增】</translation>
		</message>	
	</context>
	<context>
		<name>ErrorUiComDCDataSynchronism</name>
		<message>
			<source>Do commit?</source>
			<translation>是否提交到服务器？</translation>
		</message>
		<message>
			<source>Warning</source>
			<translation>警告</translation>
		</message>
		<message>
			<source>ErrorToCommit</source>
			<translation>提交到服务器失败，建议本地留存数据副本，避免下次登录时覆盖本地数据</translation>
		</message>
		<message>
			<source>DoCommitAndExit?</source>
			<translation>系统即将退出，是否将数据提交到服务器？</translation>
		</message>
	</context>
</TS> 