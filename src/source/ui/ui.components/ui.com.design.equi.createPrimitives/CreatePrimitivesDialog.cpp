#include "CreatePrimitivesDialog.h"
#include "core/WDCore.h"
#include "core/message/WDMessage.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/typeMgr/WDBMAttrEnumDictionary.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "businessModule/WDBMColorTable.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/math/DirectionParser.h"
#include "core/undoRedo/WDUndoStack.h"
#include "core/businessModule/WDBMClaimMgr.h"

static auto TrF(const char* str, const char* cxtStr = "CreatePrimitivesDialog")
{
    return WD::WDTs(cxtStr, str);
};

CreatePrimitivesDialog::CreatePrimitivesDialog(WD::WDCore& app, QWidget *parent)
    : QDialog(parent)
	, _app(app)
    , _positionCaptureHelpter(app)
    , _imgShowWidget(parent)
    , _nameHelpter(_app.getBMDesign())
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    // 空间占有级别, 通过字典查询
    auto pDictObst = _app.getBMDesign().attrEnumDictMgr().query("Obstruction");
    if (pDictObst != nullptr) 
    {
        QString currentText = "";
        const auto& kvs = pDictObst->get();
        for (const auto& kv: kvs)
        {
            auto trStr = _app.getBMDesign().trEDK("Obstruction", kv.first);
            QString text = QString::fromUtf8(trStr.c_str());
            int value = kv.second.toInt();
            ui.comboBoxObstruction->addItem(text, value);
            if (value == WD::WDBMObstructionLevelType::BMOL_Hard)
                currentText = text;
        }
        ui.comboBoxObstruction->setCurrentText(currentText);
    }
    {
        // BOX
        ui.comboBoxType->addItem(QString("BOX"), QVariant("BOX"));
        addAttrWidget("BOX", {
            {"Xlength", 1.0},
            {"Ylength", 1.0},
            {"Zlength", 1.0} });

        // CYLI
        ui.comboBoxType->addItem(QString("CYLI"), QVariant("CYLI"));
        addAttrWidget("CYLI", {
            {"Diameter", 1.0},
            {"Height", 1.0} });

        // CONE
        ui.comboBoxType->addItem(QString("CONE"), QVariant("CONE"));
        addAttrWidget("CONE", {
            {"Dtop", 1.0},
            {"Dbottom", 2.0},
            {"Height", 1.0} });

        // SNOU
        ui.comboBoxType->addItem(QString("SNOU"), QVariant("SNOU"));
        addAttrWidget("SNOU", {
            {"Dtop", 1.0},
            {"Dbottom", 2.0},
            {"Height", 1.0},
            {"Xoffset", 0.0},
            {"Yoffset", 0.0} });

        // PYRA
        ui.comboBoxType->addItem(QString("PYRA"), QVariant("PYRA"));
        addAttrWidget("PYRA", {
            {"Xtop", 1.0},
            {"Ytop", 1.0},
            {"Xbottom", 2.0},
            {"Ybottom", 2.0},
            {"Height", 1.0},
            {"Xoffset", 0.0},
            {"Yoffset", 0.0} });

        // CTOR
        ui.comboBoxType->addItem(QString("CTOR"), QVariant("CTOR"));
        addAttrWidget("CTOR", {
            {"Rinside", 1.0},
            {"Routside", 2.0},
            {"Angle", 360.0} });

        // RTOR
        ui.comboBoxType->addItem(QString("RTOR"), QVariant("RTOR"));
        addAttrWidget("RTOR", {
            {"Rinside", 1.0},
            {"Routside", 2.0},
            {"Height", 1.0},
            {"Angle", 360.0} });

        // DISH
        ui.comboBoxType->addItem(QString("DISH"), QVariant("DISH"));
        addAttrWidget("DISH", {
            {"Diameter", 1.0},
            {"Radius", 0.0},
            {"Height", 1.0} });

        // SLCY
        ui.comboBoxType->addItem(QString("SLCY"), QVariant("SLCY"));
        addAttrWidget("SLCY", {
            {"Diameter", 1.0f},
            {"Height", 1.0f},
            {"Xtshear", 0.0},
            {"Ytshear", 0.0},
            {"Xbshear", 0.0},
            {"Ybshear", 0.0} });

        // SPHE
        ui.comboBoxType->addItem(QString("SPHE"), QVariant("SPHE"));
        addAttrWidget("SPHE", {
            {"Diameter", 1.0} });

        // ELPS
        ui.comboBoxType->addItem(QString("ELPS"), QVariant("ELPS"));
        addAttrWidget("ELPS", {
            {"xDiameter", 1.0},
            {"yDiameter", 1.0},
            {"zDiameter", 1.0} });

        _imgPathMap["BOX"] = "Box";
        _imgPathMap["CYLI"] = "Cylinder";
        _imgPathMap["CONE"] = "Cone";
        _imgPathMap["SNOU"] = "Snout";
        _imgPathMap["PYRA"] = "Pyramid";
        _imgPathMap["CTOR"] = "CircularTorus";
        _imgPathMap["RTOR"] = "RectangularTorus";
        _imgPathMap["DISH"] = "Dish";
        _imgPathMap["SLCY"] = "SlopedCylinder";
        _imgPathMap["SPHE"] = "Sphere";
        _imgPathMap["ELPS"] = "Ellipsoid";
    }

    _positionCaptureHelpter.setCaptureTimes(UiPositionCaptureHelpter::CT_Repeat);
    // 设置坐标拾取对象
    _positionCaptureHelpter.setDoubleSpinBoxXYZ(
        ui.doubleSpinBoxCaptureX
        , ui.doubleSpinBoxCaptureY
        , ui.doubleSpinBoxCaptureZ
    );
    _positionCaptureHelpter.setCheckBoxCapture(ui.checkBoxCapture);
    _positionCaptureHelpter.setCheckBoxXYZ(
        ui.checkBoxPositionX
        , ui.checkBoxPositionY
        , ui.checkBoxPositionZ
    );

    ui.doubleSpinBoxAngle->setMaximum(360);
    ui.doubleSpinBoxAngle->setMinimum(-360);
    QStringList optionsDir;
    optionsDir << "X" << "Y" << "Z" << "-X" << "-Y" << "-Z";
    ui.comboBoxDirection->addItems(optionsDir);
    
    ui.lineEditWrt->setReadOnly(true);
    //setFixedWidth(400);
    resize(400, height());

    // 设置每次切换图像后的显示高度
    _imgShowWidget.setOriginHeight(this->height());
    _imgShowWidget.move(0, 0);

    this->retranslateUi();

    // 构建图像文件与基本体类型的映射
    std::string path = _app.dataDirPath() + std::string("primitiveLegends/");

    connect(ui.comboBoxType
        , QOverload<int>::of(&QComboBox::currentIndexChanged)
        , this
        , &CreatePrimitivesDialog::slotTypeSelectCurrentIndexChanged);

    connect(ui.checkBoxNegative
        , &QCheckBox::clicked
        , this
        , &CreatePrimitivesDialog::slotCheckBoxNegativeClicked);

    connect(ui.pushButtonOk
        , &QPushButton::clicked
        , this
        , &CreatePrimitivesDialog::slotOkButtonClicked);

    connect(ui.pushButtonCancel
        , &QPushButton::clicked
        , this
        , &CreatePrimitivesDialog::reject);
    connect(this, &CreatePrimitivesDialog::sigNeedReanalyzeParent, [&]
        {
            if (_stage)
                return;
            setCaptureTransform();
        });
    connect(ui.pushButtonFinish, &QPushButton::clicked, [&]
        {
            _app.nodeTree().setCurrentNode(_createdNode.lock());
            _createdNode.reset();
            // 移除预览轴
            hideAx();
            turnToTransformSetStage(false);
        });
    connect(ui.comboBoxDirection, &QComboBox::currentTextChanged, [&]
        {
            ui.doubleSpinBoxAngle->setValue(0);
        });
    connect(&_positionCaptureHelpter
        , &UiPositionCaptureHelpter::sigPositionChanged
        , [&]()
        {
            if (!_createdNode.expired())
            {
                auto currNode = _createdNode.lock();
                // 应用位置信息到节点
                _positionCaptureHelpter.applyPositionToNode(currNode
                    , &(_app.undoStack())
                    , [this, currNode](WD::WDNode::SharedPtr, const UiPositionCaptureHelpter&)
                    {
                        showAx(currNode);
                    });
            }
        });
    connect(ui.pushButtonApplyRot, &QPushButton::clicked, [&]
        {
            if (!_createdNode.expired())
            {
                auto pNode = _createdNode.lock();
                QString direction = ui.comboBoxDirection->currentText();
                std::string axisStr = direction.toUtf8().data();
                WD::DVec3 rotateAxis = WD::DVec3::Zero();
                if (!WD::DDirectionParserXYZ::Direction(axisStr, rotateAxis))
                    return;
                // 旋转轴
                WD::DVec3 lRotateAxis = (pNode->globalRotation() * rotateAxis).normalize();
                //旋转角度
                auto angle = ui.doubleSpinBoxAngle->value();
                // undo命令
                auto tFunc = [this, pNode](const WD::WDUndoCommand&)
                    {
                        showAx(pNode);
                    };
                auto pCmd = WD::WDBMBase::MakeRotateCommand({ pNode }, lRotateAxis, angle, pNode->globalTranslation());
                if (pCmd != nullptr)
                {
                    pCmd->setNoticeAfterRedo(tFunc);
                    pCmd->setNoticeAfterUndo(tFunc);
                    // 添加所做操作到undo/redo栈
                    _app.undoStack().push(pCmd);
                }
            }
        });
    connect(ui.pushButtonLegends, &QPushButton::clicked, [&]
        {
            if (_imgShowWidget.isHidden())
                _imgShowWidget.show();
            else
                _imgShowWidget.hide();
        });

    _nameHelpter.setLineEdit(ui.lineEditName);
}
CreatePrimitivesDialog::~CreatePrimitivesDialog()
{
    if (_pPrevWidget != nullptr)
    {
        ui.verticalLayoutParams->removeWidget(_pPrevWidget->getWidget());
        _pPrevWidget = nullptr;
    }
    for (const auto& v : _attrWidgetMap) 
    {
        if (v.second != nullptr)
            delete v.second;
    }
    _attrWidgetMap.clear();
}

void CreatePrimitivesDialog::showEvent(QShowEvent*)
{
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    // 更新参数界面
    this->updateParamsDisplay();
    _app.nodeTree().noticeCurrentNodeChanged() += {this, & CreatePrimitivesDialog::onNodeTreeCurrentNodeChanged};
    turnToTransformSetStage(false);
    _createdNode.reset();
    emit sigNeedReanalyzeParent();
}
void CreatePrimitivesDialog::hideEvent(QHideEvent*)
{
    _app.nodeTree().noticeCurrentNodeChanged() -= {this, &CreatePrimitivesDialog::onNodeTreeCurrentNodeChanged};
    _positionCaptureHelpter.exit(true);
    _app.viewer().browseAxisMgr().put("createPrim");
    turnToTransformSetStage(false);
    _createdNode.reset();
}

void CreatePrimitivesDialog::slotTypeSelectCurrentIndexChanged(int index)
{
    WDUnused(index);
    // 更新参数界面
    this->updateParamsDisplay();
    emit sigNeedReanalyzeParent();
}
void CreatePrimitivesDialog::slotCheckBoxNegativeClicked()
{
    emit sigNeedReanalyzeParent();
}

void CreatePrimitivesDialog::slotOkButtonClicked()
{
    assert(_pPrevWidget != nullptr);
    if (_pPrevWidget == nullptr)
        return;
    // 获取父节点
    auto pParentNode    = _parent.lock();
    if (pParentNode == nullptr)
    {
        WD_ERROR(TrF("Invalid parent"));
        return;
    }
    //  权限校验
    if (!_app.getBMDesign().permissionMgr().check(*pParentNode))
    {
        WD_WARN_T("CreatePrimitivesDialog", "You cannot operate the current node!");
        return;
    }
    // 申领对象
    if (!_app.getBMDesign().claimMgr().checkAdd(pParentNode))
        return;
    // 校验父节点是否存在相同名称
    if (_nameHelpter.exists())
    {
        WD_ERROR(TrF("Same name exists"));
        return;
    }
    // 名称
    std::string name    = _nameHelpter.name();
    // 类型
    auto designType     = this->getDesignType();
    // 空间占有属性 
    auto obstType       = ui.comboBoxObstruction->currentData().toInt();
    // 创建基本体节点
    auto pNode          = this->createPrisNode(pParentNode, designType, name);
    if (pNode == nullptr)
    {
        WD_ERROR(TrF("Create failed"));
        return;
    }
    _createdNode = pNode;
    // 设置空间占有属性
    pNode->setAttribute("Obstruction", WD::WDBMAttrValue(obstType));
    // Obstruction 为字典类型，需要更新字典引用
    _app.getBMDesign().updateRefs(*pNode);
    // 设置节点的默认颜色
    _app.getBMDesign().colorTable().setNodeColor(*pNode);
    // 更新节点
    pNode->triggerUpdate(true);
    _app.nodeTree().setCurrentNode(pNode);

    showAx(pNode);

    // undo/redo
    {
        auto cmdNodeCreated = WD::WDBMBase::MakeCreatedCommand({pNode});
        cmdNodeCreated->setNoticeAfterRedo([this, pNode](const WD::WDUndoCommand&)
            {
                showAx(pNode);
            });
        cmdNodeCreated->setNoticeAfterUndo([this](const WD::WDUndoCommand&)
            {
                hideAx();
            });
        auto cmdAddToScene = WD::WDBMBase::MakeSceneAddCommand({pNode});

        _app.undoStack().beginMarco("Create Primitimves");
        _app.undoStack().push(cmdNodeCreated);
        _app.undoStack().push(cmdAddToScene);
        _app.undoStack().endMarco();
    }
    // 切换界面状态
    turnToTransformSetStage(true);
}

std::string CreatePrimitivesDialog::getDesignType() const
{
    std::string type    = ui.comboBoxType->currentData().toString().toLocal8Bit().data();
    bool isNegative     = ui.checkBoxNegative->isChecked();

    if (type == "BOX") 
    {
        return isNegative ? "NBOX" : "BOX";
    }
    else if (type == "CYLI")
    {
        return isNegative ? "NCYL" : "CYLI";
    }
    else if (type == "CONE")
    {
        return isNegative ? "NCON" : "CONE";
    }
    else if (type == "SNOU")
    {
        return isNegative ? "NSNO" : "SNOU";
    }
    else if (type == "PYRA")
    {
        return isNegative ? "NPYR" : "PYRA";
    }
    else if (type == "CTOR")
    {
        return isNegative ? "NCTO" : "CTOR";
    }
    else if (type == "RTOR")
    {
        return isNegative ? "NRTO" : "RTOR";
    }
    else if (type == "DISH")
    {
        return isNegative ? "NDIS" : "DISH";
    }
    else if (type == "SLCY")
    {
        return isNegative ? "NSLC" : "SLCY";
    }
    else if (type == "SPHE")
    {
        return isNegative ? "NSPH" : "SPHE";
    }
    else if (type == "ELPS")
    {
        return isNegative ? "NELP" : "ELPS";
    }
    else
    {
        return "";
    }
}
WD::WDNode::SharedPtr CreatePrimitivesDialog::getParentNode() const
{
    auto pCurrNode = _app.nodeTree().currentNode();
    if (pCurrNode == nullptr)
        return nullptr;
    auto type = this->getDesignType();
    return _app.getBMDesign().findParentWithType(*pCurrNode, type);
}

void CreatePrimitivesDialog::updateParamsDisplay()
{
    if (_pPrevWidget != nullptr) 
    {
        ui.verticalLayoutParams->removeWidget(_pPrevWidget->getWidget());
        _pPrevWidget->getWidget()->hide();
        _pPrevWidget = nullptr;
    }
    std::string type = ui.comboBoxType->currentData().toString().toLocal8Bit().data();
    auto fItr = _attrWidgetMap.find(type);
    if (fItr != _attrWidgetMap.end())
    {
        auto pCurrWidget = fItr->second;
        if (pCurrWidget != nullptr) 
        {
            ui.verticalLayoutParams->addWidget(pCurrWidget->getWidget());
            pCurrWidget->getWidget()->show();
        }
        _pPrevWidget = pCurrWidget;
    }

    // 更新图例
    _imgShowWidget.setImage(getTypeLegendPath(type.c_str()));
}

void CreatePrimitivesDialog::onNodeTreeCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused(pCurrNode);
    WDUnused(pPrevNode);
    WDUnused(sender);
    emit sigNeedReanalyzeParent();
}

void CreatePrimitivesDialog::retranslateUi()
{
    Trs("CreatePrimitivesDialog"
        , static_cast<QDialog*>(this)
        , ui.labelType
        , ui.labelName
        , ui.labelObstruction
        , ui.comboBoxObstruction
        , ui.labelNegative
        , ui.groupBoxParams
        , ui.pushButtonOk
        , ui.pushButtonCancel
        , ui.groupBoxCapture
        , ui.checkBoxCapture
        , ui.labelWrt
        , ui.labelAngle
        , ui.labelDirection
        , ui.pushButtonFinish
        , ui.pushButtonApplyRot
        , ui.groupBoxRotate
        , ui.pushButtonLegends
        , static_cast<QDialog*>(&_imgShowWidget)
        );

        // 翻译下拉列表中的类型
        for (int i = 0; i < ui.comboBoxType->count(); ++i)
        {
            QString text = ui.comboBoxType->itemText(i);
            QString rText = QString::fromUtf8(_app.getBMDesign().trT(text.toUtf8().toStdString()).c_str());
            ui.comboBoxType->setItemText(i, rText);
        }
}

void CreatePrimitivesDialog::addAttrWidget(const std::string& typeName
    , const std::vector<std::pair<std::string, WD::WDBMAttrValue> >& attrNameInitValues)
{
    auto& bmDesign = _app.getBMDesign();
    auto pType = bmDesign.typeMgr().get(typeName);
    if (pType == nullptr)
    {
        assert(false);
        return;
    }

    std::vector<WD::WDBMAttrDesc*> attrDescs;
    attrDescs.reserve(attrNameInitValues.size());
    for (const auto& nv : attrNameInitValues)
    {
        auto pAttr = pType->get(nv.first);
        if (pAttr == nullptr)
        {
            assert(false);
            continue;
        }
        attrDescs.push_back(pAttr);
    }
    if (attrDescs.empty())
    {
        assert(false);
        return;
    }

    auto pWidget = new NodeAttributeWidget(bmDesign);
    // 属性界面的部分翻译
    pWidget->trFunction() = [](const std::string& key) ->std::string
        {
            return WD::WDTs("CreatePrimitivesDialog", key);
        };
    // 初始化界面
    pWidget->initWidget(attrDescs);
    // 更新初始显示的值
    pWidget->updateDisplayValue(attrNameInitValues);

    _attrWidgetMap[typeName] = pWidget;
}

WD::WDNode::SharedPtr CreatePrimitivesDialog::createPrisNode(WD::WDNode::SharedPtr pParent
    , const std::string& type
    , const std::string& name)
{
    if (_pPrevWidget == nullptr)
    {
        assert(false);
        return nullptr;
    }

    auto& desiMgr = _app.getBMDesign();
    auto pNode = desiMgr.create(pParent, type, name);
    if (pNode == nullptr)
    {
        assert(false);
        return nullptr;
    }
    
    // 应用界面的值到节点
    if (!_pPrevWidget->apply(*pNode, false))
    {
        assert(false);
        return nullptr;
    }

    return pNode;
}

void CreatePrimitivesDialog::setCaptureTransform()
{
    ui.lineEditName->setEnabled(true);
    // 获取合法父节点
    _parent = getParentNode();
    _nameHelpter.resetName();
    if(_parent.expired())
    {
        ui.lineEditName->clear();
        ui.lineEditName->setEnabled(false);
        return ;
    }
    // 显示当前合法父节点
    ui.lineEditWrt->setText(_parent.lock()->name().c_str());

    _positionCaptureHelpter.setTransform(_parent.lock()->globalTransform());
    _positionCaptureHelpter.setPosition({0, 0, 0}, true);
}

void CreatePrimitivesDialog::turnToTransformSetStage(bool turn)
{
    _stage = turn;
    // 设置属性参数界面的节点
    _pPrevWidget->setNode(_createdNode.lock());
    // down掉一些可选项
    ui.comboBoxType->setEnabled(!turn);
    ui.lineEditName->setEnabled(!turn);
    ui.comboBoxObstruction->setEnabled(!turn);
    ui.checkBoxNegative->setEnabled(!turn);
    // 隐藏掉ok/cancel按钮
    ui.pushButtonCancel->setVisible(!turn);
    ui.pushButtonOk->setVisible(!turn);
    // 打开finish按钮以及位置/旋转设置界面
    ui.groupBoxCapture->setVisible(turn);
    ui.groupBoxRotate->setVisible(turn);
    ui.pushButtonFinish->setVisible(turn);

    auto preW = width();
    this->adjustSize();
    this->resize(preW, height());
}

std::string CreatePrimitivesDialog::getTypeLegendPath(const char* type)
{
    auto path = _app.dataDirPath() + std::string("primitiveLegends/");
    auto itr = _imgPathMap.find(type);
    if (itr == _imgPathMap.end())
        return "";
    return path + itr->second + ".png";
}

