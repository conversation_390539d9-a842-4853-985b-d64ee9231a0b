#include "UiComNodePipeComponent.h"
#include "core/WDTranslate.h"
#include <QStack>
#include <QSplitter>
#include <QSettings>
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/message/WDMessage.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "businessModule/WDBMPermissionMgr.h"
#include "core/viewer/WDViewer.h"
#include "core/common/WDDeviation.h"
#include "core/businessModule/WDBMColorTable.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/math/DirectionParser.h"
#include "../../wizDesignerApp/UiInterface/UiSystemNotice.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/math/TKeyPoint.hpp"
#include "core/businessModule/WDBDBase.h"
#include "core/businessModule/WDBMBase.h"

// 分支连接头/尾
static constexpr const char* Head = "Head";
static constexpr const char* Tail = "Tail";

// 分支连接对象类型
static constexpr const char* NOZZ = "NOZZ";
static constexpr const char* TEE  = "TEE";
static constexpr const char* OLET = "OLET";
static constexpr const char* ELBO = "ELBO";
static constexpr const char* REDU = "REDU";
static constexpr const char* FLAN = "FLAN";
static constexpr const char* Multiway = "Multiway";
static constexpr const char* BranchHead = "Branch Head";
static constexpr const char* BranchTail = "Branch Tail";
static constexpr const char* FirstMember = "First Member";
static constexpr const char* LastMember = "Last Member";
static constexpr const char* Name = "Name";
static constexpr const char* Position = "Position";

/**
 * @brief 直接交换管件节点的出入口点
 * 注：慎用!此处直接修改属性，通常用于新增节点准备数据的阶段；需要考虑UNDO/REDO场景
 * @param node 管件节点对象
*/
void SwapArriveLeavePoint(WD::WDNode& node)
{
    auto arrive = node.getAttribute("Arrive");
    node.setAttribute("Arrive", node.getAttribute("Leave"));
    node.setAttribute("Leave", arrive);
}
/**
* @brief 节点是否在分支上
*/
static bool onBranch(WD::WDCore& core, WD::WDNode& node)
{
    auto pParent = core.getBMDesign().findParentWithType(node, "TUBI");
    if(pParent == nullptr)
        return false;
    if (pParent->isType("BRAN"))
        return true;

    return false;
}
/**
 * @brief 检查节点的某个关键点连接是否正确（管件间不存在虚线则为连接正确）
 * @param node 管件类型节点
 * @param isArrived  是否为入口点
 * @return 连接正常与否
*/
static bool CheckConnectState(WD::WDNode& node, bool isArrived)
{
    // 获取分支节点
    auto pBranch = node.parent();
    if (pBranch == nullptr)
        return false;

    if (!WD::WDBMDPipeUtils::IsPipeComponent(node))
        return false;

    const auto& gTrans = node.globalTransform();
    WD::DVec3 srcDir, srcPos, destDir, destPos;
    if (isArrived)
    {
        // 获取入口点坐标和朝向
        auto pAPoint = node.keyPoint(node.getAttribute("Arrive").toInt());
        if (pAPoint != nullptr)
        {
            srcPos = pAPoint->transformedPosition(gTrans);
            srcDir = pAPoint->transformedDirection(gTrans);
        }
        // 获取node的前一个节点
        auto pPrevNode = WD::WDBMDPipeUtils::PrevPipeComponent(pBranch, WD::WDNode::ToShared(&node), { std::string("TUBI") });
        if (pPrevNode == nullptr)
        {
            // pPrevNode为空，则node为分支下的第一个管件
            // 获取分支头的坐标和朝向
            bool ok = false;
            auto v = pBranch->getAttribute("Hposition WRT World").toDVec3(&ok);
            if(ok)
                destPos = v;
            v = pBranch->getAttribute("Hdirection WRT World").toDVec3(&ok);
            if(ok)
                destDir = v;
        }
        else
        {
            // 获取pPrevNode出口点的坐标和朝向
            if (WD::WDBMDPipeUtils::IsPipeComponent(*pPrevNode))
            {
                auto pPrevNodeLPoint = pPrevNode->keyPoint(pPrevNode->getAttribute("Leave").toInt());
                if (pPrevNodeLPoint != nullptr)
                {
                    const auto& pPrevNodeGTrans = pPrevNode->globalTransform();
                    destPos = pPrevNodeLPoint->transformedPosition(pPrevNodeGTrans);
                    destDir = pPrevNodeLPoint->transformedDirection(pPrevNodeGTrans);
                }
            }
        }
    }
    else
    {
        // 获取出口点坐标和朝向
        auto pLPoint = node.keyPoint(node.getAttribute("Leave").toInt());
        if (pLPoint != nullptr)
        {
            srcPos = pLPoint->transformedPosition(gTrans);
            srcDir = pLPoint->transformedDirection(gTrans);
        }
        // 获取后一个管件
        auto pNextNode = WD::WDBMDPipeUtils::NextPipeComponent(pBranch, WD::WDNode::ToShared(&node), { std::string("TUBI") });
        if (pNextNode == nullptr)
        {
            // pNextNode为空，则node为分支下的最后一个管件
            // 获取分支尾的坐标和朝向
            bool ok = false;
            auto v = pBranch->getAttribute("Tposition WRT World").toDVec3(&ok);
            if(ok)
                destPos = v;
            v = pBranch->getAttribute("Tdirection WRT World").toDVec3(&ok);
            if(ok)
                destDir = v;
        }
        else
        {
            // 获取pNextNode入口点的坐标和朝向
            if (WD::WDBMDPipeUtils::IsPipeComponent(*pNextNode))
            {
                auto pPrevNodeAPoint = pNextNode->keyPoint(pNextNode->getAttribute("Arrive").toInt());
                if (pPrevNodeAPoint != nullptr)
                {
                    const auto& pNextNodeGTrans = pNextNode->globalTransform();
                    destPos = pPrevNodeAPoint->transformedPosition(pNextNodeGTrans);
                    destDir = pPrevNodeAPoint->transformedDirection(pNextNodeGTrans);
                }
            }
        }
    }

    auto nRet = WD::TKeyPoint<double>::CheckConnectionTo(srcPos, srcDir, destPos, destDir, WD::WDDeviation::GetDistDeviation(WD::DVec3::Distance(srcPos, destPos)));
    return nRet.first;
}

/**
 * @brief 获取节点在指定方向上的最高最低点
 * @param node 节点
 * @param outTPoint 模型在向量方向最远的点在向量所在直线的投影点
 * @param outBPoint 模型在向量方向最近的点在向量所在直线的投影点
 * @param direction 指定的方向,默认为Z轴,必须传入单位向量
 * @return 获取是否成功
*/
bool GetNodeTopBottonPoint(const WD::WDNode& node
    , WD::DVec3& outTPoint
    , WD::DVec3& outBPoint
    , const WD::DVec3& direction = WD::DVec3::AxisZ())
{
    auto pBase = node.getBDBase();
    if (pBase == nullptr)
        return false;
    auto pGraphableSupporter = pBase->graphableSupporter();
    if (pGraphableSupporter == nullptr)
        return false;
    const auto pRGeoms =  pGraphableSupporter->gRenderGeoms();
    if (pRGeoms == nullptr || pRGeoms->empty())
        return false;

    double minDotVal = WD::NumLimits<double>::Max;
    double maxDotVal = WD::NumLimits<double>::Lowest;

    const auto& transform = node.globalTransform();

    for (auto& pGeo : (*pRGeoms))
    {
        if (pGeo == nullptr)
            continue;
        // 排除掉负实体
        if (pGeo->gFlags().hasFlag(WD::WDGeometry::GF_Negative))
            continue;

        auto pMesh = pGeo->mesh();
        if (pMesh == nullptr)
            continue;

        const auto& allPos = pMesh->positions();
        if (allPos.empty())
            continue;

        auto transMat = transform * pGeo->transform();

        std::vector<WD::DVec3> positions;
        positions.reserve(allPos.size());
        for (const auto& pos: allPos)
            positions.push_back(transMat * WD::DVec3(pos));

        auto& pris = pMesh->primitiveSets(WD::WDMesh::Solid);
        if (pris.empty())
            continue;
        for (const auto& pri : pris)
        {
            if (pri.empty())
                continue;
            switch (pri.primitiveType())
            {
            case WD::WDPrimitiveSet::PT_Triangles:
                {
                    switch (pri.drawType())
                    {
                    case WD::WDPrimitiveSet::DrawType::DT_Array:
                        {
                            auto&   priData =   pri.drawArrayData();
                            uint    nStart  =   priData.first;
                            uint    nEnd    =   priData.first + priData.second;

                            for (size_t i = nStart; i < nEnd; i += 3)
                            {
                                std::array<WD::DVec3, 3> tmpPoints = {positions[i + 0], positions[i + 1], positions[i + 2]};

                                for (auto& eachPoint : tmpPoints)
                                {
                                    auto tDot = WD::DVec3::Dot(eachPoint, direction);
                                    minDotVal = WD::Min(minDotVal, tDot);
                                    maxDotVal = WD::Max(maxDotVal, tDot);
                                }
                            }
                        } 
                        break;
                    case WD::WDPrimitiveSet::DrawType::DT_ElementByte:
                        {
                            const auto& indices = pri.drawElementByteData();
                            for (size_t i = 0; i < indices.size(); i += 3) 
                            {
                                std::array<WD::DVec3, 3> tmpPoints = {positions[indices[i + 0]], positions[indices[i + 1]], positions[indices[i + 2]]};

                                for (auto& eachPoint : tmpPoints)
                                {
                                    auto tDot = WD::DVec3::Dot(eachPoint, direction);
                                    minDotVal = WD::Min(minDotVal, tDot);
                                    maxDotVal = WD::Max(maxDotVal, tDot);
                                }
                            }
                        }
                        break;
                    case WD::WDPrimitiveSet::DrawType::DT_ElementUShort:
                        {
                            const auto& indices = pri.drawElementUShortData();
                            for (size_t i = 0; i < indices.size(); i += 3)
                            {
                                std::array<WD::DVec3, 3> tmpPoints = {positions[indices[i + 0]], positions[indices[i + 1]], positions[indices[i + 2]]};

                                for (auto& eachPoint : tmpPoints)
                                {
                                    auto tDot = WD::DVec3::Dot(eachPoint, direction);
                                    minDotVal = WD::Min(minDotVal, tDot);
                                    maxDotVal = WD::Max(maxDotVal, tDot);
                                }
                            }
                        }
                        break;
                    case WD::WDPrimitiveSet::DrawType::DT_ElementUInt:
                        {
                            const auto& indices = pri.drawElementUIntData();
                            for (size_t i = 0; i < indices.size(); i += 3)
                            {
                                std::array<WD::DVec3, 3> tmpPoints = {positions[indices[i + 0]], positions[indices[i + 1]], positions[indices[i + 2]]};

                                for (auto& eachPoint : tmpPoints)
                                {
                                    auto tDot = WD::DVec3::Dot(eachPoint, direction);
                                    minDotVal = WD::Min(minDotVal, tDot);
                                    maxDotVal = WD::Max(maxDotVal, tDot);
                                }
                            }
                        }
                        break;
                    default:
                        break;
                    }
                }
                break;
            case WD::WDPrimitiveSet::PT_TriangleStrip:
                {
                    auto&   priData =   pri.drawArrayData();
                    uint    nStart  =   priData.first;
                    uint    nEnd    =   priData.first + priData.second;
                    for (uint i = nStart; i < nEnd; ++i)
                    {
                        const auto& eachPoint = positions[i];
                        auto tDot = WD::DVec3::Dot(eachPoint, direction);
                        minDotVal = WD::Min(minDotVal, tDot);
                        maxDotVal = WD::Max(maxDotVal, tDot);
                    }
                }
                break;
            case WD::WDPrimitiveSet::PT_TriangleFan:
                {
                    auto&   priData =   pri.drawArrayData();
                    uint    nStart  =   priData.first;
                    uint    nEnd    =   priData.first + priData.second;
                    for (uint i = nStart; i < nEnd; ++i)
                    {
                        const auto& eachPoint = positions[i];
                        auto tDot = WD::DVec3::Dot(eachPoint, direction);
                        minDotVal = WD::Min(minDotVal, tDot);
                        maxDotVal = WD::Max(maxDotVal, tDot);
                    }
                }
                break;
            default:
                break;
            }
        }
    }
    
    if (minDotVal == WD::NumLimits<double>::Max || maxDotVal == WD::NumLimits<double>::Min || minDotVal > maxDotVal)
        return false;

    outBPoint = direction * minDotVal;
    outTPoint = direction * maxDotVal;

    return true;
}

EnterComboBox::EnterComboBox(QWidget *parent) : QComboBox(parent)
{
}
void EnterComboBox::keyPressEvent(QKeyEvent* event)
{
    if (event->type() == QEvent::KeyPress)
    {
        QKeyEvent* keyEvent = static_cast<QKeyEvent*>(event);
        if (keyEvent != nullptr)
        {
            if (keyEvent->key() == Qt::Key_Return || keyEvent->key() == Qt::Key_Enter)
            {
                auto pLineEdit = this->lineEdit();
                if(pLineEdit != nullptr)
                {
                    emit sigEnterPressed(pLineEdit->text());
                }
                else
                {
                    emit sigEnterPressed("");
                }
            }
            else
            {
                QComboBox::keyPressEvent(event);
            }
        }
    }
}

UiComNodePipeComponent::UiComNodePipeComponent(IMainWindow& mainWindow,  QWidget *parent)
    : QDialog(parent)
    , _mainWindow(mainWindow)
    , _core(mainWindow.core())
    , _captureFunc(CF_Similar)
{
    ui.setupUi(this);
    _pSELE.reset();
    _pSimilarCurr.reset();

    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    // 添加最大最小化
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowMinMaxButtonsHint, true));
    //定位/对齐算法
    _thros.push_back(new WD::ThroCE(_core));
    _thros.push_back(new WD::ThroCursor(_core));
    _thros.push_back(new WD::ThroIDCursor(_core));
    _thros.push_back(new WD::ThroPoint(_core));
    _thros.push_back(new WD::ThroNext(_core));
    _thros.push_back(new WD::ThroPrevious(_core));
    _thros.push_back(new WD::ThroHead(_core));
    _thros.push_back(new WD::ThroTail(_core));
    _thros.push_back(new WD::ThroConnect(_core));

    // 初始化分支连接的下拉框选项
    _connectHeadTailType = WD::Connect_Head;
    _connectObjType = NOZZ;
    initBranConnect();

    // 子界面
    _pParamsWidget = new UiComCommonParamsWidget(_core, this);
    ui.verticalLayout->insertWidget(0, _pParamsWidget);
    _branchEndingWidget = new UiComNodeBranchEnding(this);
    _pSelectPipeDialog = new SelectPipeDialog(_core, this);
    _pSelectPipeDialog->hide();
    _pSelectPipeDialog->setModal(true);

    _pNameTypeDialog = new PipeNameTypeDialog(parent);
    _pPositionTypeDialog = new PipePositionTypeDialog(_core, this);

    // 界面翻译
    this->retranslateUi();

    // 等级
    connect(_pParamsWidget
        , SIGNAL(sigSPECChanged())
        , this
        , SLOT(slotSpecNodeChanged()));
    // 类型
    connect(ui.listWidgetType
        , SIGNAL(currentItemChanged(QListWidgetItem*, QListWidgetItem*))
        , this
        , SLOT(slotListWidgetTypeCurrentItemChanged(QListWidgetItem*, QListWidgetItem*)));
    /// 以下三种调整方式实时生效
    // 出口朝向
    connect(ui.orientationComboBox
        , SIGNAL(activated(int))
        , this
        , SLOT(slotLeaveDirectActivated(int)));
    // 旋转
    _pComboBoxRotAngel = new EnterComboBox(this);
    _pComboBoxRotAngel->setEditable(true);
    _pComboBoxRotAngel->setObjectName(QString::fromUtf8("flipAnglecomboBox"));
    ui.verticalLayoutRotAngle->addWidget(_pComboBoxRotAngel);
    connect(_pComboBoxRotAngel, &EnterComboBox::sigEnterPressed, this, &UiComNodePipeComponent::slotAngleEnterPressed);
    connect(_pComboBoxRotAngel,SIGNAL(activated(int)),this, SLOT(slotRotationAngleChanged(int)));
    _pComboBoxRotAngel->addItem("15", 15.0);
    _pComboBoxRotAngel->addItem("45", 45.0);
    _pComboBoxRotAngel->addItem("90", 90.0);
    _pComboBoxRotAngel->addItem("180", 180.0);

    // 端面距离/中心距离 
    _pComboBoxDistance = new EnterComboBox(this);
    _pComboBoxDistance->setEditable(true);
    _pComboBoxDistance->setObjectName(QString::fromUtf8("distanceComboBox"));
    ui.verticalLayoutDistance->addWidget(_pComboBoxDistance);
    connect(_pComboBoxDistance, &EnterComboBox::sigEnterPressed, this, &UiComNodePipeComponent::slotComboBoxDistanceEnterPressed);
    connect(_pComboBoxDistance, SIGNAL(activated(int)), this, SLOT(slotDiatanceIndexChanged(int)));
    _pComboBoxDistance->addItem("0", 0.0);
    _pComboBoxDistance->addItem("100", 100.0);
    _pComboBoxDistance->addItem("150", 150.0);
    _pComboBoxDistance->addItem("200", 200.0);

    // 定位
    connect(ui.throComboBox, SIGNAL(activated(int)),this,SLOT(slotThroActivated(int)));

    // 按钮组响应
    connect(ui.pushButtonCreate, &QPushButton::clicked, this, &UiComNodePipeComponent::slotCreatePushButton);
    connect(ui.pushButtonTopBottomFlatFlip, &QPushButton::clicked, this, &UiComNodePipeComponent::slotTopBottomFlatFlipPushButtonClicked);
    connect(ui.pushButtonPipeBottomElevation, &QPushButton::clicked, this, &UiComNodePipeComponent::slotPipeBottomElevationPushButtonClicked);
    connect(ui.pushBtnKits, &QPushButton::clicked, this, &UiComNodePipeComponent::slotCreateVALVKits);
    connect(ui.pushBtnConnect, &QPushButton::clicked, this, &UiComNodePipeComponent::slotBtnConnectClicked);
    connect(ui.pushButtonCopy, &QPushButton::clicked, this, &UiComNodePipeComponent::slotBtnCopyClicked);
    connect(ui.pushBtnSimilar, &QPushButton::clicked, this, &UiComNodePipeComponent::slotBtnSimilarClicked);
    connect(ui.pushButtonReselect, &QPushButton::clicked, this, &UiComNodePipeComponent::slotBtnReselectClicked);
    connect(ui.flipPushButton, &QPushButton::clicked, this, &UiComNodePipeComponent::slotFlipPushButton);
    connect(ui.pushButtonElbowSlope, &QPushButton::clicked, this, &UiComNodePipeComponent::slotPushButtonElbowSlopeClicked);
    connect(ui.pushButtonEnding, &QPushButton::clicked, this, &UiComNodePipeComponent::slotPushButtonEndingClicked);
    connect(ui.pushButtonFor, &QPushButton::clicked, this, &UiComNodePipeComponent::slotForBtnMoveClicked);
    connect(ui.pushButtonBack, &QPushButton::clicked, this, &UiComNodePipeComponent::slotBackBtnMoveClicked);
    connect(ui.pushBtnCreateBRAN, &QPushButton::clicked, this, &UiComNodePipeComponent::slotBtnCreateBRANClicked);
    connect(ui.comboBoxHeadTail, SIGNAL(currentIndexChanged(int)), this, SLOT(slotHeadTailChanged(int)));
    connect(ui.comboBoxConnectObjType, SIGNAL(activated(int)), this, SLOT(slotConnectObjTypeChanged(int)));
    connect(_pPositionTypeDialog, SIGNAL(sigOKClicked()), this, SLOT(onPositionOKClicked()));
}
UiComNodePipeComponent::~UiComNodePipeComponent()
{
    _pSELE.reset();
    _pSimilarCurr.reset();
    if (_pComboBoxRotAngel != nullptr)
    {
        _pComboBoxRotAngel->deleteLater();
    }
    if (_pComboBoxDistance != nullptr)
    {
        _pComboBoxDistance->deleteLater();
    }
    if (_pParamsWidget != nullptr)
    {
        _pParamsWidget->deleteLater();
    }
    if (_branchEndingWidget != nullptr)
    {
        _branchEndingWidget->deleteLater();
    }
    if(_pSelectPipeDialog != nullptr)
    {
        delete _pSelectPipeDialog;
        _pSelectPipeDialog = nullptr;
    }
    if (_pNameTypeDialog != nullptr)
    {
        delete _pNameTypeDialog;
        _pNameTypeDialog = nullptr;
    }
    if (_pPositionTypeDialog != nullptr)
    {
        delete _pPositionTypeDialog;
        _pPositionTypeDialog = nullptr;
    }
        
    while (!_thros.empty())
    {
        if (_thros.back() != nullptr)
            delete _thros.back();
        _thros.pop_back();
    }
}

void UiComNodePipeComponent::showEvent(QShowEvent* evt)
{
    // 初始化类型表格
    slotSpecNodeChanged();

    //添加节点树当前item改变通知
    _core.nodeTree().noticeCurrentNodeChanged() += {this, & UiComNodePipeComponent::onCurrentNodeChanged};

    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode != nullptr && pCurrentNode->isType("BRAN"))
    {
        _pBranch = pCurrentNode;
    }
    else
    {
        auto pParent = _core.getBMDesign().findParentWithType(*pCurrentNode, "TUBI");
        if (pParent == nullptr)
        {
            _pBranch.reset();
        }
        else
        {
            _pBranch = pParent;
        }
    }

    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus();
    // 设置选中SPEC
    this->setSelectSPEC();
    // 恢复窗口位置
    QSettings settings("ShenZhenPengRui", "WizDesigner");
    restoreGeometry(settings.value("UiComNodePipeComponent/geometry").toByteArray());
    QDialog::showEvent(evt);
}
void UiComNodePipeComponent::hideEvent(QHideEvent* evt)
{
    //添加节点树当前item改变通知
    _core.nodeTree().noticeCurrentNodeChanged() -= {this, & UiComNodePipeComponent::onCurrentNodeChanged};

    int throIndex = ui.throComboBox->currentIndex();
    if (throIndex >= 0 && throIndex < _thros.size())
    {
        _thros[throIndex]->exit();
    }

    // 将分支连接改为默认的头/管嘴方式
    ui.comboBoxHeadTail->setCurrentIndex(0);
    ui.comboBoxConnectObjType->blockSignals(true);
    ui.comboBoxConnectObjType->setCurrentIndex(0);
    ui.comboBoxConnectObjType->blockSignals(false);

    // 保存窗口位置
    QSettings settings("ShenZhenPengRui", "WizDesigner");
    settings.setValue("UiComNodePipeComponent/geometry", saveGeometry());
    QDialog::hideEvent(evt);
}

void UiComNodePipeComponent::onResult(const WD::WDCapturePositioningResult& result
    , bool& existFlag
    , const WD::WDCapturePositioning& sender)
{
    WDUnused(sender);
    WD::WDNode::SharedPtr pNode = result.node.lock();
    if (pNode == nullptr)
        return ;

    switch(_captureFunc)
    {
    case CF_AutoElbo:
    {
        // 支持ELBO 和 BEND类型
        if (!pNode->isType("ELBO") && !pNode->isType("BEND"))
        {
            WD_ERROR_T("ErrorUiComNodePipeComponent", "Must select elbo/bend!");
            return ;
        }
        // 申领节点
        bool bCancelMd = false;
        if (!_core.getBMDesign().claimMgr().checkUpdate(pNode, { "Angle" }, bCancelMd))
            return;
        if (bCancelMd)
            return;

        WD::WDNode::SharedPtr pBran = nullptr;
        if (_slopeComs.size() == 1)
        {
            pBran = _slopeComs.front().lock()->parent();
            auto pBran1 = pNode->parent();
            if (pBran != pBran1)
            {
                WD_ERROR_T("ErrorUiComNodePipeComponent", "Must same branch!");
                return ;
            }
        }

        _slopeComs.push_back(pNode);

        if (_slopeComs.size() == 2)
        {
            //设置捕捉退出
            existFlag = true;
            if (pBran == nullptr)
                return ;
            // 判断管件顺序
            auto pComA = _slopeComs.front().lock();
            auto pComB = _slopeComs.back().lock();
            int indexA = -1;
            int indexB = -1;
            for (int i = 0; i < static_cast<int>(pBran->childCount()); ++i)
            {
                auto pChild = pBran->childAt(i);
                if (pChild == nullptr)
                    continue;
                if (pChild == pComA)
                    indexA = i;
                else if (pChild == pComB)
                    indexB = i;
                if (indexA != -1 && indexB != -1)
                    break;
            }
            if (indexA == -1 || indexB == -1)
            {
                WD_ERROR_T("ErrorUiComNodePipeComponent", "Must same branch!");
                return;
            }
            if (indexA == indexB) 
            {
                assert(false);
                return;
            }
            int minIdx = indexA < indexB ? indexA : indexB;
            int maxIdx = indexA > indexB ? indexA : indexB;
            // 如果前一个管件和后一个管件之间存在其他非ATTA类型的管件，则报错
            if (maxIdx - minIdx > 1)
            {
                for (int i = minIdx + 1; i < maxIdx; ++i)
                {
                    if (i >= pBran->childCount())
                    {
                        assert(false);
                        return;
                    }
                    auto pChild = pBran->childAt(i);
                    if (!pChild->isType("ATTA"))
                    {
                        WD_ERROR_T("ErrorUiComNodePipeComponent", "Must adjacent!");
                        return;
                    }
                }
            }
            WD::WDNode::SharedPtr pPrev = indexA < indexB ? pComA : pComB;
            WD::WDNode::SharedPtr pNext = indexA > indexB ? pComA : pComB;

            if (pPrev == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pPrev)
                || pNext == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pNext))
                return ;
            // 计算前一个弯头到后一个弯头的方向
            auto slopeDir       =   WD::DVec3::Normalize(pNext->globalTranslation() - pPrev->globalTranslation());

            auto func = [pPrev, pNext, pBran, this](const WD::DVec3& leaveDir, const WD::DVec3& arriveDir)
            {
                if (pPrev == nullptr || pNext == nullptr)
                    return ;
                WD::SetElboLeaveDirection(*pPrev, leaveDir);
                WD::SetElboArriveDirection(*pNext, arriveDir);
                //更新分支的连接
                //pBran->triggerUpdate(true);
                //_core.needRepaint();
            };

            auto elbo1PosBefore = pPrev->getAttribute("Position");
            auto elbo1DirBefore = pPrev->getAttribute("Orientation");
            auto elbo1AngBefore = pPrev->getAttribute("Angle");
            auto elbo2PosBefore = pNext->getAttribute("Position");
            auto elbo2DirBefore = pNext->getAttribute("Orientation");
            auto elbo2AngBefore = pNext->getAttribute("Angle");
            func(slopeDir, -slopeDir);
            auto elbo1PosAfter = pPrev->getAttribute("Position");
            auto elbo1DirAfter = pPrev->getAttribute("Orientation");
            auto elbo1AngAfter = pPrev->getAttribute("Angle");
            auto elbo2PosAfter = pNext->getAttribute("Position");
            auto elbo2DirAfter = pNext->getAttribute("Orientation");
            auto elbo2AngAfter = pNext->getAttribute("Angle");
            // 模拟撤销一次
            pPrev->setAttribute("Position", elbo1PosBefore);
            pPrev->setAttribute("Orientation", elbo1DirBefore);
            pPrev->setAttribute("Angle", elbo1AngBefore);
            pNext->setAttribute("Position", elbo2PosBefore);
            pNext->setAttribute("Orientation", elbo2DirBefore);
            pNext->setAttribute("Angle", elbo2AngBefore);

            // undo/redo
            auto bm = _core.currentBM();
            if (bm != nullptr)
            {
                auto cmd = new WD::WDUndoCommand("AutoElbo");
                if(cmd != nullptr)
                {
                    WD::WDBMBase::MakeAttributeSetCommand(pPrev, "Position", elbo1PosAfter, cmd);
                    WD::WDBMBase::MakeAttributeSetCommand(pPrev, "Orientation", elbo1DirAfter, cmd);
                    WD::WDBMBase::MakeAttributeSetCommand(pPrev, "Angle", elbo1AngAfter, cmd);
                    WD::WDBMBase::MakeAttributeSetCommand(pNext, "Position", elbo2PosAfter, cmd);
                    WD::WDBMBase::MakeAttributeSetCommand(pNext, "Orientation", elbo2DirAfter, cmd);
                    WD::WDBMBase::MakeAttributeSetCommand(pNext, "Angle", elbo2AngAfter, cmd);
                    cmd->setNoticeAfterRedo([=](const WD::WDUndoCommand&)
                        {
                            if (pPrev != nullptr)
                                pPrev->updateModel();
                            if (pNext != nullptr)
                                pNext->updateModel();
                            if (pBran != nullptr)
                                pBran->triggerUpdate(true);
                            _core.needRepaint();
                        });
                    cmd->setNoticeAfterUndo([=](const WD::WDUndoCommand&)
                        {
                            if (pPrev != nullptr)
                                pPrev->updateModel();
                            if (pNext != nullptr)
                                pNext->updateModel();
                            if (pBran != nullptr)
                                pBran->triggerUpdate(true);
                            _core.needRepaint();
                        });
                    _core.undoStack().push(cmd);
                }
            }
        }
    }
    break;
    case CF_Similar:
    {
        // 设置退出捕捉助手标志
        existFlag = true;

        // 场景中拾取的节点非管件类型，则退出拾取状态，不创建相似管件
        if(!WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
            return;

        auto pCurrentNode = _pSimilarCurr.lock();
        if (pCurrentNode == nullptr)
            return;

        // 克隆业务数据对象，创建节点根据插入顺序插入到分支中
        auto& desiMgr = _core.getBMDesign();
        WD::WDNode::SharedPtr pBran = nullptr;
        if(pCurrentNode->isType("BRAN"))
        {
            pBran = pCurrentNode;
        }
        else
        {
            pBran = pCurrentNode->parent();
        }
        if (pBran == nullptr)
            return;
        WD::WDNode::SharedPtr pNext = nullptr;
        switch (this->getInsertionSequence())
        {
        case WD::PipeFlowDir::PFD_Forward:
            {
                if (WD::WDBMDPipeUtils::IsPipeComponent(*pCurrentNode))
                {
                    pNext = WD::WDBMDPipeUtils::NextPipeComponent(pBran, pCurrentNode);
                }
            }
            break;
        case WD::PipeFlowDir::PFD_Backward:
            {
                if (WD::WDBMDPipeUtils::IsPipeComponent(*pCurrentNode))
                {
                    pNext = pCurrentNode;
                }
            }
            break;
        default:
            break;
        }
        // 申领对象
        if (!_core.getBMDesign().claimMgr().checkAdd(pBran, pNext))
            return;

        WD::WDNode::SharedPtr pNewNode = pNode->cloneT<WD::WDNode>();
        if (pNewNode == nullptr)
            return;
        pNewNode->setAttribute("Name", std::string(""));

        desiMgr.setParent(pNewNode, pBran, pNext, false);

        // 设置复制成功的管件为当前选中节点
        _core.nodeTree().setCurrentNode(pNewNode);

        pNewNode->triggerUpdate();
        // 重新连接管件
        this->restoreConnection(pNewNode, ui.autoConnectCheckBox->isChecked());

        auto cmdNodeCreated = WD::WDBMBase::MakeCreatedCommand({pNewNode});
        cmdNodeCreated->setNoticeAfterRedo([this, pNewNode](const WD::WDUndoCommand&){
            // 设置复制成功的管件为当前选中节点
            _core.nodeTree().setCurrentNode(pNewNode);
            _core.needRepaint(); 
        });
        cmdNodeCreated->setNoticeAfterUndo([this](const WD::WDUndoCommand&){ _core.needRepaint(); });
        if (cmdNodeCreated != nullptr)
            _core.undoStack().push(cmdNodeCreated);
    }
    break;
    case CF_BranConnect:
    {
        // 连接分支的头或尾，获取实际的连接对象（分支节点）
        if (_connectObjType == BranchHead || _connectObjType == BranchTail)
        {
            pNode = _core.getBMDesign().findParentWithType(*pNode, "TUBI");
        }
        this->branchConnect(pNode);
        _core.needRepaint();
        //设置捕捉退出
        existFlag = true;
    }
    break;
    case CF_PipeBottomElevation:
        {
            existFlag = true;
            auto pCurrentNode = _pCurrentNode.lock();
            if (pCurrentNode == nullptr)
                return;
            double diam = 0.0;
            const WD::WDKeyPoint* pKeyPt = nullptr;
            switch (this->getInsertionSequence())
            {
            case WD::PipeFlowDir::PFD_Forward:
                {
                    diam = pCurrentNode->getAttribute("Lodiam").toDouble();
                    pKeyPt = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Leave").toInt());
                }
                break;
            case WD::PipeFlowDir::PFD_Backward:
                {
                    diam = pCurrentNode->getAttribute("Aodiam").toDouble();
                    pKeyPt = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Arrive").toInt());
                }
                break;
            default:
                break;
            }
            if (pKeyPt == nullptr)
                return;
            // Z方向
            auto axisZ = WD::DVec3::AxisZ();
            // 获取世界坐标的关键点
            auto pos = pKeyPt->transformedPosition(pCurrentNode->globalTransform());
            auto dir = pKeyPt->transformedDirection(pCurrentNode->globalTransform());
            WD::DVec3 targetNodeTPos;
            WD::DVec3 targetNodeBPos;
            // 如果捕捉的是轴网的线,则取当前捕捉的线或点的最高点和最低的
            if (pNode->isType("GRIDSY")
                || (pNode->isType("SCTN") && pNode->getAttribute("Spref").toNodeRef().refNode() == nullptr ))
            {
                std::vector<WD::DVec3> points;
                if (result.pLineData)
                {
                    const auto& lines = result.pLineData.value();
                    if (!lines.indexValid())
                        return;
                    const auto& line = lines.pLines[lines.index];
                    points = {line.ePosition, line.sPosition};
                }
                else if (result.segmentData)
                {
                    const auto& segment = result.segmentData.value();
                    points = {segment.start, segment.end};
                }
                else if (result.keyPointData)
                {
                    const auto& keyPoints = result.keyPointData.value();
                    if (!keyPoints.indexValid())
                        return;
                    const auto& keyPoint = keyPoints.keyPoints[keyPoints.index];
                    points = {keyPoint.position};
                }
                else
                {
                    assert(false);
                    return;
                }

                double minDotVal = WD::NumLimits<double>::Max;
                double maxDotVal = WD::NumLimits<double>::Lowest;
                for (auto& eachPoint : points)
                {
                    auto point = WD::DVec3(eachPoint);
                    auto tDot = WD::DVec3::Dot(point, axisZ);
                    minDotVal = WD::Min(minDotVal, tDot);
                    maxDotVal = WD::Max(maxDotVal, tDot);
                }
                if (minDotVal == WD::NumLimits<double>::Max || maxDotVal == WD::NumLimits<double>::Min || minDotVal > maxDotVal)
                {
                    assert(false);
                    return;
                }
                targetNodeBPos = axisZ * minDotVal;
                targetNodeTPos = axisZ * maxDotVal;
            }
            // 否则获取捕捉的节点的最高点和最低点
            else if (!GetNodeTopBottonPoint(*pNode, targetNodeTPos, targetNodeBPos, axisZ))
            {
                assert(false);
                return;
            }
            // 竖直向下的向量
            auto downOff = WD::DVec3::Zero();
            // 这里根据出口点/入口点方向与XOY平面的夹角来计算downOff的旋转
            auto tDir = WD::DVec3(dir.x, dir.y, 0.0);
            // 说明方向是向上或者向下，这种情况下直接使用关键点的位置,因此downOff应该赋值为0
            if (tDir.lengthSq() <= 0.00001) 
            {
                downOff = WD::DVec3::Zero();
            }
            // 旋转
            else
            {
                // 单位化
                tDir.normalize();
                // 计算旋转四元数
                auto rot = WD::DQuat::FromVectors(tDir, dir);
                // 旋转朝向
                auto downV = rot * WD::DVec3::AxisNZ();
                downOff = diam * 0.5 * downV;
            }
            // 最低点
            pos += downOff;
            // 计算偏移量
            auto tarGetPos = targetNodeTPos + axisZ * ui.doubleSpinBoxPipeBottomElevation->value();
            auto offset = axisZ * (tarGetPos - pos);
            if (offset.length() > WD::NumLimits<double>::Epsilon)
            {
                auto pCommand = WD::WDBMBase::MakeMoveCommand({pCurrentNode}, offset);
                if (pCommand != nullptr)
                {
                    pCommand->setNoticeAfterRedo([&](const WD::WDUndoCommand&)
                        {
                            _core.needRepaint();
                        });
                    pCommand->setNoticeAfterUndo([&](const WD::WDUndoCommand&)
                        {
                            _core.needRepaint();
                        });
                    _core.undoStack().push(pCommand);
                }
            }
        }
        break;
    }
}
void UiComNodePipeComponent::onDeactived(const WD::WDCapturePositioning& sender)
{
    WDUnused(sender);
    _slopeComs.clear();
}

using SelectDialogParam = std::pair<WD::WDNode::SharedPtr, std::string>;
using SelectDialogParams = std::vector<SelectDialogParam>;
using TestCheckNextFunction = std::function<bool(size_t index, SelectDialogParams& params, WD::WDNode& retSpcoNode)>;
std::vector<WD::WDNode::SharedPtr> SelectSpcos( SelectDialogParams& params
    , SelectPipeDialog& selectPipeDialog
    , bool bShowPipeComDesc
    , TestCheckNextFunction funcCheckNext)
{
    std::vector<WD::WDNode::SharedPtr> rNodes;
    rNodes.resize(params.size());
    std::fill(rNodes.begin(), rNodes.end(), nullptr);

    // 根据创建kit的流程，获取选择的节点连接类型匹配的节点
    for (size_t i = 0; i < params.size(); ++i)
    {
        // 等级节点如果为空就退出
        if (std::get<0>(params[i]) == nullptr)
            break;

        // 获取符合管径的spco个数
        size_t cnt = selectPipeDialog.updateWidget(std::get<0>(params[i])
            , std::get<1>(params[i]), bShowPipeComDesc);

        // 选择管件界面选择spco节点
        WD::WDNode::SharedPtr pRetSPCONode = nullptr;
        // 个数为0 退出循环
        if (cnt == 0)
        {
            if(i == 0)
            {
                WD_WARN_T("ErrorUiComNodePipeComponent", "Current SPEC No exist VALV with bore, create kit fail!");
            }
            break;
        }
        // 个数为1 直接获取spco节点
        else if (cnt == 1)
        {
            pRetSPCONode = selectPipeDialog.getSPCONode();
        }
        // 个数为大于1 显示选择管件界面
        else
        {
            auto ret = selectPipeDialog.exec();
            // 选择管件界面退出了直接退出循环
            if (ret != QDialog::Accepted)
            {
                break;
            }
            else
            {
                // 获取管件界面选择的spco节点
                pRetSPCONode = selectPipeDialog.getSPCONode();
            }
        }

        // 校验获取的spco是否符合指定的连接类型
        if (pRetSPCONode != nullptr)
        {
            // 是否进行下一步
            if (!funcCheckNext(i, params, *pRetSPCONode))
            {
                // 是阀门类型即使不符合连接类型校验也要收集
                if( i == 0)
                {
                    rNodes[i] = pRetSPCONode;
                }
                break;
            }
            else
            {
                // 收集类型匹配的spco节点
                rNodes[i] = pRetSPCONode;
            }
        }
    }

    return rNodes;
}

void UiComNodePipeComponent::slotCreatePushButton()
{
    // 先判断是否创建所选类型的管件
    if (!checkSpecChanged())
    {
        return;
    }
    // 获取当前选择的类型SELE节点
    auto pTypeSELE = _pSELE.lock();
    if (pTypeSELE == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Please select pipe Type!");
        return;
    }

    // 从选择管件界面获取选择的spco节点
    std::string tBore = this->connectBore();
    auto spcoCount = _pSelectPipeDialog->updateWidget(pTypeSELE, tBore, this->getPipeComDescShowByConfig());
    WD::WDNode::SharedPtr pSpcoNode = nullptr;
    if (spcoCount == 0)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "No find matched spco!");
        return;
    }
    else if (spcoCount == 1)
    {
        pSpcoNode = _pSelectPipeDialog->getSPCONode();
    }
    else
    {
        auto ret = _pSelectPipeDialog->exec();
        if (ret != QDialog::Accepted)
        {
            // 直接关掉了选择管件界面
            return;
        }
        pSpcoNode = _pSelectPipeDialog->getSPCONode();
    }

    auto pCurComNode = getCurrentPipeBusinessNode();
    if (pCurComNode == nullptr)
    {
        assert(false);
        return;
    }
    // 从当前节点上行查询TEE可以挂载的父节点
    auto pSELE = _pSELE.lock();
    if (pSELE == nullptr)
        return;
    std::string tAnswerStr = "";
    if (!pSELE->getAttribute("Tanswer").toString(tAnswerStr))
        return;
    auto type = tAnswerStr;
    auto pBranchNode = this->getParentNode(type);
    if (pBranchNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "ParentNodeInvalid");
        return;
    }
    // 创建管件节点
    auto pComNode = this->createPipe(pBranchNode, type.c_str(), pSpcoNode, *pCurComNode);
    if (pComNode == nullptr)
    {
        assert(false);
        return;
    }
    // 获取是否自动连接
    bool autoConnect = ui.autoConnectCheckBox->isChecked();
    // 进行管件连接校验
    double spoolDistance = 0.0;
    auto sequence = this->getInsertionSequence();
    auto nRet = WD::WDBMDPipeUtils::CheckConnect(_core, *pComNode, sequence == WD::PFD_Backward);
    switch (nRet)
    {
    case WD::WDBMDPipeUtils::CCR_None:
        break;
    case WD::WDBMDPipeUtils::CCR_Origin:
        {
            // 效果等同于非自动连接
            autoConnect = false;
            // 报错
            WD_ERROR_T("ErrorUiComNodePipeComponent", "ConnectBoreNotMatch BadInsertion");
        }
        break;
    case WD::WDBMDPipeUtils::CCR_Flip:
        {
            SwapArriveLeavePoint(*pComNode);
        }
        break;
    case WD::WDBMDPipeUtils::CCR_Spacing:
        {
            // 警告
            WD_INFO_T("ErrorUiComNodePipeComponent", "check failed, at a point 100mm away from the end face when inserting the component");
            spoolDistance = 100.0;
        }
        break;
    case WD::WDBMDPipeUtils::CCR_SpacingFlip:
        {
            SwapArriveLeavePoint(*pComNode);
            // 警告
            WD_INFO_T("ErrorUiComNodePipeComponent", "check failed, at a point 100mm away from the end face when inserting the component");
            spoolDistance = 100.0;
        }
        break;
    default:
        break;
    }

    pComNode->setAttribute("Oriflag", true);
    pComNode->setAttribute("Posflag", true);
    // 调整节点位置
    this->restoreConnection(pComNode, autoConnect, spoolDistance);

    auto cmdNodeCreated = WD::WDBMBase::MakeCreatedCommand({pComNode});
    cmdNodeCreated->setNoticeAfterUndo([this](const WD::WDUndoCommand&)
        {
            // 重绘
            _core.needRepaint();
        });
    cmdNodeCreated->setNoticeAfterRedo([this, pComNode](const WD::WDUndoCommand&) 
        {
            //将新建的元件设置为当前节点
            _core.nodeTree().setCurrentNode(pComNode);
            pComNode->triggerUpdate();
            // 重绘
            _core.needRepaint();
        });
    auto cmdAddToScene = WD::WDBMBase::MakeSceneAddCommand({pComNode});
    if (cmdNodeCreated != nullptr && cmdAddToScene != nullptr)
    {
        _core.undoStack().beginMarco("");
        _core.undoStack().push(cmdNodeCreated);
        _core.undoStack().push(cmdAddToScene);
        _core.undoStack().endMarco();
    }
}
void UiComNodePipeComponent::slotTopBottomFlatFlipPushButtonClicked()
{
    // 获取当前节点
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select node!");
        return;
    }
    // 只能操作偏心大小头节点
    if (!pCurrentNode->isType("REDU"))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select REDU node!");
        return;
    }
    // 判断大小头节点是否是偏心
    auto pA = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Arrive").toInt());
    auto pL = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Leave").toInt());
    if (pA == nullptr || pL == nullptr)
    {
        assert(false);
        return;
    }
    auto aPoint = pA->transformed(pCurrentNode->globalTransform());
    auto lPoint = pL->transformed(pCurrentNode->globalTransform());
    // 如果大小头的出入口点方向与出入口点连线方向在一条线上,认为是同心大小头
    if (WD::DVec3::OnTheSameLine(aPoint.direction.normalized(), WD::DVec3::Normalize(lPoint.position - aPoint.position)))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Does not support concentric flipping!");
        return;
    }
    // 在执行翻转操作之前要先进行直接连接操作
    // 获取当前分支
    auto pBran = pCurrentNode->parent();
    if (pBran == nullptr || !pBran->isType("BRAN"))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Parent type error!");
        return;
    }

    WD::DVec3   pos    =   WD::DVec3::Zero();
    WD::DVec3   dir    =   WD::DVec3::AxisY();
    double      angle       =   0.0;

    // 获取创建的流向
    WD::PipeFlowDir flowDir = this->getInsertionSequence();
    switch (flowDir)
    {
    case WD::PFD_Forward:
        {
            WD::DVec3   leaveZDir   =   WD::DVec3::AxisZ();
            std::string leaveConn;
            // 获取上游出口点方向和位置
            auto pPrev = WD::WDBMDPipeUtils::PrevPipeComponent(pBran, pCurrentNode);
            if (pPrev == nullptr)
            {
                pos   =   pBran->getAttribute("Hposition WRT World").toDVec3();
                dir   =   pBran->getAttribute("Hdirection WRT World").toDVec3();
                leaveZDir   =   WD::DVec3::AxisZ();
                leaveConn   =   GetHConnType(*pBran);
            }
            else
            {
                auto pLPt = pPrev->keyPoint(pPrev->getAttribute("Leave").toInt());
                if (pLPt != nullptr)
                {
                    pos    =  pLPt->transformedPosition(pPrev->globalTransform());
                    dir    =  pLPt->transformedDirection(pPrev->globalTransform());
                    leaveConn   =  pLPt->connType();
                }
                leaveZDir   =   pPrev->globalRSTransform() * WD::DVec3::AxisZ();
            }

            //校验COCO表，如果不能连接，则报错，且管件位置不会发生变化
            if (this->getCocoCheckEnabledByConfig())
            {
                std::string nodeHConnType;
                auto pAPt = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Arrive").toInt());
                if (pAPt != nullptr)
                    nodeHConnType   =  pAPt->connType();
                auto ret = CheckCOCOTable(_core.getBMCatalog(), leaveConn, nodeHConnType);
                if (ret != WD::CheckCOCOResult::CCCR_Success)
                {
                    WD_WARN_T("ErrorUiComNodePipeComponent", "ConnectTypeNotMatch");
                    return;
                }
            }
            auto    selfZDir    =   pCurrentNode->globalRSTransform() * WD::DVec3::AxisZ();
            angle   =   WD::DVec3::Angle(selfZDir, leaveZDir);
        }
        break;
    case WD::PFD_Backward:
        {
            // 获取下游入口点方向和位置
            WD::DVec3   arriveZDir  =   WD::DVec3::AxisZ();
            std::string arriveConn;
            auto pNext = WD::WDBMDPipeUtils::NextPipeComponent(pBran, pCurrentNode);
            if (pNext == nullptr)
            {
                pos   =   pBran->getAttribute("Tposition WRT World").toDVec3();
                dir   =   pBran->getAttribute("Tdirection WRT World").toDVec3();
                arriveZDir  =   WD::DVec3::AxisZ();
                arriveConn  =   GetTConnType(*pBran);
            }
            else
            {
                auto pAPt = pNext->keyPoint(pNext->getAttribute("Arrive").toInt());
                if (pAPt != nullptr)
                {
                    pos    =  pAPt->transformedPosition(pNext->globalTransform());
                    dir    =  pAPt->transformedDirection(pNext->globalTransform());
                    arriveConn   =  pAPt->connType();
                }
                arriveZDir   =   pNext->globalRSTransform() * WD::DVec3::AxisZ();
            }

            //校验COCO表，如果不能连接，则报错，且管件位置不会发生变化
            if (this->getCocoCheckEnabledByConfig())
            {
                std::string nodeTConnType;
                auto pLPt = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Leave").toInt());
                if (pLPt != nullptr)
                    nodeTConnType   =  pLPt->connType();
                auto ret = CheckCOCOTable(_core.getBMCatalog(), nodeTConnType, arriveConn);
                if (ret != WD::CheckCOCOResult::CCCR_Success)
                {
                    WD_WARN_T("ErrorUiComNodePipeComponent", "ConnectTypeNotMatch");
                    return;
                }
            }
            auto    selfZDir    =   pCurrentNode->globalRSTransform() * WD::DVec3::AxisZ();
            angle   =   WD::DVec3::Angle(selfZDir, arriveZDir);

        }
        break;
    default:
        break;
    }

    auto    preLPos     =   pCurrentNode->getAttribute("Position").toDVec3();
    auto    preLOri     =   pCurrentNode->getAttribute("Orientation").toQuat();

    // undo/redo
    auto pCommnad = new WD::WDUndoCommand("TopBottomFlatFlipCommand");
    if (pCommnad != nullptr)
    {
        pCommnad->setNoticeAfterUndo([this, pCurrentNode, preLPos, preLOri](const WD::WDUndoCommand&)
        {
            if (pCurrentNode != nullptr)
            {
                pCurrentNode->setAttribute("Position", preLPos);
                pCurrentNode->setAttribute("Orientation", preLOri);
                pCurrentNode->triggerUpdate(true);
            }
            _core.needRepaint();
        });
        pCommnad->setNoticeAfterRedo([this, pCurrentNode, dir, pos, angle, flowDir](const WD::WDUndoCommand&)
        {
            if (pCurrentNode != nullptr)
            {
                const auto beforeConnectAxisZ = WD::DMat3::FromQuat( pCurrentNode->getAttribute("Orientation WRT World").toQuat() ) * WD::DVec3::AxisZ();

                // 直接将世界坐标系下的旋转设置为单位四元数，即设置为创建时的Z轴朝向
                pCurrentNode->setAttribute("Orientation WRT World", WD::DQuat::Identity());
                pCurrentNode->triggerUpdate();
                // 管件对齐
                ComponentAlign(_core, *pCurrentNode, -dir, pos, flowDir);
                const auto afterConnectAxisZ = WD::DMat3::FromQuat( pCurrentNode->getAttribute("Orientation WRT World").toQuat() ) * WD::DVec3::AxisZ();
                pCurrentNode->triggerUpdate();
                // 对其后根据流向方向获取旋转轴和旋转中心点
                auto pA = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Arrive").toInt());
                auto pL = pCurrentNode->keyPoint(pCurrentNode->getAttribute("Leave").toInt());
                if (pA != nullptr && pL != nullptr)
                {
                    auto aPoint = pA->transformed(pCurrentNode->globalTransform());
                    auto lPoint = pL->transformed(pCurrentNode->globalTransform());
                    WD::DVec3 rotAxis = aPoint.direction.normalized();
                    WD::DVec3 rotCenter = aPoint.position;
                    switch (flowDir)
                    {
                    case WD::PFD_Forward:
                        rotAxis = aPoint.direction.normalized();
                        rotCenter = aPoint.position;
                        break;
                    case WD::PFD_Backward:
                        rotAxis = lPoint.direction.normalized();
                        rotCenter = lPoint.position;
                        break;
                    default:
                        break;
                    }
                    if (WD::DVec3::InTheSameDirection(beforeConnectAxisZ, afterConnectAxisZ))
                    {
                        pCurrentNode->rotate(rotAxis, 180, rotCenter);
                        pCurrentNode->triggerUpdate();
                    }
                }
            }
            _core.needRepaint();
        });
        _core.undoStack().push(pCommnad);
    }
}
void UiComNodePipeComponent::slotPipeBottomElevationPushButtonClicked()
{
    _captureFunc = CF_PipeBottomElevation;
    _pCurrentNode.reset();
    auto pCurrentNode = _core.nodeTree().currentNode();
    // 管底标高必须选择管件节点
    if(pCurrentNode == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCurrentNode))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }
    // 记录管底标高的当前节点,一定是管件节点
    _pCurrentNode = pCurrentNode;
    //激活捕捉工具
    WD::WDCapturePositioningParam param;

    param.bDisplayCaptureOptionsUi  = true;
    param.bDisplayCaptureTypeUi     = true;
    param.bEnabledCaptureOptionsUi  = true;
    param.bEnabledCaptureTypeUi     = true;
    param.pMonitor = this;
    _core.viewer().capturePositioning().activate(param);
}
void UiComNodePipeComponent::slotCreateVALVKits()
{
    // 判断节点树当前是否为管件或分支节点
    auto pCurComNode = getCurrentPipeBusinessNode();
    if (pCurComNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Current Node Is Not Pipe SPEC");
        return;
    }

    // 获取当前选择的类型SELE节点
    auto pTypeSELE = _pSELE.lock();
    if (pTypeSELE == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Please select pipe Type!");
        return;
    }

    // 获取选择的管件类型
    auto selectedType = pTypeSELE->getAttribute("Tanswer").toString();

    // 可以创建套件的管件类型集
    std::set<std::string> typeSet{"VALV", "INST", "PCOM"};

    auto typeIt = typeSet.find(selectedType);
    if(typeIt == typeSet.end())
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "CurrentType unsupport to create Kits!");
        return;
    }

    // 3件套都存在 则按规则都要创建
    std::string valv = selectedType;
    std::string flan = "FLAN";
    std::string gask = "GASK";

    // 获取 管径 和 连接类型
    std::string cocoBore = "";
    std::string cocConnectType = "";
    getCOCOBoreACon(cocoBore, cocConnectType, false);
    if(cocoBore.empty())
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Current Node Is Not Pipe SPEC");
        return;
    }
    auto pSpecNode = _pParamsWidget->getSPEC();
    if(pSpecNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "CreateNodeFailed, Please select SPEC!");
        return;
    }

    auto pVALVSELENode = GetClassificationByTAnswer(pSpecNode, valv);
    auto pFLANSELENode = GetClassificationByTAnswer(pSpecNode, flan);
    auto pGASKSELENode = GetClassificationByTAnswer(pSpecNode, gask);

    // 没有阀门类型的SELE是无效
    if(pVALVSELENode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Current SPEC No exist current Type, create kit fail!");
        return;
    }    

    SelectDialogParams params = {
        std::make_pair(pVALVSELENode, cocoBore)
        , std::make_pair(pFLANSELENode, cocoBore)
        , std::make_pair(pGASKSELENode, cocoBore)
    };
    std::array<std::vector<std::string>, 3> tmpConnTypes = {std::vector<std::string>{"F","L"}
    , std::vector<std::string>{""}
    , std::vector<std::string>{""}};

    auto rSPCONodes = SelectSpcos(params, *_pSelectPipeDialog, this->getPipeComDescShowByConfig()
    , [&tmpConnTypes](size_t index, SelectDialogParams& params, WD::WDNode& retSpcoNode)
    {
        // 第index选择需要使用的校验str
        const auto& cTypeVec = tmpConnTypes[index];
        for(auto str: cTypeVec)
        {
            auto pCatValue = retSpcoNode.getAttribute("Catref");
            auto pCatRef = pCatValue.data<WD::WDBMNodeRef>();
            if (pCatRef != nullptr)
            {
                auto pCatNode = pCatRef->refNode();
                if (pCatNode == nullptr)
                    continue;

                // 获取元件的PARA3:连接类型
                auto cataParams = pCatNode->getAttribute("Param").toStringVector();
                if (cataParams.size() < 3)
                    continue;
                std::string praram1 = cataParams[0];
                // 使用第0个（阀门的spco）的第1个参数值作为法兰和垫片的公称直径来更新选择管件界面
                if (index == 0)
                {
                    if (params.size() > 2)
                    {
                        for (int paramIndex = 1; paramIndex < params.size(); ++paramIndex)
                        {
                            params[paramIndex].second = praram1;
                        }
                    }
                }
                // 校验spco引用的元件参数表的第3个参数是否和指定的connectType匹配
                std::string spcoConnectType = cataParams[2];
                if (spcoConnectType.size() < str.size())
                    continue;
                // 如果有连接类型符合其中的就可以
                if (spcoConnectType.compare(0, str.size(), str) == 0)
                    return true;
            }
        }
        return false;
    });

    // 此时已收集完所需的所有 spco节点， 根据spco节点决定是否创建套件
    // 第0个为空 直接创建阀门套件失败
    if (rSPCONodes[0] == nullptr)
    {
        return;
    }

    // 阀门和垫片无效时，只创建一个阀门 不用创建法兰 垫片 按照普通阀门的方式创建
    if(rSPCONodes[1] == nullptr || rSPCONodes[2] == nullptr)
    {
        // 从当前节点上行查询可以挂载的父节点
        auto pBranchNode = this->getParentNode(valv);
        if (pBranchNode == nullptr)
        {
            WD_WARN_T("ErrorUiComNodePipeComponent", "ParentNodeInvalid");
            return;
        }
        double spoolDistance = 0.0;
        // 创建管件节点
        auto pComNode = this->createPipe(pBranchNode, valv.c_str(), rSPCONodes[0], *pCurComNode);
        if (pComNode == nullptr)
        {
            assert(false);
            return;
        }
        if (!WD::WDBMDPipeUtils::IsPipeComponent(*pComNode))
        {
            assert(false);
            return;
        }
        // 设置管件等级(用于查询直管等级)
        pComNode->setAttribute("Ispec", WD::WDBMNodeRef(_pParamsWidget->getInsuSPEC()));
        pComNode->setAttribute("Tspec", WD::WDBMNodeRef(_pParamsWidget->getTracSPEC()));

        pComNode->updateModel();
        // 获取是否自动连接
        bool autoConnect = ui.autoConnectCheckBox->isChecked();
        // 调整节点位置
        this->restoreConnection(pComNode, autoConnect, spoolDistance);

        //将新建的元件设置为当前节点
        _core.nodeTree().setCurrentNode(pComNode);

        pComNode->triggerUpdate();

        auto cmdNodeCreated = WD::WDBMBase::MakeCreatedCommand({pComNode});
        cmdNodeCreated->setNoticeAfterUndo([this, pComNode](const WD::WDUndoCommand&){
            _core.nodeTree().setCurrentNode(pComNode);
            _core.needRepaint(); 
        });
        auto cmdAddToScene = WD::WDBMBase::MakeSceneAddCommand({pComNode});
        if (cmdNodeCreated != nullptr && cmdAddToScene != nullptr)
        {
            _core.undoStack().beginMarco("a com created");
            _core.undoStack().push(cmdNodeCreated);
            _core.undoStack().push(cmdAddToScene);
            _core.undoStack().endMarco();
        }
        
        return;
    }

    // 所有spco都有效，按顺序创建kits
    {
        auto pFlanBranchNode = this->getParentNode(flan);
        auto pValvBranchNode = this->getParentNode(valv);
        auto pGaskBranchNode = this->getParentNode(gask);

        if (pFlanBranchNode == nullptr || pValvBranchNode == nullptr || pGaskBranchNode == nullptr)
        {
            WD_WARN_T("ErrorUiComNodePipeComponent", "ParentNodeInvalid");
            return;
        }

        // 创建第1个法兰
        auto p1FlanCom = createPipe(pFlanBranchNode, flan, rSPCONodes[1], *pCurComNode);
        if(p1FlanCom != nullptr)
        {
            WD::WDNode::Nodes createdNodes;
            createdNodes.push_back(p1FlanCom);

            // 根据创建顺序，和出入口连接方式，决定法兰是否有翻转
            auto insertSequence = this->getInsertionSequence();
            // 获取法兰1的头为连接类型
            auto flan1HConnect = WD::GetComsHConnType(*p1FlanCom);
            auto flan1TConnect = WD::GetComsTConnType(*p1FlanCom);
            switch(insertSequence)
            {
                case WD::PipeFlowDir::PFD_Forward:
                {
                    // 如果从前往后创建， 入口点为法兰连接方式，则翻转，使法兰连接方式的关键点作为尾连接方式
                    if(flan1HConnect.compare(0, 1, "F") == 0 || flan1HConnect.compare(0, 1, " L") == 0)
                    {
                        SwapArriveLeavePoint(*p1FlanCom);
                    }
                }
                break;
                case WD::PipeFlowDir::PFD_Backward:
                {
                    // 如果从后往前创建， 法兰1的尾为法兰连接类型，则翻转法兰1
                    if (flan1TConnect.compare(0, 1, "F") == 0 || flan1TConnect.compare(0, 1, " L") == 0)
                    {
                        SwapArriveLeavePoint(*p1FlanCom);
                    }
                }
                break;
            }
            restoreConnection(p1FlanCom, true, 0.00);

            // 创建第1个垫片
            auto p1GaskCom = createPipe(pGaskBranchNode, gask, rSPCONodes[2], *p1FlanCom);
            if (p1GaskCom != nullptr)
            {
                createdNodes.push_back(p1GaskCom);
                // 垫片自动连接
                restoreConnection(p1GaskCom, true, 0.00);
                // 创建阀门
                auto pValvCom = createPipe(pValvBranchNode, valv, rSPCONodes[0], *p1GaskCom);
                if (pValvCom != nullptr)
                {
                    createdNodes.push_back(pValvCom);
                    // 阀门自动连接
                    restoreConnection(pValvCom, true, 0.00);
                    // 创建第2个垫片
                    auto p2GaskCom = createPipe(pGaskBranchNode, gask, rSPCONodes[2], *pValvCom);
                    if (p2GaskCom != nullptr)
                    {
                        createdNodes.push_back(p2GaskCom);
                        restoreConnection(p2GaskCom, true, 0.00);
                        // 创建第2个法兰
                        auto p2FlanCom = createPipe(pFlanBranchNode, flan, rSPCONodes[1], *p2GaskCom);
                        if (p2FlanCom != nullptr)
                        {
                            createdNodes.push_back(p2FlanCom);
                            // 获取法兰2的头尾连接类型
                            auto flan2HConnect = WD::GetComsHConnType(*p2FlanCom);
                            auto flan2TConnect = WD::GetComsTConnType(*p2FlanCom);
                            switch (insertSequence)
                            {
                            case WD::PipeFlowDir::PFD_Forward:
                            {
                                // 如果从前往后创建，法兰2的尾为法兰连接类型，则翻转法兰2
                                if (flan2TConnect.compare(0, 1, "F") == 0 || flan2TConnect.compare(0, 1, " L") == 0)
                                {
                                    SwapArriveLeavePoint(*p2FlanCom);
                                }
                            }
                            break;
                            case WD::PipeFlowDir::PFD_Backward:
                            {
                                // 如果从后往前创建，法兰2的头为法兰连接方式，则翻转法兰2
                                if (flan2HConnect.compare(0, 1, "F") == 0 || flan2HConnect.compare(0, 1, " L") == 0)
                                {
                                    SwapArriveLeavePoint(*p2FlanCom);
                                }
                            }
                            break;
                            }
                            restoreConnection(p2FlanCom, true, 0.00);
                        }
                    }
                }
            }
            for (auto& pEachNode : createdNodes)
            {
                if (pEachNode == nullptr)
                    continue;
                pEachNode->setAttribute("Oriflag", true);
                pEachNode->setAttribute("Posflag", true);
            }
            auto cmdNodesCreated = WD::WDBMBase::MakeCreatedCommand(createdNodes);
            cmdNodesCreated->setNoticeAfterUndo([this](const WD::WDUndoCommand&){ _core.needRepaint(); });
            auto cmdAddToScene = WD::WDBMBase::MakeSceneAddCommand(createdNodes);
            if (cmdNodesCreated != nullptr && cmdAddToScene != nullptr)
            {
                _core.undoStack().beginMarco("");
                _core.undoStack().push(cmdNodesCreated);
                _core.undoStack().push(cmdAddToScene);
                _core.undoStack().endMarco();
            }
            
        }
    }
}
void UiComNodePipeComponent::slotBtnConnectClicked()
{
    // 构造中直连注册在最后1个，直接获取最后1个的索引
    int index = static_cast<int>(_thros.size()) - 1;
    if (index < 0)
    {
        assert(false);
        return;
    }
    // 校验最后一个定位类型是否是直连方式
    auto pThroBase = dynamic_cast<WD::ThroConnect*>(_thros[index]);
    if (pThroBase == nullptr)
        return;
    // 直连
    slotThroActivated(index);
}
void UiComNodePipeComponent::slotBtnCopyClicked(bool)
{
    // 当前选中节点
    auto pCur = _core.nodeTree().currentNode();
    if (pCur == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return ;
    }
    // 如果是TUBI 类型直接返回，不做任何操作
    if(pCur->isType("TUBI"))
    {
        return;
    }
    if (!WD::WDBMDPipeUtils::IsPipeComponent(*pCur))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }

    auto pBran = pCur->parent();
    if (pBran == nullptr)
        return;

    auto& desiMgr = _core.getBMDesign();
    WD::WDNode::SharedPtr pNext = nullptr;
    switch (this->getInsertionSequence())
    {
    case WD::PipeFlowDir::PFD_Forward:
        {
            // 当前节点后一个节点
            pNext = WD::WDBMDPipeUtils::NextPipeComponent(pBran, pCur);
        }
        break;
    case WD::PipeFlowDir::PFD_Backward:
        {
            pNext = pCur;
        }
        break;
    default:
        break;
    }
    // 申领父对象
    if (!desiMgr.claimMgr().checkAdd(pBran, pNext))
        return;

    // 克隆业务数据对象，创建节点根据插入顺序插入到分支中
    WD::WDNode::SharedPtr pNewNode = pCur->cloneT<WD::WDNode>(); 
    if (pNewNode == nullptr)
        return ;
    desiMgr.setParent(pNewNode, pBran, pNext, false);
    pNewNode->setAttribute("Name", std::string(""));

    // 设置默认颜色
    pNewNode->setAttribute("AutoColor", WD::Color(204, 204, 0));

    pNewNode->triggerUpdate();
    // 获取是否自动连接
    bool autoConnect = ui.autoConnectCheckBox->isChecked();
    // 进行管件连接校验
    double spoolDistance = 0.0;
    auto sequence = this->getInsertionSequence();
    auto nRet = WD::WDBMDPipeUtils::CheckConnect(_core, *pNewNode, sequence == WD::PFD_Backward);
    switch (nRet)
    {
    case WD::WDBMDPipeUtils::CCR_None:
        break;
    case WD::WDBMDPipeUtils::CCR_Origin:
        {
            // 效果等同于非自动连接
            autoConnect = false;
            // 报错
            WD_ERROR_T("ErrorUiComNodePipeComponent", "ConnectBoreNotMatch BadInsertion");
        }
        break;
    case WD::WDBMDPipeUtils::CCR_Flip:
        {
            SwapArriveLeavePoint(*pNewNode);
        }
        break;
    case WD::WDBMDPipeUtils::CCR_Spacing:
        {
            // 警告
            WD_INFO_T("ErrorUiComNodePipeComponent", "check failed, at a point 100mm away from the end face when inserting the component");
            spoolDistance = 100.0;
        }
        break;
    case WD::WDBMDPipeUtils::CCR_SpacingFlip:
        {
            SwapArriveLeavePoint(*pNewNode);
            // 警告
            WD_INFO_T("ErrorUiComNodePipeComponent", "check failed, at a point 100mm away from the end face when inserting the component");
            spoolDistance = 100.0;
        }
        break;
    default:
        break;
    }
    // 重新连接管件
    this->restoreConnection(pNewNode, autoConnect, spoolDistance);
    // 设置复制成功的管件为当前选中节点
    _core.nodeTree().setCurrentNode(pNewNode);
    _core.needRepaint();

    auto cmdNodeCreated = WD::WDBMBase::MakeCreatedCommand({pNewNode});
    cmdNodeCreated->setNoticeAfterRedo([this, pNewNode](const WD::WDUndoCommand&)
        {
            _core.nodeTree().setCurrentNode(pNewNode);
            _core.needRepaint();
        });
    cmdNodeCreated->setNoticeAfterUndo([this](const WD::WDUndoCommand&)
        {
            _core.needRepaint();
        });
    _core.undoStack().push(cmdNodeCreated);
}
void UiComNodePipeComponent::slotBtnSimilarClicked(bool)
{
    _captureFunc = CF_Similar;
    _pSimilarCurr.reset();
    auto pCurrentNode = _core.nodeTree().currentNode();
    if(pCurrentNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }
    // 如果是TUBI 类型直接返回，不做任何操作
    if (pCurrentNode->isType("TUBI"))
    {
        return;
    }
    // 当前节点为BRAN类型，且存在子节点，提示选择管件
    if(pCurrentNode->isType("BRAN"))
    {
        if(pCurrentNode->childCount() > 0)
        {
            WD_WARN_T("ErrorUiComNodePipeComponent", "Please select component!");
            return;
        }
    }
    // 当前节点如果不是分支就必须是管件类型节点
    else if (!WD::WDBMDPipeUtils::IsPipeComponent(*pCurrentNode))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }

    // 记录点击相似按钮时节点树的当前节点(此节点一定是空分支节点或管件节点)
    _pSimilarCurr = pCurrentNode;
    //激活捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    WD::WDCapturePositioningParam param;
    param.pMonitor = this;

    WD::WDCapturePositioningOption option = tool.option();
    option._disaTypes = { WD::WDCapturePositioningType::CPT_Any
        , WD::WDCapturePositioningType::CPT_Aid
        , WD::WDCapturePositioningType::CPT_PLine
        , WD::WDCapturePositioningType::CPT_PPoint
        , WD::WDCapturePositioningType::CPT_Screen
        , WD::WDCapturePositioningType::CPT_Graphics };

    if (option._disaTypes.find(option.type) != option._disaTypes.end())
        option.type = WD::WDCapturePositioningType::CPT_Element;

    tool.activate(param);
}

void UiComNodePipeComponent::slotBtnReselectClicked(bool)
{
    // 获取节点树的当前选的管件节点
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }
    // 如果是TUBI 类型直接返回，不做任何操作
    if (pCurrentNode->isType("TUBI"))
    {
        return;
    }

    // 节点树当前节点一定要是管道专业的元件节点(管道专业叶子节点)
    if(!IsPipeCom(*pCurrentNode))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }

    // 更改属性签入
    bool bCancelMd = false;
    if (!WD::Core().getBMDesign().claimMgr().checkUpdate(pCurrentNode, { "Spref" , "Lstube" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    // 获取等级子界面选择的等级(SPEC)
    auto pSpec = _pParamsWidget->getSPEC();
    if (pSpec == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "CreateNodeFailed, Please select SPEC!");
        return;
    }
    
    std::string currentNodeType(pCurrentNode->type());

    // 重选管件，通过界面的选择的前后获取头管径或者尾管径
    std::string cBore = "";
    std::string connectType = "";
    // 这个接口应该需要改动，要传入一个节点进去
    getCOCOBoreACon(cBore, connectType, true);

    // 获取与当前节点类型相同的SELE节点更新选择管件界面
    auto pTypeSELENode = GetClassificationByTAnswer(pSpec, currentNodeType);
    if(pTypeSELENode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Current SPEC no exist the type of component");
        return;
    }

    auto spcoCount = _pSelectPipeDialog->updateWidget(pTypeSELENode, cBore, this->getPipeComDescShowByConfig());
    // 新spref属性
    WD::WDNode::SharedPtr pSpco = nullptr;
    if(spcoCount == 0)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "No find matched spco!");
        return;
    }
    else if(spcoCount == 1)
    {
        pSpco = _pSelectPipeDialog->getSPCONode();
    }
    else
    {
        auto ret = _pSelectPipeDialog->exec();
        if(ret != QDialog::Accepted)
        {
            return;
        }
        pSpco = _pSelectPipeDialog->getSPCONode();
    }



    // 使用设置属性的undo/redo命令设置节点Spref属性(可能会交换出入口点)
    auto setSprefBySetAttributeCmdFunc = [this](WD::WDNode::SharedPtr pNode, WD::WDNode::SharedPtr pSpco, bool flip)
    {
        if(pNode == nullptr)
            return; 
        // 用于修改节点属性后undo/redo通知节点更新场景重绘
        auto updatePipeComData = [this, pNode](const WD::WDUndoCommand&)
        {
            // 强制更新节点
            pNode->triggerUpdate(true);
            // 更新节点的模型
            pNode->updateModel();
            _core.needRepaint();
        };

        WD::WDUndoCommand* pCmd = new WD::WDUndoCommand("Reslect spco");
        WD::WDBMBase::MakeAttributeSetCommand(pNode, "Spref", WD::WDBMNodeRef(pSpco), pCmd);
        if(flip)
        {
            // 获取管件的出入口点
            auto arriveIndex = pNode->getAttribute("Arrive").toInt();
            auto leaveIndex = pNode->getAttribute("Leave").toInt();

            WD::WDBMBase::MakeAttributeSetCommand(pNode, "Arrive", WD::WDBMAttrValue(leaveIndex), pCmd);
            WD::WDBMBase::MakeAttributeSetCommand(pNode, "Leave", WD::WDBMAttrValue(arriveIndex), pCmd);
        }

        //设置undo/rodo 更新模型
        pCmd->setNoticeAfterRedo(updatePipeComData);
        pCmd->setNoticeAfterUndo(updatePipeComData);
        _core.undoStack().push(pCmd);
    };

    bool isCoCoFlag = getCocoCheckEnabledByConfig();
    if(isCoCoFlag)
    {
        // 记录原始的spco
        auto pOldSpcoNode = pCurrentNode->getAttribute("Spref").toNodeRef().refNode();
        pCurrentNode->setAttribute("Spref", WD::WDBMNodeRef(pSpco));
        pCurrentNode->triggerUpdate(true);
        pCurrentNode->updateModel();

        auto sequence = this->getInsertionSequence();
        auto checkCoCoRet = WD::WDBMDPipeUtils::CheckConnect(_core, *pCurrentNode, sequence == WD::PFD_Backward);
        switch (checkCoCoRet)
        {
        case WD::WDBMDPipeUtils::CCR_None:
            {
                // 还原spco
                pCurrentNode->setAttribute("Spref", WD::WDBMNodeRef(pOldSpcoNode));
                // 使用设置属性命令
                setSprefBySetAttributeCmdFunc(pCurrentNode, pSpco, false);
            }
            break;
        case WD::WDBMDPipeUtils::CCR_Origin:
            {
                // 还原spco
                pCurrentNode->setAttribute("Spref", WD::WDBMNodeRef(pOldSpcoNode));
                // 使用设置属性命令
                setSprefBySetAttributeCmdFunc(pCurrentNode, pSpco, false);
                // 报错
                WD_ERROR_T("ErrorUiComNodePipeComponent", "ConnectTypeNotMatch");
            }
            break;
        case WD::WDBMDPipeUtils::CCR_Flip:
            {
                pCurrentNode->setAttribute("Spref", WD::WDBMNodeRef(pOldSpcoNode));
                setSprefBySetAttributeCmdFunc(pCurrentNode, pSpco, true);
            }
            break;
        case WD::WDBMDPipeUtils::CCR_Spacing:
            {
                // 还原spco
                pCurrentNode->setAttribute("Spref", WD::WDBMNodeRef(pOldSpcoNode));
                // 使用设置属性命令
                setSprefBySetAttributeCmdFunc(pCurrentNode, pSpco, false);
                // 报错
                WD_ERROR_T("ErrorUiComNodePipeComponent", "ConnectTypeNotMatch");
            }
            break;
        case WD::WDBMDPipeUtils::CCR_SpacingFlip:
            {
                pCurrentNode->setAttribute("Spref", WD::WDBMNodeRef(pOldSpcoNode));
                setSprefBySetAttributeCmdFunc(pCurrentNode, pSpco, true);
                // 报错
                WD_ERROR_T("ErrorUiComNodePipeComponent", "ConnectTypeNotMatch");
            }
            break;
        default:
            break;
        }
    }
    else
    {
        // 不需要进行CoCo校验，直接更改节点的Spref
        setSprefBySetAttributeCmdFunc(pCurrentNode, pSpco, false);
    }    
}

void UiComNodePipeComponent::slotFlipPushButton()
{
    // 当前选中节点
    auto pNode = _core.nodeTree().currentNode();
    if (pNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }
    // 如果是TUBI 类型直接返回，不做任何操作
    if (pNode->isType("TUBI"))
    {
        return;
    }

    // 判断当前节点是否是管件类型的节点
    if (!WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }

    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pNode, { "Leave" , "Arrive" ,"Orientation", "Position", "Lstube"}, bCancelMd))
        return;
    if (bCancelMd)
        return;

    auto func = [this, pNode](const WD::WDUndoCommand&)
    {
        if (pNode == nullptr)
            return ;
        // 获取原上游朝向和坐标
        auto        inseSequ = this->getInsertionSequence();
        WD::DVec3   srcPos;
        WD::DVec3   srcDir;
        if (inseSequ == WD::PipeFlowDir::PFD_Forward)
        {
            srcPos = WD::GetHPos(*pNode, WD::WDWRTType::WRT_World);
            srcDir = WD::GetHDir(*pNode, WD::WDWRTType::WRT_World);
        }
        else if (inseSequ == WD::PipeFlowDir::PFD_Backward)
        {
            srcPos = WD::GetTPos(*pNode, WD::WDWRTType::WRT_World);
            srcDir = WD::GetTDir(*pNode, WD::WDWRTType::WRT_World);
        }

        // 交换出口入口点
        int leave = pNode->getAttribute("Leave").toInt();
        int arrive = pNode->getAttribute("Arrive").toInt();
        pNode->setAttribute("Leave", arrive);
        pNode->setAttribute("Arrive", leave);

        // 对齐原上游朝向和坐标
        WD::ComponentAlign(_core, *pNode, srcDir, srcPos, inseSequ);
        _core.needRepaint();
    };

    // undo/redo
    auto bm = _core.currentBM();
    if (bm != nullptr)
    {
        auto cmdFlip = new WD::WDUndoCommand("");
        if(cmdFlip != nullptr)
        {
            cmdFlip->setNoticeAfterRedo(func);
            cmdFlip->setNoticeAfterUndo(func);
            _core.undoStack().push(cmdFlip);
        }
    }
}
void UiComNodePipeComponent::slotLeaveDirectActivated(int index)
{
    WDUnused(index);
    WD::WDNode::SharedPtr pPipeCom = _core.nodeTree().currentNode();
    // 判断当前节点是否是管件类型的节点
    if (pPipeCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pPipeCom))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }

    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pPipeCom, { "Orientation" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    // 解析关键点方向数据
    WD::DVec3 dir = WD::DVec3::Zero();
    if (!WD::DDirectionParserXYZ::Direction(ui.orientationComboBox->currentText().toUtf8().data(), dir))
        return;

    auto pLeave = pPipeCom->keyPoint(pPipeCom->getAttribute("Leave").toInt());
    if (pLeave == nullptr)
        return ;
    auto preDir = pLeave->transformedDirection(pPipeCom->globalRSTransform());

    auto func = [this, pPipeCom](const WD::DVec3& dir)
    {
        // 管件是否可变角度
        bool isVriable = WD::IsVariablePipeComponent(*pPipeCom);
        switch (this->getInsertionSequence())
        {
        case WD::PipeFlowDir::PFD_Forward:
            {
                if (isVriable)
                {
                    // 获取可变弯头原入口点位置
                    auto pOldArrivePos = WD::GetHPos(*pPipeCom, WD::WDWRTType::WRT_World);
                    // 设置弯头出口朝向
                    WD::SetElboLeaveDirection(*pPipeCom, dir);
                    // 将朝向修改后的弯头，对齐到原入口点位置
                    WD::ComponentAlignPos(_core, *pPipeCom, pOldArrivePos, WD::PipeFlowDir::PFD_Forward);
                }
                else
                {
                    this->forwardRotateByDirection(pPipeCom, dir);
                }
            }
            break;
        case WD::PipeFlowDir::PFD_Backward:
            {
                if (isVriable)
                {
                    // 获取可变弯头原出口点位置
                    auto pOldLeavePos = WD::GetTPos(*pPipeCom, WD::WDWRTType::WRT_World);
                    // 设置弯头入口朝向
                    WD::SetElboArriveDirection(*pPipeCom, dir);
                    // 将朝向修改后的弯头，对齐到原出口点位置
                    WD::ComponentAlignPos(_core, *pPipeCom, pOldLeavePos, WD::PipeFlowDir::PFD_Backward);
                }
                else
                {
                    this->backwardRotateByDirection(pPipeCom, dir);
                }
            }
            break;
        default:
            break;
        }
        _core.needRepaint();
    };

    // undo/redo
    auto bm = _core.currentBM();
    if (bm != nullptr)
    {
        auto cmd = new WD::WDUndoCommand("LeaveDirect");
        if(cmd != nullptr)
        {
            cmd->setNoticeAfterRedo([dir, func](const WD::WDUndoCommand&)
            {
                func(dir);
            });
            cmd->setNoticeAfterUndo([preDir, func](const WD::WDUndoCommand&)
            {
                func(preDir);
            });
            _core.undoStack().push(cmd);
        }
    }
}
void UiComNodePipeComponent::slotRotationAngleChanged(int index)
{
    WD::WDNode::SharedPtr pPipeCom = _core.nodeTree().currentNode();
    if (pPipeCom == nullptr)
        return;
    // 判断当前节点是否是管件类型的节点
    if (!WD::WDBMDPipeUtils::IsPipeComponent(*pPipeCom))
        return;

    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pPipeCom, {"Orientation"}, bCancelMd))
        return;
    if (bCancelMd)
        return;
    double angle = _pComboBoxRotAngel->itemData(index).toDouble();
    rotationPipeCom(pPipeCom, angle);
}

void UiComNodePipeComponent::slotDiatanceIndexChanged(int index)
{
    WDUnused(index);
    slotComboBoxDistanceEnterPressed("");
}
void UiComNodePipeComponent::slotAngleEnterPressed(const QString& text)
{
    if (text.isEmpty())
        return ;

    WD::WDNode::SharedPtr pPipeCom = _core.nodeTree().currentNode();
    if (pPipeCom == nullptr)
        return;
    // 判断当前节点是否是管件类型的节点
    if (!WD::WDBMDPipeUtils::IsPipeComponent(*pPipeCom))
        return;
    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pPipeCom, { "Orientation" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    double angle = text.toDouble();
    rotationPipeCom(pPipeCom, angle);
}
void UiComNodePipeComponent::slotComboBoxDistanceEnterPressed(const QString&)
{
    WD::WDNode::SharedPtr pPipeCom = _core.nodeTree().currentNode();
    if (pPipeCom == nullptr)
        return;
    // 判断当前节点是否是管件类型的节点
    if (!WD::WDBMDPipeUtils::IsPipeComponent(*pPipeCom))
        return;
    // 当前管件所属的分支节点
    WD::WDNode::SharedPtr pBranchNode = pPipeCom->parent();
    if (pBranchNode == nullptr)
        return;
    //  权限校验
    if (!_core.getBMDesign().permissionMgr().check(*pBranchNode))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "You cannot operate the current node!");
        return;
    }
    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pPipeCom, { "Position" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    switch (getInsertionSequence())
    {
    case WD::PipeFlowDir::PFD_Forward:
        {
            this->forwardOrientation(pBranchNode, pPipeCom);
        }
        break;
    case WD::PipeFlowDir::PFD_Backward:
        {// 使用当前管件入口点到前一个管件出口点运用距离
            this->backwardOrientation(pBranchNode, pPipeCom);
        }
        break;
    default:
        break;
    }
    _core.needRepaint();
}
void UiComNodePipeComponent::slotPushButtonElbowSlopeClicked()
{
    _captureFunc = CF_AutoElbo;
    //激活捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    WD::WDCapturePositioningParam param;
    param.pMonitor = this;

    auto option = tool.option();
    option._disaTypes = {WD::WDCapturePositioningType::CPT_Any
        , WD::WDCapturePositioningType::CPT_Aid
        , WD::WDCapturePositioningType::CPT_PLine
        , WD::WDCapturePositioningType::CPT_PPoint
        , WD::WDCapturePositioningType::CPT_Screen
        , WD::WDCapturePositioningType::CPT_Graphics};

    // 如果当前捕捉类型被禁用,使用上一次的捕捉类型
    if (option._disaTypes.find(option.type) != option._disaTypes.end())
        option.type = WD::WDCapturePositioningType::CPT_Element;

    tool.activate(param);
}
void UiComNodePipeComponent::slotPushButtonEndingClicked()
{
    auto pCurNode   =   _core.nodeTree().currentNode();
    if (pCurNode == nullptr)
        return ;

    WD::PipeFlowDir inseSequence = this->getInsertionSequence();
    if (inseSequence == WD::PipeFlowDir::PFD_Forward)
    {
        if (pCurNode->isType("BRAN"))
        {
            // 申领对象
            bool bCancelMd = false;
            if (!_core.getBMDesign().claimMgr().checkUpdate(pCurNode, { "Tposition", "Tdirection" }, bCancelMd))
                return;
            if (bCancelMd)
                return;

            _branchEndingWidget->updateWidget(pCurNode, inseSequence);
        }
        else if (WD::WDBMDPipeUtils::IsPipeComponent(*pCurNode)
            ||  pCurNode->isType("TUBI"))
        {
            auto pBran = pCurNode->parent();
            // 申领对象
            bool bCancelMd = false;
            if (!_core.getBMDesign().claimMgr().checkUpdate(pBran, { "Tposition", "Tdirection" }, bCancelMd))
                return;
            if (bCancelMd)
                return;

            _branchEndingWidget->updateWidget(pBran, inseSequence);
        }
        else
        {
            WD_WARN_T("ErrorUiComNodePipeComponent", "Current Node Is Not Pipe SPEC");
            return ;
        }
    }
}
void UiComNodePipeComponent::slotSpecNodeChanged()
{
    ui.listWidgetType->clear();
    _pSELE.reset();
    auto pSpec = _pParamsWidget->getSPEC();
    if (pSpec == nullptr)
        return;

    // 填充类型列表
    for (size_t i = 0; i < pSpec->childCount(); ++i)
    {
        auto pChild = pSpec->childAt(i);
        if (pChild == nullptr || !pChild->isType("SELE"))
            continue;

        std::string tAnswerStr = "";
        if (!pChild->getAttribute("Tanswer").toString(tAnswerStr))
            continue;

        // 不显示管线类型
        if (tAnswerStr == "TUBE" || tAnswerStr == "BOLT")
            continue;

        QListWidgetItem* pItem = new QListWidgetItem();
        QVariant userData;
        UiWeakObject userObj(pChild);
        userData.setValue(userObj);

        // （后续更改为TAnswer
        pItem->setText(QString::fromUtf8(WD::WDTs("UiComNodePipeComponent", tAnswerStr).c_str()));
        pItem->setData(Qt::UserRole, userData);
        ui.listWidgetType->addItem(pItem);
    }
}

void UiComNodePipeComponent::slotListWidgetTypeCurrentItemChanged(QListWidgetItem* currentItem, QListWidgetItem* preItem)
{
    WDUnused(preItem);
    // 获取当前item的SELE节点
    if(currentItem == nullptr)
        return;
    auto itemData = currentItem->data(Qt::UserRole);
    if (!itemData.isValid())
        return;
    auto pQWeak = itemData.value<UiWeakObject>();
    _pSELE = WD::WDNode::ToShared(dynamic_cast<WD::WDNode*>(pQWeak.object().get()));
}
void UiComNodePipeComponent::slotThroActivated(int index)
{
    WD::WDNode::SharedPtr pPipeCom = _core.nodeTree().currentNode();
    if (pPipeCom == nullptr)
        return;
    // 判断当前节点是否是管件类型的节点
    if (!WD::WDBMDPipeUtils::IsPipeComponent(*pPipeCom))
        return;    
    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pPipeCom, { "Position", "Orientation" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    if (index >= 0 && index < _thros.size())
    {
        bool        ret         =   _thros[index]->exec(pPipeCom, this->getInsertionSequence());
        std::string lastError   =   _thros[index]->takeLastError();
        bool        bEmpty      =   lastError.empty();
        if (!ret && !bEmpty)
        {
            //报错
            WD_WARN_T("ErrorUiComNodePipeComponent", lastError);
        }
        _core.needRepaint();
    }
}
void UiComNodePipeComponent::slotForBtnMoveClicked(bool)
{
    // 移动的节点必须是管件类型节点
    auto pCurrent = _core.nodeTree().currentNode();
    if (pCurrent == nullptr || pCurrent->isType("TUBI") || (!IsPipeCom(*pCurrent)))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }

    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pCurrent, { "Position WRT World" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    // 获取当前管件移动方向
    // 判断管件的前面连接是否正确
    WD::DVec3 dir;
    const auto& gTrans = pCurrent->globalTransform();
    auto isBeforeRighet = CheckConnectState(*pCurrent, true);
    if(isBeforeRighet)
    {
        // 当前管件前面正确连接
        // 沿管件入口点的反方向移动
        auto ppoint = pCurrent->keyPoint(pCurrent->getAttribute("Arrive").toInt());
        if(ppoint != nullptr)
            dir = -(ppoint->transformedDirection(gTrans));
    }
    else
    {
        // 前面连接不正确
        // 判断后面连接是否正确
        auto isAfterRighet = CheckConnectState(*pCurrent, false);
        if(isAfterRighet)
        {
            // 管件前面连接不正确，后面连接正确
            // 沿管件出口点方向移动
            auto ppoint = pCurrent->keyPoint(pCurrent->getAttribute("Leave").toInt());
            if(ppoint != nullptr)
                dir = ppoint->transformedDirection(gTrans);
        }
        else
        {
            // 管件前面连接不正确，后面连接也不正确
            // 沿管件入口点方向反方向移动
            auto ppoint = pCurrent->keyPoint(pCurrent->getAttribute("Arrive").toInt());
            if(ppoint != nullptr)
                dir = -(ppoint->transformedDirection(gTrans));
        }
    }
    // 默认移动距离为100
    double distance = ui.doubleSpinBoxMove->value();

    // 获取偏移量
    auto offset = dir * distance;
    auto newPos = pCurrent->globalTranslation() + offset;

    // undo/redo
    auto cmd = WD::WDBMBase::MakeAttributeSetCommand(pCurrent, "Position WRT World", WD::WDBMAttrValue(newPos));
    if (cmd != nullptr)
    {
        cmd->setNoticeAfterRedo([this, pCurrent](const WD::WDUndoCommand&)
        {
            pCurrent->update();
            auto pBran = pCurrent->parent();
            if (pBran != nullptr && pBran->isType("BRAN"))
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
        cmd->setNoticeAfterUndo([this, pCurrent](const WD::WDUndoCommand&)
        {
            pCurrent->update();
            auto pBran = pCurrent->parent();
            if (pBran != nullptr && pBran->isType("BRAN"))
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
        _core.undoStack().push(cmd);
    }
}
void UiComNodePipeComponent::slotBackBtnMoveClicked(bool)
{
    // 移动的节点必须是管件类型节点
    auto pCurrent = _core.nodeTree().currentNode();
    if (pCurrent == nullptr || pCurrent->isType("TUBI") || (!IsPipeCom(*pCurrent)))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Must select component!");
        return;
    }

    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pCurrent, { "Position WRT World" }, bCancelMd))
        return;
    if (bCancelMd)
        return;

    // 获取当前管件移动方向
    // 判断管件的前面连接是否正确
    WD::DVec3 dir;
    const auto& gTrans = pCurrent->globalTransform();
    auto isBeforeRighet = CheckConnectState(*pCurrent, true);
    if (isBeforeRighet)
    {
        // 当前管件前面正确连接
        // 沿管件入口点的方向移动
        auto ppoint = pCurrent->keyPoint(pCurrent->getAttribute("Arrive").toInt());
        if(ppoint != nullptr)
            dir = ppoint->transformedDirection(gTrans);
    }
    else
    {
        // 前面连接不正确
        // 判断后面连接是否正确
        auto isAfterRighet = CheckConnectState(*pCurrent, false);
        if (isAfterRighet)
        {
            // 管件前面连接不正确，后面连接正确
            // 沿管件出口点反方向移动
            auto ppoint = pCurrent->keyPoint(pCurrent->getAttribute("Leave").toInt());
            if(ppoint != nullptr)
                dir = -(ppoint->transformedDirection(gTrans));
        }
        else
        {
            // 管件前面连接不正确，后面连接也不正确
            // 沿管件入口点方向方向移动
            auto ppoint = pCurrent->keyPoint(pCurrent->getAttribute("Arrive").toInt());
            if(ppoint != nullptr)
                dir = ppoint->transformedDirection(gTrans);
        }
    }

    // 默认移动距离为100
    double distance = ui.doubleSpinBoxMove->value();

    // 获取偏移量
    auto offset = dir * distance;
    auto newPos = pCurrent->globalTranslation() + offset;

    // undo/redo
    auto cmd = WD::WDBMBase::MakeAttributeSetCommand(pCurrent, "Position WRT World", WD::WDBMAttrValue(newPos));
    if (cmd != nullptr)
    {
        cmd->setNoticeAfterRedo([this, pCurrent](const WD::WDUndoCommand&)
        {
            pCurrent->update();
            auto pBran = pCurrent->parent();
            if (pBran != nullptr && pBran->isType("BRAN"))
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
        cmd->setNoticeAfterUndo([this, pCurrent](const WD::WDUndoCommand&)
        {
            pCurrent->update();
            auto pBran = pCurrent->parent();
            if (pBran != nullptr && pBran->isType("BRAN"))
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
        _core.undoStack().push(cmd);
    }
}
void UiComNodePipeComponent::slotBtnCreateBRANClicked()
{
    //打开创建分支界面
    UiCommandNotice cmdNotice("command.design.show.create.pipe.level.branch");
    _mainWindow.sendCommandNotice("ui_com_design_createPipeline", &cmdNotice);
}
void UiComNodePipeComponent::slotHeadTailChanged(int index)
{
    WDUnused(index);
    QVariant userData = ui.comboBoxHeadTail->currentData();
    if (!userData.isValid())
        return;
    _connectHeadTailType = WD::PipeBranchConnectTypeFromName(userData.value<QString>().toUtf8().data());
}
void UiComNodePipeComponent::slotConnectObjTypeChanged(int index)
{
    WDUnused(index);
    WD::WDNode::SharedPtr pBranch = _pBranch.lock();
    if (pBranch == nullptr)
        return;

    // 申领分支对象
    switch (_connectHeadTailType)
    {
    case WD::Connect_Head:
        {
            // 申领对象
            bool bCancelMd = false;
            if (!_core.getBMDesign().claimMgr().checkUpdate(pBranch, { "Hposition WRT World", "Hbore", "Hconnect","Hdirection WRT World" }, bCancelMd))
                return;
            if (bCancelMd)
                return;
        }
        break;
    case WD::Connect_Tail:
        {
            // 申领对象
            bool bCancelMd = false;
            if (!_core.getBMDesign().claimMgr().checkUpdate(pBranch, { "Tposition WRT World", "Tbore", "Tconnect","Tdirection WRT World" }, bCancelMd))
                return;
            if (bCancelMd)
                return;
        }
        break;
    default:
        break;
    }

    QVariant userData = ui.comboBoxConnectObjType->currentData();
    if (!userData.isValid())
        return;
    _connectObjType = userData.value<QString>().toUtf8().data();
    if (_connectObjType.empty())
        return;
    // 不同连接对象类型绑定不同事件
    if (_connectObjType == Name)
    {
        _pNameTypeDialog->exec();
    }
    else if (_connectObjType == Position)
    {
        _pPositionTypeDialog->update(*pBranch, _connectHeadTailType);
        _pPositionTypeDialog->show();
    }
    else if (_connectObjType == FirstMember || _connectObjType == LastMember)
    {
        this->branchConnect();
    }
    else
    {
        // 设置当前捕捉的功能为分支连接
        _captureFunc = CF_BranConnect;

        //激活捕捉工具
        WD::WDCapturePositioning& tool = _core.viewer().capturePositioning();
        WD::WDCapturePositioningParam   param;
        param.pMonitor = this;
        WD::WDCapturePositioningOption option(WD::WDCapturePositioningType::CPT_Element);
        tool.activate(param, option);
    }
}
void UiComNodePipeComponent::onPositionOKClicked()
{
    this->branchPosConnect();
}

void UiComNodePipeComponent::rotationPipeCom(WD::WDNode::SharedPtr pCom, double angle)
{
    if(pCom == nullptr)
        return;
    auto func = [this, pCom](double ang)
    {
        switch (this->getInsertionSequence())
        {
        case WD::PipeFlowDir::PFD_Forward:
            {
                this->forwardRotateByAngle(pCom, ang);
            }
            break;
        case WD::PipeFlowDir::PFD_Backward:
            {
                this->backwardRotateByAngle(pCom, ang);
            }
            break;
        default:
            break;
        }
        _core.needRepaint();
    };
    // undo/redo
    auto bm = _core.currentBM();
    if (bm != nullptr)
    {
        auto cmd = new WD::WDUndoCommand("rotationPipeCom");
        if(cmd != nullptr)
        {
            cmd->setNoticeAfterRedo([angle, func](const WD::WDUndoCommand&)
            {
                func(angle);
            });
            cmd->setNoticeAfterUndo([angle, func](const WD::WDUndoCommand&)
            {
                func(-angle);
            });
            _core.undoStack().push(cmd);
        }
    }
}

WD::WDNode::SharedPtr UiComNodePipeComponent::getParentNode(const std::string& type) const
{
    auto pCurrNode = _core.nodeTree().currentNode();
    if (pCurrNode == nullptr)
        return nullptr;
    // 使用当前选中节点以及挂载规则获取管件可挂载的父节点
    return _core.getBMDesign().findParentWithType(*pCurrNode, type);
}

bool UiComNodePipeComponent::namedChildExist(WD::WDNode& parentNode, const std::string& name)
{
    return WD::WDNode::ChildrenTraversalHelpterR(parentNode
        , [](const std::string& name, WD::WDNode& node)
        {
            return node.name() == name;
        }, name);
}

bool UiComNodePipeComponent::restoreConnection(WD::WDNode::SharedPtr pPipeComponentNode
    , bool autoConnection
    , double spoolDistance)
{
    bool ret = false;
    switch (this->getInsertionSequence())
    {
    case WD::PipeFlowDir::PFD_Forward:
        {
            ret = this->forwardRestoreConnect(pPipeComponentNode, autoConnection, spoolDistance);
        }
        break;
    case WD::PipeFlowDir::PFD_Backward:
        {
            ret = this->backwardRestoreConnect(pPipeComponentNode, autoConnection, spoolDistance);
        }
        break;
    default:
        break;
    }
    return ret;
}

/**
* @brief 校验是否可以建立直管连接
*/
enum CheckRet
{
    //两点重合
    CR_None,
    //两点连成线段
    CR_Segment,
    //直管
    CR_Tubing,
};
WD::WDNode::SharedPtr UiComNodePipeComponent::getLastComFromHeadPart(WD::WDNode::SharedPtr pBranch)
{
    if (pBranch == nullptr || !pBranch->isType("BRAN"))
        return nullptr;
    WD::WDNode::Nodes   coms    =   WD::WDBMDPipeUtils::PipeComponents(pBranch);
    if (coms.empty())
        return nullptr;

    // 第一个管件节点是否连接分支头
    WD::WDNode::SharedPtr   frontCom    =   coms.front();
    if (frontCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*frontCom))
        return nullptr;
    const WD::WDKeyPoint*   pFrontArrive=   frontCom->keyPoint(frontCom->getAttribute("Arrive").toInt());
    if (pFrontArrive == nullptr)
        return nullptr;

    WD::DVec3   gPrevPos    =   pBranch->getAttribute("Hposition WRT World").toDVec3();
    WD::DVec3   gPrevDir    =   pBranch->getAttribute("Hdirection WRT World").toDVec3();
    WD::DVec3   gNextPos    =   pFrontArrive->transformedPosition(frontCom->globalTransform());
    WD::DVec3   gNextDir    =   pFrontArrive->transformedDirection(frontCom->globalRSTransform());
    double      ev          =   WD::WDDeviation::GetDistDeviation(WD::DVec3::Distance(gPrevPos, gNextPos));
    auto        result      =   WD::DKeyPoint::CheckConnectionTo(gPrevPos, gPrevDir, gNextPos, gNextDir, ev);
    if (!result.first)
        return nullptr;
    if (coms.size() == 1)
        return frontCom;

    // 尾部段节点连接校验
    WD::WDNode::SharedPtr   pPrevCom    =   frontCom;
    for (int i = 1; i < coms.size(); ++i)
    {
        if (coms[i] == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*coms[i])
            || pPrevCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pPrevCom))
            continue;

        auto    pPrevLeave      =   pPrevCom->keyPoint(pPrevCom->getAttribute("Leave").toInt());
        auto    pNextArrive     =   coms[i]->keyPoint(coms[i]->getAttribute("Arrive").toInt());;
        assert(pPrevLeave != nullptr && pNextArrive != nullptr);
        if (pPrevLeave == nullptr || pNextArrive == nullptr)
            continue;
        gPrevPos    =   pPrevLeave->transformedPosition(pPrevCom->globalTransform());
        gPrevDir    =   pPrevLeave->transformedDirection(pPrevCom->globalRSTransform());
        gNextPos    =   pNextArrive->transformedPosition(coms[i]->globalTransform());
        gNextDir    =   pNextArrive->transformedDirection(coms[i]->globalRSTransform());
        ev          =   WD::WDDeviation::GetDistDeviation(WD::DVec3::Distance(gPrevPos, gNextPos));
        result      =   WD::DKeyPoint::CheckConnectionTo(gPrevPos, gPrevDir, gNextPos, gNextDir, ev);
        if (!result.first)
            return pPrevCom;
        pPrevCom = coms[i];
    }
    return pPrevCom;
}
WD::WDNode::SharedPtr UiComNodePipeComponent::getFirstComFromTailPart(WD::WDNode::SharedPtr pBranch)
{
    if (pBranch == nullptr || !pBranch->isType("BRAN"))
        return nullptr;
    WD::WDNode::Nodes       coms        =   WD::WDBMDPipeUtils::PipeComponents(pBranch);
    if (coms.empty())
        return nullptr;

    // 最后一个管件节点是否连接分支尾
    WD::WDNode::SharedPtr   backCom     =   coms.back();
    if (backCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*backCom))
        return nullptr;
    auto                    pBackLeave  =   backCom->keyPoint(backCom->getAttribute("Leave").toInt());
    if (pBackLeave == nullptr)
        return nullptr;
    WD::DVec3   gPrevPos    =   pBackLeave->transformedPosition(backCom->globalTransform());
    WD::DVec3   gPrevDir    =   pBackLeave->transformedDirection(backCom->globalRSTransform());

    WD::DVec3   gNextPos    =   pBranch->getAttribute("Tposition WRT World").toDVec3();
    WD::DVec3   gNextDir    =   pBranch->getAttribute("Tdirection WRT World").toDVec3();
    double      ev          =   WD::WDDeviation::GetDistDeviation(WD::DVec3::Distance(gPrevPos, gNextPos));
    auto        result      =   WD::DKeyPoint::CheckConnectionTo(gPrevPos, gPrevDir, gNextPos, gNextDir, ev);
    if (!result.first)
        return nullptr;
    if (coms.size() == 1)
        return backCom;

    // 尾部段节点连接校验
    WD::WDNode::SharedPtr           pNextCom    =   backCom;
    for (int i = (int)coms.size() - 2; i >= 0; --i)
    {
        if (coms[i] == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*coms[i])
            || pNextCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pNextCom))
            continue;

        auto    pPrevLeave      =   coms[i]->keyPoint(coms[i]->getAttribute("Leave").toInt());
        auto    pNextArrive     =   pNextCom->keyPoint(pNextCom->getAttribute("Arrive").toInt());
        assert(pPrevLeave != nullptr && pNextArrive != nullptr);
        if (pPrevLeave == nullptr || pNextArrive == nullptr)
            continue;
        gPrevPos    =   pPrevLeave->transformedPosition(coms[i]->globalTransform());
        gPrevDir    =   pPrevLeave->transformedDirection(coms[i]->globalRSTransform());
        gNextPos    =   pNextArrive->transformedPosition(pNextCom->globalTransform());
        gNextDir    =   pNextArrive->transformedDirection(pNextCom->globalRSTransform());
        ev          =   WD::WDDeviation::GetDistDeviation(WD::DVec3::Distance(gPrevPos, gNextPos));
        result      =   WD::DKeyPoint::CheckConnectionTo(gPrevPos, gPrevDir, gNextPos, gNextDir, ev);
        if (!result.first)
            return pNextCom;
        pNextCom = coms[i];
    }
    return pNextCom;
}

WD::WDNode::SharedPtr UiComNodePipeComponent::createPipe(WD::WDNode::SharedPtr pBran
    , const std::string& type, WD::WDNode::SharedPtr pSpco, WD::WDNode& cur)
{
    auto& mgr = _core.getBMDesign();
    // 根据当前选中的节点获取节点插入的下一个节点
    auto sequence = this->getInsertionSequence();
    WD::WDNode::SharedPtr pNextNode = nullptr;
    if (cur.isType("BRAN"))
    {
        switch (sequence)
        {
        case WD::PFD_Forward:
            {
                auto pComs = WD::WDBMDPipeUtils::PipeComponents(pBran);
                if (!pComs.empty())
                {
                    pNextNode = pComs.front();
                }
            }
            break;
        case WD::PFD_Backward:
            {
            }
            break;
        default:
            break;
        }
    }
    // 当前选中管件节点
    else if (WD::WDBMDPipeUtils::IsPipeComponent(cur))
    {
        switch (sequence)
        {
        case WD::PFD_Forward:
            {
                pNextNode = WD::WDBMDPipeUtils::NextPipeComponent(pBran, WD::WDNode::ToShared(&cur));
            }
            break;
        case WD::PFD_Backward:
            {
                pNextNode = WD::WDNode::ToShared(&cur);
            }
            break;
        default:
            break;
        }
    }

    // 申领父节点对象
    if (!mgr.claimMgr().checkAdd(pBran, pNextNode))
        return nullptr;

    auto pComNode = mgr.create(pBran, type, pNextNode);
    if (pComNode == nullptr)
        return nullptr;

    // 设置等级引用节点
    pComNode->setAttribute("Spref", WD::WDBMAttrValue(WD::WDBMNodeRef(pSpco)));
    
    // 设置默认颜色
    mgr.colorTable().setNodeColor(*pComNode);

    // 设置默认半径
    WD::WDBMDPipeUtils::UpdateDefaultRadiusToDDRADIUS(*pComNode);

    // 设置管件保温伴热
    pComNode->setAttribute("Ispec", WD::WDBMNodeRef(_pParamsWidget->getInsuSPEC()));
    pComNode->setAttribute("Tspec", WD::WDBMNodeRef(_pParamsWidget->getTracSPEC()));

    // 更新管件模型
    pComNode->updateModel();

    return pComNode;
}
std::string UiComNodePipeComponent::connectBore()
{
    // 获取当前选中的分支节点
    std::string connectBore = "";
    auto pCurrentNode = getCurrentPipeBusinessNode();
    if(pCurrentNode == nullptr)
    {
        return connectBore;
    }
    switch (this->getInsertionSequence())
    {
    case WD::PipeFlowDir::PFD_Forward:
        {
            connectBore = this->forwardBore(pCurrentNode);
        }
        break;
    case WD::PipeFlowDir::PFD_Backward:
        {
            connectBore = this->backwardBore(pCurrentNode);
        }
        break;
    default:
        break;
    }
    return connectBore;
}

WD::WDNode::SharedPtr UiComNodePipeComponent::getPrevPipeComponentNode() const
{
    WD::WDNode::SharedPtr pNode = _core.nodeTree().currentNode();
    if (pNode == nullptr)
        return nullptr;

    if (WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
    {
        return pNode;
    }
    else if (pNode->isType("BRAN"))
    {
        WD::WDNode::Nodes pipeComs = WD::WDBMDPipeUtils::PipeComponents(pNode);
        if (pipeComs.empty())
            return pNode;
        return pipeComs.back();
    }
    else if (pNode->isType("TUBI"))
    {
        return WD::WDBMDPipeUtils::PrevPipeComponent(pNode->parent(), pNode);
    }
    else
        return nullptr;
}
void UiComNodePipeComponent::setSelectSPEC()
{
    // 获取当前选中的分支节点
    auto pCurrent  =   _core.nodeTree().currentNode();
    if (pCurrent == nullptr)
        return ;

    // 设置选中SPEC
    _pParamsWidget->updateByNode(*pCurrent);
}

WD::PipeFlowDir UiComNodePipeComponent::getInsertionSequence() const
{
    if (ui.forwardRadioButton->isChecked())
        return WD::PipeFlowDir::PFD_Forward;
    else if (ui.backwordRadioButton->isChecked())
        return WD::PipeFlowDir::PFD_Backward;
    else
        return WD::PipeFlowDir::PFD_Forward;
}

UiComNodePipeComponent::DistanceType UiComNodePipeComponent::getDistanceType() const
{
    if (ui.comboBoxDistanceType->currentIndex() == 0)
        return DistanceType::Spool;
    else if (ui.comboBoxDistanceType->currentIndex() == 1)
        return DistanceType::Distance;
    else
        return DistanceType::Spool;
}

void                                    UiComNodePipeComponent::forwardOrientation(
    WD::WDNode::SharedPtr pBranch
    , WD::WDNode::SharedPtr pCom)
{
    if (pBranch == nullptr || !pBranch->isType("BRAN"))
        return;
    // 取出当前节点的管件数据
    if (pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCom))
        return;

    // 出口点(分支起点或前一个管件的出口点)
    WD::DVec3   gPosLeave   =   pBranch->getAttribute("Hposition WRT World").toDVec3();
    WD::DVec3   gDirLeave   =   pBranch->getAttribute("Hdirection WRT World").toDVec3();
    // 分支起点或前一个管件的 世界坐标位置
    WD::DVec3   gPosPrev    =   gPosLeave;
    // 当前管件的世界坐标位置
    WD::DVec3   gPos        =   pCom->globalTranslation();
    // 入口点
    auto        pPtArrive   =   pCom->keyPoint(pCom->getAttribute("Arrive").toInt());
    if (pPtArrive == nullptr)
        return;
    WD::DVec3   gPosArrive  =   pPtArrive->transformedPosition(pCom->globalTransform());
    WD::DVec3   gDirArrive  =   pPtArrive->transformedDirection(pCom->globalRSTransform());
    // 获取前一个管件
    auto        pPrevCom    =   WD::WDBMDPipeUtils::PrevPipeComponent(pBranch, pCom);
    if (pPrevCom != nullptr && WD::WDBMDPipeUtils::IsPipeComponent(*pPrevCom))
    {
        //拿到管件数据
        const WD::WDKeyPoint* pPt = pPrevCom->keyPoint(pPrevCom->getAttribute("Leave").toInt());
        if (pPt != nullptr)
        {
            gPosLeave   =   pPt->transformedPosition(pPrevCom->globalTransform());
            gDirLeave   =   WD::DVec3::Normalize(pPt->transformedDirection(pPrevCom->globalRSTransform()));
            gPosPrev    =   pPrevCom->globalTranslation();
        }
    }
    // 获取距离
    double distance = _pComboBoxDistance->currentText().toDouble();

    // 获取原位置
    auto prePos = pCom->globalTranslation();
    // undo/redo
    auto bm = _core.currentBM();

    // 距离
    DistanceType type = this->getDistanceType();
    switch (type)
    {
    case DistanceType::Spool:
        {// 使用当前管件入口点到前一个管件出口点运用距离
            WD::DVec3   rGPos = gPosLeave + gDirLeave * distance;
            auto        gOffset = rGPos - gPosArrive;
            if (bm != nullptr)
            {
                auto cmd = new WD::WDUndoCommand("Pipe Spool Distance");
                if(cmd != nullptr)
                {
                    cmd->setNoticeAfterRedo([pCom, gOffset, this, pBranch](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", pCom->getAttribute("Position WRT World").toDVec3() + gOffset);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    cmd->setNoticeAfterUndo([pCom, this, pBranch, prePos](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", prePos);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    _core.undoStack().push(cmd);
                }
            }
        }
        break;
    case DistanceType::Distance:
        {// 使用当前管件p0点到前一个管件p0运用距离
            WD::DVec3   rGPos       =   gPosPrev + gDirLeave * distance;

            if (bm != nullptr)
            {
                auto cmd = new WD::WDUndoCommand("Pipe Distance");
                if(cmd != nullptr)
                {
                    cmd->setNoticeAfterRedo([pCom, rGPos, this, pBranch](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", rGPos);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    cmd->setNoticeAfterUndo([pCom, this, pBranch, prePos](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", prePos);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    _core.undoStack().push(cmd);
                }
            }

        }break;
    default:
        break;
    }

}
void                                    UiComNodePipeComponent::backwardOrientation(
    WD::WDNode::SharedPtr pBranch
    , WD::WDNode::SharedPtr pCom)
{
    if (pBranch == nullptr || !pBranch->isType("BRAN"))
        return;
    // 获取是否自动连接
    if (pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCom))
        return;

    // 获取插入顺序
    WD::DVec3   gPosArrive  =   pBranch->getAttribute("Tposition WRT World").toDVec3();
    WD::DVec3   gDirArrive  =   pBranch->getAttribute("Tdirection WRT World").toDVec3();
    // 当前创建的管件类型
    WD::DVec3   gPosNext    =   gPosArrive;
    // 创建管件数据对象
    WD::DVec3   gPos        =   pCom->globalTranslation();
    // 当前管件出口点
    auto        pPtLeave    =   pCom->keyPoint(pCom->getAttribute("Leave").toInt());
    if (pPtLeave == nullptr)
        return;
    WD::DVec3   gPosLeave   =   pPtLeave->transformedPosition(pCom->globalTransform());
    WD::DVec3   gDirLeave   =   pPtLeave->transformedDirection(pCom->globalRSTransform());
    // 获取后一个管件
    auto        pNextCom    =   WD::WDBMDPipeUtils::NextPipeComponent(pBranch, pCom);
    if (pNextCom != nullptr && WD::WDBMDPipeUtils::IsPipeComponent(*pNextCom))
    {
        //拿到管件数据
        const WD::WDKeyPoint* pPt = pNextCom->keyPoint(pNextCom->getAttribute("Arrive").toInt());
        if (pPt != nullptr)
        {
            gPosArrive  =   pPt->transformedPosition(pNextCom->globalTransform());
            gDirArrive  =   WD::DVec3::Normalize(pPt->transformedDirection(pNextCom->globalRSTransform()));
            gPosNext    =   pNextCom->globalTranslation();
        }
    }
    // 获取距离
    double          distance    = _pComboBoxDistance->currentText().toDouble();

    // 获取原位置
    auto prePos = pCom->globalTranslation();
    // undo/redo
    auto bm = _core.currentBM();

    // 距离
    DistanceType    type        =   this->getDistanceType();
    switch (type)
    {
    case DistanceType::Spool:
        {
            WD::DVec3   rGPos       =   gPosArrive + gDirArrive * distance;
            auto        gOffset     =   rGPos - gPosLeave;

            if (bm != nullptr)
            {
                auto cmd = new WD::WDUndoCommand("Pipe Spool Distance");
                if(cmd != nullptr)
                {
                    cmd->setNoticeAfterRedo([pCom, gOffset, this, pBranch](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", pCom->getAttribute("Position WRT World").toDVec3() + gOffset);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    cmd->setNoticeAfterUndo([pCom, this, pBranch, prePos](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", prePos);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    _core.undoStack().push(cmd);
                }
            }
        }
        break;
    case DistanceType::Distance:
        {// 使用当前管件p0点到后一个管件p0运用距离
            WD::DVec3   rGPos       =   gPosNext + gDirArrive * distance;

            if (bm != nullptr)
            {
                auto cmd = new WD::WDUndoCommand("");
                if(cmd != nullptr)
                {
                    cmd->setNoticeAfterRedo([pCom, rGPos, this, pBranch](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", rGPos);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    cmd->setNoticeAfterUndo([pCom, this, pBranch, prePos](const WD::WDUndoCommand&)
                    {
                        pCom->setAttribute("Position WRT World", prePos);
                        pCom->update();
                        //更新分支的连接
                        pBranch->triggerUpdate(true);
                        _core.needRepaint();
                    });
                    _core.undoStack().push(cmd);
                }
            }
        }break;
    default:
        break;
    }
}
void                                    UiComNodePipeComponent::forwardRotateByDirection(
    WD::WDNode::SharedPtr pCom
    , const WD::DVec3& direction)
{
    if (pCom == nullptr)
        return ;

    WD::SetElboLeaveDirection(*pCom, direction);
}
void                                    UiComNodePipeComponent::backwardRotateByDirection(
    WD::WDNode::SharedPtr pCom
    , const WD::DVec3& direction)
{
    if (pCom == nullptr)
        return ;

    WD::SetElboArriveDirection(*pCom, direction);
}

bool   UiComNodePipeComponent::forwardRestoreConnect(
    WD::WDNode::SharedPtr pCom
    , bool autoConnection
    , double spoolDistance)
{
    WDUnused(spoolDistance);
    if (pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCom))
        return false;
    // 当前管件所属的分支节点
    auto pBran = pCom->parent();
    if (pBran == nullptr || !pBran->isType("BRAN"))
        return false;
    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pCom, { "Position", "Orientation"}, bCancelMd))
        return false;
    if (bCancelMd)
        return false;

    // 不自动连接，直接设置世界坐标位置与旋转返回
    if (!autoConnection)
    {
        pCom->setAttribute("Position WRT World", WD::DVec3(0.0));
        pCom->setAttribute("Orientation WRT World", WD::DQuat::Identity());
        pCom->update();
        //更新分支的连接
        pBran->triggerUpdate(true);

        return true;
    }

    pCom->update();

    // 获取上游坐标朝向
    WD::DVec3   gLeavePos;
    WD::DVec3   gLeaveDir;
    WD::WDNode::SharedPtr pPrev = WD::WDBMDPipeUtils::PrevPipeComponent(pBran, pCom);
    if (pPrev == nullptr)
    { //如果分支不存在管件，则将管件 arrive点 默认放到分支起点

        gLeavePos   =   pBran->getAttribute("Hposition WRT World").toDVec3();
        gLeaveDir   =   pBran->getAttribute("Hdirection WRT World").toDVec3();
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pPrev))
    {
        auto pPtLeave = pPrev->keyPoint(pPrev->getAttribute("Leave").toInt());
        if (pPtLeave != nullptr)
        {
            gLeavePos   =   pPtLeave->transformedPosition(pPrev->globalTransform());
            gLeaveDir   =   WD::DVec3::Normalize(pPtLeave->transformedDirection(pPrev->globalRSTransform()));
        }
    }
    else
    {
        assert(false);
    }

    // 管件出入口朝向对齐
    WD::ComponentAlignDir(_core, *pCom, -gLeaveDir, WD::PipeFlowDir::PFD_Forward);

    // 管件P0朝向对齐世界坐标系Z轴
    {
        // 上游朝向和原点构成旋转平面
        auto rotPlane = WD::DPlane(gLeaveDir, WD::DVec3::Zero());

        // 世界坐标系Z轴投影到旋转平面
        auto projGZ = rotPlane.project(WD::DVec3::AxisZ());
        if (!WD::DVec3::SamePosition(projGZ, WD::DVec3::Zero()))
        {
            // 管件P0朝向投影到旋转平面
            auto p0Dir = pCom->globalRSTransform() * WD::DVec3::AxisZ();
            auto projGP = rotPlane.project(p0Dir);
            if (!WD::DVec3::SamePosition(projGP, WD::DVec3::Zero()) && !WD::DVec3::InTheSameDirection(projGZ, projGP))
            {
                // 管件绕旋转平面法线，从P0朝向投影旋转到世界坐标系Z轴投影
                auto angle = WD::DVec3::Angle(projGP, projGZ);
                auto cross = WD::DVec3::Cross(projGP, projGZ);
                if (WD::DVec3::Dot(cross, gLeaveDir) < 0)
                    angle = -angle;
                pCom->rotate(gLeaveDir, angle);
                pCom->update();
            }
        }
    }

    // 管件出口点朝向旋转到世界坐标系XOY平面
    {
        // 获取管件出口点朝向
        auto pComLeavePt = pCom->keyPoint(pCom->getAttribute("Leave").toInt());
        auto pComArrivePt = pCom->keyPoint(pCom->getAttribute("Arrive").toInt());
        if (pComLeavePt != nullptr && pComArrivePt != nullptr)
        {
            auto gComLeaveDir = pComLeavePt->transformedDirection(pCom->globalRSTransform());
            auto gComArriveDir = pComArrivePt->transformedDirection(pCom->globalRSTransform());
            if (!WD::DVec3::OnTheSameLine(gComLeaveDir, gComArriveDir))
            {
                // 将出口朝向投影到XOY平面
                auto XOYPlane = WD::DPlane(WD::DVec3::AxisZ(), WD::DVec3::Zero());
                auto projXOYLeaveDir = XOYPlane.project(gComLeaveDir);
                if (!WD::DVec3::SamePosition(projXOYLeaveDir, WD::DVec3::Zero())) // 投影向量与XOY法线即Z轴不平行
                {
                    // 上游朝向和原点构成旋转平面
                    auto rotPlane = WD::DPlane(gLeaveDir, WD::DVec3::Zero());
                    // 将出口朝向投影到XOY平面的向量投影到旋转平面
                    auto projProjDir = rotPlane.project(projXOYLeaveDir);
                    if (!WD::DVec3::SamePosition(projProjDir, WD::DVec3::Zero()))
                    {
                        // 将出口朝向投影到旋转平面
                        auto projRotLeaveDir = rotPlane.project(gComLeaveDir);
                        if (!WD::DVec3::SamePosition(projRotLeaveDir, WD::DVec3::Zero())
                            && !WD::DVec3::InTheSameDirection(projRotLeaveDir, projProjDir))
                        {
                            // 管件绕旋转平面法线，从出口点朝向旋转到世界坐标系XOY平面
                            auto angle = WD::DVec3::Angle(projRotLeaveDir, projProjDir);
                            auto cross = WD::DVec3::Cross(projRotLeaveDir, projProjDir);
                            if (WD::DVec3::Dot(cross, gLeaveDir) < 0)
                                angle = -angle;
                            pCom->rotate(gLeaveDir, angle);
                            pCom->update();
                        }
                    }
                }
                else // 投影向量与XOY法线即Z轴平行
                {
                    pCom->rotate(gLeaveDir, 90);
                    pCom->update();
                }
            }
        }
    }

    // 管件出入口位置对齐
    WD::ComponentAlignPos(_core, *pCom, gLeavePos + gLeaveDir * spoolDistance, WD::PipeFlowDir::PFD_Forward);

    //更新分支的连接
    pBran->triggerUpdate(true);
    return true;
}
bool   UiComNodePipeComponent::backwardRestoreConnect(
    WD::WDNode::SharedPtr pCom
    , bool autoConnection
    , double spoolDistance)
{
    WDUnused(spoolDistance);
    if (pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCom))
        return false;
    // 当前管件所属的分支节点
    auto pBran = pCom->parent();
    if (pBran == nullptr || !pBran->isType("BRAN"))
        return false;
    // 申领对象
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pCom, { "Position", "Orientation"}, bCancelMd))
        return false;
    if (bCancelMd)
        return false;

    // 不自动连接，直接设置世界坐标位置与旋转返回
    if (!autoConnection)
    {
        pCom->setAttribute("Position WRT World", WD::DVec3(0.0));
        pCom->setAttribute("Orientation WRT World", WD::DQuat::Identity());
        pCom->update();
        //更新分支的连接
        pBran->triggerUpdate(true);

        return true;
    }

    pCom->update();

    // 获取上游坐标朝向
    WD::DVec3   gArrivePos;
    WD::DVec3   gArriveDir;
    WD::WDNode::SharedPtr pNext = WD::WDBMDPipeUtils::NextPipeComponent(pBran, pCom);
    if (pNext == nullptr)
    { //如果分支不存在管件，则将管件 leave点 默认放到分支终点

        gArrivePos   =   pBran->getAttribute("Tposition WRT World").toDVec3();
        gArriveDir   =   pBran->getAttribute("Tdirection WRT World").toDVec3();
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pNext))
    {
        auto pPtArrive = pNext->keyPoint(pNext->getAttribute("Arrive").toInt());
        if (pPtArrive != nullptr)
        {
            gArrivePos   =   pPtArrive->transformedPosition(pNext->globalTransform());
            gArriveDir   =   WD::DVec3::Normalize(pPtArrive->transformedDirection(pNext->globalRSTransform()));
        }
    }
    else
    {
        assert(false);
    }

    // 管件出入口朝向对齐
    WD::ComponentAlignDir(_core, *pCom, -gArriveDir, WD::PipeFlowDir::PFD_Backward);

    // 管件P0朝向对齐世界坐标系Z轴
    {
        // 上游朝向和原点构成旋转平面
        auto rotPlane = WD::DPlane(gArriveDir, WD::DVec3::Zero());

        // 世界坐标系Z轴投影到旋转平面
        auto projGZ = rotPlane.project(WD::DVec3::AxisZ());
        if (!WD::DVec3::SamePosition(projGZ, WD::DVec3::Zero()))
        {
            // 管件P0朝向投影到旋转平面
            auto p0Dir = pCom->globalRSTransform() * WD::DVec3::AxisZ();
            auto projGP = rotPlane.project(p0Dir);
            if (!WD::DVec3::SamePosition(projGP, WD::DVec3::Zero()) && !WD::DVec3::InTheSameDirection(projGZ, projGP))
            {
                // 管件绕旋转平面法线，从P0朝向投影旋转到世界坐标系Z轴投影
                auto angle = WD::DVec3::Angle(projGP, projGZ);
                auto cross = WD::DVec3::Cross(projGP, projGZ);
                if (WD::DVec3::Dot(cross, gArriveDir) < 0)
                    angle = -angle;
                pCom->rotate(gArriveDir, angle);
                pCom->update();
            }
        }
    }

    // 管件入口点朝向旋转到世界坐标系XOY平面
    {
        // 获取管件入口点朝向
        auto pComArrivePt = pCom->keyPoint(pCom->getAttribute("Arrive").toInt());
        auto pComLeavePt = pCom->keyPoint(pCom->getAttribute("Leave").toInt());
        if (pComArrivePt != nullptr && pComLeavePt != nullptr)
        {
            auto gComArriveDir = pComArrivePt->transformedDirection(pCom->globalRSTransform());
            auto gComLeaveDir = pComLeavePt->transformedDirection(pCom->globalRSTransform());
            if (!WD::DVec3::OnTheSameLine(gComArriveDir, gComLeaveDir))
            {
                // 将入口朝向投影到XOY平面
                auto XOYPlane = WD::DPlane(WD::DVec3::AxisZ(), WD::DVec3::Zero());
                auto projXOYArriveDir = XOYPlane.project(gComArriveDir);
                if (!WD::DVec3::SamePosition(projXOYArriveDir, WD::DVec3::Zero())) // 投影向量与XOY法线即Z轴不平行
                {
                    // 上游朝向和原点构成旋转平面
                    auto rotPlane = WD::DPlane(gArriveDir, WD::DVec3::Zero());
                    // 将出口朝向投影到XOY平面的向量投影到旋转平面
                    auto projProjDir = rotPlane.project(projXOYArriveDir);
                    if (!WD::DVec3::SamePosition(projProjDir, WD::DVec3::Zero()))
                    {
                        // 将入口朝向投影到旋转平面
                        auto projRotArriveDir = rotPlane.project(gComArriveDir);
                        if (!WD::DVec3::SamePosition(projRotArriveDir, WD::DVec3::Zero())
                            && !WD::DVec3::InTheSameDirection(projRotArriveDir, projProjDir))
                        {
                            // 管件绕旋转平面法线，从入口点朝向旋转到世界坐标系XOY平面
                            auto angle = WD::DVec3::Angle(projRotArriveDir, projProjDir);
                            auto cross = WD::DVec3::Cross(projRotArriveDir, projProjDir);
                            if (WD::DVec3::Dot(cross, gArriveDir) < 0)
                                angle = -angle;
                            pCom->rotate(gArriveDir, angle);
                            pCom->update();
                        }
                    }
                }
                else // 投影向量与XOY法线即Z轴平行
                {
                    pCom->rotate(gArriveDir, 90);
                    pCom->update();
                }
            }
        }
    }

    // 管件出入口坐标对齐
    WD::ComponentAlignPos(_core, *pCom, gArrivePos + gArriveDir * spoolDistance, WD::PipeFlowDir::PFD_Backward);

    //更新分支的连接
    pBran->triggerUpdate(true);
    return true;
}

std::string UiComNodePipeComponent::forwardBore(WD::WDNode::SharedPtr pNode)
{
    std::string connectBore = "";

    if (pNode == nullptr)
    {
        return connectBore;
    }

    if (pNode->isType("BRAN"))
    {
        connectBore = pNode->getAttribute("Hbore").toString();
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
    {
        auto pPtLeave = pNode->keyPoint(pNode->getAttribute("Leave").toInt());
        if (pPtLeave == nullptr)
            return "";
        return pPtLeave->bore();
    }
    else if (pNode->isType("TUBI"))
    {
        connectBore = WD::ToString<int>(pNode->getAttribute("Lbore").convertToInt());
    }

    return connectBore;
}
std::string UiComNodePipeComponent::backwardBore(WD::WDNode::SharedPtr pNode)
{
    std::string connectBore = "";

    if (pNode == nullptr)
    {
        return connectBore;
    }

    if (pNode->isType("BRAN"))
    {
        connectBore = pNode->getAttribute("Tbore").toString();
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
    {
        auto pPtArrive = pNode->keyPoint(pNode->getAttribute("Arrive").toInt());
        if (pPtArrive == nullptr)
            return "";
        return pPtArrive->bore();
    }
    else if (pNode->isType("TUBI"))
    {
        connectBore = WD::ToString<int>(pNode->getAttribute("Abore").convertToInt());
    }
    return connectBore;
}
std::string UiComNodePipeComponent::forwardConnect(WD::WDNode::SharedPtr pNode)
{
    std::string connectType = "";

    if (pNode == nullptr)
    {
        return connectType;
    }

    if (pNode->isType("BRAN"))
    {
        connectType = pNode->getAttribute("Hconnect").toString();
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
    {
        auto pPtLeave = pNode->keyPoint(pNode->getAttribute("Leave").toInt());
        if (pPtLeave != nullptr)
            connectType = pPtLeave->connType();
    }

    return connectType;
}
std::string UiComNodePipeComponent::backwardConnect(WD::WDNode::SharedPtr pNode)
{
    std::string connectType = "";

    if (pNode == nullptr)
    {
        return connectType;
    }

    // 返回分支的头连接方式，或关键的尾连接方式
    if (pNode->isType("BRAN"))
    {
        connectType = pNode->getAttribute("Tconnect").toString();
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
    {
        auto pPtArrive = pNode->keyPoint(pNode->getAttribute("Arrive").toInt());
        if (pPtArrive != nullptr)
            pPtArrive->connType();
    }

    return connectType;
}

WD::WDNode::SharedPtr UiComNodePipeComponent::getForwardPipeNode()
{
    // 当前节点为空
    auto pCurNode = getCurrentPipeBusinessNode();
    if(pCurNode == nullptr)
        return nullptr;

    // 当前节点为分支节点，返回该分支节点
    if (pCurNode->isType("BRAN"))
    {
        return pCurNode;
    }
    // 当前节点为管件节点
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pCurNode))
    {
        // 获取管件的前一个节点
        auto prevNode = WD::WDBMDPipeUtils::PrevPipeComponent(pCurNode->parent(), pCurNode);
        // 当前节点为分支下第一节点，返回分支节点
        if (prevNode == nullptr)
        {
            return pCurNode->parent();
        }
        // 返回当前节点的前一个节点
        else
        {
            return prevNode;
        }
    }
    return nullptr;
}
WD::WDNode::SharedPtr UiComNodePipeComponent::getBackwardPipeNode()
{
    auto pCurNode = getCurrentPipeBusinessNode();
    if(pCurNode == nullptr)
        return nullptr;

    // 当前节点为分支节点，返回分支节点
    if (pCurNode->isType("BRAN"))
    {
        return pCurNode;
    }
    //当前节点为管件类型节点
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pCurNode) || pCurNode->isType("TUBI"))
    {
        // 获取分支下当前节点的后一个节点
        auto pNextNode = WD::WDBMDPipeUtils::NextPipeComponent(pCurNode->parent(), pCurNode);
        // 当前节点为分支下的最后一个节点，返回分支节点（父节点）
        if (pNextNode == nullptr)
        {
            return pCurNode->parent();
        }
        // 返回当前节点的后一个节点
        else
        {
            return pNextNode;
        }
    }
    
    return nullptr;
}
WD::WDNode::SharedPtr UiComNodePipeComponent::getCOCOPipeNode()
{
    switch (this->getInsertionSequence())
    {
    case WD::PipeFlowDir::PFD_Forward:
        {
            return getForwardPipeNode();
        }
        break;
    case WD::PipeFlowDir::PFD_Backward:
        {
            return getBackwardPipeNode();
        }
        break;
    default:
        break;
    }
    return nullptr;
}
WD::WDNode::SharedPtr UiComNodePipeComponent::getCurrentPipeBusinessNode()
{
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr)
    {
        return nullptr;
    }
    if (pCurrentNode->isType("BRAN") || WD::WDBMDPipeUtils::IsPipeComponent(*pCurrentNode))
    {
        return pCurrentNode;
    }
    else if (pCurrentNode->isType("TUBI"))
    {
        auto pBranchNode = pCurrentNode->parent();
        if (pBranchNode == nullptr)
            return nullptr;
        WD::WDNode::SharedPtr pPipeComNode = nullptr;
        switch (getInsertionSequence())
        {
        case WD::PipeFlowDir::PFD_Forward:
            {
                // 找当前TUBI节点的前1个WDBMDPipeCOMS类型节点
                pPipeComNode = WD::WDBMDPipeUtils::PrevPipeComponent(pBranchNode, pCurrentNode);
            }
            break;
        case WD::PipeFlowDir::PFD_Backward:
            {
                // 找当前TUBI节点的后1个WDBMDPipeCOMS类型节点或WDBMDPipeBRAN类型节点
                pPipeComNode = WD::WDBMDPipeUtils::NextPipeComponent(pBranchNode, pCurrentNode);
            }
            break;
        default:
            break;
        }

        // 按前后顺序获取到前一个或后一个管件节点，则返回获取的管件节点
        if (pPipeComNode != nullptr)
        {
            return pPipeComNode;
        }
        // 当前TUBI节点为分支下的第一个节点或最后一个节点，返回分支节点
        else
        {
            return pBranchNode;
        }
    }
    return nullptr;
}
void UiComNodePipeComponent::getCOCOBoreACon(std::string& bore, std::string& connectType, bool isReselect)
{
    // 获取用于coco校验的节点的管径和连接方式
    switch(this->getInsertionSequence())
    {
    case WD::PipeFlowDir::PFD_Forward:
        {
            if(isReselect)
            {
                auto pPrevNode = getForwardPipeNode();
                bore = forwardBore(pPrevNode);
                connectType = forwardConnect(pPrevNode);
            }
            else
            {
                // 获取节点树当前选中的非TUBI节点（可能是分支或管件节点）
                auto pCurPipeNode = getCurrentPipeBusinessNode();
                bore = forwardBore(pCurPipeNode);
                connectType = forwardConnect(pCurPipeNode);
            }
        }
        break;
    case WD::PipeFlowDir::PFD_Backward:
        {
            if (isReselect)
            {
                auto pNextNode = getBackwardPipeNode();
                bore = backwardBore(pNextNode);
                connectType = backwardConnect(pNextNode);
            }
            else
            {
                // 获取节点树当前选中的非TUBI节点（可能是分支或管件节点）
                auto pCurPipeNode = getCurrentPipeBusinessNode();
                bore = backwardBore(pCurPipeNode);
                connectType = backwardConnect(pCurPipeNode);
            }
        }
        break;
    default:
        break;
    }
}

void    UiComNodePipeComponent::forwardRotateByAngle(WD::WDNode::SharedPtr pCom, float angle)
{
    // 设置选中SPEC
    if (pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCom))
        return;
    auto        pPtArrive   =   pCom->keyPoint(pCom->getAttribute("Arrive").toInt());
    if (pPtArrive == nullptr)
        return;
    WD::DVec3   gDirArrive  =   WD::DVec3::Normalize(pPtArrive->transformedDirection(pCom->globalRSTransform()));
    pCom->rotate(gDirArrive, angle);
    pCom->update();
    //更新分支的连接
    if (pCom->parent() != nullptr)
        pCom->parent()->triggerUpdate(true);
}
void                                    UiComNodePipeComponent::backwardRotateByAngle(
    WD::WDNode::SharedPtr pCom, float angle)
{
    // 取出当前节点的管件数据
    if (pCom == nullptr || !WD::WDBMDPipeUtils::IsPipeComponent(*pCom))
        return;
    auto        pPtLeave    =   pCom->keyPoint(pCom->getAttribute("Leave").toInt());
    if (pPtLeave == nullptr)
        return;
    WD::DVec3   gDirLeave   =   WD::DVec3::Normalize(pPtLeave->transformedDirection(pCom->globalRSTransform()));
    pCom->rotate(gDirLeave, angle);
    pCom->update();
    //更新分支的连接
    if (pCom->parent() != nullptr)
        pCom->parent()->triggerUpdate(true);
}
bool UiComNodePipeComponent::checkSpecChanged()
{
    auto pPreNode = getCurrentPipeBusinessNode();
    if (pPreNode == nullptr)
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Current Node Is Not Pipe SPEC");
        return false;
    }

    auto pPreNodeSpec = GetPipeSpec(*pPreNode);
    auto pSelectSpec = _pParamsWidget->getSPEC();
    if (pPreNodeSpec != pSelectSpec)
    {
        std::string quesInfo = WD::WDTs("ErrorUiComNodePipeComponent", "Changed SPEC");
        if (pSelectSpec != nullptr)
        {
            quesInfo.append(pSelectSpec->name());
            quesInfo.append(" ?");
            // 用户选择的非确认按钮
            if (0 != WD_QUESTION(quesInfo))
            {
                return false;
            }
        }
        else
        {
            WD_WARN_T("ErrorUiComNodePipeComponent", "CreateNodeFailed, Please select SPEC!");
            return false;
        }
    }
    return true;
}

bool UiComNodePipeComponent::getPipeComDescShowByConfig()
{
    // 管件创建相关配置项
    auto pCfgPipeBran = _core.cfg().query("pipeBranch");
    if (pCfgPipeBran == nullptr)
        return false;
    auto pCfgShowComponentDesc = pCfgPipeBran->query("component.description.visible");
    if (pCfgShowComponentDesc == nullptr)
        return false;
    auto pValue = pCfgShowComponentDesc->value<bool>();
    if (pValue == nullptr)
        return false;
    return *pValue;
}
bool UiComNodePipeComponent::getCocoCheckEnabledByConfig()
{
    auto pCfgPipeBran = _core.cfg().query("pipeBranch");
    if (pCfgPipeBran == nullptr)
        return false;
    auto pCfgEnableCOCOCheck = pCfgPipeBran->query("coco.check.enable");
    if (pCfgEnableCOCOCheck == nullptr)
        return false;
    auto pValue = pCfgEnableCOCOCheck->value<bool>();
    if (pValue == nullptr)
        return false;
    return *pValue;
}

void UiComNodePipeComponent::initBranConnect()
{
    // 初始化连接策略
    _mapStrategy[NOZZ] = WD::PipeBranchConnectStrategyNozzle::MakeShared();
    _mapStrategy[TEE] = WD::PipeBranchConnectStrategyTee::MakeShared();
    _mapStrategy[OLET] = WD::PipeBranchConnectStrategyOlet::MakeShared();
    _mapStrategy[ELBO] = WD::PipeBranchConnectStrategyElbow::MakeShared();
    _mapStrategy[REDU] = WD::PipeBranchConnectStrategyReducer::MakeShared();
    _mapStrategy[FLAN] = WD::PipeBranchConnectStrategyFlange::MakeShared();
    _mapStrategy[Multiway] = WD::PipeBranchConnectStrategyMultiway::MakeShared();
    _mapStrategy[BranchHead] = WD::PipeBranchConnectStrategyBranchHead::MakeShared();
    _mapStrategy[BranchTail] = WD::PipeBranchConnectStrategyBranchTail::MakeShared();
    _mapStrategy[FirstMember] = WD::PipeBranchConnectStrategyFirstMember::MakeShared();
    _mapStrategy[LastMember] = WD::PipeBranchConnectStrategyLastMember::MakeShared();
    _mapStrategy[Name] = WD::PipeBranchConnectStrategyName::MakeShared();
    _mapStrategy[Position] = WD::PipeBranchConnectStrategyPosition::MakeShared();

    QVariant userData;
    // 初始化头尾下拉列表
    ui.comboBoxHeadTail->addItem(QString::fromUtf8(Head));
    userData.setValue<QString>(QString(Head));
    ui.comboBoxHeadTail->setItemData(0, userData, Qt::UserRole);
    ui.comboBoxHeadTail->addItem(QString::fromUtf8(Tail));
    userData.setValue<QString>(QString(Tail));
    ui.comboBoxHeadTail->setItemData(1, userData, Qt::UserRole);
    // 初始化连接对象类型
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(NOZZ));
    userData.setValue<QString>(QString(NOZZ));
    ui.comboBoxConnectObjType->setItemData(0, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(TEE));
    userData.setValue<QString>(QString(TEE));
    ui.comboBoxConnectObjType->setItemData(1, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(OLET));
    userData.setValue<QString>(QString(OLET));
    ui.comboBoxConnectObjType->setItemData(2, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(ELBO));
    userData.setValue<QString>(QString(ELBO));
    ui.comboBoxConnectObjType->setItemData(3, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(REDU));
    userData.setValue<QString>(QString(REDU));
    ui.comboBoxConnectObjType->setItemData(4, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(FLAN));
    userData.setValue<QString>(QString(FLAN));
    ui.comboBoxConnectObjType->setItemData(5, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(Multiway));
    userData.setValue<QString>(QString(Multiway));
    ui.comboBoxConnectObjType->setItemData(6, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(BranchHead));
    userData.setValue<QString>(QString(BranchHead));
    ui.comboBoxConnectObjType->setItemData(7, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(BranchTail));
    userData.setValue<QString>(QString(BranchTail));
    ui.comboBoxConnectObjType->setItemData(8, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(FirstMember));
    userData.setValue<QString>(QString(FirstMember));
    ui.comboBoxConnectObjType->setItemData(9, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(LastMember));
    userData.setValue<QString>(QString(LastMember));
    ui.comboBoxConnectObjType->setItemData(10, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(Name));
    userData.setValue<QString>(QString(Name));
    ui.comboBoxConnectObjType->setItemData(11, userData, Qt::UserRole);
    ui.comboBoxConnectObjType->addItem(QString::fromUtf8(Position));
    userData.setValue<QString>(QString(Position));
    ui.comboBoxConnectObjType->setItemData(12, userData, Qt::UserRole);

}
void UiComNodePipeComponent::branchConnect(WD::WDNode::SharedPtr connectObj)
{
    WD::WDNode::SharedPtr pBran = _pBranch.lock();
    if (pBran == nullptr)
        return;

    // 校验流向
    if ((_connectHeadTailType == WD::PipeBranchConnectType::Connect_Head && _connectObjType == BranchHead)
        || (_connectHeadTailType == WD::PipeBranchConnectType::Connect_Tail && _connectObjType == BranchTail))
    {
        WD_WARN_T("ErrorUiComNodePipeComponent", "Flow is not support!");
        return;
    }

    if (connectObj != nullptr)
    {
        // 判断当前连接对象类型与选中节点类型是否匹配
        if (!this->matchConnectObjType(*connectObj))
        {
            WD_WARN_T("ErrorUiComNodePipeComponent", "PickNodeNotMatchConnectType");
            return;
        }
    }

    // 记录pBran和connectObj修改前 坐标、朝向、管径、连接类型、原连接的节点，原连接节点的引用节点
    WD::DVec3 branOldPos, connectObjOldPos, branOldDir, connectObjOldDir;
    std::string branOldBore, connectObjOldBore, branOldCType, connectObjOldCType;
    WD::WDNode::SharedPtr pBranOldCNode = nullptr, pConnectObjOldCNode = nullptr;
    switch (_connectHeadTailType)
    {
    case WD::Connect_Head:
        {
            // 获取pBran修改连接之前头信息
            branOldPos = GetBranHPos(*pBran, WD::WDWRTType::WRT_World);
            branOldDir = GetBranHDir(*pBran, WD::WDWRTType::WRT_World);
            branOldBore = GetBranHBore(*pBran);
            branOldCType = GetHConnType(*pBran);
            pBranOldCNode = GetHRefNode(*pBran);

            // 获取connectObj修改连接之前的尾信息
            if(connectObj != nullptr)
            {
                connectObjOldPos = GetTPos(*connectObj, WD::WDWRTType::WRT_World);
                connectObjOldDir = GetTDir(*connectObj, WD::WDWRTType::WRT_World);
                connectObjOldBore = GetTBore(*connectObj);
                connectObjOldCType = GetTConnType(*connectObj);
                pConnectObjOldCNode = GetTRefNode(*connectObj);
            }
        }
        break;
    case WD::Connect_Tail:
        {
            // 获取pBran修改连接之前尾信息
            branOldPos = GetBranTPos(*pBran, WD::WDWRTType::WRT_World);
            branOldDir = GetBranTDir(*pBran, WD::WDWRTType::WRT_World);
            branOldBore = GetBranTBore(*pBran);
            branOldCType = GetTConnType(*pBran);
            pBranOldCNode = GetTRefNode(*pBran);
            
            // 获取connectObj修改连接之前的头信息
            if (connectObj != nullptr)
            {
                connectObjOldPos = GetHPos(*connectObj, WD::WDWRTType::WRT_World);
                connectObjOldDir = GetHDir(*connectObj, WD::WDWRTType::WRT_World);
                connectObjOldBore = GetHBore(*connectObj);
                connectObjOldCType = GetHConnType(*connectObj);
                pConnectObjOldCNode = GetHRefNode(*connectObj);
            }
        }
        break;
    default:
        break;
    }

    auto funcUndo = [this
    , branOldPos, branOldDir, branOldBore, branOldCType, pBranOldCNode, pBran, connectObj
    , connectObjOldPos, connectObjOldDir, connectObjOldBore, connectObjOldCType, pConnectObjOldCNode]()
    {
        switch (_connectHeadTailType)
        {
        case WD::Connect_Head:
            {
                // 还原pBran头信息
                DisconnectHRef(*pBran);
                SetBranHPos(*pBran, branOldPos, WD::WDWRTType::WRT_World);
                SetBranHDir(*pBran, branOldDir, WD::WDWRTType::WRT_World);
                pBran->setAttribute("Hbore", branOldBore);
                SetHConnType(*pBran, branOldCType);
                SetHRefNode(*pBran, pBranOldCNode);
                if(pBranOldCNode != nullptr)
                {
                    SetTRefNode(*pBranOldCNode, pBran);
                    pBranOldCNode->triggerUpdate(true);
                }

                // 还原connectObj尾信息
                if(connectObj != nullptr)
                {
                    SetBranTPos(*connectObj, connectObjOldPos, WD::WDWRTType::WRT_World);
                    SetBranTDir(*connectObj, connectObjOldDir, WD::WDWRTType::WRT_World);
                    SetBranTBore(*connectObj, connectObjOldBore);
                    SetTConnType(*connectObj, connectObjOldCType);
                    SetTRefNode(*connectObj, pConnectObjOldCNode);
                    if (pConnectObjOldCNode != nullptr)
                    {
                        SetHRefNode(*pConnectObjOldCNode, connectObj);
                        pConnectObjOldCNode->triggerUpdate(true);
                    }
                }
            }
            break;
        case WD::Connect_Tail:
            {
                // 还原pBran尾信息
                DisconnectTRef(*pBran);
                SetBranTPos(*pBran, branOldPos, WD::WDWRTType::WRT_World);
                SetBranTDir(*pBran, branOldDir, WD::WDWRTType::WRT_World);
                SetBranTBore(*pBran, branOldBore);
                SetTConnType(*pBran, branOldCType);
                SetTRefNode(*pBran, pBranOldCNode);
                if(pBranOldCNode != nullptr)
                {
                    SetHRefNode(*pBranOldCNode, pBran);
                    pBranOldCNode->triggerUpdate(true);
                }

                // 还原connectObj头信息
                if (connectObj != nullptr)
                {
                    SetBranHPos(*connectObj, connectObjOldPos, WD::WDWRTType::WRT_World);
                    SetBranHDir(*connectObj, connectObjOldDir, WD::WDWRTType::WRT_World);
                    connectObj->setAttribute("Hbore", connectObjOldBore);
                    SetHConnType(*connectObj, connectObjOldCType);
                    SetHRefNode(*connectObj, pConnectObjOldCNode);
                    if (pConnectObjOldCNode != nullptr)
                    {
                        SetTRefNode(*pConnectObjOldCNode, connectObj);
                        pConnectObjOldCNode->triggerUpdate(true);
                    }
                }
            }
            break;
        default:
            break;
        }
        pBran->triggerUpdate(true);
        if(connectObj != nullptr)
        {
            connectObj->triggerUpdate(true);
        }
        _core.needRepaint();
    };

    auto funcRedo = [this, pBran, connectObj]()
    {
        // 根据连接对象类型采用不同连接策略
        MapStrategy::iterator curStrategy = _mapStrategy.find(_connectObjType);
        if (curStrategy == _mapStrategy.end())
            return;

        bool res;
        if (connectObj == nullptr)
        {
            res = curStrategy->second->connect(_connectHeadTailType, *pBran, *pBran);
        }
        else
        {
            res = curStrategy->second->connect(_connectHeadTailType, *pBran, *connectObj);
            connectObj->triggerUpdate(true);
        }
        pBran->triggerUpdate(true);
        // 处理报错信息
        if (!res)
        {
            std::string error = curStrategy->second->takeError();
            if (!error.empty())
            {
                WD_WARN_T("ErrorUiComNodePipeComponent", error);
            }
        }
        _core.needRepaint();
    };

    // 添加uodo/redo
    auto bm = _core.currentBM();
    if (bm != nullptr)
    {
        auto cmd = new WD::WDUndoCommand("");
        if(cmd != nullptr)
        {
            // 设置撤销
            cmd->setNoticeAfterUndo([funcUndo](const WD::WDUndoCommand&)
            {
                // 设置连接的类型
                funcUndo();
            });
            // 设置重做
            cmd->setNoticeAfterRedo([connectObj, funcRedo](const WD::WDUndoCommand&)
            {
                funcRedo();
            });

            _core.undoStack().push(cmd);
        }
    }
}
void UiComNodePipeComponent::branchPosConnect()
{
    auto pBran = _pBranch.lock();
    if (pBran == nullptr)
        return;

    // 获取目标数据
    auto gPos = _pPositionTypeDialog->position();
    auto gDir = _pPositionTypeDialog->direction();
    auto gBore = _pPositionTypeDialog->bore();
    auto gCType = _pPositionTypeDialog->cType();

    // 添加uodo/redo
    switch (this->_connectHeadTailType)
    {
    case WD::Connect_Head:
        {
            // 断开pBran头的连接
            auto pHrefCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Href", WD::WDBMAttrValue(WD::WDBMNodeRef(nullptr)));
            pHrefCmd->setNoticeAfterRedo([this, pBran](const WD::WDUndoCommand&)
            {
                pBran->triggerUpdate(true);
            _core.needRepaint();
            });
            pHrefCmd->setNoticeAfterUndo([this, pBran](const WD::WDUndoCommand&)
            {
                pBran->triggerUpdate(true);
            _core.needRepaint();
            });
            // 设置分支头的位置
            auto pHposCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hposition WRT World", WD::WDBMAttrValue(gPos));
            // 设置分支头的朝向
            auto pHDirCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hdirection WRT World", WD::WDBMAttrValue(gDir));
            // 设置分支头的连接类型
            auto pHCTypeCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hconnect", WD::WDBMAttrValue(gCType));
            // 设置分支头的管径
            auto pHBoreCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hbore", WD::WDBMAttrValue(gBore));
            _core.undoStack().beginMarco("branHeadCon2Pos");
            _core.undoStack().push(pHrefCmd);
            _core.undoStack().push(pHposCmd);
            _core.undoStack().push(pHDirCmd);
            _core.undoStack().push(pHCTypeCmd);
            _core.undoStack().push(pHBoreCmd);
            _core.undoStack().endMarco();
        }
        break;
    case WD::Connect_Tail:
        {
            // 断开pBran尾的连接
            auto pTrefCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tref", WD::WDBMAttrValue(WD::WDBMNodeRef(nullptr)));
            pTrefCmd->setNoticeAfterRedo([this, pBran](const WD::WDUndoCommand&)
            {
                pBran->triggerUpdate(true);
                _core.needRepaint();
            });
            pTrefCmd->setNoticeAfterUndo([this, pBran](const WD::WDUndoCommand&)
            {
                pBran->triggerUpdate(true);
                _core.needRepaint();
            });
            // 设置分支尾的位置
            auto pTposCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tposition WRT World", WD::WDBMAttrValue(gPos));
            // 设置分支尾的朝向
            auto pTDirCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tdirection WRT World", WD::WDBMAttrValue(gDir));
            // 设置分支尾的连接类型
            auto pTCTypeCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tconnect", WD::WDBMAttrValue(gCType));
            // 设置分支尾的管径
            auto pTBoreCmd = WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tbore", WD::WDBMAttrValue(gBore));
            pTBoreCmd->setNoticeAfterRedo([this, pBran](const WD::WDUndoCommand&)
            {
                pBran->triggerUpdate(true);
                _core.needRepaint();
            });
            pTBoreCmd->setNoticeAfterUndo([this, pBran](const WD::WDUndoCommand&)
            {
                pBran->triggerUpdate(true);
                _core.needRepaint();
            });

            _core.undoStack().beginMarco("branTailCon2Pos");
            _core.undoStack().push(pTrefCmd);
            _core.undoStack().push(pTposCmd);
            _core.undoStack().push(pTDirCmd);
            _core.undoStack().push(pTCTypeCmd);
            _core.undoStack().push(pTBoreCmd);
            _core.undoStack().endMarco();
        }
        break;
    }
}
bool UiComNodePipeComponent::matchConnectObjType(WD::WDNode& node)
{    
    // 连接节点类型
    if (_connectObjType == BranchHead || _connectObjType == BranchTail)
    {
        return onBranch(_core, node);
    }
    else
    {
        // 当前选择 连接对象类型
        if (node.isType(_connectObjType))
            return true;
    }
    return false;
}
void UiComNodePipeComponent::onCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
    , WD::WDNode::SharedPtr pPrevNode
    , WD::WDNodeTree& sender)
{
    WDUnused(pPrevNode);
    WDUnused(sender);

    if(pCurrNode == nullptr)
    {
        _pBranch.reset();
        return;
    }

    if (pCurrNode->isType("BRAN"))
    {
        _pBranch = pCurrNode;
        return;
    }
    else
    {
        auto pParent = _core.getBMDesign().findParentWithType(*pCurrNode, "TUBI");
        if (pParent == nullptr)
        {
            _pBranch.reset();
            return;
        }
        _pBranch = pParent;
    }
}
void UiComNodePipeComponent::retranslateUi()
{
    // 专业等级子界面翻译
    _pParamsWidget->setGroupName(WD::WDTs("UiComCreatePipeline", "Grade"));
    _pParamsWidget->setPipeText(WD::WDTs("UiComCreatePipeline", "Pipe"));
    _pParamsWidget->setHeatLabel(WD::WDTs("UiComCreatePipeline", "HeatPreser"));
    _pParamsWidget->setTrackLabel(WD::WDTs("UiComCreatePipeline", "Tracking"));
    _pParamsWidget->setCEText(WD::WDTs("UiComCreatePipeline", "CE"));
    

    // 定位
    ui.throComboBox->clear();
    for (size_t i = 0; i < _thros.size(); ++i)
    {
        ui.throComboBox->addItem(QString::fromUtf8(_thros[i]->name().c_str()));
    }

    // 距离
    ui.comboBoxDistanceType->clear();
    ui.comboBoxDistanceType->addItem("Spool");
    ui.comboBoxDistanceType->addItem("Distance");

    Trs("UiComNodePipeComponent"
        , static_cast<QDialog*>(this)
        , ui.groupBoxSequence
        , ui.forwardRadioButton
        , ui.backwordRadioButton
        , ui.autoConnectCheckBox
        , ui.pushButtonCreate
        , ui.pushButtonTopBottomFlatFlip
        , ui.pushButtonPipeBottomElevation
        , ui.pushButtonCopy
        , ui.pushButtonReselect
        , ui.flipPushButton
        , ui.pushButtonElbowSlope
        , ui.pushButtonEnding
        , ui.labelExitOrientation
        , ui.labelRotation
        , ui.labelLocation
        , ui.pushBtnKits
        , ui.pushBtnConnect
        , ui.pushBtnSimilar
        , ui.labelMove
        , ui.throComboBox
        , ui.comboBoxDistanceType
        , ui.pushButtonFor
        , ui.pushButtonBack
        , ui.groupBoxBRAN
        , ui.pushBtnCreateBRAN
        , ui.comboBoxHeadTail
        , ui.comboBoxConnectObjType
        );
}