#pragma once

#include    <QDialog>
#include    "ui_UiComNodePipeComponent.h"
#include    <QKeyEvent>
#include    "UiComCommonParamsWidget.h"
#include    "UiComNodeBranchEnding.h"
#include    "SelectPipeDialog.h"
#include    "PipeComponentThro.h"
#include    "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include    <QComboBox>
#include    "core/common/WDConfig.h"
#include    "../../wizDesignerApp/UiInterface/IMainWindow.h"
#include    "PipeNameTypeDialog.h"
#include    "PipePositionTypeDialog.h"
#include    "PipeBranchConnectStrategySub.h"

// 监听回车按下的QLineEdit
class EnterComboBox : public QComboBox
{  
    Q_OBJECT

signals:
    /**
     * @brief 回车按下信号
    */
    void sigEnterPressed(const QString& text);

public:
    EnterComboBox(QWidget *parent = nullptr);

protected:
    virtual void keyPressEvent(QKeyEvent* evt) override;
};

class UiComNodePipeComponent : public QDialog
    , public WD::WDCapturePositioningMonitor
{
    Q_OBJECT

public:
    UiComNodePipeComponent(IMainWindow& mainWindow, QWidget *parent = Q_NULLPTR);
    ~UiComNodePipeComponent();

public:
    using MapStrategy = std::map<std::string, WD::PipeBranchConnectStrategy::SharedPtr>;

    /**
    * @brief 距离类型
    */
    enum DistanceType 
    {
        Spool = 0,
        Distance
    };
    Q_ENUM(DistanceType)
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

private:
    virtual void    onResult(const WD::WDCapturePositioningResult& result
        , bool& existFlag
        , const WD::WDCapturePositioning& sender) override;
    virtual void    onDeactived(const WD::WDCapturePositioning& sender) override;
private slots:
    /*
    * @brief 创建按钮 通知响应
    */
    void slotCreatePushButton();
    /**
    * @brief 偏心大小头顶底平翻转
    */
    void slotTopBottomFlatFlipPushButtonClicked();
    /**
     * @brief 管底标高
    */
    void slotPipeBottomElevationPushButtonClicked();
    /**
     * @brief 创建阀门套件
    */
    void slotCreateVALVKits();
    /**
     * @brief 直接连接按钮被点击槽函数
    */
    void slotBtnConnectClicked();
    /*
    * @brief 复制按钮 通知响应
    */
    void slotBtnCopyClicked(bool b);
    /**
     * @brief 相似按钮被点击槽函数
    */
    void slotBtnSimilarClicked(bool);
    /*
    * @brief 重选按钮 通知响应
    */
    void slotBtnReselectClicked(bool b);
    /*
    * @brief 翻转按钮 通知响应
    */
    void slotFlipPushButton();
    /*
    * @brief 出口朝向 切换 通知响应
    */
    void slotLeaveDirectActivated(int index);
    /*
    * @brief 旋转角度 切换 通知响应
    */
    void slotRotationAngleChanged(int index);
    /**
     * @brief 端面距离/中心距离 的索引改变
     * @param index 
    */
    void slotDiatanceIndexChanged(int index);
    /*
    * @brief 旋转角度 回车按下 通知响应
    */
    void slotAngleEnterPressed(const QString& text);
    /**
    * @brief 应用按钮 通知响应
    */
    void slotComboBoxDistanceEnterPressed(const QString& text);
    /**
    * @brief 弯头放坡按钮 通知响应
    */
    void slotPushButtonElbowSlopeClicked();
    /**
    * @brief 收管按钮 通知响应
    */
    void slotPushButtonEndingClicked();
    /**
    * @brief 等级子窗口当前等级项改变槽函数，更新类型列表
    */
    void slotSpecNodeChanged();
    /**
    * @brief 管件类型 切换 通知响应
    */
    void slotListWidgetTypeCurrentItemChanged(QListWidgetItem* current, QListWidgetItem* previous);
    /*
    *   @brief 响应Thro 变化
    */
    void slotThroActivated(int index);
    /**
     * @brief 向前移动按钮槽函数
     * @param  
    */
    void slotForBtnMoveClicked(bool);
    /**
     * @brief 向后移动按钮槽函数
     * @param  
    */
    void slotBackBtnMoveClicked(bool);
    /**
     * @brief 创建分支按钮点击槽函数
    */
    void slotBtnCreateBRANClicked();
    /**
    * @brief 连接对象类型下拉选中通知响应
    */
    void slotHeadTailChanged(int index);
    /**
    * @brief 连接对象类型下拉选中通知响应
    */
    void slotConnectObjTypeChanged(int index);
    /**
    * @brief 位置窗口OK按下通知响应
    */
    void onPositionOKClicked();

private:
    /**
     * @brief 将管件pCom旋转angle度
     * @param pCom 管件节点
     * @param angle 角度
    */
    void rotationPipeCom(WD::WDNode::SharedPtr pCom, double angle);
    /**
    * @brief 根据模型树当前选中节点获取管件可挂载的父节点
    */
    WD::WDNode::SharedPtr   getParentNode(const std::string& type) const;
    /**
    * @brief 校验父节点下是否存在同名子节点
    */
    bool                    namedChildExist(WD::WDNode& parentNode, const std::string& name);
    bool                    restoreConnection(WD::WDNode::SharedPtr pPipeComponentNode
    , bool autoConnection
    , double spoolDistance = 0.0f);
    /**
    * @brief 获取分支头部段最后一个管件节点，若没有则返回nullptr
    * @param branch 分支节点对象
    */
    WD::WDNode::SharedPtr   getLastComFromHeadPart(WD::WDNode::SharedPtr pBranch);
    /**
    * @brief 获取分支尾部段第一个管件节点，若没有则返回nullptr
    * @param branch 分支节点对象
    */
    WD::WDNode::SharedPtr   getFirstComFromTailPart(WD::WDNode::SharedPtr pBranch);

    /**
     * @brief 根据类型创建管件节点，将pSpco设置为管件节点的引用等级元件（内部不做COCO校验）
    */
    WD::WDNode::SharedPtr createPipe(WD::WDNode::SharedPtr pBran, const std::string& type, WD::WDNode::SharedPtr pSpco, WD::WDNode& cur);
    /**
    * @brief 获取连接管径
    */
    std::string             connectBore();
    /**
     * @brief 根据当前模型树选中节点获取前一个管件节点
     *   规则: 
     *      如果当前选中的是管件节点，直接返回
     *      如果当前选中的是分支节点，则返回最后一个管件节点，如果分支没有管件，则返回分支节点自己
     *      如果当前选中的是直管节点，则返回直管的前一个管件节点
     *      否则返回nullptr
     * @return 成功返回管件节点，失败返回nullptr
    */
    WD::WDNode::SharedPtr   getPrevPipeComponentNode() const;
    /**
    * @brief 设置选中SPEC
    */
    void                    setSelectSPEC();
    /**
    * @brief 获取插入顺序
    */
    WD::PipeFlowDir         getInsertionSequence() const;
    /**
    * @brief 获取距离类型
    */
    DistanceType            getDistanceType() const;
private:
    /**
    * @brief 向前距离定位管件
    * @param pBranch 分支节点
    * @param pCom 管件节点
    */
    void                    forwardOrientation(WD::WDNode::SharedPtr pBranch, WD::WDNode::SharedPtr pCom);
    /**
    * @brief 向后距离定位管件
    * @param pBranch 分支节点
    * @param pCom 管件节点
    */
    void                    backwardOrientation(WD::WDNode::SharedPtr pBranch, WD::WDNode::SharedPtr pCom);
    /**
    * @brief 管件向前朝向旋转
    * @param pCom 管件节点
    * @param direction 朝向
    */
    void                    forwardRotateByDirection(WD::WDNode::SharedPtr pCom, const WD::DVec3& direction);
    /**
    * @brief 管件向后朝向旋转
    * @param pCom 管件节点
    * @param direction 朝向
    */
    void                    backwardRotateByDirection(WD::WDNode::SharedPtr pCom, const WD::DVec3& direction);

    /**
    * @brief 向前重新连接管件
    * @param pCom 管件节点
    */
    bool                    forwardRestoreConnect(WD::WDNode::SharedPtr pCom
    , bool autoConnection
    , double spoolDistance);
    /**
    * @brief 向后重新连接管件
    * @param pCom 管件节点
    */
    bool                    backwardRestoreConnect(WD::WDNode::SharedPtr pCom
    , bool autoConnection
    , double spoolDistance);
    /**
    * @brief 获取pNode向前进行COCO校验的管径
    * @param pNode
    * @return
    */
    std::string forwardBore(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 获取pNode向后进行COCO校验的管径
    * @param pNode
    * @return
    */    
    std::string backwardBore(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 获取pNode向前进行COCO校验的连接类型
    * @param pNode 
    * @return 
    */
    std::string forwardConnect(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 获取pNode向后进行COCO校验的连接类型
    * @param pNode 
    * @return 
    */
    std::string backwardConnect(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 获取节点树当前节点的前一个节点
    * 如果当前节点是管道元件：返回前一个节点。如果当前节点为分支下的第一个节点，则返回分支节点
    * 如果当前节点是分支节点：返回当前分支节点
    * 如果当前节点不是分支及以下节点，返回nullptr
    * @return 
    */
    WD::WDNode::SharedPtr getForwardPipeNode();
    /**
    * @brief 获取节点树当前节点的后一个节点
    * 如果当前节点是管道元件：返回后一个节点。如果当前节点为分支下的第一个节点，则返回分支节点
    * 如果当前节点是分支节点：返回当前分支节点
    * 如果当前节点不是分支及以下节点，返回nullptr
    * @return
    */    
    WD::WDNode::SharedPtr getBackwardPipeNode();
    /**
    * @brief 根据界面的前后关系，获取当前节点的前一个或后一个节点
    * @return 
    */
    WD::WDNode::SharedPtr getCOCOPipeNode();
    /**
     * @brief 获取当前管道业务节点
     * 注：1、当前节点如果是管件或分支节点则返回节点树当前节点
     *    2、如果当前节点是TUBI节点
     *          前向：获取TUBI的前1个管件节点，如果TUBI为BRAN下第一个节点，则返回父BRAN节点
     *          后向：获取TUBI的后1个管件节点，如果TUBI为BRAN下最后一个节点，则返回父BRAN节点
     *    3、其他情况返回空
    */
    WD::WDNode::SharedPtr getCurrentPipeBusinessNode();
    /**
    * @brief 根据界面选择的前后，获取coco校验的管径和连接类型
    * @param bore coco校验的管径
    * @param connectType coco校验的类型
    * @param isReselect 是否是重选
    * 注：以下说的管件节点为WDBMDPipeCOMS类型，非TUBI类型
    * true:重选
    ：     1）前向
                1、前1个节点为管件：获取前1个节点的出口点管径和连接方式
                2、当前节点为分支下的第1个节点，前1个节点为分支节点。获取分支头的管径和连接方式
           2）后向
                1、后1个节点为管件：获取后1个节点的入口点管径和连接方式
                2、当前节点为分支下的最后1个节点，后1个节点为分支节点。获取分支的尾管径和连接方式
    false: 新建
            1）前向
                1、当前节点为管件节点。获取当前节点的出口点管径和连接方式
                2、当前节点为分支节点。获取分支头的管径和连接方式
            2）后向
                1、当前节点为管件节点。获取当前节点的入口点管径和连接方式
                2、当前节点为分支节点。获取分支尾的管径和连接方式
    */
    void getCOCOBoreACon(std::string& bore, std::string& connectType, bool isReselect );
    /**
    * @brief 向前旋转管件指定角度
    * @param pCom 管件节点
    * @param angle 旋转角度
    */
    void                    forwardRotateByAngle(WD::WDNode::SharedPtr pCom, float angle);
    /**
    * @brief 向后前旋转管件指定角度
    * @param pCom 管件节点
    * @param angle 旋转角度
    */
    void                    backwardRotateByAngle(WD::WDNode::SharedPtr pCom, float angle);
    /**
    * @brief 校验等级是否发生改变
    * 创建管件界面选择的spec节点是否与前一个管件或分支的spec相等。
    * 相等则继续创建，返回true；
    * 不相等则询问，点击确认则创建返回true;点击取消或关闭则不创建,返回false
    * @return true:创建 false:不创建
    */
    bool checkSpecChanged();
    /**
     * @brief 从配置中查询是否需要显示管件描述
    */
    bool getPipeComDescShowByConfig();
    /**
     * @brief 从配置中查询是否需要进行COCO校验
    */
    bool getCocoCheckEnabledByConfig();
    /**
    * @brief 初始化分支连接
    */
    void initBranConnect();
    /**
    * @brief 分支连接
    * @param connectObj 连接对象
    */
    void branchConnect(WD::WDNode::SharedPtr connectObj = nullptr);
    /**
    * @brief 分支连接
    * @param position 目标位置
    */
    void branchPosConnect();
    /**
    * @brief 判断当前连接对象类型与选中节点类型是否匹配
    */
    bool matchConnectObjType(WD::WDNode& node);
    /**
     * @brief 节点树当前节点改变通知
     * @param currNode
     * @param prevNode
     * @param sender
    */
    void onCurrentNodeChanged(WD::WDNode::SharedPtr pCurrNode
        , WD::WDNode::SharedPtr pPrevNode
        , WD::WDNodeTree& sender);
private:
    /**
    * @brief 界面翻译
    */
    void retranslateUi();
    enum CaptureFunc
    {
        // 自动弯头
        CF_AutoElbo = 0,
        // 相似
        CF_Similar,
        // 分支连接
        CF_BranConnect,
        // 管底标高
        CF_PipeBottomElevation,
    };
private:
    Ui::UiComNodePipeComponent          ui;
    // 主窗口
    IMainWindow&                        _mainWindow;
    // core对象
    WD::WDCore&                         _core;
    // 选择管件对话框                  
    SelectPipeDialog*                   _pSelectPipeDialog;
    // 等级子窗口
    UiComCommonParamsWidget*            _pParamsWidget;
    UiComNodeBranchEnding*              _branchEndingWidget;
    // 旋转角度下拉框控件                
    EnterComboBox*                      _pComboBoxRotAngel;
    // 距离端面/中心距离下拉框控件                
    EnterComboBox*                      _pComboBoxDistance;
    // 类型SELE节点（SPEC的子节点）       
    WD::WDNode::WeakPtr                 _pSELE;
    // 对齐算法列表                       
    std::vector<WD::ThroBase*>          _thros;
    // 分支斜接管件                       
    std::vector<WD::WDNode::WeakPtr>    _slopeComs;
    // 捕捉的用途
    CaptureFunc                         _captureFunc;
    // 用于记录点击相似按钮之前的节点（由于点击相似后暂时无法实现只有三维场景可操控的效果）
    WD::WDNode::WeakPtr                 _pSimilarCurr;
    // 记录当前节点
    WD::WDNode::WeakPtr                 _pCurrentNode;
    // 名称类型窗口
    PipeNameTypeDialog*                 _pNameTypeDialog;
    // 位置类型窗口
    PipePositionTypeDialog*             _pPositionTypeDialog;
    // 当前管线分支
    WD::WDNode::WeakPtr                 _pBranch;
    // 连接方式
    WD::PipeBranchConnectType           _connectHeadTailType;
    // 连接对象类型
    std::string                         _connectObjType;
    // 策略map
    MapStrategy                         _mapStrategy;
};
