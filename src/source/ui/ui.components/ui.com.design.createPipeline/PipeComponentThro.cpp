#include "PipeComponentThro.h"
#include "core/viewer/WDViewer.h"
#include "core/math/DirectionParser.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "PipeCommon.h"
#include "core/businessModule/WDBMBase.h"

WD_NAMESPACE_BEGIN


void MakeMoveCmd(WDCore& core, const WD::WDNode::Nodes& nodes, const DVec3& offset) 
{
    auto pCmd = WD::WDBMBase::MakeMoveCommand(nodes, offset);
    if (pCmd == nullptr)
        return;
    pCmd->setNoticeAfterRedo([&core](const WD::WDUndoCommand&)
        {
            core.needRepaint();
        });
    pCmd->setNoticeAfterUndo([&core](const WD::WDUndoCommand&)
        {
            core.needRepaint();
        });
    core.undoStack().push(pCmd);
}

WDNode::SharedPtr ThroBase::branchNode(WDNode::SharedPtr pPipeComNode) const
{
    if (pPipeComNode == nullptr)
        return nullptr;
    return pPipeComNode->parent();
}
bool ThroBase::prevLeaveGPoint(WDNode::SharedPtr pPipeComNode, std::pair<DVec3, DVec3>& outPoint) const
{
    if (pPipeComNode == nullptr)
        return false;
    WDNode::SharedPtr pBranchNode = this->branchNode(pPipeComNode);
    if (pBranchNode == nullptr)
        return false;
    WDNode::SharedPtr pPrevPipeComNode = WDBMDPipeUtils::PrevPipeComponent(pBranchNode, pPipeComNode);
    if (pPrevPipeComNode == nullptr)
    {
        if (!pBranchNode->isType("BRAN"))
            return false;
        outPoint.first  = pBranchNode->getAttribute("Hposition WRT World").toDVec3();
        outPoint.second = pBranchNode->getAttribute("Hdirection WRT World").toDVec3();
        return true;
    }
    if (!WDBMDPipeUtils::IsPipeComponent(*pPrevPipeComNode))
        return false;
    auto pLeavePoint = pPrevPipeComNode->keyPoint(pPrevPipeComNode->getAttribute("Leave").toInt());
    if (pLeavePoint == nullptr)
        return false;
    const auto& gMat = pPrevPipeComNode->globalTransform();
    outPoint.first = pLeavePoint->transformedPosition(gMat);
    outPoint.second = pLeavePoint->transformedDirection(gMat);
    return true;
}
bool ThroBase::nextArriveGPoint(WDNode::SharedPtr pPipeComNode, std::pair<DVec3, DVec3>& arrive) const
{
    if (pPipeComNode == nullptr)
        return false;
    WDNode::SharedPtr pBranchNode = this->branchNode(pPipeComNode);
    if (pBranchNode == nullptr)
        return false;
    // 获取后一个管件节点
    WDNode::SharedPtr pNextPipeComNode = WDBMDPipeUtils::NextPipeComponent(pBranchNode, pPipeComNode);
    if (pNextPipeComNode == nullptr)
    {
        if (!pBranchNode->isType("BRAN"))
            return false;
        arrive.first    = pBranchNode->getAttribute("Tposition WRT World").toDVec3();
        arrive.second   = pBranchNode->getAttribute("Tdirection WRT World").toDVec3();
        return true;
    }
    if (!WDBMDPipeUtils::IsPipeComponent(*pNextPipeComNode))
        return false;
    auto pArrivePoint = pNextPipeComNode->keyPoint(pNextPipeComNode->getAttribute("Arrive").toInt());
    if (pArrivePoint == nullptr)
        return false;
    const auto& gMat = pNextPipeComNode->globalTransform();
    arrive.first = pArrivePoint->transformedPosition(gMat);
    arrive.second = pArrivePoint->transformedDirection(gMat);
    return true;
}
bool ThroBase::currArriveGPoint(WDNode::SharedPtr pPipeComNode, std::pair<DVec3, DVec3>& outPoint) const
{
    if (pPipeComNode == nullptr || !WDBMDPipeUtils::IsPipeComponent(*pPipeComNode))
        return false;
    auto pArrivePoint = pPipeComNode->keyPoint(pPipeComNode->getAttribute("Arrive").toInt());
    if (pArrivePoint == nullptr)
        return false;
    const auto& gMat = pPipeComNode->globalTransform();
    outPoint.first = pArrivePoint->transformedPosition(gMat);
    outPoint.second = pArrivePoint->transformedDirection(gMat);
    return true;
}
bool ThroBase::currLeaveGPoint(WDNode::SharedPtr pPipeComNode, std::pair<DVec3, DVec3>& leave) const
{
    if (pPipeComNode == nullptr || !WDBMDPipeUtils::IsPipeComponent(*pPipeComNode))
        return false;
    auto pLeavePoint = pPipeComNode->keyPoint(pPipeComNode->getAttribute("Leave").toInt());
    if (pLeavePoint == nullptr)
        return false;
    const auto& gMat = pPipeComNode->globalTransform();
    leave.first = pLeavePoint->transformedPosition(gMat);
    leave.second = pLeavePoint->transformedDirection(gMat);;
    return true;
}
bool ThroBase::hGPoint(WDNode::SharedPtr pBranchNode, DVec3 & outPoint) const
{
    if (pBranchNode == nullptr || !pBranchNode->isType("BRAN"))
        return false;
    outPoint = pBranchNode->getAttribute("Hposition WRT World").toDVec3();
    return true;
}
bool ThroBase::tGPoint(WDNode::SharedPtr pBranchNode, DVec3 & outPoint) const
{
    if (pBranchNode == nullptr || !pBranchNode->isType("BRAN"))
        return false;
    outPoint = pBranchNode->getAttribute("Tposition WRT World").toDVec3();
    return true;
}
bool ThroBase::prevGPoint(WDNode::SharedPtr pPipeComNode, DVec3 & outPoint) const
{
    if (pPipeComNode == nullptr)
        return false;
    WDNode::SharedPtr pBranchNode = this->branchNode(pPipeComNode);
    if (pBranchNode == nullptr)
        return false;
    WDNode::SharedPtr pPrevPipeComNode = WDBMDPipeUtils::PrevPipeComponent(pBranchNode, pPipeComNode);
    if (pPrevPipeComNode == nullptr)
        return this->hGPoint(pBranchNode, outPoint);
    outPoint = pPrevPipeComNode->globalTranslation();
    return true;
}
bool ThroBase::nextGPoint(WDNode::SharedPtr pPipeComNode, DVec3 & outPoint) const
{
    if (pPipeComNode == nullptr)
        return false;
    WDNode::SharedPtr pBranchNode = this->branchNode(pPipeComNode);
    if (pBranchNode == nullptr)
        return false;
    WDNode::SharedPtr pNextPipeComNode = WDBMDPipeUtils::NextPipeComponent(pBranchNode, pPipeComNode);
    if (pNextPipeComNode == nullptr)
        return this->tGPoint(pBranchNode, outPoint);
    outPoint = pNextPipeComNode->globalTranslation();
    return true;
}

bool ThroCE::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence) 
{
    _insertionSequence  =   insertionSequence;
    return true; 
}
void ThroCE::exit()
{
}

bool ThroCursor::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    _insertionSequence  =   insertionSequence;
    _bStartExec         =   false;

    if (pPipeComNode == nullptr)
        return false;

    _pPipeComNode = pPipeComNode;

    //激活捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    WDCapturePositioningParam param;
    param.pMonitor = this;
    WD::WDCapturePositioningOption option(WD::WDCapturePositioningType::CPT_Screen);
    if (tool.activate(param, option))
    {
        _bStartExec = true;
        return true;
    }
    return false;
}
void ThroCursor::exit()
{
    if (!_bStartExec)
        return;
    _bStartExec = false;
    _pPipeComNode.reset();
    //退出捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    tool.deativate();
}
void ThroCursor::onResult(const WDCapturePositioningResult& result
    , bool& existFlag
    , const WDCapturePositioning& sender)
{
    WDUnused(sender);
     //对齐计算
    DVec2 screen = result.point.xy();
    switch (_insertionSequence)
    {
    case WD::PFD_Forward:
        this->calcThroForward(screen);
        break;
    case WD::PFD_Backward:
        this->calcThroBackward(screen);
        break;
    default:
        break;
    }
    //重置管线节点
    _pPipeComNode.reset();
    //设置捕捉退出
    existFlag = true;
    //取消开始标志
    _bStartExec = false;
}
void ThroCursor::onDeactived(const WDCapturePositioning& sender)
{
    WDUnused(sender);
    _pPipeComNode.reset();
    _bStartExec = false;
}
void ThroCursor::calcThroForward(const DVec2& screenPos) const
{
    auto pPipeComNode = _pPipeComNode.lock();
    if (pPipeComNode == nullptr)
        return;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return ;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return ;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    
    auto pCamera = _core.viewer().camera();
    if (pCamera == nullptr) 
    {
        assert(false);
        return;
    }
    //获取摄像机的前方向
    DVec3 cameraFrontDir = pCamera->frontDir();
    
    //构造平面
    DPlane plane(-cameraFrontDir, gPos);
    //视图大小
    IVec2 viewerSize = _core.viewer().size();
    //使用摄像机构建射线
    DRay ray = pCamera->createRayFromScreen(screenPos, viewerSize);
    //计算摄像和平面的交点
    auto ret = ray.intersect(plane);
    if (!ret.first)
        return ;
    DVec3 rayPoint = ray.at(ret.second);
    //管件位置到交点的向量
    DVec3 rVector = rayPoint - gPos;
    //向量投影到前一个管件的朝向上
    double dt = DVec3::Dot(rVector, prevLeaveGDirection);
    //计算出结果点
    DVec3 rPoint = gPos + prevLeaveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());
}
void ThroCursor::calcThroBackward(const DVec2& screenPos) const
{
    auto pPipeComNode = _pPipeComNode.lock();
    if (pPipeComNode == nullptr)
        return;
    //获取后一个管件的入口方向
    std::pair<DVec3, DVec3> nextArriveGPoint;
    if (!this->nextArriveGPoint(pPipeComNode, nextArriveGPoint))
        return ;
    DVec3 nextArriveGDirection = nextArriveGPoint.second;
    if (nextArriveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return ;
    DVec3 nextArriveGPos = nextArriveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在后一个管件的入口方向所在的直线上
    {
        DVec3 gVec = nextArriveGPos - gPos;
        double dt = DVec3::Dot(gVec, nextArriveGDirection);
        gPos = nextArriveGPos + nextArriveGDirection * dt;
    }

    auto pCamera = _core.viewer().camera();
    if (pCamera == nullptr)
    {
        assert(false);
        return;
    }
    //获取摄像机的前方向
    DVec3 cameraFrontDir = pCamera->frontDir();

    //构造平面
    DPlane plane(-cameraFrontDir, gPos);
    //视图大小
    IVec2 viewerSize = _core.viewer().size();
    //使用摄像机构建射线
    DRay ray = pCamera->createRayFromScreen(screenPos, viewerSize);
    //计算摄像和平面的交点
    auto ret = ray.intersect(plane);
    if (!ret.first)
        return ;
    DVec3 rayPoint = ray.at(ret.second);
    //管件位置到交点的向量
    DVec3 rVector = rayPoint - gPos;
    //向量投影到后一个管件的朝向上
    double dt = DVec3::Dot(rVector, nextArriveGDirection);
    //计算出结果点
    DVec3 rPoint = gPos + nextArriveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());
}

bool ThroIDCursor::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    _insertionSequence  =   insertionSequence;
    if (pPipeComNode == nullptr)
        return false;

    _pPipeComNode = pPipeComNode;

    //激活捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    WDCapturePositioningParam param;
    param.pMonitor = this;
    WD::WDCapturePositioningOption option(WD::WDCapturePositioningType::CPT_Element);
    if (tool.activate(param, option))
    {
        _bStartExec = true;
        return true;
    }
    return false;

}
void ThroIDCursor::exit()
{
    if (!_bStartExec)
        return;
    _bStartExec = false;
    _pPipeComNode.reset();
    //退出捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    tool.deativate();
}
void ThroIDCursor::onResult(const WDCapturePositioningResult& result
    , bool& existFlag
    , const WDCapturePositioning& sender)
{
    WDUnused(sender);
    if (result.node.expired())
        return;
     //对齐计算
    switch (_insertionSequence)
    {
    case WD::PFD_Forward:
        this->calcThroForward(result.point);
        break;
    case WD::PFD_Backward:
        this->calcThroBackward(result.point);
        break;
    default:
        break;
    }
    //重置管线节点
    _pPipeComNode.reset();
    //设置捕捉退出
    existFlag = true;
    //取消开始标志
    _bStartExec = false;
}
void ThroIDCursor::onDeactived(const WDCapturePositioning& sender)
{
    WDUnused(sender);
    _pPipeComNode.reset();
    _bStartExec = false;
}
void ThroIDCursor::calcThroForward(const DVec3& nodePos) const
{
    auto pPipeComNode = _pPipeComNode.lock();
    if (pPipeComNode == nullptr)
        return;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return ;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return ;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //再与捕捉结果点进行对齐计算
    DVec3 rPos = gPos;
    {
        //当前管件世界坐标位置到捕捉的位置的向量
        DVec3 gVec = nodePos - gPos;
        //投影到前一个管件的出口方向上
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        rPos = gPos + prevLeaveGDirection * dt;
    }

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPos - pPipeComNode->globalTranslation());
}
void ThroIDCursor::calcThroBackward(const DVec3& nodePos) const
{
    WDNode::SharedPtr   pPipeComNode    =   _pPipeComNode.lock();
    if (pPipeComNode == nullptr)
        return;
    //获取后一个管件的入口方向
    std::pair<DVec3, DVec3> nextArriveGPoint;
    if (!this->nextArriveGPoint(pPipeComNode, nextArriveGPoint))
        return ;
    DVec3 nextArriveGDirection = nextArriveGPoint.second;
    if (nextArriveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return ;
    DVec3 nextArriveGPos = nextArriveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在后一个管件的入口方向所在的直线上
    {
        DVec3 gVec = nextArriveGPos - gPos;
        double dt = DVec3::Dot(gVec, nextArriveGDirection);
        gPos = nextArriveGPos + nextArriveGDirection * dt;
    }
    //再与捕捉结果点进行对齐计算
    DVec3 rPos = gPos;
    {
        //当前管件世界坐标位置到捕捉的位置的向量
        DVec3 gVec = nodePos - gPos;
        //投影到前一个管件的出口方向上
        double dt = DVec3::Dot(gVec, nextArriveGDirection);
        rPos = gPos + nextArriveGDirection * dt;
    }
    // undo/redo
    auto pCmd = WD::WDBMBase::MakeMoveCommand({pPipeComNode}, rPos - pPipeComNode->globalTranslation());
    if (pCmd != nullptr)
    {
        pCmd->setNoticeAfterRedo([this](const WD::WDUndoCommand&)
            {
                _core.needRepaint();
            });
        pCmd->setNoticeAfterUndo([this](const WD::WDUndoCommand&)
            {
                _core.needRepaint();
            });
        _core.undoStack().push(pCmd);
    }
}

bool ThroPoint::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    _insertionSequence  =   insertionSequence;
    if (pPipeComNode == nullptr)
        return false;

    _pPipeComNode = pPipeComNode;

    //激活捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    WDCapturePositioningParam param;
    param.pMonitor = this;
    WD::WDCapturePositioningOption option(WD::WDCapturePositioningType::CPT_PPoint);
    if (tool.activate(param, option))
    {
        _bStartExec = true;
        return true;
    }
    return false;
}
void ThroPoint::exit()
{
    if (!_bStartExec)
        return;
    _bStartExec = false;
    _pPipeComNode.reset();
    //退出捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    tool.deativate();
}
void ThroPoint::onResult(const WDCapturePositioningResult& result
    , bool& existFlag
    , const WDCapturePositioning& sender)
{
    WDUnused(sender);
    if (result.node.expired() 
        || ! result.keyPointData
        || ! result.keyPointData.value().indexValid())
        return;
     //对齐计算
    switch (_insertionSequence)
    {
    case WD::PFD_Forward:
        this->calcThroForward(result.point);
        break;
    case WD::PFD_Backward:
        this->calcThroBackward(result.point);
        break;
    default:
        break;
    }
    //重置管线节点
    _pPipeComNode.reset();
    //设置捕捉退出
    existFlag = true;
    //取消开始标志
    _bStartExec = false;
}
void ThroPoint::onDeactived(const WDCapturePositioning& sender)
{
    WDUnused(sender);
    _pPipeComNode.reset();
    _bStartExec = false;
}
void ThroPoint::calcThroForward(const DVec3& nodePos) const
{
    auto pPipeComNode = _pPipeComNode.lock();
    if (pPipeComNode == nullptr)
        return;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return ;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return ;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //再与捕捉结果点进行对齐计算
    DVec3 rPos = gPos;
    {
        //当前管件世界坐标位置到捕捉的位置的向量
        DVec3 gVec = nodePos - gPos;
        //投影到前一个管件的出口方向上
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        rPos = gPos + prevLeaveGDirection * dt;
    }

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPos - pPipeComNode->globalTranslation());
}
void ThroPoint::calcThroBackward(const DVec3& nodePos) const
{
    auto pPipeComNode = _pPipeComNode.lock();
    if (pPipeComNode == nullptr)
        return;
    //获取后一个管件的入口方向
    std::pair<DVec3, DVec3> nextArriveGPoint;
    if (!this->nextArriveGPoint(pPipeComNode, nextArriveGPoint))
        return ;
    DVec3 nextArriveGDirection = nextArriveGPoint.second;
    if (nextArriveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return ;
    DVec3 nextArriveGPos = nextArriveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在后一个管件的入口方向所在的直线上
    {
        DVec3 gVec = nextArriveGPos - gPos;
        double dt = DVec3::Dot(gVec, nextArriveGDirection);
        gPos = nextArriveGPos + nextArriveGDirection * dt;
    }
    //再与捕捉结果点进行对齐计算
    DVec3 rPos = gPos;
    {
        //当前管件世界坐标位置到捕捉的位置的向量
        DVec3 gVec = nodePos - gPos;
        //投影到前一个管件的出口方向上
        double dt = DVec3::Dot(gVec, nextArriveGDirection);
        rPos = gPos + nextArriveGDirection * dt;
    }

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPos - pPipeComNode->globalTranslation());
}

bool ThroNext::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    switch (insertionSequence)
    {
    case WD::PFD_Forward:
        return this->calcThroForward(pPipeComNode);
        break;
    case WD::PFD_Backward:
        return this->calcThroBackward(pPipeComNode);
        break;
    default:
        break;
    }
    return false;
}
void ThroNext::exit()
{
}
bool ThroNext::calcThroForward(WDNode::SharedPtr pPipeComNode)
{
    if (pPipeComNode == nullptr)
        return false;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return false;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return false;
    //下一个管件的世界坐标位置
    DVec3 nextGPos;
    if (!this->nextGPoint(pPipeComNode, nextGPos))
        return false;;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //当前管件世界坐标位置到下一个管件世界坐标位置的向量
    DVec3 gVec = nextGPos - gPos;
    //投影到前一个管件的出口方向上
    double dt = DVec3::Dot(gVec, prevLeaveGDirection);
    DVec3 rPoint = gPos + prevLeaveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());

    return true;
}
bool ThroNext::calcThroBackward(WDNode::SharedPtr pPipeComNode)
{
    if (pPipeComNode == nullptr)
        return false;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return false;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return false;
    //前一个管件的世界坐标位置
    DVec3 prevGPos;
    if (!this->prevGPoint(pPipeComNode, prevGPos))
        return false;;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //当前管件世界坐标位置到前一个管件世界坐标位置的向量
    DVec3 gVec = prevGPos - gPos;
    //投影到前一个管件的出口方向上
    double dt = DVec3::Dot(gVec, prevLeaveGDirection);
    DVec3 rPoint = gPos + prevLeaveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());

    return true;
}

bool ThroPrevious::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    switch (insertionSequence)
    {
    case WD::PFD_Forward:
        return this->calcThroForward(pPipeComNode);
        break;
    case WD::PFD_Backward:
        return this->calcThroBackward(pPipeComNode);
        break;
    default:
        break;
    }
    return false;
}
void ThroPrevious::exit()
{
}
bool ThroPrevious::calcThroForward(WDNode::SharedPtr pPipeComNode)
{
    if (pPipeComNode == nullptr)
        return false;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return false;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return false;
    //前一个管件的世界坐标位置
    DVec3 prevGPos;
    if (!this->prevGPoint(pPipeComNode, prevGPos))
        return false;;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //当前管件世界坐标位置到前一个管件世界坐标位置的向量
    DVec3 gVec = prevGPos - gPos;
    //投影到前一个管件的出口方向上
    double dt = DVec3::Dot(gVec, prevLeaveGDirection);
    DVec3 rPoint = gPos + prevLeaveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());

    return true;
}
bool ThroPrevious::calcThroBackward(WDNode::SharedPtr pPipeComNode)
{
    if (pPipeComNode == nullptr)
        return false;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return false;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return false;
    //下一个管件的世界坐标位置
    DVec3 nextGPos;
    if (!this->nextGPoint(pPipeComNode, nextGPos))
        return false;;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //当前管件世界坐标位置到下一个管件世界坐标位置的向量
    DVec3 gVec = nextGPos - gPos;
    //投影到前一个管件的出口方向上
    double dt = DVec3::Dot(gVec, prevLeaveGDirection);
    DVec3 rPoint = gPos + prevLeaveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());

    return true;
}

bool ThroHead::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    _insertionSequence  =   insertionSequence;
    if (pPipeComNode == nullptr)
        return false;
    WDNode::SharedPtr pBranchNode = this->branchNode(pPipeComNode);
    if (pBranchNode == nullptr)
        return false;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return false;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return false;
    //分支首的世界坐标位置
    DVec3 hGPos;
    if (!this->hGPoint(pBranchNode, hGPos))
        return false;;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //当前管件世界坐标位置到分支首世界坐标位置的向量
    DVec3 gVec = hGPos - gPos;
    //投影到前一个管件的出口方向上
    double dt = DVec3::Dot(gVec, prevLeaveGDirection);
    DVec3 rPoint = gPos + prevLeaveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());

    return true;
}
void ThroHead::exit()
{
}

bool ThroTail::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    _insertionSequence  =   insertionSequence;
    if (pPipeComNode == nullptr)
        return false;
    WDNode::SharedPtr pBranchNode = this->branchNode(pPipeComNode);
    if (pBranchNode == nullptr)
        return false;
    //获取前一个管件的出口方向
    std::pair<DVec3, DVec3> prevLeaveGPoint;
    if (!this->prevLeaveGPoint(pPipeComNode, prevLeaveGPoint))
        return false;
    DVec3 prevLeaveGDirection = prevLeaveGPoint.second;
    if (prevLeaveGDirection.lengthSq() <= NumLimits<float>::Epsilon)
        return false;
    DVec3 prevLeaveGPos = prevLeaveGPoint.first;
    //当前管件世界坐标位置
    DVec3 gPos = pPipeComNode->globalTranslation();
    //先保证当前管件在前一个管件的出口方向所在的直线上
    {
        DVec3 gVec = gPos - prevLeaveGPos;
        double dt = DVec3::Dot(gVec, prevLeaveGDirection);
        gPos = prevLeaveGPos + prevLeaveGDirection * dt;
    }
    //分支尾的世界坐标位置
    DVec3 tGPos;
    if (!this->tGPoint(pBranchNode, tGPos))
        return false;
    //当前管件世界坐标位置到分支尾世界坐标位置的向量
    DVec3 gVec = tGPos - gPos;
    //投影到前一个管件的出口方向上
    double dt = DVec3::Dot(gVec, prevLeaveGDirection);
    DVec3 rPoint = gPos + prevLeaveGDirection * dt;

    // undo/redo
    MakeMoveCmd(_core, {pPipeComNode}, rPoint - pPipeComNode->globalTranslation());

    return true;
}
void ThroTail::exit()
{
}

bool ThroConnect::exec(WDNode::SharedPtr pPipeComNode, PipeFlowDir insertionSequence)
{
    switch (insertionSequence)
    {
    case WD::PFD_Forward:
        return this->calcThroForward(pPipeComNode);
        break;
    case WD::PFD_Backward:
        return this->calcThroBackward(pPipeComNode);
        break;
    default:
        break;
    }
    return false;
}
void ThroConnect::exit()
{
}
bool ThroConnect::calcThroForward(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return false;
    // 当前管件所属的分支节点
    auto pBran = pNode->parent();
    if (pBran == nullptr || !pBran->isType("BRAN"))
        return false;
    auto bm = _core.currentBM();
    if (bm == nullptr)
        return false;

    // undo/redo指令所需数据
    const auto& beforePos = pNode->getAttribute("Position");
    const auto& beforeOri = pNode->getAttribute("Orientation");

    // 获取上游坐标朝向
    WD::DVec3   gLeavePos;
    WD::DVec3   gLeaveDir;
    std::string leaveConn;
    WD::WDNode::SharedPtr pPrev = WD::WDBMDPipeUtils::PrevPipeComponent(pBran, pNode);
    if (pPrev == nullptr)
    { //如果分支不存在管件，则将管件 arrive点 默认放到分支起点

        gLeavePos   =   pBran->getAttribute("Hposition WRT World").toDVec3();
        gLeaveDir   =   pBran->getAttribute("Hdirection WRT World").toDVec3();
        leaveConn   =   GetHConnType(*pBran);
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pPrev))
    {
        auto pPtLeave = pPrev->keyPoint(pPrev->getAttribute("Leave").toInt());
        if (pPtLeave != nullptr)
        {
            gLeavePos   =   pPtLeave->transformedPosition(pPrev->globalTransform());
            gLeaveDir   =   WD::DVec3::Normalize(pPtLeave->transformedDirection(pPrev->globalRSTransform()));
            leaveConn   =   GetComsTConnType(*pPrev);
        }
    }
    else
    {
        assert(false);
    }

    //校验COCO表，如果不能连接，则报错，且管件位置不会发生变化
    if (this->getCocoCheckEnabledByConfig())
    {
        auto ret = CheckCOCOTable(_core.getBMCatalog(), leaveConn, GetComsHConnType(*pNode));
        if (ret != CCCR_Success)
        {
            _lastError = WDTs("ErrorUiComNodePipeComponent", "ConnectTypeNotMatch");
            return false;
        }
    }

    // 管件出入口朝向对齐
    WD::ComponentAlignDir(_core, *pNode, -gLeaveDir, WD::PipeFlowDir::PFD_Forward);

    // 管件P0朝向对齐世界坐标系Z轴
    {
        // 上游朝向和原点构成旋转平面
        auto rotPlane = WD::DPlane(gLeaveDir, WD::DVec3::Zero());

        // 世界坐标系Z轴投影到旋转平面
        auto projGZ = rotPlane.project(WD::DVec3::AxisZ());
        if (!WD::DVec3::SamePosition(projGZ, WD::DVec3::Zero()))
        {
            // 管件P0朝向投影到旋转平面
            auto p0Dir = pNode->globalRSTransform() * WD::DVec3::AxisZ();
            auto projGP = rotPlane.project(p0Dir);
            if (!WD::DVec3::SamePosition(projGP, WD::DVec3::Zero()) && !WD::DVec3::InTheSameDirection(projGZ, projGP))
            {
                // 管件绕旋转平面法线，从P0朝向投影旋转到世界坐标系Z轴投影
                auto angle = WD::DVec3::Angle(projGP, projGZ);
                auto cross = WD::DVec3::Cross(projGP, projGZ);
                if (WD::DVec3::Dot(cross, gLeaveDir) < 0)
                    angle = -angle;
                pNode->rotate(gLeaveDir, angle);
                pNode->update();
            }
        }
    }

    // 管件出口点朝向旋转到世界坐标系XOY平面
    {
        // 获取管件出口点朝向
        auto pComLeavePt = pNode->keyPoint(pNode->getAttribute("Leave").toInt());
        auto pComArrivePt = pNode->keyPoint(pNode->getAttribute("Arrive").toInt());
        if (pComLeavePt != nullptr && pComArrivePt != nullptr)
        {
            auto gComLeaveDir = pComLeavePt->transformedDirection(pNode->globalRSTransform());
            auto gComArriveDir = pComArrivePt->transformedDirection(pNode->globalRSTransform());
            if (!WD::DVec3::OnTheSameLine(gComLeaveDir, gComArriveDir))
            {
                // 将出口朝向投影到XOY平面
                auto XOYPlane = WD::DPlane(WD::DVec3::AxisZ(), WD::DVec3::Zero());
                auto projXOYLeaveDir = XOYPlane.project(gComLeaveDir);
                if (!WD::DVec3::SamePosition(projXOYLeaveDir, WD::DVec3::Zero())) // 投影向量与XOY法线即Z轴不平行
                {
                    // 上游朝向和原点构成旋转平面
                    auto rotPlane = WD::DPlane(gLeaveDir, WD::DVec3::Zero());
                    // 将出口朝向投影到XOY平面的向量投影到旋转平面
                    auto projProjDir = rotPlane.project(projXOYLeaveDir);
                    if (!WD::DVec3::SamePosition(projProjDir, WD::DVec3::Zero()))
                    {
                        // 将出口朝向投影到旋转平面
                        auto projRotLeaveDir = rotPlane.project(gComLeaveDir);
                        if (!WD::DVec3::SamePosition(projRotLeaveDir, WD::DVec3::Zero())
                            && !WD::DVec3::InTheSameDirection(projRotLeaveDir, projProjDir))
                        {
                            // 管件绕旋转平面法线，从出口点朝向旋转到世界坐标系XOY平面
                            auto angle = WD::DVec3::Angle(projRotLeaveDir, projProjDir);
                            auto cross = WD::DVec3::Cross(projRotLeaveDir, projProjDir);
                            if (WD::DVec3::Dot(cross, gLeaveDir) < 0)
                                angle = -angle;
                            pNode->rotate(gLeaveDir, angle);
                            pNode->update();
                        }
                    }
                }
                else // 投影向量与XOY法线即Z轴平行
                {
                    pNode->rotate(gLeaveDir, 90);
                    pNode->update();
                }
            }
        }
    }

    // 管件出入口坐标对齐
    WD::ComponentAlignPos(_core, *pNode, gLeavePos, WD::PipeFlowDir::PFD_Forward);

    auto pCmd = new WD::WDUndoCommand("ThroForward");
    WD::WDBMBase::MakeAttributeSetedCommand(pNode, "Position", beforePos, pCmd);
    WD::WDBMBase::MakeAttributeSetedCommand(pNode, "Orientation", beforeOri, pCmd);
    pCmd->setNoticeAfterRedo([&, pBran](const WD::WDUndoCommand&)
        {
            //更新分支的连接
            if (pBran != nullptr)
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
    pCmd->setNoticeAfterUndo([&, pBran](const WD::WDUndoCommand&)
        {
            //更新分支的连接
            if (pBran != nullptr)
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
    _core.undoStack().push(pCmd);

    return true;
}
bool ThroConnect::calcThroBackward(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return false;
    // 当前管件所属的分支节点
    auto pBran = pNode->parent();
    if (pBran == nullptr || !pBran->isType("BRAN"))
        return false;
    auto bm = _core.currentBM();
    if (bm == nullptr)
        return false;

    // undo/redo指令所需数据
    const auto& beforePos = pNode->getAttribute("Position");
    const auto& beforeOri = pNode->getAttribute("Orientation");

    // 获取上游坐标朝向
    WD::DVec3   gArrivePos;
    WD::DVec3   gArriveDir;
    std::string arriveConn;
    WD::WDNode::SharedPtr pNext = WD::WDBMDPipeUtils::NextPipeComponent(pBran, pNode);
    if (pNext == nullptr)
    { //如果分支不存在管件，则将管件 leave点 默认放到分支终点

        gArrivePos   =   pBran->getAttribute("Tposition WRT World").toDVec3();
        gArriveDir   =   pBran->getAttribute("Tdirection WRT World").toDVec3();
        arriveConn   =   GetTConnType(*pBran);
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pNext))
    {
        auto pPtArrive = pNext->keyPoint(pNext->getAttribute("Arrive").toInt());
        if (pPtArrive != nullptr)
        {
            gArrivePos   =   pPtArrive->transformedPosition(pNext->globalTransform());
            gArriveDir   =   WD::DVec3::Normalize(pPtArrive->transformedDirection(pNext->globalRSTransform()));
            arriveConn  =   GetComsHConnType(*pNext);
        }
    }
    else
    {
        assert(false);
    }

    //校验COCO表，如果不能连接，则报错，且管件位置不会发生变化
    if (this->getCocoCheckEnabledByConfig())
    {
        auto ret = CheckCOCOTable(_core.getBMCatalog(), GetComsTConnType(*pNode), arriveConn);
        if (ret != CCCR_Success)
        {
            _lastError = WDTs("ErrorUiComNodePipeComponent", "ConnectTypeNotMatch");
            return false;
        }
    }

    // 管件出入口朝向对齐
    WD::ComponentAlignDir(_core, *pNode, -gArriveDir, WD::PipeFlowDir::PFD_Backward);

    // 管件P0朝向对齐世界坐标系Z轴
    {
        // 上游朝向和原点构成旋转平面
        auto rotPlane = WD::DPlane(gArriveDir, WD::DVec3::Zero());

        // 世界坐标系Z轴投影到旋转平面
        auto projGZ = rotPlane.project(WD::DVec3::AxisZ());
        if (!WD::DVec3::SamePosition(projGZ, WD::DVec3::Zero()))
        {
            // 管件P0朝向投影到旋转平面
            auto p0Dir = pNode->globalRSTransform() * WD::DVec3::AxisZ();
            auto projGP = rotPlane.project(p0Dir);
            if (!WD::DVec3::SamePosition(projGP, WD::DVec3::Zero()) && !WD::DVec3::InTheSameDirection(projGZ, projGP))
            {
                // 管件绕旋转平面法线，从P0朝向投影旋转到世界坐标系Z轴投影
                auto angle = WD::DVec3::Angle(projGP, projGZ);
                auto cross = WD::DVec3::Cross(projGP, projGZ);
                if (WD::DVec3::Dot(cross, gArriveDir) < 0)
                    angle = -angle;
                pNode->rotate(gArriveDir, angle);
                pNode->update();
            }
        }
    }

    // 管件入口点朝向旋转到世界坐标系XOY平面
    {
        // 获取管件入口点朝向
        auto pComArrivePt = pNode->keyPoint(pNode->getAttribute("Arrive").toInt());
        auto pComLeavePt = pNode->keyPoint(pNode->getAttribute("Leave").toInt());
        if (pComArrivePt != nullptr && pComLeavePt != nullptr)
        {
            auto gComArriveDir = pComArrivePt->transformedDirection(pNode->globalRSTransform());
            auto gComLeaveDir = pComLeavePt->transformedDirection(pNode->globalRSTransform());
            if (!WD::DVec3::OnTheSameLine(gComArriveDir, gComLeaveDir))
            {
                // 将入口朝向投影到XOY平面
                auto XOYPlane = WD::DPlane(WD::DVec3::AxisZ(), WD::DVec3::Zero());
                auto projXOYArriveDir = XOYPlane.project(gComArriveDir);
                if (!WD::DVec3::SamePosition(projXOYArriveDir, WD::DVec3::Zero())) // 投影向量与XOY法线即Z轴不平行
                {
                    // 上游朝向和原点构成旋转平面
                    auto rotPlane = WD::DPlane(gArriveDir, WD::DVec3::Zero());
                    // 将出口朝向投影到XOY平面的向量投影到旋转平面
                    auto projProjDir = rotPlane.project(projXOYArriveDir);
                    if (!WD::DVec3::SamePosition(projProjDir, WD::DVec3::Zero()))
                    {
                        // 将入口朝向投影到旋转平面
                        auto projRotArriveDir = rotPlane.project(gComArriveDir);
                        if (!WD::DVec3::SamePosition(projRotArriveDir, WD::DVec3::Zero())
                            && !WD::DVec3::InTheSameDirection(projRotArriveDir, projProjDir))
                        {
                            // 管件绕旋转平面法线，从入口点朝向旋转到世界坐标系XOY平面
                            auto angle = WD::DVec3::Angle(projRotArriveDir, projProjDir);
                            auto cross = WD::DVec3::Cross(projRotArriveDir, projProjDir);
                            if (WD::DVec3::Dot(cross, gArriveDir) < 0)
                                angle = -angle;
                            pNode->rotate(gArriveDir, angle);
                            pNode->update();
                        }
                    }
                }
                else // 投影向量与XOY法线即Z轴平行
                {
                    pNode->rotate(gArriveDir, 90);
                    pNode->update();
                }
            }
        }
    }

    // 管件出入口坐标对齐
    WD::ComponentAlignPos(_core, *pNode, gArrivePos, WD::PipeFlowDir::PFD_Backward);

    auto pCmd = new WD::WDUndoCommand("ThroForward");
    WD::WDBMBase::MakeAttributeSetedCommand(pNode, "Position", beforePos, pCmd);
    WD::WDBMBase::MakeAttributeSetedCommand(pNode, "Orientation", beforeOri, pCmd);
    pCmd->setNoticeAfterRedo([&, pBran](const WD::WDUndoCommand&)
        {
            //更新分支的连接
            if (pBran != nullptr)
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
    pCmd->setNoticeAfterUndo([&, pBran](const WD::WDUndoCommand&)
        {
            //更新分支的连接
            if (pBran != nullptr)
                pBran->triggerUpdate(true);
            _core.needRepaint();
        });
    _core.undoStack().push(pCmd);

    return true;
}


WD_NAMESPACE_END
