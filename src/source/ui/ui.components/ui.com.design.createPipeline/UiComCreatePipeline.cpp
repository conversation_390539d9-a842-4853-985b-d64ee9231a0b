#include    "UiComCreatePipeline.h"
#include    <QMessageBox>
#include    "core/viewer/WDViewer.h"
#include    "core/scene/WDScene.h"
#include    "core/nodeTree/WDNodeTree.h"
#include    "core/message/WDMessage.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include    "core/businessModule/WDBMPermissionMgr.h"
#include    "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include    "core/businessModule/WDBMClaimMgr.h"

UiComCreatePipeline::UiComCreatePipeline(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject *parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
    , _pipeBranchRender(mainWindow.core())
{
    _pPipeLineDialog        =   new UiComNodePipeLine(mWindow().core(), mWindow().widget());
    _pCreateBranchDialog    =   new CreateBranchDialog(mWindow().core(), mWindow().widget());
    _pPipeComponentDialog   =   new UiComNodePipeComponent(mWindow(), mWindow().widget());
	_pPipeArriveLeaveDialog =   new UiComNodePipeArriveLeavePoint(mWindow().core(), mWindow().widget());

    // 绑定事件通知响应
    connect(_pPipeLineDialog,   SIGNAL(sigConfirmCreatePipeLineSucc()),  this, SLOT(slotConfirmCreatePipeLine()));
}
UiComCreatePipeline::~UiComCreatePipeline()
{
    if (_pPipeLineDialog != nullptr)
    {
        delete _pPipeLineDialog;
        _pPipeLineDialog = nullptr;
    }
    if (_pCreateBranchDialog != nullptr)
    {
        delete _pCreateBranchDialog;
        _pCreateBranchDialog = nullptr;
    }
    if (_pPipeComponentDialog != nullptr)
    {
        delete _pPipeComponentDialog;
        _pPipeComponentDialog = nullptr;
    }
	if (_pPipeArriveLeaveDialog != nullptr)
	{
        delete _pPipeArriveLeaveDialog;
        _pPipeArriveLeaveDialog = nullptr;
	}
}

void UiComCreatePipeline::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        // 管线
        if (pActionNotice->action().is("action.design.create.node.pipe.level.pipe"))
        {
            if (_pPipeLineDialog->isHidden())
                _pPipeLineDialog->show();
            else
                _pPipeLineDialog->activateWindow();
        }
        // 分支
        else if (pActionNotice->action().is("action.design.create.node.pipe.level.branch"))
        {
            if (_pCreateBranchDialog->isHidden())
                _pCreateBranchDialog->show();
            else
                _pCreateBranchDialog->activateWindow();
        }
        // 管件
        else if (pActionNotice->action().is("action.design.create.node.pipe.pipeComponent"))
        {
            if (_pPipeComponentDialog->isHidden())
                _pPipeComponentDialog->show();
            else
                _pPipeComponentDialog->activateWindow();
            // 如果窗口处于最小化，则恢复正常状态
            if (_pPipeComponentDialog->isMinimized())
            {
                _pPipeComponentDialog->setWindowState(Qt::WindowNoState);
            }
        }
        // 管线流向
        else if (pActionNotice->action().is("action.design.pipeline.direction.visible"))
        {
            this->pipelineDirectionVisible();
        }
        /// 修改管道流向(逆向)(inverse dir) 
        /// req 16.1
        else if (pActionNotice->action().is("action.design.pipeline.inverse.dir"))
        {
            this->inversePipeDir();
        }
        // 管件出入口点
        else if (pActionNotice->action().is("action.design.node.mod.comp.arrive"))
        {
            // 获取当前选中节点
            auto pCurNode = WD::Core().nodeTree().currentNode();
            if (pCurNode == nullptr)
            {
                WD_ERROR_T("UiComCreatePipeline", "Must to select one node!");
                return ;
            }
            // 必须为管件节点
            if (!WD::WDBMDPipeUtils::IsPipeComponent(*pCurNode))
            {
                WD_ERROR_T("UiComCreatePipeline", "Current node is not pipeComponent!");
                return ;
            }
            if (_pPipeArriveLeaveDialog->isHidden())
                _pPipeArriveLeaveDialog->show();
            else
                _pPipeArriveLeaveDialog->activateWindow();
        }
    }
    break;
    case UiNoticeType::UNT_AllReady:
    {
        auto& core = mWindow().core();
        core.scene().addRender(&_pipeBranchRender);
    }
    break;
    case UiNoticeType::UNT_ReadyUnload:
    {
        auto& core = mWindow().core();
        core.scene().removeRender(&_pipeBranchRender);
    }
    break;
    case UiNoticeType::UNT_Command:
        {
            UiCommandNotice* pCommandNotice = static_cast<UiCommandNotice*>(pNotice);
            if (pCommandNotice->commandName() == "command.design.show.create.pipe.level.branch")
            {
                if (_pCreateBranchDialog->isHidden())
                    _pCreateBranchDialog->show();
                else
                    _pCreateBranchDialog->activateWindow();
            }
        }
        break;
    default:
        break;
    }
}

void UiComCreatePipeline::slotConfirmCreatePipeLine()
{
    _pCreateBranchDialog->show();
}

/**
 * @brief 设置分支的流向标志
 * @param branNode 分支节点
 * @param bOn 是否设置流向标志
 *  如果未指定值，则自动判断是否要设置
 * @return 是否设置成功
*/
bool SetBranchDirectionFlag(WD::WDNode& branNode, std::optional<bool> bOn = std::nullopt)
{
    bool tOn    = !WD::WDBMDPipeUtils::GetPipelineDirectionDisplayEnabled(branNode);
    if (bOn)
        tOn = bOn.value();
    // 分支数据设置流向显隐标志
    {
        WD::WDBMDPipeUtils::SetPipelineDirectionDisplayEnabled(branNode, tOn);
    }
    // 分支节点以及其所有子孙节点设置线框模式标志
    WD::WDNode::RecursionHelpter(branNode, [tOn](WD::WDNode& node)
        {
            auto flags = node.flags();
            flags.setFlag(WD::WDNode::Flag::F_WireFrame, tOn);
            node.setFlags(flags);
        });

    return true;
}
void UiComCreatePipeline::pipelineDirectionVisible()
{
    //获取当前树上的节点
    WD::WDNode::SharedPtr pNode = mWindow().core().nodeTree().currentNode();
    if (pNode == nullptr)
        return;
    //管线节点
    if (pNode->isType("PIPE"))
    {
        // 统计管道下的所有分支是否都有流向显隐标志
        // 如果是，则关闭所有分支的流向标志
        // 否则打开所有分支的流向标志
        bool bAll = true;
        for (auto pChild: pNode->children())
        {
            if (pChild == nullptr || !pChild->isType("BRAN"))
                continue;
            if (!WD::WDBMDPipeUtils::GetPipelineDirectionDisplayEnabled(*pChild))
            {
                bAll = false;
                break;
            }
        }
        bool bOn = !bAll;
        for (auto pChild : pNode->children())
        {
            if (pChild == nullptr || !pChild->isType("BRAN"))
                continue;
            SetBranchDirectionFlag(*pChild, bOn);
        }
        // 更新场景
        mWindow().core().sceneForceUpdate();
        // 触发重绘
        mWindow().core().needRepaint();
    }
    //分支节点
    else if (pNode->isType("BRAN"))
    {
        SetBranchDirectionFlag(*pNode);
        // 更新场景
        mWindow().core().sceneForceUpdate();
        // 触发重绘
        mWindow().core().needRepaint();
    }
    //管件节点
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pNode))
    {
        if (pNode->parent() == nullptr || !pNode->parent()->isType("BRAN"))
        {
            WD_WARN_T("ErrorUiComCreatePipeline", "SelectPipeTypeNode");
            return;
        }
        SetBranchDirectionFlag(*(pNode->parent()));
        // 更新场景
        mWindow().core().sceneForceUpdate();
        // 触发重绘
        mWindow().core().needRepaint();
    }
    // 直管节点
    else if (pNode->isType("TUBI"))
    {
        if (pNode->parent() == nullptr || !pNode->parent()->isType("BRAN"))
        {
            WD_WARN_T("ErrorUiComCreatePipeline", "SelectPipeTypeNode");
            return;
        }
        SetBranchDirectionFlag(*(pNode->parent()));
        // 更新场景
        mWindow().core().sceneForceUpdate();
        // 触发重绘
        mWindow().core().needRepaint();
    }
    //提示
    else
    {
        WD_WARN_T("ErrorUiComCreatePipeline", "SelectPipeTypeNode");
    }
}

void UiComCreatePipeline::inversePipeDir()
{
    WD::WDUndoCommand* pParentCmd = new WD::WDUndoCommand("InvertsBranch");

    /// 便利节点下所有子节点(递归),主要是查找分支节点
    auto    pSelectNode      =   WD::Core().nodeTree().currentNode();
    if (pSelectNode == nullptr)
    {
        WD_WARN_T("ErrorUiComCreatePipeline", "please select bran node or pipecom node!");
        return;
    }

    // 只支持分支节点
    WD::WDNode::SharedPtr pBranchNode = nullptr; 
    if (pSelectNode->isType("BRAN"))
    {
        pBranchNode = pSelectNode;
    }
    else if (WD::WDBMDPipeUtils::IsPipeComponent(*pSelectNode))
    {
        pBranchNode = pSelectNode->parent();
    }

    if (pBranchNode == nullptr)
    {
        WD_WARN_T("ErrorUiComCreatePipeline", "please select bran node or pipecom node!");
        return;
    }

    //  权限校验
    if (!WD::Core().getBMDesign().permissionMgr().check(*pBranchNode))
    {
        WD_WARN_T("ErrorUiComCreatePipeline", "You cannot operate the current node!");
        return;
    }

    // 申领修改分支节点
    bool bCancelMd = false;
    if (!WD::Core().getBMDesign().claimMgr().checkUpdate(pBranchNode
        , {"Hposition WRT World", "Hdirection WRT World", "Hbore", "Hconnect", "Href"
        , "Tposition WRT World", "Tdirection WRT World", "Tbore", "Tconnect", "Tref"}
        , bCancelMd))
        return;
    if (bCancelMd)
        return;

    WD::WDBMClaimMgr::MoveDatas checkDatas;
    WD::WDBMBase::NodeHierarchyMoveInfos infos;
    const auto& pComs = WD::WDBMDPipeUtils::PipeComponents(pBranchNode);
    for (int i = 0; i < pComs.size(); ++i)
    {
        auto pCom = pComs[i];
        if (pCom == nullptr)
            continue;

        if (i > 0)
        {
            checkDatas.emplace_back(WD::WDBMClaimMgr::MoveData(pCom, pBranchNode, pComs.at(i - 1)));
            infos.emplace_back(WD::WDBMBase::NodeHierarchyMoveInfo(pCom, pBranchNode, pComs.at(i - 1)));
        }
        else if (i == 0)
        {
            checkDatas.emplace_back(WD::WDBMClaimMgr::MoveData(pCom, pBranchNode, nullptr));
            infos.emplace_back(WD::WDBMBase::NodeHierarchyMoveInfo(pCom, pBranchNode, nullptr));
        }
    }
    // 申领移动所有管件
    if (!WD::Core().getBMDesign().claimMgr().checkMove(checkDatas))
        return ;

    // 这里只处理分支节点
    if (invertsBranch(pBranchNode, pParentCmd))
        WD_INFO_T("ErrorUiComCreatePipeline", "done in reverse order");
    else
        WD_ERROR_T("ErrorUiComCreatePipeline", "if the reverse order fails,the data my be irregular");

    auto func = [=](const WD::WDUndoCommand&)
        {
            if (pBranchNode == nullptr)
                return ;
            // 更新节点
            pBranchNode->triggerUpdate(true);
            // 触发重绘
            mWindow().core().needRepaint();
        };
    pParentCmd->setNoticeAfterRedo(func);
    pParentCmd->setNoticeAfterUndo(func);

    WD::WDBMBase::MakeNodeHierarchyMoveInfoCommand(infos, pParentCmd);
    WD::Core().undoStack().push(pParentCmd);
}
bool UiComCreatePipeline::invertsBranch(WD::WDNode::SharedPtr pBran, WD::WDUndoCommand* cmd)
{
    if (pBran == nullptr)
        return false;
    // 获取所有子节点
    auto pComs = WD::WDBMDPipeUtils::PipeComponents(pBran);
    if (pComs.empty())
        return false;

    auto& desiMgr = mWindow().core().getBMDesign();
    // 管件节点交换出入口点索引
    for (auto itr = pComs.rbegin(); itr != pComs.rend(); ++itr)
    {
        auto pNode = (*itr);
        if (pNode == nullptr)
            continue;
        // 交换出入口点
        int iArrive = pNode->getAttribute("Arrive").toInt();
        int iLeave = pNode->getAttribute("Leave").toInt();
        WD::WDBMBase::MakeAttributeSetCommand(pNode, "Arrive", iLeave, cmd);
        auto pLeaveCmd = WD::WDBMBase::MakeAttributeSetCommand(pNode, "Leave", iArrive, cmd);
        if (pLeaveCmd)
        {
            // 交换出入口点之后，设置异径管的直管等级
            auto func = [=](const WD::WDUndoCommand&)
            {
                // 更新模型数据
                pNode->updateModel();
            };
            pLeaveCmd->setNoticeAfterRedo(func);
            pLeaveCmd->setNoticeAfterUndo(func);
        }
    }
    // 交换分支头尾属性数据
    auto    hPos    =   pBran->getAttribute("Hposition").toDVec3();
    auto    hDir    =   pBran->getAttribute("Hdirection").toDVec3();
    auto    hBore   =   pBran->getAttribute("Hbore").toString();
    auto    hConn   =   pBran->getAttribute("Hconnect").toString();
    auto    pHRef   =   pBran->getAttribute("Href").toNodeRef();

    auto    tPos    =   pBran->getAttribute("Tposition").toDVec3();
    auto    tDir    =   pBran->getAttribute("Tdirection").toDVec3();
    auto    tBore   =   pBran->getAttribute("Tbore").toString();
    auto    tConn   =   pBran->getAttribute("Tconnect").toString();
    auto    pTRef   =   pBran->getAttribute("Tref").toNodeRef();

    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hposition", WD::WDBMAttrValue(tPos), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hdirection", WD::WDBMAttrValue(tDir), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hbore", WD::WDBMAttrValue(tBore), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Hconnect", WD::WDBMAttrValue(tConn), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Href", WD::WDBMAttrValue(pTRef), cmd);

    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tposition", WD::WDBMAttrValue(hPos), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tdirection", WD::WDBMAttrValue(hDir), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tbore", WD::WDBMAttrValue(hBore), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tconnect", WD::WDBMAttrValue(hConn), cmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tref", WD::WDBMAttrValue(pHRef), cmd);
    return true;
}
WD::WDNode::SharedPtr UiComCreatePipeline::getSpec(WD::WDNode::SharedPtr pSpNode)
{
    auto pParent = pSpNode;
    while (pParent != nullptr)
    {
        if (pParent->isType("SPEC"))
            return pParent;
        pParent = pParent->parent();
    }
    return  nullptr;
}