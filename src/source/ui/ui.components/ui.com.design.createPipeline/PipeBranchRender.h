#pragma     once

#include "core/scene/WDSceneNodeRender.h"
#include "core/viewer/primitiveRender/WDLineRender.h"
#include "core/viewer/primitiveRender/WDTextRender.h"
#include "core/geometry/standardPrimitives/WDGeometryCone.h"
#include "core/geometry/standardPrimitives/WDGeometrySphere.h"
#include "core/node/WDNode.h"

WD_NAMESPACE_BEGIN

class WDClip;
/**
 * @brief 绘制管线分支相关数据
*/
class PipeBranchRender : public WDSceneNodeRender
{
private:
    //这里记录一下所有的分支数据, 用于分支流向、坡度、首尾箭头等信息
    std::set<WDNode*> _branchNodeSet;
    //分支流向
    WDLineRender _branchDirectionDisplayLines;
    //分支流向的线材质
    WDLineRender::Param _branchDirectionDisplayLinesParam;
    //流向箭头几何体
    WDGeometryCone::SharedPtr _pBranchDirectionDisplayGeom;
    //分支流向箭头instance信息
    std::vector<WDInstance> _pipelineDirectionArrowInsts;
    //箭头流向的箭头材质
    WDMaterialPhong::SharedPtr _pipelineDirectionArrowMat;

    // 是否显示坡度
    bool _bShowSlope = true;
    // 坡度的显示范围
    DVec2 _showSlopeRange = DVec2(-10000.000000, 10000.000000);
    // 绘制坡度值
    WDText3DRender _tubiSlopeRender;
    // 是否绘制分支起点和终点
    bool _bShowBranchHTPos = false;
    // 分支起点和终点箭头instance信息
    std::vector<WDInstance> _branchHPosArrowInsts;
    std::vector<WDInstance> _branchTPosArrowInsts;
    // 分支起点和终点箭头材质
    WDMaterialPhong::SharedPtr _branchHTPosArrowMat;

public:
    PipeBranchRender(WDCore& app);
public:
    virtual void onNodeAdd(WDNode::SharedPtr pNode) override;
    virtual void onNodeRemove(WDNode::SharedPtr pNode) override;
    virtual void onNodeUpdate(WDNode::SharedPtr pNode) override;
    virtual void onNodeClear() override;
    virtual bool empty() const override;
    virtual bool containsRelatives(const WDNode& node)const override;
    virtual void updateAabb(WDContext& context, const WDScene& scene) override;
    virtual void update(WDContext& context, const WDScene& scene) override;
    virtual void render(WDContext& context, const WDScene& scene) override;
private:
    //添加分支数据
    void addBranchData(WDNode::SharedPtr pNode);
    //移除分支数据
    void removeBranchData(WDNode::SharedPtr pNode);
    //清除分支数据
    void clearBranchData();
    //更新包围盒
    void updateAabb();

    //根据两点生成绘制流向箭头的 Instance
    void genArrowInstances(WDInstances& outInsts, const FVec3& pt0, const FVec3& pt1, const std::string& bore, const WDClip& clip);
    // 根据位置和方向生成绘制分支起点终点箭头的 Instance
    bool genConeInstance(WDContext& context, const DVec3& pos, const DVec3& dir, WDInstance& outInst);
};

WD_NAMESPACE_END



