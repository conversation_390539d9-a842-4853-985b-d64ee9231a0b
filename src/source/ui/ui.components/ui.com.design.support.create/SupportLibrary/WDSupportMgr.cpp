#include "WDSupportMgr.h"
#include "core/WDObjectCreator.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/common/WDFileReader.hpp"
#include "WDBMNodeSerialize.h"
#include "core/businessModule/catalog/WDBMCatalog.h"

WD_NAMESPACE_BEGIN

WDSupportMgr::WDSupportMgr(WDCore& core)
    : _core(core)
{}
WDSupportMgr::~WDSupportMgr()
{
    _supports.clear();
}

WDSupport::SharedPtr    WDSupportMgr::addSupport(WDNode::SharedPtr pNode)
{
    if (pNode == nullptr)
        return nullptr;

    // 先查询支吊架模板管理中是否有同名支吊架模板
    if (this->querySupport(pNode->name()) != nullptr)
        return nullptr;

    // 新增支吊架模板
    _supports.push_back(WDSupport::MakeShared(_core, _core.getBMDesign().clone(pNode)));
    return _supports.back();
}
bool                    WDSupportMgr::addSupport(WDSupport::SharedPtr support)
{
    if (this->querySupport(support->name()) != nullptr)
    {
        //!TODO: 提示support支吊架模板名称重复
        return false;
    }

    // 整理已有supports
    std::map<std::string, WDSupports>   mapSupport;
    for(auto& pSupport : _supports)
    {
        if (pSupport == nullptr)
            continue;

        std::string gType   =   pSupport->gType();
        auto        pItr    =   mapSupport.find(gType);
        if (mapSupport.find(gType) == mapSupport.end())
        {
            mapSupport[gType] = {pSupport};
        }
        else
        {
            pItr->second.push_back(pSupport);
        }
    }
     
    // 在相同通用类型的支吊架模板中校验是否有同描述的参数模板
    std::string gType   =   support->gType();
    auto        pItr    =   mapSupport.find(gType);
    if (pItr == mapSupport.end())
    {
        _supports.push_back(support);
    }
    else
    {
        auto&   tSupports    =   pItr->second;
        for (auto& pTSupport : tSupports)
        {
            if (pTSupport == nullptr)
                continue;

            std::string desc = pTSupport->mType();
            if (desc == support->mType())
            {
                //!TODO: 提示pSupport支吊架模板在相同通用类型的支吊架模板中有相同描述的支吊架模板
                return false;
            }
        }
        _supports.push_back(support);
    }

    return true;
}
size_t                  WDSupportMgr::addSupports(const WDSupports& supports)
{
    size_t count = 0;
    for (auto& pSupport : supports)
    {
        if (this->addSupport(pSupport))
            count++;
    }
    return count;
}
bool                    WDSupportMgr::removeSupport(WDSupport::SharedPtr support)
{
    auto pItr = std::find(_supports.begin(), _supports.end(), support);
    if (pItr == _supports.end())
        return false;
    _supports.erase(pItr);
    return true;
}
WDSupport::SharedPtr    WDSupportMgr::querySupport(const std::string& name)
{
    for (auto itr = _supports.begin(); itr != _supports.end(); ++itr)
    {
        WDSupport::SharedPtr pSupport = (*itr);
        if (pSupport == nullptr)
            continue;
        if (pSupport->name() == name)
            return pSupport;
    }
    return nullptr;
}

size_t                  WDSupportMgr::read(const char* fileName)
{
    // 清空支吊架模板列表
    _supports.clear();

    // 读取文件
    WDFileReader    file(fileName);
    if (file.isBad())
    {
        return  0;
    }
    file.readAll();
    size_t length = file.length();
    if (length == 0)
        return 0;

    // 解析成json文档
    JsonDoc doc;
    doc.SetArray();
    doc.Parse((char*)file.data(), file.length());
    if (doc.HasParseError())
        return 0;
    for (auto& supportObj : doc.GetArray())
    {
        auto pSupport = this->readSupport(supportObj);
        if (pSupport != nullptr)
            _supports.push_back(pSupport);
    }

    return length;
}
size_t                  WDSupportMgr::write(const char* fileName)
{
    JsonDoc doc;
    doc.SetArray();

    //序列化支吊架模板
    {
        for (int i = 0; i < _supports.size(); ++i)
        {
            this->writeSupport(doc, _supports.at(i)->toPtr<WDSupport>());
        }
    }

    StringBuffer buffer;
    rapidjson::Writer<StringBuffer> writer(buffer);
    doc.Accept(writer);
    std::string outString = buffer.GetString();

    FILE* fp = fopen(fileName, "w");
    if (fp == nullptr)
        return 0;
    fwrite(outString.data(), 1, outString.size(), fp);
    fclose(fp);
    fp = nullptr;

    return outString.size();
}

/**
 * @brief 收集节点引用的名字
 * @param obj json对象
 * @param result key：节点id；value：引用名字
 * @param names 顺带记录引用名字的集合，避免二次遍历收集
*/
void CollectNodeRefName(const JsonValue& obj, std::map<std::string, std::string>& result, std::set<std::string>& names)
{
    if (!obj.IsObject())
        return ;

    if (!obj.HasMember("Id"))
        return ;

    if (obj.HasMember("Spref_refName"))
    {
        result[obj["Id"].GetString()] = obj["Spref_refName"].GetString();
        names.insert(obj["Spref_refName"].GetString());
    }

    if (!obj.HasMember("children"))
        return ;
    if (!obj["children"].IsArray())
        return ;

    for (auto& childObj : obj["children"].GetArray())
    {
        CollectNodeRefName(childObj, result, names);
    }
}
/**
 * @brief 获取引用等级名称和引用等级节点的map
 * @param pRoot 根节点
 * @param names 名称列表
*/
std::map<std::string, WDNode::SharedPtr> RefNodeByName(WDNode::SharedPtr pRoot, const std::set<std::string>& names)
{
    std::map<std::string, WDNode::SharedPtr> result;

    if (pRoot == nullptr)
        return result;

    WDNode::RecursionHelpterR(*pRoot, [&result](const std::set<std::string>& names, WDNode& node)
        {
            if (names.find(node.name()) == names.end())
                return false;
            result[node.name()] = WDNode::ToShared(&node);
            return false;
        }, names);

    return result;
}
/**
 * @brief 根据名称刷新等级引用
 * @param node 节点
 * @param mapIdName 节点id和引用等级名称的map
 * @param mapNameNode 引用等级名称和引用等级节点的map
*/
void ReflushByRefName(WDNode& node
    , const std::map<std::string, std::string>& mapIdName
    , const std::map<std::string, WDNode::SharedPtr>& mapNameNode)
{
    if (mapIdName.empty() || mapNameNode.empty())
        return ;
    
    WDNode::RecursionHelpterR(node, [](const std::map<std::string, std::string>& mapIdName
        , const std::map<std::string, WDNode::SharedPtr>& mapNameNode
        , WDNode& tNode)
        {
            auto fItr0 = mapIdName.find(tNode.uuid().toString());
            if (fItr0 == mapIdName.end())
                return false;
            auto fItr1 = mapNameNode.find(fItr0->second);
            if (fItr1 == mapNameNode.end())
                return false;

            // 获取等级引用属性
            auto pAttrSpRef = tNode.getAttribute("Spref");
            if (!pAttrSpRef.valid())
                return false;
            auto pValueSpRef = pAttrSpRef.data<WDBMNodeRef>();
            if (pValueSpRef == nullptr)
                return false;
            if (pValueSpRef->refNode() == nullptr)
            {
                tNode.setAttribute("Spref", WDBMAttrValue(WDBMNodeRef(fItr1->second)));
            }

            return false;
        }
    , mapIdName, mapNameNode);
}
WDSupport::SharedPtr    WDSupportMgr::readSupport(const JsonValue& supportObj)
{
    if (!supportObj.IsObject() || supportObj.ObjectEmpty())
        return nullptr;

    WDSupport::SharedPtr pSupport = nullptr;
    // 读取支吊架节点
    if (supportObj.HasMember("Equi"))
    {
        auto pBMBase = _core.getBMBase("Design");
        if (pBMBase == nullptr)
            return nullptr;
        WD::WDBMNodeSerialize serial(*pBMBase);
        JsonDoc docEqui;
        // 将equi包装成数组
        JsonDoc docArray;
        JsonValue& arrayObj = docArray.SetArray();
        JsonValue tempObject(rapidjson::kObjectType);
        tempObject.CopyFrom(supportObj["Equi"], docArray.GetAllocator());
        arrayObj.PushBack(tempObject, docArray.GetAllocator());
        auto res = serial.loadFromJson(docEqui, arrayObj);
        // 支吊架节点中所有节点id对应用引用等级名称的map
        std::map<std::string, std::string>  mapRefName;
        std::set<std::string>               names;
        CollectNodeRefName(supportObj["Equi"], mapRefName, names);
        // 遍历元件库根节点，获取等级引用名称和节点的映射表
        auto mapRefNodeByName = RefNodeByName(_core.getBMCatalog().root(), names);
        if (!res.empty())
        {
            auto pEquiNode = res.front();
            if (pEquiNode != nullptr)
            {
                // 根据名称刷新等级引用
                ReflushByRefName(*pEquiNode, mapRefName, mapRefNodeByName);
                pBMBase->updateRefs(*pEquiNode);
                pEquiNode->triggerUpdate();
                pSupport = WDSupport::MakeShared(_core, pEquiNode);
            }
        }
    }
    // 写入支吊架属性
    pSupport->read(supportObj);
    return pSupport;
}
void                    WDSupportMgr::writeSupport(JsonDoc& doc, WDSupport::SharedPtr pSupport)
{
    if (pSupport == nullptr)
        return;

    JsonValue supportObj(rapidjson::kObjectType);
    // 写入支吊架节点
    JsonDoc docEqui;
    JsonValue& equiArray = docEqui.SetArray();
    auto pBMBase = _core.getBMBase("Design");
    if (pBMBase == nullptr)
        return ;
    WD::WDBMNodeSerialize serial(*pBMBase);
    serial.saveToJson({pSupport->node()}, docEqui, equiArray);
    if (!equiArray.Empty())
    {
        JsonValue tempObj(rapidjson::kObjectType);
        tempObj.CopyFrom(equiArray[0], doc.GetAllocator());
        // TODO: equiArray[0].Move()是否深拷贝待验证
        supportObj.AddMember("Equi", tempObj, doc.GetAllocator());
    }
    {
        StringBuffer buffer;
        rapidjson::Writer<StringBuffer> writer(buffer);
        supportObj.Accept(writer);
        std::string testStr = buffer.GetString();
        if (testStr.empty()){}
    }
    // 写入支吊架属性
    pSupport->write(doc, supportObj);
    doc.PushBack(supportObj, doc.GetAllocator());
}

WD_NAMESPACE_END