#include "WDSupport.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/businessModule/catalog/WDBMCatalog.h"

WD_NAMESPACE_BEGIN

// 支吊架头尾坐标参数因子名称
static constexpr const char* Supp_Hpos_X_Name = "HPos.x";
static constexpr const char* Supp_Hpos_Y_Name = "HPos.y";
static constexpr const char* Supp_Hpos_Z_Name = "HPos.z";
static constexpr const char* Supp_Tpos_X_Name = "TPos.x";
static constexpr const char* Supp_Tpos_Y_Name = "TPos.y";
static constexpr const char* Supp_Tpos_Z_Name = "TPos.z";
static constexpr const char* Supp_Height_Name = "Height";

SupportParamType SupportParamTypeFromName(const char* name)
{
    if (_stricmp(name, "Shape") == 0)       return SupportParamType::SP_Shape;
    else if (_stricmp(name, "TRS") == 0)    return SupportParamType::SP_TRS;
      
    return SupportParamType::SP_Unknown;
}

    
ParamFactor::ParamFactor(std::string_view name, const std::string& desc, const std::string& value)
    : _name(name)
    , _description(desc)
    , _value(value)
{}
ParamFactor::~ParamFactor()
{}

WDSupportParam::WDSupportParam(WD::WDCore& core, SupportParamType type)
    : _core(core), _type(type)
{}
WDSupportParam::WDSupportParam(WD::WDCore& core, SupportParamType type, WDNode& ref)
    : _core(core)
    , _type(type)
    , _pRef(WDNode::ToShared(&ref))
{
    _refNodeId = ref.uuid();
}
WDSupportParam::~WDSupportParam()
{
}

void                WDSupportParam::update(WDSupport& support)
{
    // 更新pRef
    auto pEqui = support.node();
    if (pEqui != nullptr)
    {
        WDNode::RecursionHelpterR(*pEqui.get(), [this](const WDUuid& refNodeId, WDNode& node)
            {
                if (node.uuid() != refNodeId)
                    return false;
                this->_pRef = WDNode::ToShared(&node);
                return true;

            }, _refNodeId);
    }
}

void                WDSupportParam::copy(const WDObject* pSrcObject)
{
    const WDSupportParam* pSrc = dynamic_cast<const WDSupportParam*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDObject::copy(pSrcObject);

    this->_pRef      =   pSrc->_pRef;
}
WDObject::SharedPtr WDSupportParam::clone() const
{
    auto p = WDSupportParam::MakeShared(_core, SupportParamType::SP_Unknown);
    p->copy(this);
    return p;
}
void                WDSupportParam::write(JsonDoc& doc, JsonValue& object) const
{
    auto& allocator = doc.GetAllocator();
    // 参数因子类型
    object.AddMember("Type", JsonValue(SupportParamTypeToName(_type), allocator), allocator);
    // 引用节点GUID
    object.AddMember("RefNodeId", JsonValue(_refNodeId.toString().c_str(), allocator), allocator);
}
void                WDSupportParam::read(const JsonValue& object)
{
    // 引用节点GUID
    if (object.HasMember("RefNodeId"))
    {
        auto tempStr = object["RefNodeId"].GetString();
        _refNodeId = FromString<WD::WDUuid>(tempStr);
    }
}

static CAttributeGet CAttrGet(WD::WDCore& core, const ParamFactors& factors)
{
    std::map<std::string, std::string> vars;
    for (auto& each : factors)
    {
        if (vars.find(each.name()) != vars.end())
        {
            assert(false && "重复的参数名称!");
            continue;
        }
        vars.emplace(each.name(), each.value());
    }
    auto func = [vars](const std::string & name, std::any & outValue, const std::optional<int>& index) -> bool
        {
            WDUnused(index);
            auto itr = vars.find(name);
            if (itr == vars.end())
                return false;

            outValue = itr->second;
            return true;
        };
    return core.getBMCatalog().modelBuilder().cAttributeGet(func);
}

std::optional<double>  WDSupportParam::parseExpression(const ParamFactors& factors, const std::string& expression)
{
    auto aGet = CAttrGet(_core, factors);
    // 解析几何体参数表达式
    bool    bOk = false;
    auto    res = aGet.execExpression(expression).convertToDouble(&bOk);
    if (bOk)
        return res;
    return std::optional<double>();
}


WDSupportShapeParam::WDSupportShapeParam(WD::WDCore& core)
    : WDSupportParam(core, SupportParamType::SP_Shape)
{}
WDSupportShapeParam::WDSupportShapeParam(WD::WDCore& core, WDNode& node, std::string_view attrName, const std::string& expression)
    : WDSupportParam(core, SupportParamType::SP_Shape, node)
    , _attrName(attrName)
    , _expression(expression)
{
}
WDSupportShapeParam::~WDSupportShapeParam()
{
}

bool        WDSupportShapeParam::CheckNodeIndex(WDNode& node, std::string_view attrName)
{
    auto pTypeDesc = node.getTypeDesc();
    if (pTypeDesc == nullptr)
        return false;

    // 临时代码，待新增参数类型后删除
    std::string temp = std::string(attrName);
    auto strs = StringSplit(temp, ".");
    if (strs.size() == 2)
    {
        temp = strs.front();
    }

    return pTypeDesc->contains(temp);
}

bool        WDSupportShapeParam::apply(const ParamFactors& factors, const DVec3& hPos, const DVec3& tPos)
{
    WDUnused(hPos);
    WDUnused(tPos);
    auto pRefNode = this->pRef();
    if (pRefNode == nullptr)
    {
        // !TODO: 提示引用节点为空
        return false;
    }
    ParamFactors vars = factors;
    // 添加支吊架高度常量
    vars.emplace_back(ParamFactor(Supp_Height_Name, "", WD::ToString(DVec3::Distance(hPos, tPos))));
    auto aGet = CAttrGet(_core, vars);
    // 解析几何体参数表达式
    bool bOk = false;
    auto res = aGet.execExpression(_expression).convertToDouble(&bOk);
    if (!bOk)
        return false;

    // 更新引用节点的几何体参数
    pRefNode->setAttribute(_attrName, WD::WDBMAttrValue(res));
    pRefNode->triggerUpdate();
    return true;
}

void                WDSupportShapeParam::copy(const WDObject* pSrcObject)
{
    const WDSupportShapeParam* pSrc = dynamic_cast<const WDSupportShapeParam*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDSupportParam::copy(pSrcObject);

    this->_attrName     =   pSrc->_attrName;
    this->_expression   =   pSrc->_expression;
}
WDObject::SharedPtr WDSupportShapeParam::clone() const
{
    auto p = WDSupportShapeParam::MakeShared(_core);
    p->copy(this);
    return p;
}
void                WDSupportShapeParam::toDisplayProperties(WDPropertyGroup& pty)
{
    // 形状参数表达式
    pty.addPropertyString(WDTs("WDSupportParam", "Expression"), this->expression())
        ->setFunctionOwningObjectValueSet(std::bind(&WDSupportShapeParam::setExpression, this, std::placeholders::_1));
}
void                WDSupportShapeParam::write(JsonDoc& doc, JsonValue& object) const
{
    WDSupportParam::write(doc, object);
    auto& allocator = doc.GetAllocator();
    object.AddMember("AttrName", JsonValue(_attrName.c_str(), allocator), allocator);
    object.AddMember("Expression", JsonValue(_expression.c_str(), allocator), allocator);
}
void                WDSupportShapeParam::read(const JsonValue& object)
{
    WDSupportParam::read(object);
    if (object.HasMember("AttrName"))
    {
        _attrName = object["AttrName"].GetString();
    }
    if (object.HasMember("Expression"))
    {
        _expression = object["Expression"].GetString();
    }
}


WDSupportTRSParam::WDSupportTRSParam(WD::WDCore& core)
    : WDSupportParam(core, SupportParamType::SP_TRS)
{}
WDSupportTRSParam::WDSupportTRSParam(WD::WDCore& core
    , WDNode& node
    , const std::string& posExpressionX
    , const std::string& posExpressionY
    , const std::string& posExpressionZ
    , const std::string& dirExpression)
    : WDSupportParam(core, SupportParamType::SP_TRS, node)
    , _posExpressionX(posExpressionX)
    , _posExpressionY(posExpressionY)
    , _posExpressionZ(posExpressionZ)
    , _dirExpression(dirExpression)
{
}
WDSupportTRSParam::~WDSupportTRSParam()
{
}

bool                WDSupportTRSParam::apply(const ParamFactors& factors, const DVec3& hPos, const DVec3& tPos)
{
    auto pRefNode = this->pRef();
    if (pRefNode == nullptr)
    {
        // !TODO: 提示引用节点为空
        return false;
    }

    ParamFactors vars = factors;
    // 添加支吊架高度常量
    vars.emplace_back(ParamFactor(Supp_Height_Name, "", WD::ToString(DVec3::Distance(hPos, tPos))));
    auto aGet = CAttrGet(_core, vars);
    // 获取引用节点更新前位置和朝向
    Vec3    gPos    =   pRefNode->globalTranslation();
    // 
    std::array<double, 3> posVal = { gPos.x, gPos.y, gPos.z };
    std::array<std::string, 3> posExprs = { _posExpressionX, _posExpressionY, _posExpressionZ };

    for (int i = 0; i < posVal.size(); ++i)
    {
        if (i >= posExprs.size())
        {
            assert(false);
            break;
        }
        bool bOk = false;
        auto val = aGet.execExpression(posExprs[i]).convertToDouble(&bOk);
        if (bOk)
            posVal[i] = val;
    }
    //!TODO: 解析朝向
    
    // 更新到引用节点
    pRefNode->setAttribute("Position WRT World", WD::DVec3(posVal[0], posVal[1], posVal[2]));
    // TODO: 更新朝向
    pRefNode->update();
    return true;
}

void                WDSupportTRSParam::copy(const WDObject* pSrcObject)
{
    const WDSupportTRSParam* pSrc = dynamic_cast<const WDSupportTRSParam*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDSupportParam::copy(pSrcObject);

    this->_posExpressionX   =   pSrc->_posExpressionX;
    this->_posExpressionY   =   pSrc->_posExpressionY;
    this->_posExpressionZ   =   pSrc->_posExpressionZ;
    this->_dirExpression    =   pSrc->_dirExpression;
}
WDObject::SharedPtr WDSupportTRSParam::clone() const
{
    auto p = WDSupportTRSParam::MakeShared(_core);
    p->copy(this);
    return p;
}
void                WDSupportTRSParam::toDisplayProperties(WDPropertyGroup& pty)
{
    WDUnused(pty);
#if 0
    // 位置表达式
    pty.addPropertyString(WDTs("WDSupportParam", "PosExpression"), this->posExpression())
        ->setFunctionOwningObjectValueSet(std::bind(&WDSupportTRSParam::setPosExpression, this, std::placeholders::_1));
    // 朝向表达式
    pty.addPropertyString(WDTs("WDSupportParam", "DirExpression"), this->dirExpression())
        ->setFunctionOwningObjectValueSet(std::bind(&WDSupportTRSParam::setDirExpression, this, std::placeholders::_1));
#endif
}
void                WDSupportTRSParam::write(JsonDoc& doc, JsonValue& object) const
{
    WDSupportParam::write(doc, object);
    auto& allocator = doc.GetAllocator();
    object.AddMember("PosExpressionX", JsonValue(_posExpressionX.c_str(), allocator), allocator);
    object.AddMember("PosExpressionY", JsonValue(_posExpressionY.c_str(), allocator), allocator);
    object.AddMember("PosExpressionZ", JsonValue(_posExpressionZ.c_str(), allocator), allocator);
    
    object.AddMember("DirExpression", JsonValue(_dirExpression.c_str(), allocator), allocator);
}
void                WDSupportTRSParam::read(const JsonValue& object)
{
    WDSupportParam::read(object);
    if (object.HasMember("PosExpressionX"))
    {
        _posExpressionX = object["PosExpressionX"].GetString();
    }
    if (object.HasMember("PosExpressionY"))
    {
        _posExpressionY = object["PosExpressionY"].GetString();
    }
    if (object.HasMember("PosExpressionZ"))
    {
        _posExpressionZ = object["PosExpressionZ"].GetString();
    }
    if (object.HasMember("DirExpression"))
    {
        _dirExpression = object["DirExpression"].GetString();
    }
}

std::string WDSupport::s_LegendPath = Core().dataDirPath() + std::string("modelLibrary/support/icon/");

WDSupport::WDSupport(WD::WDCore& core) : _core(core)
{
    _pNode = WDNode::MakeShared();
}
WDSupport::WDSupport(WD::WDCore& core, WDNode::SharedPtr pNode) : _core(core)
{
    _pNode = pNode;
    if (_pNode == nullptr)
    {
        // !TODO: 提示支吊架节点参数为空
        return ;
    }
    this->setName(_pNode->name());
    if (_pNode->isType("HANG"))
    {
        // 初始化支吊架头尾坐标参数因子
        const auto hPos = _pNode->getAttribute("Hposition WRT World").toDVec3();
        const auto tPos = _pNode->getAttribute("Tposition WRT World").toDVec3();
        // 递归更新所有节点(将头坐标移动至原点)
        for (size_t i = 0; i < _pNode->childCount(); ++i)
        {
            auto pChild = _pNode->childAt(i);
            if (pChild == nullptr)
                continue;
            pChild->move(-hPos);
            pChild->update();
        }
        this->setHPos(DVec3::Zero());
        this->setTPos(tPos - hPos);
        // 将头尾坐标设置回去
        _pNode->setAttribute("Hposition WRT World", WD::WDBMAttrValue(DVec3::Zero()));
        _pNode->setAttribute("Tposition WRT World", WD::WDBMAttrValue(tPos - hPos));
    }
    else if (_pNode->isType("STRU"))
    {
        this->setHPos(DVec3::Zero());
        this->setTPos(DVec3::Zero());
        // 节点移动至原点
        _pNode->setAttribute("Position WRT World", DVec3::Zero());
        _pNode->update();
    }
}
WDSupport::~WDSupport()
{
    _pNode = nullptr;
}

DVec3                       WDSupport::hPos() const
{
    DVec3 pos;
    for (auto& factor : _factors)
    {
        if (factor.name() == Supp_Hpos_X_Name)
        {
            pos.x = FromString<double>(factor.value());
        }
        else if (factor.name() == Supp_Hpos_Y_Name)
        {
            pos.y = FromString<double>(factor.value());
        }
        else if (factor.name() == Supp_Hpos_Z_Name)
        {
            pos.z = FromString<double>(factor.value());
        }
    }
    return pos;
}
void                        WDSupport::setHPos(const DVec3& pos)
{
    this->setFactor(Supp_Hpos_X_Name, WD::WDTs("WDSupport", Supp_Hpos_X_Name), ToString(pos.x));
    this->setFactor(Supp_Hpos_Y_Name, WD::WDTs("WDSupport", Supp_Hpos_Y_Name), ToString(pos.y));
    this->setFactor(Supp_Hpos_Z_Name, WD::WDTs("WDSupport", Supp_Hpos_Z_Name), ToString(pos.z));
}
DVec3                       WDSupport::tPos() const
{
    DVec3 pos;
    for (auto& factor : _factors)
    {
        if (factor.name() == Supp_Tpos_X_Name)
        {
            pos.x = FromString<double>(factor.value());
        }
        else if (factor.name() == Supp_Tpos_Y_Name)
        {
            pos.y = FromString<double>(factor.value());
        }
        else if (factor.name() == Supp_Tpos_Z_Name)
        {
            pos.z = FromString<double>(factor.value());
        }
    }
    return pos;
}
void                        WDSupport::setTPos(const DVec3& pos)
{
    this->setFactor(Supp_Tpos_X_Name, WD::WDTs("WDSupport", Supp_Tpos_X_Name), ToString(pos.x));
    this->setFactor(Supp_Tpos_Y_Name, WD::WDTs("WDSupport", Supp_Tpos_Y_Name), ToString(pos.y));
    this->setFactor(Supp_Tpos_Z_Name, WD::WDTs("WDSupport", Supp_Tpos_Z_Name), ToString(pos.z));
}
std::string                 WDSupport::hBore() const
{
    std::string hBore;
    if (_pNode == nullptr)
        return hBore;
    auto pAttrHBore = _pNode->getAttribute("Hbore");
    if (!pAttrHBore.valid())
        return hBore;
    auto pValue = pAttrHBore.data<std::string>();
    if (pValue == nullptr)
        return hBore;
    return *pValue;
}

// 校验child节点是否为parent节点的子孙节点
bool                        isDescendant(WDNode& parent, WDNode& descendant)
{
    return WDNode::RecursionHelpterR(parent, [](WDNode& descendant, WDNode& node)
        {
            return (&node == &descendant);
        }, descendant);
}
WDSupportParam::SharedPtr    WDSupport::defineShapeParam(WDNode& descendant
        , std::string_view attrName
        , const std::string& expression)
{
    // 校验descendant是否合理
    if (!isDescendant(*_pNode, descendant))
    {
        // !TODO: 提示该节点不是支吊架节点的子孙节点
        return nullptr;
    }

    // 校验参数名称是否合理
    if (!WDSupportShapeParam::CheckNodeIndex(descendant, attrName))
    {
        // !TODO: 提示参数名称不合理
        return nullptr;
    }
    
    // 校验参数是否重定义
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;
        auto tParam = dynamic_cast<WDSupportShapeParam*>(param.get());
        if (tParam == nullptr)
            continue;
        if (tParam->pRef().get() == &descendant && tParam->attrName() == attrName)
        {
            // !TODO: 提示descendant节点指定名称参数已定义
            return nullptr;
        }
    }
    
    auto pNewParam = WDSupportShapeParam::MakeShared(_core, descendant, attrName, expression);
    pNewParam->setName(std::string(attrName));
    _params.push_back(pNewParam);
    return pNewParam;
}
WDSupportParam::SharedPtr    WDSupport::defineTRSParam(WDNode& descendant
        , const std::string& name
        , const std::string& posExpressionX
        , const std::string& posExpressionY
        , const std::string& posExpressionZ
        , const std::string& dirExpression)
{
    // 校验descendant是否合理
    if (!isDescendant(*_pNode, descendant))
    {
        // !TODO: 提示该节点不是支吊架节点的子孙节点
        return nullptr;
    }
    
    // 校验参数是否重定义
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;
        if (param->name() == name)
        {
            // !TODO: 提示已有同名参数
            return nullptr;
        }
        if (param->pRef().get() == &descendant)
        {
            // !TODO: 提示descendant节点指定index参数已定义
            return nullptr;
        }
    }
    
    auto pNewParam = WDSupportTRSParam::MakeShared(_core, descendant, posExpressionX, posExpressionY, posExpressionZ, dirExpression);
    pNewParam->setName(name);
    _params.push_back(pNewParam);
    return pNewParam;
}
bool                        WDSupport::undefineParam(WDSupportParam::SharedPtr param)
{
    auto pItr = std::find(_params.begin(), _params.end(), param);
    if (pItr == _params.end())
        return false;
    _params.erase(pItr);
    return true;
}
size_t                      WDSupport::undefineRefNodeParam(WDNode& node)
{
    size_t          count       =   _params.size();
    WDSupportParams afterParams;
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;
        if (param->pRef().get() == &node)
            continue;
        afterParams.push_back(param);
    }
    _params = afterParams;

    return count - _params.size();
}
WDSupportParam::SharedPtr    WDSupport::queryShapeParam(WDNode& descendant, std::string_view attrName, const std::string& extr)
{
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;

        if (param->pRef().get() != &descendant)
            continue;

        // 排除非shape参数
        auto pShape = dynamic_cast<WDSupportShapeParam*>(param.get());
        if (pShape == nullptr)
            continue;

        if (pShape->attrName() == std::string(attrName) + extr)
            return param;
    }

    return nullptr;
}
WDSupportParam::SharedPtr    WDSupport::queryTRSParam(WDNode& descendant)
{
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;

        if (param->pRef().get() != &descendant)
            continue;

        // 排除非TRS参数
        auto pTRS = dynamic_cast<WDSupportTRSParam*>(param.get());
        if (pTRS == nullptr)
            continue;

        return param;
    }

    return nullptr;
}

bool        WDSupport::setFactor(std::string_view name, const std::string& description, const std::string& value)
{
    // 校验是否有同名参数
    for (auto& factor : _factors)
    {
        if (factor.name() == name)
        {
            factor.setDescription(description);
            factor.setValue(value);
            return false;
        }
    }
    _factors.emplace_back(name, description, value);
    return true;
}
std::optional<ParamFactor>          WDSupport::queryFactor(std::string_view name)
{
    for (auto& factor : _factors)
    {
        if (factor.name() == name)
        {
            return factor;
        }
    }
    return std::nullopt;
}
bool                    WDSupport::modifyFactor(std::string_view name, std::string_view value)
{
    for (auto& factor : _factors)
    {
        if (factor.name() == name)
        {
            factor.setValue(value);
            return true;
        }
    }

    return false;
}
bool                        WDSupport::removeFactor(std::string_view name)
{
    for (auto fItr = _factors.begin(); fItr != _factors.end(); fItr++)
    {
        if (fItr->name() == name)
        {
            fItr = _factors.erase(fItr);
            return true;
        }
    }
    return false;
}

void                        WDSupport::apply()
{
    // 临时处理，后续需新增参数类型
    // 分离需组合的参数，目前仅支持DVec3
    WDSupportParams commons = _params;
    std::map<WDNode::SharedPtr, std::map<std::string, std::array<WDSupportShapeParam::SharedPtr, 3>>> mapRefVec3Params;
    for (auto& param : _params)
    {
        if (param == nullptr)
            continue;
        auto pTParam = param->toPtr<WDSupportShapeParam>();
        if (pTParam == nullptr)
            continue;
        auto pRefNode = pTParam->pRef();
        auto& name = pTParam->attrName();
        auto strs = StringSplit(name, ".");
        if (strs.size() == 2)
        {
            auto& attrName = strs.front();
            auto& axisName = strs.back();
            if (axisName == "x")
                mapRefVec3Params[pRefNode][attrName][0] = pTParam;
            else if (axisName == "y")
                mapRefVec3Params[pRefNode][attrName][1] = pTParam;
            else if (axisName == "z")
                mapRefVec3Params[pRefNode][attrName][2] = pTParam;
        }
        else
        {
            commons.push_back(param);
        }
    }
    // 普通参数
    for (auto& param : commons)
    {
        param->apply(_factors, this->hPos(), this->tPos());
    }
    // 组合参数
    ParamFactors vars = _factors;
    // 添加支吊架高度常量
    vars.emplace_back(ParamFactor(Supp_Height_Name, "", WD::ToString(DVec3::Distance(this->hPos(), this->tPos()))));
    auto aGet = CAttrGet(_core, vars);
    for (auto& refVec3Params : mapRefVec3Params)
    {
        WDNode::SharedPtr pRefNode = refVec3Params.first;
        if (pRefNode == nullptr)
            continue;
        for (auto& vec3Params : refVec3Params.second)
        {
            auto& xParam = vec3Params.second[0];
            auto& yParam = vec3Params.second[1];
            auto& zParam = vec3Params.second[2];
    
            // 解析几何体参数表达式
            auto pVecAttr = pRefNode->getAttribute(vec3Params.first);
            if (!pVecAttr.valid())
                continue;
            auto pValue = pVecAttr.data<DVec3>();
            if (pValue == nullptr)
                continue;
            DVec3 vec = *pValue;
            StringVector exprs;
            exprs.reserve(3);
            if (xParam != nullptr)
                exprs.emplace_back(xParam->expression());
            else
                exprs.emplace_back("");

            if (yParam != nullptr)
                exprs.emplace_back(yParam->expression());
            else
                exprs.emplace_back("");

            if (zParam != nullptr)
                exprs.emplace_back(zParam->expression());
            else
                exprs.emplace_back("");

            std::array<double, 3> posVal = {vec.x, vec.y, vec.z};

            for (int i = 0; i < posVal.size(); ++i)
            {
                if (i >= exprs.size())
                {
                    assert(false);
                    break;
                }
                bool bOk = false;
                auto val = aGet.execExpression(exprs[i]).convertToDouble(&bOk);
                if (bOk)
                    posVal[i] = val;
            }
            // 更新引用节点的几何体参数
            pRefNode->setAttribute(vec3Params.first, WD::WDBMAttrValue(DVec3(posVal[0], posVal[1], posVal[2])));
            pRefNode->triggerUpdate();
        }
    }
}

void                WDSupport::copy(const WDObject* pSrcObject)
{
    const WDSupport* pSrc = dynamic_cast<const WDSupport*>(pSrcObject);
    if (pSrc == nullptr)
        return;
    WDObject::copy(pSrcObject);
    
    this->_spec     =   pSrc->_spec;
    this->_gType    =   pSrc->_gType;
    this->_mType    =   pSrc->_mType;
    this->_legend   =   pSrc->_legend;
    if (this->_pNode != nullptr)
    {
        this->_pNode = Core().getBMDesign().clone(pSrc->_pNode);
        if (this->_pNode != nullptr)
        {
            this->_pNode->update();
        }
    }
    this->_params.clear();
    for (auto& param : pSrc->_params)
    {
        this->_params.push_back(WDSupportParam::SharedCast(param->clone()));
    }
    MapParam mapParam;
    for (auto& param : this->_params)
    {
        if (param == nullptr)
            continue;

        auto pRefNode = param->pRef();
        if (pRefNode == nullptr)
            continue;

        auto pItr = mapParam.find(pRefNode.get());
        if (pItr == mapParam.end()) 
        {
            mapParam[pRefNode.get()] = {param};
        }
        else
        {
            mapParam[pRefNode.get()].push_back(param);
        }
    }
    this->rectifyParam(pSrc->_pNode.get(), this->_pNode.get(), mapParam);
    this->_factors = pSrc->_factors;
}
WDObject::SharedPtr WDSupport::clone() const
{
    auto p = WDSupport::MakeShared(_core);
    p->copy(this);
    return p;
}
void                WDSupport::write(JsonDoc& doc, JsonValue& object) const
{
    // 支吊架节点在序列化插件中进行序列化
    auto& allocator = doc.GetAllocator();
    object.AddMember("Name", JsonValue(name().c_str(), allocator).Move(), allocator);
    object.AddMember("Spec", JsonValue(_spec.c_str(), allocator).Move(), allocator);
    object.AddMember("GType", JsonValue(_gType.c_str(), allocator).Move(), allocator);
    object.AddMember("MType", JsonValue(_mType.c_str(), allocator).Move(), allocator);
    object.AddMember("Legend", JsonValue(_legend.c_str(), allocator).Move(), allocator);
    // 参数列表
    JsonValue arrayParams(rapidjson::kArrayType);
    for (size_t i = 0; i < _params.size(); ++i)
    {
        JsonValue paramObj(rapidjson::kObjectType);
        _params[i]->write(doc, paramObj);
        arrayParams.PushBack(paramObj, allocator);
    }
    object.AddMember("Params", arrayParams, allocator);
    // 参数因子列表
    JsonValue arrayFactors(rapidjson::kArrayType);
    for (size_t i = 0; i < _factors.size(); ++i)
    {
        JsonValue factorObj(rapidjson::kObjectType);
        // 名称
        factorObj.AddMember("Name", JsonValue(_factors[i].name().c_str(), allocator), allocator);
        // 参数因子描述
        factorObj.AddMember("Description", JsonValue(_factors[i].description().c_str(), allocator), allocator);
        // 参数值
        factorObj.AddMember("Value", JsonValue(_factors[i].value().c_str(), allocator), doc.GetAllocator());
        arrayFactors.PushBack(factorObj, allocator);
    }
    object.AddMember("Factors", arrayFactors, allocator);
}
void                WDSupport::read(const JsonValue& object)
{
    if (object.HasMember("Name"))
    {
        this->setName(object["Name"].GetString());
    }
    if (object.HasMember("Spec"))
    {
        _spec = object["Spec"].GetString();
    }
    if (object.HasMember("GType"))
    {
        _gType = object["GType"].GetString();
    }
    if (object.HasMember("MType"))
    {
        _mType = object["MType"].GetString();
    }
    if (object.HasMember("Legend"))
    {
        _legend = object["Legend"].GetString();
    }
    // 参数列表
    if (object.HasMember("Params"))
    {
        auto& valueParams = object["Params"];
        assert(valueParams.IsArray());
        for (auto& valueParam : valueParams.GetArray())
        {
            if (!valueParam.HasMember("Type"))
                continue;
            auto type = SupportParamTypeFromName(valueParam["Type"].GetString());
            switch (type)
            {
            case WD::SP_Shape:
                {
                    _params.push_back(WDSupportShapeParam::MakeShared(_core));
                    _params.back()->read(valueParam);
                    _params.back()->update(*this);
                }
                break;
            case WD::SP_TRS:
                {
                    _params.push_back(WDSupportTRSParam::MakeShared(_core));
                    _params.back()->read(valueParam);
                    _params.back()->update(*this);
                }
                break;
            default:
                break;
            }

        }
    }
    // 参数因子列表
    if (object.HasMember("Factors"))
    {
        auto& valueParams = object["Factors"];
        if (valueParams.IsArray())
        {
            for (auto& valueParam : valueParams.GetArray())
            {
                std::string name;
                std::string desc;
                std::string value;
                if (valueParam.HasMember("Name"))
                {
                    name = valueParam["Name"].GetString();
                }
                if (valueParam.HasMember("Description"))
                {
                    desc = valueParam["Description"].GetString();
                }
                if (valueParam.HasMember("Value"))
                {
                    value = valueParam["Value"].GetString();
                }
                this->setFactor(name, desc, value);
            }
        }
    }
}
void                WDSupport::rectifyParam(WDNode* parent1, WDNode* parent2, const MapParam& mapParams)
{
    if (parent1 == nullptr || parent2 == nullptr)
        return ;

    auto pItr = mapParams.find(parent1);
    if (pItr != mapParams.end())
    {
        auto& params = pItr->second;
        for (auto& param : params)
        {
            if (param == nullptr)
                continue;

            param->setPRef(WDNode::ToShared(parent2));
        }
        
    }

    for (size_t i = 0; i < parent1->childCount(); ++i)
    {
        auto pChild1 = parent1->childAt(i);
        auto pChild2 = parent2->childAt(i);
        if (pChild1 == nullptr || pChild2 == nullptr)
            continue;

        rectifyParam(pChild1.get(), pChild2.get(), mapParams);
    }
}

WD_NAMESPACE_END