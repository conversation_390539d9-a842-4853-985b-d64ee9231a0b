#pragma once

#include "WDCore.h"
#include "core/node/WDNode.h"
#include <unordered_map>
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "WDRapidjson.h"
#include "../../ui.commonLibrary/ui.commonLib.property/property/WDProperties.h"

WD_NAMESPACE_BEGIN

// 参数类型
enum SupportParamType
{
    SP_Unknown = 0,
    // 形状参数
    SP_Shape,
    // TRS参数
    SP_TRS,
};
constexpr const char*   SupportParamTypeToName(SupportParamType type)
{
    switch (type)
    {
    case SupportParamType::SP_Shape:     return "Shape";
    case SupportParamType::SP_TRS:       return "TRS";
    default:
        break;
    }

    return "Unknown";
}
SupportParamType         SupportParamTypeFromName(const char* name);

// 参数因子
class ParamFactor
{   
public:
    ParamFactor(std::string_view name, const std::string& desc = "", const std::string& value = "");
    virtual ~ParamFactor();

public:
    /*
    * brief 获取名称
    */
    inline const std::string& name() const
    {
        return _name;
    }
    /*
    * brief 设置名称
    * param description 描述
    */
    inline void             setName(std::string_view name)
    {
        _name = name;
    }
    /*
    * brief 获取描述
    */
    inline const std::string& description() const
    {
        return _description;
    }
    /*
    * brief 设置描述
    * param description 描述
    */
    inline void             setDescription(std::string_view description)
    {
        _description = description;
    }
    /*
    * brief 值转换为字符串
    */
    inline const std::string& value() const
    {
        return _value;
    }
    /*
    * brief 字符串转换为值
    */
    inline void             setValue(std::string_view value)
    {
        _value = value;
    }

private:
    // 参数名称
    std::string     _name;
    // 参数描述
    std::string     _description;
    // 参数值
    std::string     _value;
};
using ParamFactors = std::vector<ParamFactor>;

// 支吊架模板参数
class WDSupport;
class WDSupportParam : public WDObject
{
    WD_DECL_OBJECT(WDSupportParam)

public:
    WDSupportParam(WD::WDCore& core, SupportParamType type);
    WDSupportParam(WD::WDCore& core, SupportParamType type, WDNode& ref);
    virtual ~WDSupportParam();

public:
    /*
    * brief 获取参数类型
    */
    inline SupportParamType         type() const
    {
        return _type;
    }
    /**
    * @brief 获取引用节点引用的对象
    */
    inline WDNode::SharedPtr        pRef()
    {
        return _pRef.lock();
    }
    /**
    * @brief 设置引用节点引用的对象
    */
    inline void                     setPRef(WDNode::SharedPtr pRef)
    {
        _pRef.reset();
        _pRef = pRef;
        if (pRef != nullptr)
        {
            _refNodeId = pRef->uuid();
        }
    }

    /**
    * @brief 以指定参数因子列表更新参数引用节点
    * @param factors 参数因子列表
    * @param hPos 支吊架头坐标
    * @return 是否更新成功
    */
    virtual bool                apply(const ParamFactors& factors, const DVec3& hPos, const DVec3& tPos)
    {
        WDUnused3(factors, hPos, tPos);
        return false;
    }
    /*
    * brief 更新支吊架数据
    */
    void                        update(WDSupport& support);

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                write(JsonDoc& doc, JsonValue& object) const;
    virtual void                read(const JsonValue& object);
    virtual void                toDisplayProperties(WDPropertyGroup& pty)
    {
        WDCxtTsBg("WDObject");

        pty.addPropertyString(WDCxtTs("name"), this->name(), true)
            ->setFunctionOwningObjectValueSet(std::bind(&WDObject::setName, this, std::placeholders::_1));

        pty.addPropertyGuid(WDCxtTs("id"), this->uuid(), false);

        WDCxtTsEd();
    }
protected:
    /**
    * @brief 解析表达式
    * @param factors 常量映射数组
    * @param expression 表达式
    */
    std::optional<double>       parseExpression(const ParamFactors& factors, const std::string& expression);

protected:
    WD::WDCore& _core;
private:
    // 引用节点GUID
    WDUuid              _refNodeId;
    // 引用节点
    WDNode::WeakPtr     _pRef;
    // 类型
    SupportParamType    _type;
};
using WDSupportParams   =   std::vector<WDSupportParam::SharedPtr>;
// 形状参数
WD_DECL_CLASS_UUID(WDSupportShapeParam,"C0013284-D888-4AEC-A26B-A31216373460");
class WDSupportShapeParam : public WDSupportParam
{
    WD_DECL_OBJECT(WDSupportShapeParam)

public:
    WDSupportShapeParam(WD::WDCore& core);
    WDSupportShapeParam(WD::WDCore& core
        , WDNode& node
        , std::string_view attrName
        , const std::string& expression = "");
    virtual ~WDSupportShapeParam();

public:
    /**
    * @brief 节点是对应类型的数据节点
    */
    static inline bool  Is(WDSupportParam::SharedPtr param)
    {
        return dynamic_cast<WDSupportShapeParam*>(param.get()) != nullptr;
    }

public:
    /**
    * @brief 校验参数引用节点与参数名称是否合理
    * @param node 参数引用节点
    * @param attrName 参数名称
    */
    static bool     CheckNodeIndex(WDNode& node, std::string_view attrName);

public:
    virtual bool                apply(const ParamFactors& factors, const DVec3& hPos, const DVec3& tPos) override;
    /**
    * @brief 获取在所属节点数据中序号
    */
    inline const std::string&   attrName() const
    {
        return _attrName;
    }
    /**
    * @brief 获取形状参数表达式
    */
    inline const std::string&   expression() const
    {
        return _expression;
    }
    /**
    * @brief 设置形状参数表达式
    */
    inline void                 setExpression(const std::string& expression)
    {
        _expression = expression;
    }

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                toDisplayProperties(WDPropertyGroup& pty) override;
    virtual void                write(JsonDoc& doc, JsonValue& object) const override;
    virtual void                read(const JsonValue& object) override;

private:
    // 参数名称
    std::string _attrName;
    // 形状参数表达式
    std::string _expression;
};
// TRS参数
WD_DECL_CLASS_UUID(WDSupportTRSParam,"054704AA-7D2A-4054-AFE0-12E440163601");
class WDSupportTRSParam : public WDSupportParam
{
    WD_DECL_OBJECT(WDSupportTRSParam)

public:
    WDSupportTRSParam(WD::WDCore& core);
    WDSupportTRSParam(WD::WDCore& core
        , WDNode& node
        , const std::string& posExpressionX = ""
        , const std::string& posExpressionY = ""
        , const std::string& posExpressionZ = ""
        , const std::string& dirExpression = "");
    virtual ~WDSupportTRSParam();

public:
    /**
    * @brief 节点是对应类型的数据节点
    */
    static inline bool                          Is(WDSupportParam::SharedPtr param)
    {
        return dynamic_cast<WDSupportTRSParam*>(param.get()) != nullptr;
    }
    /**
    * @brief 获取节点附带的该类型的数据
    */
    static inline WDSupportTRSParam::SharedPtr   Data(WDSupportParam::SharedPtr param)
    {
        return WDSupportTRSParam::ToShared(dynamic_cast<WDSupportTRSParam*>(param.get()));
    }

public:
    virtual bool    apply(const ParamFactors& factors, const DVec3& hPos, const DVec3& tPos) override;
    /**
    * @brief 获取位置表达式X
    */
    inline const std::string&   posExpressionX() const
    {
        return _posExpressionX;
    }
    /**
    * @brief 设置位置表达式
    */
    inline void                 setPosExpressionX(const std::string& posExpressionX)
    {
        _posExpressionX = posExpressionX;
    }
    /**
    * @brief 获取位置表达式X
    */
    inline const std::string&   posExpressionY() const
    {
        return _posExpressionY;
    }
    /**
    * @brief 设置位置表达式
    */
    inline void                 setPosExpressionY(const std::string& posExpressionY)
    {
        _posExpressionY = posExpressionY;
    }
    /**
    * @brief 获取位置表达式X
    */
    inline const std::string&   posExpressionZ() const
    {
        return _posExpressionZ;
    }
    /**
    * @brief 设置位置表达式
    */
    inline void                 setPosExpressionZ(const std::string& posExpressionZ)
    {
        _posExpressionZ = posExpressionZ;
    }
    /**
    * @brief 获取朝向表达式
    */
    inline const std::string&   dirExpression() const
    {
        return _dirExpression;
    }
    /**
    * @brief 设置朝向表达式
    */
    inline void                 setDirExpression(const std::string& dirExpression)
    {
        _dirExpression = dirExpression;
    }

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    virtual void                toDisplayProperties(WDPropertyGroup& pty) override;
    virtual void                write(JsonDoc& doc, JsonValue& object) const override;
    virtual void                read(const JsonValue& object) override;

private:
    // 位置表达式
    std::string _posExpressionX;
    std::string _posExpressionY;
    std::string _posExpressionZ;
    // 朝向表达式
    std::string _dirExpression;
};


// 支吊架模板
WD_DECL_CLASS_UUID(WDSupport,"7921CCAE-EB69-44A1-80F9-CD6959CC2BBF");
class WDSupport : public WDObject
{
    WD_DECL_OBJECT(WDSupport)

public:
    WDSupport(WD::WDCore& core);
    WDSupport(WD::WDCore& core, WDNode::SharedPtr pNode);
    virtual ~WDSupport();

private:
    using MapParam = std::map<WDNode*, std::vector<WDSupportParam::SharedPtr>>;

public:
    /*
    * brief 获取图例路径
    */
    static inline const std::string&           LegendPath()
    {
        return s_LegendPath;
    }

public:
    /*
    * brief 获取支吊架等级（与元件库中等级无关，仅用于分组）
    */
    inline const std::string&                   spec() const
    {
        return _spec;
    }
    /*
    * brief 设置支吊架等级（与元件库中等级无关，仅用于分组）
    */
    inline const void                           setSpec(std::string_view spec)
    {
        _spec = spec;
    }
    /*
    * brief 获取通用类型
    */
    inline const std::string&                   gType() const
    {
        return _gType;
    }
    /*
    * brief 设置通用类型
    */
    inline const void                           setGType(const std::string& gType)
    {
        _gType = gType;
    }
    /*
    * brief 获取型号
    */
    inline const std::string&                   mType() const
    {
        return _mType;
    }
    /*
    * brief 设置型号
    */
    inline const void                           setMType(const std::string& mType)
    {
        _mType = mType;
    }
    /*
    * brief 获取图例
    */
    inline const std::string&                   legend() const
    {
        return _legend;
    }
    /*
    * brief 设置图例
    */
    inline const void                           setLegend(const std::string& legend)
    {
        _legend = legend;
    }

    /*
    * brief 获取支吊架节点
    */
    inline WDNode::SharedPtr                    node()
    {
        return _pNode;
    }
    
    /**
     * @brief 获取支吊架头坐标
    */
    DVec3                                       hPos() const;
    /**
     * @brief 设置支吊架头坐标
     * @param pos 坐标
    */
    void                                        setHPos(const DVec3& pos);
    /**
     * @brief 获取支吊架尾坐标
    */
    DVec3                                       tPos() const;
    /**
     * @brief 设置支吊架尾坐标
     * @param pos 坐标
    */
    void                                        setTPos(const DVec3& pos);
    std::string                                 hBore() const;

    /*
    * brief 定义形状参数(将 支吊架节点下的子孙节点的形状参数 定义为 支吊架模板的参数)
    * param descendant 子孙节点
    * param attrName 形状参数名称
    * return 定义的参数对象指针
    */
    WDSupportParam::SharedPtr            defineShapeParam(WDNode& descendant
        , std::string_view attrName
        , const std::string& expression = std::string());
    /*
    * brief 定义TRS参数(将 支吊架节点下的子孙节点的TRS信息 定义为 支吊架模板的参数)
    * param descendant 子孙节点
    * param name 参数名称
    * return 定义的参数对象指针
    */
    WDSupportParam::SharedPtr            defineTRSParam(WDNode& descendant
        , const std::string& name = std::string()
        , const std::string& posExpressionX = std::string()
        , const std::string& posExpressionY = std::string()
        , const std::string& posExpressionZ = std::string()
        , const std::string& dirExpression = std::string());
    /*
    * brief 取消定义参数
    * param index 参数在参数列表中的序号
    */
    bool                                undefineParam(WDSupportParam::SharedPtr param);
    /*
    * brief 取消定义引用指定节点的参数
    * param node 节点
    * return 取消定义的参数个数
    */
    size_t                              undefineRefNodeParam(WDNode& node);
    /*
    * brief 取消定义所有参数
    */
    inline bool                         undefineParams()
    {
        _params.clear();
        return true;
    }
    /*
    * brief 获取参数列表
    */
    inline const WDSupportParams&   params() const
    {
        return _params;
    }
    /**
     * @brief 查询模板参数
     * @param descendant 节点
     * @param attrName 属性名称
     * @param extr 补充
    */
    WDSupportParam::SharedPtr            queryShapeParam(WDNode& descendant, std::string_view attrName, const std::string& extr = "");
    WDSupportParam::SharedPtr            queryTRSParam(WDNode& descendant);

    /*
    * brief 设置Double参数因子，如果已经有该名称参数因子则进行修改，没有则进行新增
    * param name 参数因子名称
    * param description 参数因子描述
    * param value 参数因子值
    * return 对已有参数因子设置成功返回true，新增则返回false
    */
    bool                                setFactor(std::string_view name, const std::string& description = "", const std::string& value = "");
    /*
    * brief 查询参数因子
    * param name 参数因子名称
    */
    std::optional<ParamFactor>          queryFactor(std::string_view name);
    /*
    * brief 修改Double参数因子
    * param name 参数因子名称
    * param value 参数因子值
    */
    bool                                modifyFactor(std::string_view name, std::string_view value);
    /*
    * brief 移除支吊架模板参数因子
    */
    bool                                removeFactor(std::string_view name);
    /*
    * brief 清除支吊架模板参数因子
    */
    inline void                     clearFactors()
    {
        _factors.clear();
    }
    /*
    * brief 获取参数因子列表
    */
    inline const ParamFactors& factors() const
    {
        return _factors;
    }

    /*
    * brief 更新支吊架
    * -info 将参数因子的值带入到模板参数中计算更新支吊架节点
    */
    void                            apply();

public:
    virtual void                copy(const WDObject* pSrcObject) override;
    virtual WDObject::SharedPtr clone() const override;
    void                        write(JsonDoc& doc, JsonValue& object) const;
    void                        read(const JsonValue& object);

private:
    /*
    * brief 纠正参数引用
    */
    void                        rectifyParam(WDNode* parent1, WDNode* parent2, const MapParam& mapParams);

private:
    // 图例文件路径
    static std::string  s_LegendPath;

    // 支吊架节点
    WDNode::SharedPtr   _pNode;
    // 支吊架参数因子
    ParamFactors        _factors;
    // 支吊架模板参数
    WDSupportParams     _params;
    // 支吊架等级（与元件库中等级无关，仅用于分组）
    std::string         _spec;
    // 通用类型
    std::string         _gType;
    // 型号
    std::string         _mType;
    // 图例
    std::string         _legend;

    WD::WDCore& _core;
};
using WDSupports = std::vector<WDSupport::SharedPtr>;
WD_NAMESPACE_END