#include    "ui.com.node.fileFunction.h"
#include    <QFileDialog>

#include    "businessModule/WDBMCommon.h"
#include    "extension/WDExtensionMgr.h"
#include    "core/businessModule/catalog/WDBMCatalog.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "GL/WDShareContext.h"
#include    "core/WDCore.h"
#include    "QMutex"
#include    "QWaitCondition"
#include    "globalUiIds/globalUiIds.h"
#include    "core/WDTranslate.h"
#include    "../../wizDesignerApp/UiInterface/IDCStatusBar.h"
#include    "../../wizDesignerApp/UiInterface/ICollaboration.h"
#include    "core/geometry/WDGeometryMgr.h"
#include    "core/common/WDTaskSys.h"
#include    "core/common/WDTask.h"
#include    "core/message/WDMessage.h"
#include    "core/WDObjectCreator.h"
#include    "core/apiDelegate/WDApiDelegate.h"
#include    "core/undoRedo/WDUndoStack.h"
#include    "core/businessModule/WDBMClaimMgr.h"

#if WD_PLATFORM == WD_PLATFORM_WIN32

#include <GL/glew.h>
#include <GL/wglew.h>

#elif WD_PLATFORM == WD_PLATFORM_LINUX || WD_PLATFORM == WD_PLATFORM_ANDRIOD

#include <GL/glew.h>
#include <GL/gl.h>
#include <GL/glu.h>

#endif

Q_DECLARE_METATYPE(WD::WDTask::SharedPtr);

UIComNodeFileFunction::UIComNodeFileFunction(IMainWindow& mainWindow, const UiComponentAttributes& attrs)
    : IUiComponent(mainWindow, attrs)
    , _core(mainWindow.core())
    , _exportWidget(_core, mainWindow.widget())
{
    qRegisterMetaType<WD::WDTask::SharedPtr>();

    QObject::connect(this, SIGNAL(finishLoad(WD::WDTask::SharedPtr)), this, SLOT(slotFinishLoad(WD::WDTask::SharedPtr)));
    QObject::connect(this, SIGNAL(progress(float, QString)), this, SLOT(slotProgress(float, QString)));

    // 设置文件自动保存
    _autoSave           =   false;
    _autoSaveSpacing    =   30 * 60 * 1000;
    _timer              =   new QTimer();
    connect(_timer, &QTimer::timeout, this, &UIComNodeFileFunction::slotRepeatSave);
    auto pCfgAutoSave = _core.cfg().query("auto.save");
    if (pCfgAutoSave != nullptr)
    {
        // 配置值
        auto pValue = pCfgAutoSave->value<bool>();
        if (pValue != nullptr)
        {
            _autoSave = *pValue;
        }
        pCfgAutoSave->bindFunction({this, &UIComNodeFileFunction::onCfgAutoSaveValueChanged});
    }
    auto pCfgAutoSaveSpacing = _core.cfg().query("auto.save.spacing");
    if (pCfgAutoSaveSpacing != nullptr)
    {
        // 配置值
        auto pValue = pCfgAutoSaveSpacing->value<int>();
        if (pValue != nullptr)
        {
            _autoSaveSpacing = (*pValue) * 60 * 1000;
        }
        pCfgAutoSaveSpacing->bindFunction({this, &UIComNodeFileFunction::onCfgAutoSaveSpacingValueChanged});
        pCfgAutoSaveSpacing->setMinimum(5);
        pCfgAutoSaveSpacing->setMaximum(999999);
    }
    _timer->setInterval(_autoSaveSpacing);
    _timer->start();
}

UIComNodeFileFunction::~UIComNodeFileFunction()
{
    if (_timer != nullptr)
    {
        _timer->deleteLater();
        _timer = nullptr;
    }
}

void UIComNodeFileFunction::onNotice(UiNotice* pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
        {
            // 协同模式下若项目已被初始化则屏蔽保存按钮
            if (mWindow().collaboration().actived() && mWindow().collaboration().modelServer().projectInitialed())
            {
                auto pActionSave = mWindow().queryAction("action.node.xmlSave");
                if (pActionSave != nullptr)
                {
                    pActionSave->setVisible(false);
                }
            }
        }
        break;
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        this->onAction(pActionNotice);
    }
    break;
    default:
        break;
    }
}

bool UIComNodeFileFunction::onAction(UiActionNotice* pActionNotice)
{
    if (pActionNotice->action().is("action.node.xmlSave"))   // 文件：保存
    {
        auto bOk = this->saveToLocal();
        if (bOk)
        {
            this->saveMd5(mWindow().moduleType());
        }
    }
    else if (pActionNotice->action().is("action.display.node.Import"))   // 文件：导入
    {
        if (mWindow().moduleType() == "Design")
        {
            this->onActionDesignImport();
        }
        else if (mWindow().moduleType() == "Catalog")
        {
            this->onActionCatelogImport();
        }
    }
    else if (pActionNotice->action().is("action.display.node.Export"))   // 文件：导出
    {
        if (mWindow().moduleType() == "Design")
        {
            {
                if (_exportWidget.isHidden())
                    _exportWidget.show();
                else
                    _exportWidget.activateWindow();
            }
            //this->onActionDesignExport();
        }
        else if (mWindow().moduleType() == "Catalog")
        {
            this->onActionCatelogExport();
        }
    }
    return  true;
}
void UIComNodeFileFunction::onActionDesignImport()
{
    // 获取当前节点
    auto pCur = mWindow().core().nodeTree().currentNode();
    if (pCur == nullptr)
    {
        WD_WARN_T("ErrorUIComNodeFileFunction", "NonParentNodeSelected");
        return;
    }
    // 申领对象
    if (!WD::Core().getBMDesign().claimMgr().checkAdd(pCur, nullptr))
        return;

    //转换到文件对话框需要的字符串格式
    std::string surrportTypeStr = getFilterStr(WD::WDPluginFormat::FA_Read);
    if (surrportTypeStr.empty())
    {
        return;
    }

    //文件对话框
    std::string fileName = QFileDialog::getOpenFileName(
        mWindow().widget()
        , QString::fromUtf8(WD::WDTs("UiComNodeFileFunction", "ImportDesignerData").c_str())
        , ""
        , QString::fromUtf8(surrportTypeStr.c_str())).toLocal8Bit().data();
    if (fileName.empty())
        return;

    //根据拓展名获取插件对象
    WD::WDPluginFormat* loader = _core.extensionMgr().createFormatByExtendName(fileName, "", WD::WDPluginFormat::FA_Read);
    if (loader == nullptr)
    {
        WD_INFO_T("ErrorUIComNodeFileFunction", "NoTypePlugSupport");
        return;
    }

    // 设置任务
    auto task = WD::WDTask::MakeShared();
    if (task != nullptr)
    {
        task->setUserData(loader, "loader");
        task->setUserData(fileName, "fileName");
        // 由于是异步处理，这里需要先确定当前节点
        WD::WDNode::WeakPtr pWeak = pCur;
        task->setUserData(pWeak, "currentNode");

        this->prepareTaskCallbacks(*task);
        mWindow().core().taskSys().addTask(task);
    }
}

void UIComNodeFileFunction::onActionCatelogImport()
{
    // 获取文件名
    std::string surrportTypeStr = getFilterStr(WD::WDPluginFormat::FA_Read);
    std::string fileName = QFileDialog::getOpenFileName(mWindow().widget()
        , QString::fromUtf8(WD::WDTs("UiComNodeFileFunction", "ImportComponentLibrary").c_str())
        , ""
        , QString::fromUtf8(surrportTypeStr.c_str())).toLocal8Bit().data();
    if (fileName.empty())
        return;

    // 根据拓展名获取插件对象
    WD::WDPluginFormat* loader = _core.extensionMgr().createFormatByExtendName(fileName, "");
    if (loader == nullptr)
    {
        WD_INFO_T("ErrorUIComNodeFileFunction", "NoTypePlugSupport");
        return;
    }
    //插件读取
    WD::WDPluginFormat::Objects     result;
    WD::WDPluginFormat::FormatParam param;
    param.fileName = fileName;
    loader->read(param, result);
    _core.extensionMgr().destroyExtension(loader);
    if (result.empty())
    {
        WD_WARN_T("ErrorUIComNodeFileFunction", "ImportFailed");
        return;
    }

    //更新元件库
    WD::WDNode::SharedPtr pRoot = result.back()->toPtr<WD::WDNode>();
    if (pRoot == nullptr)
    {
        WD_WARN_T("ErrorUIComNodeFileFunction", "ImportFailed");
        return;
    }

    auto& mgr = mWindow().core().getBMCatalog();
    WD::WDNode::Nodes children = pRoot->children();
    for (auto pNode : children)
    {
        mgr.setParent(pNode, mgr.root());
    }

    WD_INFO_T("ErrorUIComNodeFileFunction", "ImportSucceed");
}

void UIComNodeFileFunction::onActionDesignExport()
{
    // 选择文件
    std::string surrportTypeStr = getFilterStr(WD::WDPluginFormat::FA_Write);
    QString     fiterSelect;
    std::string fileName = QFileDialog::getSaveFileName(
        mWindow().widget()
        , QString::fromUtf8(WD::WDTs("UiComNodeFileFunction", "ExportDesignerData").c_str())
        , ""
        , QString::fromUtf8(surrportTypeStr.c_str())
        , &fiterSelect).toLocal8Bit().data();
    if (fileName.empty())
        return;

    QString suffix;
    WD::WDPluginFormat::Formats fmts;
    WD::WDExtensionMgr& extMgr = _core.extensionMgr();
    extMgr.getSupportTypes(fmts, WD::WDPluginFormat::FA_Write);
    for (const auto& fmt : fmts)
    {
        if (fmt.format() == fiterSelect.toLocal8Bit().toStdString())
        {
            // 这里一定会进
            suffix = fmt.fmt.c_str();
            break;
        }
    }
    // 如果没有后缀则加上后缀
    if (!QString::fromLocal8Bit(fileName.data()).endsWith(suffix, Qt::CaseInsensitive))
        fileName += suffix.toLocal8Bit().toStdString();

    //根据拓展名获取插件对象
    std::string filter = fiterSelect.toLocal8Bit().toStdString();
    auto        fmtVersion = this->getFmtVersion(WD::WDPluginFormat::FA_Write);
    std::string version = fmtVersion[filter];
    auto loader = extMgr.createFormatByExtendName(fileName, version, WD::WDPluginFormat::FA_Write);
    if (loader == nullptr)
    {
        WD_INFO_T("ErrorUIComNodeFileFunction", "NoTypePlugSupport");
        return;
    }

    // 获取当前节点作为导出节点
    auto pCur = _core.nodeTree().currentNode();
    if (pCur == nullptr)
    {
        WD_WARN_T("ErrorUIComNodeFileFunction", "InvalidNode");
        return;
    }
    WD::WDPluginFormat::Objects results = { pCur };

    WD::WDPluginFormat::FormatParam param;
    param.fileName = fileName;
    param.version = version;
    loader->write(param, results);
    extMgr.destroyExtension(loader);

    WD_INFO_T("ErrorUIComNodeFileFunction", "SaveSucceed");
}

void UIComNodeFileFunction::onActionCatelogExport()
{
    // 选择文件
    std::string surrportTypeStr = this->getFilterStr(WD::WDPluginFormat::FA_Write);
    QString     fiterSelect;
    std::string fileName = QFileDialog::getSaveFileName(
        mWindow().widget()
        , QString::fromUtf8(WD::WDTs("UiComNodeFileFunction", "ExportComponentLibrary").c_str())
        , ""
        , QString::fromUtf8(surrportTypeStr.c_str())
        , &fiterSelect).toLocal8Bit().data();

    if (fileName.empty())
        return;

    QString suffix;
    WD::WDPluginFormat::Formats fmts;
    WD::WDExtensionMgr& extMgr = _core.extensionMgr();
    extMgr.getSupportTypes(fmts, WD::WDPluginFormat::FA_Write);
    for (const auto& fmt : fmts)
    {
        if (fmt.format() == fiterSelect.toLocal8Bit().toStdString())
        {
            // 这里一定会进
            suffix = fmt.fmt.c_str();
            break;
        }
    }
    // 如果没有后缀则加上后缀
    if (!QString::fromLocal8Bit(fileName.data()).endsWith(suffix, Qt::CaseInsensitive))
        fileName += suffix.toLocal8Bit().toStdString();

    //根据拓展名获取插件对象
    WD::WDPluginFormat* loader = extMgr.createFormatByExtendName(fileName, "");
    if (loader == nullptr)
    {
        WD_INFO_T("ErrorUIComNodeFileFunction", "NoTypePlugSupport");
        return;
    }

    WD::WDPluginFormat::Objects results = { _core.nodeTree().currentNode() };
    if (results.empty())
    {
        WD_WARN_T("ErrorUIComNodeFileFunction", "InvalidNode");
        return;
    }
    WD::WDPluginFormat::FormatParam param;
    param.fileName = fileName;
    loader->write(param, results);
    extMgr.destroyExtension(loader);
    WD_INFO_T("ErrorUIComNodeFileFunction", "SaveSucceed");
}

std::string UIComNodeFileFunction::getFilterStr(const WD::WDPluginFormat::FormatAttr& rw)
{
    //获取支持类型数组
    Formats fmts;
    WD::WDExtensionMgr& extMgr = _core.extensionMgr();
    extMgr.getSupportTypes(fmts, rw);
    if (fmts.empty())
    {
        WD_INFO_T("ErrorUIComNodeFileFunction", "NoTypePlugSupport");
        return std::string();
    }

    //拼装为字符串 "(*.xml);;(*.txt);;(*.***);;····"
    std::string destStr;
    for (size_t index = 0; index < fmts.size(); index++)
    {
        destStr += fmts[index].format();
        if ((index + 1) != fmts.size())
        {
            destStr = destStr + ";;";
        }
    }
    return destStr;
}
UIComNodeFileFunction::MapString UIComNodeFileFunction::getFmtVersion(const WD::WDPluginFormat::FormatAttr& rw)
{
    //获取拓展管理
    WD::WDExtensionMgr& extMgr = _core.extensionMgr();
    //获取支持类型数组
    WD::WDExtensionMgr::Formats fmts;
    extMgr.getSupportTypes(fmts, rw);
    if (fmts.empty())
    {
        WD_INFO_T("ErrorUIComNodeFileFunction", "NoTypePlugSupport");
        return MapString();
    }

    MapString fmtVersion;
    for (auto& var : fmts)
    {
        fmtVersion[var.format()] = var.version;
    }
    return fmtVersion;
}

void    UIComNodeFileFunction::saveMd5(std::string_view bmName)
{
}

bool    UIComNodeFileFunction::saveToLocal()
{
    bool bSaved = false;
    const auto& moduleType = mWindow().moduleType();
    if (moduleType == "Design")
    {
        // 保存几何库中的基本体
        char geoFileName[1024] = { 0 };
        sprintf_s(geoFileName, sizeof(geoFileName), "%s/%s.geometry"
            , mWindow().projectPath().c_str()
            , mWindow().projectName().c_str());
        _core.geometryMgr().save(geoFileName);

        bSaved = _core.getBMDesign().save(mWindow().moduleDataFile("Design"));
    }
    else if (moduleType == "Catalog")
    {
        std::string fileName = mWindow().moduleDataFile("Catalog");
        bSaved = _core.getBMCatalog().save(fileName);
    }

    if (bSaved)
    {
        WD_INFO_T("ErrorUIComNodeFileFunction", "SaveSucceed");
    }
    else
    {
        WD_WARN_T("ErrorUIComNodeFileFunction", "SaveFailed");
    }

    return bSaved;
}

QWidget* UIComNodeFileFunction::getWidget(const char* name)
{
    WDUnused(name);
    return  nullptr;
}


void    UIComNodeFileFunction::slotFinishLoad(WD::WDTask::SharedPtr pTask)
{
    auto& mgr       = mWindow().core().getBMDesign();

    //当前选中节点
    auto data = pTask->userData("currentNode");
    auto pWeakPtr = std::any_cast<WD::WDNode::WeakPtr>(&data);
    if (pWeakPtr == nullptr || (*pWeakPtr).lock() == nullptr || pTask->result().empty())
    {
        WD_WARN_T("ErrorUIComNodeFileFunction", "ImportFailed");
        return;
    }

    pTask->progressEvt()(95.0f, WD::WDTs("UiComNodeFileFunction", "Adding The Results To The Node Tree"));

    auto& res = pTask->result();
    WD::WDNode::Nodes resNodes;
    for (size_t i = 0; i < res.size(); i++)
    {
        //更新设计模块
        auto pNode = res[i]->toPtr<WD::WDNode>();
        if (pNode == nullptr)
            continue;
        resNodes.push_back(pNode);
        mgr.setParent(pNode, (*pWeakPtr).lock());
    }

    // 复制节点命令
    auto pCmdCreateNode = WD::WDBMBase::MakeCreatedCommand(resNodes);
    if (pCmdCreateNode != nullptr)
    {
        _core.undoStack().push(pCmdCreateNode);
    }
    
    pTask->progressEvt()(100.0f, WD::WDTs("UiComNodeFileFunction", "Successfully Added To The Node Tree!"));
    WD_INFO_T("ErrorUIComNodeFileFunction", "ImportSucceed");
    mWindow().dcStatusBar().progressBar()->setVisible(false);
}
void    UIComNodeFileFunction::slotProgress(float p, QString msg)
{
    mWindow().dcStatusBar().progressBar()->setFormat(msg + QString("~[%p%]"));
    mWindow().dcStatusBar().progressBar()->setVisible(true);
    mWindow().dcStatusBar().progressBar()->setValue(int(p));
}
void    UIComNodeFileFunction::slotRepeatSave()
{
    if (mWindow().collaboration().actived())
        return ;
    // 是否自动保存
    if (!_autoSave)
        return ;

    // 询问是否保存
    auto res = WD_QUESTION_T("ErrorUIComNodeFileFunction", "Do save?");
    if (res != 0)
        return ;

    // 保存
    this->saveToLocal();
}

void UIComNodeFileFunction::onCfgAutoSaveValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<bool>();
    if (pValue == nullptr)
        return ;

    _autoSave = *pValue;
}
void UIComNodeFileFunction::onCfgAutoSaveSpacingValueChanged(const WD::WDConfigItem& item)
{
    auto pValue = item.value<int>();
    if (pValue == nullptr)
        return ;

    _autoSaveSpacing = *pValue * 60 * 1000;
    if (_timer != nullptr)
    {
        _timer->setInterval(_autoSaveSpacing);
    }
}

void    UIComNodeFileFunction::prepareTaskCallbacks(WD::WDTask& task)
{
    // 加载完成通知
    task.finishedEvt() = [&](WD::WDTask& task)->bool
        {
            emit finishLoad(task.toPtr<WD::WDTask>());
            return  true;
        };
    // 加载进度
    task.progressEvt() = [&](float p, const std::string& msg)
        {
            emit progress(p, QString(msg.c_str()));
        };
    // 加载执行函数,函数异步执行，不能做参数捕获
    task.execEvt() = ([&](WD::WDTask& task)->bool
        {
            //每次切到非主线程，都会新建一个共享上下文
            //1.可以解决在主线程释放在其他线程创建的资源的时候的线程冲突问题
            //2.可以解决纹理显示不全的问题（具体原因尚未查明）
            //2023.10.19 通过设置gl同步解决了纹理显示不全的问题
            //多线程仍然需要在辅助线程内同时创建和销毁ctx来处理
            WD::WDShareContext::SharedPtr   ctxPtr = nullptr;
            bool        bMake = false;
            //设置为静态变量后，函数结束不销毁变量（如果销毁变量则相当于进行了类似同步的操作）
            //static ObjectPtr   obj     =   Core().objectCreator().create(WD::OGLCtxQtGuid,nullptr);//创建OGLCtxQt
            WD::WDObject::SharedPtr   obj = _core.objectCreator().create(WD::OGLCtxQtGuid, nullptr);

            if (obj.get())
            {
                ctxPtr = obj->toPtr<WD::WDShareContext>();
            }

            if (ctxPtr.get())
            {
                bMake = ctxPtr->makeCurrent();//会新设置共享上下文
            }
            WD::WDPluginFormat* loader = nullptr;
            auto pLoader = task.userData("loader");
            if (pLoader.type() == typeid(WD::WDPluginFormat*))
            {
                auto value = std::any_cast<WD::WDPluginFormat*>(&pLoader);
                if (value != nullptr) {
                    loader = *value;
                }
            }
            std::string fileName;
            auto pFileName = task.userData("fileName");
            if (pFileName.type() == typeid(std::string))
            {
                auto value = std::any_cast<std::string>(&pFileName);
                if (value != nullptr) {
                    fileName = *value;
                }
            }

            WD::WDPluginFormat::Objects     result;
            WD::WDPluginFormat::FormatParam param;
            param.fileName = fileName;
            /// <summary>
            ///  进度函数 
            /// </summary>
            param.evtProgress = [&](float val, const char* msg)
                {
                    task.progressEvt()(val * 0.9f, msg);// 子线程内的进度值<=90
                };
            //如果没有设置成功，则直接切换到主线程运行
            if (!bMake)
            {
                QMutex qMutex;
                qMutex.lock();
                _core.apiDelegate()->sendRunable(
                    [&](void*, void*)->void*
                    {
                        //====
                        loader->read(param, result);
                        //通知辅助线程可以往下执行
                        qMutex.unlock();
                        //====
                        return nullptr;
                    }
                , nullptr, nullptr);
                //需要考虑等待主线程调用完成之后再执行之后的代码
                qMutex.lock();//阻塞等待主线程执行结束
            }
            else
            {
                loader->read(param, result);
            }
            task.result() = result;
            _core.extensionMgr().destroyExtension(loader);
            glFenceSync(GL_SYNC_GPU_COMMANDS_COMPLETE, 0);
            return  true;
        });
}