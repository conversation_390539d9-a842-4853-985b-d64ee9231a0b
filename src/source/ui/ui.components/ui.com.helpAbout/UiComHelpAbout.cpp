#include "UiComHelpAbout.h"
#include "core/message/WDMessage.h"
#include "core/WDTranslate.h"
#include <QApplication>
#include <QFile>
#include <QDesktopServices>
#include <QUrl>

#ifdef WIN32
    #include    <qaxobject.h>
#endif


UiComHelpAbout::UiComHelpAbout(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject* parent)
    : QObject(parent)
    , IUiComponent(mainWindow, attrs)
{
    // 获取WIZDesigner信息文件路径
    QString messagePath = QApplication::applicationDirPath() + "/../data/VERSION";
    // 显示当前程序版本等信息
    _pHelpAboutDialog = new HelpAboutDialog(mainWindow, mainWindow.widget());
}

UiComHelpAbout::~UiComHelpAbout()
{
    if (_pHelpAboutDialog != nullptr)
    {
        delete _pHelpAboutDialog;
        _pHelpAboutDialog = nullptr;
    }
}

void UiComHelpAbout::onNotice(UiNotice * pNotice)
{
    int nType = pNotice->type();
    switch (nType)
    {
    case UiNoticeType::UNT_Action:
    {
        UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
        this->onAction(pActionNotice);
    }
    break;
    default:
        break;
    }
}

QWidget* UiComHelpAbout::getWidget(const char* name)
{
    if (name == nullptr || strlen(name) == 0)
    {
        return  (QWidget*)this;
    }
    return  nullptr;
}

void UiComHelpAbout::onAction(UiActionNotice* pAction)
{
    if (pAction->action().is("action.help.about"))
    {
        if (_pHelpAboutDialog->isHidden())
            _pHelpAboutDialog->show();
        else
            _pHelpAboutDialog->activateWindow();
    }
    else if(pAction->action().is("action.manual"))
    {
        QString manualPathReleaseQStr = QString::fromUtf8(mWindow().core().exeDirPath()) + QString::fromUtf8("../manual/WIZ Designer 2024版操作手册（上）设计模块3DDS .pdf");
        if (!QFile::exists(manualPathReleaseQStr))
            manualPathReleaseQStr = qApp->applicationDirPath() + QString::fromUtf8("/../../../manual/WIZ Designer 2024版操作手册（上）设计模块3DDS .pdf");
        if (!QFile::exists(manualPathReleaseQStr))
        {
            WD_WARN_T("UIComHelpAbout", "open failure,no find manual");
            return;
        }

        if(!QDesktopServices::openUrl(QUrl::fromLocalFile(manualPathReleaseQStr)))
        {
            WD_WARN_T("UIComHelpAbout", "open failure,no find pdf reader");
        }
    }
#ifdef WIN32
    else
    {
        QAxObject* wpfCom = new QAxObject(pAction->action().id(),nullptr);
        if (!wpfCom->isNull())
        {
            wpfCom->dynamicCall("ShowWindow()", {});
        }
        else
        {
            delete  wpfCom;
        }
    }
#endif
}
