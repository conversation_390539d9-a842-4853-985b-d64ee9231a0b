#include "BranSplitAndMergeDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/WDBMBase.h"
#include "core/message/WDMessage.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/businessModule/design/pipeWork/WDBMDPipeUtils.h"
#include "core/businessModule/WDBMColorTable.h"
#include "core/viewer/WDViewer.h"
#include <QButtonGroup>
#include <iomanip>
#include <sstream>
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/undoRedo/WDUndoStack.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "core/businessModule/WDBMAuditObjectMgr.h"

BranSplitAndMergeDialog::BranSplitAndMergeDialog(WD::WDCore& core, QWidget* parent)
    : QDialog(parent)
    , _core(core)
{
    ui.setupUi(this);
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));
    _mergeBrans.clear();

    retranslateUi();
    QButtonGroup *dirGroup = new QButtonGroup(this);
    dirGroup->addButton(ui.checkBoxForward);
    dirGroup->addButton(ui.checkBoxBackward);
    dirGroup->setExclusive(true);
    // 默认选中沿流向方向切割
    ui.checkBoxForward->setChecked(true);

    QButtonGroup* posGroup = new QButtonGroup(this);
    posGroup->addButton(ui.checkBoxSpool);
    posGroup->addButton(ui.checkBoxDist);
    posGroup->setExclusive(true);
    // 默认选中距端面切割
    ui.checkBoxSpool->setChecked(true);

        
    connect(ui.pushButtonSplit, &QPushButton::clicked, this, &BranSplitAndMergeDialog::slotPushBtnSplitClicked);
    connect(ui.pushButtonMerge, &QPushButton::clicked, this, &BranSplitAndMergeDialog::slotPushBtnMergeClicked);
}

BranSplitAndMergeDialog::~BranSplitAndMergeDialog() 
{
    _mergeBrans.clear();
}

void BranSplitAndMergeDialog::hideEvent(QHideEvent* e)
{
    //取消激活捕捉工具
    auto& tool = WD::Core().viewer().capturePositioning();
    tool.deativate();

    QDialog::hideEvent(e);
}

void BranSplitAndMergeDialog::onResult(const WD::WDCapturePositioningResult& result
    , bool& existFlag
    , const WD::WDCapturePositioning& sender)
{
    // 需求：李广群 只要分支的尾坐标和头坐标一致，且流向相同(朝向相反)才可以合并
    Q_UNUSED(sender);

    WD::WDNode::SharedPtr pNode = result.node.lock();
    if (pNode == nullptr)
        return;

    // 获取拾取的分支节点
    auto pBranNode = pNode;
    while (pBranNode != nullptr)
    {
        if (pBranNode->isType("BRAN"))
        {
            break;
        }
        else
        {
            pBranNode = pBranNode->parent();
        }
    }

    if(pBranNode == nullptr)
    {
        WD_WARN_T("BranSplitAndMergeDialog", "Pickup node is not pipiCom, please rePickUp");
        return;
    }

    if (_mergeBrans.size() == 1)
    {
        auto pBran1 = _mergeBrans.front().lock();
        if (pBran1 == nullptr)
            return;
        if (pBranNode == pBran1)
        {
            WD_ERROR_T("BranSplitAndMergeDialog", "Can't merge the same branch!");
            //设置捕捉退出
            existFlag = true;

            return;
        }
    }

    _mergeBrans.push_back(pBranNode);

    if(_mergeBrans.size() == 1)
        WD_INFO_T("BranSplitAndMergeDialog", "Please pick up the pipe fitting of downstream branch in 3D view!");

    if (_mergeBrans.size() == 2)
    {
        //设置捕捉退出
        existFlag = true;
        auto pBm = _core.currentBM();
        if (pBm == nullptr)
        {
            assert(false);
            return;
        }

        auto pBran1 = _mergeBrans.front().lock();
        auto pBran2 = _mergeBrans.back().lock();
        if (pBran1 == nullptr || pBran2 == nullptr)
        {
            assert(false);
            return;
        }
        _mergeBrans.clear();

        // 申领
        if (!_core.getBMDesign().claimMgr().checkDelete(pBran2))
            return ;
        bool bCancelMd = false;
        if (!_core.getBMDesign().claimMgr().checkUpdate(pBran1
            , {"Tposition WRT World", "Tdirection WRT World", "Tbore", "Tconnect", "Tref"}
            , bCancelMd))
            return;
        if (bCancelMd)
            return;

        // 获取分支1的头尾坐标、朝向、
        auto bran1Hpos = pBran1->getAttribute("Hposition WRT World").toDVec3();
        auto bran1HDir = pBran1->getAttribute("Hdirection WRT World").toDVec3();
        auto bran1Tpos = pBran1->getAttribute("Tposition WRT World").toDVec3();
        auto bran1TDir = pBran1->getAttribute("Tdirection WRT World").toDVec3();

        // 获取分支2头尾坐标、朝向
        auto bran2Hpos = pBran2->getAttribute("Hposition WRT World").toDVec3();
        auto bran2HDir = pBran2->getAttribute("Hdirection WRT World").toDVec3();
        auto bran2Tpos = pBran2->getAttribute("Tposition WRT World").toDVec3();
        auto bran2TDir = pBran2->getAttribute("Tdirection WRT World").toDVec3();

        // 如果分支2的尾和分支1的头匹配、交换分支1和分支2
        if(WD::DVec3::SamePosition(bran2Tpos, bran1Hpos) && WD::DVec3::OnTheSameLine(bran2TDir.normalized(), (bran1HDir).normalized()))
        {
            // 交换分支1和分支2
            auto tmpBran = pBran1;
            pBran1 = pBran2;
            pBran2 = tmpBran;

            //重新获取 分支1 2的头尾数据
            bran1Hpos = pBran1->getAttribute("Hposition WRT World").toDVec3();
            bran1HDir = pBran1->getAttribute("Hdirection WRT World").toDVec3();
            bran1Tpos = pBran1->getAttribute("Tposition WRT World").toDVec3();
            bran1TDir = pBran1->getAttribute("Tdirection WRT World").toDVec3();
            bran2Hpos = pBran2->getAttribute("Hposition WRT World").toDVec3();
            bran2HDir = pBran2->getAttribute("Hdirection WRT World").toDVec3();
            bran2Tpos = pBran2->getAttribute("Tposition WRT World").toDVec3();
            bran2TDir = pBran2->getAttribute("Tdirection WRT World").toDVec3();
        }

        // 分支1的尾和分支2的头匹配、将分支2的管件合并到分支1
        if (WD::DVec3::SamePosition(bran1Tpos, bran2Hpos) && WD::DVec3::OnTheSameLine(bran1TDir.normalized(),(bran2HDir).normalized()))
        {
            // 获取节点树当前节点
            auto pCurrent = _core.nodeTree().currentNode();
            WD::WDUndoCommand* pParentCmd = new WD::WDUndoCommand("BranMerge");

            // 将分支2的子节点追加到分支1后面
            WD::WDBMClaimMgr::MoveDatas checkDatas;
            WD::WDBMBase::NodeHierarchyMoveInfos infos;
            auto children = pBran2->children();
            for (size_t i = 0; i < children.size(); ++i)
            {
                auto pChild = children[i];
                if (pChild == nullptr)
                    continue;
                // 直管是自动生成销毁，不需要移动
                if(pChild->isType("TUBI"))
                    continue;

                if (i < children.size() - 1)
                {
                    checkDatas.emplace_back(WD::WDBMClaimMgr::MoveData(pChild, pBran1, children.at(i + 1)));
                    infos.emplace_back(WD::WDBMBase::NodeHierarchyMoveInfo(pChild, pBran1, children.at(i + 1)));
                }
                else if (i == children.size() - 1)
                {
                    checkDatas.emplace_back(WD::WDBMClaimMgr::MoveData(pChild, pBran1, nullptr));
                    infos.emplace_back(WD::WDBMBase::NodeHierarchyMoveInfo(pChild, pBran1, nullptr));
                }
            }
            WD::WDBMBase::MakeNodeHierarchyMoveInfoCommand(infos, pParentCmd);
            // 申领移动所有管件
            if (!WD::Core().getBMDesign().claimMgr().checkMove(checkDatas))
                return;

            // 获取分支2头尾属性（管径、连接类型、连接节点）
            auto bran2TBore = pBran2->getAttribute("Tbore");
            auto bran2TCType = pBran2->getAttribute("Tconnect");
            auto bran2Tref = pBran2->getAttribute("Tref");

            // 将分支1的尾属性设置成分支2的尾属性（包括坐标、朝向、管径、连接类型、连接节点）
            WD::WDBMBase::MakeAttributeSetCommand(pBran1, "Tposition WRT World", WD::WDBMAttrValue(bran2Tpos), pParentCmd);
            WD::WDBMBase::MakeAttributeSetCommand(pBran1, "Tdirection WRT World", WD::WDBMAttrValue(bran2TDir), pParentCmd);
            WD::WDBMBase::MakeAttributeSetCommand(pBran1, "Tbore", bran2TBore, pParentCmd);
            WD::WDBMBase::MakeAttributeSetCommand(pBran1, "Tconnect",bran2TCType, pParentCmd);
            WD::WDBMBase::MakeAttributeSetCommand(pBran1, "Tref", bran2Tref, pParentCmd);

            // 删除分支2
            WD::WDBMBase::MakeSceneRemoveCommand({ pBran2 }, pParentCmd);
            WD::WDBMBase::MakeDestroyCommand({ pBran2 }, pParentCmd);

            pParentCmd->setNoticeAfterRedo([=](const WD::WDUndoCommand&)
            {
                // 设置节点树当前节点
                _core.nodeTree().setCurrentNode(pCurrent);

                // pCurrent是分支1的子节点，则激活分支1，否则不激活
                auto activeFlag = pBran1->isParent(*pCurrent);
                setBranActiveFlag(*pBran1, activeFlag);

                // 更新节点
                pBran1->triggerUpdate(true);

                // 触发重绘
                _core.needRepaint();
            });
            pParentCmd->setNoticeAfterUndo([=](const WD::WDUndoCommand&)
            {
                // 设置节点树当前节点
                _core.nodeTree().setCurrentNode(pCurrent);

                // pCurrent管件所在的分支激活，否则分支不激活
                auto activeFlag = pBran1->isParent(*pCurrent);
                setBranActiveFlag(*pBran1, activeFlag);
                activeFlag = pBran2->isParent(*pCurrent);
                setBranActiveFlag(*pBran2, activeFlag);

                // 更新节点
                pBran1->triggerUpdate(true);
                pBran2->triggerUpdate(true);

                // 触发重绘
                _core.needRepaint();
            });
            _core.undoStack().push(pParentCmd);
        }
        else
        {
            WD_ERROR_T("BranSplitAndMergeDialog", "Bran merge failure!");
            return;
        }
    }
}

void BranSplitAndMergeDialog::onDeactived(const WD::WDCapturePositioning& sender)
{
    Q_UNUSED(sender);
    _mergeBrans.clear();
}

void BranSplitAndMergeDialog::slotPushBtnSplitClicked()
{
    // 获取切割的节点
    auto pCurrentNode = _core.nodeTree().currentNode();
    if (pCurrentNode == nullptr || (!IsPipeCom(*pCurrentNode)))
    {
        WD_WARN_T("BranSplitAndMergeDialog", "Please select Pipe Node!");
        return;
    }

    // 获取管件所在的分支的节点
    auto pBran = pCurrentNode->parent();
    if (pBran == nullptr)
    {
        return;
    }
    // 获取分支所在的pipe节点
    auto pPipe = pBran->parent();
    if (pPipe == nullptr)
    {
        return;
    }
    // 申领PIPE节点和BRAN的后一个节点
    if (!_core.getBMDesign().claimMgr().checkAdd(pPipe, pBran->nextBrother()))
        return;
    bool bCancelMd = false;
    if (!_core.getBMDesign().claimMgr().checkUpdate(pBran
        , { "Tposition WRT World", "Tdirection WRT World", "Tbore", "Tconnect", "Tref" }
        , bCancelMd))
        return;
    if (bCancelMd)
        return;

    // 根据界面信息获取切割的距离、方向、基准点类型
    auto distance = ui.doubleSpinBox->value();
    auto forward = ui.checkBoxForward->isChecked();
    auto spool = ui.checkBoxSpool->isChecked();

    // 获取切割的基准点和基准点的朝向
    WD::DVec3 basePoint, basePointDir;
    std::string bore = "";
    std::string conn = "";
    getBasedPosAndDirAndBore(*pCurrentNode, forward, spool, basePoint, basePointDir, bore);

    // 获取切割点的位置
    auto splitPos = getSplitPos(basePoint, basePointDir, distance);

    // 是否可以切割
    if (!isEnableSplit(*pCurrentNode, forward, basePoint, distance))
    {
        // 不可以切割分支
        WD_WARN_T("BranSplitAndMergeDialog", "Split length is over max length!");
        return;
    }

    // 询问是否进行切割
    if (!questionSplit(*pCurrentNode, forward, spool, distance))
        return;

    // 获取新建的分支节点
    auto pNewBranch = createBranNode(*pBran);
    // 设置新分支的坐标、方向、头管径、连接类型、连接节点、分支的管道等级
    if (pNewBranch != nullptr)
    {
        pNewBranch->setAttribute("Hposition WRT World", WD::WDBMAttrValue(splitPos));
        if (forward)
        {
            pNewBranch->setAttribute("Hdirection WRT World", WD::WDBMAttrValue(basePointDir));
        }
        else
        {
            pNewBranch->setAttribute("Hdirection WRT World", WD::WDBMAttrValue(-basePointDir));
        }
        pNewBranch->setAttribute("Hbore", WD::WDBMAttrValue(bore));
        pNewBranch->setAttribute("Hconnect", WD::WDBMAttrValue(conn));
        pNewBranch->setAttribute("Href", WD::WDBMAttrValue(WD::WDBMNodeRef(pBran)));
    }

    // 收集新分支下的管件
    auto pOtherComs = collectionNodes(*pCurrentNode);

    WD::WDUndoCommand* pParentCmd = new WD::WDUndoCommand("BranSplit");

    // 移动操作会申领目标节点，但是管道分割的目标节点是新创建的，这需要在移动前手动申领该新节点
    _core.getBMDesign().claimMgr().newCreatedNodes().add({ pNewBranch });
    // 申领移动所有管件
    WD::WDBMClaimMgr::MoveDatas checkDatas;
    WD::WDBMBase::NodeHierarchyMoveInfos infos;
    for (size_t i = 0; i < pOtherComs.size(); ++i)
    {
        auto pChild = pOtherComs.at(i);
        if (pChild == nullptr)
            continue;

        if (i < pOtherComs.size() - 1)
        {
            checkDatas.emplace_back(WD::WDBMClaimMgr::MoveData(pChild, pNewBranch, pOtherComs.at(i + 1)));
            infos.emplace_back(WD::WDBMBase::NodeHierarchyMoveInfo(pChild, pNewBranch, pOtherComs.at(i + 1)));
        }
        else if (i == pOtherComs.size() - 1)
        {
            checkDatas.emplace_back(WD::WDBMClaimMgr::MoveData(pChild, pNewBranch, nullptr));
            infos.emplace_back(WD::WDBMBase::NodeHierarchyMoveInfo(pChild, pNewBranch, nullptr));
        }
    }
    WD::WDBMBase::MakeNodeHierarchyMoveInfoCommand(infos, pParentCmd);
    // 有可能收集到的节点列表本身就是空，这种情况下就没有必要再申领
    if (!checkDatas.empty() && !WD::Core().getBMDesign().claimMgr().checkMove(checkDatas))
        return ;

    // 设置原分支的尾管径、连接类型、坐标、方向、连接节点
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tposition WRT World", WD::WDBMAttrValue(splitPos), pParentCmd);
    if (forward)
    {
        WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tdirection WRT World", WD::WDBMAttrValue(-basePointDir), pParentCmd);
    }
    else
    {
        WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tdirection WRT World", WD::WDBMAttrValue(basePointDir), pParentCmd);
    }
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tbore", WD::WDBMAttrValue(bore), pParentCmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tconnect", WD::WDBMAttrValue(conn), pParentCmd);
    WD::WDBMBase::MakeAttributeSetCommand(pBran, "Tref", WD::WDBMAttrValue(WD::WDBMNodeRef(pNewBranch)), pParentCmd);

    // 设置新分支undo/redo命令
    WD::WDBMBase::MakeCreatedCommand({ pNewBranch }, pParentCmd);
    WD::WDBMBase::MakeSceneAddCommand({ pNewBranch }, pParentCmd);
    pParentCmd->setNoticeAfterRedo([=](const WD::WDUndoCommand&)
        {
            // 将搜集的节点移到新建分支下
            for (auto pNode : pOtherComs)
            {
                if (pNode == nullptr)
                    continue;
                _core.getBMDesign().setParent(pNode, pNewBranch, false);
            }
            _core.nodeTree().setCurrentNode(pCurrentNode);

            // 分支包含pCurrentNode，则激活；否则不激活
            auto activeFlag = pNewBranch->isParent(*pCurrentNode);
            setBranActiveFlag(*pNewBranch, activeFlag);
            activeFlag = pBran->isParent(*pCurrentNode);
            setBranActiveFlag(*pBran, activeFlag);

            // 更新原分支和新建分支的连接（主要是用于生成新的TUBI）
            pBran->triggerUpdate(true);
            pNewBranch->triggerUpdate(true);
            _core.needRepaint();
        });
    pParentCmd->setNoticeAfterUndo([=](const WD::WDUndoCommand& )
        {
            // 将搜集的节点移回到原分支
            for (auto pNode : pOtherComs)
            {
                if (pNode == nullptr)
                    continue;
                _core.getBMDesign().setParent(pNode, pBran, false);
            }

            _core.nodeTree().setCurrentNode(pCurrentNode);

            // 将原分支下所有的管件的激活
            setBranActiveFlag(*pBran, true);

            // 更新原分支和新建分支的连接（主要是用于生成新的TUBI）
            pBran->triggerUpdate(true);
            // 场景重绘
            _core.needRepaint();
        });
    _core.undoStack().push(pParentCmd);
}

void BranSplitAndMergeDialog::slotPushBtnMergeClicked()
{
    //激活捕捉工具
    auto& tool = _core.viewer().capturePositioning();
    WD::WDCapturePositioningParam param;
    param.pMonitor = this;

    auto option = tool.option();
    option._disaTypes = { WD::WDCapturePositioningType::CPT_Any
        , WD::WDCapturePositioningType::CPT_Aid
        , WD::WDCapturePositioningType::CPT_PLine
        , WD::WDCapturePositioningType::CPT_PPoint
        , WD::WDCapturePositioningType::CPT_Screen
        , WD::WDCapturePositioningType::CPT_Graphics };

    // 如果当前捕捉类型被禁用,使用上一次的捕捉类型
    if (option._disaTypes.find(option.type) != option._disaTypes.end())
        option.type = WD::WDCapturePositioningType::CPT_Element;

    tool.activate(param, option);

    // 显示拾取第1个管件提示信息
    WD_INFO_T("BranSplitAndMergeDialog","Please pick up the pipe fitting of upstream branch in 3D view!");
}

bool BranSplitAndMergeDialog::isEnableSplit(WD::WDNode& node, const bool& forward, const WD::DVec3& basePos, const double& distance)const
{
    if(forward)
    {
        auto pNextComNode = WD::WDBMDPipeUtils::NextPipeComponent(node.parent(), WD::WDNode::ToShared(&node), {"TUBI", "ATTA"});
        if (pNextComNode == nullptr)
        {
            auto pBranNode = node.parent();
            if(pBranNode == nullptr)
            {
                assert(false);
                return false;
            }
            // 当前管件为分支的最后一个管件。判断切割的距离是否小于管件出口点到分支尾之间的距离
            bool ok = false;
            auto v = pBranNode->getAttribute("Tposition WRT World").toDVec3(&ok);
            if(!ok)
                return false;

            auto branTPos = v;
            auto diatanceLeaveAndT = WD::DVec3::Distance(basePos, branTPos);
            if (distance > diatanceLeaveAndT)
            {
                return false;
            }
        }
        else
        {
            // 判断切割的距离是否小于基准点到下一个管件入口点的距离
            // 获取后一个管件的入口点坐标
            auto arrivePoint = pNextComNode->keyPoint(pNextComNode->getAttribute("Arrive").toInt());
            auto nextNodeArrivePos = arrivePoint->transformedPosition(pNextComNode->globalTransform());

            auto diatanceLeaveAndArrive = WD::DVec3::Distance(basePos, nextNodeArrivePos);
            if (distance > diatanceLeaveAndArrive)
            {
                return false;
            }
        }
    }
    else
    {
        // 判断是否可切割
        auto pPreComNode = WD::WDBMDPipeUtils::PrevPipeComponent(node.parent(), WD::WDNode::ToShared(&node), { "TUBI", "ATTA" });
        if (pPreComNode == nullptr)
        {
            // 当前节点为分支下的第1个节点。判断切割的距离是否小于管件入口点到分支头之间的距离
            auto pBranNode = node.parent();
            if (pBranNode == nullptr)
            {
                assert(false);
                return false;
            }

            bool ok = false;
            auto v = pBranNode->getAttribute("Hposition WRT World").toDVec3(&ok);
            if(!ok)
                return false;

            auto branHPos = v;
            auto diatanceArriveAndH = WD::DVec3::Distance(basePos, branHPos);
            if (distance > diatanceArriveAndH)
            {
                return false;
            }
        }
        else
        {
            // 判断切割的距离是否小于当前管件入口点到前一个管件出口点的距离
            auto leavePoint = pPreComNode->keyPoint(pPreComNode->getAttribute("Leave").toInt()); 
            auto prevNodeLeavePos = leavePoint->transformedPosition(pPreComNode->globalTransform());

            auto diatanceLeaveAndArrive = WD::DVec3::Distance(prevNodeLeavePos, basePos);
            if (distance > diatanceLeaveAndArrive)
            {
                return false;
            }
        }
    }

    return true;
}

void BranSplitAndMergeDialog::getBasedPosAndDirAndBore(const WD::WDNode& node, const bool& forward, const bool& spool, WD::DVec3& basedPos, WD::DVec3& basedPosDir, std::string& bore)const
{
    if(forward)
    {
         //获取当前管件出口点的坐标和朝向
        auto leavePoint = node.keyPoint(node.getAttribute("Leave").toInt());
        if(leavePoint != nullptr)
        {
            basedPosDir = leavePoint->transformedDirection(node.globalRSTransform());
            bore = leavePoint->bore();
        }
    }
    else
    {
        // 获取当前管件入口点的坐标和朝向
        auto arrivePoint = node.keyPoint(node.getAttribute("Arrive").toInt());
        if(arrivePoint != nullptr)
        {
            basedPosDir = arrivePoint->transformedDirection(node.globalRSTransform());
            bore = arrivePoint->bore();
        }
    }

    if(spool)
    {
        if (forward)
        {
            //获取当前管件出口点的坐标和朝向
            auto leavePoint = node.keyPoint(node.getAttribute("Leave").toInt());
            basedPos = leavePoint->transformedPosition(node.globalTransform());
        }
        else
        {
            // 获取当前管件入口点的坐标和朝向
            auto arrivePoint = node.keyPoint(node.getAttribute("Arrive").toInt());
            basedPos = arrivePoint->transformedPosition(node.globalTransform());
        }
    }
    else
    {
        basedPos = node.globalTranslation();
    }
}

WD::DVec3 BranSplitAndMergeDialog::getSplitPos(const WD::DVec3& BasedPos, const WD::DVec3& BasedPosDir, const double& distance)const
{
    return BasedPos + BasedPosDir * distance;
}

bool BranSplitAndMergeDialog::questionSplit(WD::WDNode& node, const bool& forward, const bool& spool, const double& distance)const
{    
    auto pBranNode = node.parent();
    if(pBranNode == nullptr)
        return false;
    auto pBm = _core.currentBM();
    if (pBm == nullptr)
    {
        assert(false);
        return false;
    }

    std::string questionInfo;
    std::string pipeComName = node.name();
    questionInfo.append(pipeComName);
    if (forward)
    {
        questionInfo.append(WD::WDTs("BranSplitAndMergeDialog", "Forward"));
    }
    else
    {
        questionInfo.append(WD::WDTs("BranSplitAndMergeDialog", "Backward"));
    }

    if (spool)
    {
        questionInfo.append(WD::WDTs("BranSplitAndMergeDialog", "Spool"));
    }
    else
    {
        questionInfo.append(WD::WDTs("BranSplitAndMergeDialog", "Dist"));
    }
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << distance;
    questionInfo.append(oss.str());
    questionInfo.append(WD::WDTs("BranSplitAndMergeDialog", "Split"));
    questionInfo.append(pBm->trT("BRAN"));
    std::string branName = pBranNode->name();
    questionInfo.append(branName);
    questionInfo.append("?");
    auto ret = WD_QUESTION(questionInfo);
    if (ret != 0)
    {
        return false;
    }
    return true;
}

WD::WDNode::Nodes BranSplitAndMergeDialog::collectionNodes(WD::WDNode& current) const
{
    WD::WDNode::Nodes result;

    auto pBran = current.parent();
    if(pBran == nullptr)
        return result;

    auto pComs = WD::WDBMDPipeUtils::PipeComponents(pBran);
    // 是否找到当前选中管件
    bool bFind = false;
    for(const auto& pChild : pComs)
    {
        if(pChild == nullptr)
            continue;

        if(pChild.get() == &current)
        {
            // 包含当前节点
            if(ui.checkBoxBackward->isChecked())
            {
                result.push_back(pChild);
            }
            bFind = true;
            continue;
        }
        if(bFind)
        {
            result.push_back(pChild);
        }
    }

    return result;
}

WD::WDNode::SharedPtr BranSplitAndMergeDialog::createBranNode(WD::WDNode& branNode)const
{
    auto parent = branNode.parent();
    if(parent == nullptr)
        return nullptr;

    auto pBm = _core.currentBM();
    if (pBm == nullptr)
    {
        assert(false);
        return nullptr;
    }

    // 新建分支节点
    WD::WDNode::SharedPtr pNewBranNode = pBm->create(parent, "BRAN", branNode.nextBrother());
    if (pNewBranNode == nullptr)
    {
        assert(false);
        return nullptr;
    }

    // 设置默认颜色
    pBm->colorTable().setNodeColor(*pNewBranNode);

    // 先拷贝原分支的所有属性值
    pNewBranNode->copy(&branNode);
    // 设置分支的默认名称
    pNewBranNode->setAttribute("Name", std::string(""));

    // 原分支处于激活状态，新建的分支是未激活的，所以移除新建分支的激活标志
    removeNodeActiveFlag(*pNewBranNode);
    return pNewBranNode;
}

void BranSplitAndMergeDialog::addNodeActiveFlag(WD::WDNode& node)const
{
    auto flags = node.flags();
    flags.addFlag(WD::WDNode::F_Active);
    node.setFlags(flags);
}

void BranSplitAndMergeDialog::removeNodeActiveFlag(WD::WDNode& node)const
{
    auto flags = node.flags();
    flags.removeFlag(WD::WDNode::F_Active);
    node.setFlags(flags);
}

void BranSplitAndMergeDialog::setBranActiveFlag(WD::WDNode& branchNode, bool addFlag)
{
    WD::WDNode::RecursionHelpter(branchNode, [=](WD::WDNode& node) {
        if (addFlag)
        {
            addNodeActiveFlag(node);
        }
        else
        {
            removeNodeActiveFlag(node);
        }
    });
}

void BranSplitAndMergeDialog::addNode2Scene(WD::WDNode::SharedPtr pNode)
{       
    _core.scene().add(pNode);
}

void BranSplitAndMergeDialog::retranslateUi()
{
    Trs("BranSplitAndMergeDialog"
    , static_cast<QDialog*>(this)
    , ui.groupBoxSplit
    , ui.groupBoxMerge
    , ui.pushButtonMerge
    , ui.pushButtonSplit
    , ui.labelDistance
    , ui.checkBoxSpool
    , ui.checkBoxDist
    , ui.plainTextEdit
    , ui.checkBoxForward
    , ui.checkBoxBackward
    );
}