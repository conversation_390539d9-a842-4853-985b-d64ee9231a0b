#pragma once

#include <QDialog>
#include "ui_BranSplitAndMergeDialog.h"
#include <QHideEvent>
#include "WDTranslate.h"
#include "core/WDCore.h"
#include "core/nodeTree/WDNodeList.h"
#include "core/viewer/capturePositioning/WDCapturePositioning.h"

class BranSplitAndMergeDialog 
    :public QDialog
    , public WD::WDCapturePositioningMonitor
{
    Q_OBJECT
public:
    BranSplitAndMergeDialog(WD::WDCore& core, QWidget* parent = nullptr);
    ~BranSplitAndMergeDialog();
protected:
    virtual void hideEvent(QHideEvent* e)override;
private:
    virtual void onResult(const WD::WDCapturePositioningResult& result
        , bool& existFlag
        , const WD::WDCapturePositioning& sender) override;
    virtual void onDeactived(const WD::WDCapturePositioning& sender) override;

private slots:
    /**
     * @brief 点击分割按钮槽函数
    */
    void slotPushBtnSplitClicked();
    /**
     * @brief 点击合并按钮槽函数
    */
    void slotPushBtnMergeClicked();
private:

    /**
     * @brief 判断node沿forward方向距离distance是否可以进行切割
     * @param node 当前选中的管件
     * @param forward true：沿流向方向 false:逆流向方向
     * @param basePos 基准点（某个关键点p0 、p1、p2）
     * @param distance 离基准点的距离
     * @return true：可以进行切割;false:不可以进行切割
    */
    bool isEnableSplit(WD::WDNode& node, const bool& forward, const WD::DVec3& basePos, const double& distance) const;
     /**
      * @brief 获取基准点的坐标和朝向，获取切割处管径
      * @param node 作为输入，当前选中的管件节点
      * @param forward 作为输入，true：沿流向方向 false:逆流向方向
      * @param spool  作为输入，true：端面false:中心点
      * @param basedPos 作为输出，关键点坐标
      * @param basedPosDir 作为输出，关键点朝向
      * @param bore 作为输出，关键点的管径
     */
     void getBasedPosAndDirAndBore(const WD::WDNode& node, const bool& forward, const bool& spool, WD::DVec3& basedPos, WD::DVec3& basedPosDir, std::string& bore)const;
     /**
      * @brief 获取切割点的坐标（也就是原分支的尾，新分支的头）
      * @param basedPos 关键点坐标
      * @param basedPosDir 关键点朝向
      * @param distance 离关键点的距离
      * @return 切割的位置坐标
     */
     WD::DVec3 getSplitPos(const WD::DVec3& basedPos, const WD::DVec3& basedPosDir, const double& distance)const;
     /**
      * @brief 切割的询问信息
      * @param pNode 当前选中的管件节点
      * @param forward true：沿流向方向 false:逆流向方向
      * @param spool true：端面false:中心点
      * @param distance 离关键点的距离
      * @return 用户界面选择结果 treu：进行切割 false：不进行切割
     */
     bool questionSplit(WD::WDNode& node, const bool& forward, const bool& spool, const double& distance)const;
    /**
     * @brief 收集新分支下的管件
     * @param current 选中的当前管件节点
    */
    WD::WDNode::Nodes collectionNodes(WD::WDNode& current) const;
    /**
     * @brief 创建新的分支节点，节点的各个属性与pCurBran节点一致，且新分支节点在pCurBran节点之后
     * @param branNode 进行切割操作的分支节点
     * @return 新建的分支节点
    */
    WD::WDNode::SharedPtr createBranNode(WD::WDNode& branNode)const;
    /**
     * @brief 添加node的激活标志
     * @param node
    */
    void addNodeActiveFlag(WD::WDNode& node)const;
    /**
     * @brief 移除node的激活标志
     * @param node 
    */
    void removeNodeActiveFlag(WD::WDNode& node)const;
    /**
     * @brief 设置branchNode节点及子孙节点的激活标志
     * @param branchNode 
     * @param addFlag true:添加激活标志 false：移除激活标志
    */
    void setBranActiveFlag(WD::WDNode& branchNode, bool addFlag);
    /**
     * @brief 将pNode节点添加到场景中
     * @param pNode 序添加到场景中的节点
    */
    void addNode2Scene(WD::WDNode::SharedPtr pNode);
    /**
    * @brief 界面中文翻译
    */
    void retranslateUi();
private:
    Ui::BranSplitAndMergeDialog ui;
    // 列表管理对象
    WD::WDCore& _core;
    // 合并的分支节点                       
    std::vector<WD::WDNode::WeakPtr> _mergeBrans;
};

