#pragma once

#include <QDialog>
#include "ui_PipeSlopeDialog.h"
#include "WDCore.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiListSelectHelpter.h"
#include "core/undoRedo/WDUndoCommand.h"

/**
 * @brief 管道放坡界面
*/
class PipeSlopeDialog : public QDialog
{
    Q_OBJECT

private:
    Ui::PipeSlopeDialog         ui;
    WD::WDCore&                 _core;
    // 节点选择窗口
    UiListSelectHelpter*        _listSelectWidget;

public:
    PipeSlopeDialog(WD::WDCore& core, QWidget *parent = Q_NULLPTR);
    virtual ~PipeSlopeDialog();
protected:
    virtual void showEvent(QShowEvent* evt) override;
    virtual void hideEvent(QHideEvent* evt) override;

private slots:
    /**
     * @brief 操作类型更改通知响应
    */
    void slotTypeChanged(bool checked);
    /**
     * @brief 确认按钮按下通知响应
    */
    void slotOkClicked(bool checked);
    /**
     * @brief 取消按钮按下通知响应
    */
    void slotCancelClicked(bool checked);

    /**
     * @brief 当前选中节点发生变化 通知响应
    */
    void slotSelectChanged(UiListSelectHelpter::Nodes preNodes
        , UiListSelectHelpter::Nodes currNodes
        , std::optional<std::string> preMode
        , std::optional<std::string> currMode);

private:
    /**
     * @brief 执行放坡操作
     * @param node 放坡节点
     * @param slopeRate 坡度
     */
    void    execSlope(WD::WDNode& node, double slopeRate);
    /**
     * @brief 管道放坡
     * @param pipe 管道节点
     * @param slopeRate 坡度
     */
    void    slopePipe(WD::WDNode& pipe, double slopeRate, WD::WDUndoCommand* pCmd);
    /**
    * @brief 分支放坡
    * @param bran 分支节点
    * @param slopeRate 坡度
    */
    void    slopeBran(WD::WDNode& bran, double slopeRate, WD::WDUndoCommand* pCmd);
    /**
    * @brief 对管件进行放坡，根据流向对从该管件开始直到分支结束的所有管件进行放坡
    * @param component 管件节点
    * @param slopeRate 坡度
    */
    void    slopeComponent(WD::WDNode& component, double slopeRate, WD::WDUndoCommand* pCmd);

    /**
     * @brief 空分支放坡
     * @param bran 空分支
     * @param slopeRate 坡度
     */
    void    slopeEmptyBran(WD::WDNode& bran, double slopeRate);
    /**
     * @brief 管件放坡
     * @param pCom 当前管件
     * @param pNext 相邻管件
     * @param slopeRate 坡度
     * @param mapClosed 管件是否紧挨
     * @return 放坡是否成功
     */
    bool    slopeInstance(WD::WDNode::SharedPtr pCom, WD::WDNode::SharedPtr pNext, double slopeRate
        , const std::map<WD::WDUuid, bool>& mapClosed);

private:
    /**
    * @brief 获取坡度
    */
    double  getSlopeRate();

    /**
    * @brief 获取放坡first的连接点局部坐标
    * @param first first节点
    * -nullptr 根据子类决定返回头/尾
    * @param pBran 分支节点，默认不传，如果传了则视为获取分支的连接点局部坐标first不生效可传空
    */
    WD::DVec3   getFirstConnectPosition(WD::WDNode::SharedPtr first, WD::WDNode::SharedPtr pBran = nullptr);
    /**
    * @brief 获取放坡second的连接点局部坐标
    * @param second second节点
    * -nullptr 根据子类决定返回头/尾
    * @param pBran 分支节点，默认不传，如果传了则视为获取分支的连接点局部坐标first不生效可传空
    */
    WD::DVec3   getSecondConnectPosition(WD::WDNode::SharedPtr second, WD::WDNode::SharedPtr pBran = nullptr);
    /**
    * @brief 获取放坡second的连接点局部朝向
    * @param second second节点
    * -nullptr 根据子类决定返回头/尾
    * @param pBran 分支节点，默认不传，如果传了则视为获取分支的连接点局部朝向first不生效可传空
    */
    WD::DVec3   getSecondConnectDirection(WD::WDNode::SharedPtr second, WD::WDNode::SharedPtr pBran = nullptr);
    /**
    * @brief 设置放坡second的连接点局部坐标
    * @param second second节点
    * -nullptr 根据子类决定设置头/尾
    * @param pBran 分支节点，默认不传，如果传了则视为设置分支的连接点局部坐标first不生效可传空
    */
    void        setSecondConnectPosition(WD::WDNode::SharedPtr second, const WD::DVec3& pos, WD::WDNode::SharedPtr pBran = nullptr);
    /**
    * @brief 相邻管件是否紧挨
    * @param first 第一个管件
    * @param second 第二个管件
    */
    bool    prevIsCloseToNext(WD::WDNode::SharedPtr first, WD::WDNode::SharedPtr second);
    /**
    * @brief 相邻管件紧挨
    * @param first 第一个管件
    * @param second 第二个管件
    */
    void    prevCloseToNext(WD::WDNode::SharedPtr first, WD::WDNode::SharedPtr second);
    /**
    * @brief 管件连接处朝向向量对齐
    * @param first 第一个管件
    * @param second 第二个管件
    */
    void    connecAlign(WD::WDNode::SharedPtr first, WD::WDNode::SharedPtr second);
    /**
     * @brief 高度偏移
     * @param pCom 当前管件
     * @param pNext 相邻管件
     * @param slopeRate 坡度
     */
    void    doOffset(WD::WDNode::SharedPtr pCom, WD::WDNode::SharedPtr pNext, double slopeRate);
    /**
    * @brief 管件与相邻管件进行放坡
    * @param pCom 当前管件
    * @param pNext 相邻管件节点
    * @param pNext 相邻管件节点
    */
    void    slope(WD::WDNode::SharedPtr pCom, WD::WDNode::SharedPtr pNext);
    /**
    * @brief 获取管件与相邻管件的水平连接距离
    * @param pCom 当前管件
    * @param pNext 相邻管件
    */
    double  horiDistWithNext(WD::WDNode::SharedPtr pCom, WD::WDNode::SharedPtr pNext);
    /**
     * @brief 根据管件列表获取紧挨map
     * @param pComponents 管件列表
     * @param reverse 是否逆序
     */
    std::map<WD::WDUuid, bool> mapClosed(const WD::WDNode::Nodes& pComponents, bool reverse);

private:
    /**
    * @brief 每个管件放坡之前处理
    * @param pCom 管件节点
    */
    bool    onSlopeBefore(WD::WDNode& component);
    /**
    * @brief 每个管件放坡完成通知响应
    * @param pCom 管件节点
    * @param slopingDir 放坡方向
    */
    bool    onSlopeFinished(WD::WDNode& component);

private:
    // 界面翻译
    void    retranslateUi();
};
