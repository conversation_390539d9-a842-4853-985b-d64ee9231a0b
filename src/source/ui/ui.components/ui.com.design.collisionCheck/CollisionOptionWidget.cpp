#include    "CollisionOptionWidget.h"
#include    <QColorDialog>
#include    <sstream>
#include    "core/viewer/WDViewer.h"
#include    "core/scene/WDScene.h"
#include    "core/material/WDDrawHelpter.h"
#include    "core/businessModule/design/WDBMDesign.h"
#include    "../../wizDesignerApp/UiInterface/UiTranslate.h"



CollisionOptionWidget::CollisionOptionWidget(WD::WDCore& app, QWidget *parent)
    : QWidget(parent), _app(app)
{
    ui.setupUi(this);

    // 初始化碰撞公差(Tolerances)相关界面
    this->initTolerancesUi();
    // 初始化碰撞选项(Clash Options)相关界面
    this->initClashOptionsUi();
    // 初始化碰撞显示(Presentation)相关界面
    this->initPresentationUi();
    // 初始化碰撞颜色(Clash Colours)相关界面
    this->initClashColoursUi();
    // 初始化碰撞过滤(Clashes Ignored Within)相关界面
    this->initClashIgnoredWithinUi();

    // 界面翻译
    this->retranslateUi();
}
CollisionOptionWidget::~CollisionOptionWidget()
{
}

void CollisionOptionWidget::prepareOptionsData()
{
    // 准备误差数据
    _tolerances.overlap = ui.doubleSpinBoxOverlap->value();
    _tolerances.touchGap = ui.doubleSpinBoxTouchGap->value();
    _tolerances.clearance = ui.doubleSpinBoxclearance->value();
    //报告碰撞的最大距离(超过该距离将不产生报告)
    _tolerances.maxN = WD::Max(_tolerances.touchGap, _tolerances.clearance);
    // 根据碰撞过滤勾选的内容缓存过滤类型列表
    _ignoredWithinTypes.clear();
    for (auto pButton : _ignoredWithinButtons)
    {
        if (pButton == nullptr)
            continue;
        if (!pButton->isChecked())
            continue;
        QString str = getUData(*pButton);
        if (str.isEmpty())
            continue;
        _ignoredWithinTypes.insert(str);
    }
    // 给过滤的父列表提前分配下内存
    _filterObject.filterParents.reserve(_ignoredWithinTypes.size() * 2);
    // 预分配内存
    _filterObject.connectionNodes.reserve(100);
}

void CollisionOptionWidget::prepareFilter(const WD::WDNode& sub)
{
    _filterObject.pSubject = &sub;
    _filterObject.pBranchNode = nullptr;
    _filterObject.comIndexInBranch = -1;
    _filterObject.filterParents.clear();
    _filterObject.branchIgType = Included;
    _filterObject.connectionsIgType = Included;
    _filterObject.connectionNodes.clear();
    
    // 是否需要缓存分支节点(用来加速过滤)
    bool needBranchNode = false;
    // 是否需要缓存当前主体在分支中的索引(用来加速过滤)
    bool needComIndexInBranch = false;
    // 分支内碰撞的选项
    int clashedBranshType = ui.comboBoxClashesBranch->currentData().toInt();
    switch (clashedBranshType)
    {
        // 包含分支内碰撞
    case Included:
        {
            // !注意: 这里做一个调整，为了与PDMS保持结果一致，这里的包含选项的效果默认调整为"忽略相邻的"
            _filterObject.branchIgType = IgnoredAdjacent;
            if (!needBranchNode) // 需要存储分支节点来忽略相邻碰撞
                needBranchNode = true;
            if (!needComIndexInBranch) // 需要存储管件所属分支的索引
                needComIndexInBranch = true;
        }
        break;
        // 忽略分支内碰撞
    case Ignored:
        {
            _filterObject.branchIgType = Ignored;
            if (!needBranchNode) // 需要存储分支节点来忽略分支内碰撞
                needBranchNode = true;
        }
        break;
        // 忽略同一分支内相邻管件(直管)的碰撞
    case IgnoredAdjacent:
        {
            _filterObject.branchIgType = IgnoredAdjacent;
            if (!needBranchNode) // 需要存储分支节点来忽略相邻碰撞
                needBranchNode = true;
            if (!needComIndexInBranch) // 需要存储管件所属分支的索引
                needComIndexInBranch = true;
        }
        break;
    default:
        {
            assert(false && "出现了意外的选项!");
            _filterObject.branchIgType = Included;
        }
        break;
    }
    // 连接选项
    int connectionsType = ui.comboBoxConnections->currentData().toInt();
    switch (connectionsType)
    {
        // 包含连接的碰撞
    case Included:
        {
            _filterObject.connectionsIgType = Included;
        }
        break;
        // 忽略连接的碰撞
    case Ignored:
        {
            _filterObject.connectionsIgType = Ignored;
            if(!needBranchNode) // 需要存储分支节点来忽略连接的碰撞
                needBranchNode = true;
            // 查询到连接的节点列表
            auto refs = sub.getAttribute("Conntections").toNodeRefVector();
            for (const auto& ref : refs) 
            {
                if (ref.refNode() == nullptr)
                    continue;
                _filterObject.connectionNodes.push_back(ref.refNode());
            }
        }
        break;
        // 等级的忽略, 先不做，没给需求
    case WithSpecIgnored:
        {
            _filterObject.connectionsIgType = WithSpecIgnored;
        }
        break;
    default:
        {
            assert(false && "出现了意外的选项!");
            _filterObject.connectionsIgType = Included;
        }
        break;
    }
    // 先根据主体对象查找对应类型的父节点列表
    const WD::WDNode* pParent = &sub;
    while (pParent != nullptr)
    {
        const QString type = QString::fromUtf8(pParent->type().data());
        // 如果是分支节点类型，在这里保存，需要处理分支内碰撞过滤
        if (needBranchNode && type == "BRAN")
            _filterObject.pBranchNode = pParent;
        // 查找是否被过滤
        auto fItr = _ignoredWithinTypes.find(type);
        if (fItr != _ignoredWithinTypes.end())
            _filterObject.filterParents.push_back(pParent);
        // 继续向上遍历
        pParent = pParent->parent().get();
    }
    // 如果是分支的子节点(说明是管件和直管节点)，这里存储对应的索引
    if (needComIndexInBranch 
        && _filterObject.pBranchNode != nullptr // 这里的分支已经在前一步存储了，所以这里只需要保证不为空即可)
        && sub.parent().get() == _filterObject.pBranchNode)
    { 
        for (size_t i = 0; i < sub.parent()->childCount(); ++i)
        {
            auto pChild = sub.parent()->childAt(i);
            if (pChild == nullptr)
                continue;
            if (pChild.get() == &sub)
            {
                _filterObject.comIndexInBranch = static_cast<int>(i);
                break;
            }
        }
    }
    // 如果还包含分支，则需要将分支连接也缓存下来
    if (_filterObject.pBranchNode != nullptr
        && sub.parent().get() == _filterObject.pBranchNode) 
    {
        // 查询到连接的节点列表
        auto refs = sub.getAttribute("Conntections").toNodeRefVector();
        for (const auto& ref : refs)
        {
            if (ref.refNode() == nullptr)
                continue;
            _filterObject.connectionNodes.push_back(ref.refNode());
        }
    }
}
bool CollisionOptionWidget::filter(const WD::WDNode& sub, const WD::WDNode& obj) const
{
    if (_filterObject.pSubject != &sub)
        return false;

    /************** 1.使用 碰撞过滤 来进行过滤 ****************/
    for (auto pFilterParent : _filterObject.filterParents)
    {
        if (pFilterParent == nullptr)
            continue;
        // 如果客体是pFilterParent的后代节点，则证明需要过滤掉
        if (obj.isDescendant(*pFilterParent))
            return true;
    }
    /************** 2.使用 碰撞选项/分支内碰撞（Clashes within branch）来进行过滤 ****************/
    if (_filterObject.pBranchNode != nullptr)
    {
        const auto& pBranch = _filterObject.pBranchNode;
        switch (_filterObject.branchIgType)
        {
            // 忽略分支内碰撞, 如果主体和客体均属于该分支，则过滤掉
        case CollisionOptionWidget::Ignored:
        {
            if (obj.isDescendant(*pBranch))
                return true;
        }
        break;
            // 如果主体和客体均属于该分支，且是前后元件，则过滤掉
        case CollisionOptionWidget::IgnoredAdjacent:
        {
            // 如果客体也是当前分支的子节点并且主体在分支下的索引有效
            if (obj.parent().get() == pBranch
                && _filterObject.comIndexInBranch >= 0
                && _filterObject.comIndexInBranch < pBranch->childCount())
            {
                // 判断客体是否是主体的前一个管件
                int prevIdx = _filterObject.comIndexInBranch - 1;
                while (prevIdx >= 0) 
                {
                    auto pPrev = pBranch->childAt(prevIdx);
                    if (pPrev == nullptr)
                    {
                        prevIdx--;
                        continue;
                    }
                    // 跳过附点
                    if (pPrev->isType("ATTA")) 
                    {
                        prevIdx--;
                        continue;
                    }
                    // 校验是否前一个
                    else if (pPrev.get() == &obj)
                    {
                        return true;
                    }
                    // 否则证明不是前一个，直接跳出
                    else
                    {
                        break;
                    }
                }
                // 判断客体是否是主体的后一个管件
                int nextIdx = _filterObject.comIndexInBranch + 1;
                while (nextIdx < pBranch->childCount())
                {
                    auto pNext = pBranch->childAt(nextIdx);
                    if (pNext == nullptr)
                    {
                        nextIdx++;
                        continue;
                    }
                    // 跳过附点
                    if (pNext->isType("ATTA"))
                    {
                        nextIdx++;
                        continue;
                    }
                    // 校验是否前一个
                    else if (pNext.get() == &obj)
                    {
                        return true;
                    }
                    // 否则证明不是前一个，直接跳出
                    else
                    {
                        break;
                    }
                }
            }
        }
        break;
        default:
            break;
        }
    }
    /************** 3.使用 碰撞选项/连接（Connections）来进行过滤 ****************/
    switch (_filterObject.connectionsIgType)
    {
        // 忽略, 如果主体分支的连接是客体或者客体分支的连接是主体，则忽略
    case Ignored:
        {
            // 先查找主体的连接是否包含客体
            for (auto pNode : _filterObject.connectionNodes) 
            {
                if (pNode == nullptr)
                    continue;
                // 如果连接中包含客体，则忽略
                if (pNode.get() == &obj)
                    return true;
            }
            // 获取客体中的连接
            auto refs = sub.getAttribute("Conntections").toNodeRefVector();
            for (const auto& ref : refs) 
            {
                if (ref.refNode().get() == _filterObject.pSubject)
                    return true;
            }
            // 如果是分支的子节点, 分支
            if (sub.parent() != nullptr && sub.parent()->isType("BRAN")) 
            {
                auto parRefs = sub.parent()->getAttribute("Conntections").toNodeRefVector();
                for (const auto& ref : parRefs)
                {
                    if (ref.refNode().get() == _filterObject.pSubject)
                        return true;
                }
            }
        }
        break;
        // 等级的忽略, 先不做，没给需求
    case WithSpecIgnored:
        {

        }
        break;
    default:
        break;
    }
    return false;
}
WD::Color CollisionOptionWidget::subjectColor() const
{
    QColor rColor = getLabelColor(*ui.labelColorMajor);
    return WD::Color(
          static_cast<WD::byte>(rColor.red())
        , static_cast<WD::byte>(rColor.green())
        , static_cast<WD::byte>(rColor.blue())
        , static_cast<WD::byte>(rColor.alpha()));
}
WD::Color CollisionOptionWidget::objectColor() const
{
    QColor rColor = getLabelColor(*ui.labelColorObstruction);
    return WD::Color(
        static_cast<WD::byte>(rColor.red())
        , static_cast<WD::byte>(rColor.green())
        , static_cast<WD::byte>(rColor.blue())
        , static_cast<WD::byte>(rColor.alpha()));
}
CollisionOptionWidget::RFlags CollisionOptionWidget::rFlags() const
{
    RFlags flags = RFlag::RF_None;
    flags.setFlag(RFlag::RF_Subject, ui.checkBoxItemAid->isChecked());
    flags.setFlag(RFlag::RF_Object, ui.checkBoxObstructionAid->isChecked());
    flags.setFlag(RFlag::RF_ClashPosition, ui.checkBoxPositionAid->isChecked());
    return flags;
}
bool CollisionOptionWidget::ignoreTouch() const
{
    int type = ui.comboBoxTouchesAre->currentData().toInt();
    return type == InIgType::Ignored;
}

void CollisionOptionWidget::initTolerancesUi()
{
    // 触碰间隙（Touch Gap）：默认为0
    ui.doubleSpinBoxTouchGap->setValue(0.0);
    // 重叠量（Overlap）：默认为2
    ui.doubleSpinBoxOverlap->setValue(2);
    // 误差（clearance）：默认为0
    ui.doubleSpinBoxclearance->setValue(0.0);
}
void CollisionOptionWidget::initClashOptionsUi()
{
    // 触碰（Touches are） : 包含（included）、忽略（ignored）；表示在“碰撞”列表中是否忽略。
    ui.comboBoxTouchesAre->addItem("included", InIgType::Included);
    ui.comboBoxTouchesAre->addItem("ignored", InIgType::Ignored);
    // 分支内碰撞（Clashes within branch）：包含（are included）、忽略相邻的（ignored adjacent）、忽略（are ignored）；
    // 表示在同一个分支各元件间的“碰撞”是否忽略，“忽略相邻里”表示前后元件间的碰撞忽略。
    ui.comboBoxClashesBranch->addItem("are included", InIgType::Included);
    ui.comboBoxClashesBranch->addItem("ignored adjacent", InIgType::IgnoredAdjacent);
    ui.comboBoxClashesBranch->addItem("are ignored", InIgType::Ignored);
    // 连接（Connections）:包含（are included）、忽略（are ignored）、带有等级的忽略（with spec are ignored）；
    // 表示有连接关系的元件或分支在“碰撞”单中是否忽略，包括分支头尾/三通/接管座/管嘴，例如：分支头和三通有连接关系，也可能存在碰撞。
    ui.comboBoxConnections->addItem("are included", InIgType::Included);
    ui.comboBoxConnections->addItem("are ignored", InIgType::Ignored);
    // 带有等级的忽略先不做
    ui.comboBoxConnections->addItem("with spec are ignored", InIgType::WithSpecIgnored);

    // 碰撞坐标中点（Clash midpoint position）；勾选在三维视图中显示碰撞中点并有坐标系；不勾选在三维视图中显示碰撞边点并有坐标系。
    ui.checkBoxClashMidpoint->setChecked(false);

    // 分支内碰撞（Clashes within branch) 需要与 BRAN过滤框联动
    connect(ui.comboBoxClashesBranch
        , QOverload<int>::of(& QComboBox::currentIndexChanged)
        , this
        , [this](int) 
        {
            int type = ui.comboBoxClashesBranch->currentData().toInt();
            switch (type)
            {
            case InIgType::Ignored:
                ui.checkBoxBRAN->setChecked(true);
                break;
            default:
                ui.checkBoxBRAN->setChecked(false);
                break;
            }
        });
}
void CollisionOptionWidget::initPresentationUi()
{
    // 碰撞/被碰组（Group Clash/Obstruction）；主要是对碰撞结果进行分类展示，第一级为主体，第二级为客体。
    ui.checkBoxObstruction->setChecked(false);
    connect(ui.checkBoxObstruction
        , &QCheckBox::clicked
        , this
        , [this](bool checked)
        {
            emit sigGroupClashObstructionChecked(checked);
        });

    // 碰撞项辅助（Clash Item Aid）；主要是在三维视图中显示碰撞元件的名称。
    ui.checkBoxItemAid->setChecked(true);
    connect(ui.checkBoxItemAid
        , &QCheckBox::clicked
        , this
        , [this](bool checked)
        {
            WDUnused(checked);
            _app.needRepaint();
        });

    // 被碰项辅助（Clash Obstruction Aid）；主要是在三维视图中显示被碰元件的名称。
    ui.checkBoxObstructionAid->setChecked(true);
    connect(ui.checkBoxObstructionAid
        , &QCheckBox::clicked
        , this
        , [this](bool checked)
        {
            WDUnused(checked);
            _app.needRepaint();
        });

    // 碰撞坐标辅助（Clash Position Aid）;主要是在三维视图中显示碰撞坐标。
    ui.checkBoxPositionAid->setChecked(true);
    connect(ui.checkBoxPositionAid
        , &QCheckBox::clicked
        , this
        , [this](bool checked)
        {
            WDUnused(checked);
            _app.needRepaint();
        });
}
void CollisionOptionWidget::initClashColoursUi()
{
    // 设置碰撞主体颜色
    setLabelColor(*ui.labelColorMajor, QColor(255, 0, 0));
    // 设置碰撞客体颜色
    setLabelColor(*ui.labelColorObstruction, QColor(0, 0, 255));
    // 设置视图背景颜色
    const auto& color = _app.viewer().backColor();
    QColor viewerColor = QColor(color.r, color.g, color.b, color.a);
    
    setLabelColor(*ui.labelColorBackground, viewerColor);

    connect(ui.pushButtonMajorSelect
        , &QPushButton::clicked
        , this
        , [this]() 
        {
            // 颜色对话框获取颜色
            QColor color = QColorDialog::getColor(Qt::red
                , this
                , ""
                , QColorDialog::ShowAlphaChannel);
            // 点击关闭或取消颜色对话框则直接返回，不改变颜色
            if (!color.isValid())
                return;
            setLabelColor(*ui.labelColorMajor, color);

            // 发送信号
            emit sigColorChanged();

            // 触发重绘
            _app.needRepaint();
        } );
    connect(ui.pushButtonObstructionSelect
        , &QPushButton::clicked
        , this
        , [this]()
        {
            // 颜色对话框获取颜色
            QColor color = QColorDialog::getColor(Qt::red
                , this
                , ""
                , QColorDialog::ShowAlphaChannel);
            if (!color.isValid())
                return;
            setLabelColor(*ui.labelColorObstruction, color);

            // 发送信号
            emit sigColorChanged();

            // 触发重绘
            _app.needRepaint();
        });
    connect(ui.pushButtonViewSelect
        , &QPushButton::clicked
        , this
        , [this]()
        {
            // 颜色对话框获取颜色
            QColor color = QColorDialog::getColor(Qt::red
                , this
                , ""
                , QColorDialog::ShowAlphaChannel);
            if (!color.isValid())
                return;
            setLabelColor(*ui.labelColorBackground, color);
            WD::Color vColor = WD::Color(
                  static_cast<WD::byte>(color.red())
                , static_cast<WD::byte>(color.green())
                , static_cast<WD::byte>(color.blue())
                , static_cast<WD::byte>(color.alpha()));
            // 设置视图背景色
            _app.viewer().setBackColor(vColor);
            _app.needRepaint();
        });
}
void CollisionOptionWidget::initClashIgnoredWithinUi()
{
    // 设置类型名称属性,方便后续使用
    setUData(*ui.checkBoxPIPE, "PIPE");
    _ignoredWithinButtons.push_back(ui.checkBoxPIPE);
    setUData(*ui.checkBoxBRAN, "BRAN");
    _ignoredWithinButtons.push_back(ui.checkBoxBRAN);
    setUData(*ui.checkBoxHANG, "HANG");
    _ignoredWithinButtons.push_back(ui.checkBoxHANG);
    setUData(*ui.checkBoxREST, "REST");
    _ignoredWithinButtons.push_back(ui.checkBoxREST);
    setUData(*ui.checkBoxEQUI, "EQUI");
    _ignoredWithinButtons.push_back(ui.checkBoxEQUI);
    setUData(*ui.checkBoxTMPL, "TMPL");
    _ignoredWithinButtons.push_back(ui.checkBoxTMPL);
    setUData(*ui.checkBoxCWBRAN, "CWBRAN");
    _ignoredWithinButtons.push_back(ui.checkBoxCWBRAN);
    setUData(*ui.checkBoxSTRU, "STRU");
    _ignoredWithinButtons.push_back(ui.checkBoxSTRU);
    setUData(*ui.checkBoxSUBS, "SUBS");
    _ignoredWithinButtons.push_back(ui.checkBoxSUBS);
    setUData(*ui.checkBoxFRMW, "FRMW");
    _ignoredWithinButtons.push_back(ui.checkBoxFRMW);
    setUData(*ui.checkBoxSBFR, "SBFR");
    _ignoredWithinButtons.push_back(ui.checkBoxSBFR);
    setUData(*ui.checkBoxSCTN, "SCTN");
    _ignoredWithinButtons.push_back(ui.checkBoxSCTN);
    setUData(*ui.checkBoxGENSEC, "GENSEC");
    _ignoredWithinButtons.push_back(ui.checkBoxGENSEC);
    setUData(*ui.checkBoxSITE, "SITE");
    _ignoredWithinButtons.push_back(ui.checkBoxSITE);
    setUData(*ui.checkBoxZONE, "ZONE");
    _ignoredWithinButtons.push_back(ui.checkBoxZONE);
    setUData(*ui.checkBoxPR, "PR");
    _ignoredWithinButtons.push_back(ui.checkBoxPR);
    // 增加互斥组来做按钮按钮之间的互斥
    _buttonGroups.push_back(new QButtonGroup(this));
    _buttonGroups.back()->setExclusive(true);
    _buttonGroups.back()->addButton(ui.checkBoxSITE);
    // 设置默认选中的按钮
    ui.checkBoxEQUI->setChecked(true);
    ui.checkBoxTMPL->setChecked(true);
    ui.checkBoxCWBRAN->setChecked(true);
    ui.checkBoxSCTN->setChecked(true);
    ui.checkBoxGENSEC->setChecked(true);
    // 勾选BRAN时， Clashes within branch(分支内碰撞), 自动改为 "are ignored"(忽略)
    // 去掉勾选BRAN时，Clashes within branch(分支内碰撞), 自动改为 "are included"(包含)
    connect(ui.checkBoxBRAN, &QCheckBox::clicked, this, [this](bool bChecked)
        {
            InIgType type = bChecked ? InIgType::Ignored: InIgType::Included;
            int index = -1;
            for (int i = 0; i < ui.comboBoxClashesBranch->count(); ++i) 
            {
                if (ui.comboBoxClashesBranch->itemData(i).toInt() == type)
                {
                    index = i;
                    break;
                }
            }
            if (index != -1)
                ui.comboBoxClashesBranch->setCurrentIndex(index);
        });
}

void CollisionOptionWidget::setLabelColor(QLabel& label, const QColor& color)
{
    QString colorName = color.name(QColor::HexArgb);
    QString style = QString("QLabel{background-color:%1;}").arg(colorName);
    label.setStyleSheet(style);
    setUData(label, colorName);
}
QColor CollisionOptionWidget::getLabelColor(const QLabel& label) const
{
    QString colorName = getUData(label);
    return QColor(colorName);
}

void CollisionOptionWidget::retranslateUi()
{
    Trs("CollisionOptionWidget"
        , static_cast<QWidget*>(this)

        , ui.groupBoxTolerances
        , ui.labelTouchGap
        , ui.labelOverlap
        , ui.labelClearance

        , ui.groupBoxClashOptions
        , ui.labelTouchesAre
        , ui.comboBoxTouchesAre
        , ui.labelClashesBranch
        , ui.comboBoxClashesBranch
        , ui.labelConnections
        , ui.comboBoxConnections
        , ui.checkBoxClashMidpoint

        , ui.groupBoxPresentation
        , ui.checkBoxObstruction
        , ui.checkBoxItemAid
        , ui.checkBoxObstructionAid
        , ui.checkBoxPositionAid

        , ui.groupBoxClashColours
        , ui.labelMajorClashItem
        , ui.pushButtonMajorSelect
        , ui.labelObstructionItem
        , ui.pushButtonObstructionSelect
        , ui.labelViewBackground
        , ui.pushButtonViewSelect

        , ui.groupBoxClashesIgnored

        , ui.groupBoxSaveRestore
        , ui.pushButtonResetDefaults
        , ui.labelUserSettings
        , ui.pushButtonSave
        , ui.pushButtonRestore
        );

    auto& desiMgr = _app.getBMDesign();
    // 翻译类型按钮的文本
    for (auto pButton : _ignoredWithinButtons)
    {
        if (pButton == nullptr)
            continue;
        std::string btnText = pButton->text().toUtf8().data();
        std::string trBtnText = desiMgr.trT(btnText);
        pButton->setText(QString::fromUtf8(trBtnText.c_str()));
    }
}
