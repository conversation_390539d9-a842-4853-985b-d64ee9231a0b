#include "ModifyInstrumentDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/WDTranslate.h"
#include "core/message/WDMessage.h"
#include "core/businessModule/catalog/modelBuilder/WDBMCModelBuilder.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/typeMgr/WDBMTypeDesc.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/WDBMClaimMgr.h"
#include "core/businessModule/WDBDBase.h"

static constexpr const char* Desp_Purpose = "DESP";

/**
* @brief 获取指定名称和类型的属性值
*/
template<typename T>
static bool GeAttributeValue(const WD::WDNode& node, std::string_view name, T& value)
{
    // 节点属性描述
    auto pAttrDesp = node.getAttribute(name);
    if (!pAttrDesp.valid())
        return false;
    auto pDespData = pAttrDesp.data<T>();
    if (pDespData == nullptr)
        return false;

    value = *pDespData;
    return true;
}

ModifyInstrumentDialog::ModifyInstrumentDialog(WD::WDCore& core,QWidget* parent)
    :QDialog(parent)
    , _core(core)
    , _ceHelpter(_core)
{
    ui.setupUi(this);
    this->retranslateUi();
    //去掉对话框右上角的问号（帮助按钮）
    this->setWindowFlags(this->windowFlags().setFlag(Qt::WindowContextHelpButtonHint, false));

    _ceHelpter.setLineEdit(ui.lineEditName);
    _ceHelpter.setPushButton(ui.pushButtonCE);
    // 设置节点过滤回调函数
    _ceHelpter.setNodeFilter([this](WD::WDNode& node, const UiCESelectHelpter& sender) -> bool
    {
        WDUnused(sender);
        if(!WD::WDBMDPipeUtils::IsPipeComponent(node))
        {
            WD_WARN_T("ModifyInstrumentDialog", "Current node is not pipe coms");
            return false;
        }

        return true;
    });
    
    connect(&_ceHelpter, &UiCESelectHelpter::sigCurrentNodeChanged, this
        , [this](WD::WDNode* pCurrentNode, WD::WDNode* pPrevNode)
        {
            WDUnused(pPrevNode);
            updateWidget(WD::WDNode::ToShared(pCurrentNode));
        });
    
    // 管件特性属性窗口
    _pWidget        =       new ObjectPropertyWidget(true, "ModifyInstrumentDialog", this);
    _pGroup         =       WD::WDPropertyGroup::MakeShared();
    ui.groupBoxProperties->layout()->addWidget(_pWidget->widget());

    connect(ui.pushButtonApply,     &QPushButton::clicked, this, &ModifyInstrumentDialog::slotApplyClicked);
    connect(ui.pushButtonDefault,   &QPushButton::clicked, this, &ModifyInstrumentDialog::slotDefaultClicked);
    connect(ui.pushButtonReset,     &QPushButton::clicked, this, &ModifyInstrumentDialog::slotResetClicked);
    connect(ui.pushButtonDissmiss,  &QPushButton::clicked, this, &ModifyInstrumentDialog::close);
}
ModifyInstrumentDialog::~ModifyInstrumentDialog()
{
    if (_pWidget != nullptr)
    {
        ui.groupBoxProperties->layout()->removeWidget(_pWidget->widget());
        _pWidget->deleteLater();
    }
    if (_pGroup != nullptr)
    {
        _pGroup = nullptr;
    }
}

void ModifyInstrumentDialog::showEvent(QShowEvent* ev)
{
    WDUnused(ev);
    // 统一将焦点设置到窗体，用于在窗口打开时清除其他控件焦点
    this->setFocus(); 
    // 设置当前节点
    auto pCurrNode = _core.nodeTree().currentNode();
    _ceHelpter.setNode(pCurrNode);
}
void ModifyInstrumentDialog::hideEvent(QHideEvent* ev)
{
    WDUnused(ev);
}

bool ModifyInstrumentDialog::updateWidget(WD::WDNode::SharedPtr pNode)
{
    // 先清除窗口
    _pWidget->clear();
    // 不支持空节点
    if (pNode == nullptr)
    {
        return false;
    }
    _ceHelpter.blockSignals(true);
    _ceHelpter.setNode(pNode);
    _ceHelpter.blockSignals(false);

    // 更新属性以及界面
    auto& rDatas = this->updateDespData(pNode);
    if (rDatas.empty())
    {
        // 无设计参数，不支持修改，弹窗提示
        WD_WARN_T("ModifyInstrumentDialog", "Current pipe com do not have editable paramter");
        return false;
    }
    // 刷新属性组
    const auto& group = this->updatePropertyGroup(rDatas);
    // 刷新属性界面显示
    _pWidget->update(group);
    return true;
}

void ModifyInstrumentDialog::slotApplyClicked()
{
    auto pCurrNode = _ceHelpter.node();
    if (pCurrNode == nullptr)
    {
        WD_INFO("节点为空，不支持修改!");
        return;
    }

    // 申领节点
    bool bCancelMd = false;
    if (!WD::Core().getBMDesign().claimMgr().checkUpdate(pCurrNode, {"Desparam"}
        , bCancelMd))
        return ;
    if (bCancelMd)
        return ;

    if (_despDatas.empty())
    {
        WD_INFO("节点设计参数为空，不支持修改!");
        return;
    }

    // 生成设计模块使用的设计参数数据列表
    auto params = despDataToDespParams(_despDatas);
    // 设置回节点
    this->setSrcDespParams(*pCurrNode, params);
}
void ModifyInstrumentDialog::slotDefaultClicked() 
{
    // 当前值恢复为默认值
    for (auto& dData : _despDatas) 
    {
        dData.currValue = dData.defValue;
    }
    // 刷新属性组
    const auto& group = this->updatePropertyGroup(_despDatas);
    // 更新界面
    _pWidget->update(group);
}
void ModifyInstrumentDialog::slotResetClicked()
{
    // 当前值恢复为默认值
    for (auto& dData : _despDatas)
    {
        dData.currValue = dData.resetValue;
    }
    // 刷新属性组
    const auto& group = this->updatePropertyGroup(_despDatas);
    // 更新界面
    _pWidget->update(group);
}

ModifyInstrumentDialog::DespDatas& ModifyInstrumentDialog::updateDespData(WD::WDNode::SharedPtr pNode)
{
    // 清除 _despDatas
    _despDatas.clear();
    
    // 获取 _despDatas
    if (pNode != nullptr) 
    {
        // 先从元件中获取设计参数描述列表, 用于决定界面显示那些数据
        _despDatas = this->getSrcDespDatas(*pNode);
        // 获取节点的设计模块设计参数列表
        auto despParams = this->getSrcDespParams(*pNode);
        // 填充当前值
        for (auto& dData : _despDatas)
        {
            // 根据 number作为下标，查询despParams中是否有对应下标的值, 有则使用，无则使用默认值
            int tIndex = dData.number - 1;
            if (tIndex >= 0 && tIndex < despParams.size())
            {
                dData.currValue     = despParams[tIndex];
                dData.resetValue    = despParams[tIndex];
            }
            else
            {
                dData.currValue     = dData.defValue;
                dData.resetValue    = dData.defValue;
            }
        }
    }

    return _despDatas;
}
WD::StringVector ModifyInstrumentDialog::despDataToDespParams(const DespDatas& despDatas)
{
    // 拿到最大的number值
    int maxNumber = 0;
    for (const auto& dData : despDatas)
    {
        maxNumber = WD::Max(maxNumber, dData.number);
    }

    // 根据最大的number值设置设计参数个数
    WD::StringVector params;
    params.resize(maxNumber, "0");

    // 根据number作为下标赋值
    for (const auto& dData : despDatas)
    {
        int tIndex = dData.number - 1;
        if (tIndex >= 0 && tIndex < params.size())
            params[tIndex] = dData.currValue;
    }
    return params;
}
const WD::WDPropertyGroup& ModifyInstrumentDialog::updatePropertyGroup(DespDatas& despDatas)
{
    // 清除属性组
    _pGroup->clear();
    // 通过despDatas重新刷新属性组
    for (auto& dData : despDatas)
    {
        auto pRPty = _pGroup->addPropertyString(dData.title, dData.currValue);
        dData.setPropertyObject(pRPty);
    }
    return *_pGroup;
}

const std::string& ModifyInstrumentDialog::getSpRefPtyName(const WD::WDNode& node) const
{
    std::string tType = std::string(node.type());
    auto fItr = _typePtyNameMap.find(tType);
    if (fItr == _typePtyNameMap.end()) 
    {
        return _defPtyName.spRefName;
    }
    return fItr->second.spRefName;
}
const std::string& ModifyInstrumentDialog::getDespParamsPtyName(const WD::WDNode& node) const
{
    std::string tType = std::string(node.type());
    auto fItr = _typePtyNameMap.find(tType);
    if (fItr == _typePtyNameMap.end())
    {
        return _defPtyName.despParamsName;
    }
    return fItr->second.despParamsName;
}

ModifyInstrumentDialog::DespDatas ModifyInstrumentDialog::getSrcDespDatas(const WD::WDNode& node) const
{
    DespDatas rDatas;
    const auto& spRefPtyName = this->getSpRefPtyName(node);
    // 节点等级引用
    WD::WDBMNodeRef resSpRef = node.getAttribute(spRefPtyName).toNodeRef();
    auto pSpRefNode = resSpRef.refNode();
    if (pSpRefNode == nullptr)
        return rDatas;

    // 根据节点等级引用获取元件节点
    WD::WDBMNodeRef resScomRef = pSpRefNode->getAttribute("Catref").toNodeRef();
    auto pScom = resScomRef.refNode();
    if (pScom == nullptr)
        return rDatas;

    // 元件节点引用的DTSE节点
    WD::WDBMNodeRef dtseRef = pScom->getAttribute("Dtref").toNodeRef();
    WD::WDNode::SharedPtr pDtse = dtseRef.refNode();
    if (pDtse == nullptr)
        return rDatas;
    // 执行元件属性表达式解析
    auto aGet = _core.getBMCatalog().modelBuilder().cAttributeGet(node);

    // 遍历DTSE下的DATA节点，获取设计参数的属性
    rDatas.reserve(pDtse->children().size());
    for (auto& pChild : pDtse->children())
    {
        if (pChild == nullptr)
            continue;
        if (!pChild->isType("DATA"))
            continue;

        // 设计参数序号和标题
        DespData tData;
        if (!GeAttributeValue(*pChild, "Number", tData.number)
            || !GeAttributeValue(*pChild, "Dtitle", tData.title)
            || !GeAttributeValue(*pChild, "Purpose", tData.purpose)
            || !GeAttributeValue(*pChild, "Dproperty", tData.defValue))
            continue;
        if (tData.purpose != Desp_Purpose)
            continue;

        // 解析管件描述
        auto rTmpValue = aGet.getAttribute(*pChild, "Dproperty");
        if (rTmpValue.valid())
            tData.defValue = rTmpValue.convertToString();

        rDatas.emplace_back(std::move(tData));
        // 重置值就是默认值
        rDatas.back().resetValue = rDatas.back().defValue;
        // 当前值就是默认值
        rDatas.back().currValue = rDatas.back().defValue;
    }

    return rDatas;
}
WD::StringVector ModifyInstrumentDialog::getSrcDespParams(const WD::WDNode& node) const
{
    const auto& despParamsPtyName = this->getDespParamsPtyName(node);
    // 获取设计参数
    WD::StringVector rParams;
    auto rAttrValue     = node.getAttribute(despParamsPtyName);
    auto rAttrValueType = rAttrValue.type();
    switch (rAttrValueType)
    {
    case WD::WDBMAttrValueType::T_StringVector:
    {
        auto pParams = rAttrValue.data<WD::StringVector>();
        if (pParams != nullptr)
            rParams = *pParams;
    }
    break;
    case WD::WDBMAttrValueType::T_DoubleVector:
    {
        auto pParams = rAttrValue.data<std::vector<double> >();
        if (pParams != nullptr)
        {
            rParams.reserve(pParams->size());
            for (auto v : (*pParams)) 
            {
                rParams.push_back(WD::ToString(v));
            }
        }
    }
    break;
    default:
        break;
    }
    return rParams;
}
void ModifyInstrumentDialog::setSrcDespParams(WD::WDNode& node, const WD::StringVector& params) const
{
    const auto& despParamsPtyName = this->getDespParamsPtyName(node);
    // 获取设计参数
    WD::StringVector rParams;
    auto rAttrValue     = node.getAttribute(despParamsPtyName);
    auto rAttrValueType = rAttrValue.type();
    switch (rAttrValueType)
    {
    case WD::WDBMAttrValueType::T_StringVector:
    {
        WD::StringVector tParams = params;
        node.setAttribute(despParamsPtyName, WD::WDBMAttrValue(tParams));
    }
    break;
    case WD::WDBMAttrValueType::T_DoubleVector:
    {
        std::vector<double> tParams;
        tParams.reserve(params.size());
        for (const auto& v : params) 
        {
            bool success = false;
            auto tempValue = WD::FromString<double>(v, &success);
            if (success) 
                tParams.push_back(tempValue);
            else
                tParams.push_back(0.0f); // 失败则设置默认值0.0f
        }
        node.setAttribute(despParamsPtyName, WD::WDBMAttrValue(tParams));
    }
    break;
    default:
        return;
    }
    // 修改管件参数可能会设计到套件自动对齐的逻辑，需强制更新分支节点来生成新的直管
    auto pB = node.parent();
    if(pB != nullptr)
        pB->update(true);
    _core.needRepaint();
}

void ModifyInstrumentDialog::retranslateUi()
{
    Trs("ModifyInstrumentDialog"
        , static_cast<QDialog*>(this)

        , ui.labelName
        , ui.pushButtonCE
        , ui.groupBoxProperties
        , ui.pushButtonApply
        , ui.pushButtonDefault
        , ui.pushButtonReset
        , ui.pushButtonDissmiss
    );
}
