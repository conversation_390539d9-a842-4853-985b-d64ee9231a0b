#include "SVGLogDialog.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"

SVGLogDialog::SVGLogDialog(QWidget *parent)
    :   QDialog(parent)
{
    ui.setupUi(this);
    this->retranslateUi();
    connect(ui.pushButtonOk
        , &QPushButton::clicked
        , this
        , &SVGLogDialog::onHandlePushButtonOkClicked);
}

SVGLogDialog::~SVGLogDialog()
{
}

void    SVGLogDialog::setLogStr(const QString& logStr)
{
    _logStr    =   logStr;
}

void    SVGLogDialog::showEvent(QShowEvent *)
{
    if (_logStr.isEmpty())
    {
        ui.textEditLog->setText("未发现错误");
    }
    else
    {
        QString     text    =   _logStr;
        ui.textEditLog->setText(text);
    }
}

void    SVGLogDialog::hideEvent(QHideEvent *)
{
    _logStr.clear();
}

void    SVGLogDialog::onHandlePushButtonOkClicked()
{
    this->close();
}

void    SVGLogDialog::retranslateUi()
{
    Trs("DrawingLogDialog"

        , static_cast<QDialog*>(this)
        , this->window()
        , ui.pushButtonOk
    );
}