#include "UiComSvg.h"
#include "core/nodeTree/WDNodeTree.h"
#include "core/WDBlockingTask.h"

// svg变换转到mat形式
static WD::Mat4 SvgMatToMat4(const WD::WDSvgTransform& svgMat)
{
    auto data = svgMat.data();
    return WD::Mat4(
          { data[0], data[1], 0.0f, 0.0f }
        , { data[2], data[3], 0.0f, 0.0f }
        , { 0.0f,    0.0f,    1.0f, 0.0f }
        , { data[4], data[5], 0.0f, 1.0f }
    );
}
// 设置节点变换
static void SetSvgNodeLocal(WD::WDNode& node, const WD::WDSvgTransform& svgMat)
{
    auto mat = SvgMatToMat4(svgMat);
    // 由于svg的y方向为屏幕向下，这里也需要对矩阵进行相应调整
    node.setAttribute("Position", mat.extractTranslation() * WD::DVec3(1, -1, 1));
    node.setAttribute("Orientation", mat.extractRotationQuat().inverse());
    node.setAttribute("Scaling", mat.extractScale());
}
// 计算svg元素个数(包括子孙)
static size_t CountSvgObjs(const WD::WDSvgObject& svgObj)
{
    size_t follow = 0;
    for (auto p : svgObj.children())
    {
        if (p == nullptr)
            continue;
        follow += CountSvgObjs(*p);
    }
    return 1 + follow;
}
// svg对象转为节点
WD::WDNode::SharedPtr UiComSvg::svgObjToNode(WD::WDSvgObject* svgObj
    , const WD::SvgAttr& realAttr
    , WD::WDNode::SharedPtr parentNode)
{
    if (svgObj == nullptr)
        return nullptr;
    auto& dMgr = _core.getBMDesign();
    const auto nodeName = WD::WDSvgObject::TypeToString(svgObj->type());
    WD::WDNode::SharedPtr pNode = nullptr;
    // y方向的值取反
    switch (svgObj->type())
    {
    case WD::WDSvgObject::T_SvgRoot:
        {
            pNode = dMgr.create("ROOT2D", nodeName);
        }
        break;
    case WD::WDSvgObject::T_Group:
        {
            auto gObj = dynamic_cast<WD::WDSvgGroup*>(svgObj);
            if (gObj == nullptr)
                break;
            pNode = dMgr.create("GROUP2D", nodeName);
            if (pNode == nullptr)
                break;
            if (!gObj->name.empty())
                pNode->setName(gObj->name);
        }
        break;
    case WD::WDSvgObject::T_Rect:
        {
            auto rectObj = dynamic_cast<WD::WDSvgRect*>(svgObj);
            if (rectObj == nullptr)
                break;
            pNode = dMgr.create("RECT2D", nodeName);
            if (pNode == nullptr)
                break;

            pNode->setAttribute("Center", WD::WDBMAttrValue(WD::DVec3(rectObj->x + rectObj->w / 2, -(rectObj->y + rectObj->h / 2), 0)));
            pNode->setAttribute("Width", WD::WDBMAttrValue(rectObj->w));
            pNode->setAttribute("Height", WD::WDBMAttrValue(rectObj->h));
            pNode->setAttribute("CornerRx", WD::WDBMAttrValue(rectObj->rx));
            pNode->setAttribute("CornerRy", WD::WDBMAttrValue(rectObj->ry));
            pNode->setAttribute("StrokeColor"
                , WD::WDBMAttrValue(realAttr.stroke ?
                    realAttr.stroke.value() : WD::SvgAttr::ColorForNotSet()));
            if (realAttr.strokeWidth)
                pNode->setAttribute("StrokeWidth"
                    , WD::WDBMAttrValue(double(realAttr.strokeWidth.value())));
            pNode->setAttribute("FillColor"
                , WD::WDBMAttrValue(realAttr.fill ?
                    realAttr.fill.value() : WD::SvgAttr::ColorForNotSet()));

            // 这个只在叶子上设置
            if (realAttr.transform)
                SetSvgNodeLocal(*pNode, realAttr.transform.value());
    }
        break;
    case WD::WDSvgObject::T_Circle:
    {
        auto circleObj = dynamic_cast<WD::WDSvgCircle*>(svgObj);
        if (circleObj == nullptr)
            break;
        pNode = dMgr.create("CIRCLE2D", nodeName);
        if (pNode == nullptr)
            break;

        pNode->setAttribute("Center", WD::WDBMAttrValue(WD::DVec3(circleObj->cx, -circleObj->cy, 0)));
        pNode->setAttribute("Diameter", WD::WDBMAttrValue(circleObj->r * 2));
        pNode->setAttribute("StrokeColor"
            , WD::WDBMAttrValue(realAttr.stroke ?
                realAttr.stroke.value() : WD::SvgAttr::ColorForNotSet()));
        if (realAttr.strokeWidth)
            pNode->setAttribute("StrokeWidth"
                , WD::WDBMAttrValue(double(realAttr.strokeWidth.value())));
        pNode->setAttribute("FillColor"
            , WD::WDBMAttrValue(realAttr.fill ?
                realAttr.fill.value() : WD::SvgAttr::ColorForNotSet()));

        if (realAttr.transform)
            SetSvgNodeLocal(*pNode, realAttr.transform.value());
    }
    break;
    case WD::WDSvgObject::T_Ellipse:
    {
        auto ellipseObj = dynamic_cast<WD::WDSvgEllipse*>(svgObj);
        if (ellipseObj == nullptr)
            break;
        pNode = dMgr.create("ELLIPSE2D", nodeName);
        if (pNode == nullptr)
            break;

        pNode->setAttribute("Center", WD::WDBMAttrValue(WD::DVec3(ellipseObj->cx, -ellipseObj->cy, 0)));
        pNode->setAttribute("xDiameter", WD::WDBMAttrValue(ellipseObj->rx * 2));
        pNode->setAttribute("yDiameter", WD::WDBMAttrValue(ellipseObj->ry * 2));
        pNode->setAttribute("StrokeColor"
            , WD::WDBMAttrValue(realAttr.stroke ?
                realAttr.stroke.value() : WD::SvgAttr::ColorForNotSet()));
        if (realAttr.strokeWidth)
            pNode->setAttribute("StrokeWidth"
                , WD::WDBMAttrValue(double(realAttr.strokeWidth.value())));
        pNode->setAttribute("FillColor"
            , WD::WDBMAttrValue(realAttr.fill ?
                realAttr.fill.value() : WD::SvgAttr::ColorForNotSet()));

        if (realAttr.transform)
            SetSvgNodeLocal(*pNode, realAttr.transform.value());
    }
    break;
    case WD::WDSvgObject::T_Text:
    {
        auto textObj = dynamic_cast<WD::WDSvgText*>(svgObj);
        if (textObj == nullptr)
            break;
        pNode = dMgr.create("TEXT2D", textObj->text);
        if (pNode == nullptr)
            break;

        pNode->setAttribute("UpDir", WD::WDBMAttrValue(WD::DVec3::AxisY()));
        pNode->setAttribute("RightDir", WD::WDBMAttrValue(WD::DVec3::AxisX()));
        pNode->setAttribute("TextPosition", WD::WDBMAttrValue(WD::DVec3(textObj->x, -textObj->y, 0)));
        pNode->setAttribute("FontSize", WD::WDBMAttrValue(textObj->fontSize));
        pNode->setAttribute("FontStyle", WD::WDBMAttrValue(textObj->style));
        pNode->setAttribute("Text", WD::WDBMAttrValue(textObj->text));
        pNode->setAttribute("FixedPixel", false);
        pNode->setAttribute("VAlign", WD::WDGraphableText::VA_Bottom);
        pNode->setAttribute("HAlign", WD::WDGraphableText::HA_Left);

        pNode->setAttribute("StrokeColor"
            , WD::WDBMAttrValue(realAttr.stroke ?
                realAttr.stroke.value() : WD::SvgAttr::ColorForNotSet()));
        if (realAttr.strokeWidth)
            pNode->setAttribute("StrokeWidth"
                , WD::WDBMAttrValue(double(realAttr.strokeWidth.value())));
        pNode->setAttribute("FillColor"
            , WD::WDBMAttrValue(realAttr.fill ?
                realAttr.fill.value() : WD::SvgAttr::ColorForNotSet()));
        if (realAttr.transform)
            SetSvgNodeLocal(*pNode, realAttr.transform.value());
    }
    break;
    case WD::WDSvgObject::T_Path:
    {
        auto pathObj = dynamic_cast<WD::WDSvgPath*>(svgObj);
        if (pathObj == nullptr)
            break;
        pNode = dMgr.create("PATH2D", nodeName);
        if (pNode == nullptr)
            break;

        for (const auto& cmd : pathObj->cmds)
        {
            auto pCmdNode = dMgr.create(pNode, "CMD2D", std::string(1, cmd.cmd));
            if (pCmdNode == nullptr)
                continue;
            pCmdNode->setAttribute("PathCmd", std::string(1, cmd.cmd));
            const auto& datas = cmd.datas;
            switch (cmd.cmd)
            {
            case WD::WDSvgPath::PC_moveTo:
            {
                if (!datas.size() == 1)
                {
                    assert(false);
                    break;
                }
                // 创建VERT节点
                auto pVertNode = dMgr.create(pCmdNode, "VERT");
                if (pVertNode == nullptr)
                    break;
                pVertNode->setAttribute("Position", WD::DVec3{ datas[0].x, -datas[0].y, 0 });
            }
            break;
            case WD::WDSvgPath::PC_lineTo:// lineto,hlineto,vlineto都在这
            {
                if (!datas.size() == 1)
                {
                    assert(false);
                    break;
                }
                // 创建VERT节点
                auto pVertNode = dMgr.create(pCmdNode, "VERT");
                if (pVertNode == nullptr)
                    break;
                pVertNode->setAttribute("Position", WD::DVec3{ datas[0].x, -datas[0].y, 0 });
            }
            break;
            case WD::WDSvgPath::PC_cubicTo:// 三次贝塞尔
            {
                if (datas.size() != 3)
                {
                    assert(false);
                    break;
                }
                WD::DVec3 p1(datas[0].x, -datas[0].y, 0);
                WD::DVec3 p2(datas[1].x, -datas[1].y, 0);
                WD::DVec3 p3(datas[2].x, -datas[2].y, 0);
                for (const auto& vert : { p1, p2, p3 })
                {
                    auto pVertNode = dMgr.create(pCmdNode, "VERT");
                    if (pVertNode == nullptr)
                        break;
                    pVertNode->setAttribute("Position", vert);
                }
            }
            break;
            case WD::WDSvgPath::PC_Z:// loop回初始点
            {
            }
            break;
            default:
                break;
            }
        }

        pNode->setAttribute("StrokeColor"
            , WD::WDBMAttrValue(realAttr.stroke ?
                realAttr.stroke.value() : WD::SvgAttr::ColorForNotSet()));
        if (realAttr.strokeWidth)
            pNode->setAttribute("StrokeWidth"
                , WD::WDBMAttrValue(double(realAttr.strokeWidth.value())));
        pNode->setAttribute("FillColor"
            , WD::WDBMAttrValue(realAttr.fill ?
                realAttr.fill.value() : WD::SvgAttr::ColorForNotSet()));

        if (realAttr.transform)
            SetSvgNodeLocal(*pNode, realAttr.transform.value());
    }
    break;
    case WD::WDSvgObject::T_Line:
    {
        auto lineObj = dynamic_cast<WD::WDSvgLine*>(svgObj);
        if (lineObj == nullptr)
            break;
        auto start = WD::Vec3(lineObj->x1, -lineObj->y1, 0);
        auto end = WD::Vec3(lineObj->x2, -lineObj->y2, 0);

        pNode = dMgr.create("POLYLINE2D", nodeName);
        if (pNode == nullptr)
            break;

        // 创建VERT节点
        for (const auto& vert : { start, end })
        {
            // 创建VERT节点
            auto pVertNode = dMgr.create(pNode, "VERT");
            if (pVertNode == nullptr)
                break;
            // 设置位置
            pVertNode->setAttribute("Position", vert);
        }
        pNode->setAttribute("BLoop", WD::WDBMAttrValue(false));
        pNode->setAttribute("StrokeColor"
            , WD::WDBMAttrValue(realAttr.stroke ? realAttr.stroke.value() : WD::SvgAttr::ColorForNotSet()));
        if (realAttr.strokeWidth)
            pNode->setAttribute("StrokeWidth", WD::WDBMAttrValue(double(realAttr.strokeWidth.value())));
        // 目前_attr里面没有处理实线/虚线等其他属性
        // 所以这里也暂时不做处理，内部默认实线
        // 同时填充色也没有意义

        if (realAttr.transform)
            SetSvgNodeLocal(*pNode, realAttr.transform.value());
    }
    break;
    case WD::WDSvgObject::T_PolyLine:
    case WD::WDSvgObject::T_Polygon:
    {
            pNode = dMgr.create("POLYLINE2D", nodeName);
            if (pNode == nullptr)
                break;

            auto points = svgObj->points();
            for (auto& p : points)
            {
                p.y *= -1;
                // 创建VERT节点
                auto pVertNode = dMgr.create(pNode, "VERT");
                if (pVertNode == nullptr)
                    break;
                // 设置位置
                pVertNode->setAttribute("Position", p);
            }
            pNode->setAttribute("BLoop", WD::WDBMAttrValue(svgObj->type() == WD::WDSvgObject::T_Polygon));
            pNode->setAttribute("StrokeColor"
                , WD::WDBMAttrValue(realAttr.stroke ?
                    realAttr.stroke.value() : WD::SvgAttr::ColorForNotSet()));
            if (realAttr.strokeWidth)
                pNode->setAttribute("StrokeWidth"
                    , WD::WDBMAttrValue(double(realAttr.strokeWidth.value())));
            pNode->setAttribute("FillColor"
                , WD::WDBMAttrValue(realAttr.fill ?
                    realAttr.fill.value() : WD::SvgAttr::ColorForNotSet()));

            if (realAttr.transform)
                SetSvgNodeLocal(*pNode, realAttr.transform.value());
        }
        break;
    default:
        break;
    }
    if (pNode != nullptr && parentNode != nullptr)
        pNode->setParent(parentNode);
    return pNode;
}

WD::WDNode::SharedPtr UiComSvg::visitSvgObject(WD::WDSvgObject* svgObj, const WD::SvgAttr& parentAttr, WD::WDNode::SharedPtr parentNode, std::function<void(void)> progressFunc)
{
    if (svgObj == nullptr)
        return nullptr;
    // 创建节点
    auto realAttr = svgObj->attr().mergeParent(parentAttr);
    auto pNode = svgObjToNode(svgObj, realAttr, parentNode);
    if (pNode == nullptr)
        return nullptr;

    // 记录进度
    progressFunc();

    if (!svgObj->children().empty())
    {
        // 递归子元素
        for (auto& child : svgObj->children())
        {
            if (child == nullptr)
                continue;
            visitSvgObject(child, realAttr, pNode, progressFunc);
        }
    }
    return pNode;
}

void UiComSvg::onNotice(UiNotice* pNotice)
{
    auto    pCurNode    =   _core.nodeTree().currentNode();
    int     nType       =   pNotice->type();

    switch (nType)
    {
    case UiNoticeType::UNT_AllReady:
        {
        }
        break;
    case UiNoticeType::UNT_Action:
        {
            UiActionNotice* pActionNotice = static_cast<UiActionNotice*>(pNotice);
            if (pActionNotice->action().is("action.design.svg.import"))
            {
                _importedSvgRoot = nullptr;
                // 选择文件
                QStringList filePaths =  QFileDialog::getOpenFileNames(mWindow().widget()
                    , QString::fromUtf8("图纸导入")
                    , ""
                    , QString::fromUtf8("*.svg"));

                if (filePaths.isEmpty())
                    break;
                // 文件路径
                const std::string fileName = filePaths[0].toLocal8Bit().data();
                // 异步处理
                auto pBar = _core.blockingTask();
                if (pBar == nullptr || pBar->isRunning())
                {
                    WD_WARN_T("ErrorPromptSvg", "Resource occupied, try later");
                    break;
                }
                // 获取图纸节点
                WD::WDNode::SharedPtr drawRoot = nullptr;
                if (pCurNode != nullptr && pCurNode->isType("BOARD2D"))
                    drawRoot = pCurNode;
                else
                {
                    auto& dMgr = _core.getBMDesign();
                    drawRoot = dMgr.create(dMgr.root(), "BOARD2D", "BOARD2D");
                    if (drawRoot == nullptr)
                    {
                        _logger.addLog("节点创建失败：BOARD2D", WD::PluginLog::L_Error);
                        break;
                    }
                }
                WD::WDBlockingTask::Config cfg;
                cfg.closable = true;
                cfg.progress = true;
                cfg.decimals = 4;
                pBar->setConfig(cfg);
                pBar->start(
                [this, fileName](WD::WDBlockingTask& bar)
                {
                    bar.setProgressText("SVG导入... ", 0.0f, 0);
                    // 读取文件
                    WD::WDSvgReadWriter svgRW;
                    WD::WDSvgSvgRoot* svgRoot = nullptr;
                    bool bLoad = svgRW.load(fileName.data());
                    bar.setProgress(0.1f);
                    if (bLoad)
                        svgRoot = svgRW.root();
                    if (!bLoad || svgRoot == nullptr)
                    {
                        this->_logger.addLog("SVG文件读取失败！", WD::PluginLog::L_Error);
                        return ;
                    }

                    // 计算节点个数用于进度条显示
                    size_t cnt = CountSvgObjs(*svgRoot);
                    size_t runtimeCnt = 0;
                    // 转为节点
                    auto svg = this->visitSvgObject(svgRoot, WD::SvgAttr(), nullptr, [cnt, &bar, &runtimeCnt]()
                        {
                            runtimeCnt++;
                            if (runtimeCnt % 100 == 0)
                            {
                                float ratio = float(runtimeCnt) / float(cnt);
                                bar.setProgress(0.1 + ratio * 0.7);
                            }
                        });
                    if (svg != nullptr)
                    {
                        // 文件名（无后缀）
                        const std::string noExtName = std::filesystem::path(fileName).stem().string();
                        svg->setName(QString::fromLocal8Bit(noExtName.data()).toUtf8().toStdString());
                        svg->update(true);
                        this->_importedSvgRoot = svg;

                        std::string msg = "成功读取：" + std::to_string(runtimeCnt) + " 个节点！";
                        this->_logger.addLog(msg.data(), WD::PluginLog::L_Info);
                        bar.setProgress(1.0f);
                    }
                },
                // 结束后在主线程运行
                [this, drawRoot](WD::WDBlockingTask&)
                {
                    if (this->_importedSvgRoot != nullptr)
                    {
                        this->_importedSvgRoot->setParent(drawRoot);
                        // 更新父的aabb
                        this->_importedSvgRoot->updateParentAabbData();
                        // 提示导入成功
                        WD_INFO_T("ErrorPromptSvg", "ImportSucceed");
                    }
                    else
                    {
                        WD_INFO_T("ErrorPromptSvg", "ImportFaild");
                    }
                    showLog();
                });
            }
            else if (pActionNotice->action().is("action.design.svg.mgr"))
            {
                if (_manageDialog->isHidden())
                    _manageDialog->show();
                else
                    _manageDialog->activateWindow();
            }
        }
        break;
    default:
        break;
    }
}


void UiComSvg::showLog()
{
    // 处理日志
    const auto& logs = _logger.logs();
    std::string logStr;
    for (const auto& log : logs)
    {
        std::string strLevel;
        switch (log.level)
        {
        case WD::PluginLog::L_Error:
        {
            strLevel = "[Error]";
        }
        break;
        case WD::PluginLog::L_Info:
        {
            strLevel = "[Info]";
        }
        break;
        case WD::PluginLog::L_Warn:
        {
            strLevel = "[Warning]";
        }
        break;
        case WD::PluginLog::L_User:
        {
            strLevel = "[暂不支持的SVG元素类型]";
        }
        break;
        default:
            break;
        }
        logStr += (strLevel + ": " + log.text + "\n");
    }
    if (!logStr.empty())
    {
        _logDialog->setLogStr(logStr.c_str());
        _logDialog->exec();
    }
}