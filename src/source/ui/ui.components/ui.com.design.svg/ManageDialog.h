#pragma once
#include "ui_ManageDialog.h"
#include "../../ui.commonLibrary/ui.commonLib.weakObject/WeakObject.h"
#include "../../wizDesignerApp/UiInterface/UiTranslate.h"
#include "core/businessModule/design/WDBMDesign.h"

class ManageDialog
    : public QDialog
    , public WD::WDNodeObserver
{
    Q_OBJECT
    using MapWeakNodes = std::map<WD::WDUuid, WD::WDNode::WeakPtr>;
private:
    Ui::ManageDialog   ui;
    WD::WDCore& _core;
private:
    MapWeakNodes _drawingNodes;
public:
    ManageDialog(WD::WDCore& core, QWidget* parent = nullptr);
    virtual ~ManageDialog()
    {
        auto root = _core.getBMDesign().root();
        if (root != nullptr)
            root->observers().remove(this);

        for (auto itr : _drawingNodes)
        {
            if (itr.second.expired())
                continue;
            itr.second.lock()->observers().remove(this);
            for (auto pChild : itr.second.lock()->children())
            {
                if (pChild == nullptr)
                    continue;
                pChild->observers().remove(this);
            }
        }
    }
public:
    virtual void    showEvent(QShowEvent*) override;
private:
    /**
     * @brief 显示到管理窗口
     * @param mapWNodes 
     * @param treeWidget 
    */
    void showNodes(MapWeakNodes& mapWNodes, QTreeWidget* treeWidget);
    void tryDeleteItem(QTreeWidget* treeWidget, QTreeWidgetItem* item, WD::WDNode::Nodes& nodes);
    void setNodeShowState(bool bShow, QTreeWidget* treeWidget, QTreeWidgetItem* item);
    /**
    * @brief 界面翻译
    */
    void retranslateUi();
public:
    virtual void onNodeRemoveChildAfter(WD::WDNode::SharedPtr pNode, WD::WDNode::SharedPtr pChild) override final
    {
        if (pNode == nullptr || pChild == nullptr)
            return ;
        if (pChild->isType("BOARD2D"))
        {
            _drawingNodes.erase(pChild->uuid());
            pChild->observers().remove(this);
            showNodes(_drawingNodes, ui.svgTreeWidget);
        }
        else if (pChild->isType("ROOT2D"))
        {
            pChild->observers().remove(this);
            showNodes(_drawingNodes, ui.svgTreeWidget);
        }
        else if (pChild->isType("GROUP2D"))
        {
            showNodes(_drawingNodes, ui.svgTreeWidget);
        }
    }
    virtual void onNodeAddChildAfter(WD::WDNode::SharedPtr pNode, WD::WDNode::SharedPtr pChild)
    {
        if (pNode == nullptr || pChild == nullptr)
            return ;
        if (pChild->isType("BOARD2D"))
        {
            _drawingNodes[pChild->uuid()] = pChild->toPtr<WD::WDNode>();
            pChild->observers().push_back(this);
            showNodes(_drawingNodes, ui.svgTreeWidget);
        }
        else if (pChild->isType("ROOT2D"))
        {
            pChild->observers().push_back(this);
            showNodes(_drawingNodes, ui.svgTreeWidget);
        }
        // 管理界面打开的同时，如果导入一个SVGGROUP很多的文件
        // 会导致刷新过多从而会卡
        else if(pChild->isType("GROUP2D"))
        {
            //showNodes(_drawingNodes, ui.svgTreeWidget);
            for (int idx = 0; idx < ui.svgTreeWidget->topLevelItemCount(); ++idx)
            {
                auto item = ui.svgTreeWidget->topLevelItem(idx);
                if (item == nullptr)
                    continue;
                auto itemNode = WD::WDNode::SharedCast(item->data(0, Qt::UserRole).value<UiWeakObject>().object());
                if (itemNode == nullptr)
                    return;
                if (itemNode->uuid() == pNode->uuid())
                {
                    // 新增一个item
                    auto subItem = new QTreeWidgetItem();
                    subItem->setText(0, pChild->name().c_str());
                    QVariant userData;
                    userData.setValue(UiWeakObject(pChild->toPtr<WD::WDNode>()));
                    subItem->setData(0, Qt::UserRole, userData);
                    item->addChild(subItem);
                    auto checkBox = new QCheckBox(ui.svgTreeWidget);
                    connect(checkBox, &QCheckBox::toggled, [&](bool bChecked)
                        {
                            if (bChecked)
                                return;
                            auto tempItem = ui.svgTreeWidget->currentItem();
                            while (tempItem != nullptr)
                            {
                                auto* itemWidget = ui.svgTreeWidget->itemWidget(tempItem, 1);
                                if (itemWidget == nullptr)
                                    return;
                                auto* itemCheckBox = static_cast<QCheckBox*>(itemWidget);
                                if (itemCheckBox == nullptr)
                                    return;
                                itemCheckBox->blockSignals(true);
                                itemCheckBox->setChecked(false);
                                itemCheckBox->blockSignals(false);
                                tempItem = tempItem->parent();
                            }
                        });
                    ui.svgTreeWidget->setItemWidget(subItem, 1, checkBox);
                    break;
                }
            }
        }
    }
};
