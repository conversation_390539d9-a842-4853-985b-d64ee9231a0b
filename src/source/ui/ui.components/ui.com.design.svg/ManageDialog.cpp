#include "ManageDialog.h"
#include "../../ui.commonLibrary/ui.commonLib.custom/UiCommon.h"
#include "core/message/WDMessage.h"
#include "core/viewer/WDViewer.h"
ManageDialog::ManageDialog(WD::WDCore& core, QWidget* parent)
    : QDialog(parent), _core(core)
{
    ui.setupUi(this);
    retranslateUi();

    ui.svgTreeWidget->setColumnCount(2);
    ui.svgTreeWidget->header()->setVisible(false);
    ui.svgTreeWidget->header()->setSectionResizeMode(QHeaderView::ResizeMode::Stretch);

    auto root = _core.getBMDesign().root();
    if (root != nullptr)
        root->observers().push_back(this);

    for (auto pNode : root->children())
    {
        if (pNode == nullptr || !pNode->isType("BOARD2D"))
            continue;
        _drawingNodes[pNode->uuid()] = pNode;
        pNode->observers().push_back(this);
        for (auto pSVG : pNode->children())
        {
            if (pSVG == nullptr || !pSVG->isType("ROOT2D"))
                continue;
            pSVG->observers().push_back(this);
        }
    }

    connect(ui.tabWidget, &QTabWidget::currentChanged, [&](int idx)
        {
            switch (idx)
            {
            case 0:
                {
                    showNodes(_drawingNodes, ui.svgTreeWidget);
                }
                break;
            default:
                break;
            }
        });
    connect(ui.pushButtonDeleteSVG, &QPushButton::clicked, [&]()
        {
            // 删除节点
            auto localTranslate = [](const std::string& src)
            {
                WD::WDCxtTsBg("ErrorPromptSvg");
                auto res =  QString::fromUtf8(WD::WDCxtTs(src).c_str());
                WD::WDCxtTsEd();
                return res.toStdString();
            };

            if (WD_QUESTION(localTranslate("SureToDeleteSVG?")) != 0)
                return;
            WD::WDNode::Nodes nodes;
            for (int idx = 0; idx < ui.svgTreeWidget->topLevelItemCount(); ++idx)
            {
                auto item = ui.svgTreeWidget->topLevelItem(idx);
                if (item == nullptr)
                    continue;
                tryDeleteItem(ui.svgTreeWidget, item, nodes);
            }
            DeleteGivenNodes(nodes, _core, "ErrorPromptSvg", false);
            showNodes(_drawingNodes, ui.svgTreeWidget); // 刷新界面，被删除节点将不会再出现
        });
    connect(ui.pushButtonClose, &QPushButton::clicked, [&]()
        {
            reject();
        });
    connect(ui.pushButtonApply, &QPushButton::clicked, [&]()
        {
            bool bShow = ui.checkBoxShowDrawing->isChecked();
            for (int idx = 0; idx < ui.svgTreeWidget->topLevelItemCount(); ++idx)
            {
                auto item = ui.svgTreeWidget->topLevelItem(idx);
                if (item == nullptr)
                    continue;
                setNodeShowState(bShow, ui.svgTreeWidget, item);
            }
        });
}

void    ManageDialog::showEvent(QShowEvent*)
{
    showNodes(_drawingNodes, ui.svgTreeWidget);
    ui.checkBoxShowDrawing->setChecked(true);
}


void ManageDialog::showNodes(MapWeakNodes& mapWNodes, QTreeWidget* treeWidget)
{
    if (!isVisible())
        return ;
    if (treeWidget == nullptr)
        return ;
    treeWidget->clear();
    std::set<WD::WDUuid> uids;
    for (const auto itr : mapWNodes)
    {
        if (itr.second.expired() || !itr.second.lock()->isType("BOARD2D"))
        {
            uids.insert(itr.first);
            continue;
        }
        for (auto& pSVG : itr.second.lock()->children())
        {
            if (pSVG == nullptr || !pSVG->isType("ROOT2D"))
                continue;

            auto item = new QTreeWidgetItem();
            for (auto& pChild : pSVG->children())
            {
                if (pChild == nullptr || !pChild->isType("GROUP2D"))
                    continue;

                auto subItem = new QTreeWidgetItem();
                subItem->setText(0, pChild->name().c_str());
                QVariant userData;
                userData.setValue(UiWeakObject(pChild));
                subItem->setData(0, Qt::UserRole, userData);
                item->addChild(subItem);
                auto checkBox = new QCheckBox(ui.svgTreeWidget);
                connect(checkBox, &QCheckBox::toggled, [&](bool bChecked)
                    {
                        if (bChecked)
                            return;
                        auto tempItem = ui.svgTreeWidget->currentItem();
                        while (tempItem != nullptr)
                        {
                            auto* itemWidget = ui.svgTreeWidget->itemWidget(tempItem, 1);
                            if (itemWidget == nullptr)
                                return;
                            auto* itemCheckBox = static_cast<QCheckBox*>(itemWidget);
                            if (itemCheckBox == nullptr)
                                return;
                            itemCheckBox->blockSignals(true);
                            itemCheckBox->setChecked(false);
                            itemCheckBox->blockSignals(false);
                            tempItem = tempItem->parent();
                        }
                    });
                ui.svgTreeWidget->setItemWidget(subItem, 1, checkBox);
            }

            item->setText(0, pSVG->name().c_str());
            QVariant topUserData;
            topUserData.setValue(UiWeakObject(pSVG));
            item->setData(0, Qt::UserRole, topUserData);
            ui.svgTreeWidget->addTopLevelItem(item);

            auto checkBox = new QCheckBox(ui.svgTreeWidget);
            ui.svgTreeWidget->setItemWidget(item, 1, checkBox);
            connect(checkBox, &QCheckBox::toggled, [&](bool bChecked)
                {
                    auto item = ui.svgTreeWidget->currentItem();
                    if (item == nullptr)
                        return;
                    for (int i = 0; i < item->childCount(); ++i)
                    {
                        auto each = item->child(i);
                        if (each == nullptr)
                            continue;
                        auto widget = ui.svgTreeWidget->itemWidget(each, 1);
                        if (widget == nullptr)
                            continue;
                        auto checkBox = static_cast<QCheckBox*>(widget);
                        if (checkBox == nullptr)
                            continue;
                        checkBox->setChecked(bChecked);
                    }
                });
        }
    }
    for (const auto& id : uids)
    {
        mapWNodes.erase(id);
    }
}

void ManageDialog::tryDeleteItem(QTreeWidget* treeWidget, QTreeWidgetItem* item, WD::WDNode::Nodes& nodes)
{
    if (item == nullptr)
        return ;
    auto topCheckBox = static_cast<QCheckBox*>(treeWidget->itemWidget(item, 1));
    if (topCheckBox == nullptr)
        return ;
    if (topCheckBox->isChecked())
    {
        auto pNode = WD::WDNode::SharedCast(item->data(0, Qt::UserRole).value<UiWeakObject>().object());
        if (pNode == nullptr)
            return;
        nodes.push_back(pNode);
    }
    else
    {
        for (int i = 0; i < item->childCount(); i++)
        {
            auto childItem = item->child(i);
            tryDeleteItem(treeWidget, childItem, nodes);
        }
    }
}

void ManageDialog::setNodeShowState(bool bShow, QTreeWidget* treeWidget, QTreeWidgetItem* item)
{
    if (item == nullptr)
        return ;
    auto& viewer = _core.viewer();
    auto pScene = viewer.scene();
    if (pScene == nullptr)
        return ;

    auto topCheckBox = static_cast<QCheckBox*>(treeWidget->itemWidget(item, 1));
    if (topCheckBox == nullptr)
        return ;

    if (topCheckBox->isChecked())
    {
        auto pNode = WD::WDNode::SharedCast(item->data(0, Qt::UserRole).value<UiWeakObject>().object());
        if (pNode == nullptr)
            return;
        if (bShow)
            pScene->add(pNode);
        else
            pScene->remove(pNode);
    }
    else
    {
        for (int i = 0; i < item->childCount(); i++)
        {
            auto childItem = item->child(i);
            setNodeShowState(bShow, treeWidget, childItem);
        }
    }
}

void ManageDialog::retranslateUi()
{
    Trs("ManageDialog"
        , static_cast<QDialog*>(this)
        , this->window()
        , ui.pushButtonDeleteSVG
        , ui.pushButtonClose
        , ui.pushButtonApply
        , ui.checkBoxShowDrawing
    );
}