#pragma     once

#include    "../../wizDesignerApp/UiInterface/UiInterface.h"
#include    <QFileDialog>
#include    "core/viewer/WDViewer.h"
#include    <filesystem>
#include    "SVGLogDialog.h"
#include    "core/extension/WDPluginFormat.h"
#include    "ManageDialog.h"
#include    "message/WDMessage.h"
#include    "../../../utilLib/util.svgTool/WDSvgHeaders.h"

class UiComSvg
    : public QObject
    , public IUiComponent
{
    Q_OBJECT
private:
    SVGLogDialog* _logDialog;
    WD::WDNode::Nodes _SVGRoots;
    ManageDialog* _manageDialog;
    WD::PluginLog _logger;
    WD::WDCore& _core;
    WD::WDNode::SharedPtr _importedSvgRoot = nullptr;// 保证异步线程中能拿到持久量
public:
    UiComSvg(IMainWindow& mainWindow, const UiComponentAttributes& attrs, QObject* parent = nullptr)
        : QObject(parent)
        , IUiComponent(mainWindow, attrs)
        , _core(mainWindow.core())
    {
        _logDialog = new SVGLogDialog(mWindow().widget());
        _manageDialog = new ManageDialog(mWindow().core(), mWindow().widget());
    }
    ~UiComSvg()
    {
    }
public:
    /**
    *   @brief 通知事件
    *   @param pNotice 事件对象
    */
    virtual void onNotice(UiNotice* pNotice) override;
private:
    /**
     * @brief 显示日志
    */
    void showLog();
    /**
     * @brief 将一个svg对象转化为节点
     * @param svgObj svg对象
     * @param realAttr 实际属性，由上级传递以及合并下来
     * @param parentNode 父节点
     * @return 转化出来的节点
    */
    WD::WDNode::SharedPtr svgObjToNode(WD::WDSvgObject* svgObj, const WD::SvgAttr& realAttr, WD::WDNode::SharedPtr parentNode);
    /**
     * @brief 递归遍历每一个svg对象，转化为节点
     * @param svgObj svg对象
     * @param parentAttr 父属性
     * @param parentNode 父节点
     * @param progressFunc 进度函数
     * @return 转化出来的节点
    */
    WD::WDNode::SharedPtr visitSvgObject(WD::WDSvgObject* svgObj, const WD::SvgAttr& parentAttr, WD::WDNode::SharedPtr parentNode, std::function<void(void)> progressFunc);
};