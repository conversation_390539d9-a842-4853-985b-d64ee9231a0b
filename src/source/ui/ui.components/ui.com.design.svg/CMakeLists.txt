set(TARGET_NAME ui_com_design_svg)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets Xml REQUIRED)

set(HEADER_FILES
	"UiComSvg.h"
	"SVGLogDialog.h"
	"ManageDialog.h"
)

set(SOURCE_FILES
    "main.cpp"
	"UiComSvg.cpp"
	"SVGLogDialog.cpp"
	"ManageDialog.cpp"
)

set(FORM_FILES
	"SVGLogDialog.ui"
	"ManageDialog.ui"
)

add_library(${TARGET_NAME} SHARED
		${HEADER_FILES}
		${SOURCE_FILES}
		${FORM_FILES}
)

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore ui.commonLib.WeakObject ui.commonLib.custom)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets)
target_link_libraries(${TARGET_NAME} PUBLIC util.svgTool)
target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

add_custom_command(
	TARGET ${TARGET_NAME}
	POST_BUILD
    COMMAND             ${CMAKE_COMMAND} -E make_directory ./translations
    COMMAND             ${CMAKE_COMMAND} -E copy_directory ./translations ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ui/components/${TARGET_NAME}/translations
	WORKING_DIRECTORY	${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT             "copy directory translations..."
)