#include "WIZDesignerDictionaryMainWindow.h"
#include "core/businessModule/catalog/WDBMCatalog.h"
#include "core/businessModule/design/WDBMDesign.h"
#include "core/businessModule/typeMgr/WDBMTypeMgr.h"
#include "WDTranslate.h"
#include <QFile>
#include <QFileDialog>
#include <qmessagebox.h>
#include <fstream>
#include "core/message/WDMessage.h"
#include "WDRapidxml.h"
#include "core/common/WDStringConvert.h"

WD_NAMESPACE_USE

#define TOOL_READ_ONLY true

WIZDesignerDictionaryMainWindow::WIZDesignerDictionaryMainWindow(WD::WDCore& core, QWidget* parent)
    : _core(core)
{
    ui.setupUi(this);
    this->setParent(parent);

    initDialog();
    slotComboBoxModuleIndexChanged(0);
    updateType();
    connect(ui.lineEditTypeSearch, &QLineEdit::textChanged
        , this, &WIZDesignerDictionaryMainWindow::slotLineEditTypeSearchTextChanged);
    connect(ui.lineEditAttributeSearch, &QLineEdit::textChanged
        , this, &WIZDesignerDictionaryMainWindow::slotLineEditAttributeSearchChanged);
    connect(ui.comboBoxModule, QOverload<int>::of(&QComboBox::currentIndexChanged)
        , this, &WIZDesignerDictionaryMainWindow::slotComboBoxModuleIndexChanged);
    connect(ui.tableWidgetTypes, &QTableWidget::currentItemChanged
        , this, &WIZDesignerDictionaryMainWindow::slotTableWidgetTypesItemChanged);
    connect(ui.tableWidgetAttributes, &QTableWidget::currentItemChanged
        , this, &WIZDesignerDictionaryMainWindow::slotTableWidgetAttributesItemChanged);

    /********************************* 类型的参数 ***************************/
    // 名称
    connect(ui.lineEditTypeName, &QLineEdit::editingFinished, this, &WIZDesignerDictionaryMainWindow::slotLineEditTypeNameEditingFinished);
    // 触发更新标志
    connect(ui.checkBoxTriggerUpdate, &QCheckBox::toggled, [&] (bool bChecked){
        assert(_currentType != nullptr);
        if (_currentType == nullptr)
            return;
        _currentType->flags.setFlag(WD::WDBMTypeDesc::Flag::F_TriggerUpdate, bChecked);
    });
    // 自动生成标志
    ui.checkBoxAutoGeneration->setEnabled(false);
    // 更改父节点列表 !TODO:做成界面,让用户在界面中的类型列表内选择父节点类型
    connect(ui.pushButtonChange, &QPushButton::clicked, [&] ()
    {
        assert(_currentType != nullptr);
        if (_currentType == nullptr)
            return;
        if (QMessageBox::question(this, "?", "您确定要更改当前类型父类型列表吗?", "确定", "取消") != 0)
            return;
        StringVector vec;
        WIZDesignerDictionaryCommon::AnalysisStringBySpace(ui.textEditParentsList->toPlainText().toLocal8Bit().data(), vec);
        if (vec.empty())
        {
            QMessageBox::warning(this, "警告", "父节点列表不能为空!");
            ui.textEditParentsList->setText(_currentType->parentsStr().c_str());
            return ;
        }

        auto moduleItr = _moduleInfos.find(WIZDesignerDictionaryCommon::ModuleType(ui.comboBoxModule->currentData().toInt()));
        if (moduleItr == _moduleInfos.end())
            return;
        auto& typeInfos = moduleItr->second;

        StringVector errorStrs;
        std::set<WIZDesignerDictionaryCommon::TypeInfo*> newParents;
        for (auto& each : vec)
        {
            auto typeItr = typeInfos.find(each);
            if (typeItr == typeInfos.end())
            {
                errorStrs.emplace_back(each);
                continue;
            }
            if (typeItr->second == nullptr)
            {
                assert(false);
                continue;
            }
            newParents.emplace(typeItr->second);
        }
        char info[2048] = { 0 };
        if (!errorStrs.empty())
            sprintf_s(info, sizeof(info), u8"类型:[%s]未找到!", StringConcat(errorStrs, " ").c_str());

        if (newParents.empty())
        {
            sprintf_s(info, sizeof(info), u8"%s父节点列表设置失败", info);
            QMessageBox::warning(this, "警告", info);
            ui.textEditParentsList->setText(_currentType->parentsStr().c_str());
            return ;
        }
        sprintf_s(info, sizeof(info), u8"%s父节点列表设置成功", info);
        QMessageBox::information(this, "提示", info);
        _currentType->parentTypes = newParents;
        updateType(ui.tableWidgetAttributes->currentRow());
    });
    // 添加资源
    connect(ui.pushButtonAdd, &QPushButton::clicked, this, &WIZDesignerDictionaryMainWindow::slotPushButtonAddClicked);
    // 移除已选资源
    connect(ui.pushButtonRemoveSelects, &QPushButton::clicked, this, &WIZDesignerDictionaryMainWindow::slotPushButtonRemoveSelectsClicked);
    // 移除所有资源
    connect(ui.pushButtonRemoveAll, &QPushButton::clicked, this, &WIZDesignerDictionaryMainWindow::slotPushButtonRemoveAllClicked);
    // 资源
    connect(ui.tableWidgetResource, &QTableWidget::itemChanged, [&] (QTableWidgetItem* pItem)
    {
        assert(_currentType != nullptr);
        if (_currentType == nullptr)
            return;
        if (pItem == nullptr)
            return;
        _currentType->resources.clear();
        for (int i = 0; i < ui.tableWidgetResource->rowCount(); ++i)
        {
            auto itemName = ui.tableWidgetResource->item(i, 0);
            auto itemValue = ui.tableWidgetResource->item(i, 1);
            if (itemName == nullptr || itemValue == nullptr)
                continue;
            std::string name = itemName->text().toUtf8().data();
            std::string value = itemValue->text().toUtf8().data();
            if (name.empty() || value.empty())
                continue;
            if (_currentType->resources.find(name) != _currentType->resources.end())
            {
                QMessageBox::warning(this, "警告", "名称重复!");
                itemName->setText("");
                continue;
            }
            _currentType->resources[name] = WD::WDBMTypeDesc::Resource(value);
        }
    });
    // 描述
    connect(ui.textEditDescribe, &QTextEdit::textChanged, [&] ()
    {
        assert(_currentType != nullptr);
        if (_currentType == nullptr)
            return;
        _currentType->describe = ui.textEditDescribe->toPlainText().toUtf8().data();
    });

    /********************************* 属性的参数 ***************************/
    // 名称
    connect(ui.lineEditAttrName, &QLineEdit::editingFinished, this, &WIZDesignerDictionaryMainWindow::slotLineEditAttrNameEditingFinished);
    // 数据类型
    connect(ui.comboBoxDataType, QOverload<int>::of(&QComboBox::currentIndexChanged)
        , this, &WIZDesignerDictionaryMainWindow::slotComboBoxDataTypeCurrentIndexChanged);
    // 隐藏标志
    connect(ui.checkBoxHide, &QCheckBox::toggled, [&](bool bChecked){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        _currentAttr->flags().setFlag(WD::WDBMAttrDesc::Flag::F_Hidden, bChecked);
    });
    // 只读标志
    connect(ui.checkBoxReadOnly, &QCheckBox::toggled, [&](bool bChecked){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        _currentAttr->flags().setFlag(WDBMAttrDesc::Flag::F_ReadOnly, bChecked);
    });
    // 默认值
    connect(ui.lineEditDefaultValue, &QLineEdit::editingFinished, [&](){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        std::string defValueStr = ui.lineEditDefaultValue->text().toLocal8Bit().data();
        bool bStrOk = false;
        auto value = WDBMAttrValue::FromString(WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt()), defValueStr, &bStrOk);
        if (!bStrOk)
        {
            QMessageBox::warning(this, "警告", "默认值格式不正确");
            return;
        }
        _currentAttr->setDefaultValue(value);
    });
    // 最小值
    connect(ui.doubleSpinBoxMin, &QDoubleSpinBox::editingFinished, [&](){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        double minD = ui.doubleSpinBoxMin->value();
        WDBMAttrValue minValue;
        auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
        switch (type)
        {
        case WD::WDBMAttrValueType::T_Int:
        case WD::WDBMAttrValueType::T_IntVector:
            minValue = WDBMAttrValue(static_cast<int>(minD));
            break;
        case WD::WDBMAttrValueType::T_Double:
        case WD::WDBMAttrValueType::T_DoubleVector:
            minValue = WDBMAttrValue(minD);
            break;
        default:
            return;
            break;
        }
        _currentAttr->setMinimumValue(minValue);
    });
    // 最大值
    connect(ui.doubleSpinBoxMax, &QDoubleSpinBox::editingFinished, [&](){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        double maxD = ui.doubleSpinBoxMax->value();
        WDBMAttrValue maxValue;
        auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
        switch (type)
        {
        case WD::WDBMAttrValueType::T_Int:
        case WD::WDBMAttrValueType::T_IntVector:
            maxValue = WDBMAttrValue(static_cast<int>(maxD));
            break;
        case WD::WDBMAttrValueType::T_Double:
        case WD::WDBMAttrValueType::T_DoubleVector:
            maxValue = WDBMAttrValue(maxD);
            break;
        default:
            return;
            break;
        }
        _currentAttr->setMaximumValue(maxValue);
    });
    // 步长
    connect(ui.doubleSpinBoxStep, &QDoubleSpinBox::editingFinished, [&](){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        double step = ui.doubleSpinBoxStep->value();
        WDBMAttrValue stepValue;
        auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
        switch (type)
        {
        case WD::WDBMAttrValueType::T_Int:
        case WD::WDBMAttrValueType::T_IntVector:
            stepValue = WDBMAttrValue(static_cast<int>(step));
            break;
        case WD::WDBMAttrValueType::T_Double:
        case WD::WDBMAttrValueType::T_DoubleVector:
            stepValue = WDBMAttrValue(step);
            break;
        default:
            return;
            break;
        }
        _currentAttr->setMaximumValue(stepValue);
    });
    // 精度
    connect(ui.spinBoxDecimals, &QDoubleSpinBox::editingFinished, [&](){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        int decimals = ui.spinBoxDecimals->value();
        auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
        switch (type)
        {
        case WD::WDBMAttrValueType::T_Double:
        case WD::WDBMAttrValueType::T_DoubleVector:
            _currentAttr->setDecimals(decimals);
            break;
        default:
                return;
            break;
        }
    });
    // 正则表达式
    connect(ui.lineEditRegex, &QLineEdit::editingFinished, [&](){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        std::string regex = ui.lineEditRegex->text().toLocal8Bit().data();
        auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
        switch (type)
        {
        case WD::WDBMAttrValueType::T_String:
        case WD::WDBMAttrValueType::T_StringVector:
        case WD::WDBMAttrValueType::T_Word:
            _currentAttr->setRegexp(regex);
            break;
        default:
            return;
            break;
        }
    });
    // 字典名称
    connect(ui.lineEditEnumDictName, &QLineEdit::editingFinished, [&](){
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        std::string enumDictName = ui.lineEditEnumDictName->text().toLocal8Bit().data();
        auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
        switch (type)
        {
        case WD::WDBMAttrValueType::T_Word:
            _currentAttr->setEnumDictionaryName(enumDictName);
            break;
        default:
            return;
            break;
        }
    });
    // 引用属性引用的节点所属模块
    connect(ui.comboBoxRefNodeModule, QOverload<int>::of(&QComboBox::activated), [&] (int) {
        assert(_currentAttr != nullptr);
        if (_currentAttr == nullptr)
            return;
        auto moduleType = WIZDesignerDictionaryCommon::ModuleType(ui.comboBoxRefNodeModule->currentData().toInt());

        auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
        switch (type)
        {
        case WD::WDBMAttrValueType::T_NodeRef:
        case WD::WDBMAttrValueType::T_NodeRefs:
            _currentAttr->setRefNodeModuleName(WIZDesignerDictionaryCommon::ModuleTypeToString(moduleType));
            break;
        default:
            return;
            break;
        }
    });

    connect(ui.pushButtonNew,       &QPushButton::clicked, this, &WIZDesignerDictionaryMainWindow::slotPushButtonNewClicked);
    connect(ui.pushButtonDelete,    &QPushButton::clicked, this, &WIZDesignerDictionaryMainWindow::slotPushButtonDeleteClicked);
    connect(ui.pushButtonSave,      &QPushButton::clicked, this, &WIZDesignerDictionaryMainWindow::slotPushButtonSaveClicked);
    connect(ui.pushButtonExit,      &QPushButton::clicked, this, &WIZDesignerDictionaryMainWindow::slotPushButtonExitClicked);

    if (TOOL_READ_ONLY)
    {
        ui.pushButtonChange->setVisible(false);

        ui.pushButtonAdd->setVisible(false);
        ui.pushButtonRemoveSelects->setVisible(false);
        ui.pushButtonRemoveAll->setVisible(false);

        ui.pushButtonDelete->setVisible(false);
        ui.pushButtonSave->setVisible(false);
        ui.pushButtonNew->setVisible(false);

        ui.lineEditTypeName->setReadOnly(true);
        ui.checkBoxAutoGeneration->setEnabled(false);
        ui.checkBoxTriggerUpdate->setEnabled(false);
        ui.textEditParentsList->setReadOnly(true);
        ui.tableWidgetResource->setEnabled(false);
        ui.textEditDescribe->setReadOnly(true);

        ui.lineEditAttrName->setReadOnly(true);
        ui.lineEditAttrSampleName->setReadOnly(true);
        ui.comboBoxDataType->setEnabled(false);
        ui.lineEditDefaultValue->setReadOnly(true);
        ui.checkBoxHide->setEnabled(false);
        ui.checkBoxReadOnly->setEnabled(false);
        ui.doubleSpinBoxMin->setReadOnly(true);
        ui.doubleSpinBoxMin->setReadOnly(true);
        ui.doubleSpinBoxStep->setReadOnly(true);
        ui.spinBoxDecimals->setReadOnly(true);
        ui.lineEditRegex->setReadOnly(true);
        ui.lineEditEnumDictName->setReadOnly(true);
        ui.comboBoxRefNodeModule->setEnabled(false);
    }
}
WIZDesignerDictionaryMainWindow::~WIZDesignerDictionaryMainWindow()
{
    for (auto& eachModule : _moduleInfos)
    {
        for (auto& each : eachModule.second)
        {
            if (each.second != nullptr)
            {
                delete each.second;
                each.second = nullptr;
            }
        }
    }
    _moduleInfos.clear();
    _currentAttr = nullptr;
    _currentType = nullptr;
}

void WIZDesignerDictionaryMainWindow::slotLineEditTypeSearchTextChanged(const QString& str)
{
    if (str.isEmpty())
    {
        for (int i = 0; i < ui.tableWidgetTypes->rowCount(); i++)
            ui.tableWidgetTypes->setRowHidden(i, false);
        return;
    }
    for (int i = 0; i < ui.tableWidgetTypes->rowCount(); i++)
        ui.tableWidgetTypes->setRowHidden(i, true);

    auto list = ui.tableWidgetTypes->findItems(str, Qt::MatchFlag::MatchContains);
    if (list.empty())
    {
        ui.tableWidgetAttributes->blockSignals(true);
        ui.tableWidgetAttributes->clearContents();
        ui.tableWidgetAttributes->setRowCount(0);
        ui.tableWidgetAttributes->blockSignals(false);
        ui.lineEditAttrName->blockSignals(true);
        ui.lineEditAttrName->clear();
        ui.lineEditAttrName->blockSignals(false);
        setAttributeInfoEnabled(false);
        _currentType = nullptr;
        _currentAttr = nullptr;
        return;
    }
    for (auto& each : list)
    {
        if (each != nullptr)
            ui.tableWidgetTypes->setRowHidden(each->row(), false);
    }
    ui.tableWidgetTypes->setCurrentCell(list.front()->row(), 0);
}

void WIZDesignerDictionaryMainWindow::slotLineEditAttributeSearchChanged(const QString& str)
{
    if (str.isEmpty())
    {
        for (int i = 0; i < ui.tableWidgetAttributes->rowCount(); i++)
            ui.tableWidgetAttributes->setRowHidden(i, false);
        return;
    }
    for (int i = 0; i < ui.tableWidgetAttributes->rowCount(); i++)
        ui.tableWidgetAttributes->setRowHidden(i, true);

    auto list = ui.tableWidgetAttributes->findItems(str, Qt::MatchFlag::MatchContains);
    if (list.empty())
    {
        setAttributeInfoEnabled(false);
        _currentAttr = nullptr;
        return;
    }
    for (auto& each : list)
    {
        if (each != nullptr)
            ui.tableWidgetAttributes->setRowHidden(each->row(), false);
    }
    ui.tableWidgetAttributes->setCurrentCell(list.front()->row(), 0);
}

void WIZDesignerDictionaryMainWindow::slotComboBoxModuleIndexChanged(int)
{
    ui.tableWidgetTypes->blockSignals(true);
    WDBMBase*   pMgr        = nullptr;
    auto moduleItr = _moduleInfos.find(WIZDesignerDictionaryCommon::ModuleType(ui.comboBoxModule->currentData().toInt()));
    if (moduleItr == _moduleInfos.end())
    {
        QMessageBox::warning(this, "警告", "未知错误!");
        return;
    }
    switch (ui.comboBoxModule->currentData().toInt())
    {
    case WIZDesignerDictionaryCommon::ModuleType::MT_Catalog:
        pMgr    = &_core.getBMCatalog();
        break;
    case WIZDesignerDictionaryCommon::ModuleType::MT_Design:
        pMgr    = &_core.getBMDesign();
        break;
    default:
        break;
    }
    assert(pMgr != nullptr);
    if (pMgr != nullptr)
    {
        ui.tableWidgetTypes->clearContents();
        ui.tableWidgetTypes->setRowCount(int(moduleItr->second.size()));
        int index = 0;
        for (auto& typeItem : moduleItr->second)
        {
            auto& name = typeItem.first;
            std::string transName = pMgr->trT(name);
            if (transName == name)
                transName = "";
            assert(!name.empty());
            auto item = new QTableWidgetItem(name.c_str());
            auto transItem = new QTableWidgetItem(transName.c_str());
            ui.tableWidgetTypes->setItem(index, 0, item);
            ui.tableWidgetTypes->setItem(index, 1, transItem);
            item->setFlags(Qt::ItemFlag::NoItemFlags | Qt::ItemFlag::ItemIsSelectable | Qt::ItemFlag::ItemIsEnabled);
            transItem->setFlags(Qt::ItemFlag::NoItemFlags | Qt::ItemFlag::ItemIsSelectable | Qt::ItemFlag::ItemIsEnabled);
            index++;
        }
    }
    if (ui.tableWidgetTypes->rowCount() > 0 && ui.tableWidgetTypes->columnCount() > 0)
    {
        ui.tableWidgetTypes->setCurrentCell(0, 0);
        updateType();
    }
    ui.tableWidgetTypes->blockSignals(false);
    auto typeStr = ui.lineEditTypeSearch->text();
    if (!typeStr.isEmpty())
        slotLineEditTypeSearchTextChanged(typeStr);
    auto attrStr = ui.lineEditAttributeSearch->text();
    if (!attrStr.isEmpty())
        slotLineEditAttributeSearchChanged(attrStr);
    return;
}

void WIZDesignerDictionaryMainWindow::slotTableWidgetTypesItemChanged(QTableWidgetItem*, QTableWidgetItem*)
{
    updateType();
}

void WIZDesignerDictionaryMainWindow::slotTableWidgetAttributesItemChanged(QTableWidgetItem*, QTableWidgetItem* )
{
    assert(_currentType != nullptr);
    if (_currentType == nullptr)
        return;
    auto& attributes = _currentType->attributes;
    auto item = ui.tableWidgetAttributes->item(ui.tableWidgetAttributes->currentRow(), 0);
    if (item != nullptr)
    {
        int idx = item->data(Qt::UserRole).toInt();
        if (idx >= 0 && idx <= attributes.size())
            return  updateAttribute(attributes[idx]);
    }
    assert(false);
}
void WIZDesignerDictionaryMainWindow::slotComboBoxDataTypeCurrentIndexChanged(int)
{
    auto type = WD::WDBMAttrValueType(ui.comboBoxDataType->currentData().toInt());
    assert(_currentAttr != nullptr);
    if (_currentAttr == nullptr)
        return;
    _currentAttr->setType(type);
    updateAttribute(*_currentAttr);
}

void WIZDesignerDictionaryMainWindow::slotPushButtonAddClicked()
{
    ui.tableWidgetResource->setRowCount(ui.tableWidgetResource->rowCount() + 1);
}
void WIZDesignerDictionaryMainWindow::slotPushButtonRemoveSelectsClicked()
{
    if (QMessageBox::question(this, "?", "是否要移除所有已选择的项?", "确定", "取消") != 0)
        return;

    // 从列表上删除此行
    QModelIndexList indexes = ui.tableWidgetResource->selectionModel()->selectedIndexes();
    std::set<int> rowIndexes;
    for (auto itr = indexes.begin(); itr != indexes.end(); itr++)
    {
        int row = itr->row();
        rowIndexes.emplace(row);
    }
    // 因为indexes中index是根据用户点选顺序排序的，所以需要先排序再移除行
    for (auto itr = rowIndexes.rbegin(); itr != rowIndexes.rend(); itr++)
    {
        ui.tableWidgetResource->removeRow(*itr);
    }
    ui.tableWidgetResource->blockSignals(false);

}
void WIZDesignerDictionaryMainWindow::slotPushButtonRemoveAllClicked()
{
    if (QMessageBox::question(this, "?", "是否要移除所有项?", "确定", "取消") != 0)
        return;
    ui.tableWidgetResource->clearContents();
    ui.tableWidgetResource->setRowCount(0);
}

void WIZDesignerDictionaryMainWindow::slotPushButtonNewClicked()
{
    assert(_currentType != nullptr);
    if (_currentType == nullptr)
        return;
    auto checkName = [](const WIZDesignerDictionaryCommon::Attributes& attributes, const std::string& name) ->bool{
        for (auto& each : attributes)
        {
            if (name == each.name())
                return false;
        }
        return true;
    };
    auto& attributes = _currentType->attributes;
    char newName[64];
    for (int idx = 1; ;idx++)
    {
        sprintf_s(newName, sizeof(newName), "default_%d", idx);
        if (checkName(attributes, newName))
            break;
    }
    auto attr = WD::WDBMAttrDesc(newName, WD::WDBMAttrValueType::T_String);
    attributes.push_back(attr);
    updateType();
    if (ui.tableWidgetAttributes->rowCount() > 0 && ui.tableWidgetAttributes->columnCount() > 0)
        ui.tableWidgetAttributes->setCurrentCell(ui.tableWidgetAttributes->rowCount() - 1, 0);
}
void WIZDesignerDictionaryMainWindow::slotPushButtonDeleteClicked()
{
    if (QMessageBox::question(nullptr, "提示", "是否确定删除当前属性?", "确定", "取消") != 0)
        return;
    assert(_currentType != nullptr);
    if (_currentType == nullptr)
        return;

    auto idxItem = ui.tableWidgetAttributes->item(ui.tableWidgetAttributes->currentRow(), 0);
    if (idxItem == nullptr)
        return;
    auto idx = idxItem->data(Qt::UserRole).toInt();
    auto itemIndex = ui.tableWidgetAttributes->currentRow();

    auto& attributes = _currentType->attributes;
    assert(idx >= 0 && idx < attributes.size());
    if (idx < 0 || idx >= attributes.size())
        return;
    {
        attributes.erase(attributes.begin() + idx);
        auto item = ui.tableWidgetAttributes->takeItem(idx, 0);
        if (item != nullptr)
            delete item;
        item = ui.tableWidgetAttributes->takeItem(idx, 1);
        if (item != nullptr)
            delete item;
        ui.tableWidgetAttributes->blockSignals(true);
        ui.tableWidgetAttributes->removeRow(idx);
        ui.tableWidgetAttributes->blockSignals(false);
    }
    for (int i = itemIndex; i < ui.tableWidgetAttributes->rowCount(); ++ i)
    {
        auto item = ui.tableWidgetAttributes->item(i, 0);
        if (item == nullptr)
            continue;
        item->setData(Qt::UserRole, item->data(Qt::UserRole).toInt() - 1);
    }

    updateType();
    if (ui.tableWidgetAttributes->rowCount() > 0 && ui.tableWidgetAttributes->columnCount() > 0)
    {
        if (itemIndex >= ui.tableWidgetAttributes->rowCount())
            ui.tableWidgetAttributes->setCurrentCell(ui.tableWidgetAttributes->rowCount() - 1, 0);
        else
            ui.tableWidgetAttributes->setCurrentCell(itemIndex, 0);
    }
}

void WIZDesignerDictionaryMainWindow::slotPushButtonSaveClicked()
{
    // 根节点
    const char* XMLNode_Root                    = "Root";
    // 节点类型
    const char* XMLNode_Type                    = "Type";
    // 类型名称
    const char* XMLNode_TypeName                = "Name";
    // 类型父节点类型列表
    const char* XMLNode_TypeParents             = "Parents";
    // 类型标志列表
    const char* XMLNode_TypeFlags               = "Flags";
    // 类型属性列表
    const char* XMLNode_TypeAttrs               = "Attributes";
    // 类型属性列表中指定的系统属性
    const char* XMLAttr_TypeAttrsSystem         = "system";
    // 类型属性项
    const char* XMLNode_TypeAttr                = "Attribute";
    // 类型描述
    const char* XMLNode_TypeDescribe            = "Describe";
    // 类型资源列表
    const char* XMLNode_TypeResources           = "Resources";
    // 类型资源项
    const char* XMLNode_TypeResource            = "Resource";
    // 类型资源类型
    const char* XMLAttr_TypeResourceType        = "type";
    // 类型资源值
    const char* XMLAttr_TypeResourceValue       = "value";


    // 属性名称
    const char* XMLAttr_AttrName                = "name";
    // 属性名称简称
    const char* XMLAttr_AttrSampleName          = "sampleName";
    // 属性值类型
    const char* XMLAttr_AttrType                = "type";
    // 属性类别
    const char* XMLAttr_AttrCategory            = "category";
    // 属性标记
    const char* XMLAttr_AttrFlags               = "flags";
    // 属性默认值
    const char* XMLAttr_AttrDefault             = "defaultValue";
    // 最小值
    const char* XMLAttr_AttrMinValue            = "minValue";
    // 最大值
    const char* XMLAttr_AttrMaxValue            = "maxValue";
    // 步长
    const char* XMLAttr_AttrStep                = "step";
    // 精度
    const char* XMLAttr_AttrDecimals            = "decimals";
    // 字典名称
    const char* XMLAttr_AttrDictionaryName      = "dictionaryName";
    // 正则表达式
    const char* XMLAttr_AttrRegexp              = "regexp";
    // 引用属性引用节点所属模块的名称
    const char* XMLAttr_AttrRefNodeModuleName   = "refNodeModuleName";


    bool newAttributeUi = true;

    auto testNodes = [&] (const WD::WDCore& core, const WD::StringVector& types, const WIZDesignerDictionaryCommon::ModuleType& moduleType)
    {
        auto moduleItr = _moduleInfos.find(moduleType);
        if (moduleItr == _moduleInfos.end())
            return;
        auto& typeInfos = moduleItr->second;

        char xmlPath[1024] = { 0 };
        sprintf_s(xmlPath, sizeof(xmlPath), "%s/nodeTypeConfig/nodeTypeConfig%s.xml", core.dataDirPath(), WIZDesignerDictionaryCommon::ModuleTypeToString(moduleType));
        WD::XMLDoc doc;
        WD::XMLNode* xmlinfo = doc.allocate_node(rapidxml::node_pi, "xml version='1.0' encoding='utf-8'");
        doc.append_node(xmlinfo);
        WD::XMLNode* xmlNodeRoot = doc.allocate_node(rapidxml::node_element, XMLNode_Root);
        doc.append_node(xmlNodeRoot);

        std::vector<WD::WDBMTypeDesc::Flag> allFlags = {WDBMTypeDesc::Flag::F_AutoGeneration
            , WDBMTypeDesc::Flag::F_TriggerUpdate};
        for (auto& each : types)
        {
            auto itr = typeInfos.find(each);
            if (itr == typeInfos.end())
                continue;
            auto pType = itr->second;
            if (pType == nullptr)
            {
                assert(false);
                continue;
            }
            const auto& name                = pType->name;
            const auto  parentStr           = pType->parentsStr();
            const auto& describe            = pType->describe;
            const auto& resources           = pType->resources;
            const auto& flags               = pType->flags;
            const auto& bHavePosition       = pType->bHavePosition;
            const auto& bHaveOrientation    = pType->bHaveOrientation;
            const auto& attributes          = pType->attributes;

            // 类型项
            WD::XMLNode* xmlNodeType = doc.allocate_node(rapidxml::node_element, XMLNode_Type);
            xmlNodeRoot->append_node(xmlNodeType);
            // 类型名称
            WD::XMLNode* xmlNodeName = doc.allocate_node(rapidxml::node_element, XMLNode_TypeName, doc.allocate_string(name.c_str()));
            xmlNodeType->append_node(xmlNodeName);
            // 父节点类型列表
            WD::XMLNode* xmlNodeParent = doc.allocate_node(rapidxml::node_element, XMLNode_TypeParents, doc.allocate_string(parentStr.c_str()));
            xmlNodeType->append_node(xmlNodeParent);
            // 类型的标志
            std::string typeFlags;
            for (auto& eachFlag : allFlags)
            {
                if (flags.hasFlag(eachFlag))
                {
                    if (!typeFlags.empty())
                        typeFlags.push_back(' ');
                    typeFlags += WD::WDBMTypeDesc::FlagToStr(eachFlag);
                }
            }
            if (!typeFlags.empty())
            {
                WD::XMLNode* xmlNodeFalgs = doc.allocate_node(rapidxml::node_element, XMLNode_TypeFlags, doc.allocate_string(typeFlags.c_str()));
                xmlNodeType->append_node(xmlNodeFalgs);
            }
            WD::XMLNode* xmlNodeAttributes = doc.allocate_node(rapidxml::node_element, XMLNode_TypeAttrs);

            // 类型属性列表中指定的系统属性
            char attrsSystemStr[2048] = { 0 };
            if (bHavePosition)
                sprintf_s(attrsSystemStr, sizeof(attrsSystemStr), "%s %s", attrsSystemStr, "Position");
            if (bHaveOrientation)
                sprintf_s(attrsSystemStr, sizeof(attrsSystemStr), "%s %s", attrsSystemStr, "Orientation");

            if (strlen(attrsSystemStr) > 0)
            {
                // 加一是为了跳过第一个空格
                auto xmlAttrsSystemStr = doc.allocate_attribute(XMLAttr_TypeAttrsSystem, doc.allocate_string(attrsSystemStr + 1));
                xmlNodeAttributes->append_attribute(xmlAttrsSystemStr);
            }

            for (auto& attribute : attributes)
            {
                if(attribute.isCustom())
                    continue;  
                // 跳过几个固定的属性，因为这些属性是从代码中注册的，不需要配置
                if (attribute.name() == WIZDesignerDictionaryCommon::NodeTypePropertyName
                    || attribute.name() == WIZDesignerDictionaryCommon::NodeNamePropertyName
                    || attribute.name() == WIZDesignerDictionaryCommon::NodeRefNoPropertyName
                    || attribute.name() == WIZDesignerDictionaryCommon::NodeOwnerPropertyName
                    || attribute.name() == WIZDesignerDictionaryCommon::NodeLockPropertyName
                    || attribute.name() == WIZDesignerDictionaryCommon::NodeVisiblePropertyName)
                    continue;
                // 属性项
                WD::XMLNode* xmlNodeAttribute = doc.allocate_node(rapidxml::node_element, XMLNode_TypeAttr);
                xmlNodeAttributes->append_node(xmlNodeAttribute);
                // 属性名称
                auto attributeName = doc.allocate_attribute(XMLAttr_AttrName, doc.allocate_string(attribute.name().c_str()));
                xmlNodeAttribute->append_attribute(attributeName);
                // 属性数值类型
                auto attributeType = doc.allocate_attribute(XMLAttr_AttrType, doc.allocate_string(WD::WDBMAttrValue::TypeToStr(attribute.type())));
                xmlNodeAttribute->append_attribute(attributeType);
                // 属性名称简称
                auto& sampleName = attribute.sampleName();
                if (!sampleName.empty())
                {
                    auto attributeSampleName = doc.allocate_attribute(XMLAttr_AttrSampleName, doc.allocate_string(sampleName.c_str()));
                    xmlNodeAttribute->append_attribute(attributeSampleName);
                }
                // 属性类别
                auto& categoryStr = attribute.category();
                if (!categoryStr.empty())
                {
                    auto attributeCategoryValue = doc.allocate_attribute(XMLAttr_AttrCategory, doc.allocate_string(categoryStr.c_str()));
                    xmlNodeAttribute->append_attribute(attributeCategoryValue);
                }
                // 属性默认值
                auto defaultValueStr = attribute.defaultValue().convertToString();
                if (!defaultValueStr.empty())
                {
                    auto attributeDefaultValue = doc.allocate_attribute(XMLAttr_AttrDefault, doc.allocate_string(defaultValueStr.c_str()));
                    xmlNodeAttribute->append_attribute(attributeDefaultValue);
                }
                if (attribute.type() == WD::WDBMAttrValueType::T_Int || attribute.type() == WD::WDBMAttrValueType::T_Double)
                {
                    // 属性的最小值
                    auto minValueStr = attribute.minimumValue().convertToString();
                    if (!minValueStr.empty())
                    {
                        auto attributeMinValue = doc.allocate_attribute(XMLAttr_AttrMinValue, doc.allocate_string(minValueStr.c_str()));
                        xmlNodeAttribute->append_attribute(attributeMinValue);
                    }
                    // 属性的最大值
                    auto maxValueStr = attribute.maximumValue().convertToString();
                    if (!maxValueStr.empty())
                    {
                        auto attributeMaxValue = doc.allocate_attribute(XMLAttr_AttrMaxValue, doc.allocate_string(maxValueStr.c_str()));
                        xmlNodeAttribute->append_attribute(attributeMaxValue);
                    }
                    // 属性的步长
                    auto stepStr = attribute.singleStep().convertToString();
                    if (!stepStr.empty())
                    {
                        auto attributeStepValue = doc.allocate_attribute(XMLAttr_AttrStep, doc.allocate_string(stepStr.c_str()));
                        xmlNodeAttribute->append_attribute(attributeStepValue);
                    }
                }
                // 浮点数精度
                if ((attribute.type() == WD::WDBMAttrValueType::T_Double) && (attribute.decimals() != WD::WDBMAttrDesc::DefaultDecimals))
                {
                    auto decimalsStr = WD::ToString(attribute.decimals());
                    auto attributeDecimalsValue = doc.allocate_attribute(XMLAttr_AttrDecimals, doc.allocate_string(decimalsStr.c_str()));
                    xmlNodeAttribute->append_attribute(attributeDecimalsValue);
                }
                // 字典名称
                if (attribute.type() == WD::WDBMAttrValueType::T_Word)
                {
                    auto& dictionaryName = attribute.enumDictionaryName();
                    if (!dictionaryName.empty())
                    {
                        auto attributeDictionaryNameValue = doc.allocate_attribute(XMLAttr_AttrDictionaryName, doc.allocate_string(dictionaryName.c_str()));
                        xmlNodeAttribute->append_attribute(attributeDictionaryNameValue);
                    }
                }
                // 正则表达式
                if (attribute.type() == WD::WDBMAttrValueType::T_String
                    || attribute.type() == WD::WDBMAttrValueType::T_StringVector
                    || attribute.type() == WD::WDBMAttrValueType::T_Word)
                {
                    auto regexpStr = WD::ToString(attribute.regexp());
                    if (!regexpStr.empty())
                    {
                        auto attributeRegexpValue = doc.allocate_attribute(XMLAttr_AttrRegexp, doc.allocate_string(regexpStr.c_str()));
                        xmlNodeAttribute->append_attribute(attributeRegexpValue);
                    }
                }
                // 引用属性引用节点所属模块的名称
                if (attribute.type() == WD::WDBMAttrValueType::T_NodeRef
                    || attribute.type() == WD::WDBMAttrValueType::T_NodeRefs)
                {
                    auto& value = attribute.refNodeModuleName();
                    if (!value.empty())
                    {
                        auto attributeRefNodeModuleName = doc.allocate_attribute(XMLAttr_AttrRefNodeModuleName, doc.allocate_string(value.c_str()));
                        xmlNodeAttribute->append_attribute(attributeRefNodeModuleName);
                    }
                }
                // 属性标记
                WD::StringVector flagStrs;
                if (attribute.flags().hasFlag(WD::WDBMAttrDesc::Flag::F_Hidden))
                    flagStrs.push_back(WD::WDBMAttrDesc::FlagToStr(WD::WDBMAttrDesc::Flag::F_Hidden));
                if (attribute.flags().hasFlag(WD::WDBMAttrDesc::Flag::F_ReadOnly))
                    flagStrs.push_back(WD::WDBMAttrDesc::FlagToStr(WD::WDBMAttrDesc::Flag::F_ReadOnly));
                if (attribute.flags().hasFlag(WD::WDBMAttrDesc::Flag::F_Update))
                    flagStrs.push_back(WD::WDBMAttrDesc::FlagToStr(WD::WDBMAttrDesc::Flag::F_Update));
                if (!flagStrs.empty())
                {
                    auto attributeFlag = doc.allocate_attribute(XMLAttr_AttrFlags
                        , doc.allocate_string(WD::StringConcat(flagStrs, " ").c_str()));
                    xmlNodeAttribute->append_attribute(attributeFlag);
                }
            }
            xmlNodeType->append_node(xmlNodeAttributes);
            // 类型描述
            WD::XMLNode* xmlNodeDescribe = doc.allocate_node(rapidxml::node_element, XMLNode_TypeDescribe, doc.allocate_string(describe.c_str()));
            xmlNodeType->append_node(xmlNodeDescribe);
            // 资源列表
            WD::XMLNode* xmlNodeResources = doc.allocate_node(rapidxml::node_element, XMLNode_TypeResources);
            xmlNodeType->append_node(xmlNodeResources);
            for (auto& resource : resources)
            {
                WD::XMLNode* xmlNodeResource = doc.allocate_node(rapidxml::node_element, XMLNode_TypeResource);
                xmlNodeResources->append_node(xmlNodeResource);
                // 资源类型
                auto resourceType = doc.allocate_attribute(XMLAttr_TypeResourceType, doc.allocate_string(resource.first.c_str()));
                xmlNodeResource->append_attribute(resourceType);
                // 资源值
                auto resourceValue = doc.allocate_attribute(XMLAttr_TypeResourceValue, doc.allocate_string(resource.second.c_str()));
                xmlNodeResource->append_attribute(resourceValue);
            }
        }
        std::string outString;
        rapidxml::print(std::back_inserter(outString), doc, 0);
        //只读方式打开xml文件
        QFile file(QString::fromStdString(xmlPath));
        if (file.open(QIODevice::WriteOnly))
        {
            file.write(outString.data(), outString.size());
            //关闭文件
            file.close();
        }
        else
        {
            QMessageBox::information(nullptr, "提示", "元件模块配置文件保存失败!");
        }
    };
    if (newAttributeUi)
    {
        testNodes(_core, _catalogTypes,     WIZDesignerDictionaryCommon::MT_Catalog);
        testNodes(_core, _designTypes, WIZDesignerDictionaryCommon::MT_Design);
    }

    auto moduleTypeExport = [&] (const WD::WDCore& core, const WD::StringVector& types
        , const WIZDesignerDictionaryCommon::ModuleType& moduleType) ->bool
    {
        auto moduleItr = _moduleInfos.find(moduleType);
        if (moduleItr == _moduleInfos.end())
            return false;
        auto& typeInfos = moduleItr->second;

        char xmlPath[1024] = { 0 };
        sprintf_s(xmlPath, sizeof(xmlPath), "%s/nodeTypeConfig/nodeTypeConfig%s.xml", core.dataDirPath(), WIZDesignerDictionaryCommon::ModuleTypeToString(moduleType));
        WD::XMLDoc doc;
        WD::XMLNode* xmlinfo = doc.allocate_node(rapidxml::node_pi, "xml version='1.0' encoding='utf-8'");
        doc.append_node(xmlinfo);
        WD::XMLNode* xmlNodeRoot = doc.allocate_node(rapidxml::node_element, XMLNode_Root);
        doc.append_node(xmlNodeRoot);
        for (auto& each : types)
        {
            auto itr = typeInfos.find(each);
            if (itr == typeInfos.end())
                continue;
            auto& nodeType = itr->first;
            auto pType = itr->second;
            if (pType == nullptr)
            {
                assert(false);
                continue;
            }
            auto& attributes    = pType->attributes;
            auto  parentStr     = pType->parentsStr();
            auto& describe      = pType->describe;
            auto& resources     = pType->resources;
            WD::XMLNode* xmlNodeType = doc.allocate_node(rapidxml::node_element, XMLNode_Type);
            xmlNodeRoot->append_node(xmlNodeType);
            WD::XMLNode* xmlNodeName = doc.allocate_node(rapidxml::node_element, XMLNode_TypeName, doc.allocate_string(nodeType.c_str()));
            xmlNodeType->append_node(xmlNodeName);
            WD::XMLNode* xmlNodeParent = doc.allocate_node(rapidxml::node_element, XMLNode_TypeParents, doc.allocate_string(parentStr.c_str()));
            xmlNodeType->append_node(xmlNodeParent);
            WD::XMLNode* xmlNodeAttributes = doc.allocate_node(rapidxml::node_element, XMLNode_TypeAttrs);
            for (auto& attribute : attributes)
            {
                if(!attribute.isCustom())
                    continue;
                WD::XMLNode* xmlNodeAttribute = doc.allocate_node(rapidxml::node_element, XMLNode_TypeAttr);
                xmlNodeAttributes->append_node(xmlNodeAttribute);
                // 自定义属性,需要移除前缀 ':' 保存
                const char* srcName = attribute.name().c_str();
                const char* tName = srcName + 1;
                auto attributeName = doc.allocate_attribute(XMLAttr_AttrName, doc.allocate_string(tName));
                xmlNodeAttribute->append_attribute(attributeName);
                auto attributeType = doc.allocate_attribute(XMLAttr_AttrType, doc.allocate_string(WD::WDBMAttrValue::TypeToStr(attribute.type())));
                xmlNodeAttribute->append_attribute(attributeType);
                auto defaultValueStr = attribute.defaultValue().convertToString();
                if (!defaultValueStr.empty())
                {
                    auto attributeDefaultValue = doc.allocate_attribute(XMLAttr_AttrDefault, doc.allocate_string(defaultValueStr.c_str()));
                    xmlNodeAttribute->append_attribute(attributeDefaultValue);
                }

                WD::StringVector flags;
                if (attribute.flags().hasFlag(WD::WDBMAttrDesc::Flag::F_Hidden))
                    flags.push_back(WD::WDBMAttrDesc::FlagToStr(WD::WDBMAttrDesc::Flag::F_Hidden));
                if (attribute.flags().hasFlag(WD::WDBMAttrDesc::Flag::F_ReadOnly))
                    flags.push_back(WD::WDBMAttrDesc::FlagToStr(WD::WDBMAttrDesc::Flag::F_ReadOnly));
                if (attribute.flags().hasFlag(WD::WDBMAttrDesc::Flag::F_Update))
                    flags.push_back(WD::WDBMAttrDesc::FlagToStr(WD::WDBMAttrDesc::Flag::F_Update));
                if (!flags.empty())
                {
                    auto attributeFlag = doc.allocate_attribute(XMLAttr_AttrFlags, doc.allocate_string(WD::StringConcat(flags, "|").c_str()));
                    xmlNodeAttribute->append_attribute(attributeFlag);
                }
            }
            xmlNodeType->append_node(xmlNodeAttributes);
            WD::XMLNode* xmlNodeDescribe = doc.allocate_node(rapidxml::node_element, XMLNode_TypeDescribe, doc.allocate_string(describe.c_str()));
            xmlNodeType->append_node(xmlNodeDescribe);
            WD::XMLNode* xmlNodeResources = doc.allocate_node(rapidxml::node_element, XMLNode_TypeResources);
            xmlNodeType->append_node(xmlNodeResources);
            for (auto& resource : resources)
            {
                WD::XMLNode* xmlNodeResource = doc.allocate_node(rapidxml::node_element, XMLNode_TypeResource);
                xmlNodeResources->append_node(xmlNodeResource);
                auto resourceType = doc.allocate_attribute(XMLAttr_TypeResourceType, doc.allocate_string(resource.first.c_str()));
                xmlNodeResource->append_attribute(resourceType);
                auto resourceValue = doc.allocate_attribute(XMLAttr_TypeResourceValue, doc.allocate_string(resource.second.c_str()));
                xmlNodeResource->append_attribute(resourceValue);
            }
        }
        std::string outString;
        rapidxml::print(std::back_inserter(outString), doc, 0);
        //只读方式打开xml文件
        QFile file(QString::fromStdString(xmlPath));
        if (file.open(QIODevice::WriteOnly))
        {
            file.write(outString.data(), outString.size());
            //关闭文件
            file.close();

            return true;
        }
        return false;
    };
    if (!newAttributeUi)
    {
        // 保存元件模块的配置文件
        if (!moduleTypeExport(_core, _catalogTypes, WIZDesignerDictionaryCommon::MT_Catalog))
            QMessageBox::information(nullptr, "提示", "元件模块配置文件保存失败!");

        // 保存设计模块的配置文件
        if (!moduleTypeExport(_core, _designTypes, WIZDesignerDictionaryCommon::MT_Design))
            QMessageBox::information(nullptr, "提示", "设计模块配置文件保存失败!");
    }
    QMessageBox::information(nullptr, "提示", "保存完成!");
}
void WIZDesignerDictionaryMainWindow::slotPushButtonExitClicked()
{
    this->close();
}

void WIZDesignerDictionaryMainWindow::slotLineEditTypeNameEditingFinished()
{
    assert(_currentType != nullptr);
    if (_currentType == nullptr)
        return;

    std::string newName = ui.lineEditTypeName->text().toLocal8Bit().data();
    auto moduleItr = _moduleInfos.find(WIZDesignerDictionaryCommon::ModuleType(ui.comboBoxModule->currentData().toInt()));
    if (moduleItr == _moduleInfos.end())
        return;
    auto& typeInfos = moduleItr->second;
    auto itr = typeInfos.find(newName);
    if (itr != typeInfos.end() && itr->second != _currentType)
    {
        QMessageBox::warning(this, "警告", "名称已存在");
        ui.lineEditTypeName->setText(_currentType->name.c_str());
        return;
    }
    WDBMBase* pMgr = nullptr;
    switch (ui.comboBoxModule->currentData().toInt())
    {
    case WIZDesignerDictionaryCommon::ModuleType::MT_Catalog:
        pMgr = &_core.getBMCatalog();
        break;
    case WIZDesignerDictionaryCommon::ModuleType::MT_Design:
        pMgr = &_core.getBMDesign();
        break;
    default:
        assert(false);
        break;
    }
    _currentType->name = newName;
    auto nodeTypeItem = ui.tableWidgetTypes->item(ui.tableWidgetTypes->currentRow(), 0);
    if (nodeTypeItem != nullptr)
    {
        nodeTypeItem->setText(_currentType->name.c_str());
        auto nodeTypeTransItem = ui.tableWidgetTypes->item(ui.tableWidgetTypes->currentRow(), 1);
        if (nodeTypeTransItem != nullptr && pMgr != nullptr)
        {
            auto& transName = pMgr->trT(_currentType->name);
            if (transName != _currentType->name)
                nodeTypeTransItem->setText(transName.c_str());
            else
                nodeTypeTransItem->setText("");
        }
    }
}
void WIZDesignerDictionaryMainWindow::slotLineEditAttrNameEditingFinished()
{
    assert(_currentAttr != nullptr && _currentType != nullptr);
    if (_currentAttr == nullptr || _currentType == nullptr)
        return;
    std::string name = ui.lineEditAttrName->text().toUtf8().data();
    if (name.empty())
        return;
    auto checkAttribute = [](const WIZDesignerDictionaryCommon::Attributes& attrs, const std::string& attrName, int type) ->bool
    {
        for (auto& each : attrs)
            if (each.name() == attrName && each.type() == type)
                return false;
        return true;
    };
    auto type = ui.comboBoxDataType->currentData().toInt();

    // 名称未修改
    if (_currentAttr->name() == name)
        return;

    auto& attributes = _currentType->attributes;
    ui.lineEditAttrName->blockSignals(true);
    if (!checkAttribute(attributes, name, type))
        QMessageBox::warning(this, "警告", "名称已存在");
    else
        _currentAttr->setName(name);
    ui.lineEditAttrName->blockSignals(false);
    return updateType(ui.tableWidgetAttributes->currentRow());
}

void WIZDesignerDictionaryMainWindow::updateType(const int index)
{
    _currentType = nullptr;
    ui.tableWidgetAttributes->blockSignals(true);
    ui.tableWidgetAttributes->clearContents();
    ui.tableWidgetAttributes->setRowCount(0);
    ui.tableWidgetAttributes->blockSignals(false);
    auto nodeTypeItem = ui.tableWidgetTypes->item(ui.tableWidgetTypes->currentRow(), 0);
    if (nodeTypeItem == nullptr)
    {
        nodeTypeItem = ui.tableWidgetTypes->item(0, 0);
        if (nodeTypeItem == nullptr)
            return assert(false);
        ui.tableWidgetTypes->blockSignals(true);
        ui.tableWidgetTypes->setCurrentItem(nodeTypeItem);
        ui.tableWidgetTypes->blockSignals(false);
    }
    auto nodeType = nodeTypeItem->text().toUtf8().toStdString();
    auto moduleItr = _moduleInfos.find(WIZDesignerDictionaryCommon::ModuleType(ui.comboBoxModule->currentData().toInt()));
    if (moduleItr == _moduleInfos.end())
        return;
    auto& typeInfos = moduleItr->second;
    auto itr = typeInfos.find(nodeType);
    if (itr != typeInfos.end())
        _currentType = itr->second;
    assert(_currentType != nullptr);
    if (_currentType == nullptr)
        return;
    WD::WDBMBase* pMgr = nullptr;
    switch (ui.comboBoxModule->currentData().toInt())
    {
    case WIZDesignerDictionaryCommon::ModuleType::MT_Catalog:
        pMgr = &_core.getBMCatalog();
        break;
    case WIZDesignerDictionaryCommon::ModuleType::MT_Design:
        pMgr = &_core.getBMDesign();
        break;
    default:
        assert(false);
        break;
    }
    assert(pMgr != nullptr);
    if (pMgr == nullptr)
        return;
    auto& attributes = _currentType->attributes;
    ui.tableWidgetAttributes->blockSignals(true);
    int attrSize = static_cast<int>(attributes.size());
    int row = index;
    int currentRow = 0;
    for (int idx = 0; idx < attrSize; idx++)
    {
        auto& attribute = attributes[idx];
        auto& name = attribute.name();
        std::string transName = pMgr->trA(name);
        if (transName == name)
            transName = "";
        assert(!name.empty());
        auto item = new QTableWidgetItem(name.c_str());
        item->setData(Qt::UserRole, idx);
        auto transItem = new QTableWidgetItem(transName.c_str());

        ui.tableWidgetAttributes->setRowCount(currentRow + 1);
        ui.tableWidgetAttributes->setItem(currentRow, 0, item);
        ui.tableWidgetAttributes->setItem(currentRow, 1, transItem);
        ++currentRow;

        item->setFlags(Qt::ItemFlag::NoItemFlags | Qt::ItemFlag::ItemIsSelectable | Qt::ItemFlag::ItemIsEnabled);
        transItem->setFlags(Qt::ItemFlag::NoItemFlags | Qt::ItemFlag::ItemIsSelectable | Qt::ItemFlag::ItemIsEnabled);
    }

    ui.checkBoxAutoGeneration->blockSignals(true);
    ui.checkBoxAutoGeneration->setChecked(_currentType->flags.hasFlag(WD::WDBMTypeDesc::Flag::F_AutoGeneration));
    ui.checkBoxAutoGeneration->blockSignals(false);

    ui.checkBoxTriggerUpdate->blockSignals(true);
    ui.checkBoxTriggerUpdate->setChecked(_currentType->flags.hasFlag(WD::WDBMTypeDesc::Flag::F_TriggerUpdate));
    ui.checkBoxTriggerUpdate->blockSignals(false);

    if (row >= attrSize)
        row = attrSize - 1;
    ui.tableWidgetAttributes->setCurrentCell(row, 0);
    ui.tableWidgetAttributes->blockSignals(true);
    updateAttribute(attributes[row]);
    ui.tableWidgetAttributes->blockSignals(false);
    // 类型名称
    ui.lineEditTypeName->blockSignals(true);
    ui.lineEditTypeName->setText(QString::fromUtf8(_currentType->name.c_str()));
    ui.lineEditTypeName->blockSignals(false);
    // 父节点列表
    ui.textEditParentsList->blockSignals(true);
    ui.textEditParentsList->setText(QString::fromUtf8(_currentType->parentsStr().c_str()));
    ui.textEditParentsList->blockSignals(false);
    // 资源列表
    ui.tableWidgetResource->blockSignals(true);
    ui.tableWidgetResource->clearContents();
    ui.tableWidgetResource->setRowCount(static_cast<int>(_currentType->resources.size()));
    int idx = 0;
    for (auto& each : _currentType->resources)
    {
        auto itemName = new QTableWidgetItem(each.first.c_str());
        auto itemValue = new QTableWidgetItem(each.second.c_str());

        ui.tableWidgetResource->setItem(idx, 0, itemName);
        ui.tableWidgetResource->setItem(idx, 1, itemValue);
        ++idx;
    }
    ui.tableWidgetResource->blockSignals(false);
    // 类型描述
    ui.textEditDescribe->blockSignals(true);
    ui.textEditDescribe->setText(QString::fromUtf8(_currentType->describe.c_str()));
    ui.textEditDescribe->blockSignals(false);
    ui.lineEditTypeName->blockSignals(true);
    ui.lineEditTypeName->setText(_currentType->name.c_str());
    ui.lineEditTypeName->blockSignals(false);
    auto attrStr = ui.lineEditAttributeSearch->text();
    if (!attrStr.isEmpty())
        slotLineEditAttributeSearchChanged(attrStr);
}

void WIZDesignerDictionaryMainWindow::updateAttribute(WD::WDBMAttrDesc& attr)
{
    if (WIZDesignerDictionaryCommon::IsAttributeCanEdit(attr))
        setAttributeInfoEnabled(true);
    else
        setAttributeInfoEnabled(false);
    _currentAttr = &attr;
    assert(_currentAttr != nullptr);
    for (int idx = 0; idx < ui.comboBoxDataType->count(); idx++)
    {
        if (attr.type() != ui.comboBoxDataType->itemData(idx, Qt::UserRole).toInt())
            continue;
        ui.comboBoxDataType->setCurrentIndex(idx);
        break;
    }
    ui.groupBoxDigit->blockSignals(true);
    ui.doubleSpinBoxMax->blockSignals(true);
    ui.doubleSpinBoxMin->blockSignals(true);
    ui.doubleSpinBoxStep->blockSignals(true);
    ui.spinBoxDecimals->blockSignals(true);
    ui.lineEditRegex->blockSignals(true);
    ui.comboBoxRefNodeModule->blockSignals(true);

    setAttributeInfoEnabled(false, true);

    ui.doubleSpinBoxMax->setValue(0.0);
    ui.doubleSpinBoxMin->setValue(0.0);
    ui.doubleSpinBoxStep->setValue(0.0);
    ui.spinBoxDecimals->setValue(2);
    ui.comboBoxRefNodeModule->clear();
    ui.lineEditEnumDictName->clear();
    ui.lineEditRegex->clear();

    if (!TOOL_READ_ONLY)
    {
        switch (attr.type())
        {
        case WD::WDBMAttrValueType::T_Bool:
            break;
        case WD::WDBMAttrValueType::T_Int:
        case WD::WDBMAttrValueType::T_Double:
        case WD::WDBMAttrValueType::T_IntVector:
        case WD::WDBMAttrValueType::T_DoubleVector:
            {
                ui.doubleSpinBoxMax->setEnabled(true);
                ui.doubleSpinBoxMin->setEnabled(true);
                ui.doubleSpinBoxStep->setEnabled(true);
                ui.spinBoxDecimals->setEnabled(true);

                auto& max = attr.maximumValue();
                if (max.valid())
                    ui.doubleSpinBoxMax->setValue(max.convertToDouble());

                auto& min = attr.minimumValue();
                if (min.valid())
                    ui.doubleSpinBoxMin->setValue(min.convertToDouble());

                auto& step = attr.singleStep();
                if (step.valid())
                    ui.doubleSpinBoxStep->setValue(step.convertToDouble());

                ui.spinBoxDecimals->setValue(attr.decimals());
            }
            break;
        case WD::WDBMAttrValueType::T_StringVector:
        case WD::WDBMAttrValueType::T_String:
            {
                ui.lineEditRegex->setReadOnly(false);
                ui.lineEditRegex->setText(attr.regexp().c_str());
            }
            break;
        case WD::WDBMAttrValueType::T_Word:
            {
                ui.lineEditRegex->setReadOnly(false);
                ui.lineEditRegex->setText(attr.regexp().c_str());
                ui.lineEditEnumDictName->setReadOnly(false);
                ui.lineEditRegex->setText(attr.enumDictionaryName().c_str());
            }
            break;
        case WD::WDBMAttrValueType::T_NodeRef:
        case WD::WDBMAttrValueType::T_NodeRefs:
            {
                ui.comboBoxRefNodeModule->setEnabled(true);
                auto type = WIZDesignerDictionaryCommon::ModuleTypeFromString(attr.refNodeModuleName().c_str());
                ui.comboBoxRefNodeModule->addItem(QString::fromUtf8("设计模块"), WIZDesignerDictionaryCommon::MT_Design);
                ui.comboBoxRefNodeModule->addItem(QString::fromUtf8("元件模块"), WIZDesignerDictionaryCommon::MT_Catalog);

                for (int i = 0; i < ui.comboBoxRefNodeModule->count(); ++i)
                {
                    if (type == ui.comboBoxRefNodeModule->itemData(i).toInt())
                    {
                        ui.comboBoxRefNodeModule->setCurrentIndex(i);
                        break;
                    }
                }
            }
            break;
        case WD::WDBMAttrValueType::T_DVec2:
        case WD::WDBMAttrValueType::T_DVec3:
        case WD::WDBMAttrValueType::T_DQuat:
        case WD::WDBMAttrValueType::T_Color:
        case WD::WDBMAttrValueType::T_Uuid:
        case WD::WDBMAttrValueType::T_LevelRange:
        case WD::WDBMAttrValueType::T_GeometryRef:
        default:
            break;
        }
    }
    ui.comboBoxRefNodeModule->blockSignals(false);
    ui.groupBoxDigit->blockSignals(false);
    ui.doubleSpinBoxMax->blockSignals(false);
    ui.doubleSpinBoxMin->blockSignals(false);
    ui.doubleSpinBoxStep->blockSignals(false);
    ui.spinBoxDecimals->blockSignals(false);
    ui.lineEditRegex->blockSignals(false);

    ui.lineEditAttrName->blockSignals(true);
    ui.lineEditAttrName->setText(QString::fromUtf8(attr.name().c_str()));
    ui.lineEditAttrName->blockSignals(false);

    ui.lineEditAttrSampleName->blockSignals(true);
    ui.lineEditAttrSampleName->setText(QString::fromUtf8(attr.sampleName().c_str()));
    ui.lineEditAttrSampleName->blockSignals(false);

    ui.lineEditDefaultValue->blockSignals(true);
    ui.lineEditDefaultValue->setText(QString::fromUtf8(attr.defaultValue().convertToString().c_str()));
    ui.lineEditDefaultValue->blockSignals(false);

    auto& flags = attr.flags();
    ui.checkBoxReadOnly->blockSignals(true);
    ui.checkBoxReadOnly->setChecked(flags.hasFlag(WD::WDBMAttrDesc::Flag::F_ReadOnly));
    ui.checkBoxReadOnly->blockSignals(false);

    ui.checkBoxHide->blockSignals(true);
    ui.checkBoxHide->setChecked(flags.hasFlag(WD::WDBMAttrDesc::Flag::F_Hidden));
    ui.checkBoxHide->blockSignals(false);
}

void WIZDesignerDictionaryMainWindow::setAttributeInfoEnabled(bool bEnable, bool bOnlySpecialPart)
{
    if (!TOOL_READ_ONLY)
    {
        // 这些是属性的通用参数
        if (!bOnlySpecialPart)
        {
            ui.lineEditAttrName->setReadOnly(!bEnable);
            ui.comboBoxDataType->setEnabled(bEnable);
            ui.checkBoxReadOnly->setEnabled(bEnable);
            ui.checkBoxHide->setEnabled(bEnable);
            ui.lineEditDefaultValue->setReadOnly(!bEnable);

            ui.pushButtonDelete->setEnabled(bEnable);
        }
        // 这些是属性的特殊参数,即只有部分类型的属性才有的参数
        ui.doubleSpinBoxMin->setEnabled(bEnable);
        ui.doubleSpinBoxMax->setEnabled(bEnable);
        ui.doubleSpinBoxStep->setEnabled(bEnable);
        ui.spinBoxDecimals->setEnabled(bEnable);
        ui.lineEditRegex->setReadOnly(!bEnable);
        ui.lineEditEnumDictName->setReadOnly(!bEnable);
        ui.comboBoxRefNodeModule->setEnabled(bEnable);
    }
}

void WIZDesignerDictionaryMainWindow::initDialog()
{
    ui.comboBoxModule->addItem("设计模块", WIZDesignerDictionaryCommon::MT_Design);
    ui.comboBoxModule->addItem("元件模块", WIZDesignerDictionaryCommon::MT_Catalog);
    ui.comboBoxModule->setCurrentIndex(0);

    ui.comboBoxDataType->addItem("Bool", WD::WDBMAttrValueType::T_Bool);
    ui.comboBoxDataType->addItem("Int", WD::WDBMAttrValueType::T_Int);
    ui.comboBoxDataType->addItem("Double", WD::WDBMAttrValueType::T_Double);
    ui.comboBoxDataType->addItem("String", WD::WDBMAttrValueType::T_String);
    ui.comboBoxDataType->addItem("Word", WD::WDBMAttrValueType::T_Word);
    ui.comboBoxDataType->addItem("DVec2", WD::WDBMAttrValueType::T_DVec2);
    ui.comboBoxDataType->addItem("DVec3", WD::WDBMAttrValueType::T_DVec3);
    ui.comboBoxDataType->addItem("DQuat", WD::WDBMAttrValueType::T_DQuat);
    ui.comboBoxDataType->addItem("Color", WD::WDBMAttrValueType::T_Color);
    ui.comboBoxDataType->addItem("Uuid", WD::WDBMAttrValueType::T_Uuid);
    ui.comboBoxDataType->addItem("LevelRange", WD::WDBMAttrValueType::T_LevelRange);
    ui.comboBoxDataType->addItem("NodeRef", WD::WDBMAttrValueType::T_NodeRef);
    ui.comboBoxDataType->addItem("IntVector", WD::WDBMAttrValueType::T_IntVector);
    ui.comboBoxDataType->addItem("DoubleVector", WD::WDBMAttrValueType::T_DoubleVector);
    ui.comboBoxDataType->addItem("StringVector", WD::WDBMAttrValueType::T_StringVector);
    ui.comboBoxDataType->addItem("NodeRefs", WD::WDBMAttrValueType::T_NodeRefs);
    ui.comboBoxDataType->addItem("GeometryRef", WD::WDBMAttrValueType::T_GeometryRef);

    auto getTypeInfo = [] (WDBMBase* pMgr, WIZDesignerDictionaryCommon::TypeInfos& infos, WD::StringVector& datas)
    {
        if (pMgr == nullptr)
        {
            assert(false);
            return;
        }
        const auto& registTypes = pMgr->typeMgr().registedTypes();
        datas.reserve(registTypes.size());

        std::map<WIZDesignerDictionaryCommon::TypeInfo*, StringVector> tempParents;

        for (auto& eachType : registTypes)
        {
            if (eachType == nullptr)
                continue;
            auto& name = eachType->name();
            if (name.empty())
                continue;
            if (name == WIZDesignerDictionaryCommon::UserStr)
                continue;
            if (infos.find(name) == infos.end())
            {
                WIZDesignerDictionaryCommon::TypeInfo* info = new WIZDesignerDictionaryCommon::TypeInfo;
                info->attributes.reserve(eachType->attrDescs().size());
                for (auto pAttr : eachType->attrDescs()) 
                {
                    if (pAttr == nullptr)
                        continue;
                    info->attributes.push_back(*pAttr);
                }

                info->name      = eachType->name();
                info->describe  = eachType->describe();
                info->resources = eachType->resources();
                info->flags     = eachType->flags();
                infos.emplace(eachType->name(), info);

                auto parent     = eachType->parentTypes();
                parent.erase(std::remove(parent.begin(), parent.end(), WIZDesignerDictionaryCommon::UserStr), parent.end());
                tempParents.emplace(info, parent);

                datas.emplace_back(name);
            }
            else
            {
                assert(false && "节点类型重复!");
            }
        }

        for (auto& each : tempParents)
        {
            auto pType = each.first;
            const auto& parents = each.second;
            if (pType == nullptr || parents.empty())
                continue;
            for (auto& eachParentStr : parents)
            {
                auto pParentItr = infos.find(eachParentStr);
                if (pParentItr == infos.end())
                    continue;
                pType->parentTypes.emplace(pParentItr->second);
            }
        }
    };
    {
        auto typeInfos = WIZDesignerDictionaryCommon::TypeInfos();
        getTypeInfo(&_core.getBMCatalog(), typeInfos, _catalogTypes);
        _moduleInfos.emplace(WIZDesignerDictionaryCommon::MT_Catalog, typeInfos);
    }
    {
        auto typeInfos = WIZDesignerDictionaryCommon::TypeInfos();
        getTypeInfo(&_core.getBMDesign(), typeInfos, _designTypes);
        _moduleInfos.emplace(WIZDesignerDictionaryCommon::MT_Design, typeInfos);
    }

    ui.tableWidgetTypes->setColumnCount(2);
    ui.tableWidgetTypes->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeMode::Stretch);
    ui.tableWidgetTypes->verticalHeader()->setHidden(true);
    ui.tableWidgetTypes->setHorizontalHeaderLabels({"类型名称", "翻译名称"});
    ui.tableWidgetTypes->setSelectionMode(QAbstractItemView::SelectionMode::SingleSelection);
    ui.tableWidgetTypes->setSelectionBehavior(QAbstractItemView::SelectionBehavior::SelectRows);
    ui.tableWidgetAttributes->setColumnCount(2);
    ui.tableWidgetAttributes->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeMode::Stretch);
    ui.tableWidgetAttributes->setHorizontalHeaderLabels({"属性名称", "翻译名称"});
    ui.tableWidgetAttributes->verticalHeader()->setHidden(true);
    ui.tableWidgetAttributes->setSelectionMode(QAbstractItemView::SelectionMode::SingleSelection);
    ui.tableWidgetAttributes->setSelectionBehavior(QAbstractItemView::SelectionBehavior::SelectRows);

    ui.tableWidgetResource->setColumnCount(2);
    ui.tableWidgetResource->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeMode::Stretch);
    ui.tableWidgetResource->setHorizontalHeaderLabels({"资源类型", "资源值"});
    ui.tableWidgetResource->setSelectionMode(QAbstractItemView::SelectionMode::ExtendedSelection);


    auto* pValidator = new QRegExpValidator(QRegExp("[a-zA-Z]*"));
    ui.lineEditTypeName->setValidator(pValidator);
    ui.lineEditAttrName->setValidator(pValidator);
    ui.lineEditAttrSampleName->setValidator(pValidator);
    ui.lineEditEnumDictName->setValidator(pValidator);
    
}