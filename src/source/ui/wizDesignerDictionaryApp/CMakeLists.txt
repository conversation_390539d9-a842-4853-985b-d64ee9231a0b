set(TARGET_NAME WIZDesignerDictionary)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON) 
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

set(HEADER_FILES
    "WIZDesignerDictionaryMainWindow.h"
    "WIZDesignerDictionaryCommon.h"
)

set(SOURCE_FILES
    "main.cpp"
    "WIZDesignerDictionaryMainWindow.cpp"
)

set(FORM_FILES
    "WIZDesignerDictionaryMainWindow.ui"
)

set(RCC_FILES
    "WIZDesignerDictionaryMainWindow.qrc"
)

if(WIN32)
    add_executable(${TARGET_NAME} WIN32
		    ${HEADER_FILES}
		    ${SOURCE_FILES}
		    ${FORM_FILES}
		    ${RCC_FILES}
            "WIZDesignerDictionaryMainWindow.rc"
    )
else()
    add_executable(${TARGET_NAME}
		    ${HEADER_FILES}
		    ${SOURCE_FILES}
		    ${FORM_FILES}
            ${RCC_FILES}
    )
endif()

target_link_libraries(${TARGET_NAME} PUBLIC wizDesignerCore util.rapidxml)
target_link_libraries(${TARGET_NAME} PUBLIC Qt5::Core Qt5::Widgets qtpropertybrowser QtnRibbon)

target_include_directories(${TARGET_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

set_target_properties(${TARGET_NAME} PROPERTIES FOLDER "ui")

DeployQt(${TARGET_NAME})