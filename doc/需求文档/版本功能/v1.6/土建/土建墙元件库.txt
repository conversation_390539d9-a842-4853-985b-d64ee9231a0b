$S-  -- Synonym translation OFF
-- ----------------------------------------------------------------
-- Data Listing    Date : 14 Nov 2023 17:46

ONERROR GOLABEL /ERROR3

INPUT BEGIN
NEW CATALOGUE /STD-WALLS

NEW STSECTION /STD-WALLS-SPECIAL

NEW STCATEGORY /STD-WALLS-MAJOR-CATA

NEW SPRFILE /Major_wall
GTYP SCTN
PARA 10000

END
NEW TEXT /STD-WALLS-MAJOR-CATA-PA1
STEX 'All Design Parameters'

END
NEW TEXT /STD-WALLS-MAJOR-CATA-PTSS
STEX 'STD-WALLS-MAJOR-PTSS'

END
NEW TEXT /STD-WALLS-MAJOR-CATA-GMSS
STEX 'STD-WALLS-MAJOR-GMSS'

END
END
NEW STCATEGORY /STD-WALLS-DOME-<PERSON><PERSON>

NEW SPRFILE /Dome_as_a_wall
GTYP SCTN
PARA 10000

END
NEW TEXT /STD-WALLS-DOME-STCA-PA1
STEX 'All Design Parameters'

END
NEW TEXT /STD-WALLS-DOME-STCA-PTSS
STEX 'STD-WALLS-DOME-PTSS'

END
NEW TEXT /STD-WALLS-DOME-STCA-GMSS
STEX 'STD-WALLS-DOME-GMSS'

END
END
END
NEW STSECTION /STD-WALLS-SINGLE

NEW STCATEGORY /STD-WALLS-SINGLE-STCA

NEW SPRFILE /Single_leaf_wall
GTYP WALL
PARA 10000

END
NEW SPRFILE /100_thk_wall
GTYP WALL
PARA 100

END
NEW SPRFILE /200_thk_wall
GTYP WALL
PARA 200

END
NEW SPRFILE /Variable_thk_wall
GTYP WALL

END
NEW SPRFILE /Single_leaf_wall_strip_footing
GTYP WALL
PARA 10000

END
NEW TEXT /STD-WALLS-SINGLE-STCA-PA1
STEX 'None'

END
NEW TEXT /STD-WALLS-SINGLE-STCA-PTSS
STEX 'STD-WALLS-SINGLE-PTSS'

END
NEW TEXT /STD-WALLS-SINGLE-STCA-GMSS
STEX 'STD-WALLS-SINGLE-GMSS'

END
END
END
NEW STSECTION /STD-WALLS-CAVITY

NEW STCATEGORY /STD-WALLS-CAVITY-STCA

NEW SPRFILE /Cavity_wall_with_footing
GTYP WALL
PARA 10000

END
NEW SPRFILE /Cavity_wall
GTYP WALL
PARA 10000

END
NEW TEXT
STEX 'None'

END
NEW TEXT
STEX 'STD-WALLS-SINGLE-PTSS'

END
NEW TEXT
STEX 'STD-WALLS-SINGLE-GMSS'

END
END
END
NEW STSECTION /STD-WALLS-REFERENCE

NEW STCATEGORY /STD-WALLS-PTSS

NEW PTSSET /STD-WALLS-MAJOR-PTSS

NEW PLINE
PKEY IBOW
PX ( ATTRIB DESP[1 ] )
PY 0
PLAX X
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY NA
CLFL true
TUFL true

END
NEW PLINE
PKEY OBOW
PX 0
PY 0
PLAX -X
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY CBOW
PX ( ATTRIB DESP[1 ] / 2 )
PY 0
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY OTOW
PX 0
PY ( ATTRIB DESP[2 ] )
PLAX -X
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY ITOW
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[2 ] )
PLAX X
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY CTOW
PX ( ATTRIB DESP[1 ] / 2 )
PY ( ATTRIB DESP[2 ] )
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY CORB
PX ( 3 * ATTRIB DESP[1 ] / 2 )
PY ( ATTRIB DESP[3 ] )
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY LEVO
PX 0
PY ( ATTRIB DESP[4 ] )
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY LEVT
PX 0
PY ( ATTRIB DESP[5 ] )
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY LEVH
PX 0
PY ( ATTRIB DESP[6 ] )
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY LEVF
PX 0
PY ( ATTRIB DESP[7 ] )
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY LEVV
PX 0
PY ( ATTRIB DESP[8 ] )
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY LEVS
PX 0
PY ( ATTRIB DESP[9 ] )
LEVE 0 6
TUFL true

END
END
NEW PTSSET /STD-WALLS-DOME-PTSS

NEW PLINE
PKEY IBOW
PX ( ATTRIB DESP[2 ] )
PY 0
PLAX -X
TUFL true

END
NEW PLINE
PKEY NA
PX 0
PY 0
TUFL true

END
NEW PLINE
PKEY OBOW
PX 0
PY 0
PLAX X
TUFL true

END
END
NEW PTSSET /STD-WALLS-SINGLE-PTSS

NEW PLINE
PKEY IBOW
PX ( ATTRIB DESP[1 ] )
PY 0
PLAX X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY NA
CLFL true
TUFL true

END
NEW PLINE
PKEY OBOW
PX 0
PY 0
PLAX -X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY CBOW
PX ( ATTRIB DESP[1 ] / 2 )
PY 0
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY OTOW
PX 0
PY ( ATTRIB DESP[2 ] )
PLAX -X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY ITOW
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[2 ] )
PLAX X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY CTOW
PX ( ATTRIB DESP[1 ] / 2 )
PY ( ATTRIB DESP[2 ] )
LEVE 0 6
TUFL true

END
END
NEW PTSSET /STD-WALLS-SIZE-PTSS

NEW PLINE
PKEY IBOW
PX ( ATTRIB CPAR[1 ] )
PY 0
PLAX X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY NA
CLFL true
TUFL true

END
NEW PLINE
PKEY OBOW
PX 0
PY 0
PLAX -X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY CBOW
PX ( ATTRIB CPAR[1 ] / 2 )
PY 0
LEVE 0 6
TUFL true

END
NEW PLINE
PKEY OTOW
PX 0
PY ( ATTRIB DESP[1 ] )
PLAX -X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY ITOW
PX ( ATTRIB CPAR[1 ] )
PY ( ATTRIB DESP[1 ] )
PLAX X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY CTOW
PX ( ATTRIB CPAR[1 ] / 2 )
PY ( ATTRIB DESP[1 ] )
LEVE 0 6
TUFL true

END
END
NEW PTSSET /STD-WALLS-DOUBLE-PTSS

NEW PLINE
PKEY IBOW
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY 0
PLAX X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY NA
CLFL true
TUFL true

END
NEW PLINE
PKEY OBOW
PX 0
PY 0
PLAX -X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY OTOW
PX 0
PY ( ATTRIB DESP[4 ] )
PLAX -X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY ITOW
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY ( ATTRIB DESP[4 ] )
PLAX X
LEVE 0 6
TUFL true
CCON ANY

END
NEW PLINE
PKEY CBOW
PX ( ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] ) / 2 )
PY 0
PLAX -Y
LEVE 0 6
CLFL true
TUFL true

END
NEW PLINE
PKEY CTOW
PX ( ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] ) / 2 )
PY ( ATTRIB DESP[4 ] )
LEVE 0 6
CLFL true
TUFL true

END
END
END
NEW STCATEGORY /STD-WALLS-GMSS

NEW GMSSET /STD-WALLS-MAJOR-GMSS

NEW SPROFILE
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[3 ] - ATTRIB DESP[1 ] * 2 )
PRAD 0

END
NEW SPVERT
PX ( 2 * ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[3 ] - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( 2 * ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[3 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[3 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[2 ] )
PRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[2 ] )
PRAD 0

END
END
END
NEW GMSSET /STD-WALLS-DOME-GMSS

NEW SPROFILE
TUFL true

NEW SPVERT
PX ( - ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[1 ] / 2 )
PRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[1 ] / 3 )
PY ( ATTRIB DESP[1 ] / 2 )
PRAD ( ATTRIB DESP[1 ] )

END
NEW SPVERT
PX ( ATTRIB DESP[2 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[1 ] / 3 - ATTRIB DESP[2 ] )
PY ( ATTRIB DESP[1 ] / 2 - ATTRIB DESP[2 ] )
PRAD ( ATTRIB DESP[1 ] )

END
NEW SPVERT
PX ( - ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[1 ] / 2 - ATTRIB DESP[2 ] )
PRAD 0

END
END
END
NEW GMSSET /STD-WALLS-SINGLE-GMSS

NEW SPROFILE
LEVE 4 8
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[2 ] )
PRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[2 ] )
PRAD 0

END
END
NEW SPROFILE
LEVE 0 3
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[2 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[2 ] )
PRAD 0
DRAD 0

END
END
END
NEW GMSSET /STD-WALLS-SIZE-GMSS

NEW SPROFILE
LEVE 4 8
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB CPAR[1 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB CPAR[1 ] )
PY ( ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[1 ] )
PRAD 0

END
END
NEW SPROFILE
LEVE 0 3
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB CPAR[1 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB CPAR[1 ] )
PY ( ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
END
END
NEW GMSSET /STD-WALLS-DOUBLE-GMSS

NEW SPROFILE
LEVE 4 8
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
END
NEW SPROFILE
LEVE 0 3
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
END
NEW SPROFILE
LEVE 4 8
TUFL true

NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
END
NEW SPROFILE
LEVE 0 3
TUFL true

NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
END
END
NEW GMSSET /STD-WALLS-STRIP-GMSS

NEW SPROFILE
LEVE 4 8
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX 0
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[2 ] / 8 )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[2 ] / 8 )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[2 ] )
PRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[2 ] )
PRAD 0

END
END
NEW SPROFILE
LEVE 0 3
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX 0
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[2 ] / 8 )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[2 ] / 8 )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[2 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[2 ] )
PRAD 0
DRAD 0

END
END
END
NEW GMSSET /STD-WALLS-DSTRIP-GMSS

NEW SPROFILE
LEVE 4 8
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
END
NEW SPROFILE
LEVE 0 3
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX 0
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
END
NEW SPROFILE
LEVE 4 8
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0

END
NEW SPVERT
PX 0
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[4 ] / 8 )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] + ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[4 ] / 8 )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] + ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY ( - ATTRIB DESP[1 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY 0
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY 0
PRAD 0

END
END
NEW SPROFILE
LEVE 0 3
TUFL true

NEW SPVERT
PX 0
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX 0
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( - ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[4 ] / 8 )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] + ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] - ATTRIB DESP[4 ] / 8 )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] + ATTRIB DESP[4 ] / 6 )
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY ( - ATTRIB DESP[1 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY 0
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] + ATTRIB DESP[3 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY ( ATTRIB DESP[4 ] )
PRAD 0
DRAD 0

END
NEW SPVERT
PX ( ATTRIB DESP[1 ] + ATTRIB DESP[2 ] )
PY 0
PRAD 0
DRAD 0

END
END
END
END
NEW STCATEGORY /STD-WALLS-DTSE

NEW DTSET /STD-WALLS-DOME-DTSE

NEW DATA
DKEY RAD
PTYP DIST
PPRO ( ATTRIB DESP[1 ] )
DPRO ( 20000 )
PURP DESP
NUMB 1
DTIT 'Radius of Dome'

END
NEW DATA
DESC 'Thickness of Dome'
DKEY THCK
PTYP DIST
PPRO ( ATTRIB DESP[2 ] )
DPRO ( ATTRIB DESP[1 ] / 12 )
PURP DESP
NUMB 2
DTIT 'Thickness of Dome'

END
END
NEW DTSET /STD-WALLS-SINGLE-DTSE

NEW DATA
DESC 'Plot file'
DKEY PLOT
PTYP PPRT
PPRO ( 'SINGLE.plt' )
DPRO ( 'WALLS' )
PURP PLOT
DTIT 'Plot File'

END
NEW DATA
DKEY THK
PTYP DIST
PPRO ( ATTRIB DESP[1 ] )
DPRO ( 100 )
PURP DESP
NUMB 1
DTIT 'Thickness'

END
NEW DATA
DKEY HEIG
PTYP DIST
PPRO ( ATTRIB DESP[2 ] )
DPRO ( 2000 )
PURP DESP
NUMB 2
DTIT 'Height'

END
END
NEW DTSET /STD-WALLS-STRIP-DTSE

NEW DATA
DESC 'Plot file'
DKEY PLOT
PTYP PPRT
PPRO ( 'FOOTING.plt' )
DPRO ( 'WALLS' )
PURP PLOT
DTIT 'Plot File'

END
NEW DATA
DKEY THK
PTYP DIST
PPRO ( ATTRIB DESP[1 ] )
DPRO ( 100 )
PURP DESP
NUMB 1
DTIT 'Thickness'

END
NEW DATA
DKEY HEIG
PTYP DIST
PPRO ( ATTRIB DESP[2 ] )
DPRO ( 2000 )
PURP DESP
NUMB 2
DTIT 'Height'

END
END
NEW DTSET /STD-WALLS-SIZE-DTSE

NEW DATA
DKEY HEIG
PTYP DIST
PPRO ( ATTRIB DESP[1 ] )
DPRO ( 3000 )
PURP DESP
NUMB 1
DTIT 'Height'

END
NEW DATA
DKEY THK
PPRO ( ATTRIB PARA[1 ] )
DPRO ( 200 )
PURP DATA
NUMB 2
DTIT 'Thickness'

END
END
NEW DTSET /STD-WALLS-MAJOR-DTSE

NEW DATA
DKEY THK
PTYP DIST
PPRO ( ATTRIB DESP[1 ] )
DPRO ( 1000 )
PURP DESP
NUMB 1
DTIT 'Thickness'

END
NEW DATA
DKEY HEIG
PTYP DIST
PPRO ( ATTRIB DESP[2 ] )
DPRO ( 10000 )
PURP DESP
NUMB 2
DTIT 'Height'

END
NEW DATA
DKEY CORB
PTYP DIST
PPRO ( ATTRIB DESP[3 ] )
DPRO ( 8000 )
PURP DESP
NUMB 3
DTIT 'Corbel Level'

END
NEW DATA
DKEY LEVO
PTYP DIST
PPRO ( ATTRIB DESP[4 ] )
DPRO ( 3000 )
PURP DESP
NUMB 4
DTIT 'Level One'

END
NEW DATA
DKEY LEVT
PTYP DIST
PPRO ( ATTRIB DESP[5 ] )
DPRO ( 5000 )
PURP DESP
NUMB 5
DTIT 'Level Two'

END
NEW DATA
DKEY LEVH
PTYP DIST
PPRO ( ATTRIB DESP[6 ] )
DPRO ( 6000 )
PURP DESP
NUMB 6
DTIT 'Level Three'

END
NEW DATA
DKEY LEVF
PTYP DIST
PPRO ( ATTRIB DESP[7 ] )
DPRO ( 7000 )
PURP DESP
NUMB 7
DTIT 'Level Four'

END
NEW DATA
DKEY LEVV
PTYP DIST
PPRO ( ATTRIB DESP[8 ] )
DPRO ( 8000 )
PURP DESP
NUMB 8
DTIT 'Level Five'

END
NEW DATA
DKEY LEVS
PTYP DIST
PPRO ( ATTRIB DESP[9 ] )
DPRO ( 9000 )
PURP DESP
NUMB 9
DTIT 'Level Six'

END
END
NEW DTSET /STD-WALLS-DOUBLE-DTSE

NEW DATA
DESC 'Plot file'
DKEY PLOT
PTYP PPRT
PPRO ( 'CAVITY.plt' )
DPRO ( 'WALLS' )
PURP PLOT
DTIT 'Plot File'

END
NEW DATA
DKEY OTHK
PTYP DIST
PPRO ( ATTRIB DESP[1 ] )
DPRO ( 100 )
PURP DESP
NUMB 1
DTIT 'Outer Thickness'

END
NEW DATA
DKEY GAP
PTYP DIST
PPRO ( ATTRIB DESP[2 ] )
DPRO ( 50 )
PURP DESP
NUMB 2
DTIT 'Cavity Gap'

END
NEW DATA
DKEY ITHK
PTYP DIST
PPRO ( ATTRIB DESP[3 ] )
DPRO ( 140 )
PURP DESP
NUMB 3
DTIT 'Inner Thickness'

END
NEW DATA
DKEY HEIG
PTYP DIST
PPRO ( ATTRIB DESP[4 ] )
DPRO ( 4000 )
PURP DESP
NUMB 4
DTIT 'Height'

END
NEW DATA
DKEY THK
PPRO ( ATTRIB RPRO OTHK + ATTRIB RPRO GAP + ATTRIB RPRO ITHK )
DPRO ( 290 )
PURP DATA
NUMB 5
DTIT 'Overall Thickness'

END
END
NEW DTSET /STD-WALLS-DSTRIP-DTSE

NEW DATA
DESC 'Plot file'
DKEY PLOT
PTYP PPRT
PPRO ( 'CFOOTING.plt' )
DPRO ( 'WALLS' )
PURP PLOT
DTIT 'Plot File'

END
NEW DATA
DKEY OTHK
PTYP DIST
PPRO ( ATTRIB DESP[1 ] )
DPRO ( 100 )
PURP DESP
NUMB 1
DTIT 'Outer Thickness'

END
NEW DATA
DKEY GAP
PTYP DIST
PPRO ( ATTRIB DESP[2 ] )
DPRO ( 50 )
PURP DESP
NUMB 2
DTIT 'Cavity Gap'

END
NEW DATA
DKEY ITHK
PTYP DIST
PPRO ( ATTRIB DESP[3 ] )
DPRO ( 140 )
PURP DESP
NUMB 3
DTIT 'Inner Thickness'

END
NEW DATA
DKEY HEIG
PTYP DIST
PPRO ( ATTRIB DESP[4 ] )
DPRO ( 4000 )
PURP DESP
NUMB 4
DTIT 'Height'

END
NEW DATA
DKEY THK
PPRO ( ATTRIB RPRO OTHK + ATTRIB RPRO GAP + ATTRIB RPRO ITHK )
DPRO ( 300 )
PURP DATA
NUMB 5
DTIT 'Overall Thickness'

END
END
END
END
END
OLD SPRFILE /Major_wall
PSTR PTSSET /STD-WALLS-MAJOR-PTSS
GSTR GMSSET /STD-WALLS-MAJOR-GMSS
DTRE DTSET /STD-WALLS-MAJOR-DTSE

OLD SPRFILE /Dome_as_a_wall
PSTR PTSSET /STD-WALLS-DOME-PTSS
GSTR GMSSET /STD-WALLS-DOME-GMSS
DTRE DTSET /STD-WALLS-DOME-DTSE

OLD SPRFILE /Single_leaf_wall
PSTR PTSSET /STD-WALLS-SINGLE-PTSS
GSTR GMSSET /STD-WALLS-SINGLE-GMSS
DTRE DTSET /STD-WALLS-SINGLE-DTSE

OLD SPRFILE /100_thk_wall
PSTR PTSSET /STD-WALLS-SIZE-PTSS
GSTR GMSSET /STD-WALLS-SIZE-GMSS
DTRE DTSET /STD-WALLS-SIZE-DTSE

OLD SPRFILE /200_thk_wall
PSTR PTSSET /STD-WALLS-SIZE-PTSS
GSTR GMSSET /STD-WALLS-SIZE-GMSS
DTRE DTSET /STD-WALLS-SIZE-DTSE

OLD SPRFILE /Variable_thk_wall
PSTR PTSSET /STD-WALLS-SINGLE-PTSS
GSTR GMSSET /STD-WALLS-SINGLE-GMSS
DTRE DTSET /STD-WALLS-SINGLE-DTSE

OLD SPRFILE /Single_leaf_wall_strip_footing
PSTR PTSSET /STD-WALLS-SINGLE-PTSS
GSTR GMSSET /STD-WALLS-STRIP-GMSS
DTRE DTSET /STD-WALLS-STRIP-DTSE

OLD SPRFILE /Cavity_wall_with_footing
PSTR PTSSET /STD-WALLS-DOUBLE-PTSS
GSTR GMSSET /STD-WALLS-DSTRIP-GMSS
DTRE DTSET /STD-WALLS-DSTRIP-DTSE

OLD SPRFILE /Cavity_wall
PSTR PTSSET /STD-WALLS-DOUBLE-PTSS
GSTR GMSSET /STD-WALLS-DOUBLE-GMSS
DTRE DTSET /STD-WALLS-DOUBLE-DTSE

OLD PTSSET /STD-WALLS-DOME-PTSS
NARE PLINE 2 of PTSSET /STD-WALLS-DOME-PTSS

INPUT END  CATALOGUE /STD-WALLS
INPUT FINISH
-- Switch synonyms back on if an error occurs.
LABEL /ERROR3
handle ANY
$S+
RETURN ERROR
endhandle

-- End Data Listing    Date : 14 Nov 2023 17:46
$S+  -- Synonym translation ON
-- ----------------------------------------------------------------

